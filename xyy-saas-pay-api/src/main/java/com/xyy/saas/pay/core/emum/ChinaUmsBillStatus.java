package com.xyy.saas.pay.core.emum;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 全民支付状态枚举类
 * <AUTHOR>
 *
 */
public class ChinaUmsBillStatus {
	public static final int PAID = 1 ;    // 支付
	public static final int UNPAID = 11;   // 未支付
	public static final int REFUND = 2;   // 退款成功
	public static final int CLOSED = 14;   // 关闭
	public static final int UNKNOWN = 15;   // 未知
	public static final int ERROE = 99;   // 未知
	//给会员服务使用
	public static final int PARAM_ERROR = 100;   // 回调会员服务参数错误，比如订单号不存在，机构号
	public static final int RUNTIME_ERROR = 101;   // 未知
	public static final int RECORD_NOTEXIST = 102;   // 会员服务无对应记录
	
	//
	public static final int VERIFY_TIME_FAILED = -99;   // 
	public static final String VERIFY_TIME_FAILED_TEXT = "请求二维码过于频繁";   
	public static final int LOCAL_SAVE_ERROR = -98;   // 
	public static final String LOCAL_SAVE_ERROR_TEXT = "本地存储错误";  
	
	
	public static enum ChinaUmsBillStatusEnum {
		
		/**
		 * 已支付
		 */
		PAID_ENUM("已支付", "PAID", PAID),	
		
		/**
		 * 未支付
		 */
		UNPAID_ENUM("未支付", "UNPAID", UNPAID),
		
		/**
		 * 已退款
		 */
		REFUND_ENUM("已退款", "REFUND", REFUND),
		
		/**
		 * 关闭
		 */
		CLOSED_ENUM("关闭", "CLOSED", CLOSED),
		
		/**
		 * 未知
		 */
		UNKNOWN_ENUM("未知", "UNKNOWN", UNKNOWN)

		;
		
		private static final Map<String, Integer> NCMap = new ConcurrentHashMap<String, Integer>();
		private static final Map<Integer, String> CNMap = new ConcurrentHashMap<Integer, String>();
		private static final Map<Integer, String> CVMap = new ConcurrentHashMap<Integer, String>();
		private static final Map<String, String> NVMap = new ConcurrentHashMap<String, String>();
		
		static {
			for (ChinaUmsBillStatusEnum e : ChinaUmsBillStatusEnum.values()) {
				NCMap.put(e.getName(), e.getCode());
				CNMap.put(e.getCode(), e.getName());
				CVMap.put(e.getCode(), e.getValue());
				NVMap.put(e.getName(), e.getValue());
			}
		}
		
		private String value;
		
		private String name;
		
		private int code;
		
		private ChinaUmsBillStatusEnum(String value, String name, int code){
			this.value=value;
			this.name=name;
			this.code=code;
		}
		
		private int getCode() {
			return code;
		}

		private String getName() {
			return name;
		}
		
		private String getValue() {
			return value;
		}
		
		public static String getName(int code) {
			if(CNMap.containsKey(code)) {
				return CNMap.get(code);
			}
			return "";
		}
		
		public static int getCode(String name) {
			if(NCMap.containsKey(name)) {
				return NCMap.get(name);
			}
			return ERROE;
		}
		
		public static String getValue(int code) {
			if(CVMap.containsKey(code)) {
				return CVMap.get(code);
			}
			return "";
		}
		
		public static String getValue(String name) {
			if(NVMap.containsKey(name)) {
				return NVMap.get(name);
			}
			return "";
		}
		
		public static boolean containsCode(int code) {
			return CNMap.containsKey(code);
		}
		
		public static boolean containsName(String name) {
			return NCMap.containsKey(name);
		}
		
	}
	
}
