package com.xyy.saas.purchase.core.api.purchase;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.ReturnBillDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseWarehousingDetailDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseWarehousingInfoDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseWarehousingQueryDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseWarehousingTaxDto;

import java.util.List;
import java.util.Map;

/**
 * 类名称:PurchaseWarehousingApi
 * 创建人:sunyue
 * 创建时间:2020/4/7 21:25
 */

public interface PurchaseWarehousingApi {

    /**
     * 连锁采购入库单分页查询
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<PurchaseWarehousingInfoDto>> getListByQuery(PurchaseWarehousingQueryDto queryDto);

    /**
     * 连锁采购入库单分页查询 -总部采购退货单-提取采购入库单
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<PurchaseWarehousingInfoDto>> getListByQueryForReturn(PurchaseWarehousingQueryDto queryDto);

    /**
     * 根据订单编号查详情
     * @param queryDto
     * @return
     */
    ResultVO<PurchaseWarehousingInfoDto> selectDetailByBillNo(PurchaseWarehousingQueryDto queryDto);


    /**
     * 根据订单编号查询
     * @param organSign
     * @param billNo
     * @return
     */
    ResultVO<List<PurchaseWarehousingDetailDto>> getListByBillNo(String organSign, String billNo, Integer pullYn);


    /**
     * 采购入库
     * @param dto
     * @return
     */
    ResultVO updatePurchaseWarehousing(PurchaseWarehousingInfoDto dto);


    /**
     * GSP-总部入库记录-单据汇总-分页
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<PurchaseWarehousingInfoDto>> getListByQueryGsp(PurchaseWarehousingQueryDto queryDto);



    /**
     * GSP-总部入库记录-商品汇总-分页
     * @return
     */
    ResultVO<PageInfo<PurchaseWarehousingDetailDto>> getDetailListGsp(PurchaseWarehousingQueryDto queryDto);


    /**
     * 查询创建过单据的采购人员列表
     * @param dto
     * @return
     */
    ResultVO getPurchaseBillUsers(PurchaseWarehousingQueryDto  dto);

    /**
     * 采购入库单详情打印
     * @param queryDto
     * @param printRowSize
     * @return
     */
    ResultVO printDetailsInfo(PurchaseWarehousingQueryDto queryDto, List<Integer> printRowSize);


    /**
     * 采购入库单回退
     * @param returnBillDto
     * @return
     */
    ResultVO returnBill(ReturnBillDto returnBillDto) ;

    /**
     * 采购入库单商品明细列表查询（分页）
     * @param dto
     * @return
     */
    ResultVO<PageInfo<PurchaseWarehousingDetailDto>> getProductDetailListByQuery(PurchaseWarehousingQueryDto dto);
    /**
     * 采购入库单汇总数据查询
     * @param dto
     * @return
     */
    ResultVO<PurchaseWarehousingDetailDto> getPurchaseWarehousingSumCount(PurchaseWarehousingQueryDto dto);

    /**
     * 批量根据入库单号查询税率/含税价
     * @param organSign 机构号
     * @param type 0:总部 1:门店
     * @param list 入库单号
     * @return
     */
    ResultVO<List<PurchaseWarehousingTaxDto>> getPurchaseWarehousingTax(String organSign,int type,List<String> list);

}
