package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @annotation:计划单明细DTO
 * @create 2018-09-18 17:49
 **/
@Data
public class PurchasePlanDetailDto implements Serializable {

    private static final long serialVersionUID = -2064330294134276762L;

    private Integer id;

    private Integer detailId;

    private Integer purchasePlanId;

    private Integer productId;

    private String productCode;

    private String productName;

    private String producingArea;

    private String billNo;

    private String productBatchNo;

    private String specifications;

    private String manufacturer;
    private BigDecimal stockNumber;

    private BigDecimal amount;

    private BigDecimal productTaxPrice;

    private BigDecimal productMallPrice;
    /** '零售价'*/
    private BigDecimal retailPrice;
    /** '最后一次供应商'*/
    private String lastProvidePref;
    /** '商品分类'*/
    private Integer productType;
    /** '商品分类展示'*/
    private Integer productTypeStr;
    /** '建议计划量'*/
    private BigDecimal productSuggestPlanCount;
    private String createUser;

    private Date createTime;

    private String updateUser;

    private Date updateTime;

    private Boolean yn;

    private String supplierNo;
    //供应商混合查询字段
    private String supplierMnemonicCode;

    private String supplierName;

    private String registeredAddress;

    private Integer standardLibraryId;

    private Integer page;//页码
    private Integer rows;//每页条数
    private  Byte isProductHidden;//商品是否展示字段
    private String pharmacyPref;//药店各自商品编号

    private String organSign;

    /** 剂型 */
    private String agentName;

    /**
     * 销售量
     */
    private BigDecimal productNum;
    /**
     * 销售金额
     */
    private BigDecimal actualAmount;

    /**
     * 单位
     */
    private String packingUnit;

    /**
     * 产地
     */
    private String productOriginAddress;

    /**
     * 最后采购价
     */
    private BigDecimal lastPrice;

    /**
     * 批准文号
     */
    private String approvalNumber;

    /**
     * 经营范围
     **/
    private Integer businessScope;

    private String providerScope;//供应商经营范围

    /** 是否禁用 */
    private Integer used;

    /**
     *  采购数量
     **/
    private BigDecimal productAmount;

    //总的含税金额
    private BigDecimal totalAmount;

    private String drugPermissionPerson;

    private String businessKey;
    private Integer businessScene;

}

