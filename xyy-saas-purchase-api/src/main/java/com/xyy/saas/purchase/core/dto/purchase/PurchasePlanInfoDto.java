package com.xyy.saas.purchase.core.dto.purchase;

import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * SaasPurchasePlanPo
 */
@Data
@Slf4j
public class PurchasePlanInfoDto  extends PurchaseBaseDto implements Serializable {

    private Integer id;

    private String organSign;

    private String billNo;

    private Date billTime;

    private String billTimes;

    private String beginBillTime;

    private String endBillTime;

    private String supplierNo;

    private String supplierName;

    private String billingUser;

    private String billingUserName;

    /**  计划单状态:1-未完成；2-已完成*/
    private Byte status;
    private String statusCN;

    private Byte receivingState;

    private String receivingStateCN;

    private Date receivingPlanDate;

    private String receivingPlanDates;

    private String remark;

    private BigDecimal totalAmount;

    private List<PurchasePlanInfoDetailDto> detailDtos = Lists.newArrayList();

    private Integer baseVersion;

    private Long updateVersion;

    private String createUser;

    private String updateUser;

    private Date createTime;

    private Date updateTime;

    /**  审核状态:0-审核中；1-已通过；2-已驳回*/
    private Byte approveStatus;

    private String approveStatusCN;

    /**
     * 当前待办角色ID
     */
    private Integer approveRoleId;

    private String approveRoleName;

    /**
     * 当前待办人id
     */
    private Integer approveUserId;

    private String approveUserName;

    /**
     * 当前待办部门
     */
    private String approveOrgansignType;

    private String approveOrgansignName;

    /**
     * 审核所需的businessKey
     */
    private String businessKey;

    /**
     * 审核所需要业务场景
     */
    private int businessScene;

    /**
     * 采购内容
     */
    private List<Map<String, Object>> purchaseContent;

    /**
     * 有无审批权限 0-无权限 1-有权限
     */
    private int ApprovePower;

    private Integer productTypeNumbers;

    /**
     * 采购总金额
     */
    private BigDecimal totalTaxPrice;

    /**
     * 采购订单状态
     */
    private Integer purchaseState;

    private String purchaseStateCN;

    //商品信息
    private  String productInfo;

    //复用关联编号
    private  String relateBillNo;

}



