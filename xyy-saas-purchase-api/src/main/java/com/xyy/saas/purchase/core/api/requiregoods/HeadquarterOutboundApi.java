package com.xyy.saas.purchase.core.api.requiregoods;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseCommonAmountDto;
import com.xyy.saas.purchase.core.dto.requiregoods.*;

import java.util.List;
import java.util.Map;

/**
 * 类名称:HeadquarterOutboundApi
 * 类描述:总部出库单
 * 创建人:sunyue
 * 创建时间:2020/4/5 16:56
 */

public interface HeadquarterOutboundApi {
    /**
     * 分页查询总部配送单
     *
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<HeadquarterOutboundInfoDto>> getListByQuery(HeadquarterOutboundQueryDto queryDto);


    /**
     * 查询总部配送单 汇总统计
     * @param queryDto
     * @return
     */
    ResultVO<HeadquarterOutboundAmountDto> getPurchaseOrderOutboundAmount(HeadquarterOutboundQueryDto queryDto);


    /**
     * 根据订单编号查询详情
     *
     * @return
     */
    ResultVO<HeadquarterOutboundInfoDto> getDetailListByBillNo(String organSign, String billNo);


    /**
     * 根据编号查询详情
     *
     * @return
     */
    ResultVO<List<HeadquarterOutboundDetailDto>> selectDetailByBillNo(String organSign, String billNo);


    /**
     * 保存出库单
     *
     * @param dto
     * @return
     */
    ResultVO updateOutbound(HeadquarterOutboundInfoDto dto);

    /**
     * 修改出库单状态  补偿接口
     * @param organSign
     * @param id
     * @return
     */
    ResultVO updateHeadOutboundState(String organSign, Long id, Byte outboundState,Integer updateVersion);


    /**
     * GSP-出库复核记录-单据汇总
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<HeadquarterOutboundInfoDto>> getGspListByQuery(HeadquarterOutboundQueryDto queryDto);


    /**
     * GSP-出库复核记录-商品汇总
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<HeadquarterOutboundDetailDto>> selectDetailsByConditionGspList(HeadquarterOutboundQueryDto queryDto);


    /**
     * 获取创建过单据的复核员
     * @param headquartersOrganSign
     * @return
     */
    ResultVO getReviewUsers(String headquartersOrganSign);

    /**
     * 委托配送出库单打印
     * @param queryDto
     * @param printingRowSize
     * @return
     */
    Map<String, Object> headquarterOutboundPrint(HeadquarterOutboundQueryDto queryDto, List<Integer> printingRowSize);

    /**
     * 刷数据-委托配送时总部出库单详情里的标准库id
     * @return
     */
    ResultVO setConsignDetailsStandLibraryId();

    /**
     * GSP-出库复核记录-商品汇总-数据汇总统计
     * @param dto
     * @return
     */
    ResultVO<HeadquarterSumAmountDto> getSumAmountByConditionGsp(HeadquarterOutboundQueryDto dto);

    /**
     * 分页查询销售出库单
     *
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<HeadquarterOutboundInfoDto>> getSalesDeliveryListByQuery(HeadquarterOutboundQueryDto queryDto);

    /**
     * 销售出库单明细列表分页查询
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<HeadquarterOutboundInfoAndDetailDto>> getSalesDeliveryDetailList(HeadquarterOutboundQueryDto queryDto);

    ResultVO<PurchaseCommonAmountDto> getSalesDeliverySumAmount(HeadquarterOutboundQueryDto queryDto);
    /**
     * 分页查询总部配送出库单商品明细列表
     * @param dto
     * @return
     */
    ResultVO<PageInfo<HeadquarterOutboundDetailDto>> getProductDetailListByQuery(HeadquarterOutboundQueryDto dto);
}
