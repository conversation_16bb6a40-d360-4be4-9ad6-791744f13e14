package com.xyy.saas.purchase.core.api.thirdparty;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.SaasPurchaseOrgMappingPoDto;

import java.util.List;

/**
 * @ClassName PurchaseOrgMappingApi
 * @Description TODO
 * <AUTHOR> hongda
 * @Date 2020/5/11 10:16
 * @Version 1.0
 */
public interface ThirdPartyOrgMappingApi {

    /**
     * <AUTHOR> hongda
     * @Description //根据orgType查询
     * @Date 17:44 2020/5/11
     * @Param [orgType]
     * @return com.xyy.saas.common.util.ResultVO
     **/
    public ResultVO getOrgMapping(String orgType);


    /**
     * <AUTHOR> hongda
     * @Description //批量保存
     * @Date 17:44 2020/5/11
     * @Param [dto]
     * @return com.xyy.saas.common.util.ResultVO
     **/
    public ResultVO saveOrgMapping(List<SaasPurchaseOrgMappingPoDto> dto);

    /**
     * <AUTHOR> hongda
     * @Description //保存供应商机构映射关系
     * @Date 19:31 2020/5/13
     * @Param [dto]
     * @return com.xyy.saas.common.util.ResultVO
     **/
    public ResultVO updateProviderMapping(List<SaasPurchaseOrgMappingPoDto> dto);

    public ResultVO deleteOrgMapping(List<String> orgThirdList);
}
