package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class PurchaseWarehousingDataForFmsDto implements Serializable {

    private Long id;
    /**
     * 单据编号
     */
    private String billNo;
    /**
     *门店机构号
     */
    private String contactNo;
    /**
     *门店机构名称
     */
    private String contactName;
    /**
     *总部机构号
     */
    private String organSign;
    /**
     *操作人
     */
    private String operateUser;
    /**
     *金额
     */
    private BigDecimal billTotalAmount;
    /**
     *创建时间
     */
    private Date billTime;
    /**
     * 单据类型(1:配送单;2:门店退货入库单;5:采购入库单;6:总部退货出库单)
     */
    private Integer billType;

    /**
     * billType: 1  为门店要货申请单
     * billType: 2  为门店退货申请单
     * billType: 5  为采购入库单
     * billType: 6  为总部退货出库单
     */
    private String relatedBillNo;

    /**
     * 发mq时relatedBillNo 要用这个传
     */
    private String sourceBillNo;
}
