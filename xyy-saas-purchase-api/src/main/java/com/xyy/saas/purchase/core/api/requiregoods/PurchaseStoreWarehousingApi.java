package com.xyy.saas.purchase.core.api.requiregoods;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.exception.BusinessException;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.BillSummaryDto;
import com.xyy.saas.purchase.core.dto.ReturnBillDto;
import com.xyy.saas.purchase.core.dto.requiregoods.*;
import com.xyy.saas.purchase.core.dto.returngoods.PurchaseStoreExitLibraryApplyInfoDto;

import java.util.List;

/**
 * Created by zhangjinxia on 2020/4/4.
 * 门店入库单
 */
public interface PurchaseStoreWarehousingApi {
    /**
     * 分页查询入库单列表
     *
     * @param model
     * @return
     */
    ResultVO<PageInfo<PurchaseStoreWarehousingBillInfoDto>> selectByCondition(PurchaseStoreWarehousingBillQueryDto model);

    /**
     * 查询info列表
     *
     * @param queryDto
     * @return
     */
    ResultVO<List<PurchaseStoreWarehousingBillInfoDto>> selectInfoListByCondition(PurchaseStoreWarehousingBillQueryDto queryDto);

    /**
     * 查询detail列表
     *
     * @param organSign 机构编码
     * @param billNo    单号
     * @return
     */
    ResultVO<List<PurchaseStoreWarehousingBillDetailDto>> selectDetailListByBillNo(String organSign, String billNo);

    /**
     * 根据单号 查入库单 info和details
     *
     * @param organSign
     * @param billNo
     * @return
     */
    ResultVO<PurchaseStoreWarehousingBillInfoDto> selectInfoAndDetailsByBillNo(String organSign, String billNo);

    /**
     * 修改入库单
     *
     * @param dto
     * @return
     */
    ResultVO updateWarehousingBill(PurchaseStoreWarehousingBillInfoDto dto);

    /**
     * 修改入库单
     *
     * @param dto
     * @return
     */
    ResultVO updateWarehousingBillV2(PurchaseStoreWarehousingBillInfoDto dto);

    /**
     * 修改入库单状态
     *
     * @param organSign
     * @param id
     * @param warehousingState
     */
    void updateWarehousingBillStatus(String organSign, Long id, String warehousingState);

    //给GSP管理提供的报表接口 start

    /**
     * GSP分页查询入库单列表
     *
     * @param model
     * @return
     */
    ResultVO<PageInfo<PurchaseStoreWarehousingBillInfoDto>> selectByConditionGsp(PurchaseStoreWarehousingBillQueryDto model);

    /**
     * GSP-入库记录-商品汇总-分页
     *
     * @param model
     * @return
     */
    ResultVO<PageInfo<PurchaseStoreWarehousingBillDetailDto>> selectDetailsByConditionGsp(PurchaseStoreWarehousingBillQueryDto model);

    /**
     * GSP-入库记录-商品汇总
     *
     * @param model
     * @return
     */
    ResultVO<List<PurchaseStoreWarehousingBillDetailDto>> selectDetailsByConditionGspList(PurchaseStoreWarehousingBillQueryDto model);

    //给GSP管理提供的报表接口 end

    /**
     * 报表-门店采购明细
     *
     * @param model
     * @return
     */
    ResultVO<PageInfo<PurchaseStoreWarehousingBillDetailDto>> getStoreWarehousingDetailsByQuery(PurchaseStoreWarehousingBillQueryDto model);

    /**
     * 报表-门店采购明细合计
     * @param model
     * @return
     */
    ResultVO<BillSummaryDto> getStorePurchaseBillSummary(PurchaseStoreWarehousingBillQueryDto model);

    /**
     * 查询入库单信息列表
     *
     * @param queryParam
     * @return
     */
    ResultVO<PageInfo<PurchaseStoreWarehouseInfoListDto>> pageListDetail(SelPurchaseStoreWarehouseInfoListDto queryParam);


    /**
     * 门店入库单回退
     * @param returnBillDto
     * @return
     */
    ResultVO returnBill(ReturnBillDto returnBillDto) ;
    /**
     * 连锁总部查询门店入库单商品明细列表（分页）
     * @param model
     * @return
     */
    ResultVO<PageInfo<PurchaseStoreWarehousingBillDetailDto>> selectPurchaseStoreWarehousingProductDetail(PurchaseStoreWarehousingBillQueryDto model);
    /**
     * 连锁总部查询门店入库单汇总数据
     * @param model
     * @return
     */
    ResultVO<PurchaseStoreWarehousingBillDetailDto> selectTotalStorageAmount(PurchaseStoreWarehousingBillQueryDto model);


    ResultVO<BillSummaryDto> selectInfoSummary(PurchaseStoreWarehousingBillQueryDto queryDto);

    ResultVO<BillSummaryDto> selectDetailSummary(String organSign, String billNo);

    ResultVO handInWarehouse(String organSign, String billNo);

    ResultVO<List<PurchaseStoreWarehousingBillInfoDto>> selectRemoteReceiveApplyList(String organSign, List<String> billNos);


    /**
     * 根据机构id和电商订单编号来获取门店入库单
     * @param organSign
     * @param ecOrderNo
     * @return
     */
    ResultVO<PurchaseStoreWarehousingBillInfoDto> selectStoreWarehousingBillInfoByOrganSignAndEcOrderNo(String organSign, String ecOrderNo);


    /**
     * 根据机构号和单据编号查询入库信息
     * @param organSign
     * @param billNo
     * @return
     */
    ResultVO<List<PurchaseStoreWarehousingBillDetailDto>> selectDetailsByBillNoForceMaster(String organSign, String billNo);


    ResultVO findInventoryStockByOrganSignAndEcOrderNo(String organSign, String ecOrderNo);

    ResultVO syncHistoryBatchDetail(String organSign,List<String> billNos);

    ResultVO syncHistoryBatchDetail(String organSign, Long startId,Long maxId);

    ResultVO storeWarehousingSaveHeadRejectBills(PurchaseStoreWarehousingBillInfoDto infoDto) throws BusinessException;

}