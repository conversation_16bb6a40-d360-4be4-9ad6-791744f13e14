package com.xyy.saas.purchase.core.api;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.*;
import com.xyy.saas.purchase.core.dto.purchase.PurchasePullSaleOutDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseSaasSaleOutDto;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: DUKAI
 * Date: 2019/08/21
 * Time: 下午1:45
 * Explain:
 */
public interface ProcessSaleApi {

    /**
     * 出库列表
     * @param organSign
     */
    ResultVO processSaleOut(String organSign, String billNo, Integer page,Integer row);

    /**
     * 未出库列表
     * @param organSign
     */
    ResultVO unProcessSaleOut(String organSign, Integer page,Integer row,String startDate);


    /**
     * 根据订单号查询出库详情
     * @param organSign
     */
    ResultVO processSaleOutDetails(String name,String businessId,String organSign);

    /**
     * 修改标准库id
     * @param dto
     * @return
     */
    ResultVO updateProductCode(ProcessUpdateProductCodeDto dto);

    /**
     * 同步获取神农,时空销售出库数据
     * @param organSign
     */
    ResultVO save(SavePurchaseDto dto, String organSign);

    /**
     * 修改提取状态
     */
    ResultVO editPullYn(ProcessUpdatePullYnDto dto);

    /**
     * 物理删除药帮忙订单
     */
    ResultVO deleteOrder(ProcessUpdatePullYnDto dto);

    /**
     * 获取Apollo配置
     */
    String getApolloConfig();

    /**
     * 保存
     * @param saleOutDto
     * @return
     */
    ResultVO save(PurchaseSaasSaleOutDto saleOutDto);

    /**
     * 查询第三方出库单
     * @return
     */
    ResultVO selectByCondition(SaleOutPoDto dto);

    /**
     * 逻辑删除
     * @param id
     * @return
     */
    ResultVO deleteById(Long id);

    /**
     * 保存第三方销售出库单
     * @param dto 参数
     * @return
     */
    ResultVO saveSaleOutBillForOrg(PurchasePullSaleOutDto dto);

    /**
     * 删除拉取第三方出库单起始时间
     * @return
     */
    ResultVO deletePullSaleOutStartTime(List<String> fields);

    /**
     * 查询所有机构的拉取开始时间
     * @return
     */
    ResultVO selectAllOrgPullSaleOutStartTime();

    /**
     * 首页查询一键入库信息
     * @param organSign
     * @return
     */
    ResultVO queryOneStepInInfo(String organSign);

    /**
     * 保存单条入库数据
     * @param saleOutDetailPoDto
     * @return
     */
    ResultVO saveReceiveNumber(SaleOutDetailPoDto saleOutDetailPoDto);

    /**
     * 查询待提取药帮忙订单
     * @param saleOutPoDto
     * @return
     */
    ResultVO<PageInfo<PurchaseSaasSaleOutDto>> queryToBeExtractedYBMList(SaleOutPoDto saleOutPoDto);

    ResultVO submitOneStepInV3(String organSign,List<PurchaseSaasSaleOutDto> saleOutPoDtoList,String userId,String ip);


    /**
     *
     * @param organSign 机构号
     * @param platform 三方平台（平台id）
     * @param orderNos 订单号列表
     * @return 已存在的三方单号
     */
    List<String> selectOrderNosIfExist(String organSign,String platform,List<String> orderNos);

    /**
     * 批量保存待入库单
     * @param SaleOuts
     * @return
     */
    ResultVO batchSaveSaleOuts(List<SaleOutPoDto> SaleOuts);

    /**
     * 查询待提取三方平台（excel导入）订单
     * @param saleOutPoDto
     * @return
     */
    ResultVO<PageInfo<PurchaseSaasSaleOutDto>> queryToBeExtractedExcelList(SaleOutPoDto saleOutPoDto);


    /**
     * 删除待提取三方平台（excel导入）订单
     * @param id
     * @return
     */
    ResultVO deleteSaleOut(Long id);

    /**
     * 根据id查询待入库详情
     * @param id
     */
    ResultVO selectSaleOutDetail(Long id);

    ResultVO selectLatestOrderSource(String organSign);
}
