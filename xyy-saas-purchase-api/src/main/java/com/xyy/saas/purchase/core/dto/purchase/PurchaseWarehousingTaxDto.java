package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class PurchaseWarehousingTaxDto implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 机构编号
     */
    private String organSign;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 商品内码
     */
    private String productCode;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品批次
     */
    private String productBatchNo;

    /**
     * 进项税率
     */
    private BigDecimal incomeTaxRate;
}
