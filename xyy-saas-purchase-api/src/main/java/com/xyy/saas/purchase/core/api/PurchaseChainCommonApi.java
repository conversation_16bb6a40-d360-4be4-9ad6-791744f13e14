package com.xyy.saas.purchase.core.api;

import com.xyy.saas.common.util.ResultVO;

/**
 * 类名称:PurchaseChainCommonApi
 * 类描述:刷新采购单详情表商品分类、储存条件、验收方式的历史数据
 * 创建人:sunyue
 * 创建时间:2020/7/15 9:59
 */
public interface PurchaseChainCommonApi {


    /**
     *
     * @param organSign
     * @param billNo
     * @param billType
     * @return
     */
    ResultVO refreshPurchseData(String organSign, String billNo, String billType);


    /**
     * 刷数据 - 采购单据的进项税率
     * @param organSign
     * @param billNo
     * @param billType
     * @return
     */
    ResultVO refreshPurchaseIncomeTaxRate(String organSign, String billNo, String billType);

}
