package com.xyy.saas.purchase.core.dto.purchase;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class SaasPurchaseBillDetailDto implements Serializable{

    private static final long serialVersionUID = 8260320967878247185L;
    private Integer id;

    private String guid;
    
    /**
     * 解决jqgrid的id绑定问题
     */
    private Integer idTemp;

    private Integer purchaseBillId;

    private String productCode;
    
    private String productCodeText; 	//仅前端显示使用

    private String productName;

    private String productBatchNo;

    private Date productDate;

    private Date productExpiryDate;

    private String specifications;

    private String packingUnit;

    private String manufacturer;

    private BigDecimal productAmount;

    private BigDecimal stockNum;

    private BigDecimal productTaxPrice;

    private BigDecimal productTaxPriceSum;

    private BigDecimal discount;

    private BigDecimal productDiscountTaxPrice;

    private BigDecimal productDiscountTaxPriceSum;

    private BigDecimal productRetailPrice;

    private BigDecimal productMemberPrice;

    private BigDecimal productRejectionAmount;

    private BigDecimal productSampleAmount;

    private BigDecimal productQualifiedAmount;

    private BigDecimal productUnqualifiedAmount;

    private String productUnqualifiedRemark;

    private String productReturnReason;

    private String remark;

    private Boolean reviewConclusion;
    
    /**
     * 解决jqgrid不能传递select的value问题
     */
    private String reviewConclusionText;

    private String approvalNumber;

    private String productOriginAddress;

    private String createUser;

    private Date createTime;

    private String updateUser;

    private Date updateTime;

    private Boolean yn;

    private String organSign;

    private String baseVersion;
    /**
     * 采购退出单使用，原单数量
     */
    private BigDecimal oldAmount;
    
    /**
     * 采购退出单使用，已退出量
     */
    private BigDecimal oldRejectAmount;
    
    /**
     * 采购退出单使用, 库存状态
     */
    private String stockStatusTest;
    /**
     * 库存数量，退出开票单使用
     */
    private BigDecimal stockAmount;
    
    /**
     * 1：不可以被下游流程提取；当bill_type类型为04的时候，表示是否可以被采购退出开票提取
     */
    private Byte cannotFind;

    //新增，查询使用
    private String supplier_name;

    private String lastThreeTaxPrice;

    private List<String> supplierList;
    private String pharmacyPref;// 供应商标识

    private String productPharmacyPref; // 商品编号

    private Integer businessScope;// 经营范围

    private Byte status;


    private String productValidity;

    private Integer ykqPrice;   //修改零售价与宜块钱价格比较   1：宜块钱对应商品下架。0：不下架

    private String drugPermissionPerson;

    public String getDrugPermissionPerson() {
        return drugPermissionPerson;
    }

    public void setDrugPermissionPerson(String drugPermissionPerson) {
        this.drugPermissionPerson = drugPermissionPerson;
    }

    public Integer getYkqPrice() {
        return ykqPrice;
    }

    public void setYkqPrice(Integer ykqPrice) {
        this.ykqPrice = ykqPrice;
    }

    public String getProductPharmacyPref() {
        return productPharmacyPref;
    }

    public void setProductPharmacyPref(String productPharmacyPref) {
        this.productPharmacyPref = productPharmacyPref;
    }

    public String getProductValidity() {
        return productValidity;
    }

    public void setProductValidity(String productValidity) {
        this.productValidity = productValidity;
    }


    public BigDecimal getStockNum() {
        return stockNum;
    }

    public void setStockNum(BigDecimal stockNum) {
        this.stockNum = stockNum;
    }

    /**
     * 最后一次供应商名称
     */
    private String lastSupplierName;
    /**
     * 最后一次供应商编码
     */
    private String lastSupplierNo;

    private Long productId;//新增返回商品主键id
    private BigDecimal retailPrice;//商品零售价，给前端返回统一零售价字段参数

    /**
     * 最近入库价
     */
    private BigDecimal lastPrice;

    /**
     * 处理措施
     */
    private String productTreatment;

    /**
     * 采购订单供应商
     */
    private String supplierNo;
    /**
     * 采购订单供应商名称
     */
    private String supplierName;

    /**
     * 货位
     **/
    private String positionNo;

    /**
     * 货位名称
     **/
    private String positionName;

    /**
     * 货位禁用状态
     **/
    private Integer positionUsed;

    /**
     * 收货数量
     **/
    private BigDecimal receivingAmount;

    /**
     *  采购数量
     **/
    private BigDecimal productOrderAmount;


    private BigDecimal orderAmountSum;// 订单采购总数量

    private BigDecimal productAmountSum; // 入库单入库总数量



    private String providerScope;//供应商经营范围

    /**
     *  标准库id
     **/
    private Long standardLibraryId;

    /**
     *   商品新分类
     **/
    private Integer systemType;

    /**
     *   是否是中药饮片  1：是 0：否
     **/
    private Integer specialType;

    /**
     *   是是否启用：0---未启用   1---启用
     **/
    private Integer used;

    /**
     * 以商品为维度查询条件增加
     **/
    //收货状态
    private Byte receivingState;
    //单据类型
    private String billType;
    //处理单据人员
    private String billingUser;
    //商品通用名称
    private String commonName;
   //商品编码
   private String pref;
    //助记码
    private String mnemonicCode;
    //条形码
    private String barCode;
    //开票日期（采购日期）
    private Date billTime;
    /**
     * 单据编号
     */
    private String billNo;

    private Integer page;//页码
    private Integer rows;//每页条数
    private String sidx;
    private String sord;

    private List<Integer> detailIds; // 详情页选中的id

    public BigDecimal getProductAmountSum() {
        return productAmountSum;
    }

    public void setProductAmountSum(BigDecimal productAmountSum) {
        this.productAmountSum = productAmountSum;
    }

    public BigDecimal getOrderAmountSum() {
        return orderAmountSum;
    }

    public void setOrderAmountSum(BigDecimal orderAmountSum) {
        this.orderAmountSum = orderAmountSum;
    }

    public List<Integer> getDetailIds() {
        return detailIds;
    }

    public void setDetailIds(List<Integer> detailIds) {
        this.detailIds = detailIds;
    }

    public Integer getPositionUsed() {
        return positionUsed;
    }

    public void setPositionUsed(Integer positionUsed) {
        this.positionUsed = positionUsed;
    }

    public String getPositionName() {
        return positionName;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public Integer getUsed() {
        return used;
    }

    public void setUsed(Integer used) {
        this.used = used;
    }

    public Integer getSpecialType() {
        return specialType;
    }

    public void setSpecialType(Integer specialType) {
        this.specialType = specialType;
    }

    public Integer getSystemType() {
        return systemType;
    }

    public void setSystemType(Integer systemType) {
        this.systemType = systemType;
    }

    public Long getStandardLibraryId() {
        return standardLibraryId;
    }

    public void setStandardLibraryId(Long standardLibraryId) {
        this.standardLibraryId = standardLibraryId;
    }

    public String getProviderScope() {
        return providerScope;
    }

    public void setProviderScope(String providerScope) {
        this.providerScope = providerScope;
    }



    public BigDecimal getReceivingAmount() {
        return receivingAmount;
    }

    public void setReceivingAmount(BigDecimal receivingAmount) {
        this.receivingAmount = receivingAmount;
    }

    public String getPositionNo() {
        return positionNo;
    }

    public void setPositionNo(String positionNo) {
        this.positionNo = positionNo;
    }

    public String getSupplierNo() {
        return supplierNo;
    }

    public void setSupplierNo(String supplierNo) {
        this.supplierNo = supplierNo;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getProductTreatment() {
        return productTreatment;
    }

    public void setProductTreatment(String productTreatment) {
        this.productTreatment = productTreatment;
    }

    private String agentName;

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public BigDecimal getLastPrice() {
        return lastPrice;
    }

    public void setLastPrice(BigDecimal lastPrice) {
        this.lastPrice = lastPrice;
    }


    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getLastSupplierName() {
        return lastSupplierName;
    }

    public void setLastSupplierName(String lastSupplierName) {
        this.lastSupplierName = lastSupplierName;
    }

    public String getLastSupplierNo() {
        return lastSupplierNo;
    }

    public void setLastSupplierNo(String lastSupplierNo) {
        this.lastSupplierNo = lastSupplierNo;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Integer getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(Integer businessScope) {
        this.businessScope = businessScope;
    }

    public String getSupplier_name() {
        return supplier_name;
    }

    public void setSupplier_name(String supplier_name) {
        this.supplier_name = supplier_name;
    }

    public String getLastThreeTaxPrice() {
        return lastThreeTaxPrice;
    }

    public void setLastThreeTaxPrice(String lastThreeTaxPrice) {
        this.lastThreeTaxPrice = lastThreeTaxPrice;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getPurchaseBillId() {
        return purchaseBillId;
    }

    public void setPurchaseBillId(Integer purchaseBillId) {
        this.purchaseBillId = purchaseBillId;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode == null ? null : productCode.trim();
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    public String getProductBatchNo() {
        return productBatchNo;
    }

    public void setProductBatchNo(String productBatchNo) {
        this.productBatchNo = productBatchNo == null ? null : productBatchNo.trim();
    }

    public Date getProductDate() {
        return productDate;
    }

    public void setProductDate(Date productDate) {
        this.productDate = productDate;
    }

    public Date getProductExpiryDate() {
        return productExpiryDate;
    }

    public void setProductExpiryDate(Date productExpiryDate) {
        this.productExpiryDate = productExpiryDate;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications == null ? null : specifications.trim();
    }

    public String getPackingUnit() {
        return packingUnit;
    }

    public void setPackingUnit(String packingUnit) {
        this.packingUnit = packingUnit == null ? null : packingUnit.trim();
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer == null ? null : manufacturer.trim();
    }

    public BigDecimal getProductAmount() {
        return productAmount;
    }

    public void setProductAmount(BigDecimal productAmount) {
        this.productAmount = productAmount;
    }

    public BigDecimal getProductTaxPrice() {
        return productTaxPrice;
    }

    public void setProductTaxPrice(BigDecimal productTaxPrice) {
        this.productTaxPrice = productTaxPrice;
    }

    public BigDecimal getProductTaxPriceSum() {
        return productTaxPriceSum;
    }

    public void setProductTaxPriceSum(BigDecimal productTaxPriceSum) {
        this.productTaxPriceSum = productTaxPriceSum;
    }

    public BigDecimal getDiscount() {
        return discount;
    }

    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }

    public BigDecimal getProductDiscountTaxPrice() {
        return productDiscountTaxPrice;
    }

    public void setProductDiscountTaxPrice(BigDecimal productDiscountTaxPrice) {
        this.productDiscountTaxPrice = productDiscountTaxPrice;
    }

    public BigDecimal getProductDiscountTaxPriceSum() {
        return productDiscountTaxPriceSum;
    }

    public void setProductDiscountTaxPriceSum(BigDecimal productDiscountTaxPriceSum) {
        this.productDiscountTaxPriceSum = productDiscountTaxPriceSum;
    }

    public BigDecimal getProductRetailPrice() {
        return productRetailPrice;
    }

    public void setProductRetailPrice(BigDecimal productRetailPrice) {
        this.productRetailPrice = productRetailPrice;
    }

    public BigDecimal getProductMemberPrice() {
        return productMemberPrice;
    }

    public void setProductMemberPrice(BigDecimal productMemberPrice) {
        this.productMemberPrice = productMemberPrice;
    }

    public BigDecimal getProductRejectionAmount() {
        return productRejectionAmount;
    }

    public void setProductRejectionAmount(BigDecimal productRejectionAmount) {
        this.productRejectionAmount = productRejectionAmount;
    }

    public BigDecimal getProductSampleAmount() {
        return productSampleAmount;
    }

    public void setProductSampleAmount(BigDecimal productSampleAmount) {
        this.productSampleAmount = productSampleAmount;
    }

    public BigDecimal getProductQualifiedAmount() {
        return productQualifiedAmount;
    }

    public void setProductQualifiedAmount(BigDecimal productQualifiedAmount) {
        this.productQualifiedAmount = productQualifiedAmount;
    }

    public BigDecimal getProductUnqualifiedAmount() {
        return productUnqualifiedAmount;
    }

    public void setProductUnqualifiedAmount(BigDecimal productUnqualifiedAmount) {
        this.productUnqualifiedAmount = productUnqualifiedAmount;
    }

    public String getProductUnqualifiedRemark() {
        return productUnqualifiedRemark;
    }

    public void setProductUnqualifiedRemark(String productUnqualifiedRemark) {
        this.productUnqualifiedRemark = productUnqualifiedRemark == null ? null : productUnqualifiedRemark.trim();
    }

    public String getProductReturnReason() {
        return productReturnReason;
    }

    public void setProductReturnReason(String productReturnReason) {
        this.productReturnReason = productReturnReason == null ? null : productReturnReason.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Boolean getReviewConclusion() {
        return reviewConclusion;
    }

    public void setReviewConclusion(Boolean reviewConclusion) {
        this.reviewConclusion = reviewConclusion;
    }

    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber == null ? null : approvalNumber.trim();
    }

    public String getProductOriginAddress() {
        return productOriginAddress;
    }

    public void setProductOriginAddress(String productOriginAddress) {
        this.productOriginAddress = productOriginAddress == null ? null : productOriginAddress.trim();
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getYn() {
        return yn;
    }

    public void setYn(Boolean yn) {
        this.yn = yn;
    }


	public Integer getIdTemp() {
		if(id != null) {
			idTemp = id;
		}
		return idTemp;
	}

	public void setIdTemp(Integer idTemp) {
		this.idTemp = idTemp;
	}

	public String getReviewConclusionText() {
		if(reviewConclusion != null) {
			if(reviewConclusion) {
				reviewConclusionText = "合格";
			}else{
				reviewConclusionText = "不合格";
			}
		}
		return reviewConclusionText;
	}

	public void setReviewConclusionText(String reviewConclusionText) {
		this.reviewConclusionText = reviewConclusionText;
	}

	public BigDecimal getOldAmount() {
		return oldAmount;
	}

	public void setOldAmount(BigDecimal oldAmount) {
		this.oldAmount = oldAmount;
	}

	public BigDecimal getOldRejectAmount() {
		return oldRejectAmount = oldRejectAmount == null ? new BigDecimal(0) : oldRejectAmount;
	}

	public void setOldRejectAmount(BigDecimal oldRejectAmount) {
		this.oldRejectAmount = oldRejectAmount;
	}

	public String getStockStatusTest() {
		return stockStatusTest;
	}

	public void setStockStatusTest(String stockStatusTest) {
		this.stockStatusTest = stockStatusTest;
	}

	public BigDecimal getStockAmount() {
		return stockAmount;
	}

	public void setStockAmount(BigDecimal stockAmount) {
		this.stockAmount = stockAmount;
	}

	public Byte getCannotFind() {
		return cannotFind;
	}

	public void setCannotFind(Byte cannotFind) {
		this.cannotFind = cannotFind;
	}

	public String getProductCodeText() {
		if(productCode != null) {
			productCodeText = productCode;
		}
		
		return productCodeText;
	}

	public void setProductCodeText(String productCodeText) {
		this.productCodeText = productCodeText;
	}

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public List<String> getSupplierList() {
        return supplierList;
    }

    public void setSupplierList(List<String> supplierList) {
        this.supplierList = supplierList;
    }

    public String getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(String baseVersion) {
        this.baseVersion = baseVersion;
    }

    public String getPharmacyPref() {
        return pharmacyPref;
    }

    public void setPharmacyPref(String pharmacyPref) {
        this.pharmacyPref = pharmacyPref;
    }

    public Long getProductId() { return productId; }

    public void setProductId(Long productId) { this.productId = productId; }

    public BigDecimal getRetailPrice() { return retailPrice; }

    public void setRetailPrice(BigDecimal retailPrice) { this.retailPrice = retailPrice; }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public BigDecimal getProductOrderAmount() {
        return productOrderAmount;
    }


    public void setProductOrderAmount(BigDecimal productOrderAmount) {
        this.productOrderAmount = productOrderAmount;
    }

    public Byte getReceivingState() {
        return receivingState;
    }

    public void setReceivingState(Byte receivingState) {
        this.receivingState = receivingState;
    }

    public String getBillType() {
        return billType;
    }

    public void setBillType(String billType) {
        this.billType = billType;
    }

    public String getBillingUser() {
        return billingUser;
    }

    public void setBillingUser(String billingUser) {
        this.billingUser = billingUser;
    }

    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    public String getMnemonicCode() {
        return mnemonicCode;
    }

    public void setMnemonicCode(String mnemonicCode) {
        this.mnemonicCode = mnemonicCode;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public Date getBillTime() {
        return billTime;
    }

    public void setBillTime(Date billTime) {
        this.billTime = billTime;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public String getSidx() {
        return sidx;
    }

    public void setSidx(String sidx) {
        this.sidx = sidx;
    }

    public String getSord() {
        return sord;
    }

    public void setSord(String sord) {
        this.sord = sord;
    }
}