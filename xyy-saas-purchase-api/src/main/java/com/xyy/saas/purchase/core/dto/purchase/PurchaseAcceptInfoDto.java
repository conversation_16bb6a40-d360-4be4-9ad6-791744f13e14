package com.xyy.saas.purchase.core.dto.purchase;

import com.google.common.collect.Lists;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class PurchaseAcceptInfoDto extends PurchaseCommonDto implements Serializable {

    private static final long serialVersionUID = -8131179927028905423L;

    /**
     * 验收日期
     */
    private Date checkTime;

    /**
     * 验收日期字符串
     */
    private String checkTimeStr;

    /**
     * 收货日期
     */
    private Date receiveTime;

    /**
     * 收货日期字符串
     */
    private String receiveTimeStr;

    /**
     * 到货日期
     */
    private Date arrivalTime;

    /**
     * 到货日期字符串
     */
    private String arrivalTimeStr;

    /**
     * 起始日期
     */
    private String receiveStartTime;

    /**
     * 结束日期
     */
    private String receiveEndTime;

    /**
     * 验收状态筛选 0-未验收; 1-已验收; 2-暂存
     */
    private String queryAcceptState;

    /**
     *
     * 商品种类
     */
    private Integer productKind;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商编号
     */
    private String supplierNo;

    /**
     *
     * 验收员
     */
    private String checkUser;

    /**
     *
     * 收货员
     */
    private String receiveUser;

    /**
     *
     * 验收员
     */
    private String checkUserName;

    /**
     *
     * 收货员
     */
    private String receiveUserName;

    /**
     *
     * 采购单含税总金额
     */
    private BigDecimal taxAmountSum;

    /**
     *
     * 整单折扣
     */
    private BigDecimal discount;

    /**
     *
     * 折后金额
     */
    private BigDecimal priceAfterDiscount;

    /**
     *
     *折扣金额
     */
    private BigDecimal priceDiscounted;

    /**
     *
     * 单据状态: 01-暂存；02-提交
     */
    private String submitStatus;

    /**
     *
     * 验收状态，0-待验收; 1-已验收, 9-暂存
     */
    private Boolean acceptState;

    /**
     * 验收状态，用于展示
     */
    private String acceptStateStr;

    /**
     *
     * 备注
     */
    private String remarks;

    /**
     *
     *创建人
     */
    private String createUser;

    /**
     *
     * 创建时间
     */
    private Date createTime;

    /**
     *
     * 修改人
     */
    private String updateUser;

    /**
     *
     * 修改时间
     */
    private Date updateTime;

    /**
     *
     * 逻辑删除 1-未删除 0-已删除
     */
    private Boolean yn;

    /**
     *
     * 操作版本号
     */
    private String baseVersion;

    /**
     * 经营模式
     */
    private String bizModel;

    private String beginTime;

    private String endTime;

    /**
     * 验收单详情
     */
    private List<PurchaseAcceptDetailDto> details = Lists.newArrayList();

}