package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 对接物流开放平台-参数
 */
@Data
public class PurchaseShipmentLogisticsPlaform {
    private String organSign;
    /**
     * 智慧脸订单编码
     */
    private String thirdPartyOrderCode;
    /**
     * 订单备注
     */
    private String orderRemarks;
    /**
     * 接收人手机号
     */
    private String receiverPhone;
    /**
     * 接收人姓名
     */
    private String receiverName;
    /**
     * 收货地址
     */
    private String receiverAddress;
    /**
     * 总门店-客户编码
     */
    private String customerCode;
    /**
     * 	机构编码
     */
    private String orgCode;
    /**
     * 连锁门店名称
     */
    private String storeName;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 	市编码
     */
    private String cityCode;
    /**
     * 区编码
     */
    private String areaCode;
    /**
     * 乡/街道
     */
    private String streetCode;
    /**
     * 商品详情信息
     */
    private List<PurchaseOrderDetailLogisticsPlaform> detailList;

    /**
     * 自定义参数 json串
     */
    private String tripartiteParameter;

    /**
     * 创建时间
     */
    private Date orderTime;
    /**
     * 1 :标准订单 2 : 共仓订单 神农，saas侧对应purchaseOrderModel
     */
    private String orderType;
    /**
     * 给神农来标识拆单
      */
    private Long parentOrderId;
}
