package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * ClassName: LogisticsPlatformQueryDetailDto
 * Function:  委托配送共仓 详情调用erp  入参
 * Date:      2020/8/21 16:44
 * author     sunyue
 */
@Getter
@Setter
public class LogisticsPlatformQueryDetailDto {
    /**
     * 商品标准库id
     */
    private String standardProductId;
    /**
     * 第三方商品内码
     */
    private String thirdpartyProductCode;
    /**
     * 商品小包装数量（采购数量）
     */
    private Long productPackCountSmall;
    /**
     * 商品税率
     */
    private BigDecimal productEntryTax;
    /**
     * 商品含税单价 支持六位小数
     */
    private BigDecimal productContainTaxPrice;
    /**
     * 商品备注(50个汉字)
     */
    private String productRemark;
}
