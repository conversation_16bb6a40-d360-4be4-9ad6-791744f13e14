package com.xyy.saas.purchase.core.api.purchase;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.purchase.PurchasePlanDetailQueryDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchasePlanInfoDetailDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchasePlanInfoDto;
import com.xyy.saas.purchase.core.dto.requiregoods.PurchaseProductInfoQueryDto;

import java.util.List;

/**
 * <AUTHOR>
 * @annotation:采购计划单API`
 * @create 2018-09-18 17:19
 **/
public interface PurchaseChainPlanApi {

    /**
     * 查询采购计划单列表
     * @param purchasePlanDto
     * @return
     */
    ResultVO<PageInfo<PurchasePlanInfoDto>> selectPurchasePlanList(PurchasePlanInfoDto purchasePlanDto);

    /**
     * 保存或编辑采购计划单 - new
     * @param dto
     * @return
     */
    ResultVO savePurchasePlan(PurchasePlanInfoDto dto);

    /**
     * 删除采购计划
     */
    ResultVO deletePlan(Integer id);

    /**
     * 创建采购计划单-添加商品页面
     * @param purchaseProductInfoQueryDto
     * @return
     */
    ResultVO selectProducts(PurchaseProductInfoQueryDto purchaseProductInfoQueryDto);

    /**
     * 根据机构号和单据编号查询单据信息
     * @param organSign
     * @param billNo
     * @return
     */
    ResultVO<PurchasePlanInfoDto> selectPlanInfo(String organSign, String billNo);

    /**
     * 根据机构号和单据编号查询单据详情信息
     * @param organSign
     * @param billNo
     * @return
     */
    ResultVO<List<PurchasePlanInfoDetailDto>> selectPlanDetailInfo(String organSign, String billNo);

    /**
     * 采购计划单启用
     * @param organSign
     * @param billNo
     * @return
     */
    ResultVO resetOpen(String organSign, String billNo);


    /**
     * 查询采购计划单列表-单据汇总
     * @param purchasePlanDto
     * @return
     */
    ResultVO<PageInfo<PurchasePlanInfoDto>> queryChainPurchasePlanPage(PurchasePlanInfoDto purchasePlanDto);

    /**
     * 查询采购计划单列表-商品汇总
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<PurchasePlanInfoDetailDto>> queryChainPurchasePlanDetailPage(PurchasePlanDetailQueryDto queryDto);

    /**
     * 根据单号查采购计划详情
     * @param billNo
     * @return
     */
    ResultVO<PurchasePlanInfoDto> getPurchasePlanInfoDetailByBillNo(String organSign, String billNo);

    /**
     * 查询采购计划单商品明细分页列表（分页）
     * @param dto
     * @return
     */
    ResultVO<PageInfo<PurchasePlanInfoDetailDto>> selectPurchasePlanDetailList(PurchasePlanInfoDto dto);
    /**
     * 查询采购计划单汇总字段信息
     * @param dto
     * @return
     */
    ResultVO<PurchasePlanInfoDetailDto> selectPurchasePlanSumCount(PurchasePlanInfoDto dto);
}

