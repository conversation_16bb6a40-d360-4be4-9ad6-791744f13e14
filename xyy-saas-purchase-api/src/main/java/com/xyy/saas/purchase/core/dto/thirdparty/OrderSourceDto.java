package com.xyy.saas.purchase.core.dto.thirdparty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/01/29 13:28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderSourceDto implements Serializable {

    /**
     * 来源渠道code
     */
    private Integer sourceCode;


    /**
     * 来源渠道名称
     */
    private String sourceName;

}
