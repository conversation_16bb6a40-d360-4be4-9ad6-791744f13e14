package com.xyy.saas.purchase.core.api.returngoods;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.returngoods.PurchaseExitLibraryApplyDetailDto;
import com.xyy.saas.purchase.core.dto.returngoods.PurchaseExitLibraryApplyInfoDto;
import com.xyy.saas.purchase.core.dto.returngoods.PurchaseStoreExitLibraryApplyInfoDto;

import javax.xml.transform.Result;
import java.util.List;

/**
 * @Author: <PERSON> Tin<PERSON>
 * @CreateTime: 2020-04-05 17:56
 * @Description: 供应商退货申请单相关API
 */
public interface PurchaseExitLibraryApplyInfoApi {
    /**
     * 查询退货申请单列表
     * @param purchaseExitLibraryApplyInfoDto
     * @return
     */
    ResultVO<PageInfo<PurchaseExitLibraryApplyInfoDto>> selectPurchaseExitLibraryApplyInfoList(PageInfo pageInfo, PurchaseExitLibraryApplyInfoDto purchaseExitLibraryApplyInfoDto);

    /**
     * 根据机构号，单据编号查询单据是否存在
     * @param purchaseExitLibraryApplyInfoDto
     * @return
     */
    ResultVO<PurchaseExitLibraryApplyInfoDto> selectPurchaseExitLibraryApplyInfo(PurchaseExitLibraryApplyInfoDto purchaseExitLibraryApplyInfoDto);

    /**
     * 删除退出申请单
     * @param organSign
     * @param billNo
     * @return
     */
    ResultVO deletePurchaseExitLibraryApplyInfo(String organSign, String billNo);

    /**
     * 查询退货申请单详情列表
     * @param organSign
     * @param billNo
     * @return
     */
    ResultVO<List<PurchaseExitLibraryApplyDetailDto>> selectPurchaseExitLibraryApplyDetailList(String organSign, String billNo);

    /**
     * 保存退货申请单
     * @param applyInfoDto
     */
    ResultVO savePurchaseExitLibraryApplyInfo(PurchaseExitLibraryApplyInfoDto applyInfoDto);

    /**
     * 修改退货申请单
     * @param applyInfoDto
     */
    ResultVO updatePurchaseExitLibraryApplyInfo(PurchaseExitLibraryApplyInfoDto applyInfoDto);

}
