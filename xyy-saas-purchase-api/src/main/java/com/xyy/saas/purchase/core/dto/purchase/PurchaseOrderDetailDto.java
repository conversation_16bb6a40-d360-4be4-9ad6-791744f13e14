package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class PurchaseOrderDetailDto implements Serializable {

    private Integer id;


    private String organSign;


    private String billNo;
    private BigDecimal productRetailPrice;
    private BigDecimal productMemberPrice;
    private BigDecimal retailPrice;//商品零售价，给前端返回统一零售价字段参数
    private Long productId;//新增返回商品主键id

    // 上市许可持有人
    private String drugPermissionPerson;
    // 生产许可证号
    private String productLicenseRecordNo;
    // 注册证号
    private String registerCertificateNo;
    private String registerLicenseNumber;
    /**
     * 采购订单供应商
     */
    private String supplierNo;
    /**
     * 采购订单供应商名称
     */
    private String supplierName;
    /**
     * 商品内码
     */
    private String productCode;


    private String productPharmacyPref;


    private String productName;

    /**
     * 库存数量
     */
    private BigDecimal productAmount;
    private BigDecimal stockAmount;

    private String commonName;


    private String specifications;


    private String packingUnit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column saas_purchase_order_detail.manufacturer
     *
     * @mbg.generated Thu Apr 02 19:43:23 CST 2020
     */
    private String manufacturer;

    /**
     *  采购数量
     */
    private BigDecimal orderAmount;
    private BigDecimal amount;
    /**
     * 收货数量 取采购入库单入库数量
     */
    private BigDecimal receivingAmount;

    private BigDecimal productTaxPrice;


    private BigDecimal productTaxPriceSum;


    private String remarks;


    private String createUser;

    /**
     * 采购员id
     */
    private String purchaseUser;

    /**
     * 采购员姓名
     */
    private String purchaseName;


    private Date createTime;

    private String createTimeStr;


    private String updateUser;


    private Date updateTime;


    private Boolean yn;


    private Integer baseVersion;

    //产地
    private String productPlace;

    //批准文号
    private String approvalNumber;

    /** 上市许可证持有人字段 */
    private String permitOwner;

    /**
     * 剂型
     */
    private String dosageForm;
    /**
     * 验收方式  1:常规药品 2：冷链药品 3:中药材 4：中药饮片
     */
    private Integer checkMode;
    /**
     * 商品经营范围
     */
    private Integer businessScope;


    /**
     *  标准库id
     **/
    private String standardLibraryId;

    /**
     *  采购标准库id
     **/
    private String purchaseStandardLibraryId;

    /**
     * 生产许可证编号
     */
    private String productionLicenseNumber;

    // 供货者 （对应供应商管理 销售人员）
    private String supplyUserName;
    /**
     * 小药药库存数量
     */
    private BigDecimal ybmStockNumber;
    /**
     * 最近有效期至
     */
    private String productLatelyExpiryStr;
    private Date productLatelyExpiry;
    /**
     * 最远有效期至
     */
    private String productFurthestExpiryStr;
    private Date productFurthestExpiry;
    /**
     * 历史含税价
     */
    private BigDecimal lastTaxPrice;
    /**
     * 进项税率 %
     */
    private BigDecimal incomeTaxRate;
    /**
     * 加点
     */
    private  BigDecimal raise;
    /**
     * 加点后含税价
     */
    private BigDecimal raiseProductTaxPrice;
    /**
     * 加点后含税金额
     */
    private BigDecimal raiseProductTaxPriceSum;

    /**
     * 总部采购价
     */
    private BigDecimal headquartersPurchasePrice;
    /**
     * 上级单据编号
     */
    private String parentBillNo;
    /**
     * 单据状态: 01-暂存；02-提交
     */
    private String submitStatus;

    /**
     * 开票员 页面为采购员
     */
    private String billingUser;
    /**
     * 采购日期
     */
    private Date billTime;
    /**
     * 要货门店
     */
    private String storeOrganSign;
    /**
     * 要货门店名称
     */
    private String storeOrganSignName;

    /**
     *  采购订单模式 1标准（B2B无仓） 2共仓（B2C无仓单，宜块钱）
     */
    private Byte purchaseOrderModel;
    /**
     * 开票员名称 页面为采购员
     */
    private String billingUserName;

    /**
     * 采购日期字符串
     */
    private String billTimes;
    /**
     * 采购订单模式 名称
     */
    private String purchaseOrderModelName;

    /**
     * 采购状态，0-未采购 1-正常入库已采购 2-一步入库已采购
     */
    private Integer purchaseState;
    /**
     * 状态文字
     */
    private String purchaseStateText;

    /**
     * 第三方商品编码
     */
    private String thirdPartyProductCode;

    /**
     * 第三方商品包装比例
     */
    private String thirdPartyProductUniteRate;

    /**
     * 渠道ID
     */
    private String channelId;
}