package com.xyy.saas.purchase.core.api.purchase;

import java.util.List;

/**
 * 采购用户相关的Api
 */
public interface PurchaseUserApi {
    /**
     * 查单体所有创建人的员工id
     * @param organSign
     * @return
     */
    List<Integer> queryEmployeeIdsFromPurchasePlan(String organSign);

    /**
     * 查询单体单据的操作人员
     * @param organSign
     * @param billType
     * @return
     */
    List<Integer> queryEmployeeIdsFromPurchaseBillInfo(String organSign, String billType);

    List<Integer> queryEmployeeIdsFromRetrieveBillInfo(String organSign, String billType);
}
