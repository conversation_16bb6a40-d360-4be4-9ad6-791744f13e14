package com.xyy.saas.purchase.core.api;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @annotation:采购计划单API
 * @create 2018-09-18 17:19
 **/
public interface PurchasePlanApi {
    /**
     * 查询采购计划列表
     */
    ResultVO<PageInfo<SaasPurchasePlanDto>> getList(SaasPurchasePlanDto saasPurchasePlanDto);
    /**
     * 查询采购计划单
     */
    ResultVO<List<SaasPurchasePlanDetailDto>> getDetailById(Integer planId, String isProductHidden, String organSign);

    /**
     * 分页查询导入/导出成功商品详情集合
     * @return
     */
    ResultVO<PageInfo<PurchasePlanImportOrExportDetailDto>> getImportProductDetailList(PurchasePlanImportOrExportDetailDto detailDto);
    /**
     * 保存采购计划单-POS
     */
    ResultVO addPurchasePlan(List<SaasPurchasePlanDetailDto> list, String userName, String organSign,String remark,String receivingPlanDate,String planNo,String createUser,String createTime,String flag);

    /**
     * 保存采购计划单
     */
    ResultVO addPurchasePlan(List<SaasPurchasePlanDetailDto> list, String userName, String organSign,String remark,String receivingPlanDate,String planNo,String createUser,String createTime,String flag, Integer orderMedicineType);
    /**
     * 编辑采购计划单
     */
    ResultVO updatePurchasePlan(List<SaasPurchasePlanDetailDto> list, String userName, String organSign,String remark,String receivingPlanDate,String planNo,String createUser,String createTime,String flag);
    /**
     * 编辑采购计划单
     */
    ResultVO updatePurchasePlan(List<SaasPurchasePlanDetailDto> list, String userName, String organSign,String remark,String receivingPlanDate,String planNo,String createUser,String createTime,String flag, Integer orderMedicineType);
    /**
     * 完成采购计划单
     */
    ResultVO completePurchasePlan(List<SaasPurchasePlanDetailDto> list, Integer planId,String billNo, String userName, String organSign);
    /**
     * 删除采购计划
     */
    ResultVO deletePlan(Integer id);
    /**
     * 删除采购计划单
     */
    ResultVO deletePlanDetail(Integer id);
    /**
     * 跳转药帮忙搜索商品页面 获取url - 找相似
     */
    Map<String,String> getFindSameProduct(String productName, String organSign);
    /**
     *  通过主键查找采购计划信息
     */
    ResultVO<SaasPurchasePlanDto> getById(Integer id,String organSign);
    /**
     * 跳转药帮忙购物车 获取url
     */
    Map<String,String> getYbmCart(Integer planId, String organSign);
    /**
     * 获取半年之内的最后供应商、最后单价信息
     */
    Map<String,Object>  getCacheByLastProductTaxPrice(String organsign,List<String> productCodes);
    /**
     * 获取指定天销售量放到缓存中
     */
    Map<String,Object>  getCacheByOderThirtyProduct(PurchasePlanSaleDto purchasePlanSaleVo);
    /**
     * 获取推荐商品列表-根据产品日销售量建议
     */
    List<SaasPurchasePlanDetailDto>  quereyPlanProduct(PurchasePlanSaleDto purchasePlanSaleVo);
    /**
     * 获取推荐商品列表-按库存上下线推荐
     */
    List<SaasPurchasePlanDetailDto> getRecommendProductsNew(Byte isProductHidden,String organSign);
    /**
     * 获取推荐商品列表-按库存上下线推荐
     */
    List<SaasPurchasePlanDetailDto> getRecommendProductsNew(Byte isProductHidden,String organSign, Integer orderMedicineType);
    /**
     * 启用 - 复制当前采购计划，类似于copy动作
     * 区分中药单
     */
    ResultVO resertOpen (String userId,String userName,String organSign,Integer planId, List<String> planNos);

    /**
     * 启用之前检查该单
     */
    ResultVO checkPlanDetails (String userId,String userName,String organSign,Integer planId);

    /**
     * 线下采购-根据当前是否开启快速入库标识，走不同的代码逻辑
     */
    ResultVO  purchaseUnderTheLine (String userId,String userName,String organSign,Integer planId);
    /**
     * 获取采购计划订单号-由于需求变动，采购计划的订单号需要在预先生成，故提供此接口
     */
    String getPlanNo();
    /**
     * 通过订单号及机构号获取记录id
     */
    ResultVO<SaasPurchasePlanDto> getPlanIdByPlanNo(String planNo,String userId,String organSign);
    /**
     * 查询详情信息，主要是根据type和billNo来查询详情
     * @param purchaseBillInfoPoDto
     * @return
     */
    ResultVO getDetailPurchaseBillInfo(SaasPurchaseBillInfoPoDto purchaseBillInfoPoDto);


    /**
     * 已废弃不用
     * 获取一个：采购计划详情、收货复核详情、入库验收详情、采购入库详情
     * @param billNo
     */
    ResultVO getOnePurchase(String billNo, String organSign);

    /**
     * 已废弃不用
     * 获取一个单据详情：采购计划详情、收货复核详情、入库验收详情、采购入库详情
     * @param billNo
     * @param sidx
     * @param sord
     * @return
     */
    ResultVO getOnePurchaseDetail(String billNo, String sidx, String sord);

    /**
     * 已废弃不用
     * 通过ProductNoList 获取商品库存
     * @param productCode
     * @param productBatchNo
     * @return
     */
    BigDecimal getStockLotNumByProductNo(String productCode, String productBatchNo);

    /**
     * 已废弃不用
     * 获取一个单据详情：采购计划详情、收货复核详情、
     * 入库验收详情、采购入库详情，详情中不包含已经被提取的单据
     * @param billNo
     * @return
     */
    ResultVO getOnePurchaseDetailOfExtract(String billNo,String organSign);

    /**
     * 已废弃不用
     * 获取推荐商品列表 - 原按库存上下线推荐
     * @param isProductHidden
     * @param organSign
     * @return
     */

    List<SaasPurchasePlanDetailDto> getRecommendProducts(Byte isProductHidden,String organSign);

    /**
     * 已废弃不用
     */
    ResultVO  addPurchaseListPlan(Map<String,Object> map);

    PageInfo<PurchaseProductDto> getPurchaseProductListForPlanSelect(PageInfo info,Map<String,Object> param,String beginTime,
                                                                     String endTime,Integer plan,String organSign);

    PageInfo<PurchaseProductDto> getPurchaseProductListForPlanSelect(PageInfo info,Map<String,Object> param,String beginTime,
                                                           String endTime,Integer plan,String organSign, Integer orderMedicineType);



    /**
     * 采购计划单详情打印
     * @param purchaseBillDto
     * @param rowSize
     * @param identity
     * @return
     */
    Map<String, Object> purchasePlanBillPrinting(SaasPurchaseBillInfoPoDto purchaseBillDto, List<Integer> rowSize, String identity);

    /**
     * 获取一个：采购计划单
     * @param billNo
     * @param organSign
     */
    ResultVO<SaasPurchasePlanDto> getOnePurchasePlan(String organSign, String billNo);

}

