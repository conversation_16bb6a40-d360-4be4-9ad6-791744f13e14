package com.xyy.saas.purchase.core.api.returngoods;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.returngoods.PurchaseConsignExitLibraryDetailDto;
import com.xyy.saas.purchase.core.dto.returngoods.PurchaseConsignExitLibraryInfoDto;
import com.xyy.saas.purchase.core.dto.returngoods.PurchaseConsignExitLibraryQueryDto;

/**
 * 委托配送退货单
 * <AUTHOR>
 * @date 2020/7/8 11:34
 */
public interface PurchaseConsignExitLibraryApi {

    /**
     * 分页查询委托配送退货单列表信息
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<PurchaseConsignExitLibraryInfoDto>> selectByCondition(PurchaseConsignExitLibraryQueryDto queryDto);

    /**
     * 查看委托配送退货单详情
     * @param queryDto
     * @return
     */
    ResultVO<PurchaseConsignExitLibraryInfoDto> selectInfoAndDetailsByBillNo(PurchaseConsignExitLibraryQueryDto queryDto);

    /**
     * 查询开票员列表
     * @param organSign
     * @return
     */
    ResultVO getBillUsers(String organSign);

    /**
     * 修改委托配送退货单
     * @param consignExitLibraryInfoDto
     * @return
     */
    ResultVO updateConsignExitLibrary(PurchaseConsignExitLibraryInfoDto consignExitLibraryInfoDto);

    /**
     * 查询委托配送退货单处理进度
     * @param id
     * @return
     * @throws Exception
     */
    ResultVO selectConsignorExitLibraryBillStatus(Long id);

    /**
     * 修改单据状态
     * @param id
     * @param billStatus
     * @param consignFailReason
     */
    void updateBillStatus(Long id, String billStatus, String consignFailReason);

    /**
     * 分页查询委托配送退货单商品明细
     * @param queryDto
     * @return
     */
    PageInfo<PurchaseConsignExitLibraryDetailDto> selectConsignExitLibraryDetailList(PurchaseConsignExitLibraryQueryDto queryDto);
}
