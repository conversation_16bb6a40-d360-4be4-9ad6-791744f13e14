package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: <PERSON>
 * @CreateTime: 2020-06-11 18:09
 * @Description: 神农对接 - 参数详情实体类
 */
@Data
public class PurchaseOrderDetailConsignDelivery {
    /**
     * 商品编码 （标准库id）
     */
    private String standardProductId;

    /**
     * 采购数量
     */
    private BigDecimal purchaseQuantity;

    /**
     * 渠道ID: 默认 1 : 药帮忙
     */
    private String channelId;

    /**
     * 自定义参数 json串
     */
    private String tripartiteParameter;

    /**
     * 含税价
     */
    private BigDecimal productTaxPrice;
    /**
     * 门店价格
     * <AUTHOR>
     * @date 2021/3/4 10:43 上午
     */
    private BigDecimal storePrice;
}
