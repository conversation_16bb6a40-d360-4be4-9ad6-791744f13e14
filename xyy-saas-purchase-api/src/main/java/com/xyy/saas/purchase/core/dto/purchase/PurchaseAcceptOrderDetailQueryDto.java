package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class PurchaseAcceptOrderDetailQueryDto extends BasePurchaseDetailQueryDto {
    /**
     * 验收人
     */
    private String checkUser;

    /**
     * 商品系统分类
     */
    private List<Integer> systemTypes;

    /**
     * 生产厂家
     */
    private String manufacturer;
}
