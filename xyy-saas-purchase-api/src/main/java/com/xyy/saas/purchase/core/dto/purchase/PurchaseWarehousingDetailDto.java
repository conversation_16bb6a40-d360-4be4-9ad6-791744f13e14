package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * saas_purchase_warehousing_detail
 * <AUTHOR>
@Data
public class PurchaseWarehousingDetailDto implements Serializable {
    private Long id;

    /**
     * 机构编码
     */
    private String organSign;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 入库验收单编号
     */
    private String parentBillNo;

    /**
     * 入库员名称
     */
    private String warehouseUserName;

    /**
     * 入库时间
     */
    private String billTimeStr;

    /**
     * 商品内码
     */
    private String productCode;

    /**
     * 商品外码
     */
    private String productPharmacyPref;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品通用名称
     */
    private String commonName;

    /**
     * 商品规格/型号
     */
    private String specifications;

    /**
     * 产地
     */
    private String attributeSpecification;

    /**
     * 商品剂型ID
     */
    private String dosageFormName;

    /**
     * 商品生产厂家
     */
    private String manufacturer;

    /**
     * 商品产地
     */
    private String productOriginAddress;

    /**
     * 商品批准文号
     */
    private String approvalNumber;

    /**
     * 入库数量
     */
    private BigDecimal warehousingAmount;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 商品含税单价
     */
    private BigDecimal productTaxPrice;

    /**
     * 商品含税总价
     */
    private BigDecimal productTaxPriceSum;

    /**
     * 商品批号
     */
    private String productBatchNo;

    /**
     * 商品生产日期
     */
    private Date productDate;

    /**
     * 商品有效期
     */
    private Date productExpiryDate;

    /**
     * 商品生产日期
     */
    private String productDateStr;

    /**
     * 商品有效期
     */
    private String productExpiryDateStr;

    /**
     * 验收结论: 1-合格 0-锁定
     */
    private Boolean acceptConclusion;


    private String acceptConclusionStr;

    /**
     * 货区
     */
    private String cargoArea;

    /**
     * 货位
     */
    private String positionNo;

    /**
     * 货区
     */
    private String cargoAreaName;

    /**
     * 货位
     */
    private String positionNoName;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 逻辑删除 1-未删除 0-已删除
     */
    private Boolean yn;

    /**
     * 操作版本号
     */
    private String baseVersion;

    /**
     * 合格数量
     */
    private BigDecimal productQualifiedAmount;

    /**
     * 不合格数量
     */
    private BigDecimal productUnqualifiedAmount;

    private String lastSupplierNo;

    private String providerScope;

    private String lastSupplierName;

    /**
     * 批号库存数量
     */
    private BigDecimal stockAmount;

    /**
     * 拒收数量
     */
    private BigDecimal oldRejectAmount;

    /**
     * 采购数量
     */
    private BigDecimal orderAmount;

    /**
     * 不合品事项
     */
    private String  productUnqualifiedRemark;
    /**
     * 处理措施
     */
    private String  productTreatment;

    /**
     * 进项税率
     */
    private BigDecimal incomeTaxRate;

    /**
     * 随货同行单
     */
    private String withCargoNo;

    /**
     * 采购员
     */
    private String purchaseUserName;

    /**
     * 采购订单编号
     */
    private String orderBillNo;

    /**
     * 未含税价（含税价/(1+税率%)）
     */
    private BigDecimal noProductTaxPrice;

    /**
     * 税额（含税金额-未税金额）
     */
    private BigDecimal taxRateSum;

    /**
     * 未税金额（未税金额=含税金额/（1+税率%））
     */
    private BigDecimal noProductTaxPriceSum;

    /**
     * 入库数量总计
     */
    private BigDecimal totalWarehousingAmount;

    /**
     * 商品含税总价总计
     */
    private BigDecimal totalProductTaxPriceSum;

    /**
     * 灭菌批号
     */
    private String sterilizationLotNo;


    /**
     * 拥有者
     */
    private String permitOwner;

    /**
     * 商品批次号
     */
    private String productBatchNumber;
    /**
     * 生产许可证号
     */
    private String productionLicenseNumber;
    /**
     * 上市许可持有人
     */
    private String drugPermissionPerson;
    /**
     * 供应商
     */
    private String supplierNo;
    private String supplierName;
    /**
     * 供应商外码
     */
    private String pharmacyPref;
    /**
     * 源货位id
     */
    private Integer sourcePositionNo;
    /**
     * 货位名称
     */
    private String sourcePositionName;
    /**
     * 源货位状态: 1-合格 2-不合格
     */
    private Byte sourcePositionStatus;
    /**
     * 源货位状态文本
     */
    private String sourcePositionStatusText;
    /**
     * 退货原因
     */
    private String returnReason;

    /**
     * 源行号
     */
    private Long sourceLineNo;

    /**
     * 商品批次id
     */
    private Integer productBatchId;

    private Integer systemType;

    /**
     * 标准库id
     */
    private String standardLibraryId;

    private Integer storageCondition;
    private Integer checkMode;
    // 货区类型 1:合格 2:不合格 3:待验区 4:退货区 入库智鹿用
    private Integer areaType;

    /**
     * 第三方出库编号
     */
    private String thirdPartyWarehouseBillNo;


    /**
     * 申请退货数量
     */
    private BigDecimal returnAmount;

    /**
     * 结算方式:1->预付款;2->实销实结;3->账期;4->压一付二
     */
    private Byte settlementType;

    /**
     * 发票号
     */
    private String invoiceNo;

    /**
     * 截取发票号，供前端展示用
     */
    private String substrInvoiceNo;

    /**
     * 结算方式名称
     */
    private String settlementTypeName;

    /**
     * 采购标准库ID
     */
    private String purchaseStandardLibraryId;


    private String warehouseStateStr;

    private String storeOrganSign;

    private String storeOrganSignName;

    private String checkUserName;

    private String acceptTimeStr;

    private String isInvoice;

    private String remarks;

    private String qualityUserName;
    private String receiveUserName;
    private String operateUserName;
    private String remoteReceiveBillNo;

    /**
     * 渠道ID
     */
    private String channelId;

    // >>>>>>>>>  门店退货入库商品明细新增金额字段  <<<<<<<<<<<<<<< //
    private BigDecimal storeInPrice;    // 门店入库价
    private BigDecimal storeInPriceSum;  // 门店入库金额
    private BigDecimal rootInTaxPrice;   // 总部采购价
    private BigDecimal rootInTaxPriceSum;  // 总部采购金额
    private BigDecimal noTaxPurchasePriceSum;  //总部采购未税金额
    private BigDecimal purchaseTaxSum;   //采购税额

    private Byte billSourceType;

    private String detailSupplierName;

    /**
     * 医保等级, 1甲类 2乙类 3丙类
     */
    private Integer medicalInsuranceLevel;

    private String medicalInsuranceLevelName;

    //国家药品标准编码
    private String nationalDrugCode;

    //国家药品标准名称
    private String nationalDrugName;



    public String getProductBatchNo() {
        if (productBatchNo != null)
        {
            return productBatchNo.trim();
        } else {
            return productBatchNo;
        }
    }

    public void setProductBatchNo(String productBatchNo) {
        if (productBatchNo != null)
        {
            this.productBatchNo = productBatchNo.trim();
        } else {
            this.productBatchNo = productBatchNo;
        }
    }

}