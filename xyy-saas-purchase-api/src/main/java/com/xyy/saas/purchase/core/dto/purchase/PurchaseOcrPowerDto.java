package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 *
 * <AUTHOR>
 * @date   2022/09/15
 */
@Getter
@Setter
public class PurchaseOcrPowerDto implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 机构编码
     */
    private String organSign;

    /**
     * ocr识别次数规则表-主键id
     */
    private Long ruleId;

    /**
     * 是否开启 1 开启
     */
    private Boolean isOpen;

    /**
     * 创建时间,缺省值CURRENT_TIMESTAMP
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 每天识别次数上限
     */
    private Integer maxDayNum;

    /**
     * 每月识别次数上限
     */
    private Integer maxMonthNum;

    /**
     * 累计识别次数上限
     */
    private Integer maxTotalNum;

    /**
     * 规则是否开启 1 开启
     */
    private Boolean ruleIsOpen;

    /**
     * 已用次数
     */
    private Integer usedNum;
}