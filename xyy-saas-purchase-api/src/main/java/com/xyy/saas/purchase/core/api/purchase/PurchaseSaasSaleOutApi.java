package com.xyy.saas.purchase.core.api.purchase;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseSaasSaleOutDto;

import java.util.Map;

/**
 * @Author: <PERSON>
 * @CreateTime: 2020-04-05 11:33
 * @Description: 提取商城订单相关API
 */
public interface PurchaseSaasSaleOutApi {
    /**
     * 查询可提取的商城订单列表
     * @param saasSaleOutDto
     * @return
     */
    PageInfo<Map<String,Object>> querySaleOutOrderList(PageInfo pageInfo, PurchaseSaasSaleOutDto saasSaleOutDto);

    /**
     *  查询需要匹配或新增的商品列表
     * @param bussinessId
     * @param organSign
     * @return
     */
    ResultVO getMatchingOrderCancelProductList(String bussinessId, String organSign);

    /**
     *  查询需要匹配或新增的商品列表
     * @param bussinessId
     * @param organSign
     * @return
     */
    ResultVO getMatchingProductList(String bussinessId, String organSign);
}
