package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Data;

import java.io.Serializable;
import java.util.Set;

@Data
public class BasePurchaseDetailQueryDto extends PurchaseBaseDto implements Serializable {
    private String organSign;
    /**
     * 单据编号
     */
    private String billNo;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 商品信息 商品编号, 助记码, 名称, 通用名
     */
    private String goodsInfo;
    /**
     * 供应商编号
     */
    private String supplierNo;

    private String beginTime;

    private String endTime;
}
