package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * describe:
 *
 * <AUTHOR>
 * @date 2020/01/15
 */
@Data
public class PurchaseReceiveTemperatureDto implements Serializable {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 机构编码
     */
    private String organSign;

    /**
     * 复核单号
     */
    private String billNo;

    /**
     * 逻辑删除 1-有效 0-删除
     */
    private Byte yn;

    /**
     * 状态: 0-暂存；1-提交
     */
    private Byte isCommit;

    /**
     * 是否冷藏
     */
    private Byte coldStorage;

    /**
     * 启运温度
     */
    private BigDecimal startT;

    /**
     * 运输温度json
     */
    private String transportT;

    /**
     * 到货湿度
     */
    private BigDecimal endHumidity;


    /**
     * 到货温度
     */
    private BigDecimal arrivalTemperature;
}
