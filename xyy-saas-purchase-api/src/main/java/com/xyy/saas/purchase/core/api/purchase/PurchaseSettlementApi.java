package com.xyy.saas.purchase.core.api.purchase;


import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseSettlementDetailDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseSettlementInfoDto;

import java.util.List;

/**
 * @Author: <PERSON>
 * @CreateTime: 2020-06-28 10:34
 * @Description: 采购结算单API
 */
public interface PurchaseSettlementApi {

    /**
     * 分页查询采购结算单列表
     *
     * @param infoDto
     * @return
     */
    ResultVO<PageInfo<PurchaseSettlementInfoDto>> selectPurchaseSettlementList(PurchaseSettlementInfoDto infoDto);

    /**
     * 查询单据信息
     * @param infoDto
     * @return
     */
    ResultVO<PurchaseSettlementInfoDto> selectPurchaseSettlementInfo(PurchaseSettlementInfoDto infoDto);

    /**
     * 查询采购结算单详情信息
     * @param organSign
     * @param billNo
     * @return
     */
    ResultVO<List<PurchaseSettlementDetailDto>> selectPurchaseSettlementDetails(String organSign, String billNo);

    /**
     * 删除采购结算单
     * @param organSign
     * @param billNo
     * @return
     */
    ResultVO delete(String organSign, String billNo);

    /**
     * 采购结算单保存
     * @param dto
     * @param detailDtos
     * @return
     */
    ResultVO saveOrUpdate(PurchaseSettlementInfoDto dto, List<PurchaseSettlementDetailDto> detailDtos);

    ResultVO generateBillNo();

    List<PurchaseSettlementDetailDto> selectPurchaseSettlementDetailsByPurchaseBillNo(String organSign, String purchaseBillNo);
}
