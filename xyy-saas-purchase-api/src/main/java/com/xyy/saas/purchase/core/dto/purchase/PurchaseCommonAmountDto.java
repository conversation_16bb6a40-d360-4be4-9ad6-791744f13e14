package com.xyy.saas.purchase.core.dto.purchase;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 采购订单列表左下角汇总统计
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PurchaseCommonAmountDto implements Serializable {
    /**
     * 统计数量
     */
    private BigDecimal orderAmount = BigDecimal.ZERO;

    /**
     * 统计总金额
     */
    private BigDecimal productTaxPriceSum = BigDecimal.ZERO;

    /**
     * 品种数
     */
    private Integer productKindNumAmount;

    /**
     * 未税金额
     */
    private BigDecimal noProductTaxPriceSum;

    /**
     * 税额
     */
    private BigDecimal taxRateSum;

    public static PurchaseCommonAmountDto defaultInstance = new PurchaseCommonAmountDto(BigDecimal.ZERO, BigDecimal.ZERO,0,BigDecimal.ZERO,BigDecimal.ZERO);
}
