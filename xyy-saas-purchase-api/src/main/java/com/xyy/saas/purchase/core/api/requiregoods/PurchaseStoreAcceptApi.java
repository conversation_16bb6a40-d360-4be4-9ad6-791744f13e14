package com.xyy.saas.purchase.core.api.requiregoods;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.BillSummaryDto;
import com.xyy.saas.purchase.core.dto.ReturnBillDto;
import com.xyy.saas.purchase.core.dto.requiregoods.*;

import java.util.List;

/**
 * Created by zhangjinxia on 2020/4/2.
 * 门店验收单
 */
public interface PurchaseStoreAcceptApi {

    /**
     * 分页查询验收单列表
     * @param model
     * @return
     */
    ResultVO<PageInfo<PurchaseStoreAcceptBillInfoDto>> selectByCondition(PurchaseStoreAcceptBillQueryDto model);

    /**
     * 查询info列表
     * @param queryDto
     * @return
     */
    ResultVO<List<PurchaseStoreAcceptBillInfoDto>> selectInfoListByCondition(PurchaseStoreAcceptBillQueryDto queryDto);

    /**
     * 查询detail列表
     * @param organSign 机构编码
     * @param billNo 单号
     * @return
     */
    ResultVO<List<PurchaseStoreAcceptBillDetailDto>> selectDetailListByBillNo(String organSign, String billNo);

    /**
     * 根据单号 查验收单 info和details
     * @param organSign
     * @param billNo
     * @return
     */
    ResultVO<PurchaseStoreAcceptBillInfoDto> selectInfoAndDetailsByBillNo(String organSign, String billNo);

    /**
     * 修改验收单
     * @param dto
     * @return
     */
    ResultVO updateAcceptBill(PurchaseStoreAcceptBillInfoDto dto);

    //给GSP管理提供的报表接口 start

    /**
     * GSP分页查询验收单列表
     * @param model
     * @return
     */
    ResultVO<PageInfo<PurchaseStoreAcceptBillInfoDto>> selectByConditionGsp(PurchaseStoreAcceptBillQueryDto model);

    /**
     * GSP-验收记录-商品汇总-分页
     * @param model
     * @return
     */
    ResultVO<PageInfo<PurchaseStoreAcceptBillDetailDto>> selectDetailsByConditionGsp(PurchaseStoreAcceptBillQueryDto model);

    /**
     * GSP-验收记录-商品汇总
     * @param model
     * @return
     */
    ResultVO<List<PurchaseStoreAcceptBillDetailDto>> selectDetailsByConditionGspList(PurchaseStoreAcceptBillQueryDto model);


    //给GSP管理提供的报表接口 end

    /**
     * 门店验收单回退
     * @param returnBillDto
     * @return
     */
    ResultVO returnBill(ReturnBillDto returnBillDto) ;


    ResultVO<BillSummaryDto> selectInfoSummary(PurchaseStoreAcceptBillQueryDto queryDto);

    ResultVO<BillSummaryDto> selectDetailSummary(String organSign, String billNo);
}
