package com.xyy.saas.purchase.core.api;

import com.xyy.saas.common.util.ResultVO;

import java.util.Date;

public interface PullTaskServiceApi {

    /**
     * 同步获取销售出库数据
     * @param organSign
     */
    ResultVO processSaleOut(String bindCode, Date bindTime, String organSign);

    /**
     * 同步神农获取销售出库数据
     * @param organSign
     */
    ResultVO processErpSaleOut(String bindCode, Date bindTime, String organSign);

    /**
     * 同步获取销退出库数据
     * @param organSign
     */
    ResultVO processSaleReturn(String organSign);

    /**
     * 手动同步获取销售出库数据
     * @param organSign
     */
    ResultVO manualSaleOut(String bindCode, String bindTime, String organSign);

    /**
     * 手动同步获取销售出库数据
     * @param organSign
     */
    ResultVO manualErpSaleOut(String bindCode, String bindTime, String organSign);

    /**
     * 删除提取商城订单key
     * @param organSign
     */
    boolean deleteSaleOutTaskKey(String organSign);

    /**
     * @Description
     * @param id : 商城详情主键id
     * @param standardLibraryId : 标准库id
     * @return void
     * <AUTHOR>
     * @Date 2019/5/16
     **/
    void updateSaleOutDetailStandardLibraryId(Long id,Long standardLibraryId);

    /**
     * @Description 更新4.价位接口
     * @param organSign :  机构号
     * @return void
     * <AUTHOR>
     * @Date 2019/5/22
     **/
    void updateUpdatePurchasePosition(String organSign);

    /**
     * 清空商城订单
     * @param
     * <AUTHOR>
     * @param organSign
     */
    ResultVO  deleteSaleOutOrder(String organSign);


}
