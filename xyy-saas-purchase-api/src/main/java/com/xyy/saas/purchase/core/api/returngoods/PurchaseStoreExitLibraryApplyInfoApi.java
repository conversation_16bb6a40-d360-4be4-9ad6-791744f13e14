package com.xyy.saas.purchase.core.api.returngoods;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.BillSummaryDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseWarehousingInfoDto;
import com.xyy.saas.purchase.core.dto.returngoods.*;

import java.util.List;

/**
 * @Author: <PERSON>
 * @CreateTime: 2020-04-05 17:56
 * @Description: 门店退货申请单相关API
 */
public interface PurchaseStoreExitLibraryApplyInfoApi {
    /**
     * 查询退货申请单列表
     * @param applyInfoDto
     * @return
     */
    ResultVO<PageInfo<PurchaseStoreExitLibraryApplyInfoDto>> selectPurchaseStoreExitLibraryApplyInfoList(PageInfo pageInfo, PurchaseStoreExitLibraryApplyInfoDto applyInfoDto);



    /**
     * 查询退货申请列表 或 商品明细 页面的汇总信息
     * @param purchaseExitLibraryApplyInfoDto
     * @return
     */
    ResultVO<BillSummaryDto> selectExitLibraryApplySummary(PurchaseStoreExitLibraryApplyInfoDto purchaseExitLibraryApplyInfoDto);

    /**
     * 根据机构号，单据编号查询单据是否存在
     * @param applyInfoDto
     * @return
     */
    ResultVO<PurchaseStoreExitLibraryApplyInfoDto> selectPurchaseStoreExitLibraryApplyInfo(PurchaseStoreExitLibraryApplyInfoDto applyInfoDto);
    /**
     * 删除退出申请单
     * @param organSign
     * @param billNo
     * @return
     */
    int deletePurchaseStoreExitLibraryApplyInfo(String organSign, String billNo);

    PageInfo selectProductInventoryList(PageInfo pageInfo, PurchaseProductInventoryDto inventoryDto);

    /**
     * 保存门店退货申请单
     * @param applyInfoDto
     */
    ResultVO savePurchaseStoreExitLibraryApplyInfo(PurchaseStoreExitLibraryApplyInfoDto applyInfoDto);

    /**
     * 修改门店退货申请单
     * @param applyInfoDto
     */
    ResultVO updatePurchaseStoreExitLibraryApplyInfo(PurchaseStoreExitLibraryApplyInfoDto applyInfoDto);

    /**
     * 查询退货申请单详情列表
     * @param organSign
     * @param billNo
     * @return
     */
    List<PurchaseStoreExitLibraryApplyDetailDto> selectPurchaseStoreExitLibraryApplyDetailList(String organSign, String billNo);


    /**
     * GSP-门店退货申请单-单据汇总
     * @param applyInfoDto
     * @return
     */
    ResultVO<PageInfo<PurchaseStoreExitLibraryApplyInfoDto>> selectInfoListGsp(PageInfo pageInfo, PurchaseStoreExitLibraryApplyInfoDto applyInfoDto);


    /**
     * GSP-门店退货申请单-商品汇总
     * @param applyInfoDto
     * @return
     */
    ResultVO<PageInfo<PurchaseStoreExitLibraryApplyDetailDto>> selectDetailListGsp(PageInfo pageInfo, PurchaseStoreExitLibraryApplyInfoDto applyInfoDto);

    /**
     * 退货申请-提取出库单-申请明细
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<PurchaseStoreExitLibraryApplyDetailDto>> queryAssemblePurchaseStoreExitLibraryApplyDetailDtoListWhenCreate(PurchaseStoreExitApplyDetailsWithOriginalOrderQueryDto queryDto);

    /**
     * 暂存
     * @return
     */
    ResultVO<Integer> pauseSaveApplyInfo(PurchaseStoreExitLibraryApplyInfoDto applyInfoDto);

    /**
     * 提交审批
     * @param applyInfoDto
     * @return
     */
    ResultVO<Integer> submitSaveApplyInfo(PurchaseStoreExitLibraryApplyInfoDto applyInfoDto);

    /**
     * 分页查询门店退货申请单商品明细
     * @param pageInfo
     * @param purchaseExitLibraryApplyInfoDto
     * @return
     */
    PageInfo<PurchaseStoreExitLibraryApplyDetailDto> selectStorePurchaseExitLibraryApplyDetailList(PageInfo pageInfo, PurchaseStoreExitLibraryApplyInfoDto purchaseExitLibraryApplyInfoDto);
    /**
     * 暂存
     * @return
     */
    ResultVO<Integer> batchPauseSaveApplyInfo(List<PurchaseStoreExitLibraryApplyInfoDto> params);

    /**
     * 提交审批
     * @param params
     * @return
     */
    ResultVO<Integer> batchSubmitSaveApplyInfo(List<PurchaseStoreExitLibraryApplyInfoDto> params);


    /**
     * 刷数据
     */
    void  flushLibranyApplyTaxRate();

    /**
     * 门店退货申请单审批通过-库存一入一出重试
     * @param infoDto
     */
    void purchaseStoreExitApplyApprovedInAndImmediateOut(PurchaseWarehousingInfoDto infoDto);
}
