package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购出货单商品数据传输实体
 */
@Getter
@Setter
public class PurchaseShipmentDetailDto implements Serializable {

    private Long id;


    private String organSign;


    private String billNo;

    /**
     * 采购订单供应商
     */
    private String supplierNo;
    /**
     * 采购订单供应商名称
     */
    private String supplierName;
    /**
     * 商品内码
     */
    private String productCode;

    /**
     * 商品外码
     */
    private String productPharmacyPref;

    //商品名称
    private String productName;

    /**
     * 通用名称
     * @mbg.generated Thu Aug 20 20:41:13 CST 2020
     */
    private String commonName;

    /**
     * 规格
     * @mbg.generated Thu Aug 20 20:41:13 CST 2020
     */
    private String specifications;
    /**
     * 包装单位
     * @mbg.generated Thu Aug 20 20:41:13 CST 2020
     */private String packingUnit;

    /**
     * 生产厂家
     * @mbg.generated Thu Aug 20 20:41:13 CST 2020
     */
    private String manufacturer;

    /**
     * 批准文号
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column saas_purchase_shipment_detail.approval_number
     *
     * @mbg.generated Thu Aug 20 20:41:13 CST 2020
     */
    private String approvalNumber;
    /**
     * 药品上市许可证持有人
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column saas_purchase_shipment_detail.drug_permission_person
     *
     * @mbg.generated Thu Aug 20 20:41:13 CST 2020
     */
    private String drugPermissionPerson;

    /**
     * 出货数量
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column saas_purchase_shipment_detail.shipment_amount
     *
     * @mbg.generated Thu Aug 20 20:41:13 CST 2020
     */
    private BigDecimal shipmentAmount;

    /**
     * 含税价
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column saas_purchase_shipment_detail.product_tax_price
     *
     * @mbg.generated Thu Aug 20 20:41:13 CST 2020
     */
    private BigDecimal productTaxPrice;

    /**
     * 出货商品含税总价
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column saas_purchase_shipment_detail.product_tax_price_sum
     *
     * @mbg.generated Thu Aug 20 20:41:13 CST 2020
     */
    private BigDecimal productTaxPriceSum;


    private String remarks;


    private String createUser;

    /**
     * 创建人名称/制单人名称
     */
    private String createUserName;

    private Date createTime;

    private String createTimeStr;


    private String updateUser;


    private Date updateTime;

    /**
     * 逻辑删除 1-未删除 0-已删除
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column saas_purchase_shipment_detail.yn
     *
     * @mbg.generated Thu Aug 20 20:41:13 CST 2020
     */
    private Boolean yn;

    /**
     * 产地
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column saas_purchase_shipment_detail.product_place
     *
     * @mbg.generated Thu Aug 20 20:41:13 CST 2020
     */
    private String productPlace;

    /**
     * 剂型
     */
    private String dosageForm;

    /**
     *  标准库id
     **/
    private String standardLibraryId;

    /**
     * 智鹿外采库存数量
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column saas_purchase_shipment_detail.ybm_stock_number
     *
     * @mbg.generated Thu Aug 20 20:41:13 CST 2020
     */
    private BigDecimal ybmStockNumber;
    /**
     * 最近有效期至
     */
    private String productLatelyExpiryStr;
    private Date productLatelyExpiry;
    /**
     * 最远有效期至
     */
    private String productFurthestExpiryStr;
    private Date productFurthestExpiry;
    /**
     * 进项税率
     */
    private BigDecimal incomeTaxRate;
    /**
     * 加点
     */
    private  BigDecimal raise;
    /**
     * 加点后含税价
     */
    private BigDecimal raiseProductTaxPrice;
    /**
     * 加点后含税金额
     */
    private BigDecimal raiseProductTaxPriceSum;

    /*批发业务销售订单使用*/
    /**
     * 业务类型 1-出货单 2-销售订单
     */
    private Byte billType;
    /**
     * 折扣金额
     */
    private BigDecimal discountAmount;
    /**
     * 折扣
     */
    private BigDecimal discount;
    /**
     * 销项税
     */
    private BigDecimal outputTax;
    /**
     * 商品混合查询字段
     */
    private String productMixedQuery;
    /**
     * 销售单位混合查询字段
     */
    private String mixedQuery;

}