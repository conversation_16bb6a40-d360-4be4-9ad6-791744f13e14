package com.xyy.saas.purchase.core.api.requiregoods;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.exception.BusinessException;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.BillSummaryDto;
import com.xyy.saas.purchase.core.dto.requiregoods.*;

import java.util.List;
import java.util.Map;

/**
 * Created by zhangjinxia on 2020/4/2.
 * 门店要货单
 */
public interface PurchaseStoreRequireApi {

    /**
     * 保存要货单
     * @param dto
     * @return
     */
    ResultVO saveRequireGoods(PurchaseStoreRequireGoodsInfoDto dto);

    /**
     * 修改要货单
     * @param dto
     * @return
     */
    ResultVO updateRequireGoods(PurchaseStoreRequireGoodsInfoDto dto);

    /**
     * 逻辑删除
     * @param organSign
     * @param billNo
     */
    ResultVO deleteByBillNo(String organSign, String billNo, Integer version);

    /**
     * 分页查询要货单列表
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<PurchaseStoreRequireGoodsInfoDto>> selectByCondition(PurchaseStoreRequireGoodsQueryDto queryDto);

    /**
     * 查询detail列表
     * @param organSign 机构编码
     * @param billNo 单号
     * @return
     */
    ResultVO<List<PurchaseStoreRequireGoodsDetailDto>> selectDetailListByBillNo(String organSign, String billNo);

    /**
     * 查询info列表
     * @param queryDto
     * @return
     */
    ResultVO<List<PurchaseStoreRequireGoodsInfoDto>> selectInfoListByCondition(PurchaseStoreRequireGoodsQueryDto queryDto);

    /**
     * 查询当前待办角色列表
     * @param organSign
     * @return
     */
    ResultVO<List<PurchaseStoreRequireGoodsApproveRoleDto>> selectApproveRoles(String organSign);

    /**
     * 根据要货单号 查要货单 info和details
     * @param dto
     * @return
     */
    ResultVO<PurchaseStoreRequireGoodsInfoDto> selectInfoAndDetailsByBillNo(PurchaseStoreRequireGoodsQueryDto dto);

    /**
     * 根据要货单号 查要货单 info和details - 带反查库存数量的接口
     * @param dto
     * @return
     */
    ResultVO<PurchaseStoreRequireGoodsInfoDto> selectDetailsAndStockByBillNo(PurchaseStoreRequireGoodsQueryDto dto);

    /**
     * 复制出一个要货单(暂存状态)
     * @param organSign
     * @param billNo
     * @return
     */
    ResultVO copyAnApprovedRequireGoodsBill(String organSign, String billNo, String billingUser);

    /**
     * 创建门店要货单-添加商品页面
     * @param purchaseProductInfoQueryDto
     * @return
     */
    ResultVO selectProducts(PurchaseProductInfoQueryDto purchaseProductInfoQueryDto);

    /**
     * 查询导入的历史要货记录
     * @param queryDto
     * @return
     */
    ResultVO selectStoreInitRequireGoods(PurchaseStoreInitRequireGoodsQueryDto queryDto);

    //给GSP管理提供的报表接口 start

    /**
     * GSP-要货记录
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<PurchaseStoreRequireGoodsInfoDto>> selectByConditionGsp(PurchaseStoreRequireGoodsQueryDto queryDto);

    /**
     * GSP-要货记录-商品汇总-分页
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<PurchaseStoreRequireGoodsDetailDto>> selectDetailsByConditionGsp(PurchaseStoreRequireGoodsQueryDto queryDto);

    /**
     * GSP-要货记录-商品汇总
     * @param queryDto
     * @return
     */
    ResultVO<List<PurchaseStoreRequireGoodsDetailDto>> selectDetailsByConditionGspList(PurchaseStoreRequireGoodsQueryDto queryDto);

    /**
     * 查询导入的历史要货记录-不分页
     * @param queryDto
     * @return
     */
    ResultVO<List<PurchaseStoreInitRequireGoodsDto>> selectStoreInitRequireGoodsList(PurchaseStoreInitRequireGoodsQueryDto queryDto);

    //给GSP管理提供的报表接口 end

    /**
     * 查询erp商品信息（小药药库存数量，效期，含税价等）
     * @return
     */
    ResultVO<List<PurchaseProductInfoDto>> selectErpProductInfo(String organSign, List<String> dtos);

    /**
     * 门店要货单导入功能  根据taskID查询商品信息
     * @param dto
     * @return
     */
    ResultVO getExcelProducts(PurchaseProductInfoQueryDto dto);

    /**
     * 查询物流平台商品信息（小药药与智鹿库存数量，小药药与智鹿效期，小药药与智鹿含税价等）- 价格管控需求
     * @return
     */
    ResultVO<List<PurchaseProductInfoDto>> selectErpWuLiuProductInfos(String organSign, Byte purchaseOrderModel, List<PurchaseProductInfoDto> dtos);

    /**
     * 门店要货管理 - 提交 - 判断提交商品列表的调价方案是否合法
     * @param organSign
     * @param productCodes
     * @return
     */
    ResultVO<List<String>> judgeAdjustPrice(String organSign, List<String> productCodes);


    ResultVO<BillSummaryDto> getRequireGoodsOrderSummary(PurchaseStoreRequireGoodsQueryDto queryDto);

    /**
     * 门店智能要货——按库存上限要货
     * @param organSign
     * @return
     */
    ResultVO selectProductsByMaxLimit(String organSign);

    /**
     * 门店智能要货——按日均销售要货
     * @param organSign
     * @param infos
     * @return
     */
    ResultVO selectProductsBySales(String organSign, PurchaseProductInfoDto infos);

    ResultVO getEcControl(String organSign,String standardCode);

    /**
     * 门店要货-采购建议区间设置
     * @param adviceInfoDto
     * @return
     */
    ResultVO savePurchaseAdviceRangeInfo(PurchaseStoreRequireAdviceInfoDto adviceInfoDto);

    /**
     * 门店要货-采购建议区间查询
     * @param organSign
     * @param businessType
     * @return
     */
    ResultVO selectPurchaseAdviceByOrganSignAndType(String organSign,Integer businessType);


    /**
     * 获取国家药品编码
     * @param organSign
     * @param productCodes
     * @return
     */
    Map<String,PurchaseCnProductInfoDto> getCnProductMap(String organSign, List<String> productCodes);
}
