package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

@Data
public class PurchaseRetrievePriceInfoDto implements Serializable {
    /**
     *
     * 主键
     */
    private Integer id;

    /**
     *
     * 机构号
     */
    private String organSign;

    /**
     *
     *订单号
     */
    private String billNo;

    /**
     *
     * 上级订单号
     */
    private String parentBillNo;

    /**
     *
     * 开票时间
     */
    private Date billTime;

    private String billTimeStr;

    /**
     *
     * 供应商编号
     */
    private String supplierNo;

    /**
     * 供应商外码
     */
    private String pharmacyPref;

    /**
     *
     * 供应商名称
     */
    private String supplierName;

    /**
     *
     * 开票员
     */
    private String billingUser;

    /**
     *
     * 采购单含税总金额
     */
    private BigDecimal taxAmountSum;

    /**
     *
     * 冲价总金额
     */
    private BigDecimal taxChargeAmount;

    /**
     *
     * 备注
     */
    private String remarks;

    /**
     *
     * 创建人
     */
    private String createUser;

    /**
     *
     * 创建时间
     */
    private Date createTime;

    /**
     *
     * 修改人
     */
    private String updateUser;

    /**
     *
     *修改时间
     */
    private Date updateTime;

    /**
     *
     * 逻辑删除 1-未删除 0-已删除
     */
    private Boolean yn;

    /**
     *
     * 操作版本号
     */
    private Integer baseVersion;

    /**
     * 经营模式
     */
    private String bizModel;

    private String status;

    private String statusText;

    private String beginTime;

    private String endTime;

    private Integer rows;
    private Integer page;

    private String productCode;

}