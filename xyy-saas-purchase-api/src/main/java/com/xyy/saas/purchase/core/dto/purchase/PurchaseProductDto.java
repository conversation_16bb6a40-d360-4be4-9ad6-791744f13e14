package com.xyy.saas.purchase.core.dto.purchase;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class PurchaseProductDto implements Serializable {
    private static final long serialVersionUID = 4560308950453760601L;

    private String productCode; // 商品编号
    private String productName; // 通用名
    private String specifications; // 规格、型号
    private String manufacturer; // 生产厂家
    private String approvalNumber; // 批准文号
    private Integer inventoryNumber; // 批号
    private Date producedDate; // 生产日期
    private BigDecimal stockNumber; // 库存数量
    private String barCode; //条形码
    private BigDecimal productTaxPrice; // 上次进购价
    private Long standardLibraryId;	//标准库id
    private String productName2; //商品名称
    private BigDecimal stockAmount;
    private String pharmacyPref;
    /** '零售价'*/
    private BigDecimal retailPrice;
    /** '最后一次供应商'*/
    private String lastProvidePref;
    /** '商品分类'*/
    private Integer productType;
    /** '商品分类展示'*/
    private Integer productTypeStr;
    /** '建议计划量'*/
    private BigDecimal productSuggestPlanCount;
    private BigDecimal amount;
    private String supplierNo;
    //供应商混合查询字段
    private String supplierMnemonicCode;
    private Long id;
    private String supplierName;

    /** 剂型 */
    private String agentName;
    /** 单位 */
    private String unit;

    /**
     * 销售量
     */
    private BigDecimal productNum;
    /**
     * 销售金额
     */
    private BigDecimal actualAmount;

    /**
     * 单位
     */
    private String packingUnit;

    /**
     * 产地
     */
    private String productOriginAddress;

    /**
     * 最后采购价
     */
    private BigDecimal lastPrice;

    private Integer businessScope;// 经营范围

    private String providerScope;//供应商经营范围

    private String drugPermissionPerson;

    public String getDrugPermissionPerson() {
        return drugPermissionPerson;
    }

    public void setDrugPermissionPerson(String drugPermissionPerson) {
        this.drugPermissionPerson = drugPermissionPerson;
    }


    public String getProviderScope() {
        return providerScope;
    }

    public void setProviderScope(String providerScope) {
        this.providerScope = providerScope;
    }

    public Integer getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(Integer businessScope) {
        this.businessScope = businessScope;
    }

    public BigDecimal getLastPrice() {
        return lastPrice;
    }

    public void setLastPrice(BigDecimal lastPrice) {
        this.lastPrice = lastPrice;
    }

    public String getPackingUnit() {
        return packingUnit;
    }

    public void setPackingUnit(String packingUnit) {
        this.packingUnit = packingUnit;
    }

    public String getProductOriginAddress() {
        return productOriginAddress;
    }

    public void setProductOriginAddress(String productOriginAddress) {
        this.productOriginAddress = productOriginAddress;
    }

    public BigDecimal getProductNum() {
        return productNum;
    }

    public void setProductNum(BigDecimal productNum) {
        this.productNum = productNum;
    }

    public BigDecimal getActualAmount() {
        return actualAmount;
    }

    public void setActualAmount(BigDecimal actualAmount) {
        this.actualAmount = actualAmount;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSupplierNo() {
        return supplierNo;
    }

    public void setSupplierNo(String supplierNo) {
        this.supplierNo = supplierNo;
    }

    public String getSupplierMnemonicCode() {
        return supplierMnemonicCode;
    }

    public void setSupplierMnemonicCode(String supplierMnemonicCode) {
        this.supplierMnemonicCode = supplierMnemonicCode;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public BigDecimal getRetailPrice() {
        return retailPrice;
    }

    public void setRetailPrice(BigDecimal retailPrice) {
        this.retailPrice = retailPrice;
    }

    public String getLastProvidePref() {
        return lastProvidePref;
    }

    public void setLastProvidePref(String lastProvidePref) {
        this.lastProvidePref = lastProvidePref;
    }

    public Integer getProductType() {
        return productType;
    }

    public void setProductType(Integer productType) {
        this.productType = productType;
    }

    public Integer getProductTypeStr() {
        return productTypeStr;
    }

    public void setProductTypeStr(Integer productTypeStr) {
        this.productTypeStr = productTypeStr;
    }

    public BigDecimal getProductSuggestPlanCount() {
        return productSuggestPlanCount;
    }

    public void setProductSuggestPlanCount(BigDecimal productSuggestPlanCount) {
        this.productSuggestPlanCount = productSuggestPlanCount;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    public Integer getInventoryNumber() {
        return inventoryNumber;
    }

    public void setInventoryNumber(Integer inventoryNumber) {
        this.inventoryNumber = inventoryNumber;
    }

    public Date getProducedDate() {
        return producedDate;
    }

    public void setProducedDate(Date producedDate) {
        this.producedDate = producedDate;
    }

    public BigDecimal getStockNumber() {
        return stockNumber;
    }

    public void setStockNumber(BigDecimal stockNumber) {
        this.stockNumber = stockNumber;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public BigDecimal getProductTaxPrice() {
        return productTaxPrice;
    }

    public void setProductTaxPrice(BigDecimal productTaxPrice) {
        this.productTaxPrice = productTaxPrice;
    }

    public Long getStandardLibraryId() {
        return standardLibraryId;
    }

    public void setStandardLibraryId(Long standardLibraryId) {
        this.standardLibraryId = standardLibraryId;
    }

    public String getProductName2() {
        return productName2;
    }

    public void setProductName2(String productName2) {
        this.productName2 = productName2;
    }

    public BigDecimal getStockAmount() {
        return stockAmount;
    }

    public void setStockAmount(BigDecimal stockAmount) {
        this.stockAmount = stockAmount;
    }

    public String getPharmacyPref() {
        return pharmacyPref;
    }

    public void setPharmacyPref(String pharmacyPref) {
        this.pharmacyPref = pharmacyPref;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }
}
