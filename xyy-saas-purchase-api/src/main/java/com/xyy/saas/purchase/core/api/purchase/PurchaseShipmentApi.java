package com.xyy.saas.purchase.core.api.purchase;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseShipmentDetailDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseShipmentInfoDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-08-21
 * @mondify
 * @copyright
 */
public interface PurchaseShipmentApi {
    /**
     * 分页查询出货单信息
     * @param pageInfo
     * @param purchaseShipmentInfoDto
     * @return
     */
    ResultVO<PageInfo<PurchaseShipmentInfoDto>> getPurchaseShipmentListByPage(PageInfo pageInfo, PurchaseShipmentInfoDto purchaseShipmentInfoDto);

    /**
     * 根据机构号，单据编号查询单据信息
     * @param organSign
     * @param billNo
     * @param employeeId 当前系统登录人
     * @return
     */
    ResultVO<PurchaseShipmentInfoDto> getPurchaseShipmentInfoAndDetailByBillNo(String organSign,String billNo,Integer employeeId);

    /**
     * 保存出货单 - 总部请货拆单调用
     * @param dto
     * @return
     */
    ResultVO<Integer> savePurchaseShipmentInfoAndDetail(PurchaseShipmentInfoDto dto);

    /**
     * 暂存
     * @return
     */
    ResultVO<Integer> pausePurchaseShipment(PurchaseShipmentInfoDto applyInfoDto);

    /**
     * 出货/编辑
     * @param dto
     * @return
     */
    ResultVO<Integer> submitPurchaseShipment(PurchaseShipmentInfoDto dto) ;

    /**
     * 出货单复用
     * @param dto{organSign, billNo(), employeeId.toString()}
     * @return
     */
    ResultVO purchMultiplex(PurchaseShipmentInfoDto dto);
}
