package com.xyy.saas.purchase.core.api;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.YBMNotOutOrderReqDTO;
import com.xyy.saas.purchase.core.dto.YBMNotOutOrderRespDTO;
import com.xyy.saas.purchase.core.dto.YBMOrderLogisticRespDTO;
import com.xyy.saas.purchase.core.dto.YBMProviderRespDTO;
import com.xyy.saas.purchase.core.dto.thirdparty.YbmOrderStatusChangeDto;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface PurchaseYBMServiceApi {

    /**
     * 查询药帮忙供应商信息
     * @param orgIdList
     * @return
     */
    ResultVO<List<YBMProviderRespDTO>> queryPopCorporationInfo(List<String> orgIdList);

    /**
     * 查询药帮忙供应商信息
     * @param name
     * @return
     */
    ResultVO<List<YBMProviderRespDTO>> queryPopCorporationInfoByName(String name);

    /**
     * 查询药帮忙未出库订单信息
     * @param reqDTO
     * @return
     */
    ResultVO<PageInfo<YBMNotOutOrderRespDTO>> queryNotOutOrderList(YBMNotOutOrderReqDTO reqDTO);

    /**
     * 订单物流轨迹查询接口
     * @param orderNo
     * @return
     */
    ResultVO<YBMOrderLogisticRespDTO> queryOrderLogisticDetail(String orderNo);

    /**
     * 绑定YBM商品内码至本地
     * @param ownProductId
     * @param thirdProductId
     * @param businessId
     * @return
     */
    int manualProduct(String businessId,String thirdProductId,String ownProductId);

    /**
     * 根据三方订单状态变化保存销售出库单
     * @return
     */
    ResultVO saveSaleOutBillByYbmOrderStatusChange(YbmOrderStatusChangeDto dto);
}
