package com.xyy.saas.purchase.core.api;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.*;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseBillDetailCommonDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseSettlementExtractBillQueryDto;

import java.util.List;

/**
 * 采购入库单API
 * Created by zhangjinxia on 2020/2/17.
 */
public interface PurchaseBillStorageInfoApi {

    /**
     * 查询采购入库单列表
     * @param saasPurchaseBillInfoPoDto
     * @return
     */
    PageInfo<SaasPurchaseBillInfoDto> getBillInfoList(PageInfo pageInfo, SaasPurchaseBillInfoPoDto saasPurchaseBillInfoPoDto);

    /**
     * 查询详情信息，主要是根据type和billNo来查询详情
     * @param purchaseBillInfoPoDto
     * @return
     */
    ResultVO getDetailPurchaseBillInfo(SaasPurchaseBillInfoPoDto purchaseBillInfoPoDto);

    /**
     * 查询采购入库单详情信息 - 适用于采购结算单需求
     * @param queryDto
     * @return
     */
    ResultVO<List<PurchaseBillDetailCommonDto>> getDetailInfo(PurchaseSettlementExtractBillQueryDto queryDto);

    /**
     * 查询商城订单入库列表
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<SaasPurchaseBillDetailDto>> getWarehouseRecordDetail(WarehouseRecordQueryDto queryDto);
}
