package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class PurchaseSaasSaleOutDto extends PurchaseBaseDto implements Serializable {

    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column saas_sale_out.business_id
     *
     * @mbg.generated Sun Apr 05 11:40:25 CST 2020
     */
    private String businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column saas_sale_out.bill_number
     *
     * @mbg.generated Sun Apr 05 11:40:25 CST 2020
     */
    private String billNumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column saas_sale_out.status
     *
     * @mbg.generated Sun Apr 05 11:40:25 CST 2020
     */
    private Integer status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column saas_sale_out.org_id
     *
     * @mbg.generated Sun Apr 05 11:40:25 CST 2020
     */
    private String orgId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column saas_sale_out.org_code
     *
     * @mbg.generated Sun Apr 05 11:40:25 CST 2020
     */
    private String orgCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column saas_sale_out.provider_name
     *
     * @mbg.generated Sun Apr 05 11:40:25 CST 2020
     */
    private String providerName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column saas_sale_out.provider_number
     *
     * @mbg.generated Sun Apr 05 11:40:25 CST 2020
     */
    private String providerNumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column saas_sale_out.billing_create_date
     *
     * @mbg.generated Sun Apr 05 11:40:25 CST 2020
     */
    private Date billingCreateDate;

    private String billingCreateDateStr;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column saas_sale_out.pull_yn
     *
     * @mbg.generated Sun Apr 05 11:40:25 CST 2020
     */
    private Integer pullYn;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column saas_sale_out.total_tax_amount
     *
     * @mbg.generated Sun Apr 05 11:40:25 CST 2020
     */
    private BigDecimal totalTaxAmount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column saas_sale_out.drug_store_id
     *
     * @mbg.generated Sun Apr 05 11:40:25 CST 2020
     */
    private String drugStoreId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column saas_sale_out.drug_store_name
     *
     * @mbg.generated Sun Apr 05 11:40:25 CST 2020
     */
    private String drugStoreName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column saas_sale_out.product_id
     *
     * @mbg.generated Sun Apr 05 11:40:25 CST 2020
     */
    private String productId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column saas_sale_out.discount_amount
     *
     * @mbg.generated Sun Apr 05 11:40:25 CST 2020
     */
    private BigDecimal discountAmount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column saas_sale_out.yn
     *
     * @mbg.generated Sun Apr 05 11:40:25 CST 2020
     */
    private Byte yn;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column saas_sale_out.product_matching
     *
     * @mbg.generated Sun Apr 05 11:40:25 CST 2020
     */
    private Byte productMatching;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column saas_sale_out.organSign
     *
     * @mbg.generated Sun Apr 05 11:40:25 CST 2020
     */
    private String organsign;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column saas_sale_out.remark
     *
     * @mbg.generated Sun Apr 05 11:40:25 CST 2020
     */
    private String remark;

    /**
     * 开票开始时间
     */
    private String startTime;

    /**
     * 开票结束时间
     */
    private String endTime;

    private String createUser;

    private Date createTime;

    /**
     * 版本号
     */
    private int baseVersion;

    /** 单据来源
     *  0:商城订单
     *  1:同济堂
     * */
    private String orderSource;

    /**
     * 三方出库单详情
     */
    private List<PurchaseSaasSaleOutDetailDto> details;

    /**
     * 调用来源 0：同步商城订单（单号） 1：定时任务  2：同步商城订单（全部，默认一个月）
     */
    private Byte invokeSource;

    /**
     * 明细行数
     */
    private int detailCount;

    /**
     * saas供应商内码
     */
    private String saasProviderPref;

    /**
     * saas供应商名称
     */
    private String saasProviderName;

    /**
     * 1代表忽略
     */
    private Integer ignoreStatus;

    /**
     * 1-优惠前、2-优惠后
     */
    private Integer priceCalculationStrategy;

    /** 单据来源
     *  0:商城订单
     *  1:同济堂
     * */
    private String orderSourceName;
}