package com.xyy.saas.purchase.core.api.returngoods;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.returngoods.*;

import javax.xml.transform.Result;
import java.util.List;

/**
 * @Author: <PERSON>
 * @CreateTime: 2020-04-05 17:56
 * @Description: 门店退出出库单相关API
 */
public interface PurchaseStoreExitLibraryInfoApi {
    /**
     * 查询退出出库单列表
     * @param purchaseStoreExitLibraryInfoDto
     * @return
     */
    ResultVO<PageInfo<PurchaseStoreExitLibraryInfoDto>> selectPurchaseStoreExitLibraryInfoList(PageInfo pageInfo, PurchaseStoreExitLibraryInfoDto purchaseStoreExitLibraryInfoDto);

    /**
     * 根据机构号，单据编号查询退出出库单单据是否存在
     * @param purchaseExitLibraryInfoDto
     * @return
     */
    ResultVO<PurchaseStoreExitLibraryInfoDto> selectPurchaseStoreExitLibraryInfo(PurchaseStoreExitLibraryInfoDto purchaseExitLibraryInfoDto);
    /**
     * 删除退出申请单
     * @param organSign
     * @param billNo
     * @return
     */
    ResultVO deletePurchaseStoreExitLibraryInfo(String organSign, String billNo);

    PageInfo selectProductInventoryList(PageInfo pageInfo, PurchaseProductInventoryDto inventoryDto);

    /**
     * 查询门店退出出库单详情列表
     * @param organSign
     * @param billNo
     * @return
     */
    ResultVO<List<PurchaseStoreExitLibraryDetailDto>> selectPurchaseStoreExitLibraryDetailList(String organSign, String billNo);

    /**
     * 保存门店退出出库单
     * @param dto
     * @return
     */
    ResultVO saveStoreExitLibrary(PurchaseStoreExitLibraryInfoDto dto);

    /**
     * 编辑门店退出出库单
     * @param dto
     * @return
     */
    ResultVO updateStoreExitLibrary(PurchaseStoreExitLibraryInfoDto dto);

    /**
     * GSP报表-查询退出出库单列表
     * @param purchaseStoreExitLibraryInfoDto
     * @return
     */
    ResultVO<PageInfo<PurchaseStoreExitLibraryInfoDto>> selectPurchaseStoreExitLibraryInfoListGsp(PageInfo pageInfo, PurchaseStoreExitLibraryInfoDto purchaseStoreExitLibraryInfoDto);

    /**
     * GSP-退货记录-商品汇总-分页查询
     * @param purchaseStoreExitLibraryInfoDto
     * @return
     */
    ResultVO<PageInfo<PurchaseStoreExitLibraryDetailDto>> selectDetailsByConditionGsp(PurchaseStoreExitLibraryInfoDto purchaseStoreExitLibraryInfoDto);

    /**
     * GSP-退货记录-商品汇总-不分页-用于导出
     * @param purchaseStoreExitLibraryInfoDto
     * @return
     */
    ResultVO<List<PurchaseStoreExitLibraryDetailDto>> selectDetailsByConditionGspList(PurchaseStoreExitLibraryInfoDto purchaseStoreExitLibraryInfoDto);

    /**
     * 复核员列表
     * @param organSign
     * @return
     */
    ResultVO selectReviewUsers(String organSign);

    /**
     * 分页查询-门店退货出库单详情
     * @param queryDto
     * @return
     */
    ResultVO selectDetailsByCondition(PurchaseStoreExitLibraryDetailQueryDto queryDto);


    /**
     * 报表-门店退货出库单合计
     * @param queryDto
     * @return
     */
    ResultVO selectStoreReturnFinanceTotal(PurchaseStoreExitLibraryDetailQueryDto queryDto);
}
