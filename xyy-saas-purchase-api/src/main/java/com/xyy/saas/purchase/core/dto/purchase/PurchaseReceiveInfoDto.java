package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class PurchaseReceiveInfoDto extends PurchaseBaseDto implements Serializable {

    private Integer id;

    private String organSign;

    /**
     * 收货员
     */
    private String receiveUser;

    private String receiveUserName;

    /**
     * 采购订单编号
     */
    private String parentBillNo;

    /**
     * 收货复核单单号
     */
    private String billNo;

    /**
     * 开票日期
     */
    private Date billTime;

    private String billTimes;

    /**
     * 开票开始日期 - 接收前端传参
     */
    private String beginBillTime;

    /**
     * 开票结束日期 - 接收后端传参
     */
    private String endBillTime;

    /**
     * 收货日期
     */
    private Date receiveTime;

    private String receiveTimes;

    /**
     * 收货开始时间
     */
    private String beginReceiveTime;

    /**
     * 收货结束时间
     */
    private String endReceiveTime;

    /**
     * 商品种类
     */
    private Integer productKind;

    /**
     * 供应商编号
     */
    private String supplierNo;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 总金额
     */
    private BigDecimal taxAmountSum;

    /**
     * 折扣
     */
    private BigDecimal discount;

    /**
     * 折后金额
     */
    private BigDecimal priceAfterDiscount;

    /**
     * 折扣金额
     */
    private BigDecimal priceDiscounted;

    /**
     * 是否冷藏
     */
    private Byte coldStorage;

    /**
     * 承运单位
     */
    private String carrierUnit;

    /**
     * 承运方式
     */
    private String transportMode;

    /**
     * 启运地址
     */
    private String shipment;

    /**
     * 启运时间
     */
    private Date departureTime;

    private String departureTimes;

    /**
     * 到货时间
     */
    private Date arrivalTime;

    private String arrivalTimes;

    /**
     * 收货运单号
     */
    private String receivingNo;

    /**
     * 商城订单号
     */
    private String businessId;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 提交方式
     */
    private String submitStatus;


    private String submitType;

    /**
     * 收货状态
     */
    private Byte receivingState;

    /**
     * 收货状态 - 中文
     */
    private String receivingStateCN;


    private String createUser;


    private Date createTime;


    private String updateUser;


    private Date updateTime;


    private Boolean yn;

    /**
     * 发票日期
     */
    private Date invoiceTime;
    private String invoiceTimes;

    /**
     * 发票结束日期
     */
    private String beginInvoiceTime;

    /**
     * 发票开始日期
     */
    private String endInvoiceTime;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 发票号
     */
    private String invoiceNo;

    /**
     * 质检报告
     */
    private String billFile;


    private String baseVersion;


    private List<String> supplierList;

    /**
     * 供应商外码信息
     */
    private String pharmacyPref;

    /**
     * 采购内容
     */
    private List<Map<String, Object>> purchaseInfo;

    /**
     * 零售价总计
     */
    private BigDecimal retailPriceSum;

    /**
     * 单据详情信息
     */
    private List<PurchaseReceiveDetailDto> purchaseReceiveDetailDtos;

    /**
     * 温湿度记录
     */
    private PurchaseReceiveTemperatureDto temperatureDto;

    private String orderTimes;

    private Date orderTime;

    private String orderUser;

    private String orderUserName;

    private Long updateVersion;

    /**
     * gsp-商品汇总-商品信息
     */
    private String productQuery;

    private BigDecimal receivingAmount;

    /**
     * 发票文件url
     */
    private String invoiceFileUrl;

    /**
     * 随货同行单url
     */
    private String withCargoFileUrl;

    /**
     * 随货同行单名称
     */
    private String withCargoFileName;
    /**
     * 发票文件名称
     */
    private String invoiceFileName;

    /**
     * 存储条件
     */
    private List<Integer> storageCondition;

    /**
     * 收货单类型:1-采购收货; 2-门店退货收货
     */
    private Byte receiptType = 1;

    /**
     * 生产厂家
     */
    private String manufacturer;
}