package com.xyy.saas.purchase.core.api.returngoods;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.exception.BusinessException;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.returngoods.*;

public interface PurchaseReturnWarehouseApi {

    ResultVO<PageInfo<PurchaseReturnWarehouseInfoDto>> list(PurchaseReturnWarehouseQueryRequest request);

    ResultVO<PurchaseReturnWarehouseInfoDto> detail(long id);

    ResultVO inbound(PurchaseReturnWarehouseRequestDto returnWarehouseRequestDto);

    /**
     * 推荐（默认）退货入库库位
     * @param organSign
     * @return
     */
    ResultVO<PurchasePositionRecommendationDto> recommendInboundPosition(String organSign);

}
