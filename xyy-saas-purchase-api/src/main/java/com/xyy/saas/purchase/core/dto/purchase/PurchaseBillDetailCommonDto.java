package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: <PERSON>
 * @CreateTime: 2020-06-28 16:20
 * @Description: 单店采购单据详情实体（兼容采购入库单 采购退出出库单 采购退补价单）
 */
@Setter
@Getter
public class PurchaseBillDetailCommonDto implements Serializable {
    private Long id;

    /**
     * 机构编码
     */
    private String organSign;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 商品内码
     */
    private String productCode;

    /**
     * 商品外码
     */
    private String productPharmacyPref;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品通用名称
     */
    private String commonName;

    /**
     * 商品规格/型号
     */
    private String specifications;

    /**
     * 单位
     */
    private String packingUnit;

    /**
     * 商品生产厂家
     */
    private String manufacturer;

    /**
     * 商品产地
     */
    private String productOriginAddress;

    /**
     * 商品批准文号
     */
    private String approvalNumber;

    /**
     * 数量
     */
    private BigDecimal productAmount;


    /**
     * 商品含税单价 / 新含税价
     */
    private BigDecimal productTaxPrice;

    /**
     * 商品含税总价 / 金额
     */
    private BigDecimal productTaxPriceSum;

    /**
     * 商品批号
     */
    private String productBatchNo;

    /**
     * 商品生产日期
     */
    private Date productDate;
    private String productDateStr;

    /**
     * 商品有效期
     */
    private Date productExpiryDate;
    private String productExpiryDateStr;

    /**
     * 成本均价(后需求更改为成对应含税价含义)
     */
    private BigDecimal costPrice;
}