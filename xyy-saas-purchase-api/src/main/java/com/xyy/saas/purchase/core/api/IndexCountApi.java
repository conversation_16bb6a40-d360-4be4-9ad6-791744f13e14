package com.xyy.saas.purchase.core.api;

import com.xyy.saas.purchase.core.dto.IndexCountDto;

import java.util.List;

/**
 * 首页统计
 * <AUTHOR>
 */
public interface IndexCountApi {

    /**
     * 首页统计
     * 收货复核单、入库验收单、采购入库单
     * @param organSign 机构编码
     * @return
     */
    List<IndexCountDto> getIndexCountNum(String organSign);
    /**
     * 首页统计
     * 收货复核单
     * @param organSign 机构编码
     * @return
     */
    public int receiptReviewCountNum(String organSign);
    /**
     * 首页统计
     * 入库验收单
     * @param organSign 机构编码
     * @return
     */
    public int warehousingAcceptanceNum(String organSign);
    /**
     * 首页统计
     * 采购入库单
     * @param organSign 机构编码
     * @return
     */
    public int purchaseWarehousing(String organSign);

    /**
     * 采购新需求(面包屑)
     * 采购订单、收货复核单、入库验收单、采购入库单
     * @param organSign 机构编码
     * @return
     */
    List<IndexCountDto> getPurchaseCountNum(String organSign);
}
