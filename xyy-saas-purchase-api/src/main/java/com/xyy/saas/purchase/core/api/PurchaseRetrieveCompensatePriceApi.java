package com.xyy.saas.purchase.core.api;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.SaasPurchaseRetrieveBillDetailDto;
import com.xyy.saas.purchase.core.dto.SaasPurchaseRetrieveBillInfoDto;

import java.util.List;

/**
 * Created by zhangjinxia on 2020/2/20.
 */
public interface PurchaseRetrieveCompensatePriceApi {

    /**
     * 采购退补价列表查询
     * @param purchaseRetrieveBillInfoDto
     * @return
     */
    ResultVO getRetrieveCompensatePriceList(SaasPurchaseRetrieveBillInfoDto purchaseRetrieveBillInfoDto);

    /**
     * 查询退补价详情
     * @param billNo
     * @param sortColumn 排序字段 product_date/product_expiry_date/position_no
     * @param sortOrder 排序方式 asc/desc
     * @return
     */
    List<SaasPurchaseRetrieveBillDetailDto> getRetrieveCompensatePriceDetail(String billNo, String sortColumn, String sortOrder, Byte isProductHidden);

    List<SaasPurchaseRetrieveBillDetailDto> getRetrieveCompensatePriceDetail(String billNo, String sortColumn, String sortOrder, Byte isProductHidden, String organSign);
}
