package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by zhangjinxia on 2020/4/18.
 */
@Data
public class PurchaseAcceptInfoQueryDto implements Serializable {

    private static final long serialVersionUID = 2452983172579169959L;

    /**
     * 第几页
     */
    private int pageNum = 1;

    /**
     * 每页多少条
     */
    private int pageSize = 50;

    /**
     * 机构编码
     */
    private String organSign;

    /**
     * 当前操作人id
     */
    private Integer employeeId;

    /**
     * 起始日期
     */
    private String receiveStartTime;

    /**
     * 结束日期
     */
    private String receiveEndTime;

    /**
     * 验收单编号
     */
    private String billNo;

    /**
     * 收货单编号
     */
    private String parentBillNo;

    /**
     *
     * 供应商编号
     */
    private String supplierNo;

    /**
     *
     * 验收员
     */
    private String checkUser;

    /**
     * 验收状态，0-待验收 1-已验收 2-暂存
     */
    private String queryAcceptState;

    /**
     * 导出id列表
     */
    private List<Long> ids;

    /**
     * 验收单类型:1-采购验收; 2-门店退货验收
     */
    private Byte acceptType = 1;
}
