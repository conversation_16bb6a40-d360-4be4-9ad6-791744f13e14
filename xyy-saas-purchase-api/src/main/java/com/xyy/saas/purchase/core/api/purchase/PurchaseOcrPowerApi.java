package com.xyy.saas.purchase.core.api.purchase;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseOcrPowerDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/20 15:20
 */
public interface PurchaseOcrPowerApi {

    /**
     * 批量保存
     * @param list
     * @return
     */
    ResultVO batchSave(List<PurchaseOcrPowerDto> list);

    /**
     * 查询机构的ocr限制次数
     * @param org
     * @return
     */
    PurchaseOcrPowerDto selectOrgPower(String org);
}
