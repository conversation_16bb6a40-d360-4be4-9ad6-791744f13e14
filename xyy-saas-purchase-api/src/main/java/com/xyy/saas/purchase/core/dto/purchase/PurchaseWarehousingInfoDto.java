package com.xyy.saas.purchase.core.dto.purchase;

import com.xyy.saas.purchase.contract.dto.basic.PurchaseBaseInfoDto;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * ClassName: PurchaseWarehousingInfoDto
 * Date:      2020/4/7 15:58
 * author     sunyue
 */
@Data
public class PurchaseWarehousingInfoDto extends PurchaseBaseInfoDto implements Serializable {
    private static final long serialVersionUID = 1877383859114916521L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 机构编码
     */
    private String organSign;
    private String organSignName;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 出库单号（用于一入一出逻辑）
     */
    private String outBillNo;

    /**
     * 入库验收单编号
     */
    private String parentBillNo;

    /**
     * 入库时间
     */
    private Date billTime;
    private String billTimeStr;

    /**
     * 验收时间
     */
    private Date acceptTime;
    private String acceptTimeStr;

    /**
     * 入库员
     */
    private String warehouseUser;

    /**
     * 入库员名称
     */
    private String warehouseUserName;

    /**
     * 验收员
     */
    private String checkUser;

    /**
     * 商品种类数量
     */
    private Integer productKindNum;

    /**
     * 供应商编号
     */
    private String supplierNo;

    /**
     * 供应商外码
     */
    private String pharmacyPref;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 采购单含税总金额/退货金额
     */
    private BigDecimal taxAmountSum;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 单据状态: 01-暂存；02-提交
     */
    private String submitStatus;

    /**
     * 含税金额总计
     */
    private BigDecimal productTaxPriceSum;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 逻辑删除 1-未删除 0-已删除
     */
    private Boolean yn;

    /**
     * 入库状态，0-未入库 1-已入库 9-暂存
     */
    private Byte warehouseState;
    private String warehouseStateStr;

    /**
     * 验收员名称
     */
    private String checkUserName;

    private Integer updateVersion;

    /**
     * 随货同行单
     */
    private String withCargoNo;

    /**
     * 采购订单编号
     */
    private String orderBillNo;
    /**
     * 入库单编号（三方物流平台）
     */
    private String thirdPartyWarehouseBillNo;

    /**
     * 采购员
     */
    private String purchaseUserName;
    /**
     * 收货员
     */
    private String receiveUserName;

    /**
     * 质检员
     */
    private String qualityUserName;

    /**
     * 操作员
     */
    private String operateUserName;

    /**
     * '采购模式 1：有仓-采购 2：无仓要货采购（以销定采）3：无仓采购（以采定销-仓库托管）
     */
    private Integer purchaseMode;

    /**
     * 入库数量总和
     */
    private BigDecimal warehousingAmountSum;

    /**
     * 门店机构号
     */
    private String storeOrganSign;
    /**
     * 门店名称
     */
    private String storeOrganSignName;
    /**
     * 入库类型: 1-采购入库; 2-门店退货入库
     */
    private Byte warehousingType;

    private Long baseVersion;

    /**
     * 未税金额汇总
     */
    private BigDecimal noProductTaxPriceSum;
    /**
     * 税额汇总
     */
    private BigDecimal taxRateSum;

    /**
     * 结算方式:1->预付款;2->实销实结;3->账期;4->压一付二
     */
    private Byte settlementType;

    /**
     * 发票号
     */
    private String invoiceNo;

    private String isInvoiceName;

    /**
     * 截取发票号，供前端展示用
     */
    private String substrInvoiceNo;

    /**
     * 结算方式名称
     */
    private String settlementTypeName;

    /**
     * 入库内容/详情列表
     */
    private List<PurchaseWarehousingDetailDto> detailDtos;


    /**
     * 三方销售退回入库单号(神农XSTHRK)
     */
    private String refundStorageCode;

}
