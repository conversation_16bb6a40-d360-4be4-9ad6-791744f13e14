package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020-11-17
 * @mondify
 * @copyright
 */
@Data
public class ImportTaskDto implements Serializable {

    private Long taskId;

    private String organSign;
    /**
     * 0导入中，1导入全部完成 2导入部分成功部分失败 3导入失败
     */
    private Byte taskStatus;
    /**
     * 失败条数
     */
    private Integer taskFailTotalCount;
    /**
     * 成功条数
     */
    private Integer taskSuccesTotalCount;
    /**
     * 总条数
     */
    private Integer taskTotalCount;



}
