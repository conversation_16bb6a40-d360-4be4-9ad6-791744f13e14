package com.xyy.saas.purchase.core.dto.thirdparty;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * ClassName: ConfigPropertyDto
 * Function:  配置属性 dto
 * Date:      2020/10/14 16:45
 * author     sunyue
 */
@Data
public class ConfigPropertyDto implements Serializable {

    private static final long serialVersionUID = -3411364184539577770L;

    private String organSign;

    @JsonProperty("requireGoodsConf")
    private Requiregoodsconf requiregoodsconf;
    @JsonProperty("requireGoodsPurchaseConf")
    private Requiregoodspurchaseconf requiregoodspurchaseconf;
    @JsonProperty("returnExitStockConf")
    private Returnexitstockconf returnexitstockconf;
    @JsonProperty("deliveryOrderConf")
    private Deliveryorderconf deliveryorderconf;
    @JsonProperty("receiveBillConf")
    private Receivebillconf receivebillconf;
    @JsonProperty("headStoreConf")
    private Headstoreconf headstoreconf;
    @JsonProperty("acceptBillConf")
    private Acceptbillconf acceptbillconf;
    @JsonProperty("warehousingBillConf")
    private Warehousingbillconf warehousingbillconf;
    @JsonProperty("returnGoodsApplyConf")
    private Returngoodsapplyconf returngoodsapplyconf;
    @JsonProperty("returnGoodsConf")
    private Returngoodsconf returngoodsconf;

    /**
     * 总部采购收货单
     */
    @JsonProperty("headPurchaseReceiveConf")
    private HeadPurchaseReceiveConf headPurchaseReceiveConf;

    /**
     * 总部采购验收单
     */
    @JsonProperty("headPurchaseAcceptConf")
    private HeadPurchaseAcceptConf headPurchaseAcceptConf;

    /**
     * 总部采购入库单
     */
    @JsonProperty("headPurchaseWarehouseConf")
    private HeadPurchaseWarehouseConf headPurchaseWarehouseConf;

    /**
     * 总部配送单
     */
    @JsonProperty("headDeliveryConf")
    private HeadDeliveryConf headDeliveryConf;

    /**
     * 门店退货收货单
     */
    @JsonProperty("storeReturnReceiveConf")
    private StoreReturnReceiveConf storeReturnReceiveConf;

    /**
     * 门店退货验收单
     */
    @JsonProperty("storeReturnAcceptConf")
    private StoreReturnAcceptConf storeReturnAcceptConf;

    /**
     * 门店退货入库单
     */
    @JsonProperty("storeReturnWarehouseConf")
    private StoreReturnWarehouseConf storeReturnWarehouseConf;

    /**
     * 总部采购退货出库单
     */
    @JsonProperty("headPurchaseReturnOutInventoryConf")
    private HeadPurchaseReturnOutInventoryConf headPurchaseReturnOutInventoryConf;

    public HeadPurchaseReceiveConf getHeadPurchaseReceiveConf() {
        return headPurchaseReceiveConf;
    }

    public void setHeadPurchaseReceiveConf(HeadPurchaseReceiveConf headPurchaseReceiveConf) {
        this.headPurchaseReceiveConf = headPurchaseReceiveConf;
    }

    public HeadPurchaseAcceptConf getHeadPurchaseAcceptConf() {
        return headPurchaseAcceptConf;
    }

    public void setHeadPurchaseAcceptConf(HeadPurchaseAcceptConf headPurchaseAcceptConf) {
        this.headPurchaseAcceptConf = headPurchaseAcceptConf;
    }

    public HeadPurchaseWarehouseConf getHeadPurchaseWarehouseConf() {
        return headPurchaseWarehouseConf;
    }

    public void setHeadPurchaseWarehouseConf(HeadPurchaseWarehouseConf headPurchaseWarehouseConf) {
        this.headPurchaseWarehouseConf = headPurchaseWarehouseConf;
    }

    public HeadDeliveryConf getHeadDeliveryConf() {
        return headDeliveryConf;
    }

    public void setHeadDeliveryConf(HeadDeliveryConf headDeliveryConf) {
        this.headDeliveryConf = headDeliveryConf;
    }

    public StoreReturnReceiveConf getStoreReturnReceiveConf() {
        return storeReturnReceiveConf;
    }

    public void setStoreReturnReceiveConf(StoreReturnReceiveConf storeReturnReceiveConf) {
        this.storeReturnReceiveConf = storeReturnReceiveConf;
    }

    public StoreReturnAcceptConf getStoreReturnAcceptConf() {
        return storeReturnAcceptConf;
    }

    public void setStoreReturnAcceptConf(StoreReturnAcceptConf storeReturnAcceptConf) {
        this.storeReturnAcceptConf = storeReturnAcceptConf;
    }

    public StoreReturnWarehouseConf getStoreReturnWarehouseConf() {
        return storeReturnWarehouseConf;
    }

    public void setStoreReturnWarehouseConf(StoreReturnWarehouseConf storeReturnWarehouseConf) {
        this.storeReturnWarehouseConf = storeReturnWarehouseConf;
    }

    public HeadPurchaseReturnOutInventoryConf getHeadPurchaseReturnOutInventoryConf() {
        return headPurchaseReturnOutInventoryConf;
    }

    public void setHeadPurchaseReturnOutInventoryConf(HeadPurchaseReturnOutInventoryConf headPurchaseReturnOutInventoryConf) {
        this.headPurchaseReturnOutInventoryConf = headPurchaseReturnOutInventoryConf;
    }

    public void setRequiregoodsconf(Requiregoodsconf requiregoodsconf) {
        this.requiregoodsconf = requiregoodsconf;
    }
    public Requiregoodsconf getRequiregoodsconf() {
        return requiregoodsconf;
    }

    public void setRequiregoodspurchaseconf(Requiregoodspurchaseconf requiregoodspurchaseconf) {
        this.requiregoodspurchaseconf = requiregoodspurchaseconf;
    }
    public Requiregoodspurchaseconf getRequiregoodspurchaseconf() {
        return requiregoodspurchaseconf;
    }

    public void setReturnexitstockconf(Returnexitstockconf returnexitstockconf) {
        this.returnexitstockconf = returnexitstockconf;
    }
    public Returnexitstockconf getReturnexitstockconf() {
        return returnexitstockconf;
    }

    public void setDeliveryorderconf(Deliveryorderconf deliveryorderconf) {
        this.deliveryorderconf = deliveryorderconf;
    }
    public Deliveryorderconf getDeliveryorderconf() {
        return deliveryorderconf;
    }

    public void setReceivebillconf(Receivebillconf receivebillconf) {
        this.receivebillconf = receivebillconf;
    }
    public Receivebillconf getReceivebillconf() {
        return receivebillconf;
    }

    public void setHeadstoreconf(Headstoreconf headstoreconf) {
        this.headstoreconf = headstoreconf;
    }
    public Headstoreconf getHeadstoreconf() {
        return headstoreconf;
    }

    public void setAcceptbillconf(Acceptbillconf acceptbillconf) {
        this.acceptbillconf = acceptbillconf;
    }
    public Acceptbillconf getAcceptbillconf() {
        return acceptbillconf;
    }

    public void setWarehousingbillconf(Warehousingbillconf warehousingbillconf) {
        this.warehousingbillconf = warehousingbillconf;
    }
    public Warehousingbillconf getWarehousingbillconf() {
        return warehousingbillconf;
    }

    public void setReturngoodsapplyconf(Returngoodsapplyconf returngoodsapplyconf) {
        this.returngoodsapplyconf = returngoodsapplyconf;
    }
    public Returngoodsapplyconf getReturngoodsapplyconf() {
        return returngoodsapplyconf;
    }

    public void setReturngoodsconf(Returngoodsconf returngoodsconf) {
        this.returngoodsconf = returngoodsconf;
    }
    public Returngoodsconf getReturngoodsconf() {
        return returngoodsconf;
    }


}
