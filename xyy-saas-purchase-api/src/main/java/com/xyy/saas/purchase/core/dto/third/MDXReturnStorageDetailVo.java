package com.xyy.saas.purchase.core.dto.third;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 曼迪新退货回执
 * <AUTHOR>
 * @date 2020/12/25 15:49
 */
@Data
public class MDXReturnStorageDetailVo implements Serializable {
    /**
     * 商品编码
     */
    private String productCode;

    /**
     * 退货数量(mdx退货回执)
     */
    private BigDecimal returnWarehousingAmount;

    /**
     * 商品拒收数量(mdx退货回执)
     */
    private BigDecimal refuseNumber;

    /**
     * 批号
     */
    private String productBatchNo;

    /**
     * 生产日期
     */
    private String productDate;
    /**
     * 有效期
     */
    private String expireDate;

    /**
     * mdx三方明细序号
     */
    private String mdxId;



}
