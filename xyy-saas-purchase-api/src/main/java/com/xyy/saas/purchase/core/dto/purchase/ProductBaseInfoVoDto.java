package com.xyy.saas.purchase.core.dto.purchase;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ProductBaseInfoVoDto implements Serializable {

    private static final long serialVersionUID = 4050041524939928311L;

    private String productNo; // 商品编号
    private String productName; // 商品名称
    private String attributeSpecification; // 规格、型号
    private Integer unitId; // 单位ID
    private String productPackageUnit;		//包装单位
    private String manufacturer; // 生产厂家
    private String approvalNumber; // 批准文号
    private String producingArea; // 产地
    private Byte prescriptionYn; // 是否处方
    private Integer inventoryNumber; // 批号
    private Date producedDate; // 生产日期
    private Date expirationDate; // 过期时间
    private Integer loseEfficacyDay; //失效天数
    private BigDecimal stockNum; // 库存数量
    private String ruleSpecification; // 拆零规则规格
    private Integer ruleUnitId; // 拆零规则单位ID
    private String categoryName; //商品类别名称
    private Integer categoryId; //商品类别Id
    private Integer agentId; //剂型Id
    private String agentName; //剂型名称
    private String unitName; //单位名称
    private Integer prescriptionId; //处方分类Id
    private String  prescriptionName; //处方分类名称
    private Integer containingHempYn; //是否含麻
    private String incomeTaxRate; //进项税率
    private String ouputTaxRate; //销项税率
    private String barCode; //条形码
    private BigDecimal retailPrice; // 零售价
    private BigDecimal vipPrice; // 会员价
    private BigDecimal costPrice; // 成本价
    private BigDecimal lastCostPrice; // 成本价
    private Long standardLibraryId;	//标准库id
    private String lastThreeTaxPrice; // 最后三次采购入库价格
    private String commonName; //通用名称
    private BigDecimal stockAmount;

    private BigDecimal buyingPrice; // 进货价

    private BigDecimal taxPrice;  //含税价

    public BigDecimal getStockAmount() {
        return stockAmount;
    }

    public void setStockAmount(BigDecimal stockAmount) {
        this.stockAmount = stockAmount;
    }

    public String getProductNo() {
		return productNo;
	}

	public void setProductNo(String productNo) {
		this.productNo = productNo;
	}

	public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getAttributeSpecification() {
        return attributeSpecification;
    }

    public void setAttributeSpecification(String attributeSpecification) {
        this.attributeSpecification = attributeSpecification;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    public String getProducingArea() {
        return producingArea;
    }

    public void setProducingArea(String producingArea) {
        this.producingArea = producingArea;
    }

    public Byte getPrescriptionYn() {
        return prescriptionYn;
    }

    public void setPrescriptionYn(Byte prescriptionYn) {
        this.prescriptionYn = prescriptionYn;
    }

    public Integer getInventoryNumber() {
        return inventoryNumber;
    }

    public void setInventoryNumber(Integer inventoryNumber) {
        this.inventoryNumber = inventoryNumber;
    }

    public Date getProducedDate() {
        return producedDate;
    }

    public void setProducedDate(Date producedDate) {
        this.producedDate = producedDate;
    }

    public Date getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }

    public Integer getLoseEfficacyDay() {
        return loseEfficacyDay;
    }

    public void setLoseEfficacyDay(Integer loseEfficacyDay) {
        this.loseEfficacyDay = loseEfficacyDay;
    }

    public BigDecimal getStockNum() {
        return stockNum;
    }

    public void setStockNum(BigDecimal stockNum) {
        this.stockNum = stockNum;
    }

    public String getRuleSpecification() {
        return ruleSpecification;
    }

    public void setRuleSpecification(String ruleSpecification) {
        this.ruleSpecification = ruleSpecification;
    }

    public Integer getRuleUnitId() {
        return ruleUnitId;
    }

    public void setRuleUnitId(Integer ruleUnitId) {
        this.ruleUnitId = ruleUnitId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getAgentId() {
        return agentId;
    }

    public void setAgentId(Integer agentId) {
        this.agentId = agentId;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Integer getPrescriptionId() {
        return prescriptionId;
    }

    public void setPrescriptionId(Integer prescriptionId) {
        this.prescriptionId = prescriptionId;
    }

    public String getPrescriptionName() {
        return prescriptionName;
    }

    public void setPrescriptionName(String prescriptionName) {
        this.prescriptionName = prescriptionName;
    }

    public Integer getContainingHempYn() {
        return containingHempYn;
    }

    public void setContainingHempYn(Integer containingHempYn) {
        this.containingHempYn = containingHempYn;
    }

    public String getIncomeTaxRate() {
        return incomeTaxRate;
    }

    public void setIncomeTaxRate(String incomeTaxRate) {
        this.incomeTaxRate = incomeTaxRate;
    }

    public String getOuputTaxRate() {
        return ouputTaxRate;
    }

    public void setOuputTaxRate(String ouputTaxRate) {
        this.ouputTaxRate = ouputTaxRate;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public BigDecimal getRetailPrice() {
        return retailPrice;
    }

    public void setRetailPrice(BigDecimal retailPrice) {
        this.retailPrice = retailPrice;
    }

    public BigDecimal getVipPrice() {
        return vipPrice;
    }

    public void setVipPrice(BigDecimal vipPrice) {
        this.vipPrice = vipPrice;
    }

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

	public String getProductPackageUnit() {
		return productPackageUnit;
	}

	public void setProductPackageUnit(String productPackageUnit) {
		this.productPackageUnit = productPackageUnit;
	}

	public Long getStandardLibraryId() {
		return standardLibraryId;
	}

	public void setStandardLibraryId(Long standardLibraryId) {
		this.standardLibraryId = standardLibraryId;
	}

	public BigDecimal getLastCostPrice() {
		return lastCostPrice;
	}

	public void setLastCostPrice(BigDecimal lastCostPrice) {
		this.lastCostPrice = lastCostPrice;
	}

    public String getLastThreeTaxPrice() {
        return lastThreeTaxPrice;
    }

    public void setLastThreeTaxPrice(String lastThreeTaxPrice) {
        this.lastThreeTaxPrice = lastThreeTaxPrice;
    }

    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    public BigDecimal getBuyingPrice() {
        return buyingPrice;
    }

    public void setBuyingPrice(BigDecimal buyingPrice) {
        this.buyingPrice = buyingPrice;
    }

    public BigDecimal getTaxPrice() {
        return taxPrice;
    }

    public void setTaxPrice(BigDecimal taxPrice) {
        this.taxPrice = taxPrice;
    }
}
