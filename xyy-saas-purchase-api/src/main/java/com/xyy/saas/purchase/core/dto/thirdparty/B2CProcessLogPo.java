package com.xyy.saas.purchase.core.dto.thirdparty;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class B2CProcessLogPo  implements Serializable {

    private static final long serialVersionUID = 7581186740968115456L;

    private Long id;
    /**
     * 三方订单号
     */
    private String clientOrderCode;

    /**
     * 平台订单号
     */
    private String platOrderCode;

    /**
     * 流程类型 00:采购正向；01:销售正向； 10:销售逆向； 11:采购逆向
     */
    private String orderType;

    private String orderTypeStr;
    /**
     * 当前步骤
     */
    private String step;

    /**
     * 执行结果  0-成功  1-失败
     */
    private int result;

    private String resultStr;
    /**
     * 完成当前步骤的时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stepTime;

    /**
     * 完成当前步骤的时间
     */
    private String stepTimeStr;

    /**
     * 备注
     */
    private String remark;

    private Date createTime;

    /*
    * 是否可以重试
    */
    private Boolean retry;

    /*
     * 需要重试的ID
     */
    private Long progressId;
}
