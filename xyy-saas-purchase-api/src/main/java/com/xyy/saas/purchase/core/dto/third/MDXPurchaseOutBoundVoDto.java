package com.xyy.saas.purchase.core.dto.third;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class MDXPurchaseOutBoundVoDto implements Serializable {

    private String organSign;

    /**
     * saas侧业务编码 （机构号：出货单）  例：ZHL000123456:CGD202010100001
     */
    private String businessNo;

    /**
     * 三方出库业务编码
     */
    private String outStorageBusinessNo;

    /**
     * 预留参数
     */
    private String tripartiteParameter;

    /**
     * 出库日期
     */
    private String outStorageDate;


    /**
     * 出库类型（A库：请货采购单  B库：出货单）
     */
    private String outStorageType;
    /**
     * 明细
     */
    List<MDXPurchaseOutBoundDetailVoDto> productDetails;
}
