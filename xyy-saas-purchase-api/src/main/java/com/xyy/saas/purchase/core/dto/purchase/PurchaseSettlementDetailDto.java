package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * saas_purchase_settlement_detail
 *
 * <AUTHOR>
@Setter
@Getter
public class PurchaseSettlementDetailDto implements Serializable {
    private Long id;
    /**
     * 采购结算单单据编号
     */
    private String billNo;

    /**
     * 采购单单据编号
     */
    private String purchaseBillNo;

    /**
     * 采购单开票日期
     */
    private Date purchaseBillTime;

    /**
     * 采购单开票日期 - 字符串
     */
    private String purchaseBillTimes;

    /**
     * 单据来源 04-采购入库单 06-采购退出出库单 07-采购退补价单
     */
    private String purchaseSource;

    private String purchaseSourceName;

    /**
     * 销售员
     */
    private String salesperson;

    /**
     * 销售员
     */
    private String salespersonName;

    /**
     * 采购单开票员
     */
    private String purchaseBillingUser;

    /**
     * 采购单开票员Us
     */
    private String purchaseBillingUserName;

    /**
     * 采购单总金额
     */
    private BigDecimal purchaseTotalAmount;

    /**
     * 已结金额
     */
    private BigDecimal settledAmount;

    /**
     * 未结金额
     */
    private BigDecimal openAmount;

    /**
     * 本次结算金额
     */
    private BigDecimal thisTimeSettledAmount;

    /**
     * 操作版本号
     */
    private String baseVersion;

    private static final long serialVersionUID = 1L;
}