package com.xyy.saas.purchase.core.api.purchase;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseBillFailInfoDto;

public interface PurchaseBillFailInfoApi {
    /**
     * 保存调用三方失败信息
     * @param organSign
     * @param billNo
     * @param businessType
     * @param billName
     * @param failInfo
     * @return
     */
    ResultVO<Integer> insertFailInfo(String organSign, String billNo, String businessType, String billName, String failInfo, String createUser);

    /**
     * 查询单据失败信息
     * @param failInfoDto
     * @return
     */
    ResultVO<PageInfo<PurchaseBillFailInfoDto>> selectList(PurchaseBillFailInfoDto failInfoDto);
}
