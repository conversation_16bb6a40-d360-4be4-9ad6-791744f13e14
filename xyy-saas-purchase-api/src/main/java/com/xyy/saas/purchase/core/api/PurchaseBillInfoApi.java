package com.xyy.saas.purchase.core.api;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.datasync.api.DataSyncApi;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.*;
import com.xyy.saas.purchase.core.dto.ocr.OcrAnalysisResultDto;
import com.xyy.saas.purchase.core.dto.ocr.OcrImageParam;
import com.xyy.saas.purchase.core.dto.ocr.SaasSuiHuoTongXingDanDataDto;
import com.xyy.saas.purchase.core.dto.purchase.SaasPurchaseCheckDto;

import java.util.List;
import java.util.Map;

/**
 * 采购模块api接口
 * <AUTHOR>
 * @date 2018/9/8
 */
public interface PurchaseBillInfoApi extends DataSyncApi<SaasPurchaseBillDetailDto> {
    /**
     *查询采购单列表信息
     * @param saasPurchaseBillInfoPoDto
     * @return
     */
    PageInfo<SaasPurchaseBillInfoDto> getPurchaseBillInfoList(PageInfo pageInfo,SaasPurchaseBillInfoPoDto saasPurchaseBillInfoPoDto);

    /**
     *查询采购单列表信息4.0新街口
     * @param saasPurchaseBillInfoPoDto
     * @return
     */
    @Deprecated
    PageInfo<SaasPurchaseBillInfoDto>  getPurchaseBillInfoListPlus(PageInfo pageInfo,SaasPurchaseBillInfoPoDto saasPurchaseBillInfoPoDto);

    /**
     * 删除采购单列表信息
     * @param billNo
     * @return
     */
    ResultVO deletePurchaseBillInfo(String billNo, String organSign);

    /**
     * 采购信息新增后保存
     * @param userId  用户id
     * @param userName  登录名
     * @param organSign 药品唯一标识
     * @param purchaseBillInfoPoDto 采购基本信息
     * @param detailStr 商品字符串
     * @return
     */
    ResultVO savedPurchaseBillInfo(Long userId, String userName, String organSign, SaasPurchaseBillInfoPoDto purchaseBillInfoPoDto, String detailStr);

    /**
     * 采购信息新增后提交
     * @param userId  用户id
     * @param userName  登录名
     * @param organSign 药品唯一标识
     * @param purchaseBillInfoPoDto 采购基本信息
     * @param detailStr 商品字符串
     * @return
     */
    ResultVO submitPurchaseBillInfo(Long userId, String userName, String organSign, SaasPurchaseBillInfoPoDto purchaseBillInfoPoDto, String detailStr);

    /**
     * 查询详情信息，主要是根据type和billNo来查询详情
     * @param purchaseBillInfoPoDto
     * @return
     */
    @Deprecated
    ResultVO getDetailPurchaseBillInfo(SaasPurchaseBillInfoPoDto purchaseBillInfoPoDto);

    /**
     * 查询详情信息，主要是根据type和billNo来查询详情
     * @param purchaseBillInfoPoDto
     * @return
     */
    ResultVO getEditPurchaseBillInfo(SaasPurchaseBillInfoPoDto purchaseBillInfoPoDto);

    /**
     * 收货复核-新增-提取采购单  superiorBillType =01
     * 入库验收单-新增-提取收货复核单  superiorBillType =02
     * 采购入库单-新增-提取入库验收单  superiorBillType =03
     * 采购退出出库 新增-提取采购入库单  superiorBillType =04
     * 采购退出补价单 新增-提取采购入库单  superiorBillType =05
     */
    ResultVO getSuperiorBillList(String superiorBillType);

    /**
     * 编辑前先判断是否可编辑，即单据状态是录入状态为可编辑status = "01"
     * @param billNo
     * @return
     */
    ResultVO checkBeforeEdit(String billNo, String organSign);

    /**
     * 通过编号或者id查询详情信息
     * @param billId,String
     * @return
     */
    ResultVO getDetailListByIdOrBillNo(Long billId,String billNo, String organSign);

    /**
     * 采购退出出库单-保存
     * @param userId
     * @param userName
     * @param organSign
     * @param retrieveBillInfoDto
     * @param retrDetailStr
     * @return
     */
    ResultVO saveRetrieve(Long userId, String userName, String organSign, SaasPurchaseRetrieveBillInfoDto retrieveBillInfoDto, String retrDetailStr);

    /**
     * 采购退出出库单-提交
     * @param userId
     * @param userName
     * @param organSign
     * @param retrieveBillInfoDto
     * @param retrDetailStr
     * @return
     */
    ResultVO submitRetrieve(Long userId, String userName, String organSign, SaasPurchaseRetrieveBillInfoDto retrieveBillInfoDto, String retrDetailStr);

    /**
     * 通过父类编号获取退出单商品详情信息(适用场景：提取单据信息时，详情中的数据)
     * @param parentBillNo
     * @return
     */
    ResultVO getRetrDetailInfoListByParentBillNo(String parentBillNo, String organSign);
    /**
     * 商品批号搜索
     * @param inventoryLotNumberParamDto
     * @return
     */
//    ResultVO queryLotNumber(InventoryLotNumberParamDto inventoryLotNumberParamDto);

    /**
     * 提取商城订单列表
     * @param saleOutDetailParamDto
     * @return
     */
    ResultVO getEcOrderList(PurchaseSaleOutDetailParamDto saleOutDetailParamDto);

    /**
     * 提取商城中-判断有无匹配的商品insertInitPurchaseBillBatch
     * @param saleOutDetailParamDto
     * @return
     */
    ResultVO haveNeedMatchingProduct(PurchaseSaleOutDetailParamDto saleOutDetailParamDto);

    /**
     * 获取一个单据详情：采购退出开票单、采购退出出库单、采购退补价单
     * @param ss
     * @param sidx
     * @param sord
     * @return
     */
    List<SaasPurchaseRetrieveBillDetailDto> getOnePurchaseOfRetrieveDetail(String billNo, String sidx, String sord, Byte isProductHidden);

    /*---*/

    /**
     * 查询采购单明细
     */
    //PageInfo<SaasPurchaseBillDetailDto> getDetailPurchaseListByPage(PageInfo<SaasPurchaseBillDetailDto> pageInfo, SaasPurchaseBillDetailDto billDetailPo);


    /**
     * 提取商城退回订单
     */
    PageInfo<Map<String, Object>> querySaleReturnOrderList(PageInfo<Map<String, Object>> pageInfo, SaleReturnDto dto, String organSign);


    /**
     * 历史采购记录条件分页查询
     * @param pageInfo
     * @param po
     * @return
     */
    ResultVO findPurchaseHistoryPageInfo(PageInfo pageInfo, InitPurchaseBillDto po);

    /**
     * 批量插入历史采购记录
     */
    void insertInitPurchaseBillBatch(List<InitPurchaseBillDto> list,String organSign);

    /**
     * 历史采购记录条件查询
     */
    ResultVO findList(InitPurchaseBillDto po);

    /**
     * 获取一个：采购计划详情、收货复核详情、入库验收详情、采购入库详情
     * @param billNo
     */
    SaasPurchaseBillInfoDto getOnePurchase(String billNo, String organSign);

    /**
     * 获取GSP 入库单详情供应商信息
     * @param billNo
     * @param organSign
     * @return
     */
    SaasPurchaseBillInfoDto getPurchaseInfoByNo(String billNo, String organSign);

    /**
     * 生成电子采购计划单
     * @return
     */
    Map generateElectronicPlanOrder(String organSign);

    /**
     * 删除一个单据：采购计划详情、收货复核详情、入库验收详情、采购入库详情
     * @param billNo
     */
    void deleteOnePurchase(String billNo, String organSign);

    /*---*/

    List<SaasPurchaseRetrieveBillDetailDto> getECCancelOrderDetail(String billNo,String organSign);

    Integer updatePurchaseOfRetrieveStatus(Long billId);

    /**
     * 获取需要匹配的订单商品数量
     * @param bussinessId
     * @return
     */
    Integer getCountMatchingProductList(String bussinessId,String organSign, String providerNum);

    /**
     * 获取需要匹配的退回订单商品数量
     * @param bussinessId
     * @return
     */
    Integer getCountMatchingCancelProductList(String bussinessId,String organSign);

    PageInfo getShelfPostionList(PageInfo pageInfo,Integer flag , Integer shelfPosition, String commodityVal, Integer inventoryFlag);

    //通过编号查询采购单信息
    SaasPurchaseBillInfoPoDto getPurchaseBillInfoByBillNo(String billNo, String organSign);

    /**
     * 查询收获明细
     * @param pageInfo
     * @param purchaseReceiveReportDto
     * @return
     */
    PageInfo<PurchaseReceiveReportDto> queryReceiveDetail(PageInfo pageInfo, PurchaseReceiveReportDto purchaseReceiveReportDto);

    /**
    *@describe:收货明细分类统计
    *@author: Lic
    *@date: 2019/2/15 - 14:22
    *@param: [receiveGroupVoDto]
    *@return: com.github.pagehelper.PageInfo<com.xyy.saas.purchase.core.dto.PurchaseReceiveReportDto>
    */
    PageInfo<PurchaseReceiveReportDto> purchaseReceiveSubtotal(PurchaseReceiveGroupVoDto receiveGroupVoDto);

    /**
     * 查询收获明细统计字段
     * @param
     * @param purchaseReceiveReportDto
     * @return
     */
    Map<String,Object> queryReceiveDetailCount( PurchaseReceiveReportDto purchaseReceiveReportDto);


    /**
     * 提取商城订单
     * @return
     */
    PageInfo querySaleOutOrderList(PageInfo<Map<String, Object>> pageInfo, SaleOutPoDto po, String organSign);

    /**
     * 根据年、月、日统计入库单金额、数量
     * @param selectType 1 日  2 月  3年
     * @param organSign 药店标识
     * @return
     */
    List<Map<String,Object>> selectGroupBySumPriceForMove(Integer selectType,String  organSign);

    /**
     * 根据年、月、日统计入库单金额、数量
     * @param selectType 1 日  2 月  3年
     * @param organSign 药店标识
     * @return
     */
    List<SaasPurchaseMoveGroupDto> selectGroupBySumPriceForMoveNew(Integer selectType,String  organSign);

    /**
     * GSP入库单列表页查询
     * @param saasPurchaseBillInfoDto
     * @return
     */
    ResultVO<PageInfo<SaasPurchaseBillInfoDto>> getGSPPurchaseBillInfoList(SaasPurchaseBillInfoDto saasPurchaseBillInfoDto);



    /**
     * GSP采购记录和入库记录共用一个接口,以商品为维度
     * @param saasPurchaseBillDetailDto
     * @return
     */
    ResultVO<PageInfo<SaasPurchaseBillDetailDto>> getGSPurchaseBillDetailList(PageInfo pageInfo,SaasPurchaseBillDetailDto saasPurchaseBillDetailDto);

    /**
     * GSP采购退出出库单列表查询,以商品为维度
     * @param saasPurchaseRetrieveBillDetailDto
     * @return
     */
    ResultVO getGSPPurchaseRetrList(SaasPurchaseRetrieveBillDetailDto saasPurchaseRetrieveBillDetailDto);

    /**
     * GSP采购记录和入库记录共用一个接口 详情
     * @param id
     * @return
     */
    ResultVO getGSPurchaseBillDetailById(Long id,String organSign, String billNo);
    /**
     * GSP采购退出出库单详情
     * @param id
     * @return
     */
    ResultVO getPurchaseRetrDetailById(Long id,String organSign);

    /**
     * GSP 入库单详情页
     * @param pageInfo
     * @param purchaseBillDto
     * @return
     */
    PageInfo<PurchaseBillDto> getPurchaseDetailList(PageInfo pageInfo,PurchaseBillDto purchaseBillDto);


    // ============================================= 采购新需求接口 ->=============================================
    /**
     * 查询采购入库聚合页列表数据新接口
     * @param saasPurchaseBillInfoPoDto
     * @return
     */
    PageInfo<SaasPurchaseBillInfoDto>  getPurchaseGatherList(PageInfo pageInfo,SaasPurchaseBillInfoPoDto saasPurchaseBillInfoPoDto);

    /**
     * 入库验收退回功能
     * @param billInfoPo
     * @return
     */
    void updatePurchaseBillByBillNo(SaasPurchaseBillInfoPoDto billInfoPo, String organSign);
    // ============================================= <- 采购新需求接口=============================================

    /**
     * 为EC返券v1.0提供的接口 - 月采购入库量
     * beginTime endTime 参数格式为yyyy-MM-dd
     */
    Integer purchaseWarehousing(String organSign, String beginTime, String endTime);

    /**
     * 根据id或billNo获取info信息
     * @param id
     * @param billNo
     * @param organSign
     * @return
     */
    ResultVO<SaasPurchaseBillInfoDto> getPurchaseByIdOrBillNo(Long id, String billNo, String organSign);

    /**
     * 采购入库单、采购退出出库单单据详情打印预览
     * @param purchaseBillDto
     * @param rowSize
     * @param identity
     * @return
     */
    Map<String, Object> purchaseWarehousingPrinting(SaasPurchaseBillInfoPoDto purchaseBillDto, List<Integer> rowSize, String identity);


    /**
     * 采购计划单、采购退出出库单单据详情打印
     * @param purchaseBillDto
     * @param printingRowSize
     * @param identity
     * @return
     */
    List<Map<String, Object>> purchaseWarehousingDetailPrinting(SaasPurchaseBillInfoPoDto purchaseBillDto, List<Integer> printingRowSize, String identity);

    /**
     * 单体采购单据保存时检验商品经营范围是否在机构和供应商经营范围内接口
     * @param organSign
     * @param list
     * @return
     */
    ResultVO checkProductBusinessScope(String organSign, List<SaasPurchaseCheckDto> list);

    /**
     * 根据机构号、单号查询单据ID，用于前端读取缓存之前校验是否可以读取，如果单号在数据库中已存在，则前端不弹出"是否读取缓存"
     * @param organSign
     * @param billNo
     * @return
     */
    ResultVO selectIdByOrgSignAndBillNo(String organSign, String billNo);

    /**
     *药监查询采购单列表信息
     * @param saasPurchaseBillInfoPoDto
     * @return
     */
    PageInfo<SaasPurchaseBillInfoDto> getPurchaseBillInfoListSFDA(PageInfo pageInfo,SaasPurchaseBillInfoPoDto saasPurchaseBillInfoPoDto);

    /**
     * ocr识别图片 用于一步入库
     * @param ocrImageParam
     * @return
     */
    ResultVO<List<OcrAnalysisResultDto<SaasSuiHuoTongXingDanDataDto>>> ocrImagesForSavePurchaseOneStep(OcrImageParam ocrImageParam);
}
