package com.xyy.saas.purchase.core.api.returngoods;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.returngoods.*;

import java.util.List;

/**
 * @Author: <PERSON>
 * @CreateTime: 2020-04-05 17:56
 * @Description: 供应商退出出库单相关API
 */
public interface PurchaseExitLibraryInfoApi {
    /**
     * 查询退出出库单列表
     * @return
     */
    ResultVO<PageInfo<PurchaseExitLibraryInfoDto>> selectPurchaseExitLibraryInfoList(PageInfo pageInfo, PurchaseExitLibraryInfoDto libraryInfoDto);

    /**
     * 根据机构号，单据编号查询退出出库单单据是否存在
     * @param purchaseExitLibraryInfoDto
     * @return
     */
    ResultVO<PurchaseExitLibraryInfoDto> selectPurchaseExitLibraryInfo(PurchaseExitLibraryInfoDto purchaseExitLibraryInfoDto);
    /**
     * 删除退出申请单
     * @param organSign
     * @param billNo
     * @return
     */
    int deletePurchaseExitLibraryInfo(String organSign, String billNo);

    PageInfo selectProductInventoryList(PageInfo pageInfo, PurchaseProductInventoryDto inventoryDto);

    /**
     * 查询退货申请单详情列表
     * @param organSign
     * @param billNo
     * @return
     */
    ResultVO<List<PurchaseExitLibraryDetailDto>> selectPurchaseExitLibraryDetailList(String organSign, String billNo);

    /**
     * 保存供应商退出出库单
     * @param dto
     * @return
     */
    ResultVO saveExitLibrary(PurchaseExitLibraryInfoDto dto);

    /**
     * 编辑供应商退出出库单
     * @param dto
     * @return
     */
    ResultVO updateExitLibrary(PurchaseExitLibraryInfoDto dto);

    /**
     * GSP-供应商退货出库-商品汇总
     * @param libraryInfoDto
     * @return
     */
    ResultVO<PageInfo<PurchaseExitLibraryDetailDto>> selectDetailListGsp(PageInfo pageInfo, PurchaseExitLibraryInfoDto libraryInfoDto);



    /**
     * GSP-供应商退货出库-单据汇总
     * @param libraryInfoDto
     * @return
     */
    ResultVO<PageInfo<PurchaseExitLibraryInfoDto>> selectInfoListGsp(PageInfo pageInfo, PurchaseExitLibraryInfoDto libraryInfoDto);

    /**
     * 复核员列表
     * @param organSign
     * @return
     */
    ResultVO selectReviewUsers(String organSign);


}
