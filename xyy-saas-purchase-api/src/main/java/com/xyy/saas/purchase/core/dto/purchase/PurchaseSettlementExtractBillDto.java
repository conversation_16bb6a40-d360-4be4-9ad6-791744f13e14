package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: <PERSON>
 * @CreateTime: 2020-06-29 19:49
 * @Description: 采购结算单提取的单据实体
 */
@Setter
@Getter
public class PurchaseSettlementExtractBillDto implements Serializable {

    private Integer id;
    /**
     * 单据编号
     */
    private String purchaseBillNo;
    /**
     * 开票时间
     */
    private Date purchaseBillTime;
    /**
     * 开票时间字符串
     */
    private String purchaseBillTimes;
    /**
     * 单据来源
     */
    private String purchaseSource;
    /**
     * 单据来源-中文
     */
    private String purchaseSourceName;
    /**
     * 销售员
     */
    private String salesperson;

    /**
     * 销售员
     */
    private String salespersonName;

    /**
     * 开票员
     */
    private String purchaseBillingUser;

    /**
     * 销售员
     */
    private String purchaseBillingUserName;

    /**
     * 总金额
     */
    private BigDecimal purchaseTotalAmount;
    /**
     * 已结金额
     */
    private BigDecimal settledAmount;
    /**
     * 未结金额
     */
    private BigDecimal openAmount;

    /**
     * 版本号，用于乐观锁
     */
    private Integer version;

    private String billType;
}
