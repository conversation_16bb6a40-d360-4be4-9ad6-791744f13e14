package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class PurchaseBillFailInfoDto implements Serializable {

    private int pageNum = 1;

    private int pageSize = 50;

    private String organSign;

    private String billNo;

    private String billName;

    private String businessType;

    private String requestParam;

    private String failInfo;

    /**
     * 单据调用三方状态 0-失败 1-成功
     */
    private Boolean billState;

    private Date createTime;

    private String createUser;
}