package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

@Data
public class PurchasePlanDto implements Serializable {
    /** */
    private Integer id;

    /** 单据编号*/
    private String billNo;

    private String providerName;

    //提交类型
    private String submitType;

    private String detailStr;

    private String manufacturer;

    private String productName;

    private String beginTime;

    private String userName;

    private String endTime;

    private Integer plan;

    private String employeeId;

    /** 开票日期*/
    private Date billTime;

    /** 供应商编号*/
    private String supplierNo;

    /** 供应商名称*/
    private String supplierName;

    /** 处理单据人员*/
    private String billingUser;

    /** 状态： 1 新建 2已提交 3暂存*/
    private Byte status;

    /** 创建人*/
    private String createUser;

    /** 创建时间*/
    private Date createTime;

    /** 修改人*/
    private String updateUser;

    /** 修改时间*/
    private Date updateTime;

    /** 逻辑删除 1 有效 0 删除*/
    private Byte yn;

    /**  审核状态:0-审核中；1-已通过；2-已驳回*/
    private Byte approveStatus;

    /**
     * 当前待办角色ID
     */
    private Integer approveRoleId;

    /**
     * 当前待办人id
     */
    private Integer approveUserId;

    /**
     * 当前待办部门
     */
    private String approveOrgansignType;

    /**
     * 经营模式 0单体，1连锁，2联营
     */
    private Byte bizModel;

    private String businessKey;

    private int businessScene;

    private String organSign;

    /**
     *来源: 0-正常入库, 1-一步入库
     */
    private Byte submitSource;

    /** 操作版本号*/
    private Integer baseVersion;

    /** 连锁计划开始时间 */
    private String planBeginTime;

    /** 连锁计划结束时间 */
    private String planEndTime;

    private String registeredAddress;

    private List<String> createUsers;

    private List<String> supplierList;
    /** 商品种类*/
    private Long productTypeNumbers;
    /** 金额总计*/
    private BigDecimal totalAmount;
    /** 备注*/
    private String remark;
    /**
     * 预计收货日期
     */
    private Date receivingPlanDate;
    /** 业务创建人,由用户在页面自定义*/
    private String bussinessCreateUser;
    /**
     * 业务创建日期，由用户在页面自定义
     */
    private Date bussinessCreateTime;

    private Byte receivingState;

    private BigDecimal taxAmountSum;

    private BigDecimal priceAfterDiscount;

    private BigDecimal priceDiscounted;

    private String pharmacyPref;

    private String sidx;

    private String sord;

    private String statusType;

    private String approveStatusType;

    private String approveOrgansignTypeDesc;

    private String approveRoleType;

    List<Map<String, Object>> details;

    /** 是否是连锁 0 单体 1 连锁*/
    private Byte isChain;

    private Integer page;

    private Integer rows;

    /**
     * This enum was generated by MyBatis Generator.
     * This enum corresponds to the database table saas_purchase_plan
     *
     * @mbg.generated Thu Apr 02 19:43:23 CST 2020
     */
    public enum Column {
        id("id", "id", "INTEGER", false),
        billNo("bill_no", "billNo", "VARCHAR", false),
        billTime("bill_time", "billTime", "TIMESTAMP", false),
        supplierNo("supplier_no", "supplierNo", "VARCHAR", false),
        supplierName("supplier_name", "supplierName", "VARCHAR", false),
        billingUser("billing_user", "billingUser", "VARCHAR", false),
        status("status", "status", "TINYINT", false),
        createUser("create_user", "createUser", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateUser("update_user", "updateUser", "VARCHAR", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        yn("yn", "yn", "TINYINT", false),
        baseVersion("base_version", "baseVersion", "VARCHAR", false),
        organsign("organSign", "organsign", "VARCHAR", false),
        receivingPlanDate("receiving_plan_date", "receivingPlanDate", "TIMESTAMP", false),
        remark("remark", "remark", "VARCHAR", false),
        bussinessCreateUser("bussiness_create_user", "bussinessCreateUser", "VARCHAR", false),
        bussinessCreateTime("bussiness_create_time", "bussinessCreateTime", "TIMESTAMP", false),
        receivingState("receiving_state", "receivingState", "TINYINT", false),
        productTypeNumbers("product_type_numbers", "productTypeNumbers", "INTEGER", false),
        totalAmount("total_amount", "totalAmount", "DECIMAL", false),
        approveStatus("approve_status", "approveStatus", "BIT", false),
        approveRoleId("approve_role_id", "approveRoleId", "INTEGER", false),
        approveUserId("approve_user_id", "approveUserId", "INTEGER", false),
        submitSource("submit_source", "submitSource", "BIT", false);

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table saas_purchase_plan
         *
         * @mbg.generated Thu Apr 02 19:43:23 CST 2020
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table saas_purchase_plan
         *
         * @mbg.generated Thu Apr 02 19:43:23 CST 2020
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table saas_purchase_plan
         *
         * @mbg.generated Thu Apr 02 19:43:23 CST 2020
         */
        private final String column;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table saas_purchase_plan
         *
         * @mbg.generated Thu Apr 02 19:43:23 CST 2020
         */
        private final boolean isColumnNameDelimited;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table saas_purchase_plan
         *
         * @mbg.generated Thu Apr 02 19:43:23 CST 2020
         */
        private final String javaProperty;

        /**
         * This field was generated by MyBatis Generator.
         * This field corresponds to the database table saas_purchase_plan
         *
         * @mbg.generated Thu Apr 02 19:43:23 CST 2020
         */
        private final String jdbcType;

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table saas_purchase_plan
         *
         * @mbg.generated Thu Apr 02 19:43:23 CST 2020
         */
        public String value() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table saas_purchase_plan
         *
         * @mbg.generated Thu Apr 02 19:43:23 CST 2020
         */
        public String getValue() {
            return this.column;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table saas_purchase_plan
         *
         * @mbg.generated Thu Apr 02 19:43:23 CST 2020
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table saas_purchase_plan
         *
         * @mbg.generated Thu Apr 02 19:43:23 CST 2020
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table saas_purchase_plan
         *
         * @mbg.generated Thu Apr 02 19:43:23 CST 2020
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table saas_purchase_plan
         *
         * @mbg.generated Thu Apr 02 19:43:23 CST 2020
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table saas_purchase_plan
         *
         * @mbg.generated Thu Apr 02 19:43:23 CST 2020
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table saas_purchase_plan
         *
         * @mbg.generated Thu Apr 02 19:43:23 CST 2020
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table saas_purchase_plan
         *
         * @mbg.generated Thu Apr 02 19:43:23 CST 2020
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table saas_purchase_plan
         *
         * @mbg.generated Thu Apr 02 19:43:23 CST 2020
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         * This method was generated by MyBatis Generator.
         * This method corresponds to the database table saas_purchase_plan
         *
         * @mbg.generated Thu Apr 02 19:43:23 CST 2020
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}