package com.xyy.saas.purchase.core.dto.purchase;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 采购计划量VO
 */
public class PurchasePlanSaleDto implements Serializable {
    /**
     * 商品编码
     */
    private  String productCode;
    /**
     * 销售量
     */
    private BigDecimal productNum;
    /**
     * 销售金额
     */
    private BigDecimal actualAmount;

    private  String supplierCode;

    /** 药店唯一标识*/
    private String organsign;

    /**
     * 建议计划天数
     */
    private Integer planDay;
    /**
     * 供应商关键字
     */
    private String planSupplierName;
    /**
     * 建议量大于0
     */
    private String planCount;
    /**
     * 商品名称
     */
    private String planProductName;

    /**
     * 商品类别
     */
    private Integer planProductType;

    private   Byte isHidden;

    private String beginTime;

    private String endTime;


    public BigDecimal getActualAmount() {
        return actualAmount;
    }

    public void setActualAmount(BigDecimal actualAmount) {
        this.actualAmount = actualAmount;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public Byte getIsHidden() {
        return isHidden;
    }

    public void setIsHidden(Byte isHidden) {
        this.isHidden = isHidden;
    }

    public Integer getPlanDay() {
        return planDay;
    }

    public void setPlanDay(Integer planDay) {
        this.planDay = planDay;
    }

    public String getPlanSupplierName() {
        return planSupplierName;
    }

    public void setPlanSupplierName(String planSupplierName) {
        this.planSupplierName = planSupplierName;
    }

    public String getPlanCount() {
        return planCount;
    }

    public void setPlanCount(String planCount) {
        this.planCount = planCount;
    }

    public String getPlanProductName() {
        return planProductName;
    }

    public void setPlanProductName(String planProductName) {
        this.planProductName = planProductName;
    }

    public Integer getPlanProductType() {
        return planProductType;
    }

    public void setPlanProductType(Integer planProductType) {
        this.planProductType = planProductType;
    }

    public String getOrgansign() {
        return organsign;
    }

    public void setOrgansign(String organsign) {
        this.organsign = organsign;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public BigDecimal getProductNum() {
        return productNum;
    }

    public void setProductNum(BigDecimal productNum) {
        this.productNum = productNum;
    }
}
