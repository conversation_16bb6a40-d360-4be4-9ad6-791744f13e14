package com.xyy.saas.purchase.core.api.purchase;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseOcrPowerDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseOcrRuleDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/20 15:20
 */
public interface PurchaseOcrRuleApi {

    /**
     * 批量保存
     * @param list
     * @return
     */
    ResultVO batchSave(List<PurchaseOcrRuleDto> list);
}
