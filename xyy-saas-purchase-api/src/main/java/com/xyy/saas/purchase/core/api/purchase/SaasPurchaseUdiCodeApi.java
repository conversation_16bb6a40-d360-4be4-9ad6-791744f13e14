package com.xyy.saas.purchase.core.api.purchase;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.SaasPurchaseUdiCodeDto;

import java.util.List;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2024/05/23 14:49
 */
public interface SaasPurchaseUdiCodeApi {

    /**
     * 记录udi信息 (损益单使用)
     * @param udiCodeDtos
     * @return
     */
    ResultVO<?> recordSaasUdiCode(String organSign,String billNo,List<SaasPurchaseUdiCodeDto> udiCodeDtos);

    /**
     * 查询udi信息
     * @param udiCodeDto
     * @return
     */
    List<SaasPurchaseUdiCodeDto> querySaasPurchaseUdiCode(SaasPurchaseUdiCodeDto udiCodeDto);

}
