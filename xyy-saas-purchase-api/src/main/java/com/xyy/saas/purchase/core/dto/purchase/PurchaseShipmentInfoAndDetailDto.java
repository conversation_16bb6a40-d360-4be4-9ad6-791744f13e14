package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 采购出货单/销售订单单据和商品聚合数据传输实体
 */
@Getter
@Setter
public class PurchaseShipmentInfoAndDetailDto extends PurchaseShipmentDetailDto  {


    /**
     * 上级单据编号
     */
    private String parentBillNo;

    /**
     * 出货日期，页面选择
     */
    private Date billTime;

    /**
     * 出货日期，页面选择
     */
    private String billTimes;

    /**
     * 开票员
     */
    private String billingUser;
    /**
     * 开票员名称
     */
    private String billingUserName;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 单据状态: 01-暂存；02-提交
     */
    private String submitStatus;

    /**
     * 出货状态，0-未出货 1-出货成功 2-出货中 3-出货失败
     * 仅当仅是查询时9代表暂存状态
     */
    private Byte shipmentState;
    /**
     * 出货状态名称，0-未出货 1-出货成功 2-出货中 3-出货失败
     *
     */
    private String shipmentStateName;
    /**
     * 出库单含税总金额
     */
    private BigDecimal taxAmountSum;


    /**
     * 商品种类
     */
    private Integer productKind;

    /**
     * 出货单商品详情集合
     */
    private List<PurchaseShipmentDetailDto> detailDtos;

    /**
     * 查询开始时间
     */
    private String beginTime;

    /**
     * 查询结束时间
     */
    private String endTime;


    /**
     * 要货门店
     */
    private String storeOrganSign;
    /**
     * 要货门店名称
     */
    private String storeOrganSignCN;

    /**
     * 总部机构名称
     */
    private String hqOrganSignCN;

    /**
     * 收货人id，用于委托配送
     */
    private String receiveUser;
    /**
     * 收货人名称，用于委托配送
     */
    private String receiveUserName;

    /**
     * 收货人电话，用于委托配送
     */
    private String telephone;

    /**
     * 收货人地址，用于委托配送
     */
    private String receiveAddress;

    /**
     * 客户编码(ybm绑定码)，用于委托配送
     */
    private String bindCode;

    private Integer version;

    /**
     * 省市区编码
     */
    private String areaCode;

    /**
     * 委托配送 - 第三方整单取消 - 取消原因
     */
    private String cancelReason;

    /**
     *  采购订单模式 1标准（B2B无仓） 2共仓（B2C无仓单，宜块钱）
     */
    private Byte purchaseOrderModel;
    /**
     *  采购订单模式名称 1标准（B2B无仓） 2共仓（B2C无仓单，宜块钱）
     */
    private String purchaseOrderModelName;
    /**
     *  电商订单编号（宜块钱） 示例：ZX1278284378358681691
     */
    private String ecOrderNo;

    /**
     * 要货单主键id
     */
    private Long requireBillId;

    /**
     * 采购模式 1：有仓-采购 2：无仓要货采购（以销定采）3：无仓采购（以采定销-仓库托管）
     */
    private int purchaseMode;
    /**
     * 出货内容
     */
    private List<Map<String,Object>> shipmentContent;


    /**
     * 提交状态：save 保存，submit提交  表单提交判断用
     */
    private String submitType;

    /*批发业务销售订单使用*/
    /**
     * 销售总数量
     */
    private BigDecimal saleAmountSum;
    /**
     * 业务类型 1-出货单 2-销售订单
     */
    private Byte billType;
    /**
     * 整单折扣
     */
    private BigDecimal wholeDiscount;

}