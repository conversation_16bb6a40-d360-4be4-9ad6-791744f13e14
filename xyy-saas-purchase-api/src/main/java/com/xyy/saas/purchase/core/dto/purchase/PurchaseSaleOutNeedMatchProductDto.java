package com.xyy.saas.purchase.core.dto.purchase;

import com.xyy.saas.purchase.core.dto.ProductBaseInfoVoDto;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/15 17:09
 */
@Getter
@Setter
public class PurchaseSaleOutNeedMatchProductDto implements Serializable {

    private static final long serialVersionUID = 6292318115856094483L;

    /**
     * 订单详情
     */
    private List<ProductBaseInfoVoDto> list;

    /**
     * 系统自动匹配的标准库ID数量
     */
    private int matchedProCount;

    /**
     * 需要手动匹配的标准库ID数量
     */
    private int needMatchProCount;
}
