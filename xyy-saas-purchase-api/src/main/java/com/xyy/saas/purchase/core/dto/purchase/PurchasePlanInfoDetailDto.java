package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @annotation:计划单明细DTO
 * @create 2018-09-18 17:49
 **/
@Data
public class PurchasePlanInfoDetailDto implements Serializable {

    private static final long serialVersionUID = -2064330294134276762L;

    private Integer id;

    private String purchasePlanId;

    private String productCode;

    private String productPharmacyPref;

    private String productName;

    private String commonName;

    private String dosageForm;

    private String specifications;

    private String packingUnit;

    private String manufacturer;

    private BigDecimal stockAmount;
    private BigDecimal productAmount;

    private BigDecimal amount;
    private BigDecimal orderAmount;

    private BigDecimal lastTaxPrice;

    private BigDecimal productTaxPrice;

    private BigDecimal productTaxPriceSum;

    private String supplierNo;

    private String supplierName;

    private String productBatchNo;

    private BigDecimal productMallPrice;

    private BigDecimal retailPrice;

    private String lastProvidePref;

    private String productPlace;

    private String approvalNumber;

    private int productType;

    private BigDecimal productSuggestPlanCount;

    private String createUser;

    private Date createTime;

    private String createTimes;

    private String organSign;

    private Integer baseVersion;

    /**
     * 商品经营范围
     */
    private Integer businessScope;

    private String updateUser;

    private Date updateTime;

    /**
     * 进项税率
     */
    private BigDecimal incomeTaxRate;

    /**
     * 是否禁采(0:否;1:是)
     */
    private Byte disablePurchase;

    //是否启用：0---未启用   1---启用
    private Byte used;

    private String billNo;

    private Date billTime;

    private String billTimes;

    private Byte status;

    private Byte purchaseState;

    private String  purchaseStateCN;

    private String billingUserName;

    private String billingUser;

    /** 上市许可证持有人字段 */
    private String drugPermissionPerson;

    /**
     * 采购数量总计
     */
    private BigDecimal totalAmount;
    /**
     * 采购金额总计
     */
    private BigDecimal totalproductTaxPriceSumAmount;

    /**
     * 商品通用名称
     */
    private String productCommonName;

    /**
     * 委托配送-标准库ID
     */
    private String standardLibraryId;

    /**
     * 商品剂型名称
     */
    private String dosageFormName;

    /**
     * 规格/型号
     */
    private String attributeSpecification;

    /**
     * 商品单位名称
     */
    private String unitName;

    /**
     * 商品零售价
     */
    private BigDecimal productRetailPrice;

    /**
     * 要货数量
     */
    private BigDecimal requireAmount;

    /**
     * 总部采购价
     */
    private BigDecimal headquartersPurchasePrice;

    /**
     * 预占库存数量
     */
    private BigDecimal preemptionNumber;

    /**
     * 预占库存数量
     */
    private BigDecimal notOutPreemptionNumber;

    /**
     * 出库数量
     */
    private BigDecimal deliveryNumber;

    /**
     * 货区Id
     */
    private String cargoArea;

    /**
     * 货位Id
     */
    private String positionNo;

    /**
     * 货区
     */
    private String cargoAreaName;

    /**
     * 销项税率
     */
    private BigDecimal outputTaxRate;

    /**
     * 货位
     */
    private String PositionNoName;

    /**
     * 出库总金额
     */
    private BigDecimal deliveryTaxPriceSum;

    /**
     * 总部采购金额汇总(商品+批号维度)
     */
    private BigDecimal headquartersPurchasePriceSum;

    /**
     * 总部采购金额汇总(商品+批号维度)
     */
    private BigDecimal headquartersPurchasePriceSummary;

    /**
     * 商品生产日期
     */
    private Date productDateStr;

    /**
     * 商品有效期至
     */
    private Date productExpiryDateStr;

    /**
     * 商品产地
     */
    private String productOriginAddress;

    /**
     * 生产许可证号
     */
    private String productLicenseRecordNo;
}

