package com.xyy.saas.purchase.core.api.purchase;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseAcceptDetailDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseAcceptInfoDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseAcceptInfoQueryDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseAcceptOrderDetailQueryDto;

import java.util.List;

/**
 * 入库验收单api
 */
public interface PurchaseAcceptApi {

    /**
     * 查询入库验收单列表
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<PurchaseAcceptInfoDto>> selectByCondition(PurchaseAcceptInfoQueryDto queryDto);

    /**
     * 查询详情信息，主要是根据type和billNo来查询详情
     * @param purchaseAcceptInfoDto
     * @return
     */
    ResultVO getDetailPurchaseBillInfo(PurchaseAcceptInfoDto purchaseAcceptInfoDto);

    /**
     * 查看验收单info 和 details
     * @param dto
     * @return
     */
    ResultVO<PurchaseAcceptInfoDto> selectInfoAndDetailsByBillNo(PurchaseAcceptInfoDto dto);

    /**
     * 根据id查询采购列表
     */
    ResultVO<List<PurchaseAcceptInfoDto>> getListByIds(List<Long> ids);

    /**
     * 验收单退回
     * @param organSign
     * @param billNo
     * @param employeeId
     */
    ResultVO returnPurchaseAcceptByBillNo(String organSign, String billNo, Integer employeeId, Integer version);

    Integer checkSubmit(PurchaseAcceptInfoDto info, List<PurchaseAcceptDetailDto> list);

    /**
     * 导出info列表使用
     * @param queryDto
     * @return
     */
    ResultVO<List<PurchaseAcceptInfoDto>> selectInfoListByCondition(PurchaseAcceptInfoQueryDto queryDto);

    /**
     * 导出detail列表使用
     * @param queryDto
     * @return
     */
    ResultVO<List<PurchaseAcceptDetailDto>> selectDetailListByBillNo(PurchaseAcceptInfoQueryDto queryDto);

    /**
     * 查询验收员列表
     * @param organSign
     * @return
     */
    ResultVO getCreatedBillUsers(String organSign);

    /**
     * 修改验收单
     * @param dto
     * @return
     */
    ResultVO updateAcceptBill(PurchaseAcceptInfoDto dto);

    /**
     * 查询采购验收单单据列表
     * @param purchaseAcceptInfoDto
     * @return
     */
    ResultVO<PageInfo<PurchaseAcceptInfoDto>> queryPurchaseAcceptOrderPage(PurchaseAcceptInfoDto purchaseAcceptInfoDto);

    /**
     * 查询采购验收单商品
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<PurchaseAcceptDetailDto>> queryChainPurchaseAcceptOrderDetailPage(PurchaseAcceptOrderDetailQueryDto queryDto);

    /**
     * 根据单号查验收单详情
     * @param billNo
     * @return
     */
    ResultVO<PurchaseAcceptInfoDto> getPurchaseAcceptOrderDetailByBillNo(String organSign, String billNo);


}
