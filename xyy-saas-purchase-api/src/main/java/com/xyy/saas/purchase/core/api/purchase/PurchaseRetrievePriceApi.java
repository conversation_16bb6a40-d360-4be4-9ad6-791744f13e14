package com.xyy.saas.purchase.core.api.purchase;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.SaasPurchaseBillInfoDto;
import com.xyy.saas.purchase.core.dto.SaasPurchaseRetrieveBillDetailDto;
import com.xyy.saas.purchase.core.dto.SaasPurchaseRetrieveBillInfoDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseAcceptInfoDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseOrderInfoDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseRetrievePriceDetailDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseRetrievePriceInfoDto;

import java.util.List;

/**
 * 采购退补价单api
 */
public interface PurchaseRetrievePriceApi {

    /**
     * 查询采购退补价单列表
     * @param purchaseRetrievePriceInfoDto
     * @return
     */
    ResultVO getBillInfoList(PurchaseRetrievePriceInfoDto purchaseRetrievePriceInfoDto);

    /**
     * 查询详情信息，主要是根据type和billNo来查询详情
     * @param billNo
     * @return
     */
    List<PurchaseRetrievePriceDetailDto> getRetrievePriceDetail(String billNo, String organSign,String sortColumn, String sortOrder);

    /**
     * 查询商品列表
     * @param pageInfo
     * @param name
     * @param organSign
     * @param isHidden
     * @return
     */
    PageInfo queryProductsAndAllStork(PageInfo pageInfo, String name, String organSign, Byte isHidden,String manufacturer,String type);

    ResultVO checkCostPriceByRefund(String organSign, List<PurchaseRetrievePriceDetailDto> purchaseRetrievePriceDetailDto);

    /**
     * 保存采购退补价
     * @param info
     * @param list
     * @param userName
     * @return
     */
    Integer savePurchaseOfRetrieve(PurchaseRetrievePriceInfoDto info, List<PurchaseRetrievePriceDetailDto> list, String userName);

    ResultVO<List<PurchaseRetrievePriceInfoDto>> getListByIds(List<Long> ids);

    ResultVO<PurchaseRetrievePriceInfoDto> getPurchaseRetrInfo(String billNo, String organSign);

    /**
     * 查询采购退补价出现过的员工
     * @param purchaseRetrievePriceInfoDto
     * @return
     */
    ResultVO getBillingUsers(PurchaseRetrievePriceInfoDto purchaseRetrievePriceInfoDto);



}
