package com.xyy.saas.purchase.core.api.purchase;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.purchase.ImportTaskDto;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-11-17
 * @mondify
 * @copyright
 */
public interface PurchaseImportTaskApi {
    /**
     * 查询导入任务信息
     * @param importTaskDto
     * @return
     */
    ResultVO<Map<String,Object>> getImportTaskInfo(ImportTaskDto importTaskDto);
}
