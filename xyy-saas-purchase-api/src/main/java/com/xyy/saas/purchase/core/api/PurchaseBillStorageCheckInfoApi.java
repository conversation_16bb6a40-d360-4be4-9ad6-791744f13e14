package com.xyy.saas.purchase.core.api;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.SaasPurchaseBillInfoDto;
import com.xyy.saas.purchase.core.dto.SaasPurchaseBillInfoPoDto;

/**
 * 入库验收单API
 * Created by zhangjinxia on 2020/2/17.
 */
public interface PurchaseBillStorageCheckInfoApi {

    /**
     * 查询入库验收单列表
     * @param saasPurchaseBillInfoPoDto
     * @return
     */
    PageInfo<SaasPurchaseBillInfoDto> getBillInfoList(PageInfo pageInfo, SaasPurchaseBillInfoPoDto saasPurchaseBillInfoPoDto);

    /**
     * 查询详情信息，主要是根据type和billNo来查询详情
     * @param purchaseBillInfoPoDto
     * @return
     */
    ResultVO getDetailPurchaseBillInfo(SaasPurchaseBillInfoPoDto purchaseBillInfoPoDto);
}
