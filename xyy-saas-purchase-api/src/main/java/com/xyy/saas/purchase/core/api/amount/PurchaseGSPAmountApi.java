package com.xyy.saas.purchase.core.api.amount;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseCommonAmountDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseOrderDetailQueryDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseOrderInfoDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseWarehousingQueryDto;
import com.xyy.saas.purchase.core.dto.requiregoods.PurchaseStoreRequireGoodsQueryDto;

public interface PurchaseGSPAmountApi {
    /**
     * 查询门店要货单GSP汇总
     * @return
     */
    ResultVO<PurchaseCommonAmountDto> queryStoreRequireGoodsOrderGSPAmount(PurchaseStoreRequireGoodsQueryDto queryDto);

    /**
     * 查询门店要货单明细GSP汇总
     * @param queryDto
     * @return
     */
    ResultVO<PurchaseCommonAmountDto> queryStoreRequireGoodsDetailGSPAmount(PurchaseStoreRequireGoodsQueryDto queryDto);
    /**
     * 查询采购订单GSP汇总
     * @param queryDto
     * @return
     */
    ResultVO<PurchaseCommonAmountDto> queryPurchaseOrderGSPAmount(PurchaseOrderInfoDto queryDto);
    /**
     * 查询采购订单明细GSP汇总
     * @param queryDto
     * @return
     */
    ResultVO<PurchaseCommonAmountDto> queryPurchaseDetailGSPAmount(PurchaseOrderDetailQueryDto queryDto);

    /**
     * 查询采购订单GSP汇总
     * @param queryDto
     * @return
     */
    ResultVO<PurchaseCommonAmountDto> queryPurchaseWareHousingOderGSPAmount(PurchaseWarehousingQueryDto queryDto);
    /**
     * 查询采购订单明细GSP汇总
     * @param queryDto
     * @return
     */
    ResultVO<PurchaseCommonAmountDto> queryPurchaseWareHousingGSPAmount(PurchaseWarehousingQueryDto queryDto);


}
