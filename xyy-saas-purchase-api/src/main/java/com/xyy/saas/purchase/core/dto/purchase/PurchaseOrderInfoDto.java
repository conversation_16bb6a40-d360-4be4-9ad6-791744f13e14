package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class PurchaseOrderInfoDto extends PurchaseBaseDto implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 机构号
     */
    private String organSign;

    /**
     * 上级单据编号
     */
    private String parentBillNo;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 采购日期
     */
    private Date billTime;

    /**
     * 采购日期
     */
    private String billTimes;

    /**
     * 供应商编号
     */
    private String supplierNo;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 商品种类
     */
    private Integer productKind;

    /**
     * 开票员
     */
    private String billingUser;

    /**
     * 开票员
     */
    private String purchaserUser;

    /**
     * 开票员名称
     */
    private String billingUserName;

    /**
     * 采购单含税总金额
     */
    private BigDecimal taxAmountSum;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 单据状态: 01-暂存；02-提交
     */
    private String submitStatus;

    /**
     * 采购状态，0-未采购 1-正常入库已采购 2-一步入库已采购
     */
    private Integer purchaseState;
    /**
     * 状态文字
     */
    private String purchaseStateText;

    /**
     * 可提取状态，0-可提取 1-不可提取
     */
    private Integer extractState;

    /**
     *  审核状态:0-暂存;1-审核中；2-已通过；3-已驳回
     */
    private Byte approveStatus;
    private String approveStatusCN;

    /**
     * 创建人
     */
    private String createUser;
    private String createUserName;

    /**
     * 创建时间
     */
    private Date createTime;
    private String createTimeStr;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 逻辑删除 1-未删除 0-已删除
     */
    private Integer yn;

    /**
     * 操作版本号
     */
    private String baseVersion;
    /**
     * 更新版本号
     */
    private String updateVersion;

    /**
     * 更新回传字段
     */
    private String updateVersionFront;

    /**
     * 采购员
     */
    private String purchaseUser;

    /**
     * 采购员中文
     */
    private String purchaseUserName;
    /**
     * 采购员名称
     */
    private String purchaseName;

    /**
     * 采购内容
     */
    private List<Map<String, Object>> detailPos;

    /**
     * 商品编号
     */
    private String pharmacyPref;

    /**
     * 采购订单详情
     */
    private List<PurchaseOrderDetailDto> detailDtos;

    private BigDecimal priceAfterDiscount;

    private BigDecimal priceDiscounted;

    private String submitType;

    private String submitTypeReal;

    /**
     * 查询开始时间
     */
    private String beginTime;

    /**
     * 查询结束时间
     */
    private String endTime;

    // *********************************************** 连锁 & 委托配送 & 对接神农 新加字段 ***************************************************
    /**
     * 要货门店
     */
    private String storeOrganSign;
    /**
     * 要货门店名称
     */
    private String storeOrganSignCN;

    /**
     * 总部机构名称
     */
    private String organSignCN;

    /**
     * 收货人id，用于委托配送
     */
    private String receiveUser;
    /**
     * 收货人名称，用于委托配送
     */
    private String receiveUserName;

    /**
     * 收货人电话，用于委托配送
     */
    private String telephone;

    /**
     * 收货人地址，用于委托配送
     */
    private String receiveAddress;

    /**
     * 客户编码(ybm绑定码)，用于委托配送
     */
    private String bindCode;

    /**
     * 发票类型，用于委托配送
     */
    private Integer invoiceType;

    /**
     * 电子发票是否随货通行 0：不随行 1：随行，用于委托配送
     */
    private Integer invoiceDelivery;

    private Integer version;

    /**
     * 省市区编码
     */
    private String areaCode;

    /**
     * 委托配送 - 第三方整单取消 - 取消原因
     */
    private String cancelReason;
    /**
     * PAUSE: 暂存 WAIT: 待采购  COMPLETE: 已采购 DOING: 采购中 FAILED: 失败
     * 全部:"";待采购:0;已采购:1
     */
    private String stateStr;
    // *********************************************** 连锁 & 委托配送 & 对接神农 新加字段 ***************************************************

    /**
     * 采购单含税总金额
     */
    private BigDecimal requireTaxAmountSum;
    /**
     *  采购订单模式 1标准（B2B无仓） 2共仓（B2C无仓单，宜块钱）
     */
    private Byte purchaseOrderModel;
    /**
     *  采购订单模式名称 1标准（B2B无仓） 2共仓（B2C无仓单，宜块钱）
     */
    private String purchaseOrderModelName;
    /**
     *  电商订单编号（宜块钱） 示例：ZX1278284378358681691
     */
    private String ecOrderNo;

    /**
     * 随货同行单
     */
    private String withCargoNo;



    /**
     * 要货单主键id
     */
    private Long requireBillId;

    /**
     * 采购模式 1：有仓-采购 2：无仓要货采购（以销定采）3：无仓采购（以采定销-仓库托管）
     */
    private Integer purchaseMode;


    /**
     * 预计到货时间
     */
    private Date  predictArrivalTime;
    /**
     * 运输方式  1：陆运 2：空运 3：船运
     */
    private Byte transportMode;
    /**
     * 运输工具  1：厢式货车 2：冷藏车 3：保温车 4：冷藏箱 5：其他封闭式车辆
     */
    private Byte transportMeans;

    /**
     * 送货方式  1：送货上门 2：公司配送 3：公司自提 4：客户自提 5：委托配送
     */
    private Byte deliveryMode;

    /**
     * 委托运输  1：是   0：否
     */
    private Byte consignTransport;

    /**
     * 当前待办人id
     */
    private Integer approveUserId;
    private String approveUserName;

    /**
     * 当前待办角色id
     */
    private String approveRoleId;
    private String approveRoleName;

    /**
     * 当前待办部门
     */
    private String approveOrgansignType;
    private String approveOrganSignTypeName;

    /**
     * 提交状态 01-暂存 02-提交
     */
    private Byte status;
    /**
     * 采购单采购总数量
     */
    private BigDecimal totalAmount;
    /**
     * 采购商品种类
     */
    private Integer productTypeNumbers;

    /**
     * 审核所需的businessKey
     */
    private String businessKey;

    /**
     * 审核所需要业务场景
     */
    private int businessScene;

    /**
     * 有无审批权限 0-无权限 1-有权限
     */
    private Integer approvePower = 0;

    /**
     * 机构名称
     */
    private String drugstoreName;

    /**
     * 商品名称
     */
    private String productQuery;

    private Integer pageSize;

    private Integer pageNum;

    /**
     * 第三方单号
     */
    private String thirdPartyBillNo;

    /**
     * 第三方销售单编号 神农的销售单号
     */
    private String thirdPartySellNo;

    /**
     * 三方销售单状态 0：待同步 1:待审核;2:待人工处理;3:出库中;4:已出库;5:拆单6:已取消
     */
    private Integer thirdPartySellStatus;

    /**
     * 采购订单生成来源:0-新建;1-提取采购计划单;2-提取要货单
     */
    private Byte extract;

    /**
     * 订单状态
     */
    private String stateSql;

    /**
     * 当前登录人
     */
    private Integer employeeId;

    /**
     * 采购版本 默认1.0，采购标准版：2.0
     * @link PurchaseConstants
     */
    private String packageVersion ;
}