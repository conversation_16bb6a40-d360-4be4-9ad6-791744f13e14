package com.xyy.saas.purchase.core.dto.purchase;

import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 采购实体dto
 *
 * <AUTHOR>
 * @date 2018/9/8
 */
public class SaasPurchaseBillInfoPoDto implements Serializable{

    private static final long serialVersionUID = -766842111666641454L;
    private Long id;

    private String guid;

    private String parentBillNo;

    @Pattern(regexp="^(CGD|FHD|YSD|RKD).*")
    private String billNo;

    private Date billTime;

    private String billType;

    private String supplierNo;

    /**
     * 仅前端显示使用
     */
    private String supplierNoText;

    private String supplierName;

    /**
     * 真是名字
     */
    private String billingUser;

    private String billingId;
    /**
     * 登录名字
     */
    private String billingUserLoginName;

    private BigDecimal taxAmountSum;

    private BigDecimal discount;

    private BigDecimal priceAfterDiscount;

    private BigDecimal priceDiscounted;

    private BigDecimal arrivalTemperature;

    private String carrierUnit;

    private String shipment;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date departureTime;

    private String transportMode;

    private String remarks;

    private String status;

    private String createUser;

    private Date createTime;

    private String updateUser;

    private Date updateTime;

    private Boolean yn;

    private Byte upload;

    private Byte receivingState;

    private String purchaseBillNo;

    private String beginTime;
    private String endTime;
    private String sidx;//排序字段
    private String sord;//排序方式
    private Integer baseVersion;//版本
    private String organSign;//机构唯一标识
    private Integer page;//页码
    private Integer rows;//每页条数

    private String billTimes;//扩展字段时间

    //新增，查询使用
    private String supplier_name;
    private String lastThreeTaxPrice;

    private List<String> supplierList;

    private List<String> billingUserList;

    /**
     * 商品种类数
     **/
    private  Integer productKind;

    /**
     * 收货日期
     **/
    private Date receivedTime;

    private String reviewUser;//审核人

    private String planNo;//计划单号

    private String businessId;//ybm单号

    private String bizModel;//经营模式

    /*
     * 发票日期
     */
    private String invoiceTimes;


    private Date invoiceTime;
    /*
     * 发票代码
     */
    private String invoiceCode;
    /*
     * 发票号
     */
    private String invoiceNo;
    /*
     * 收货运单号
     */
    private String receivingNo;

    /**
     * 是否冷藏
     */
    private Byte coldStorage;

    /**
     * 启运温度
     */
    private BigDecimal startT;

    /**
     * 到货温度
     */
    private BigDecimal endT;

    /**
     * 运输温度json
     */
    private String transportT;

    /**
     * 到货湿度
     */
    private BigDecimal endHumidity;

    /**
     * 收货复核单-附件
     */
    private String billFile;

    public String getBizModel() {
        return bizModel;
    }

    public void setBizModel(String bizModel) {
        this.bizModel = bizModel;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Byte getColdStorage() {
        return coldStorage;
    }

    public void setColdStorage(Byte coldStorage) {
        this.coldStorage = coldStorage;
    }

    public String getTransportT() {
        return transportT;
    }

    public void setTransportT(String transportT) {
        this.transportT = transportT;
    }

    public BigDecimal getEndT() {
        return endT;
    }

    public void setEndT(BigDecimal endT) {
        this.endT = endT;
    }

    public BigDecimal getEndHumidity() {
        return endHumidity;
    }

    public void setEndHumidity(BigDecimal endHumidity) {
        this.endHumidity = endHumidity;
    }

    public String getInvoiceTimes() {
        return invoiceTimes;
    }

    public void setInvoiceTimes(String invoiceTimes) {
        this.invoiceTimes = invoiceTimes;
    }

    public Date getInvoiceTime() {
        return invoiceTime;
    }

    public void setInvoiceTime(Date invoiceTime) {
        this.invoiceTime = invoiceTime;
    }

    /**
     * 到货日期
     */
    private Date arrivalTime;
    private String arrivalTimes;
    // 查询收货复核单时，多传一个参数source：来源 区分是采购管理查询还是GSP查询
    private String source;

    public String getArrivalTimes() {
        return arrivalTimes;
    }

    public void setArrivalTimes(String arrivalTimes) {
        this.arrivalTimes = arrivalTimes;
    }

    public Date getArrivalTime() {
        return arrivalTime;
    }

    public void setArrivalTime(Date arrivalTime) {
        this.arrivalTime = arrivalTime;
    }

    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode;
    }

    public String getReceivingNo() {
        return receivingNo;
    }

    public void setReceivingNo(String receivingNo) {
        this.receivingNo = receivingNo;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }



    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getPlanNo() {
        return planNo;
    }

    public void setPlanNo(String planNo) {
        this.planNo = planNo;
    }

    public String getReviewUser() {
        return reviewUser;
    }

    public void setReviewUser(String reviewUser) {
        this.reviewUser = reviewUser;
    }

    public Integer getProductKind() {
        return productKind;
    }

    public void setProductKind(Integer productKind) {
        this.productKind = productKind;
    }

    public Date getReceivedTime() {
        return receivedTime;
    }

    public void setReceivedTime(Date receivedTime) {
        this.receivedTime = receivedTime;
    }

    public List<String> getBillingUserList() {
        return billingUserList;
    }

    public void setBillingUserList(List<String> billingUserList) {
        this.billingUserList = billingUserList;
    }

    public String getBillingId() {
        return billingId;
    }

    public void setBillingId(String billingId) {
        this.billingId = billingId;
    }

    public String getSupplier_name() {
        return supplier_name;
    }

    public void setSupplier_name(String supplier_name) {
        this.supplier_name = supplier_name;
    }

    public String getLastThreeTaxPrice() {
        return lastThreeTaxPrice;
    }

    public void setLastThreeTaxPrice(String lastThreeTaxPrice) {
        this.lastThreeTaxPrice = lastThreeTaxPrice;
    }

    public Integer getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(Integer baseVersion) {
        this.baseVersion = baseVersion;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public String getSidx() {
        return sidx;
    }

    public void setSidx(String sidx) {
        this.sidx = sidx;
    }

    public String getSord() {
        return sord;
    }

    public void setSord(String sord) {
        this.sord = sord;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getParentBillNo() {
        return parentBillNo;
    }

    public void setParentBillNo(String parentBillNo) {
        this.parentBillNo = parentBillNo == null ? null : parentBillNo.trim();
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo == null ? null : billNo.trim();
    }

    public Date getBillTime() {
        return billTime;
    }

    public void setBillTime(Date billTime) {
        this.billTime = billTime;
    }

    public String getBillType() {
        return billType;
    }

    public void setBillType(String billType) {
        this.billType = billType == null ? null : billType.trim();
    }

    public String getSupplierNo() {
        return supplierNo;
    }

    public void setSupplierNo(String supplierNo) {
        this.supplierNo = supplierNo == null ? null : supplierNo.trim();
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName == null ? null : supplierName.trim();
    }

    public String getBillingUser() {
        return billingUser;
    }

    public void setBillingUser(String billingUser) {
        this.billingUser = billingUser == null ? null : billingUser.trim();
    }

    public BigDecimal getTaxAmountSum() {
        return taxAmountSum;
    }

    public void setTaxAmountSum(BigDecimal taxAmountSum) {
        this.taxAmountSum = taxAmountSum;
    }

    public BigDecimal getDiscount() {
        return discount;
    }

    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }

    public BigDecimal getPriceAfterDiscount() {
        return priceAfterDiscount;
    }

    public void setPriceAfterDiscount(BigDecimal priceAfterDiscount) {
        this.priceAfterDiscount = priceAfterDiscount;
    }

    public BigDecimal getPriceDiscounted() {
        return priceDiscounted;
    }

    public void setPriceDiscounted(BigDecimal priceDiscounted) {
        this.priceDiscounted = priceDiscounted;
    }

    public BigDecimal getArrivalTemperature() {
        return arrivalTemperature;
    }

    public void setArrivalTemperature(BigDecimal arrivalTemperature) {
        this.arrivalTemperature = arrivalTemperature;
    }

    public String getCarrierUnit() {
        return carrierUnit;
    }

    public void setCarrierUnit(String carrierUnit) {
        this.carrierUnit = carrierUnit == null ? null : carrierUnit.trim();
    }

    public String getShipment() {
        return shipment;
    }

    public void setShipment(String shipment) {
        this.shipment = shipment == null ? null : shipment.trim();
    }

    public Date getDepartureTime() {
        return departureTime;
    }

    public void setDepartureTime(Date departureTime) {
        this.departureTime = departureTime;
    }

    public String getTransportMode() {
        return transportMode;
    }

    public void setTransportMode(String transportMode) {
        this.transportMode = transportMode == null ? null : transportMode.trim();
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks == null ? null : remarks.trim();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getYn() {
        return yn;
    }

    public void setYn(Boolean yn) {
        this.yn = yn;
    }

    public Byte getUpload() {
        return upload;
    }

    public void setUpload(Byte upload) {
        this.upload = upload;
    }

    public Byte getReceivingState() {
        return receivingState;
    }

    public void setReceivingState(Byte receivingState) {
        this.receivingState = receivingState;
    }

    public String getPurchaseBillNo() {
        return purchaseBillNo;
    }

    public void setPurchaseBillNo(String purchaseBillNo) {
        this.purchaseBillNo = purchaseBillNo == null ? null : purchaseBillNo.trim();
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getBillingUserLoginName() {
        return billingUserLoginName;
    }

    public void setBillingUserLoginName(String billingUserLoginName) {
        this.billingUserLoginName = billingUserLoginName;
    }

    public String getSupplierNoText() {
        if(supplierNo != null) {
            supplierNoText = supplierNo;
        }

        return supplierNoText;
    }

    public void setSupplierNoText(String supplierNoText) {
        this.supplierNoText = supplierNoText;
    }

    public List<String> getSupplierList() {
        return supplierList;
    }

    public void setSupplierList(List<String> supplierList) {
        this.supplierList = supplierList;
    }

    public String getBillTimes() {
        return billTimes;
    }

    public void setBillTimes(String billTimes) {
        this.billTimes = billTimes;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public BigDecimal getStartT() {
        return startT;
    }

    public void setStartT(BigDecimal startT) {
        this.startT = startT;
    }

    public String getBillFile() {
        return billFile;
    }

    public void setBillFile(String billFile) {
        this.billFile = billFile;
    }
}
