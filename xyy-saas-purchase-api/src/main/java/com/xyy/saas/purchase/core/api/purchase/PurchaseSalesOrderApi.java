package com.xyy.saas.purchase.core.api.purchase;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseShipmentDetailDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseShipmentInfoAndDetailDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseShipmentInfoDto;
import com.xyy.saas.purchase.core.dto.requiregoods.PurchaseProductInfoQueryDto;

import java.util.List;

/**
 * 销售订单管理API(批发业务-非药品)
 * <AUTHOR>
 * @date 2020-12-15
 * @mondify
 * @copyright
 */
public interface PurchaseSalesOrderApi {
    /**
     * 分页查询销售订单信息
     * @param pageInfo
     * @param purchaseShipmentInfoDto
     * @return
     */
    ResultVO<PageInfo<PurchaseShipmentInfoDto>> getPurchaseSalesOrderListByPage(PageInfo pageInfo, PurchaseShipmentInfoDto purchaseShipmentInfoDto);

    /**
     * 根据机构号，单据编号查询单据信息
     * @param organSign
     * @param billNo
     * @param employeeId 当前系统登录人
     * @return
     */
    ResultVO<PurchaseShipmentInfoDto> getPurchaseSalesOrderInfoAndDetailByBillNo(String organSign, String billNo, Integer employeeId);

    /**
     * 保存销售订单 - 总部请货拆单调用
     * @param dto
     * @return
     */
    ResultVO<Integer> savePurchaseSalesOrderInfoAndDetail(PurchaseShipmentInfoDto dto);

    /**
     * 条件查询销售订单汇总数据
     * @param purchaseShipmentInfoDto
     * @return
     */
    ResultVO<PurchaseShipmentInfoDto> getPurchaseSalesOrderSummaryByCondition(PurchaseShipmentInfoDto purchaseShipmentInfoDto);

    /**
     * 条件查询销售订单明细数据
     * @param purchaseShipmentInfoDto
     * @return
     */
    ResultVO<PageInfo<PurchaseShipmentInfoAndDetailDto>> getPurchaseSalesOrderDetailByPage(PageInfo pageInfo,PurchaseShipmentInfoDto purchaseShipmentInfoDto);
    /**
     * 暂存
     * @return
     */
    ResultVO<Integer> pausePurchaseSalesOrder(PurchaseShipmentInfoDto applyInfoDto);

    /**
     * 出货/编辑
     * @param dto
     * @return
     */
    ResultVO<Integer> submitPurchaseSalesOrder(PurchaseShipmentInfoDto dto) ;

    /**
     * 销售订单复用
     * @param dto{organSign, billNo(), employeeId.toString()}
     * @return
     */
    ResultVO purchMultiplex(PurchaseShipmentInfoDto dto);

    ResultVO<List<PurchaseShipmentDetailDto>> getImportSellDetailList(PurchaseProductInfoQueryDto dto);

    /**
     * 删除销售订单
     * @param dto{organSign, billNo(), employeeId.toString()}
     * @return
     */
    ResultVO delSalesOrder(PurchaseShipmentInfoDto dto);
}
