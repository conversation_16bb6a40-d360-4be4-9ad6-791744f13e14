package com.xyy.saas.purchase.core.dto.purchase;

import com.xyy.saas.purchase.core.dto.SaasPurchaseBillDetailDto;
import com.xyy.saas.purchase.core.dto.SaasPurchaseBillInfoPoDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *查看详情临时dto
 * Created by lijing on 2018/9/8.
 */
@Data
public class PurchaseInfoAndStrTemporaryDto implements Serializable {
    private static final long serialVersionUID = 3050041524939928596L;

    private PurchaseOrderInfoDto purchaseOrderInfoDto;//采购订单实体dto

    private List<PurchaseOrderDetailDto> purchaseOrderDetailDto;//采购订单详情

    private PurchaseAcceptInfoDto purchaseAcceptInfoDto;//采购验收单实体dto

    private List<PurchaseAcceptDetailDto> purchaseAcceptDetailDto;//采购验收单详情


}
