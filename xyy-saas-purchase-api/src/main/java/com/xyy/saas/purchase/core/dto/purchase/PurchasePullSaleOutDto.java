package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/8/20 14:40
 */
@Getter
@Setter
public class PurchasePullSaleOutDto implements Serializable {

    private static final long serialVersionUID = 8347898399743234509L;

    private String organSign;

    private String thirdPartyCode;

    private String ecOrderNo;

    private Date startTime;

    private Date endTime;

    private Boolean updateRedisStartTime;

    /**
     * 订单来源 0-药帮忙 2-扁鹊 1-POP
     */
    private String orderSource;

    /**
    * 调用来源 0：同步商城订单（单号） 1：定时任务  2：同步商城订单（全部，默认一个月）
    */
    private Byte invokeSource;
}
