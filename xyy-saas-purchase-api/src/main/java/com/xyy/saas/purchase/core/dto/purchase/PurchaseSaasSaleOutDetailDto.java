package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 字段类型不要改 不要写基础类型
 * 此类有用  com.xyy.saas.purchase.server.service.purchase.impl.ThirdPurchaseDataImportServiceImpl#analysisPurchaseExcel(java.util.List, com.xyy.saas.common.dto.SaasThirdImportConfigDto, com.xyy.saas.purchase.contract.dto.purchase.query.ThirdPurchaseDataImportQueryDto)
 * ${@link com.xyy.saas.purchase.server.service.purchase.impl.ThirdPurchaseDataImportServiceImpl}
 * <AUTHOR>
 * @date 2020/8/14 17:18
 */
@Getter
@Setter
public class PurchaseSaasSaleOutDetailDto implements Serializable {

    private int seqIndex;
    /**
     * 主键
     */
    private Long id;

    /**
     * 关联的主表id
     */
    private Long saleOutId;

    /**
     * 三方订单号
     */
    private String businessId;

    /**
     * 货商编号
     */
    private String providerNumber;

    /**
     * 三方商品编号
     */
    private String drugNumber;

    /**
     * 规格
     */
    private String drugSpecification;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 生产厂家
     */
    private String manufacturerName;

    /**
     * 批准文号
     */
    private String approvalNumber;

    /**
     * 产地
     */
    private String producingArea;

    /**
     * 发货数量
     */
    private BigDecimal receiveNumber;

    /**
     * 采购收货单回写的数量
     */
    private BigDecimal confirmNumber;

    /**
     * 是否提取，1--是，0--否
     */
    private Integer pullYn;

    /**
     * 含税价
     */
    private BigDecimal taxPrice;

    /**
     * 含税总价 进货价（优惠后）
     */
    private BigDecimal totalTaxAmount;

    /**
     * 进货价（优惠前）
     */
    private BigDecimal originalAmount;

    /**
     * 批号
     */
    private String batchNumber;

    /**
     * 生产日期
     */
    private String producedDate;

    /**
     * 有效期至
     */
    private String expiredDate;

    /**
     * 三方药店ID
     */
    private String drugStoreId;

    /**
     * 标准库id
     */
    private Long productId;

    /**
     * 第三方商品编号
     */
    private String productNumber;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * saas机构标识
     */
    private String organsign;

    /**
     * 版本号
     */
    private int baseVersion;

    /**
     * 商品内码
     */
    private String productCode;

    /**
     * 条形码
     */
    private String barCode;

    /**
     * 商品外码
     */
    private String productPharmacyPref;

    /**
     * 商品通用名
     */
    private String productCommonName;

    /**
     * 商品图片
     */
    private String productImg;


    //************* 主单字段

    /**
     * 供应商名称
     */
    private String providerName;

    /**
     * 订单创建时间
     */
    private String billingCreateDate;

    /**
     * 订单来源
     */
    private String orderSource;

    /**
     * 订单来源
     */
    private String orderSourceName;

}
