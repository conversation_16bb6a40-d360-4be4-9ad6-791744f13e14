package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class PurchaseReceiveDetailDto implements Serializable {

    private Integer id;

    private String organSign;
    private String billNo;

    private String productCode;

    private String productPharmacyPref;

    private String productName;

    private String commonName;

    private String attributeSpecification;

    private String dosageForm;

    private String manufacturer;

    private String productOriginAddress;

    private BigDecimal orderAmount;

    private String productBatchNo;

    private Date productDate;

    private Date productExpiryDate;

    private BigDecimal productTaxPrice;
    private BigDecimal discount;
    private BigDecimal productDiscountTaxPrice;

    private BigDecimal productDiscountTaxPriceSum;

    private BigDecimal productTaxPriceSum;

    private String packingUnit;

    private BigDecimal arrivalAmount;

    private BigDecimal receivingAmount;

    private String receiveUnit;

    private BigDecimal rejectionAmount;

    private BigDecimal productRetailPrice;

    private BigDecimal productMemberPrice;

    private BigDecimal stockNumber;

    private Byte reviewConclusion;

    private String reviewConclusionCN;
    private String createUser;

    private String productDateVo;

    private String productExpiryDateVo;

    private String baseVersion;

    //产地
    private String productPlace;

    private String approvalNumber;
    /** 上市许可证持有人字段 */
    private String permitOwner;

    /**
     * 收货员
     */
    private String receiveUser;

    private String receiveUserName;

    /**
     * 收货日期
     */
    private Date receiveTime;

    private String receiveTimes;

    /**
     * 采购订单编号
     */
    private String parentBillNo;

    /**
     * 供应商名称
     */
    private String supplierName;

    private String detailSupplierName;

    /**
     * 上市许可证人
     */
    private String drugPermissionPerson;

    //1:常规药品 2：冷链药品 3:中药材 4：中药饮片
    private Integer checkMode;

    /**
     * 承运方式
     */
    private String transportMode;

    /**
     * 启运地址
     */
    private String shipment;

    /**
     * 启运时间
     */
    private String departureTime;

    /**
     * 存储条件
     */
    private Integer storageCondition;

    private String storageConditionMsg;


    /**
     * 进项税率
     */
    private BigDecimal incomeTaxRate;

    /**
     * 灭菌批号
     */
    private String sterilizationLotNo;

    private Byte billSourceType;
}