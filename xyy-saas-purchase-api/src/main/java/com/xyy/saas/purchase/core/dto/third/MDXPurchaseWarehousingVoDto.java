package com.xyy.saas.purchase.core.dto.third;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class MDXPurchaseWarehousingVoDto implements Serializable {

    /**
     * 唯一业务编码 （saas侧采购订单业务编码）例：ZHL000123456:CGD202010100001
     */
    private String businessNo;

    /**
     * 三方入库业务编码
     */
    private String warehousingBusinessNo;

    /**
     * 供应商编码
     */
    private String supplierNo;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 采购员
     */
    private String purchaseUserName;

    /**
     * 收货员
     */
    private String receiveUserName;

    /**
     * 质检员
     */
    private String qualityUserName;

    /**
     * 操作员
     */
    private String operateUserName;
    /**
     * 预留参数
     */
    private String tripartiteParameter;
    /**
     * 单据备注信息
     */
    private String remark;

    /**
     * 入库日期
     */
    private String storageDate;

    /**
     * 采购明细
     */
    List<MDXPurchaseWarehousingDetailVoDto> productDetails;
}
