package com.xyy.saas.purchase.core.api.requiregoods;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.requiregoods.*;
import com.xyy.saas.purchase.core.dto.returngoods.PurchaseExitReceiptQueryDto;

import java.util.List;
import java.util.Map;

/**
 * 类名称:HeadquarterDeliveryApi
 * 类描述:总部配送单
 * 创建人:sunyue
 * 创建时间:2020/4/5 11:19
 */

public interface HeadquarterDeliveryApi {
    /**
     * 分页查询总部配送单
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<HeadquarterDeliveryOrderInfoDto>> getListByQuery(HeadquarterDeliveryOrderQueryDto queryDto);


    /**
     * 根据编号查询详情
     * @return
     */
    ResultVO<List<HeadquarterDeliveryOrderDetailDto>> selectDetailByBillNo(String organSign,String billNo);


    /**
     * 查询创建过单据的人员列表
     * @param organSign
     * @param billType
     * @return
     */
    ResultVO getCreatedBillUsers(String organSign, String billType);


    /**
     * 查询创建过单据的门店
     * @param headquartersOrganSign
     * @param type
     * @return
     */
    ResultVO selectNeedStoreByHo(String headquartersOrganSign, Integer type);

    /**
     * 根据配送编号查询配送记录
     * @param billNo
     * @return
     */
    ResultVO<HeadquarterTransportRecordDto> selectTransportRecordByBillNo(String organSign, String billNo);

    /**
     * 保存配送记录
     * @param transportRecordDto
     */
    ResultVO saveTransportRecord(HeadquarterTransportRecordDto transportRecordDto);


    /**
     * gsp-运输记录
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<HeadquarterTransportRecordGspDto>> getTransportRecordByQueryGsp(HeadquarterDeliveryOrderQueryDto queryDto);

    /**
     * 打印详情
     * @param organSign
     * @param billNo
     * @param printRowSize
     * @return
     */
    ResultVO<Map<String, Object>> printInfoAndDetails(String organSign, String billNo, List<Integer> printRowSize);

    /**
     * 批量查询调剂出库机构名称
     */
    ResultVO<Map<String,String>> getOutOrganSignByBillNos(String organSign,List<String> billNos);
}
