package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseOrderDetailQueryDto extends BasePurchaseDetailQueryDto implements Serializable {
    /**
     * 采购人
     */
    private String purchaseUser;

    private List<Integer> systemTypes;

    /**
     * 采购版本 默认1.0，采购标准版：2.0
     * @link PurchaseConstants
     */
    private String packageVersion ;

    /**
     * 生产厂家
     */
    private String manufacturer;


}
