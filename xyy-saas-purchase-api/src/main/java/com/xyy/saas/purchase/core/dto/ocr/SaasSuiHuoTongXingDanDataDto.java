package com.xyy.saas.purchase.core.dto.ocr;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 随货同行单-解析、检索结果
 * <AUTHOR>
 * @date 2021/5/21 11:46
 */
@Getter
@Setter
public class SaasSuiHuoTongXingDanDataDto implements Serializable {

    /**
     * 供应商名称
     */
    private String providerName;

    /**
     * 供应商内码
     */
    private String providerPref;

    /**
     * 商品信息
     */
    private List<SaasSuiHuoTongXingDanProductDto> products;

    public SaasSuiHuoTongXingDanDataDto(String providerName, String providerPref, List<SaasSuiHuoTongXingDanProductDto> products) {
        this.providerName = providerName;
        this.providerPref = providerPref;
        this.products = products;
    }

    public SaasSuiHuoTongXingDanDataDto() {
        super();
    }
}
