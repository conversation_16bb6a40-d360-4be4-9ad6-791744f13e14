package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class PurchaseAcceptDetailDto implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 机构编码
     */
    private String organSign;

    /**
     * 验收日期字符串
     */
    private String checkTimeStr;


    /**
     * 验收日期
     */
    private Date checkTime;

    /**
     * 收货日期
     */
    private Date receiveTime;

    /**
     * 验收员
     */
    private String checkUserName;
    private String checkUser;

    /**
     *
     * 验收状态，0-待验收; 1-已验收
     */
    private Boolean acceptState;

    /**
     * 验收状态，用于展示
     */
    private String acceptStateStr;

    /**
     *
     * 单据状态: 01-暂存；02-提交
     */
    private String submitStatus;
    /**
     * 收货日期字符串
     */
    private String receiveTimeStr;

    /**
     * 单据编号
     */
    private String billNo;

    private String parentBillNo;

    /**
     * 零售价
     */
    private BigDecimal productRetailPrice;

    // 上市许可持有人
    private String drugPermissionPerson;
    // 生产许可证号
    private String productLicenseRecordNo;
    // 注册证号
    private String registerCertificateNo;
    /**
     * 会员价
     */
    private BigDecimal productMemberPrice;
    private BigDecimal retailPrice;//商品零售价，给前端返回统一零售价字段参数
    private Long productId;//新增返回商品主键id
    /**
     * 采购订单供应商
     */
    private String supplierNo;
    /**
     * 采购订单供应商名称
     */
    private String supplierName;

    /**
     * 商品外码
     */
    private String productCode;

    /**
     * 商品内码
     */
    private String productPharmacyPref;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品通用名称
     */
    private String commonName;

    /**
     * 商品批号
     */
    private String productBatchNo;

    /**
     * 生产日期
     */
    private Date productDate;

    /**
     * 有效期至
     */
    private Date productExpiryDate;

    /**
     * 生产日期
     */
    private String productDateStr;

    /**
     * 有效期至
     */
    private String productExpiryDateStr;

    /**
     * 规格
     */
    private String specifications;

    /**
     * 单位名称
     */
    private String packingUnit;

    /**
     * 收货单位
     */
    private String receiveUnit;

    /**
     * 剂型名称
     */
    private String dosageFormName;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 批准文号
     */
    private String approvalNumber;

    /**
     * 收货数量
     */
    private BigDecimal receivingAmount;

    /**
     * 到货数量
     */
    private BigDecimal arrivalAmount;

    private String arrivalTimeStr;

    /**
     * 采购数量
     */
    private BigDecimal orderAmount;

    /**
     * 商品含税单价
     */
    private BigDecimal productTaxPrice;

    /**
     * 商品含税总价
     */
    private BigDecimal productTaxPriceSum;

    /**
     * 商品产地
     */
    private String productOriginAddress;

    /**
     * 折扣(百分比)
     */
    private BigDecimal discount;

    /**
     * 商品折后单价
     */
    private BigDecimal productDiscountTaxPrice;

    /**
     * 商品折后总价
     */
    private BigDecimal productDiscountTaxPriceSum;

    /**
     * 商品抽样数量
     */
    private BigDecimal productSampleAmount;

    /**
     * 商品合格数量
     */
    private BigDecimal productQualifiedAmount;

    /**
     * 商品不合格数量
     */
    private BigDecimal productUnqualifiedAmount;

    /**
     * 商品不合格事项
     */
    private String productUnqualifiedRemark;

    /**
     * 处理措施
     */
    private String productTreatment;

    /**
     * 验收结论: 1-合格 0-锁定
     */
    private Boolean acceptConclusion;

    /**
     * 验收结论描述
     */
    private String acceptConclusionText;

    private String createUser;
    private Date createTime;
    private String updateUser;
    private Date updateTime;
    private Boolean yn;
    private Integer baseVersion;

    /**
     * 商品数量
     */
    private BigDecimal productAmount;

    /**
     * 产地
     */
    private String productPlace;

    /**
     * 上市许可证持有人字段
     */
    private String permitOwner;

    /**
     * 进项税率
     */
    private BigDecimal incomeTaxRate;

    /**
     * 灭菌批号
     */
    private String sterilizationLotNo;

    private String detailSupplierName;

    private Byte billSourceType;

}