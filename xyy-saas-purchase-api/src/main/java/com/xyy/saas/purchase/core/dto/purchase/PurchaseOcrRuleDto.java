package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 *
 * <AUTHOR>
 * @date   2022/09/20
 */
@Getter
@Setter
public class PurchaseOcrRuleDto implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 每天识别次数上限
     */
    private Integer maxDayNum;

    /**
     * 每月识别次数上限
     */
    private Integer maxMonthNum;

    /**
     * 累计识别次数上限
     */
    private Integer maxTotalNum;

    /**
     * 创建时间,缺省值CURRENT_TIMESTAMP
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 逻辑删除 1 开启
     */
    private Boolean isOpen;

}