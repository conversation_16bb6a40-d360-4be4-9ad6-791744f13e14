package com.xyy.saas.purchase.core.api;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.SaasPurchaseBillInfoDto;
import com.xyy.saas.purchase.core.dto.SaasPurchaseBillInfoPoDto;

import java.util.List;
import java.util.Map;

/**
 * 采购订单API
 * Created by zhangjinxia on 2020/2/17.
 */
public interface PurchaseBillOrderInfoApi {

    /**
     * 查询采购订单列表
     * @param saasPurchaseBillInfoPoDto
     * @return
     */
    PageInfo<SaasPurchaseBillInfoDto> getBillInfoList(PageInfo pageInfo, SaasPurchaseBillInfoPoDto saasPurchaseBillInfoPoDto);

    /**
     * 查询详情信息，主要是根据type和billNo来查询详情
     * @param purchaseBillInfoPoDto
     * @return
     */
    ResultVO getDetailPurchaseBillInfo(SaasPurchaseBillInfoPoDto purchaseBillInfoPoDto);

    /**
     * 采购订单详情打印
     * @param purchaseBillDto
     * @param rowSize
     * @param identity
     * @return
     */
    Map<String, Object> purchaseOrderBillPrinting(SaasPurchaseBillInfoPoDto purchaseBillDto, List<Integer> rowSize, String identity);
}
