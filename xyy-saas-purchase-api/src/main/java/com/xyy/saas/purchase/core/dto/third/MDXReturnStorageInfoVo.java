package com.xyy.saas.purchase.core.dto.third;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/25 15:29:59
 * 曼迪新退货入库回执结果类
 */
@Setter
@Getter
public class MDXReturnStorageInfoVo  implements Serializable {
    /**
     * 商品明细
     */
    private List<MDXReturnStorageDetailVo> productDetails;

    /**
     * 智慧脸提交退货单给曼迪新的唯一业务编码 "ZHL00012987:THDZ2010280166"
     */
    private String businessNo;
    /**
     * 曼迪新退货入库单号
     */
    private String returnWarehousingBusinessNo;
    /**
     * 智慧脸提交退货给曼迪新的的采购订单唯一业务编码  ZHL00012987:CGD202010100001
     */
    private String purchaseOrderBusinessNo;
    /**
     * 退货日期
     */
    private String returnWarehousingDate;
    /**
     * 退货渠道 A库/B库 （值: A或B）
     */
    private String returnType;

}
