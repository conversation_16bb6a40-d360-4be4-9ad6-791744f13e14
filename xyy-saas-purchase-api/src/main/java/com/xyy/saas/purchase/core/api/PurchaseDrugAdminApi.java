package com.xyy.saas.purchase.core.api;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.PurchaseDrugAdminQueryDto;

/**
 * 类名称:PurchaseDrugAdminApi
 * 类描述:河北药监Api
 * 创建人:sunyue
 * 创建时间:2020/5/15 14:34
 */

public interface PurchaseDrugAdminApi {


    /**
     * 获取河北药监数据 xml
     * @param dto
     * @return
     */
    ResultVO getPurchaseDrugAdmin(PurchaseDrugAdminQueryDto dto);

}
