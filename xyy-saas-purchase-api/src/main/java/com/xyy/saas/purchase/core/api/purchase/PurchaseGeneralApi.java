package com.xyy.saas.purchase.core.api.purchase;

import com.xyy.saas.common.dto.SystemDictDto;
import com.xyy.saas.common.util.ResultVO;

import java.util.List;
import java.util.Map;

/**
 * @Author: <PERSON>
 * @CreateTime: 2020-04-09 10:44
 * @Description: 采购通用api
 */
public interface PurchaseGeneralApi {

    /**
     * 根据单据类型查询相关创建人列表
     * @param organSign
     * @param billType
     * @return
     */
    ResultVO<List<Map<String, Object>>> selectCreateBillingUser(String organSign, String billType);

    /**
     * 根据业务id查询字典信息
     * @param businessId
     * @param organSign
     * @param yn
     * @param status
     * @return
     */
    ResultVO<List<SystemDictDto>> querySystemDictList(Integer businessId, String organSign, Byte yn, Byte status);

    /**
     * 根据业务id集合查询字典信息
     * @param businessIds
     * @param organSign
     * @param yn
     * @param status
     * @return
     */
    ResultVO<List<SystemDictDto>> querySystemDictListBatch(List<Integer> businessIds, String organSign, Byte yn, Byte status);

    /**
     * 根据业务id集合查询字典信息
     * @param businessIds
     * @param organSign
     * @param yn
     * @param status
     * @return
     */
    ResultVO<Map<Integer, Map<Integer, String>>> querySysDictMap(List<Integer> businessIds, String organSign, Byte yn, Byte status);

    /**
     * 根据总部机构号获取委托配送配置项
     * @param organSign
     * @return
     */
    ResultVO getSaasConsignPropertiesByOrgSign(String organSign);
}
