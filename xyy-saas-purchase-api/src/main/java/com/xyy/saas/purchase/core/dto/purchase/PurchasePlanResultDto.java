package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Data
public class PurchasePlanResultDto implements Serializable {
        
    /** */
    private Long id;

    /** 单据编号*/
    private String billNo;


    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 计划时间
     */
    private String planDate;

    /**
     * 审批状态
     */
    private String approveStatusType;
    

    /**
     * 审批状态
     */
    private Byte approveStatus;

    /**
     * 供应商id
     */
    private String supplierNo;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 商品种类
     */
    private int productTypeNumbers;

    /**
     * "总部" 当前待办部门
     */
    private String approveOrgansignType;

    /**
     * 当前待办人
     */
    private String approveRoleType;

    /**
     * 当前代办人id
     */
    private String approveUserId;

    /**
     * 当前代办人名称
     */
    private String approveUserName;

    /**
     * 审批流程使用key
     */
    private String businessKey;
    /**
     * 审批流程使用
     */
    private Integer businessScene;
  

    List<Map<String, Object>> details;
    
    public String getPlanDate() {
        if (createTime == null) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(createTime);
    }

}