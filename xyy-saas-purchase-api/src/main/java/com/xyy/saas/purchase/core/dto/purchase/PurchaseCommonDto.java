package com.xyy.saas.purchase.core.dto.purchase;

import com.xyy.saas.purchase.contract.dto.basic.PurchaseBaseInfoDto;
import lombok.Data;

import java.util.List;

@Data
public class PurchaseCommonDto extends PurchaseBaseInfoDto {

    private Integer page;

    private Integer rows;

    /**
     * 主键
     */
    private Long id;

    /**
     * 导出选中的id
     */
    private List<Long> ids;

    /**
     * 机构号
     */
    private String organSign;

    /**
     * 当前操作人id
     */
    private Integer employeeId;

    /**
     * 上级机构号
     */
    private String parentBillNo;

    /**
     * 订单号
     */
    private String billNo;

    /**
     *
     * 供应商编号
     */
    private String supplierNo;

    /**
     *
     * 供应商名称
     */
    private String supplierName;

    private List<String> supplierList;

    /**
     * 行数据版本号
     */
    private Integer version;
}
