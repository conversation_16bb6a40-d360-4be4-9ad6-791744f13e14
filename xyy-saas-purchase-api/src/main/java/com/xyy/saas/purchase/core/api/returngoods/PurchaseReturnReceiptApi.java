package com.xyy.saas.purchase.core.api.returngoods;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.BillSummaryDto;
import com.xyy.saas.purchase.core.dto.returngoods.PurchaseExitReceiptDetailDto;
import com.xyy.saas.purchase.core.dto.returngoods.PurchaseExitReceiptInfoDto;
import com.xyy.saas.purchase.core.dto.returngoods.PurchaseExitReceiptQueryDto;

import java.util.List;

/**
 * 采购退货回执单（外采供应商退货回执），和门店退货回执共用同一表，API接口层面解耦
 */
public interface PurchaseReturnReceiptApi {

    /**
     * 分页查询回执单列表信息
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<PurchaseExitReceiptInfoDto>> getReturnReceiptListByPage(PurchaseExitReceiptQueryDto queryDto);

    /**
     * 查看回执单详情
     * @param queryDto
     * @return
     */
    ResultVO<PurchaseExitReceiptInfoDto> getInfoAndDetailsByBillNo(PurchaseExitReceiptQueryDto queryDto);

    /**
     * 查询开票员列表
     * @param organSign
     * @param returnMode
     * @return
     */
    ResultVO getBillUsers(String organSign,Byte returnMode);

    /**
     * 保存回执单
     * @param exitReceiptInfoDto
     * @return
     */
    ResultVO saveExitReceipt(PurchaseExitReceiptInfoDto exitReceiptInfoDto);

    /**
     * 按条件查询-不分页
     * @param queryDto
     * @return
     */
    ResultVO<List<PurchaseExitReceiptInfoDto>> getByConditionNoPage(PurchaseExitReceiptQueryDto queryDto);

    /**
     * 查询回执单详情
     * @param queryDto
     * @return
     */
    ResultVO<List<PurchaseExitReceiptDetailDto>> getDetailsByBillNo(PurchaseExitReceiptQueryDto queryDto);

    void pullJztService(PurchaseExitReceiptQueryDto queryDto);

    /**
     * 分页查询回执单商品明细
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<PurchaseExitReceiptDetailDto>> getReturnReceiptDetailListByPage(PurchaseExitReceiptQueryDto queryDto);

    /**
     * 列表汇总详细查询
     * @param queryDto
     * @return
     */
    ResultVO<BillSummaryDto> selectByConditionSummary(PurchaseExitReceiptQueryDto queryDto);
}
