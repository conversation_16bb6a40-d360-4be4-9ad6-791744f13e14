package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: <PERSON>
 * @CreateTime: 2020-06-28 16:20
 * @Description:
 */
@Getter
@Setter
public class PurchaseSettlementPayInfoDto implements Serializable {
    /**
     * 支付方式名
     */
    private String paymentMethod;
    /**
     * 支付金额
     */
    private BigDecimal payAmount;
}
