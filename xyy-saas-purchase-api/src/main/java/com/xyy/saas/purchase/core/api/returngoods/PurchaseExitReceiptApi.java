package com.xyy.saas.purchase.core.api.returngoods;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.returngoods.PurchaseExitReceiptDetailDto;
import com.xyy.saas.purchase.core.dto.returngoods.PurchaseExitReceiptInfoDto;
import com.xyy.saas.purchase.core.dto.returngoods.PurchaseExitReceiptQueryDto;
import com.xyy.saas.purchase.core.dto.third.MDXReturnStorageInfoVo;

import java.util.List;
import java.util.Map;

/**
 * 回执单
 * <AUTHOR>
 * @date 2020/7/8 11:34
 */
public interface PurchaseExitReceiptApi {

    /**
     * 分页查询回执单列表信息
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<PurchaseExitReceiptInfoDto>> selectByCondition(PurchaseExitReceiptQueryDto queryDto);

    /**
     * 分页查询回执单商品明细
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<PurchaseExitReceiptDetailDto>> selectDetailsByCondition(PurchaseExitReceiptQueryDto queryDto);

    /**
     * 查看回执单详情
     * @param queryDto
     * @return
     */
    ResultVO<PurchaseExitReceiptInfoDto> selectInfoAndDetailsByBillNo(PurchaseExitReceiptQueryDto queryDto);

    /**
     * 查询开票员列表
     * @param organSign
     * @return
     */
    ResultVO getBillUsers(String organSign);

    /**
     * 保存回执单
     * @param exitReceiptInfoDto
     * @return
     */
    ResultVO saveExitReceipt(PurchaseExitReceiptInfoDto exitReceiptInfoDto);

    /**
     * 按条件查询-不分页
     * @param queryDto
     * @return
     */
    ResultVO<List<PurchaseExitReceiptInfoDto>> selectByConditionNoPage(PurchaseExitReceiptQueryDto queryDto);

    /**
     * 查询回执单详情
     * @param queryDto
     * @return
     */
    ResultVO<List<PurchaseExitReceiptDetailDto>> selectDetailsByBillNo(PurchaseExitReceiptQueryDto queryDto);

    /**
     * 打印详情
     * @param queryDto
     * @param printRowSize
     * @return
     */
    ResultVO<Map<String, Object>> printInfoAndDetails(PurchaseExitReceiptQueryDto queryDto, List<Integer> printRowSize);

    /**
     * 查询退货回执单汇总
     * @param queryDto
     * @return
     */
    ResultVO selectByConditionSummary(PurchaseExitReceiptQueryDto queryDto);

    /**
     * 保存曼迪新退货回执单（A库或B库）
     * @param mdxReturnStorageInfoVo
     * @return
     */
    ResultVO saveMDXExitReceipt(MDXReturnStorageInfoVo mdxReturnStorageInfoVo);
}
