package com.xyy.saas.purchase.core.dto.log;

import lombok.Data;

import java.io.Serializable;

/**
 * @Auther: wangzhipeng12550
 * @Date: 20/12/4 15:43
 * @Description: 文档见 : https://wiki.int.ybm100.com/pages/viewpage.action?pageId=351222904
 */
@Data
public class ActionLogMsg implements Serializable {
    private static final long serialVersionUID = 845179719620360430L;
    //必填，UUID
    private String msgId;
    //必填，消息名称
    private String msgTitle;
    //必填，消息发送系统
    private String systemId;
    //必填，消息生成时间戳
    private Long timestamp;
    //用户ID
    private ActionLogBody body;
}
