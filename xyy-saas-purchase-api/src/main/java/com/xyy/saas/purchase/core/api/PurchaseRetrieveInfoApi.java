package com.xyy.saas.purchase.core.api;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.*;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseBillDetailCommonDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseSettlementExtractBillQueryDto;

import java.util.List;
import java.util.Map;

/**
 * 采购退出模块api接口
 * <AUTHOR>
 * @date 2019/11/12
 */
public interface PurchaseRetrieveInfoApi {

    /**
     * 采购退出出库单列表查询
     * @param purchaseRetrieveBillInfoDto
     * @return
     */
    @Deprecated
    ResultVO getPurchaseRetrList(SaasPurchaseRetrieveBillInfoDto purchaseRetrieveBillInfoDto);

    /**
     * 通过编号查询退出出库信息
     * @param billNo
     * @return
     */
    @Deprecated
    ResultVO<SaasPurchaseRetrieveBillInfoDto> getPurchaseRetrInfo(String billNo, String organSign);

    /**
     * 采购退出出库单记录审核
     * @param billInfoDto
     * @return
     */
    ResultVO retrieveReview(SaasPurchaseRetrieveBillInfoDto billInfoDto, List<SaasPurchaseRetrieveBillDetailDto> list);

    /**
     * 采购退出出库单删除
     */
    ResultVO deletePurchaseRetr(String billNo, String organSign);

    /**
     * 采购退出出库单删除
     */
    ResultVO deleteRetrieve(String billNo, String organSign);

    /**
     * 采购退出出库单编辑（正常情况下，点击编辑数据信息，查询出的数据）
     */
    ResultVO getToAddOrEditPurchaseRetr(SaasPurchaseRetrieveBillInfoDto purchaseRetrieveBillInfoDto);

    /**
     * 采购退出出库审核列表查询
     * @return
     */
    PageInfo<PurchaseBillDto> getPurchaseRetrieveReviewList(PageInfo pageInfo,SaasPurchaseRetrieveBillInfoDto purchaseBillDto);

    /**
     * 查询操作过退出出库审核的人员列表
     * @param organSign
     * @return
     */
    List<Map<String, String>> selectReviewers(String organSign);

    /**
     * 成本价负值校验接口
     * @param organSign
     * @param purchaseRetrieveBillDetailDtos
     * @return
     */
    ResultVO checkCostPriceByRefund(String organSign, List<SaasPurchaseRetrieveBillDetailDto> purchaseRetrieveBillDetailDtos, String billType);

    /**
     * 查询采购退出出库单详情
     * @param billNo
     * @param sortColumn 排序字段 product_date/product_expiry_date/position_no
     * @param sortOrder 排序方式 asc/desc
     * @return
     */
    List<SaasPurchaseRetrieveBillDetailDto> getRetrieveDetail(String billNo, String organSign, String sortColumn, String sortOrder, Byte isProductHidden);

    /**
     * 采购退补价单详情打印
     * @param purchaseBillDto
     * @param rowSize
     * @param identity
     * @return
     */
    Map<String, Object> purchaseRetrievePrinting(SaasPurchaseBillInfoPoDto purchaseBillDto, List<Integer> rowSize, String identity);

    /**
     * 查询采购入库单详情信息 - 适用于采购结算单需求
     * @param queryDto
     * @return
     */
    ResultVO<List<PurchaseBillDetailCommonDto>> getDetailInfo(PurchaseSettlementExtractBillQueryDto queryDto);
}
