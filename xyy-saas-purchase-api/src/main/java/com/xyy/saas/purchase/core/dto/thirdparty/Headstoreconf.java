package com.xyy.saas.purchase.core.dto.thirdparty;

import com.fasterxml.jackson.annotation.JsonProperty;

public class Headstoreconf {

    @JsonProperty("allowChainDestroyReject")
    private String allowchaindestroyreject;
    @JsonProperty("reverseBuildOrderSwitch")
    private String reversebuildorderswitch;
    @JsonProperty("supplierName")
    private String suppliername;
    @JsonProperty("costAccounting")
    private String costaccounting;
    @JsonProperty("supplierInfoPref")
    private String supplierinfopref;
    @JsonProperty("currentChooseDrug")
    private String currentchoosedrug;
    @JsonProperty("ecCode")
    private String eccode;
    @JsonProperty("orderSyncSwitch")
    private String ordersyncswitch;
    @JsonProperty("supplierInfoName")
    private String supplierinfoname;
    @JsonProperty("channelId")
    private String channelid;
    public void setAllowchaindestroyreject(String allowchaindestroyreject) {
        this.allowchaindestroyreject = allowchaindestroyreject;
    }
    public String getAllowchaindestroyreject() {
        return allowchaindestroyreject;
    }

    public void setReversebuildorderswitch(String reversebuildorderswitch) {
        this.reversebuildorderswitch = reversebuildorderswitch;
    }
    public String getReversebuildorderswitch() {
        return reversebuildorderswitch;
    }

    public void setSuppliername(String suppliername) {
        this.suppliername = suppliername;
    }
    public String getSuppliername() {
        return suppliername;
    }

    public void setCostaccounting(String costaccounting) {
        this.costaccounting = costaccounting;
    }
    public String getCostaccounting() {
        return costaccounting;
    }

    public void setSupplierinfopref(String supplierinfopref) {
        this.supplierinfopref = supplierinfopref;
    }
    public String getSupplierinfopref() {
        return supplierinfopref;
    }

    public void setCurrentchoosedrug(String currentchoosedrug) {
        this.currentchoosedrug = currentchoosedrug;
    }
    public String getCurrentchoosedrug() {
        return currentchoosedrug;
    }

    public void setEccode(String eccode) {
        this.eccode = eccode;
    }
    public String getEccode() {
        return eccode;
    }

    public void setOrdersyncswitch(String ordersyncswitch) {
        this.ordersyncswitch = ordersyncswitch;
    }
    public String getOrdersyncswitch() {
        return ordersyncswitch;
    }

    public void setSupplierinfoname(String supplierinfoname) {
        this.supplierinfoname = supplierinfoname;
    }
    public String getSupplierinfoname() {
        return supplierinfoname;
    }

    public void setChannelid(String channelid) {
        this.channelid = channelid;
    }
    public String getChannelid() {
        return channelid;
    }
}
