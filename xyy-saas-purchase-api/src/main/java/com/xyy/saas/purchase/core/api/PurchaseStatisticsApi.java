package com.xyy.saas.purchase.core.api;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.purchase.core.dto.PurchaseBillDetailDto;
import com.xyy.saas.purchase.core.dto.PurchaseDayStatisticsDto;
import com.xyy.saas.purchase.core.dto.PurchaseDayStatisticsVoDto;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**  采购分析
 * Created by zh on 2018/6/20.
 */
public interface PurchaseStatisticsApi {

    /**
     * 进行采购日统计分析
     * @return
     */
    Integer doPurchaseDayStatistics(String organSign, Date startTime, Date endTime);

    /**
     * 列表条件查询
     * @param pageInfo
     * @param purchaseDayStatisticsVoDto
     * @return
     */
    PageInfo<PurchaseDayStatisticsDto> findByList(PageInfo pageInfo, PurchaseDayStatisticsVoDto purchaseDayStatisticsVoDto);

    /**
     * 列表条件查询
     * @param pageSize
     * @param pageNo
     * @param purchaseDayStatisticsVoDto
     * @return
     */
    PageInfo<List<PurchaseDayStatisticsDto>> findByParam(Integer pageSize, Integer pageNo, PurchaseDayStatisticsVoDto purchaseDayStatisticsVoDto);


    /**
     * 带条件分页查询
     * @param purchaseBillDetailDto
     * @return
     */

    PageInfo<PurchaseBillDetailDto> selectByExample(Integer pageSize, Integer pageNo,PurchaseBillDetailDto purchaseBillDetailDto);

    /**
     * 总采购数量查询
     * @param queryParams
     * @return
     */
    List<String> getTaxAmountSumInSevenDays(Map<String,Object> queryParams);

    /**
     * 小药药采购数量
     * @param queryParams
     * @return
     */
    List<String> getXyyTaxAmountSumInSevenDays(Map<String,Object> queryParams);

    /**
     * 查询采购次数（采购入库单数量）
     * @param paramMap
     * @return
     */
    int findPurchasingTimes(Map<String,Object> paramMap);

    /**
     * 批量查询采购次数
     * @param list
     * @return
     */
    List<Map<String,Object>> findPurchasingTimesAll(List<Map<String, Object>> list);

}
