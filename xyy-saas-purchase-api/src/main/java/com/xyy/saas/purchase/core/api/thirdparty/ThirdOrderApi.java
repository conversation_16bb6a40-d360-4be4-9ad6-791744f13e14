package com.xyy.saas.purchase.core.api.thirdparty;

import com.github.pagehelper.PageInfo;
import com.sun.org.apache.xpath.internal.operations.Bool;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.purchase.PurchasePullSaleOutDto;
import com.xyy.saas.purchase.core.dto.requiregoods.PurchaseStoreWarehousingBillInfoDto;
import com.xyy.saas.purchase.core.dto.requiregoods.ThirdPartyPurchaseOrderQueryDto;
import com.xyy.saas.purchase.core.dto.thirdparty.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/10 19:41
 */
public interface ThirdOrderApi {

    /**
     * 手动同步
     * @param id
     */
    ResultVO manualExecutionSync(Long id);

    /**
     * 根据小药药开放平台订单号查询门店入库单
     * @param platformOrderNo
     * @return
     */
    ResultVO<PurchaseStoreWarehousingBillInfoDto> selectStoreWarehousingByPlatformOrder(String platformOrderNo);

    /**
     * 根据id查询B2C单据详情
     * @return
     */
    ResultVO<SaasPurchaseThirdOrderInfoDto> selectOrderDetailsById(Long id);


    ResultVO<PageInfo<SaasPurchaseThirdOrderStatusInfoDto>> selectOrderList(ThirdPartyPurchaseOrderQueryDto queryDto);

    ResultVO<List<ThirdProductMatchDto>> queryMatchingProductList(String ecOrderNo, String organSign);

    ResultVO updateClientOrderCode(Long id, String clientOrderCode);

    List<ThirdOrderSourceDto> getOrderSources();

    ResultVO<Integer> synchronousSaleoutOrder(PurchasePullSaleOutDto dto);

    ResultVO<Integer> synchronousSaleOutOrderControlTime(PurchasePullSaleOutDto dto);

    ResultVO sendProductMatch(List<ThirdProductMatchDto> list,String organSign);

    /**
     * 根据三方订单状态变化保存销售出库单
     * @return
     */
    ResultVO saveSaleOutBillByYbmOrderStatusChange(YbmOrderStatusChangeDto dto);

    /**
     * 刷数据0300
     * @return
     */
    ResultVO setSaleOutBillProductCodeAndProvider(List<String> organSigns);

    /**
     * 修改单据上的saas供应商内码 药帮忙供应商与saas供应商匹配关系
     * @param ybmProviderDto
     */
    ResultVO updateProviderPrefByOrgAndProviderName(PurchaseYbmProviderDto ybmProviderDto);

    /**
     * 忽略
     * @param organSign
     * @param businessId
     * @return
     */
    ResultVO ignoreSaleOutBill(String organSign, Long employeeId, String businessId);

    /**
     * 添加荷叶渠道
     * @param reqDto
     * @return
     */
    ResultVO addOrderSource(HeyeOrderSourceDto reqDto);

    /**
     * 修改荷叶渠道
     * @param reqDto
     * @return
     */
    ResultVO updateOrderSource(HeyeOrderSourceDto reqDto);

    ResultVO initOrderSource();
}
