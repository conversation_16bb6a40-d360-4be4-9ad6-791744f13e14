package com.xyy.saas.purchase.core.dto.print;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 打印表体
 */
@Data
public class HeaderDto implements Serializable {
    private static final long serialVersionUID = -2906026207725088696L;
    /**
     * 单据编号
     */
    private String billNo;

    /**
     * 要货单编号
     */
    private String requireBillNo;

    /**
     * 药店机构
     */
    private String organSign;
    /**
     * 药店机构名称
     */
    private String organSignName;

    /**
     * 要货单位名称
     */
    private String requireOrganSignName;

    /**
     * 单据日期
     */
    private String billTime;
    /**
     * 单据相关人员
     */
    private String billUser;
    /**
     * 业务员
     */
    private String billUserName;

    /**
     * 供应商名称
     */
    private String supplier;
    private String supplierName;
    /**
     * 金额总计及大写
     */
    private BigDecimal totalAmount;
    private String totalAmountCN;

    /**
     * 复核员
     */
    private String reviewUser;
    private String reviewUserName;

    /**
     * 拣货员
     */
    private String pickingUser;
    private String pickingUserName;

    /**
     * 退货类型
     */
    private Byte returnType;
    private String returnTypeCN;

    /**
     * 原因
     */
    private String reason;

    /**
     * 采购状态
     */
    private String purchaseState;
    private String purchaseStateText;

    /**
     * 采购员,收货员,质检员名称
     */
    private String purchaseUserName;
    private String receiveUser;
    private String receiveUserName;
    private String qualityUserName;

    /**
     * 单据状态
     */
    private String billStatus;
    private String billStatusText;
    /**
     * 出货单单据状态
     */
    private String shipmentState;
    private String shipmentStateText;

    /**
     * 联系人电话
     */
    private String telephonel;
    /**
     * 收货地址
     */
    private String receiveAddress;

    /**
     * 模式:1标准(B2B无仓) 2共仓(B2C无仓单，宜块钱)
     */
    private Byte purchaseOrderModel;
    private String purchaseOrderModelText;

    /**
     * 退货出库员名称
     */
    private String returnOutOfStockUserName;
    /**
     * 创建人
     */
    private String createUser;

    private String createUserName;

    /**
     * 验收员
     */
    private String acceptUser;
    private String acceptUserName;

    /**
     * 承运人
     */
    private String carrierUser;

    /**
     * 承运单位
     */
    private String carrierUnit;

    /**
     * 送货地址
     */
    private String deliveryAddress;
}
