package com.xyy.saas.purchase.core.dto.thirdparty;

import com.fasterxml.jackson.annotation.JsonProperty;

public class Requiregoodsconf {

    @JsonProperty("requireGoodsPhone")
    private String requiregoodsphone;
    @JsonProperty("requireGoodsAddress")
    private String requiregoodsaddress;
    @JsonProperty("requireGoodsReceiver")
    private String requiregoodsreceiver;
    @JsonProperty("requireGoodsCity")
    private String requiregoodscity;
    @JsonProperty("requireGoodsArea")
    private String requiregoodsarea;
    @JsonProperty("requireGoodsCreator")
    private String requiregoodscreator;
    @JsonProperty("requireGoodsProvince")
    private String requiregoodsprovince;
    public void setRequiregoodsphone(String requiregoodsphone) {
        this.requiregoodsphone = requiregoodsphone;
    }
    public String getRequiregoodsphone() {
        return requiregoodsphone;
    }

    public void setRequiregoodsaddress(String requiregoodsaddress) {
        this.requiregoodsaddress = requiregoodsaddress;
    }
    public String getRequiregoodsaddress() {
        return requiregoodsaddress;
    }

    public void setRequiregoodsreceiver(String requiregoodsreceiver) {
        this.requiregoodsreceiver = requiregoodsreceiver;
    }
    public String getRequiregoodsreceiver() {
        return requiregoodsreceiver;
    }

    public void setRequiregoodscity(String requiregoodscity) {
        this.requiregoodscity = requiregoodscity;
    }
    public String getRequiregoodscity() {
        return requiregoodscity;
    }

    public void setRequiregoodsarea(String requiregoodsarea) {
        this.requiregoodsarea = requiregoodsarea;
    }
    public String getRequiregoodsarea() {
        return requiregoodsarea;
    }

    public void setRequiregoodscreator(String requiregoodscreator) {
        this.requiregoodscreator = requiregoodscreator;
    }
    public String getRequiregoodscreator() {
        return requiregoodscreator;
    }

    public void setRequiregoodsprovince(String requiregoodsprovince) {
        this.requiregoodsprovince = requiregoodsprovince;
    }
    public String getRequiregoodsprovince() {
        return requiregoodsprovince;
    }

}
