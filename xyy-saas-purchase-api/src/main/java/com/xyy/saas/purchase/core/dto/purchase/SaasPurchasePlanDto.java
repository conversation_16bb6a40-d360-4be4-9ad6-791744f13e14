package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @annotation:采购计划单DTO
 * @create 2018-09-18 17:48
 **/
@Data
public class SaasPurchasePlanDto implements Serializable {

    private static final long serialVersionUID = -1900194102415351346L;

    private Integer id;

    /** 单据编号*/
    private String billNo;

    /** 开票日期*/
    private Date billTime;

    /** 供应商编号*/
    private String supplierNo;

    /** 供应商名称*/
    private String supplierName;

    /** 处理单据人员*/
    private String billingUser;

    /** 状态： 1 新建 2已提交  3暂存*/
    private Byte status;

    /**
     * 审核状态:0-审核中；1-已通过；2-已驳回
     */
    private Byte approveStatus;

    /** 创建人*/
    private String createUser;

    /** 创建时间*/
    private Date createTime;

    /** 修改人*/
    private String updateUser;

    /** 修改时间*/
    private Date updateTime;

    /** 逻辑删除 1 有效 0 删除*/
    private Byte yn;

    /** 是否是连锁 0 单体 1 连锁*/
    private Byte isChain;

    /** 操作版本号*/
    private Integer baseVersion;

    /** 药店唯一标识*/
    private String organsign;

    private Integer page;//页码
    private Integer rows;//每页条数

    /** 开始时间 */
    private String beginTime;

    /** 结束时间 */
    private String endTime;
    /** 商品种类*/
    private Long productTypeNumbers;
    /** 金额总计*/
    private BigDecimal totalAmount;
    /** 备注*/
    private String remark;

    /** 预计收货时间 */
    private Date receivingPlanDate;
    /** 预计收货时间 */
    private String bussinessCreateUser;
    /** 预计收货时间 */
    private Date bussinessCreateTime;
    /** 采购计划单推送状态 */
    private Byte receivingState;

    private String businessKey;
    private String businessScene;

    private String pharmacyPref;

    private String registeredAddress;

    private String sidx;

    private String sord;
    private String statusType;
    private String approveStatusType;

    private String approveOrgansignTypeDesc;

    private String approveRoleType;
    // 采购内容
    List<Map<String, Object>> details;

    private String orderDetails;

    /** 连锁计划开始时间 */
    private String planBeginTime;

    /** 连锁计划结束时间 */
    private String planEndTime;

}

