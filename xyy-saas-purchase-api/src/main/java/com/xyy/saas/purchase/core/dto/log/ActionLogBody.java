package com.xyy.saas.purchase.core.dto.log;

import lombok.Data;

import java.io.Serializable;

/**
 * @Auther: wangzhipeng12550
 * @Date: 20/12/4 15:45
 * @Description:
 */
@Data
public class ActionLogBody implements Serializable {

    private static final long serialVersionUID = -3402984158732711205L;
    private Long userId;  //用户ID

    private String account;     //用户登录帐号

    private String organSign;   //用户所在机构

    private String phone;   //用户手机号

    private String userName;    //用户名称

    private String requestId;   //请求编号标识

    private Integer systemCode;  //系统编码

    private String systemTitle; //系统名称

    private Integer moduleCode;  //模块编码

    private String moduleTitle; //模块名称

    private Integer pageCode;    //页面编码

    private String pageTitle;   //页面名称

    private Integer actionCode;  //操作编码

    private String actionTitle; //操作名称

    private String createTime;  //操作时间

    private String actionData;  //操作数据

    private String userIp;  //用户IP
}
