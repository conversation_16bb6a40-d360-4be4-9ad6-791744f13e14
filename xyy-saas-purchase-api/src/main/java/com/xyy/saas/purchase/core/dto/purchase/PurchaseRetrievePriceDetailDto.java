package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PurchaseRetrievePriceDetailDto implements Serializable{

    private static final long serialVersionUID = -234388320359610793L;
    /**
     * 主键id
     */
    private Integer id;

    /**
     * 采购订单id
     */
    private Long purchaseRetrieveBillId;

    /**
     * 机构号
     */
    private String organSign;

    /**
     * 采购退补价单号
     */
    private String billNo;

    /**
     * 商品编号
     */
    private String productCode;

    /**
     * 商品编号外码
     */
    private String productPharmacyPref;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品通用名称
     */
    private String commonName;

    /**
     * 库存批号
     */
    private String productBatchNo;

    /**
     * 生产时间
     */
    private Date productDate;

    /**
     * 有效期
     */
    private Date productExpiryDate;

    /**
     * 规格
     */
    private String specifications;

    /**
     * 单位
     */
    private String packingUnit;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 库存数
     */
    private BigDecimal stockAmount;

    /**
     * 含税价
     */
    private BigDecimal productTaxPrice;

    /**
     * 新含税价
     */
    private BigDecimal productNewTaxPrice;

    /**
     * 冲价数量
     */
    private BigDecimal chargeAmount;

    /**
     * 冲价金额
     */
    private BigDecimal productChargeTaxPriceSum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建日期
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除状态
     */
    private Integer yn;

    /**
     * 版本号
     */
    private String baseVersion;

    /**
     * 批准文号
     */
    private String approvalNumber;

    /**
     * 产地
     */
    private String productOriginAddress;

    /**
     * 剂型
     */
    private String dosageFormName;

    /**
     * 货位id
     */
    private Integer positionNo;

    /**
     * 货位名称
     */
    private String positionName;

    /**
     * 成本价
     */
    private BigDecimal costPrice;

    /**
     * 进项税率
     */
    private BigDecimal incomeTaxRate;
}