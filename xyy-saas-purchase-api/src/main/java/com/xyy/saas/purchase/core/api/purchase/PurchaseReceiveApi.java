package com.xyy.saas.purchase.core.api.purchase;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.ReturnBillDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseReceiveDetailDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseReceiveInfoDto;

import java.util.List;

/**
 * @Author: <PERSON>
 * @CreateTime: 2020-04-02 20:04
 * @Description: 收货复核单相关DUBBO API
 */
public interface PurchaseReceiveApi {
    /**
     * 分页查询收货复核单列表
     *
     * @param pageInfo
     * @param infoDto
     * @return
     */
    ResultVO<PageInfo<PurchaseReceiveInfoDto>> selectPurchaseReceiveBillList(PageInfo pageInfo, PurchaseReceiveInfoDto infoDto);

    /**
     * 根据organSign billNo获取收货复核单信息
     *
     * @param billNo
     * @param organSign
     * @return
     */
    ResultVO<PurchaseReceiveInfoDto> getPurchaseByBillNo(String billNo, String organSign);

    /**
     * 保存收货复核单
     * @param infoDto
     * @param detailDtos
     */
    ResultVO updatePurchaseReceive(PurchaseReceiveInfoDto infoDto, List<PurchaseReceiveDetailDto> detailDtos);

    /**
     * 查询收货复核单详情信息
     * @param organSign
     * @param billNo
     * @param requestType 请求类型：0 - 收货查询详情 1 - 双击查询详情 用于区分是否实时查询库存
     * @return
     */
    ResultVO<List<PurchaseReceiveDetailDto>> selectPurchaseReceiveDetail(String organSign, String billNo, Integer requestType);



    /**
     * GSP-单据汇总
     * @return
     */
    ResultVO<PageInfo<PurchaseReceiveInfoDto>> selectByConditionGsp(PageInfo pageInfo, PurchaseReceiveInfoDto infoDto);


    /**
     * GSP-商品汇总-分页
     * @return
     */
    ResultVO<PageInfo<PurchaseReceiveDetailDto>> getDetailListGsp(PageInfo pageInfo, PurchaseReceiveInfoDto infoDto);



    /**
     * 分页查询有拒收数量的收货复核单列表
     *
     * @param pageInfo
     * @param infoDto
     * @return
     */
    ResultVO<PageInfo<PurchaseReceiveInfoDto>> selectHaveRejectionAmountBills(PageInfo pageInfo, PurchaseReceiveInfoDto infoDto);


    /**
     * 收货复核单回退
     * @param returnBillDto
     * @return
     */
    ResultVO returnBill(ReturnBillDto returnBillDto) ;


}
