package com.xyy.saas.purchase.core.api.requiregoods;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.exception.BusinessException;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.BillSummaryDto;
import com.xyy.saas.purchase.core.dto.requiregoods.*;

import java.util.List;

/**
 * Created by zhangjinxia on 2020/4/2.
 * 门店收货单
 */
public interface PurchaseStoreReceiveApi {
    /**
     * 分页查询收货单列表
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<PurchaseStoreReceiveBillInfoDto>> selectByCondition(PurchaseStoreReceiveBillQueryDto queryDto);

    /**
     * 查询info列表
     * @param queryDto
     * @return
     */
    ResultVO<List<PurchaseStoreReceiveBillInfoDto>> selectInfoListByCondition(PurchaseStoreReceiveBillQueryDto queryDto);

    /**
     * 查询detail列表
     * @param organSign 机构编码
     * @param billNo 单号
     * @return
     */
    ResultVO<List<PurchaseStoreReceiveBillDetailDto>> selectDetailListByBillNo(String organSign, String billNo);

    /**
     * 根据收货单号 查收货单 info和details
     * @param organSign
     * @param billNo
     * @return
     */
    ResultVO<PurchaseStoreReceiveBillInfoDto> selectInfoAndDetailsByBillNo(String organSign, String billNo);

    /**
     * 总部提交出库单，自动生成门店收货单-待收货状态
     * @param dto
     * @return
     */
    ResultVO saveReceiveBillByHeadQuarters(PurchaseStoreReceiveBillInfoDto dto);

    /**
     * 修改收货单
     * @param dto
     * @returnupdateReceive
     */
    ResultVO updateReceiveBill(PurchaseStoreReceiveBillInfoDto dto);

    //给GSP管理提供的报表接口 start

    /**
     * GSP分页查询收货单列表
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<PurchaseStoreReceiveBillInfoDto>> selectByConditionGsp(PurchaseStoreReceiveBillQueryDto queryDto);

    /**
     * GSP-收货记录-商品汇总-分页
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<PurchaseStoreReceiveBillDetailDto>> selectDetailsByConditionGsp(PurchaseStoreReceiveBillQueryDto queryDto);

    /**
     * GSP-收货记录-商品汇总
     * @param queryDto
     * @return
     */
    ResultVO<List<PurchaseStoreReceiveBillDetailDto>> selectDetailsByConditionGspList(PurchaseStoreReceiveBillQueryDto queryDto);

    //给GSP管理提供的报表接口 end

    ResultVO<BillSummaryDto> selectInfoSummary(PurchaseStoreReceiveBillQueryDto queryDto);

    ResultVO<BillSummaryDto> selectDetailSummary(String organSign, String billNo);

    /**
     * 门店收货单全部拒收
     * @param receiveBillInfoDto
     * @return
     */
    ResultVO receiveBillAllReject(PurchaseStoreReceiveBillInfoDto receiveBillInfoDto) throws BusinessException;
}
