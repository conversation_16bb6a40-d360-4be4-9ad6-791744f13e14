package com.xyy.saas.purchase.core.api.requiregoods;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.requiregoods.PurchaseSupplierForbidProductDeleteDto;
import com.xyy.saas.purchase.core.dto.requiregoods.PurchaseSupplierForbidProductDto;
import com.xyy.saas.purchase.core.dto.requiregoods.PurchaseSupplierForbidProductQueryDto;

import java.util.List;

/**
 * 供应商禁采设置
 */
public interface PurchaseSupplierForbidProductApi {

    /**
     * 查询供应商禁采数据
     * @param model
     * @return
     */
    ResultVO<PageInfo<PurchaseSupplierForbidProductDto>> selectByCondition(PageInfo pageInfo, PurchaseSupplierForbidProductQueryDto model);

    /**
     * 保存供应商禁采数据,只支持单个供应商进行保存
     * @param dtos
     * @return
     */
    ResultVO savePurchaseSupplierForbidProduct(List<PurchaseSupplierForbidProductDto> dtos);

    /**
     * 删除供应商禁采数据
     * @return
     */
    ResultVO deletePurchaseSupplierForbidProduct(PurchaseSupplierForbidProductDeleteDto dto);

    /**
     * 导出数据
     * @param model
     * @return
     */
    ResultVO<List<PurchaseSupplierForbidProductDto>> selectPurchaseSupplierForbidProduct(PurchaseSupplierForbidProductQueryDto model);

    Long getImportTaskId(String organSign);



}
