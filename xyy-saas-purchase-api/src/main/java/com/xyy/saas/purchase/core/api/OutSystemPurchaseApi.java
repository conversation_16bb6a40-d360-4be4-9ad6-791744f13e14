package com.xyy.saas.purchase.core.api;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.PurchaseBillDataDto;
import com.xyy.saas.purchase.core.dto.PurchaseBillDto;

import java.util.List;
import java.util.Set;

/**
 * 采购系统对外提供的dubbo-api类
 * Created by lijing on 2018/10/10.
 */
public interface OutSystemPurchaseApi {

    /**
     * 通过单据编号 bill_no 查询saas_purchase_bill_info 表中的-供应商名称
     */
    String selectPurchaseInStorageSupplier(String billNo,String organSign);

    /**
     * 通过单据编号 bill_no saas_purchase_retrieve_bill_info 表中的-供应商名称
     */
    String selectPurchaseOutStorageSupplier(String billNo,String organSign);

    /**
     * 提供给库存的接口，根据采购入库单查询单据上供应商信息
     * @param organSign
     * @param billNos
     * @return
     */
    ResultVO queryBillsInfoList(String organSign, Set<String> billNos);

    /**
     * 采购退出出库单列表查询
     * @return
     */
    PageInfo<PurchaseBillDto> getExitOutWareHoustList(PageInfo pageInfo,PurchaseBillDto purchaseBillDto);

    /**
     * 采购退出出库单列表查询
     * @return
     */
    List<PurchaseBillDto> getExitOutWareHoustList(PurchaseBillDto purchaseBillDto);

    /**
     * 收货记录和入库验收记录
     */
    PageInfo<PurchaseBillDataDto> selectPurchaseRecodeList(PageInfo pageInfo, PurchaseBillDataDto purchaseBillDataVo);

    /**
     * 收货记录和入库验收记录
     */
    List<PurchaseBillDataDto> selectPurchaseRecodeListExcel(PurchaseBillDataDto purchaseBillDataVo);

    /**
     * 采购订单列表
     * @param pageInfo
     * @param purchaseBillVo
     * @return
     */
    PageInfo<PurchaseBillDto> getPurchaseOrderList(PageInfo pageInfo, PurchaseBillDto purchaseBillVo);//

    /**
     * 采购订单列表无分页
     * @param
     * @param purchaseBillVo
     * @return
     */
    List<PurchaseBillDto> getPurchaseOrderList(PurchaseBillDto purchaseBillVo);//
}
