package com.xyy.saas.purchase.core.dto.ocr;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/5/21 11:47
 */
@Getter
@Setter
public class SaasSuiHuoTongXingDanProductDto implements Serializable {

    /**
     * 下标
     */
    private int index;

    /**
     * 供应商名称
     */
    @JSONField(serialzeFeatures = SerializerFeature.WriteNullStringAsEmpty)
    private String providerName;

    /**
     * 供应商内码
     */
    @JSONField(serialzeFeatures = SerializerFeature.WriteNullStringAsEmpty)
    private String providerPref;

    /**
     * 商品名称
     */
    @JSONField(serialzeFeatures = SerializerFeature.WriteNullStringAsEmpty)
    private String productName;

    /**
     * 商品内码
     */
    @JSONField(serialzeFeatures = SerializerFeature.WriteNullStringAsEmpty)
    private String pharmacyPref;

    /**
     * 商品规格
     */
    @JSONField(serialzeFeatures = SerializerFeature.WriteNullStringAsEmpty)
    private String specifications;

    /**
     * 商品生产厂家
     */
    @JSONField(serialzeFeatures = SerializerFeature.WriteNullStringAsEmpty)
    private String manufacturer;

    /**
     * 商品批准文号
     */
    @JSONField(serialzeFeatures = SerializerFeature.WriteNullStringAsEmpty)
    private String approvalNumber;

    /**
     * 商品批号
     */
    @JSONField(serialzeFeatures = SerializerFeature.WriteNullStringAsEmpty)
    private String productBatchNo;

    /**
     * 商品生产日期
     */
    @JSONField(serialzeFeatures = SerializerFeature.WriteNullStringAsEmpty)
    private String productDate;

    /**
     * 商品有效期至
     */
    @JSONField(serialzeFeatures = SerializerFeature.WriteNullStringAsEmpty)
    private String productExpiryDate;

    /**
     * 商品数量
     */
    @JSONField(serialzeFeatures = SerializerFeature.WriteNullStringAsEmpty)
    private String productAmount;

    /**
     * 商品含税价
     */
    @JSONField(serialzeFeatures = SerializerFeature.WriteNullStringAsEmpty)
    private String productTaxPrice;

    //-----

    /**
     * 商品通用名称
     */
    @JSONField(serialzeFeatures = SerializerFeature.WriteNullStringAsEmpty)
    private String productCommonName;

    /**
     * 商品内码
     */
    @JSONField(serialzeFeatures = SerializerFeature.WriteNullStringAsEmpty)
    private String pref;

    /**
     * 商品条码
     */
    @JSONField(serialzeFeatures = SerializerFeature.WriteNullStringAsEmpty)
    private String barCode;

    public SaasSuiHuoTongXingDanProductDto(int index, String providerName, String providerPref, String productName, String pharmacyPref, String specifications, String manufacturer, String approvalNumber, String productBatchNo, String productDate, String productExpiryDate, String productAmount, String productTaxPrice, String productCommonName, String pref, String barCode) {
        this.index = index;
        this.providerName = providerName;
        this.providerPref = providerPref;
        this.productName = productName;
        this.pharmacyPref = pharmacyPref;
        this.specifications = specifications;
        this.manufacturer = manufacturer;
        this.approvalNumber = approvalNumber;
        this.productBatchNo = productBatchNo;
        this.productDate = productDate;
        this.productExpiryDate = productExpiryDate;
        this.productAmount = productAmount;
        this.productTaxPrice = productTaxPrice;
        this.productCommonName = productCommonName;
        this.pref = pref;
        this.barCode = barCode;
    }
}
