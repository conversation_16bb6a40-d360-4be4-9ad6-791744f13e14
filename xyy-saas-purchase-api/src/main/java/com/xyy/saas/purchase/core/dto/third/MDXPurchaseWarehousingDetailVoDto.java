package com.xyy.saas.purchase.core.dto.third;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
public class MDXPurchaseWarehousingDetailVoDto implements Serializable {

    /**
     * 商品内码
     */
    private String productCode;

    /**
     * 标准库id
     */
    private String standardProductId;

    /**
     * 入库数量
     */
    private BigDecimal warehousingAmount;

    /**
     * 商品含税单价
     */
    private BigDecimal productTaxPrice;

    /**
     * 商品含税总价
     */
    private BigDecimal productTaxPriceSum;

    /**
     * 商品批号
     */
    private String productBatchNo;

    /**
     * 商品生产日期
     */
    private String productDate;

    /**
     * 商品有效期
     */
    private String expiryDate;

    /**
     * 合格数量
     */
    private BigDecimal productQualifiedAmount;

    /**
     * 不合格数量
     */
    private BigDecimal productUnqualifiedAmount;

    /**
     * 拒收数量
     */
    private BigDecimal oldRejectAmount;

    /**
     * 采购数量
     */
    private BigDecimal orderAmount;

    /**
     * 处理措施
     */
    private String  productTreatment;

    /**
     * saas侧预留参数
     */
    private String tripartiteParameter;
}
