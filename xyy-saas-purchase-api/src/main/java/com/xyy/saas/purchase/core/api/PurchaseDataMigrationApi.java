package com.xyy.saas.purchase.core.api;


import com.xyy.saas.purchase.core.dto.*;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * 数据迁移保存接口
 */
public interface PurchaseDataMigrationApi {

    Integer saveInitPurchaseBill(List<InitPurchaseBillDto> dto);

    Integer saveSaasPurchaseBillInfo(List<SaasPurchaseBillInfoPoDto> dto);

    Map<String,String> saveSaasPurchaseBillInfoEx(List<SaasPurchaseBillInfoPoDto> dto) throws SQLException;

    Integer saveSaasPurchaseBillDetail(List<SaasPurchaseBillDetailPoDto> dto);

    Integer saveSaasPurchasePlan(List<SaasPurchasePlanDto> dto);

    Map<String, String> saveSaasPurchasePlanEx(List<SaasPurchasePlanDto> dtoList) throws SQLException;

    Integer saveSaasPurchasePlanDetail(List<SaasPurchasePlanDetailExDto> dto);

    Map<String,String> saveSaasPurchaseRetrieveBillInfo(List<SaasPurchaseRetrieveBillInfoDto> dto) throws SQLException;

    Integer saveSaasPurchaseRetrieveBillDetail(List<SaasPurchaseRetrieveBillDetailDto> dto);

    Integer saveSaasSaleOut(List<SaleOutPoDto> dto);

    Integer saveSaasSaleOutDetail(List<SaleOutDetailPoDto> dto);

}
