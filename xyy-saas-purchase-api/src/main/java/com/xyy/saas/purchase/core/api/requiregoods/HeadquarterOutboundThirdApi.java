package com.xyy.saas.purchase.core.api.requiregoods;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.third.MDXPurchaseOutBoundVoDto;

/**
 * 类名称:HeadquarterOutboundThirdApi
 * 类描述:三方总部出库单
 * 创建人:chenmanhui
 * 创建时间:2020/10/16 16:56
 */

public interface HeadquarterOutboundThirdApi {
    /**
     * 三方回传出库单回执生成总部委托配送出库单(B库出货单) 默认走B库
     * @param outBoundVo
     * @return
     */
    ResultVO saveHeadquarterOutbound(MDXPurchaseOutBoundVoDto outBoundVo);
    /**
     * 三方回传出库单回执生成总部委托配送出库单(A库采购订单)
     * @param outBoundVo
     * @return
     */
    ResultVO saveHeadquarterOutboundForPurchaseOrder(MDXPurchaseOutBoundVoDto outBoundVo);
}
