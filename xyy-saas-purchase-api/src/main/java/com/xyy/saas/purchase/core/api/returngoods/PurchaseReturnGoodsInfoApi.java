package com.xyy.saas.purchase.core.api.returngoods;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseWarehousingDetailDto;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseWarehousingQueryDto;
import com.xyy.saas.purchase.core.dto.returngoods.PurchaseReturnGoodsDetailDto;
import com.xyy.saas.purchase.core.dto.returngoods.PurchaseReturnGoodsInfoDto;
import com.xyy.saas.purchase.core.dto.returngoods.PurchaseReturnGoodQueryDto;
import com.xyy.saas.purchase.core.dto.returngoods.PurchaseStockInfoDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/10 15:18
 * @Description 采购退货单（外采供应商退货） API接口
 */
public interface PurchaseReturnGoodsInfoApi {


    /**
     * 分页查询采购退货单列表信息
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<PurchaseReturnGoodsInfoDto>> listPage(PageInfo pageInfo, PurchaseReturnGoodQueryDto queryDto);


    /**
     * 查询采购退货单详情
     * @return
     */
    ResultVO<PurchaseReturnGoodsInfoDto> detail(PurchaseReturnGoodQueryDto queryDto);

    /**
     *  删除采购退货单
     * @param organSign
     * @param billNo
     * @return
     */
    ResultVO delete(String organSign, String billNo);

    /**
     * 根据订单编号分页查询入库单商品详情,用于采购退货-提取入库单
     * @param dto {"organSign":"ZHL00003424","billNo":"CGRKZ2009100213","ids":[3403,3404]}
     * @return
     */
    ResultVO<PageInfo<PurchaseReturnGoodsDetailDto>> getDetailListByBillNoForReturn(PurchaseWarehousingQueryDto dto);

    /**
     * 暂存
     * @param infoDto
     * @return
     */
    ResultVO<Integer> pausePurchaseReturnGoods(PurchaseReturnGoodsInfoDto infoDto);

    /**
     * 出货/编辑
     * @param infoDto
     * @return
     */
    ResultVO<Integer> submitPurchaseReturnGoods(PurchaseReturnGoodsInfoDto infoDto) ;

    /**
     * 根据商品编码productpref和批号lotNumber获取(合格品和不合格 等库位)库存信息
     * @param infoDto
     * @return
     */
    ResultVO<List<PurchaseStockInfoDto>> getAreaPositionStockInfo(PurchaseStockInfoDto infoDto) ;

    /**
     * 校验库位库存数量是否符合总部退货数量
     * @param infoDto
     * @return
     */
    ResultVO<Integer> checkInventroyStockNumber(PurchaseReturnGoodsInfoDto infoDto) ;
}
