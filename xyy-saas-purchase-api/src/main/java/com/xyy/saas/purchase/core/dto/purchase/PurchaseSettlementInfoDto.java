package com.xyy.saas.purchase.core.dto.purchase;

import com.xyy.saas.common.util.DateUtil;
import com.xyy.saas.purchase.core.common.PurchaseBillConstants;
import com.xyy.saas.purchase.core.common.PurchaseSettlementStatusEnum;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * saas_purchase_settlement_info
 * <AUTHOR>
@Setter
@Getter
public class PurchaseSettlementInfoDto extends PurchaseBaseDto implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 药店唯一标识
     */
    private String organSign;

    /**
     * 采购结算单单据编号
     */
    private String billNo;
    /**
     * 上级单据编号，要货单编号
     */
    private String parentBillNo;

    /**
     * 开票日期
     */
    private Date billTime;

    /**
     * 开票日期
     */
    private String billTimeStr;

    /**
     * 开票开始日期 - 接收前端传参
     */
    private String beginBillTime;

    /**
     * 开票结束日期 - 接收后端传参
     */
    private String endBillTime;

    /**
     * 结算类型 1-供应商结算 2-单据结算
     */
    private Integer statementType;

    /**
     * 结算类型 1-供应商结算 2-单据结算
     */
    private String statementTypeCN;

    /**
     * 结算金额(实付金额)
     */
    private BigDecimal statementAmount;

    /**
     * 应付金额
     */
    private BigDecimal totalAmount;

    /**
     * 供应商编号 （外码）
     */
    private String supplierPharmacyPref;

    /**
     * 供应商编号 （内码）
     */
    private String suppierNo;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商编号
     */
    private List<String> supplierList;

    /**
     * 供应商联系人
     */
    private String supplierContacts;

    /**
     * 供应商联系地址
     */
    private String supplierAddress;

    /**
     * 供应商开户行
     */
    private String depositBank;

    /**
     * 供应商银行账户
     */
    private String bankAccount;

    /**
     * 结算员id
     */
    private String billingUser;

    /**
     * 结算员姓名
     */
    private String billingUserName;

    /**
     * 支付信息：支付宝 微信 银联 现金
     */
    private List<PurchaseSettlementPayInfoDto> payInfo;

    /**
     * 供应商支付备注
     */
    private String payRemarkds;

    /**
     * 单据备注
     */
    private String remarks;

    /**
     * 单据状态: 1-暂存；2-正式
     */
    private Integer submitStatus;

    /**
     * 单据状态: 1-暂存；2-正式
     */
    private String submitStatusCN;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String updateUser;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 操作版本号
     */
    private String baseVersion;

    /**
     * 版本号，用于乐观锁
     */
    private Integer version;

    private static final long serialVersionUID = 1L;


}