package com.xyy.saas.purchase.core.api.purchase;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.ReturnBillDto;
import com.xyy.saas.purchase.core.dto.purchase.*;
import com.xyy.saas.purchase.core.dto.requiregoods.PurchaseProductInfoDto;
import com.xyy.saas.purchase.core.dto.requiregoods.PurchaseProductInfoQueryDto;

import java.util.List;
import java.util.Map;

/**
 * 采购订单API
 */
public interface PurchaseOrderApi {

    /**
     * 查询采购订单列表
     * @param purchaseOrderInfoDto
     * @return
     */
    ResultVO<PageInfo<PurchaseOrderInfoDto>> getPurchaseOrderInfoList(PageInfo pageInfo, PurchaseOrderInfoDto purchaseOrderInfoDto);

    /**
     * 查询详情信息，主要是根据type和billNo来查询详情
     * @param purchaseOrderInfoDto
     * @return
     */
    ResultVO getPurchaseBillInfo(PurchaseOrderInfoDto purchaseOrderInfoDto);

    /**
     * 查询详情信息，主要是根据type和billNo来查询详情
     * @param purchaseOrderInfoDto
     * @return
     */
    ResultVO getDetailList(PurchaseOrderInfoDto purchaseOrderInfoDto);

    /**
     * 查询详情信息，主要是根据type和billNo来查询详情
     * @param purchaseOrderInfoDto
     * @return
     */
    ResultVO getBillingUsers(PurchaseOrderInfoDto purchaseOrderInfoDto);

    /**
     * 保存采购订单信息
     * @param info
     * @param list
     * @return
     */
    ResultVO purchaseAndSubmit(PurchaseOrderInfoDto info, List<PurchaseOrderDetailDto> list);

    /**
     * 采购订单单据汇总
     * @param dto
     * @return
     */
    ResultVO<PageInfo<PurchaseOrderInfoDto>> queryPurchaseInfoOrderPage(PurchaseOrderInfoDto dto);

    /**
     * 采购单商品汇总
     * @param queryDto
     * @return
     */
    ResultVO<PageInfo<PurchaseOrderDetailDto>> queryChainPurchaseOrderDetailPage(PurchaseOrderDetailQueryDto queryDto);


    /**
     * 根据单号查采购计划详情
     * @param billNo
     * @return
     */
    ResultVO<PurchaseOrderInfoDto> getPurchaseOrderInfoDetailByBillNo(String organSign, String billNo);

    /**
     * 采购订单复用功能
     * @param organSign
     * @param billNo
     * @return
     */
    ResultVO purchMultiplex(String organSign, String billNo, String createUser);

    /**
     * 采购订单打印
     * @param orderInfoDto
     * @param printingRowSize
     * @return
     */
    Map<String, Object> purchaseOrderPrint(PurchaseOrderInfoDto orderInfoDto, List<Integer> printingRowSize);

    /**
     * 采购订单列表页面查询采购数量, 金额总计
     *
     * @return
     */
    ResultVO<PurchaseCommonAmountDto> getPurchaseOrderAmount(PurchaseOrderInfoDto orderInfoDto);


    /**
     * 删除采购订单信息
     * @param organSign
     * @param billNo
     * @return
     */
    ResultVO deletaPurchaseByBillNo(String organSign, String billNo);

    /**
     * 校验供应商(供应商经营范围拆分管控 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=379545794)
     * @param dtoList
     * @return
     */
    ResultVO validateProductProvider(String organSign, List<ValidateProductProviderDto> dtoList);

    /**
     * 保存或编辑采购订单 - new
     * @param dto
     * @return
     */
    ResultVO savePurchaseOrder(PurchaseOrderInfoDto dto);

    /**
     * 请货采购单明细查询 区分根据主表编号查询
     * @param pageInfo
     * @param paramDto
     * @return
     */
    ResultVO findPurchaseDetailByParam(PageInfo pageInfo,PurchaseOrderInfoDto paramDto);


    /**
     * 由采购计划单生成采购订单的补偿接口
     * @param planNo
     * @return
     */
    ResultVO savePurchasePlanCompensate(String planNo, String organSign);


    /**
     * 采购订单回退
     * @param returnBillDto
     * @return
     */
    ResultVO returnBill(ReturnBillDto returnBillDto) ;


    /**
     * 创建采购单-新增-快速导入商品
     * @param dto
     * @return
     */
    ResultVO<PageInfo<PurchaseProductInfoDto>> getImportProducts(PurchaseProductInfoQueryDto dto);



    /**
     * 查询无仓-采购订单列表
     * @param purchaseOrderInfoDto
     * @return
     */
    ResultVO<PageInfo<PurchaseOrderInfoDto>> getPurchaseOrderInfoByQuery(PageInfo pageInfo, PurchaseOrderInfoDto purchaseOrderInfoDto);


    /**
     * 无仓采购订单列表页面查询采购数量, 金额总计
     *
     * @return
     */
    ResultVO<PurchaseCommonAmountDto> getConsignPurchaseOrderAmount(PurchaseOrderInfoDto orderInfoDto);

    /**
     * 通过机构id和采购单号查询采购订单信息
     * @param organSign
     * @param billNo
     * @return
     */
    ResultVO<PurchaseOrderInfoDto> getPurchaseOrderInfoByBillNo(String organSign, String billNo);


    ResultVO updatePurchaseState(String organSign, String billNo, Integer purchaseState, Integer prePurchaseState);
}
