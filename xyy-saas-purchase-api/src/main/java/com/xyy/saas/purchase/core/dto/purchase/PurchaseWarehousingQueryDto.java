package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * ClassName: PurchaseWarehousingQueryDto
 * Function:  采购入库单查询
 * Date:      2020/4/7 15:29
 * author     sunyue
 */
@Data
public class PurchaseWarehousingQueryDto implements Serializable {
    private static final long serialVersionUID = -7250706644715107352L;

    /**
     * 验收单编号
     */
    private String parentBillNo;

    /**
     * 入库单编号
     */
    private String billNo;

    /**
     * 订单编号
     */
    private String orderBillNo;


    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商编号
     */
    private String supplierNo;

    /**
     * 入库员
     */
    private String warehouseUser;


    /**
     * 入库状态 入库状态，0-未入库 1-已入库 2-暂存
     */
    private Byte warehouseState;

    /**
     * 制单开始时间
     */
    private String billTimeStart;

    /**
     * 制单结束时间
     */
    private String billTimeEnd;
    /**
     * 第几页
     */
    private int pageNum = 1;

    /**
     * 每页多少条
     */
    private int pageSize = 50;

    /**
     * 机构编码
     */
    private String organSign;

    /**
     * gsp-商品汇总-商品信息
     */
    private String productQuery;


    private Set<Long> ids;


    private List<String> supplierNoList;

    /**
     * 随货同行单
     */
    private String withCargoNo;

    /**
     * 采购员
     */
    private String purchaseUserName;

    /**
     * '采购模式 1：有仓-采购 2：无仓要货采购（以销定采）3：无仓采购（以采定销-仓库托管）
     */
    private Integer purchaseMode;

    /**
     * 商品信息
     */
    private String productInfo;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 入库单类型:1-采购入库; 2-门店退货入库
     */
    private Byte warehousingType = 1;

    /**
     * 第三方出库编号
     */
    private String thirdPartyWarehouseBillNo;
}
