package com.xyy.saas.purchase.core.dto.ocr;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * ocr识别后解析成java对象
 * <AUTHOR>
 * @date 2021/5/21 11:41
 */
@Getter
@Setter
public class OcrAnalysisResultDto<T> implements Serializable {

    /**
     * 上传图片的地址
     */
    private String img;

    /**
     * ocr平台模板id
     */
    private String templateSign;

//    /**
//     * ocr平台模板名称
//     */
//    private String templateName;

    /**
     * 比如 随货同行单数据
     */
    private T data;
}
