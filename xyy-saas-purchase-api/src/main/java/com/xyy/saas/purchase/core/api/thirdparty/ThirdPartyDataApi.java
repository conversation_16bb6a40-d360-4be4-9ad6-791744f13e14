package com.xyy.saas.purchase.core.api.thirdparty;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.SaasPurchaseBillDetailPoDto;
import com.xyy.saas.purchase.core.dto.SaleOutPoDto;

import java.util.List;
import java.util.Map;

public interface ThirdPartyDataApi {

    /**
     * 给第三方提供的生成订单的接口
     */
    ResultVO createOrderByThirdParty(String jsonStr);

    ResultVO thirdPartyDetailInfoJson(String organSign, String businessId, String providerNum);

    ResultVO<List<SaasPurchaseBillDetailPoDto>> thirdPartyDetailInfoJsonNew(String organSign, String businessId, String providerNum);
    /**
     * 提取商城订单
     * @return
     */
    PageInfo querySaleOutOrderListWithType(PageInfo<Map<String, Object>> pageInfo, SaleOutPoDto po);
}
