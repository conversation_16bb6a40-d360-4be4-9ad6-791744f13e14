package com.xyy.saas.purchase.core.dto.print;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 打印表体
 */
@Data
public class BodyDto implements Serializable {
    private static final long serialVersionUID = 3415288465851676801L;
    private Long id;
    /**
     * 商品编号
     */
    private String productCode;
    /**
     * 通用名称
     */
    private String commonName;

    /**
     * 商品编号 + 通用名称
     */
    private String productCodeAndName;

    /**
     *  规格
     */
    private String specifications;
    /**
     *  剂型
     */
    private String dosageForm;
    /**
     * 单位
     */
    private String packingUnit;
    /**
     * 生产厂家
     */
    private String manufacturer;
    /**
     * 产地
     */
    private String productPlace;
    /**
     * 批准文号
     */
    private String approvalNumber;
    /**
     * 批号
     */
    private String batchNo;
    /**
     * 业务操作数量
     */
    private BigDecimal amount;
    /**
     * 含税价
     */
    private BigDecimal taxPrice;
    /**
     * 含税总金额
     */
    private BigDecimal taxPriceSum;

    /**
     *  存放货区/货位
     */
    private String areaPositionName;

    /**
     * 税率
     */
    private BigDecimal rate;

    /**
     * 生产日期/有效期至
     */
    private String productionDateAndValidUntil;
    /**
     * 生产日期
     */
    private String productionDate;
    /**
     * 有效期至
     */
    private String validUntil;

    /**
     * 当前零售价
     */
    private BigDecimal retailPrice;
    /**
     * 库存数量
     */
    private BigDecimal stockNumber;
    /**
     * 历史含税价
     */
    private BigDecimal lastTaxPrice;
    /**
     * 门店出库数量
     */
    private BigDecimal storeExitAmount;
    /**
     * 申请数量
     */
    private BigDecimal applyAmount;

    /**
     * 上市许可证人
     */
    private String drugPermissionPerson;
    /**
     * 退货原因
     */
    private String returnReasonName;

    /**
     * 库存状态/验收结论
     */
    private String acceptConclusion;

    /**
     * 合格数量
     */
    private BigDecimal qualifiedAmount;

    /**
     * 货区
     */
    private String cargoAreaName;

    /**
     * 货位
     */
    private String positionNoName;

    /**
     * 存储条件名称
     */
    private String storageConditionName;
}
