package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Data;

import java.util.List;

/**
 * ClassName: LogisticsPlatformQueryDto
 * Function: 委托配送共仓 调用erp  入参
 * Date:      2020/8/21 16:25
 * author     sunyue
 */
@Data
public class LogisticsPlatformQueryDto {
    /**
     * ec机构编号
     */
    private String ecOrgCode;
    /**
     * 业务类型code ： 默认55 为智鹿
     */
    private Integer channelCode;
    /**
     * 业务类型名称
     */
    private String channelName;

    /**
     * 仓库类别 默认可为 1
     */
    private Integer storehouseType;

    /**
     * 供应商仓库地址 （发货地址）
     */
    private String supplyStoreAddress;

    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 供应商编号
     */
    private String supplierCode;

    /**
     * 供应商运输工具(0:厢式送货;1:冷藏车;2:保温车;3:冷藏箱;4:其他封闭式车辆)
     */
    private Integer supplyTransMethod;

    /**
     * 创建人oaId
     */
    private Long createUserId;
    /**
     * 创建人名称
     */
    private String createUserName;
    /**
     *  采购智鹿单号
     */
    private String thirdpartyOrderNo;
    /**
     * 	更新人oaId
     */
    private Long updateUserId;

    /**
     * 更新人名称
     */
    private String updateUserName;

    /**
     * 订单备注信息
     */
    private String supplyRemark;
    /**
     * 订单类型默认 0 为智鹿
     */
    private Integer orderType;
    /**
     * 运输方式 1陆运 2空运 3船运
     */
    private Integer transportModel;

    /**
     * 送（提）货方式（0:送货上门;1:公司配送;2:客户自提;3:委托配送;4:公司自提）
     */
    private Integer deliveryMethod;

    /**
     * 采购时间
     */
    private String purchaseTime;

    /**
     * 采购订单状态(0:草稿;1:审核中;2:在途;3:已入库;4:异常关闭;5:已驳回) 默认在途 2
     */
    private Integer orderStatus;
    /**
     * 预计到货时间
     */
    private String expectDeliveryDate;


    private List<LogisticsPlatformQueryDetailDto> productDetail;

}
