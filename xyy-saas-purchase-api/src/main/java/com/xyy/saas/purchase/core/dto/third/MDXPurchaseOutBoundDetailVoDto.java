package com.xyy.saas.purchase.core.dto.third;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
public class MDXPurchaseOutBoundDetailVoDto implements Serializable {
    /**
     * 曼迪新传来的明细id
     */
    private String mdxId;

    /**
     * 商品内码
     */
    private String productCode;

    /**
     * 标准库id
     */
    private String standardProductId;

    /**
     * 出库数量
     */
    private BigDecimal outStorageAmount;

    /**
     * 商品含税单价
     */
    private BigDecimal productTaxPrice;

    /**
     * 商品含税总价
     */
    private BigDecimal productTaxPriceSum;

    /**
     * 商品批号
     */
    private String productBatchNo;

    /**
     * 商品生产日期
     */
    private String productDate;

    /**
     * 商品有效期
     */
    private String expireDate;


    /**
     * saas侧预留参数
     */
    private String tripartiteParameter;
}
