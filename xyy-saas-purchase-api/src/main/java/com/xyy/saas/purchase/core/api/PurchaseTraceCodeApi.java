package com.xyy.saas.purchase.core.api;


import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.SendEventDto;
import com.xyy.saas.purchase.core.dto.PurchaseTraceCodeDto;
import com.xyy.saas.purchase.core.dto.PurchaseTraceCodeValidationDto;
import com.xyy.saas.purchase.core.dto.SaasPurchaseBillDetailDto;
import com.xyy.saas.purchase.core.dto.SaasPurchaseRetrieveBillDetailDto;

import java.util.List;

/**
 * ClassName: PurchaseTraceCodeApi
 * Function:  采购追溯码api
 * Date:      2020/10/20 14:50
 * author     sunyue
 */
public interface PurchaseTraceCodeApi {

    /**
     * 根据订单编号、商品内码查询追溯码信息
     *
     * @param organSign
     * @param billNo
     * @param productPref
     * @return
     */
    ResultVO<List<PurchaseTraceCodeDto>> selectTraceCodeByProductPref(String organSign, String billNo, String productPref);

    /**
     * 批量查询采购追溯码
     * @param organSign
     * @param billNos
     * @param productPrefs
     * @return
     */
    ResultVO<List<PurchaseTraceCodeDto>> selectTraceCodeByProductPrefs(String organSign, List<String> billNos, List<String> productPrefs);

    /**
     * 根据机构号、追溯码查询信息
     *
     * @param organSign
     * @param traceCode
     * @return
     */
    ResultVO<PurchaseTraceCodeDto> selectTraceCodeByCode(String organSign, String traceCode);

    /**
     * 校验追溯码信息是否存在
     * @param traceCodeDto
     * @return
     */
    ResultVO<Boolean> checkTraceCode(PurchaseTraceCodeDto traceCodeDto);

    ResultVO sendTraceCodeEvent(SendEventDto sendEventDto);

    /**
     * 查询暂存，提交过的溯码
     * @param traceCodeDto
     * @return
     */
    ResultVO<List<String>> selectSubmitTraceCode(PurchaseTraceCodeDto traceCodeDto);

    /**
     * 根据条件查询
     *
     * @param purchaseTraceCodeDto
     * @return
     */
    ResultVO<List<PurchaseTraceCodeDto>> selectTraceCodeByQuery(PurchaseTraceCodeDto purchaseTraceCodeDto);

    /**
     * 处理上传失败的历史数据
     * @return
     */
    ResultVO handleTraceCodeHistory();

    /**
     * 处理上传失败的历史数据
     * @return
     */
    ResultVO handleTraceCodeFailInfo(String organSign);

    /**
     * 校验追溯码
     * @param organSign 机构
     * @param products 商品追溯码参数
     * @param isReceived true 收货 false 退货
     * @return
     */
    ResultVO validTraceCode(String organSign, List<PurchaseTraceCodeValidationDto> products, boolean isReceived);

    /**
     * 校验追溯码
     * @param organSign 机构
     * @param list 单据详情
     * @return
     */
    ResultVO validNonMsfxTraceCodes(String organSign, List<SaasPurchaseBillDetailDto> list);


    /**
     * 校验追溯码(出库)
     * @param organSign 机构
     * @param list 单据详情
     * @return
     */
    ResultVO validNonMsfxTraceCodesOut(String organSign, List<SaasPurchaseRetrieveBillDetailDto> list);
}
