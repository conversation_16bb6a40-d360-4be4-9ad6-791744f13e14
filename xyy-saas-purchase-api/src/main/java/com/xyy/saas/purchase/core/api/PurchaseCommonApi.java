package com.xyy.saas.purchase.core.api;


import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.print.PrintParamDto;
import com.xyy.saas.purchase.core.dto.requiregoods.PurchaseProductInfoDto;

import java.math.BigDecimal;
import java.util.List;

/**
 * 类名称:PurchaseCommonApi
 * 创建人:sunyue
 * 创建时间:2020/6/8 10:28
 */

public interface PurchaseCommonApi {


    /**
     * 根据区域 返回必填字段
     * @return
     */
    ResultVO getRequirteFieldByArea(String organSign);


    ResultVO<List<PurchaseProductInfoDto>> getProductInfo(List<String> productCodeList);

    ResultVO<List<BigDecimal>> getDefaultIncomeTaxRateList();

    /**
     * 采购全场景构建打印信息
     * @param printParamDto
     * @return
     */
    ResultVO buildPrintInfo(PrintParamDto printParamDto);

    /**
     * 采购全场景构建打印信息
     * @param printParamDto
     * @return
     */
    ResultVO buildPrintInfos(PrintParamDto printParamDto);

    /**
     * 判断连锁总部是否有设置库管角色
     * @param organSign
     * @return
     */
    ResultVO judgeChainHeadquartersHasExiterRole(String organSign);

    Byte getOrgHaveWareHouse(String organSign);
}
