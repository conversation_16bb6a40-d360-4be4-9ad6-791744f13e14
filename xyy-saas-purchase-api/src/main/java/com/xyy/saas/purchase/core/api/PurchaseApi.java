package com.xyy.saas.purchase.core.api;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.datasync.api.DataSyncApi;
import com.xyy.saas.common.dto.HightWoolGoodsDto;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.contract.dto.returngoods.PurchaseStoreExitLibraryApplyDetailDtoV2;
import com.xyy.saas.purchase.core.dto.*;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseQueryProductsVo;
import com.xyy.saas.purchase.core.dto.purchase.PurchaseSaleOutNeedMatchProductDto;
import com.xyy.saas.purchase.core.dto.requiregoods.PurchaseProductInfoQueryDto;
import com.xyy.saas.purchase.core.dto.requiregoods.PurchaseStoreWarehousingBillInfoDto;
import com.xyy.saas.purchase.core.dto.thirdparty.B2CProcessLogPo;

import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author: Jane<PERSON>Wang
 * Date: 2018/10/25
 * Time: 下午8:45
 * Explain:
 */
public interface PurchaseApi  extends DataSyncApi<SaasPurchaseBillInfoDto> {


    String heartbeat();


    /**
     * @Description 采够生成编号
     * @param billType : 单据编号
     * @return java.lang.String
     * <AUTHOR>
     * @Date 2019/4/28
     **/
    String getBillNo(String billType);

    /**
     * @Description  采购订单保存
     * @param info : 单据信息
     * @param list :  单据详情
     * @param userName : 操作人
     * @return java.lang.Integer
     * <AUTHOR>
     * @Date 2019/4/28
     **/
    Integer savePurchaseOrder(SaasPurchaseBillInfoPoDto info, List<SaasPurchaseBillDetailDto> list, String userName,String submitType);

    /**
     * @Description 去收货接口
     * @param billNo : 单据编号
     * @param organSign : 机构
     * @return java.lang.Integer
     * <AUTHOR>
     * @Date 2019/4/28
     **/
    Integer updateOrSubmitPurchaseOrderStatus(String billNo,String organSign,String userName);

    /**
     * 新增或修改收货复核单
     * @param info
     * @param list
     * @param userName
     * @return
     */
    Integer savePurchaseOneStep(String ip, SaasPurchaseBillInfoPoDto info, List<SaasPurchaseBillDetailDto> list, String userName,String submitType, List<String> billNos);

    /**
     * 一步入库检查是否需要拆单
     * @param billInfo
     * @param detailList
     * @return
     */
    List<String> checkDetails(SaasPurchaseBillInfoPoDto billInfo, List<SaasPurchaseBillDetailDto> detailList);


    Integer savePurchase(String ip, SaasPurchaseBillInfoPoDto info, List<SaasPurchaseBillDetailDto> list, String userName,String submitType);

    /**
     * 新增或修改采购退补价单
     * @param info
     * @param list
     * @param userName
     * @return
     */
    Integer savePurchaseOfRetrieve(SaasPurchaseRetrieveBillInfoDto info, List<SaasPurchaseRetrieveBillDetailDto> list, String userName);

    /**
     * 查询商品列表
     * @param pageInfo
     * @param name
     * @param organSign
     * @param isHidden
     * @return
     */
    PageInfo queryProductsAndAllStork(PageInfo pageInfo, String name, String organSign, Byte isHidden,String manufacturer,String type);

    /**
     * 查询商品列表
     * @param pageInfo
     * @param name
     * @param organSign
     * @param isHidden
     * @return
     */
    PageInfo queryProductsAndAllStork(PageInfo pageInfo, String name, String organSign, Byte isHidden,String manufacturer,String type, Integer orderMedicintType);

    List queryProductsAndAllStork(PurchaseQueryProductsVo purchaseQueryProductsVo);

    /**
     * 提取商城订单中手工匹配的查询商品信息的接口
     * @param productName
     * @param manufacturer
     * @param approvalNumber
     * @return
     */
    PageInfo<ProductBaseinfoPoDto> queryManualMatchingPros(String productName, String manufacturer, String approvalNumber, PageInfo pageInfo);


    /**
     * 获取商城订单详情
     * @param billNo
     * @return
     */
    ResultVO getECOrderDetail(String billNo, String organSign, String providerNum);

    /**
     * 获取订单中需要匹配的所有商品信息
     * @param bussinessId
     * @return
     */
    ResultVO getMatchingOrderCancelProductList(String bussinessId,String organSign);

    /**
     * 获取订单中需要匹配的所有商品信息
     * @param bussinessId
     * @return
     */
    ResultVO getMatchingProductList(String bussinessId,String organSign,String providerNum);

    /**
     * 查询商城订单中未匹配标准库ID的商品、商城订单总商品数、未匹配的商品数
     * @param businessId
     * @param organSign
     * @param providerNum
     * @return
     */
    ResultVO<PurchaseSaleOutNeedMatchProductDto> getMatchingProductListWithCount(String businessId,String organSign,String providerNum);

    boolean delRedis(String key);

    /**
     * 删除历史采购
     * @param organSign
     * @return
     */
    int deleteInitPurchaseBill(String organSign);

    /**
     * 根据商品内码查询是否有有相应的采购计划单，采购订单和收货复合
     * @param productPref
     * @param organSign
     * @return
     */
    boolean checkPurchaseCount(String productPref,String organSign);

    /**
     * 根据时间查询商品最大最小进价
     * @param productPref 商品pref
     * @param startDate  起始时间
     * @param end  结束时间
     * @param organSign
     * @return
     */
    ResultVO getMaxMinPurchasePrice(String productPref,String startDate,String end,String organSign);

    /**
     * 根据时间查询商品最大最小进价
     * @param productCodes 商品pref
     * @param startDate  起始时间
     * @param endDate  结束时间
     * @param organSign
     * @return
     */
    ResultVO getMaxMinPurchasePriceAll(List<String> productCodes,String startDate,String endDate,String organSign);

    /**
     * 根据商品外码，结构编号（药店唯一标识），是否隐藏标识来进行商品有库存的批号信息相关查询
     * @param name 商品外码（查询时要转换为商品内码进行数据库表的查询）
     * @param organSign 机构编号（药店唯一标识）
     * @param aByte 是否隐藏标识（系统统一标识）
     * @return ProductLotNumberInfoDto
     */
    ProductLotNumberInfoDto gainProductLotNumberInfo(String name, String organSign, Byte aByte);

    /**
     * @Description 采购退补价供应商刷数据接口
     * @param organSign :
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * <AUTHOR>
     * @Date 2019/4/18
     **/
    List<Map<String,Object>> getPurchaseRetrieveBusinessDealAccount(String organSign);

    /**
     * @Description 采购退补价供应商刷数据接口
     * @param organSign :机构id
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * <AUTHOR>
     * @Date 2019/4/18
     **/
    List<Map<String,Object>> getPurchaseRetrievePrice(String organSign,String supplierNo);

    /**
     * @Description 查询入库单数
     * @param startDate : 起始时间
     * @param endDate : 结束时间
     * @param organSign : 机构号
     * @return java.util.List<com.xyy.saas.purchase.core.dto.SaasPurchaseBillDetailPoDto>
     * <AUTHOR>
     * @Date 2019/5/24
     **/
    List<SaasPurchaseBillDetailPoDto> getPurchaseProductLotNumber(String startDate,String endDate,String organSign);

    List<HightWoolGoodsDto> selectNotMinedGoods(String organSign,Integer provinceCode);

    /**
     * @Description 根据ybm订单号获取质检图片地址
     * @param businessId :  ybm订单号
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @Date 2019/6/11
     **/
    List<String> getDrugReport(String businessId);

    /**
     * 查询商品最后三次入库采购价格
     * @param productCode
     * @param organSign
     * @return
     */
    Map<String, Object> getLastThreePurchasePrice(String productCode, String organSign);


    /**
     * 查询商品有无做过采购
     * @param organSign
     * @param productCode
     * @return
     */
    SaasPurchaseBillDetailPoDto purchaseProduct(String organSign, String productCode);

    /**
     * 查询供应商有无做过采购
     * @param organSign
     * @param supplierNo
     * @return
     */
    SaasPurchaseBillInfoPoDto purchaseSupplier(String organSign, String supplierNo);

    /**
     * 校验单据中商品的经营范围
     * @param billNo
     * @param organSign
     * @param productCodes
     * @return
     */
    ResultVO checkBusinessScope(String billNo, String organSign, String productCodes);

    /**
     * 收货复核单-上传质检报告单
     * @param organSign
     * @param id
     * @param url
     */
    ResultVO saveReceiveCheckFile(String organSign, Long id, String url);

    /**
     * 收货复核单-删除质检报告单
     * @param organSign
     * @param id
     * @return
     */
    ResultVO<String> delReceiveCheckFile(String organSign, Long id);

    /**
     * 收货复核单导入功能  根据taskID查询商品信息
     * @param dto
     * @return
     */
    ResultVO getReceiveBillExcelProducts(PurchaseProductInfoQueryDto dto);

    /**
     * 药监查询采购退出出库单
     * @param purchaseRetrieveBillInfoDto
     * @return
     */
    PageInfo<SaasPurchaseRetrieveBillInfoDto> getPurchaseRetrInfoListSFDA(PageInfo pageInfo,SaasPurchaseRetrieveBillInfoDto purchaseRetrieveBillInfoDto);


    /**
     * 查询B2C订单链路
     * @param queryStr 三方订单或者平台订单号
     * @return
     */
    ResultVO<List<B2CProcessLogPo>> queryB2CLink(String queryStr);

    /**
     * B2C采购重试
     *
     * @param progressId
     * @return com.xyy.saas.common.util.ResultVO<java.lang.Boolean>
     * <AUTHOR>
     * @date 2022/11/7 10:36
     **/
    ResultVO<Boolean> retryPurchaseB2CProgress(Long progressId);

    ResultVO<List<PurchaseB2CRobotDto>> queryB2CRobotMessage();


    /**
     * 根据调剂入库单号查询调剂申请单号
     * @param organSign 机构号
     * @param tjrkBillNos 调剂入库单号
     * @return 调剂申请单号
     */
    Map<String,String> queryAllocateBillNo(String organSign, List<String> tjrkBillNos);

    /**
     * 根据调剂申请单号插叙调剂入库单信息
     * @param organSign
     * @param parentBillNo
     * @return
     */
    PurchaseStoreWarehousingBillInfoDto getInfoByOrganAndParentBillNo(String organSign, String parentBillNo);

    List<PurchaseStoreExitLibraryApplyDetailDtoV2> queryStoreExitBatchList(String organSign, String tjsqBillNo);
    
    ResultVO queryEcOrderByHttp();
    
    ResultVO queryEcOrderByRpc();

    /**
     * 江西药监功 查询数据(根据机构号和id集合查询)
     * @param organSign
     * @param ids
     * @return
     */
//    ResultVO<List<SaasPurchaseBillInfoPoDto>> getPurchaseBillInfoByOrganSignAndIds(String organSign,List<Long> ids);
}
