package com.xyy.saas.purchase.core.api;

import com.xyy.saas.common.dto.InitDto;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.purchase.core.dto.InitPurchaseBillQueryDto;

/**
 * @ClassName PurchaseInitApi
 * @User: Lv.<PERSON>
 * @Date: 2018/12/26 18:36
 * @Description: 数据初始化Api
 */
public interface PurchaseInitApi {

    /**
     * 删除历史采购记录
     * @param organsign
     * @return
     */
    boolean deletePurchaseHistory(InitDto initDto);

    /**
     * 删除全部表数据
     * @param organsign
     * @return
     */
    boolean deleteAll(InitDto initDto);

    /**
     * 查询是否发生采购行为
     * @param organsign
     * @return
     */
    boolean isMakePurchase(String organsign);

    /**
     * 分页查询导入的历史采购记录
     * @param queryDto
     * @return
     */
    ResultVO selectByCondition(InitPurchaseBillQueryDto queryDto);
}
