package com.xyy.saas.purchase.core.dto.purchase;

import lombok.Data;

import java.math.BigDecimal;

/**
 * http对接物流开放平台- 订单详情参数实体
 */
@Data
public class PurchaseOrderDetailLogisticsPlaform {
    /**
     * 商品编码 （标准库id）
     */
    private String standardProductId;

    /**
     * 采购数量
     */
    private BigDecimal purchaseQuantity;

    /**
     * 渠道ID: 默认 1 : 药帮忙 55智鹿
     */
    private String channelId;

    /**
     * 自定义参数 json串
     */
    private String tripartiteParameter;

    /**
     * 含税价
     */
    private BigDecimal productTaxPrice;
}
