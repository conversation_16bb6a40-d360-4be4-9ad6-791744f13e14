package com.xyy.user.center.common.enums;

public enum CodeEnum {
    ERROR(-1, "失败"),
    SUCCESS(0, "成功"),
    PARAM_VALID_ERROR(400, "参数校验异常"),
    INTERNAL_ERROR(500, "内部错误"),

    ;

    CodeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private Integer code;
    private String msg;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
