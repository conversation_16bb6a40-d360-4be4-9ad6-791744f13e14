package com.xyy.saas.logistics.api;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.logistics.dto.SaasDistributionBillDto;

/**
 * @ClassName DistributionBillApi
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020/11/6 16:55
 **/
public interface DistributionBillApi {

    ResultVO queryList(SaasDistributionBillDto saasDistributionBillDto);

    ResultVO saveDistuibutionBill(SaasDistributionBillDto saasDistributionBillDto);

    ResultVO getDistributionBillPref(String organSign);

    ResultVO queryDetails(SaasDistributionBillDto saasDistributionBillDto);

    ResultVO deleteDistuibuteBill(SaasDistributionBillDto saasDistributionBillDto);

    ResultVO beginDistuibuteBill(SaasDistributionBillDto saasDistributionBillDto);

    ResultVO completeDistuibuteBill(SaasDistributionBillDto saasDistributionBillDto);

}
