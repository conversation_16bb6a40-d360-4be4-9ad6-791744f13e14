package com.xyy.saas.logistics.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @ClassName DistributionBillDto
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020/11/6 16:56
 **/

public class SaasDistributionOrderBillDto implements Serializable {
    private static final long serialVersionUID = -6346820270076749682L;

    private String organSign;
    //小票号
    private String ticketNo;

    //患者姓名
    private String memberName;
    //订单来源
    private String orderSourceStr;
    //商品原价
    private BigDecimal receivableAmount;
    //实收金额
    private BigDecimal actualAmount;
    //优惠金额
    private BigDecimal discountAmount;
    //备注
    private String remark;


    //下单开始时间
    private Date startDate;


    //下单结束时间
    private Date endDate;


    //当前页
    private Integer pageNum;

    //每页数量
    private Integer pageSize;

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public String getTicketNo() {
        return ticketNo;
    }

    public void setTicketNo(String ticketNo) {
        this.ticketNo = ticketNo;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getOrderSourceStr() {
        return orderSourceStr;
    }

    public void setOrderSourceStr(String orderSourceStr) {
        this.orderSourceStr = orderSourceStr;
    }

    public BigDecimal getReceivableAmount() {
        return receivableAmount;
    }

    public void setReceivableAmount(BigDecimal receivableAmount) {
        this.receivableAmount = receivableAmount;
    }

    public BigDecimal getActualAmount() {
        return actualAmount;
    }

    public void setActualAmount(BigDecimal actualAmount) {
        this.actualAmount = actualAmount;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
