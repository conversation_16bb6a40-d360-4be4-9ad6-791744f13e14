package com.xyy.saas.logistics.api;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.logistics.dto.SaasDistributionBillDto;
import com.xyy.saas.logistics.dto.SaasDistributionOrderBillDto;
import com.xyy.saas.logistics.dto.SaasDistributionOrderDetailDto;

/**
 * <AUTHOR>
 * @Description 配送订单
 * @Date 10:42 2020/11/9
 * @Param
 * @return
**/
public interface DistributionOrderApi {
    /**
     * @Description 配送订单列表
     * @Param
     * @return
     **/
    ResultVO queryOrderBill(SaasDistributionOrderBillDto saasDistributionOrderBillDto);
    /**
     * @Description 配送订单详情
     * @Param
     * @return
     **/
    ResultVO queryOrderDetail(SaasDistributionOrderDetailDto saasDistributionOrderDetailDto);
}
