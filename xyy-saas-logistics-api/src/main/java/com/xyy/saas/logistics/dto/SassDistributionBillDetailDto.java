package com.xyy.saas.logistics.dto;

import java.io.Serializable;
import java.math.BigDecimal;


public class SassDistributionBillDetailDto implements Serializable {
    private static final long serialVersionUID = 112583606845250525L;

    private Long id;

    private String distributionBill;

    private Integer version;

    private String baseVersion;

    private String organSign;

    private String productPref;

    private String productMixQuery;

    private String lotNumber;

    private BigDecimal distributionNumber;

    private BigDecimal orderNumber;

    private BigDecimal productRetailPrice;

    private Byte yn;

    //返回页面字段
    //商品编码
    private String pharmacyPref;
    //通用名字
    private String commonName;
    //商品名称
    private String productName;
    //单位
    private Integer unitId;
    private String unitName;
    //规格
    private String attributeSpecification;
    //剂型
    private Integer dosageFormId;
    private String dosageFormName;
    //生产厂家
    private String manufacturer;

    public Long getId() { return id; }

    public void setId(Long id) { this.id = id; }

    public String getDistributionBill() { return distributionBill; }

    public void setDistributionBill(String distributionBill) { this.distributionBill = distributionBill; }

    public Integer getVersion() { return version; }

    public void setVersion(Integer version) { this.version = version; }

    public String getBaseVersion() { return baseVersion; }

    public void setBaseVersion(String baseVersion) { this.baseVersion = baseVersion; }

    public String getOrganSign() { return organSign; }

    public void setOrganSign(String organSign) { this.organSign = organSign; }

    public String getProductPref() { return productPref; }

    public void setProductPref(String productPref) { this.productPref = productPref; }

    public String getLotNumber() { return lotNumber; }

    public void setLotNumber(String lotNumber) { this.lotNumber = lotNumber; }

    public BigDecimal getDistributionNumber() { return distributionNumber; }

    public void setDistributionNumber(BigDecimal distributionNumber) { this.distributionNumber = distributionNumber; }

    public BigDecimal getOrderNumber() { return orderNumber; }

    public void setOrderNumber(BigDecimal orderNumber) { this.orderNumber = orderNumber; }

    public BigDecimal getProductRetailPrice() { return productRetailPrice; }

    public void setProductRetailPrice(BigDecimal productRetailPrice) { this.productRetailPrice = productRetailPrice; }

    public Byte getYn() { return yn; }

    public void setYn(Byte yn) { this.yn = yn; }

    public String getPharmacyPref() { return pharmacyPref; }

    public void setPharmacyPref(String pharmacyPref) { this.pharmacyPref = pharmacyPref; }

    public String getCommonName() { return commonName; }

    public void setCommonName(String commonName) { this.commonName = commonName; }

    public String getProductName() { return productName; }

    public void setProductName(String productName) { this.productName = productName; }

    public Integer getUnitId() { return unitId; }

    public void setUnitId(Integer unitId) { this.unitId = unitId; }

    public String getUnitName() { return unitName; }

    public void setUnitName(String unitName) { this.unitName = unitName; }

    public String getAttributeSpecification() { return attributeSpecification; }

    public void setAttributeSpecification(String attributeSpecification) { this.attributeSpecification = attributeSpecification; }

    public Integer getDosageFormId() { return dosageFormId; }

    public void setDosageFormId(Integer dosageFormId) { this.dosageFormId = dosageFormId; }

    public String getDosageFormName() { return dosageFormName; }

    public void setDosageFormName(String dosageFormName) { this.dosageFormName = dosageFormName; }

    public String getManufacturer() { return manufacturer; }

    public void setManufacturer(String manufacturer) { this.manufacturer = manufacturer; }

    public String getProductMixQuery() { return productMixQuery; }

    public void setProductMixQuery(String productMixQuery) { this.productMixQuery = productMixQuery; }
}
