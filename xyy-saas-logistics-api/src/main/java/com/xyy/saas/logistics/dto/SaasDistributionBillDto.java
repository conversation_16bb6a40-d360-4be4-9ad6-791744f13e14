package com.xyy.saas.logistics.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @ClassName DistributionBillDto
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020/11/6 16:56
 **/

public class SaasDistributionBillDto implements Serializable {
    private static final long serialVersionUID = -6346820270076749682L;

    private Long id;

    private String pref;

    private Integer version;

    private String baseVersion;

    private String createUser;

    private Date createTime;

    private String updateUser;

    private Date updateTime;

    private String organSign;

    private String orderNo;

    //患者 -id
    private String patientUser;

    private List<String> patientUserList;

    //患者 -名字
    private String patientName;

    private String receiveUser;

    private String receivePhone;

    private String receiveArea;

    private String receiveAddress;

    //配送员id
    private String distributionUser;

    //配送员名称
    private String distributionUserStr;

    private Integer status;

    private Integer storageEnvironment;

    private Integer deliveryKit;

    private Integer iceRow;

    private String orderSource;

    //下单时间
    private Date orderTime;

    //下单时间
    private String orderTimeStr;

    //页面查询条件-下单开始时间
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date orderBeginTime;

    //页面查询条件-下单结束时间
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date orderEndTime;

    private BigDecimal orderAmount;

    private BigDecimal receivableAmount;

    //预约送达时间
    private Date appointmentTime;

    private String appointmentTimeStr;

    //页面查询条件-预约送达开始时间
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date appointmentBeginTime;

    //页面查询条件-预约送达结束时间
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date appointmentEndTime;

    //配送完成时间
    private Date deliveryTime;

    private String deliveryTimeStr;

    //页面查询条件-配送开始时间
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date deliveryBeginTime;

    //页面查询条件-配送结束时间
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date deliveryEndTime;

    //页面查询条件-商品信息
    private String productMixQuery;

    //当前页
    private Integer pageNum;

    //每页数量
    private Integer pageSize;

    //出库门店名字
    private String outDrugStoreName;

    private String remark;

    private Byte yn;

    private List<SassDistributionBillDetailDto> distributionDetailList;

    private List<Integer> storageEnvironmentList;

    public Long getId() { return id; }

    public void setId(Long id) { this.id = id; }

    public String getPref() { return pref; }

    public void setPref(String pref) { this.pref = pref; }

    public Integer getVersion() { return version; }

    public void setVersion(Integer version) { this.version = version; }

    public String getBaseVersion() { return baseVersion; }

    public void setBaseVersion(String baseVersion) { this.baseVersion = baseVersion; }

    public String getCreateUser() { return createUser; }

    public void setCreateUser(String createUser) { this.createUser = createUser; }

    public Date getCreateTime() { return createTime; }

    public void setCreateTime(Date createTime) { this.createTime = createTime; }

    public String getUpdateUser() { return updateUser; }

    public void setUpdateUser(String updateUser) { this.updateUser = updateUser; }

    public Date getUpdateTime() { return updateTime; }

    public void setUpdateTime(Date updateTime) { this.updateTime = updateTime; }

    public String getOrganSign() { return organSign; }

    public void setOrganSign(String organSign) { this.organSign = organSign; }

    public String getOrderNo() { return orderNo; }

    public void setOrderNo(String orderNo) { this.orderNo = orderNo; }

    public String getPatientUser() { return patientUser; }

    public void setPatientUser(String patientUser) { this.patientUser = patientUser; }

    public String getPatientName() { return patientName; }

    public void setPatientName(String patientName) { this.patientName = patientName; }

    public String getReceiveUser() { return receiveUser; }

    public void setReceiveUser(String receiveUser) { this.receiveUser = receiveUser; }

    public String getReceivePhone() { return receivePhone; }

    public void setReceivePhone(String receivePhone) { this.receivePhone = receivePhone; }

    public String getReceiveArea() { return receiveArea; }

    public void setReceiveArea(String receiveArea) { this.receiveArea = receiveArea; }

    public String getReceiveAddress() { return receiveAddress; }

    public void setReceiveAddress(String receiveAddress) { this.receiveAddress = receiveAddress; }

    public String getDistributionUser() { return distributionUser; }

    public void setDistributionUser(String distributionUser) { this.distributionUser = distributionUser; }

    public Integer getStatus() { return status; }

    public void setStatus(Integer status) { this.status = status; }

    public Integer getStorageEnvironment() { return storageEnvironment; }

    public void setStorageEnvironment(Integer storageEnvironment) { this.storageEnvironment = storageEnvironment; }

    public Integer getDeliveryKit() { return deliveryKit; }

    public void setDeliveryKit(Integer deliveryKit) { this.deliveryKit = deliveryKit; }

    public Integer getIceRow() { return iceRow; }

    public void setIceRow(Integer iceRow) { this.iceRow = iceRow; }

    public String getOrderSource() { return orderSource; }

    public void setOrderSource(String orderSource) { this.orderSource = orderSource; }

    public Date getOrderTime() { return orderTime; }

    public void setOrderTime(Date orderTime) { this.orderTime = orderTime; }

    public BigDecimal getOrderAmount() { return orderAmount; }

    public void setOrderAmount(BigDecimal orderAmount) { this.orderAmount = orderAmount; }

    public BigDecimal getReceivableAmount() { return receivableAmount; }

    public void setReceivableAmount(BigDecimal receivableAmount) { this.receivableAmount = receivableAmount; }

    public Date getAppointmentTime() { return appointmentTime; }

    public void setAppointmentTime(Date appointmentTime) { this.appointmentTime = appointmentTime; }

    public Date getDeliveryTime() { return deliveryTime; }

    public void setDeliveryTime(Date deliveryTime) { this.deliveryTime = deliveryTime; }

    public String getRemark() { return remark; }

    public void setRemark(String remark) { this.remark = remark; }

    public Byte getYn() { return yn; }

    public void setYn(Byte yn) { this.yn = yn; }

    public List<String> getPatientUserList() { return patientUserList; }

    public void setPatientUserList(List<String> patientUserList) { this.patientUserList = patientUserList; }

    public String getDistributionUserStr() { return distributionUserStr; }

    public void setDistributionUserStr(String distributionUserStr) { this.distributionUserStr = distributionUserStr; }

    public String getOrderTimeStr() { return orderTimeStr; }

    public void setOrderTimeStr(String orderTimeStr) { this.orderTimeStr = orderTimeStr; }

    public Date getOrderBeginTime() { return orderBeginTime; }

    public void setOrderBeginTime(Date orderBeginTime) { this.orderBeginTime = orderBeginTime; }

    public Date getOrderEndTime() { return orderEndTime; }

    public void setOrderEndTime(Date orderEndTime) { this.orderEndTime = orderEndTime; }

    public String getAppointmentTimeStr() { return appointmentTimeStr; }

    public void setAppointmentTimeStr(String appointmentTimeStr) { this.appointmentTimeStr = appointmentTimeStr; }

    public Date getAppointmentBeginTime() { return appointmentBeginTime; }

    public void setAppointmentBeginTime(Date appointmentBeginTime) { this.appointmentBeginTime = appointmentBeginTime; }

    public Date getAppointmentEndTime() { return appointmentEndTime; }

    public void setAppointmentEndTime(Date appointmentEndTime) { this.appointmentEndTime = appointmentEndTime; }

    public String getDeliveryTimeStr() { return deliveryTimeStr; }

    public void setDeliveryTimeStr(String deliveryTimeStr) { this.deliveryTimeStr = deliveryTimeStr; }

    public Date getDeliveryBeginTime() { return deliveryBeginTime; }

    public void setDeliveryBeginTime(Date deliveryBeginTime) { this.deliveryBeginTime = deliveryBeginTime; }

    public Date getDeliveryEndTime() { return deliveryEndTime; }

    public void setDeliveryEndTime(Date deliveryEndTime) { this.deliveryEndTime = deliveryEndTime; }

    public String getProductMixQuery() { return productMixQuery; }

    public void setProductMixQuery(String productMixQuery) { this.productMixQuery = productMixQuery; }

    public Integer getPageNum() { return pageNum; }

    public void setPageNum(Integer pageNum) { this.pageNum = pageNum; }

    public Integer getPageSize() { return pageSize; }

    public void setPageSize(Integer pageSize) { this.pageSize = pageSize; }

    public String getOutDrugStoreName() { return outDrugStoreName; }

    public void setOutDrugStoreName(String outDrugStoreName) { this.outDrugStoreName = outDrugStoreName; }

    public List<SassDistributionBillDetailDto> getDistributionDetailList() { return distributionDetailList; }

    public void setDistributionDetailList(List<SassDistributionBillDetailDto> distributionDetailList) { this.distributionDetailList = distributionDetailList; }

    public List<Integer> getStorageEnvironmentList() {
        return storageEnvironmentList;
    }

    public void setStorageEnvironmentList(List<Integer> storageEnvironmentList) {
        this.storageEnvironmentList = storageEnvironmentList;
    }
}
