<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.xyy.saas</groupId>
  <artifactId>xyy-saas-open-all</artifactId>
  <packaging>pom</packaging>
  <version>1.0.0-SNAPSHOT</version>
  <modules>
    <module>xyy-saas-open-api</module>
    <module>xyy-saas-open-web</module>
  </modules>

  <name>xyy-saas-open-all</name>


  <parent>
    <groupId>com.xyy.saas</groupId>
    <artifactId>saas-parent</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.1</version>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <distributionManagement>
    <repository>
      <id>maven-releases</id>
      <name>maven-releases</name>
      <url>http://maven.int.ybm100.com/repository/maven-releases/</url>
    </repository>
    <snapshotRepository>
      <id>maven-snapshots</id>
      <name>maven-snapshots</name>
      <url>http://maven.int.ybm100.com/repository/maven-snapshots/</url>
    </snapshotRepository>
  </distributionManagement>
</project>
