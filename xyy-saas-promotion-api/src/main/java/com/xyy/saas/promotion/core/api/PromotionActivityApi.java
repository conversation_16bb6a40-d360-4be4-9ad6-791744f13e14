package com.xyy.saas.promotion.core.api;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.datasync.api.DataSyncApi;
import com.xyy.saas.promotion.core.dto.*;

import java.util.List;
import java.util.Map;

/**
 * @Title:  PromotionActivityApi
 * @Description: 促销活动接口
 * @date:   2019-09-10 15:07 
 * @version V1.0.0
 */
public interface PromotionActivityApi extends DataSyncApi<PromotionActivityDto> {

    /**
     * 新增/编辑促销活动
     * @param promotionActivitySaveDto
     */
    int saveOrUpdatePromotionActivity(PromotionActivitySaveDto promotionActivitySaveDto);

    /**
     * 新增/编辑促销活动
     * @param promotionActivitySaveDto
     */
    PromotionActivityDto saveOrUpdatePromotionActivityV2(PromotionActivitySaveDto promotionActivitySaveDto);

    /**
     * 逻辑删除
     * @param id
     * @param userId
     */
    int deletePromotionActivityById(Long id,Integer userId);

    /**
     * 终止活动
     * @param id
     * @param userId
     */
    int updatePromotionActivityStatus(Long id,Integer userId,Integer status);

    /**
     * 分页查询
     * @param dto
     */
    PageInfo<PromotionActivityDto> list(PageInfo pageInfo,PromotionActivityDto dto);

    /**
     * 分页查询(商品维度)
     * @param dto
     */
    PageInfo<PromotionActivityProductResDto> listByProduct(PageInfo pageInfo,PromotionActivityProductReqDto dto);

    /**
     * 获取促销活动状态条目数
     * @param dto
     * @return
     */
    List<PromotionActivityStatusStatDto> getPromotionProductNumberByStatus(PromotionActivityProductReqDto dto);



    /**
     * 分页查询,带明细信息
     * @param dto
     * @return
     */
    PageInfo<PromotionActivityResItemDto> selectPageDetailInfo(PromotionActivityReqDto dto);

    /**
     * 通过id查询
     * @param id
     * @return
     */
    PromotionActivityInfoDto selectById(Long id,Integer hasCopy);

    /**
     * 门店端查询促销报表
     * @param dto
     * @return
     */
    PromotionActivityDto getPromotionByPre(PromotionActivityDto dto);

    /**
     * 根据促销编号集合查询
     * @param promotionPrefs
     * @return
     */
    Map<String, String> getPromotionListByPrefs(List<String> promotionPrefs);

    /**
     * 审核活动
     * @param organSign
     * @param promotionPref
     * @param auditStatus 6通过 7驳回
     * @return
     */
    int auditPromotionActivity(String organSign,String promotionPref,Integer auditStatus, Integer updateUser);

    /**
     * 查看适用门店列表
     * @param pageInfo
     * @param organSign
     * @param id
     * @return
     */
    PageInfo<TargetStoreVo> getTargetStoreListByPage(PageInfo pageInfo, String organSign, Long id);

    /**
     * 总部端查询促销报表
     * @param dto
     * @return
     */
    PromotionActivityDto getPromotionReportForm(PromotionActivityDto dto);

    /**
     * 促销活动数量查询
     * @param activityDto
     * @return
     */
    int selectActivityCount(PromotionActivityDto activityDto);

    /**
     * 促销活动列表
     * @param activityDto
     * @return
     */
    List<PromotionActivityDto> selectPromotionByDate(PromotionActivityDto activityDto);

    /**
     * 根据活动状态获取活动数量
     * @param activityDto
     * @return
     */
    List<PromotionActivityStatusStatDto> getPromotionNumberByStatus(PromotionActivityDto activityDto);

    /**
     * (线程池)补偿策略，补偿门店活动
     */
    Boolean timingUpdateActivityTask();

}