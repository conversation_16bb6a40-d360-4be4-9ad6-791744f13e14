package com.xyy.saas.promotion.core.api;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.promotion.core.dto.PromotionActivityDetailBufferReqDto;
import com.xyy.saas.promotion.core.dto.PromotionActivityDetailBufferRespDto;
import com.xyy.saas.promotion.core.dto.PromotionProductInfoCacheDto;

public interface PromotionActivityDetailBufferApi {
    /**
     * 缓存前端添加的商品,仅限前端使用
     */
    ResultVO<PromotionActivityDetailBufferRespDto> cacheAddProducts(PromotionActivityDetailBufferReqDto promotionActivityDetailBufferReqDto);

    /**
     * 编辑前端已缓存商品,仅限前端使用
     */
    int cacheEditProducts(PromotionActivityDetailBufferReqDto promotionActivityDetailBufferReqDto);

    /**
     * 删除前端已缓存添加的商品,仅限前端使用
     */
    int cacheDeleteProducts(PromotionActivityDetailBufferReqDto promotionActivityDetailBufferReqDto);

    /**
     * 清除操作缓存
     */
    int clearDetailCache(PromotionActivityDetailBufferReqDto promotionActivityDetailBufferReqDto);

    /**
     * 分页查询缓存商品
     */
    PageInfo<PromotionProductInfoCacheDto> pageQueryDetailCache(PromotionActivityDetailBufferReqDto promotionActivityDetailBufferReqDto);

    /**
     *查询数量
     */
    int queryCacheProductsNum(PromotionActivityDetailBufferReqDto promotionActivityDetailBufferReqDto);

    /**
     *缓存数据准备
     */
    ResultVO<PromotionActivityDetailBufferRespDto> preCacheData(PromotionActivityDetailBufferReqDto reqDto);

}
