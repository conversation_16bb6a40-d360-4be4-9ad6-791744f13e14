package com.xyy.saas.promotion.core.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/*
 * Description: 组合促销商品明细阶梯 返回对象
 * @date: 2021/08/05 17:59
 */
public class PromotionDetailInfoListDto implements Serializable {
    /**
     * 组合促销商品明细
     */
    private List<PromotionDetailInfoDto> promotionCombinedProductList = new ArrayList<>();

    /**
     * 累计限量(组合促销传值)
     */
    private  Integer ladderTotalLimit;

    /**
     * 日限量(组合促销传值)
     */
    private  Integer ladderDailyLimit;

    public List<PromotionDetailInfoDto> getPromotionCombinedProductList() {
        return promotionCombinedProductList;
    }

    public void setPromotionCombinedProductList(List<PromotionDetailInfoDto> promotionCombinedProductList) {
        this.promotionCombinedProductList = promotionCombinedProductList;
    }

    public Integer getLadderTotalLimit() {
        return ladderTotalLimit;
    }

    public void setLadderTotalLimit(Integer ladderTotalLimit) {
        this.ladderTotalLimit = ladderTotalLimit;
    }

    public Integer getLadderDailyLimit() {
        return ladderDailyLimit;
    }

    public void setLadderDailyLimit(Integer ladderDailyLimit) {
        this.ladderDailyLimit = ladderDailyLimit;
    }

}
