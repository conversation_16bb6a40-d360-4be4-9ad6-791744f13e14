package com.xyy.saas.promotion.core.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class PromotionActivityDetailDto implements Serializable {
    private static final long serialVersionUID = -1447295128096307329L;

    private Long id;

    /**
     * 促销活动编号
     */
    private String promotionPref;

    /**
     * 商品编号
     */
    private String productPref;

    /**
     * 折扣，1-100
     */
    private BigDecimal discountRate;

    /**
     * 促销价
     */
    private BigDecimal promotionPrice;

    /**
     * 每天限量剩余
     */
    private Integer remainDaily;

    /**
     * 累计限量剩余
     */
    private Integer remainTotal;

    /**
     * 逻辑删除 1 有效 0 删除
     */
    private Byte yn;

    /**
     * 药店唯一标识
     */
    private String organSign;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作版本号
     */
    private Long baseVersion;

    /**
     * 创建人
     */
    private Integer createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private Integer updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    //会员每日限量
    private Integer memberDailyLimit;
    //会员活动期间限量
    private Integer memberActivityLimit;

    /**
     * 组合数量(组合促销专用)
     */
    private Integer combinationCount;

    /**
     * 促销阶梯id(组合促销专用)
     */
    private Integer activityLadderId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPromotionPref() {
        return promotionPref;
    }

    public void setPromotionPref(String promotionPref) {
        this.promotionPref = promotionPref;
    }

    public String getProductPref() {
        return productPref;
    }

    public void setProductPref(String productPref) {
        this.productPref = productPref;
    }

    public BigDecimal getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }

    public BigDecimal getPromotionPrice() {
        return promotionPrice;
    }

    public void setPromotionPrice(BigDecimal promotionPrice) {
        this.promotionPrice = promotionPrice;
    }

    public Integer getRemainDaily() {
        return remainDaily;
    }

    public void setRemainDaily(Integer remainDaily) {
        this.remainDaily = remainDaily;
    }

    public Integer getRemainTotal() {
        return remainTotal;
    }

    public void setRemainTotal(Integer remainTotal) {
        this.remainTotal = remainTotal;
    }

    public Byte getYn() {
        return yn;
    }

    public void setYn(Byte yn) {
        this.yn = yn;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(Long baseVersion) {
        this.baseVersion = baseVersion;
    }

    public Integer getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Integer createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Integer updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getMemberDailyLimit() {
        return memberDailyLimit;
    }

    public void setMemberDailyLimit(Integer memberDailyLimit) {
        this.memberDailyLimit = memberDailyLimit;
    }

    public Integer getMemberActivityLimit() {
        return memberActivityLimit;
    }

    public void setMemberActivityLimit(Integer memberActivityLimit) {
        this.memberActivityLimit = memberActivityLimit;
    }

    public Integer getCombinationCount() {
        return combinationCount;
    }

    public void setCombinationCount(Integer combinationCount) {
        this.combinationCount = combinationCount;
    }

    public Integer getActivityLadderId() {
        return activityLadderId;
    }

    public void setActivityLadderId(Integer activityLadderId) {
        this.activityLadderId = activityLadderId;
    }
}
