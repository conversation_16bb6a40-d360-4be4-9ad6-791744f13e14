package com.xyy.saas.promotion.core.dto;

import java.io.Serializable;
import java.util.List;

public class PromotionTypeDto implements Serializable {

    private static final long serialVersionUID = -326399632083928780L;
    //主键id
    private Long id;

    //活动类型：1--特价促销，2--折扣促销，3--赠品促销，4--满减促销
    private Byte promotionType;

    //活动方式：1--单品特价，2--满件特价，3--倍数特价，4--批号特价，5--单品折扣，6--满件折扣，7--满额折扣，8--组合折扣,9--倍数折扣，10--满件赠，11--满额赠，12--组合赠,13--买N件免一件，14--满件减现金，15--满额减现金，16--组合减现金
    private Byte promotionMode;

    //活动类型名称
    private String promotionTypeName;

    //活动方式名称
    private String promotionModeName;

    //备注
    private String remarks;

    //活动方式集合
    private List<PromotionTypeDto> promotionModes;

    //活动类别 1-一类  2-二类
    private Integer activityType;

    //版本号
    private Long baseVersion;

    //逻辑删除 1 有效 0 删除
    private Byte yn;

    //禁用启用 1 启用 0 禁用
    private Byte used;

    //机构号
    private String organSign;

  public Byte getPromotionType() {
        return promotionType;
    }

    public void setPromotionType(Byte promotionType) {
        this.promotionType = promotionType;
    }

    public Byte getPromotionMode() {
        return promotionMode;
    }

    public void setPromotionMode(Byte promotionMode) {
        this.promotionMode = promotionMode;
    }

    public String getPromotionTypeName() {
        return promotionTypeName;
    }

    public void setPromotionTypeName(String promotionTypeName) {
        this.promotionTypeName = promotionTypeName;
    }

    public String getPromotionModeName() {
        return promotionModeName;
    }

    public void setPromotionModeName(String promotionModeName) {
        this.promotionModeName = promotionModeName;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public List<PromotionTypeDto> getPromotionModes() {
        return promotionModes;
    }

    public void setPromotionModes(List<PromotionTypeDto> promotionModes) {
        this.promotionModes = promotionModes;
    }

    public Integer getActivityType() {
        return activityType;
    }

    public void setActivityType(Integer activityType) {
        this.activityType = activityType;
    }

    public Long getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(Long baseVersion) {
        this.baseVersion = baseVersion;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Byte getYn() {
        return yn;
    }

    public void setYn(Byte yn) {
        this.yn = yn;
    }

    public Byte getUsed() {
        return used;
    }

    public void setUsed(Byte used) {
        this.used = used;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }
}
