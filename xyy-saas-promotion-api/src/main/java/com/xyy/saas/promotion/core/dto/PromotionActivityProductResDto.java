package com.xyy.saas.promotion.core.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 功能描述 促销活动商品维度返回结果
 *
 * <AUTHOR>
 * @Date 2021/6/15 10:15
 * @Email <EMAIL>
 */
public class PromotionActivityProductResDto implements Serializable{
    private static final long serialVersionUID = -8548708105705238045L;
    /**
     * 促销活动编号
     */
    private String pref;
    //商品编号
    private String productPref;
    //通用名称
    private String commonName;
    //规格
    private String attributeSpecification;
    //单位
    private String unitName;
    //生产厂家
    private String manufacturer;
    //活动名称
    private String promotionName;
    //创建时间
    private Date createTime;
    //活动开始时间
    private Date startDate;
    //活动结束时间
    private Date endDate;
    //促销方式
    private String promotionModeName;
    //商品类型 1-活动商品 0-活动赠品
    private Byte productType;
    //适用门店
    private String organSign;
    //创建类型 1-门店创建 3-'总部创建-指定商品 4-总部创建-自主商品
    private Byte createType;
    //活动状态:1:已结束， 2:未开始，3:已暂停，4:进行中 5:待审核 7:审核驳回 8:同步中
    private Byte status;
    //商品名称
    private String productName;
    //剂型
    private String dosageFormName;
    //商品分类名称
    private String systemTypeName;
    //自定义分类名称
    private String productTypeName;
    //abc分类名称
    private String abcDividingName;
    //零售价
    private BigDecimal retailPrice;
    //会员价
    private BigDecimal vipPrice;
    //标注库id
    private Long standardLibraryId;

    private String createTimeStr;

    private String activityDateStr;

    private String productTypeStr;

    private String createTypeStr;

    private String statusStr;

    public Long getStandardLibraryId() {
        return standardLibraryId;
    }

    public void setStandardLibraryId(Long standardLibraryId) {
        this.standardLibraryId = standardLibraryId;
    }

    public String getCreateTimeStr() {
        return createTimeStr;
    }

    public void setCreateTimeStr(String createTimeStr) {
        this.createTimeStr = createTimeStr;
    }

    public String getActivityDateStr() {
        return activityDateStr;
    }

    public void setActivityDateStr(String activityDateStr) {
        this.activityDateStr = activityDateStr;
    }

    public String getProductTypeStr() {
        return productTypeStr;
    }

    public void setProductTypeStr(String productTypeStr) {
        this.productTypeStr = productTypeStr;
    }

    public String getCreateTypeStr() {
        return createTypeStr;
    }

    public void setCreateTypeStr(String createTypeStr) {
        this.createTypeStr = createTypeStr;
    }

    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

    /**
     * 适用门店列表
     */
    private List<String> targetStoreList;

    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    public List<String> getTargetStoreList() {
        return targetStoreList;
    }

    public void setTargetStoreList(List<String> targetStoreList) {
        this.targetStoreList = targetStoreList;
    }

    public String getProductPref() {
        return productPref;
    }

    public void setProductPref(String productPref) {
        this.productPref = productPref;
    }

    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    public String getAttributeSpecification() {
        return attributeSpecification;
    }

    public void setAttributeSpecification(String attributeSpecification) {
        this.attributeSpecification = attributeSpecification;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getPromotionName() {
        return promotionName;
    }

    public void setPromotionName(String promotionName) {
        this.promotionName = promotionName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Byte getProductType() {
        return productType;
    }

    public void setProductType(Byte productType) {
        this.productType = productType;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public Byte getCreateType() {
        return createType;
    }

    public void setCreateType(Byte createType) {
        this.createType = createType;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getDosageFormName() {
        return dosageFormName;
    }

    public void setDosageFormName(String dosageFormName) {
        this.dosageFormName = dosageFormName;
    }

    public String getSystemTypeName() {
        return systemTypeName;
    }

    public void setSystemTypeName(String systemTypeName) {
        this.systemTypeName = systemTypeName;
    }

    public String getProductTypeName() {
        return productTypeName;
    }

    public void setProductTypeName(String productTypeName) {
        this.productTypeName = productTypeName;
    }

    public String getAbcDividingName() {
        return abcDividingName;
    }

    public void setAbcDividingName(String abcDividingName) {
        this.abcDividingName = abcDividingName;
    }

    public BigDecimal getRetailPrice() {
        return retailPrice;
    }

    public void setRetailPrice(BigDecimal retailPrice) {
        this.retailPrice = retailPrice;
    }

    public BigDecimal getVipPrice() {
        return vipPrice;
    }

    public void setVipPrice(BigDecimal vipPrice) {
        this.vipPrice = vipPrice;
    }

    public String getPromotionModeName() {
        return promotionModeName;
    }

    public void setPromotionModeName(String promotionModeName) {
        this.promotionModeName = promotionModeName;
    }
}
