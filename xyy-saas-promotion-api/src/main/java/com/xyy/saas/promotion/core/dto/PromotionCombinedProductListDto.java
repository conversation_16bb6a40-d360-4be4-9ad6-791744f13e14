package com.xyy.saas.promotion.core.dto;

import java.io.Serializable;
import java.util.List;

public class PromotionCombinedProductListDto implements Serializable {
    /**
     * 商品编号
     */
    private List<PromotionCombinedProductDto> promotionCombinedProductList;
    /**
     * 累计限量(组合促销传值)
     */
    private  Integer ladderTotalLimit;

    /**
     * 日限量(组合促销传值)
     */
    private  Integer ladderDailyLimit;

    public List<PromotionCombinedProductDto> getPromotionCombinedProductList() {
        return promotionCombinedProductList;
    }

    public void setPromotionCombinedProductList(List<PromotionCombinedProductDto> promotionCombinedProductList) {
        this.promotionCombinedProductList = promotionCombinedProductList;
    }

    public Integer getLadderTotalLimit() {
        return ladderTotalLimit;
    }

    public void setLadderTotalLimit(Integer ladderTotalLimit) {
        this.ladderTotalLimit = ladderTotalLimit;
    }

    public Integer getLadderDailyLimit() {
        return ladderDailyLimit;
    }

    public void setLadderDailyLimit(Integer ladderDailyLimit) {
        this.ladderDailyLimit = ladderDailyLimit;
    }
}
