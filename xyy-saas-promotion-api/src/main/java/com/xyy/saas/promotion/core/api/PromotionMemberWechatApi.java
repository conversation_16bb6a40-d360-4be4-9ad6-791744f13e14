package com.xyy.saas.promotion.core.api;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.promotion.core.dto.PromotionMemberCenterInfoDto;
import com.xyy.saas.promotion.core.dto.PromotionMemberCenterInfoRequestDto;

import java.util.List;


public interface PromotionMemberWechatApi {

    /**
     * 获取会员中心信息
     * @param requestDto
     * @return
     */
    ResultVO<List<PromotionMemberCenterInfoDto>> getPromotionMemberCenterInfo(PromotionMemberCenterInfoRequestDto requestDto);
}
