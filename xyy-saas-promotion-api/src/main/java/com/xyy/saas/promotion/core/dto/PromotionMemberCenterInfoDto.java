package com.xyy.saas.promotion.core.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class PromotionMemberCenterInfoDto implements Serializable {

    private static final long serialVersionUID = -5334767472106326702L;

    private String memberGuid;

    private String cartNo;

    private String organSign;

    private String drugstoreName;

    /**
     * 余额
     */
    private BigDecimal totalAmount;

    /**
     * 积分
     */
    private BigDecimal point;

    private String pointStr = "";

    /**
     * 优惠券张数
     */
    private int couponsCount;

    /**
     * 月会员日
     */
    private String monthDayStr = "";

    /**
     * 周会员日
     */
    private String weakDayStr = "";

    /**
     * 创建时间
     */
    private Date createTime;

    public String getMemberGuid() {
        return memberGuid;
    }

    public void setMemberGuid(String memberGuid) {
        this.memberGuid = memberGuid;
    }

    public String getCartNo() {
        return cartNo;
    }

    public void setCartNo(String cartNo) {
        this.cartNo = cartNo;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public String getDrugstoreName() {
        return drugstoreName;
    }

    public void setDrugstoreName(String drugstoreName) {
        this.drugstoreName = drugstoreName;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getPoint() {
        return point;
    }

    public void setPoint(BigDecimal point) {
        this.point = point;
    }

    public String getPointStr() {
        return pointStr;
    }

    public void setPointStr(String pointStr) {
        this.pointStr = pointStr;
    }

    public int getCouponsCount() {
        return couponsCount;
    }

    public void setCouponsCount(int couponsCount) {
        this.couponsCount = couponsCount;
    }

    public String getMonthDayStr() {
        return monthDayStr;
    }

    public void setMonthDayStr(String monthDayStr) {
        this.monthDayStr = monthDayStr;
    }

    public String getWeakDayStr() {
        return weakDayStr;
    }

    public void setWeakDayStr(String weakDayStr) {
        this.weakDayStr = weakDayStr;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
