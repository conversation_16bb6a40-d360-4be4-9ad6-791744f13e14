package com.xyy.saas.promotion.core.dto;

import com.github.pagehelper.PageInfo;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 优惠券报表
 */
public class StatisticalCouponTemplateDto implements Serializable {

    private static final long serialVersionUID = 5329733958634587385L;

    /**
     * 券模式: 1下单返券, 2生日关怀
     */
    private Byte couponModel;

    /**
     * 活动名称
     */
    private String couponName;

    /**
     * 券类型: 1代金券, 2折扣券
     */
    private Byte couponType;

    /**
     * 状态: 1进行中, 2暂停中, 3已结束
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 累计发放优惠券
     */
    private Integer couponGrantTotal;

    /**
     * 剩余优惠券数
     */
    private Integer couponResidueTotal;

    /**
     * 累计发放会员数
     */
    private Integer couponGrantMemberTotal;

    /**
     * 累计成交订单数
     */
    private Long orderNum;

    /**
     * 活动累计成交金额
     */
    private BigDecimal actualAmount;

    /**
     * 活动累计优惠金额
     */
    private BigDecimal couponAmount;

    /**
     * 门店数据列表
     * @return
     */
    private PageInfo<StatisticalCouponTemplateItemDto> couponTemplateDetail;

    /**
     * 折扣，1-100
     */
    private BigDecimal discountRate;
    /**
     * 减免金额
     */
    private BigDecimal remissionAmount;
    /**
     * 毛利率
     */
    private BigDecimal rateGross;
    /**
     * 起用券最低消费金额
     */
    private BigDecimal minMoneyToEnable;
    /**
     * 创建类型
     */
    private Byte createType;

    public Byte getCouponModel() {
        return couponModel;
    }

    public void setCouponModel(Byte couponModel) {
        this.couponModel = couponModel;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }

    public Byte getCouponType() {
        return couponType;
    }

    public void setCouponType(Byte couponType) {
        this.couponType = couponType;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getCouponGrantTotal() {
        return couponGrantTotal;
    }

    public void setCouponGrantTotal(Integer couponGrantTotal) {
        this.couponGrantTotal = couponGrantTotal;
    }

    public Integer getCouponResidueTotal() {
        return couponResidueTotal;
    }

    public void setCouponResidueTotal(Integer couponResidueTotal) {
        this.couponResidueTotal = couponResidueTotal;
    }

    public Integer getCouponGrantMemberTotal() {
        return couponGrantMemberTotal;
    }

    public void setCouponGrantMemberTotal(Integer couponGrantMemberTotal) {
        this.couponGrantMemberTotal = couponGrantMemberTotal;
    }

    public Long getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Long orderNum) {
        this.orderNum = orderNum;
    }

    public BigDecimal getActualAmount() {
        return actualAmount;
    }

    public void setActualAmount(BigDecimal actualAmount) {
        this.actualAmount = actualAmount;
    }

    public BigDecimal getCouponAmount() {
        return couponAmount;
    }

    public void setCouponAmount(BigDecimal couponAmount) {
        this.couponAmount = couponAmount;
    }

    public PageInfo<StatisticalCouponTemplateItemDto> getCouponTemplateDetail() {
        return couponTemplateDetail;
    }

    public void setCouponTemplateDetail(PageInfo<StatisticalCouponTemplateItemDto> couponTemplateDetail) {
        this.couponTemplateDetail = couponTemplateDetail;
    }

    public BigDecimal getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }

    public BigDecimal getRemissionAmount() {
        return remissionAmount;
    }

    public void setRemissionAmount(BigDecimal remissionAmount) {
        this.remissionAmount = remissionAmount;
    }

    public BigDecimal getRateGross() {
        return rateGross;
    }

    public void setRateGross(BigDecimal rateGross) {
        this.rateGross = rateGross;
    }

    public BigDecimal getMinMoneyToEnable() {
        return minMoneyToEnable;
    }

    public void setMinMoneyToEnable(BigDecimal minMoneyToEnable) {
        this.minMoneyToEnable = minMoneyToEnable;
    }

    public Byte getCreateType() {
        return createType;
    }

    public void setCreateType(Byte createType) {
        this.createType = createType;
    }
}
