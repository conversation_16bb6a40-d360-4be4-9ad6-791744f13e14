package com.xyy.saas.promotion.core.dto;

import com.github.pagehelper.PageInfo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 优惠券发券明细对象
 * <AUTHOR>
 */
public class StatCouponDetailDto implements Serializable {

    private Byte couponModel; //券模式: 1下单返券, 2生日关怀, 3会员券, 4新手券
    private String couponName; //活动名称
    private Byte couponType; //券类型: 1代金券, 2折扣券
    private BigDecimal discountRate; //折扣，1-100
    private BigDecimal remissionAmount; //减免金额
    private Date createTime;   //模板创建时间
    private Byte status; //模板状态
    private Byte createType; //创建类型
    private BigDecimal minMoneyToEnable; //最低消费金额


    private int couponCount;   //累计发放
    private int unUsedCount;   //未使用
    private int usedCount;     //已使用
    private int overdueCount;  //已过期

    //发券明细分页列表
    private PageInfo<CouponDetailDto> couponDetail;

    public Byte getCouponModel() {
        return couponModel;
    }

    public void setCouponModel(Byte couponModel) {
        this.couponModel = couponModel;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }

    public Byte getCouponType() {
        return couponType;
    }

    public void setCouponType(Byte couponType) {
        this.couponType = couponType;
    }

    public BigDecimal getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }

    public BigDecimal getRemissionAmount() {
        return remissionAmount;
    }

    public void setRemissionAmount(BigDecimal remissionAmount) {
        this.remissionAmount = remissionAmount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date templateCreateTime) {
        this.createTime = templateCreateTime;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Byte getCreateType() {
        return createType;
    }

    public void setCreateType(Byte createType) {
        this.createType = createType;
    }

    public BigDecimal getMinMoneyToEnable() {
        return minMoneyToEnable;
    }

    public void setMinMoneyToEnable(BigDecimal minMoneyToEnable) {
        this.minMoneyToEnable = minMoneyToEnable;
    }

    public int getCouponCount() {
        return couponCount;
    }

    public void setCouponCount(int couponCount) {
        this.couponCount = couponCount;
    }

    public int getUnUsedCount() {
        return unUsedCount;
    }

    public void setUnUsedCount(int unUsedCount) {
        this.unUsedCount = unUsedCount;
    }

    public int getUsedCount() {
        return usedCount;
    }

    public void setUsedCount(int usedCount) {
        this.usedCount = usedCount;
    }

    public int getOverdueCount() {
        return overdueCount;
    }

    public void setOverdueCount(int overdueCount) {
        this.overdueCount = overdueCount;
    }

    public PageInfo<CouponDetailDto> getCouponDetail() {
        return couponDetail;
    }

    public void setCouponDetail(PageInfo<CouponDetailDto> couponDetail) {
        this.couponDetail = couponDetail;
    }
}
