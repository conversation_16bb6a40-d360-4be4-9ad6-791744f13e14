package com.xyy.saas.promotion.core.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description 优惠券详情Dto
 * <AUTHOR>
 * @Create 2020-11-09 17:46
 */
public class CouponDetailDto implements Serializable {

    private static final long serialVersionUID = 3271485679502663619L;

    private String name; //姓名
    private String cartNo; //卡号
    private String sourceStore; //会员来源门店
    private String telephone; //手机号
    private Date createTime; //创建时间
    private String validityDate; //有效期[年月日]
    private String giveOrganSign; //发券方
    private String targetOrganSign; //适用门店
    private Byte status; //优惠券状态

    private Byte couponModel; //券模式: 1下单返券, 2生日关怀, 3会员券, 4新手券
    private String couponName; //活动名称
    private Byte couponType; //券类型: 1代金券, 2折扣券
    private BigDecimal discountRate; //折扣，1-100
    private BigDecimal remissionAmount; //减免金额

    //冗余的标识字段
    private Long id;
    //会员guid
    private String memberGuid;
    //券模板编号
    private String templatePref;
    /**
     * 创建类型
     */
    private Byte createType;

    //后端翻译字段汇总
    private String createTimeStr;
    private String statusStr;
    private String couponModelStr;
    private String couponTypeStr;
    private String createTypeStr;

    private BigDecimal minMoneyToEnable; //使用条件 最低消费金额

    /**
     * 有效期开始到结束时间
     */
    private String validityEndAndStartDate;


    public String getValidityDate() {
        return validityDate;
    }

    public void setValidityDate(String validityDate) {
        this.validityDate = validityDate;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMemberGuid() {
        return memberGuid;
    }

    public void setMemberGuid(String memberGuid) {
        this.memberGuid = memberGuid;
    }

    public String getTemplatePref() {
        return templatePref;
    }

    public void setTemplatePref(String templatePref) {
        this.templatePref = templatePref;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCartNo() {
        return cartNo;
    }

    public void setCartNo(String cartNo) {
        this.cartNo = cartNo;
    }

    public String getSourceStore() {
        return sourceStore;
    }

    public void setSourceStore(String sourceStore) {
        this.sourceStore = sourceStore;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getGiveOrganSign() {
        return giveOrganSign;
    }

    public void setGiveOrganSign(String giveOrganSign) {
        this.giveOrganSign = giveOrganSign;
    }

    public String getTargetOrganSign() {
        return targetOrganSign;
    }

    public void setTargetOrganSign(String targetOrganSign) {
        this.targetOrganSign = targetOrganSign;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Byte getCouponModel() {
        return couponModel;
    }

    public void setCouponModel(Byte couponModel) {
        this.couponModel = couponModel;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }

    public Byte getCouponType() {
        return couponType;
    }

    public void setCouponType(Byte couponType) {
        this.couponType = couponType;
    }

    public BigDecimal getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }

    public BigDecimal getRemissionAmount() {
        return remissionAmount;
    }

    public void setRemissionAmount(BigDecimal remissionAmount) {
        this.remissionAmount = remissionAmount;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public Byte getCreateType() {
        return createType;
    }

    public void setCreateType(Byte createType) {
        this.createType = createType;
    }

    public String getCreateTimeStr() {
        return createTimeStr;
    }

    public void setCreateTimeStr(String createTimeStr) {
        this.createTimeStr = createTimeStr;
    }

    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

    public String getCouponModelStr() {
        return couponModelStr;
    }

    public void setCouponModelStr(String couponModelStr) {
        this.couponModelStr = couponModelStr;
    }

    public String getCouponTypeStr() {
        return couponTypeStr;
    }

    public void setCouponTypeStr(String couponTypeStr) {
        this.couponTypeStr = couponTypeStr;
    }

    public String getCreateTypeStr() {
        return createTypeStr;
    }

    public void setCreateTypeStr(String createTypeStr) {
        this.createTypeStr = createTypeStr;
    }

    public BigDecimal getMinMoneyToEnable() {
        return minMoneyToEnable;
    }

    public void setMinMoneyToEnable(BigDecimal minMoneyToEnable) {
        this.minMoneyToEnable = minMoneyToEnable;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getValidityEndAndStartDate() {
        return validityEndAndStartDate;
    }

    public void setValidityEndAndStartDate(String validityEndAndStartDate) {
        this.validityEndAndStartDate = validityEndAndStartDate;
    }
}
