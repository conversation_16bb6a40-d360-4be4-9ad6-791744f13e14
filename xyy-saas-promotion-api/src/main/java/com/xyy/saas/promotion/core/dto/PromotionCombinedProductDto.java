package com.xyy.saas.promotion.core.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/*
 * Description: 组合促销商品信息专用类
 * @date: 2021/08/05 16:50
 */
public class PromotionCombinedProductDto implements Serializable {
    /**
     * 商品编号
     */
    private String productPref;
    /**
     * 商品标准库id
     */
    private Long standardLibraryId;

    /**
     * 商品数量
     */
    private Integer combinedProductNum;

    /**
     * 组合价格
     */
    private BigDecimal combinedProductPrice;

    public String getProductPref() {
        return productPref;
    }

    public void setProductPref(String productPref) {
        this.productPref = productPref;
    }

    public Long getStandardLibraryId() {
        return standardLibraryId;
    }

    public void setStandardLibraryId(Long standardLibraryId) {
        this.standardLibraryId = standardLibraryId;
    }

    public Integer getCombinedProductNum() {
        return combinedProductNum;
    }

    public void setCombinedProductNum(Integer combinedProductNum) {
        this.combinedProductNum = combinedProductNum;
    }

    public BigDecimal getCombinedProductPrice() {
        return combinedProductPrice;
    }

    public void setCombinedProductPrice(BigDecimal combinedProductPrice) {
        this.combinedProductPrice = combinedProductPrice;
    }
}
