package com.xyy.saas.promotion.core.api;

import com.xyy.saas.common.datasync.api.DataSyncApi;
import com.xyy.saas.promotion.core.dto.PromotionActivityDto;
import com.xyy.saas.promotion.core.dto.PromotionTypeDto;

import java.util.List;

public interface PromotionActivityTypeApi extends DataSyncApi<PromotionTypeDto>{

    /**
     * 获取所有促销类型
     * @return
     */
    List<PromotionTypeDto> getAllPromotionType();

    /**
     * 根据促销类型获取促销方式
     * @param  type
     * @return
     */
    List<PromotionTypeDto> getPromotionModeByType(Byte type);

    /**
     * 获取所有促销类型
     * @return
     */
    List<PromotionTypeDto> getAllPromotionMode();

    /**
     * 获取所有促销类型(根据apollo配置进行过滤)
     */
    List<PromotionTypeDto> getAllPromotionMode(String organSign);

    /**
     * 获取Apollo配置
     */
    String getApolloConfig(String configKey);

    /**
     * 获取Apollo配置展示商品限制数量
     */
    Integer getPromotionProductNumLimitConfig(String organSign);

    /**
     * 促销活动销售管控
     */
    Boolean sellControlFilter(String organSign);

    /**
     * 获取Apollo配置
     * @return
     */
    Boolean isShowConfig(String organSign, String configKey);
}
