package com.xyy.saas.promotion.core.api;


import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.datasync.api.DataSyncApi;
import com.xyy.saas.promotion.core.dto.*;

import java.util.List;
import java.util.Map;

/**
 * @Title:  PromotionActivityDetailApi
 * @Description: 促销商品明细接口
 * @date:   2019-09-10 16:43 
 * @version V1.0.0
 */
public interface PromotionActivityDetailApi extends DataSyncApi<PromotionActivityDetailDto> {

    /**
     * 查询详情列表
     * @param dto
     * @return
     */
    List<PromotionDetailInfoDto> selectDetailList(PromotionDetailSelectListParamDto dto);

    /**
     * 查询商品列表
     * @param dto
     * @return
     */
    PageInfo<PromotionProductInfoDto> selectProductList(PromotionProductSelectListParamDto dto);

    /**
     * 根据商品编号校验门店是否有此商品、并且商品是否已参加活动
     * @param
     * @return index=0 已参加其本门店他活动的商品集合、 index=1 所有商品集合
     */
    List<Map<String,String>> getProductInfo(String organSign);

    /**
     * 手动新增促销明细
     * @param dto
     * @return
     */
    int insertPromotionDetails(PromotionActivityDetailDto dto);

    /**
     * 从rule同步数据到ladder(已废弃)
     * @param organSigns
     * @return
     */
    int syncRuleToLadder(String organSigns);

    /**
     * 总部创建的活动，门店商品重复的数据
     * @param promotionPref
     * @param organSign
     * @return
     */
    PageInfo<PromotionActivitySameDetailDto> selectSamePromotionDetails(String promotionPref, String organSign, Integer pageSize, Integer pageNum);

    /**
     * 总部创建的活动，适用门店是否可开启
     * @param promotionPref
     * @param organSign
     * @return
     */
    boolean isStartPromotionActivity(String promotionPref, String organSign);

    /**
     * 删除活动中的商品
     * @param dto
     * @return
     */
    int deletePromotionDetail(PromotionActivityDetailDto dto);

    String getImportTaskId(String organSign, String operId);

}