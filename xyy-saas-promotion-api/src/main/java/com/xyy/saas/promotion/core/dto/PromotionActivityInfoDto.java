package com.xyy.saas.promotion.core.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 李建伟
 */
public class PromotionActivityInfoDto implements Serializable {


	private static final long serialVersionUID = -6541171191368496792L;

	private Long id;
	   
	/**
	 * 促销活动编号
	 */
	private String pref;
	   
	/**
	 * 活动名称
	 */
	private String promotionName;
	   
	/**
	 * 活动类型
	 */
	private Byte promotionType;
	   
	/**
	 * 活动方式
	 */
	private Byte promotionMode;
	   
	/**
	 * 活动状态: 1:已结束; 2:未开始，3:已暂停,4:进行中，
	 */
	private Byte status;

	/**
	 * 开始时间
	 */
	private Date startDate;

	/**
	 * 结束时间
	 */
	private Date endDate;

	/**
	 * 药店唯一标识
	 */
	private String organSign;
	   
	/**
	 * 数据同步版本号
	 */
	private Long baseVersion;
	   
	/**
	 * 创建人
	 */
	private Integer createUser;
	   
	/**
	 * 创建时间
	 */
	private Date createTime;
	   
	/**
	 * 修改人
	 */
	private Integer updateUser;
	   
	/**
	 * 修改时间
	 */
	private Date updateTime;
	   
	/**
	 * 逻辑删除 1 有效 0 删除
	 */
	private Byte yn;

	/**
	 * 赠品限量
	 */
	private Integer giftLimit;

	private PromotionActivityRuleDto ruleDto;

	private List<PromotionDetailInfoDto> detailList;

	/**
	 * 正在参加促销活动的商品集合
	 */
	private List<PromotionDetailInfoDto> repeatDetailList;

	private List<PromotionGiftInfoDto> giftList;

	/**
	 * 加价购赠品(阶梯)
	 */
	private List<PromotionGiftInfoLadderListDto> giftLadderList;

	/**
	 * 促销阶梯
	 */
	private List<PromotionActivityLadderDto> ladderList;

	/**
	 * 加价购商品明细
	 */
	private  List<PromotionActivityPurchaseListDto> purchaseProductList;

	/**
	 * 1 门店自建 2 总店派发
	 */
	private Byte createType;

	//会员每日限量
	private Integer memberDailyLimit;
	//会员活动期间限量
	private Integer memberActivityLimit;

	public Byte getProductMode() {
		return productMode;
	}

	public void setProductMode(Byte productMode) {
		this.productMode = productMode;
	}

	/**
	 * 商品类型 0:门店创建  1:总部创建-指定商品 2:总部创建-自主商品
	 */
	private Byte productMode;

	/**
	 * 组合促销 商品明细阶梯
	 */
	private List<PromotionDetailInfoListDto> promotionCombinedProductLists;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	
	public String getPref() {
		return pref;
	}
	public void setPref(String pref) {
		this.pref = pref;
	}
	
	public String getPromotionName() {
		return promotionName;
	}
	public void setPromotionName(String promotionName) {
		this.promotionName = promotionName;
	}
	
	public Byte getPromotionType() {
		return promotionType;
	}
	public void setPromotionType(Byte promotionType) {
		this.promotionType = promotionType;
	}
	
	public Byte getPromotionMode() {
		return promotionMode;
	}
	public void setPromotionMode(Byte promotionMode) {
		this.promotionMode = promotionMode;
	}
	
	public Byte getStatus() {
		return status;
	}
	public void setStatus(Byte status) {
		this.status = status;
	}
	
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	
	public String getOrganSign() {
		return organSign;
	}
	public void setOrganSign(String organSign) {
		this.organSign = organSign;
	}
	
	public Long getBaseVersion() {
		return baseVersion;
	}
	public void setBaseVersion(Long baseVersion) {
		this.baseVersion = baseVersion;
	}
	
	public Integer getCreateUser() {
		return createUser;
	}
	public void setCreateUser(Integer createUser) {
		this.createUser = createUser;
	}
	
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
	public Integer getUpdateUser() {
		return updateUser;
	}
	public void setUpdateUser(Integer updateUser) {
		this.updateUser = updateUser;
	}
	
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	
	public Byte getYn() {
		return yn;
	}
	public void setYn(Byte yn) {
		this.yn = yn;
	}

	public PromotionActivityRuleDto getRuleDto() {
		return ruleDto;
	}

	public void setRuleDto(PromotionActivityRuleDto ruleDto) {
		this.ruleDto = ruleDto;
	}

	public List<PromotionDetailInfoDto> getDetailList() {
		return detailList;
	}

	public void setDetailList(List<PromotionDetailInfoDto> detailList) {
		this.detailList = detailList;
	}

	public List<PromotionGiftInfoDto> getGiftList() {
		return giftList;
	}

	public void setGiftList(List<PromotionGiftInfoDto> giftList) {
		this.giftList = giftList;
	}

	public Integer getGiftLimit() {
		return giftLimit;
	}

	public void setGiftLimit(Integer giftLimit) {
		this.giftLimit = giftLimit;
	}

	public List<PromotionActivityLadderDto> getLadderList() {
		return ladderList;
	}

	public void setLadderList(List<PromotionActivityLadderDto> ladderList) {
		this.ladderList = ladderList;
	}

	public Byte getCreateType() {
		return createType;
	}

	public void setCreateType(Byte createType) {
		this.createType = createType;
	}

	public Integer getMemberDailyLimit() {
		return memberDailyLimit;
	}

	public void setMemberDailyLimit(Integer memberDailyLimit) {
		this.memberDailyLimit = memberDailyLimit;
	}

	public Integer getMemberActivityLimit() {
		return memberActivityLimit;
	}

	public void setMemberActivityLimit(Integer memberActivityLimit) {
		this.memberActivityLimit = memberActivityLimit;
	}

	public List<PromotionDetailInfoDto> getRepeatDetailList() {
		return repeatDetailList;
	}

	public void setRepeatDetailList(List<PromotionDetailInfoDto> repeatDetailList) {
		this.repeatDetailList = repeatDetailList;
	}



	public List<PromotionActivityPurchaseListDto> getPurchaseProductList() {
		return purchaseProductList;
	}

	public void setPurchaseProductList(List<PromotionActivityPurchaseListDto> purchaseProductList) {
		this.purchaseProductList = purchaseProductList;
	}

	public List<PromotionGiftInfoLadderListDto> getGiftLadderList() {
		return giftLadderList;
	}

	public void setGiftLadderList(List<PromotionGiftInfoLadderListDto> giftLadderList) {
		this.giftLadderList = giftLadderList;
	}

	public List<PromotionDetailInfoListDto> getPromotionCombinedProductLists() {
		return promotionCombinedProductLists;
	}

	public void setPromotionCombinedProductLists(List<PromotionDetailInfoListDto> promotionCombinedProductLists) {
		this.promotionCombinedProductLists = promotionCombinedProductLists;
	}
}