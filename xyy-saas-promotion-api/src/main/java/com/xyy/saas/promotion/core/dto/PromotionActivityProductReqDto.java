package com.xyy.saas.promotion.core.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @Date 2021/6/16 10:02
 * @Email <EMAIL>
 */
public class PromotionActivityProductReqDto implements Serializable {
    private static final long serialVersionUID = 8626599467717881163L;

    //商品信息
    private String productQuery;
    //活动名称
    private String promotionName;
    //促销方式
    private Byte promotionMode;
    //活动状态:1:已结束， 2:未开始，3:已暂停，4:进行中 5:待审核 7:审核驳回 8:同步中
    private Byte status;
    //适用门店：总部端才有值，所有门店 取值空串
    private String organSign;
    //商品分类
    private Integer systemTypeId;
    //自定义分类
    private Integer customTypeId;
    //abc分类
    private Integer abcTypeId;
    /**
     * 排序 1:按照创建时间倒序排序  2:按照开始时间倒序排序
     */
    private Integer sort;
    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 页码大小
     */
    private Integer pageSize;

    private String SortParams;

    private String targetStore;

    /**
     * 1 门店自建 2 总店派发
     */
    private Byte createType;

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 适用门店列表
     */
    private List<String> targetStoreList;

    /**
     * 是否隐藏门店
     */
    private Byte isDrugstoreHidden;

    public Byte getCreateType() {
        return createType;
    }

    public void setCreateType(Byte createType) {
        this.createType = createType;
    }

    public List<String> getTargetStoreList() {
        return targetStoreList;
    }

    public void setTargetStoreList(List<String> targetStoreList) {
        this.targetStoreList = targetStoreList;
    }

    public String getTargetStore() {
        return targetStore;
    }

    public void setTargetStore(String targetStore) {
        this.targetStore = targetStore;
    }

    public String getSortParams() {
        return SortParams;
    }

    public void setSortParams(String sortParams) {
        SortParams = sortParams;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getProductQuery() {
        return productQuery;
    }

    public void setProductQuery(String productQuery) {
        this.productQuery = productQuery;
    }

    public String getPromotionName() {
        return promotionName;
    }

    public void setPromotionName(String promotionName) {
        this.promotionName = promotionName;
    }

    public Byte getPromotionMode() {
        return promotionMode;
    }

    public void setPromotionMode(Byte promotionMode) {
        this.promotionMode = promotionMode;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public Integer getSystemTypeId() {
        return systemTypeId;
    }

    public void setSystemTypeId(Integer systemTypeId) {
        this.systemTypeId = systemTypeId;
    }

    public Integer getCustomTypeId() {
        return customTypeId;
    }

    public void setCustomTypeId(Integer customTypeId) {
        this.customTypeId = customTypeId;
    }

    public Integer getAbcTypeId() {
        return abcTypeId;
    }

    public void setAbcTypeId(Integer abcTypeId) {
        this.abcTypeId = abcTypeId;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Byte getIsDrugstoreHidden() {
        return isDrugstoreHidden;
    }

    public void setIsDrugstoreHidden(Byte isDrugstoreHidden) {
        this.isDrugstoreHidden = isDrugstoreHidden;
    }
}
