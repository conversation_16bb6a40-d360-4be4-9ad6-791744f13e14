package com.xyy.saas.promotion.core.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 优惠券统计
 */
public class StatCouponDto implements Serializable {


    private int orderCouponUsedCount;

    private int orderCouponSum;

    private List<Long> orderCouponIdList;

    private int birthdayCouponUsedCount;

    private int birthdayCouponSum;

    private List<Long> birthdayCouponIdList;

    public int getOrderCouponUsedCount() {
        return orderCouponUsedCount;
    }

    public void setOrderCouponUsedCount(int orderCouponUsedCount) {
        this.orderCouponUsedCount = orderCouponUsedCount;
    }

    public int getBirthdayCouponUsedCount() {
        return birthdayCouponUsedCount;
    }

    public void setBirthdayCouponUsedCount(int birthdayCouponUsedCount) {
        this.birthdayCouponUsedCount = birthdayCouponUsedCount;
    }

    public int getOrderCouponSum() {
        return orderCouponSum;
    }

    public void setOrderCouponSum(int orderCouponSum) {
        this.orderCouponSum = orderCouponSum;
    }

    public int getBirthdayCouponSum() {
        return birthdayCouponSum;
    }

    public void setBirthdayCouponSum(int birthdayCouponSum) {
        this.birthdayCouponSum = birthdayCouponSum;
    }

    public List<Long> getOrderCouponIdList() {
        return orderCouponIdList;
    }

    public void setOrderCouponIdList(List<Long> orderCouponIdList) {
        this.orderCouponIdList = orderCouponIdList;
    }

    public List<Long> getBirthdayCouponIdList() {
        return birthdayCouponIdList;
    }

    public void setBirthdayCouponIdList(List<Long> birthdayCouponIdList) {
        this.birthdayCouponIdList = birthdayCouponIdList;
    }
}
