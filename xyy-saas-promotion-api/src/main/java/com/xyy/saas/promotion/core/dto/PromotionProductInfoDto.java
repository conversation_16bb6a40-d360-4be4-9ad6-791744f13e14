package com.xyy.saas.promotion.core.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class PromotionProductInfoDto implements Serializable {

    private static final long serialVersionUID = 5106080928449579258L;

    //商品内码
    private String productPref;
    //商品编号,外码,用作展示
    private String pharmacyPref;
    //通用名称
    private String commonName;
    private String productName;
    //规格型号
    private String attributeSpecification;
    //单位名称
    private String unitName;
    //单位id
    private Integer unitId;
    //剂型id
    private Integer dosageFormId;
    //剂型名称
    private String dosageFormName;
    //生产厂家
    private String manufacturer;
    //商品分类id
    private Integer systemTypeId;
    //商品分类名称
    private String systemTypeName;
    //自定义分类id
    private Integer productType;
    //自定义分类名称
    private String productTypeName;
    //abc类型id
    private Integer abcDividing;
    //abc类型名称
    private String abcDividingName;
    //库存数量
    private BigDecimal stockNumber;
    //成本价
    private BigDecimal costPrice;
    //零售价
    private BigDecimal retailPrice;
    //vip会员价
    private BigDecimal vipPrice;
    //毛利率
    private BigDecimal grossMargin;
    //产地
    private String producingArea;
    //是否为特殊商品 0:否, 1:是
    private Byte special;
    //标准库id
    private Long standardLibraryId;
    //价格来源
    private String priceFromOrganSignName;
    //批准文号
    private String approvalNumber;
    //条形码
    private String barCode;
    //商品特价
    private  BigDecimal promotionPrice;

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public BigDecimal getPromotionPrice() {
        return promotionPrice;
    }

    public void setPromotionPrice(BigDecimal promotionPrice) {
        this.promotionPrice = promotionPrice;
    }

    public String getPharmacyPref() {
        return pharmacyPref;
    }

    public void setPharmacyPref(String pharmacyPref) {
        this.pharmacyPref = pharmacyPref;
    }

    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    public String getAttributeSpecification() {
        return attributeSpecification;
    }

    public void setAttributeSpecification(String attributeSpecification) {
        this.attributeSpecification = attributeSpecification;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getDosageFormId() {
        return dosageFormId;
    }

    public void setDosageFormId(Integer dosageFormId) {
        this.dosageFormId = dosageFormId;
    }

    public String getDosageFormName() {
        return dosageFormName;
    }

    public void setDosageFormName(String dosageFormName) {
        this.dosageFormName = dosageFormName;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public Integer getSystemTypeId() {
        return systemTypeId;
    }

    public void setSystemTypeId(Integer systemTypeId) {
        this.systemTypeId = systemTypeId;
    }

    public String getSystemTypeName() {
        return systemTypeName;
    }

    public void setSystemTypeName(String systemTypeName) {
        this.systemTypeName = systemTypeName;
    }

    public Integer getProductType() {
        return productType;
    }

    public void setProductType(Integer productType) {
        this.productType = productType;
    }

    public String getProductTypeName() {
        return productTypeName;
    }

    public void setProductTypeName(String productTypeName) {
        this.productTypeName = productTypeName;
    }

    public Integer getAbcDividing() {
        return abcDividing;
    }

    public void setAbcDividing(Integer abcDividing) {
        this.abcDividing = abcDividing;
    }

    public String getAbcDividingName() {
        return abcDividingName;
    }

    public void setAbcDividingName(String abcDividingName) {
        this.abcDividingName = abcDividingName;
    }

    public BigDecimal getStockNumber() {
        return stockNumber;
    }

    public void setStockNumber(BigDecimal stockNumber) {
        this.stockNumber = stockNumber;
    }

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public BigDecimal getRetailPrice() {
        return retailPrice;
    }

    public void setRetailPrice(BigDecimal retailPrice) {
        this.retailPrice = retailPrice;
    }

    public BigDecimal getVipPrice() {
        return vipPrice;
    }

    public void setVipPrice(BigDecimal vipPrice) {
        this.vipPrice = vipPrice;
    }


    public String getProducingArea() {
        return producingArea;
    }

    public void setProducingArea(String producingArea) {
        this.producingArea = producingArea;
    }

    public String getProductPref() {
        return productPref;
    }

    public void setProductPref(String productPref) {
        this.productPref = productPref;
    }

    public Byte getSpecial() {
        return special;
    }

    public void setSpecial(Byte special) {
        this.special = special;
    }

    public BigDecimal getGrossMargin() {
        return grossMargin;
    }

    public void setGrossMargin(BigDecimal grossMargin) {
        this.grossMargin = grossMargin;
    }

    public Long getStandardLibraryId() {
        return standardLibraryId;
    }

    public void setStandardLibraryId(Long standardLibraryId) {
        this.standardLibraryId = standardLibraryId;
    }

    public String getPriceFromOrganSignName() {
        return priceFromOrganSignName;
    }

    public void setPriceFromOrganSignName(String priceFromOrganSignName) {
        this.priceFromOrganSignName = priceFromOrganSignName;
    }

    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }
}
