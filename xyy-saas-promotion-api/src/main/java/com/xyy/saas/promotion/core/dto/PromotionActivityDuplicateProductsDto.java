package com.xyy.saas.promotion.core.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @Date 2021/6/10 16:57
 * @Email <EMAIL>
 */
public class PromotionActivityDuplicateProductsDto implements Serializable {

    private List<PromotionDetailInfoDto> detailList;
    /**
     * 正在参加促销活动的商品集合
     */
    private List<PromotionDetailInfoDto> repeatDetailList;

    private boolean hasShow = true;

    public boolean isHasShow() {
        return hasShow;
    }

    public void setHasShow(boolean hasShow) {
        this.hasShow = hasShow;
    }

    public List<PromotionDetailInfoDto> getDetailList() {
        return detailList;
    }

    public void setDetailList(List<PromotionDetailInfoDto> detailList) {
        this.detailList = detailList;
    }

    public List<PromotionDetailInfoDto> getRepeatDetailList() {
        return repeatDetailList;
    }

    public void setRepeatDetailList(List<PromotionDetailInfoDto> repeatDetailList) {
        this.repeatDetailList = repeatDetailList;
    }
}
