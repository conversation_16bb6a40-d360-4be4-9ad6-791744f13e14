package com.xyy.saas.promotion.core.dto;

import java.io.Serializable;
import java.util.Date;

public class PromotionActivityResItemDto implements Serializable {

    private static final long serialVersionUID = -7151264808095129265L;

    /**
     * 促销活动编号
     */
    private String pref;

    /**
     * 活动方式
     */
    private Byte promotionMode;

    /**
     * 活动方式中文
     */
    private String promotionModeStr;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 活动规则
     */
    private String rulesInfo = "";

    /**
     * 活动商品
     */
    private String goodsInfo = "";

    /**
     * 名额限制, 限6位数字
     */
    private Integer numberLimit;

    /**
     * 时间限制: 0:活动期间，1: 每天
     */
    private Byte dayLimit;

    private String dayLimitStr = "";

    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    public Byte getPromotionMode() {
        return promotionMode;
    }

    public void setPromotionMode(Byte promotionMode) {
        this.promotionMode = promotionMode;
    }

    public String getPromotionModeStr() {
        return promotionModeStr;
    }

    public void setPromotionModeStr(String promotionModeStr) {
        this.promotionModeStr = promotionModeStr;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getRulesInfo() {
        return rulesInfo;
    }

    public void setRulesInfo(String rulesInfo) {
        this.rulesInfo = rulesInfo;
    }

    public String getGoodsInfo() {
        return goodsInfo;
    }

    public void setGoodsInfo(String goodsInfo) {
        this.goodsInfo = goodsInfo;
    }

    public Integer getNumberLimit() {
        return numberLimit;
    }

    public void setNumberLimit(Integer numberLimit) {
        this.numberLimit = numberLimit;
    }

    public Byte getDayLimit() {
        return dayLimit;
    }

    public void setDayLimit(Byte dayLimit) {
        this.dayLimit = dayLimit;
    }

    public String getDayLimitStr() {
        return dayLimitStr;
    }

    public void setDayLimitStr(String dayLimitStr) {
        this.dayLimitStr = dayLimitStr;
    }
}
