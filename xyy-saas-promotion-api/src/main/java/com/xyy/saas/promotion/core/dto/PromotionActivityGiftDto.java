package com.xyy.saas.promotion.core.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class PromotionActivityGiftDto implements Serializable {
    private static final long serialVersionUID = -2408734412727361744L;

    private Long id;

    /**
     * 促销活动编号
     */
    private String promotionPref;

    /**
     * 赠品编号
     */
    private String giftPref;

    /**
     * 赠品数量
     */
    private BigDecimal giftNum;

    /**
     * 赠品限量
     */
    private Integer giftLimit;

    /**
     * 加价购限量（前端根据字段校验，故新增一个字段）
     */
    private Integer purchaseLimit;

    /**
     * 限量剩余
     */
    private Integer remainLimit;

    /**
     * 逻辑删除 1 有效 0 删除
     */
    private Byte yn;

    /**
     * 药店唯一标识
     */
    private String organSign;

    /**
     * 操作版本号
     */
    private Long baseVersion;

    /**
     * 创建人
     */
    private Integer createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private Integer updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 赠品标准库id
     */
    private Long giftStandardLibraryId;

    /**
     * 排序字段
     */
    private Integer ladderId;

    public Long getId() {
        return id;
    }
    public void setId(Long id) {
        this.id = id;
    }

    public String getPromotionPref() {
        return promotionPref;
    }
    public void setPromotionPref(String promotionPref) {
        this.promotionPref = promotionPref;
    }

    public String getGiftPref() {
        return giftPref;
    }
    public void setGiftPref(String giftPref) {
        this.giftPref = giftPref;
    }

    public BigDecimal getGiftNum() {
        return giftNum;
    }
    public void setGiftNum(BigDecimal giftNum) {
        this.giftNum = giftNum;
    }

    public Integer getGiftLimit() {
        return giftLimit;
    }
    public void setGiftLimit(Integer giftLimit) {
        this.giftLimit = giftLimit;
    }

    public Integer getRemainLimit() {
        return remainLimit;
    }
    public void setRemainLimit(Integer remainLimit) {
        this.remainLimit = remainLimit;
    }

    public Byte getYn() {
        return yn;
    }
    public void setYn(Byte yn) {
        this.yn = yn;
    }

    public String getOrganSign() {
        return organSign;
    }
    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public Long getBaseVersion() {
        return baseVersion;
    }
    public void setBaseVersion(Long baseVersion) {
        this.baseVersion = baseVersion;
    }

    public Integer getCreateUser() {
        return createUser;
    }
    public void setCreateUser(Integer createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdateUser() {
        return updateUser;
    }
    public void setUpdateUser(Integer updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getGiftStandardLibraryId() {
        return giftStandardLibraryId;
    }

    public void setGiftStandardLibraryId(Long giftStandardLibraryId) {
        this.giftStandardLibraryId = giftStandardLibraryId;
    }

    public Integer getPurchaseLimit() {
        return purchaseLimit;
    }

    public void setPurchaseLimit(Integer purchaseLimit) {
        this.purchaseLimit = purchaseLimit;
    }

    public Integer getLadderId() {
        return ladderId;
    }

    public void setLadderId(Integer ladderId) {
        this.ladderId = ladderId;
    }
}
