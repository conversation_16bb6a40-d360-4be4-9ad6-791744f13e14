package com.xyy.saas.promotion.core.api;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.promotion.core.dto.*;
import java.util.List;
import com.xyy.saas.member.core.dto.MemberBaseDto;

/**
 * 优惠券模板api
 */
public interface CouponTemplateApi {

    /**
     * 新增
     * @param couponTemplateDto
     * @return
     */
    int insertCouponTemplate(CouponTemplateDto couponTemplateDto);

    /**
     * 更新审核驳回的活动
     * @param couponTemplateDto
     * @return
     */
    int updateCouponTemplate(CouponTemplateDto couponTemplateDto);

    /**
     * 分页查询
     * @param dto
     * @return
     */
    PageInfo<CouponTemplateDto> selectCouponTemplateList(CouponTemplateDto dto);

    /**
     * 通过id查询
     * @param dto
     * @return
     */
    CouponTemplateDto selectById(CouponTemplateDto dto);

    /**
     * 修改优惠券模板状态
     */
    int updateStatus(CouponTemplateDto couponTemplateDto);

    /**
     * 更新库存
     * @param couponTemplateDto
     * @return
     */
    int updateResidue(CouponTemplateDto couponTemplateDto);

    /**
     * 查看优惠卷模板报表
     * @param couponTemplateDto
     * @return
     */
    StatisticalCouponTemplateDto selectStatisticalCouponTemplate(CouponTemplateDto couponTemplateDto);

    /**
     * 优惠券模板统计
     * @param couponTemplateDto
     * @return
     */
    StatTemplateDto selectStatTemplate(CouponTemplateDto couponTemplateDto);

    /**
     * 发券明细列表查询
     * @param dto
     * @return
     */
    PageInfo<CouponDetailDto> couponDetailList(CouponDetailReqDto dto);

    /**
     * 通过id软删除
     * @param dto
     * @return
     */
    boolean deleteById(CouponTemplateDto dto);

    /**
     * 查询各状态的优惠券活动数量
     * @param dto
     * @return
     */
    List<StatCouponCountDto> getCouponTemplateStatusCount(CouponTemplateDto dto);

    /**
     * 前端页面是否显示毛利率
     * @param organSign
     * @return
     */
    boolean isShowCouponRateGross(String organSign);

    /**
     * 优惠活动是否开启了发送短信开关
     * @param templateDto
     * @return
     */
    boolean isCouponTemplateOpenSendMsg(CouponTemplateDto templateDto);

    PageInfo<PromotionProductInfoDto> selectProductListByTaskId(CouponSelectListParamDto dto);

    PageInfo<MemberBaseDto> selectMemberListByTaskId(CouponSelectListParamDto dto);

    Boolean getCouponApolloConfig(String configKey, String organSign);

    PageInfo<PromotionProductInfoDto> selectCouponProductList(CouponSelectListParamDto dto);

    PageInfo<MemberBaseDto> selectCouponMemberList(CouponSelectListParamDto dto);

    String getImportTaskId(String organSign, String model,String operId);
}