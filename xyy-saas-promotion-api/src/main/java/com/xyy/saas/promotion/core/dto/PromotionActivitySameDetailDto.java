package com.xyy.saas.promotion.core.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * 商品重复的促销活动实体
 * <AUTHOR>
 */
public class PromotionActivitySameDetailDto implements Serializable {
    private static final long serialVersionUID = -1936056123260652892L;

    /**
     * 促销活动Id
     */
    private Long id;

    /**
     * 促销活动编号
     */
    private String promotionPref;

    /**
     * 活动名称
     */
    private String promotionName;

    /**
     * 活动方式名称
     */
    private String promotionModeName;

    /**
     * 活动状态: 1:已结束; 2:未开始，3:已暂停,4:进行中，5:待审核  7:审核驳回
     */
    private Byte status;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 商品编号
     */
    private String productPref;

    /**
     * 通用名称
     */
    private String commonName;

    /**
     * 参与商品数量
     */
    private Long productNum;

    /**
     * 商品内码
     */
    private String pref;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPromotionPref() {
        return promotionPref;
    }

    public void setPromotionPref(String promotionPref) {
        this.promotionPref = promotionPref;
    }

    public String getPromotionName() {
        return promotionName;
    }

    public void setPromotionName(String promotionName) {
        this.promotionName = promotionName;
    }

    public String getPromotionModeName() {
        return promotionModeName;
    }

    public void setPromotionModeName(String promotionModeName) {
        this.promotionModeName = promotionModeName;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getProductPref() {
        return productPref;
    }

    public void setProductPref(String productPref) {
        this.productPref = productPref;
    }

    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    public Long getProductNum() {
        return productNum;
    }

    public void setProductNum(Long productNum) {
        this.productNum = productNum;
    }

    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }
}
