package com.xyy.saas.promotion.core.dto;

import java.io.Serializable;
import java.math.BigDecimal;

public class PromotionActivityDetailBufferItemDto implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 促销价
     */
    private BigDecimal promotionPrice;

    /**
     * 商品编号
     */
    private String productPref;

    /**
     * 商品编号
     */
    private String commonName;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品外码
     */
    private String pharmacyPref;

    /**
     * 商品外码
     */
    private Integer special;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public BigDecimal getPromotionPrice() {
        return promotionPrice;
    }

    public void setPromotionPrice(BigDecimal promotionPrice) {
        this.promotionPrice = promotionPrice;
    }

    public String getProductPref() {
        return productPref;
    }

    public void setProductPref(String productPref) {
        this.productPref = productPref;
    }

    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getPharmacyPref() {
        return pharmacyPref;
    }

    public void setPharmacyPref(String pharmacyPref) {
        this.pharmacyPref = pharmacyPref;
    }

    public Integer getSpecial() {
        return special;
    }

    public void setSpecial(Integer special) {
        this.special = special;
    }
}
