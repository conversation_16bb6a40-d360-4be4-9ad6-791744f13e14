package com.xyy.saas.promotion.core.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 促销阶梯
 */
public class PromotionActivityLadderDto implements Serializable {

    private static final long serialVersionUID = 9010691458073981082L;

    private Long id;

    /**
     * 促销活动编号
     */
    private String promotionPref;

    /**
     * 购买金额
     */
    private BigDecimal buyFullAmount;

    /**
     * 购买件数
     */
    private BigDecimal buyFullNumber;

    /**
     * 折扣
     */
    private BigDecimal discountRate;

    /**
     * 赠品数量
     */
    private BigDecimal giftNumber;

    /**
     * 排序字段
     */
    private Integer idx;

    /**
     * 立减金额
     */
    private BigDecimal reduceAmount;

    /**
     * 操作版本号
     */
    private Long baseVersion;

    /**
     * 创建人
     */
    private Integer createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private Integer updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 逻辑删除 1 有效 0 删除
     */
    private Byte yn;

    /**
     * 药店唯一标识
     */
    private String organSign;

    /**
     * 购买金额(加价购,由于前端根据字段校验，故需要重新添加字段)
     */
    private BigDecimal purchaseAmount;

    /**
     * 购买件数(加价购)
     */
    private BigDecimal purchaseNumber;

    /**
     * 加价金额
     */
    private BigDecimal addPurchaseAmount;

    /**
     * 累计限量(组合促销传值)
     */
    private  Integer ladderTotalLimit;

    /**
     * 日限量(组合促销传值)
     */
    private  Integer ladderDailyLimit;

    /**
     * 阶梯累计限量剩余[组合促销]
     */
    private Integer remainLadderTotal;

    /**
     * 阶梯日限量剩余[组合促销]
     */
    private Integer remainLadderDaily;

    public BigDecimal getBuyFullAmount() {
        return buyFullAmount;
    }

    public void setBuyFullAmount(BigDecimal buyFullAmount) {
        this.buyFullAmount = buyFullAmount;
    }

    public BigDecimal getBuyFullNumber() {
        return buyFullNumber;
    }

    public void setBuyFullNumber(BigDecimal buyFullNumber) {
        this.buyFullNumber = buyFullNumber;
    }

    public BigDecimal getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }

    public BigDecimal getGiftNumber() {
        return giftNumber;
    }

    public void setGiftNumber(BigDecimal giftNumber) {
        this.giftNumber = giftNumber;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getIdx() {
        return idx;
    }

    public void setIdx(Integer idx) {
        this.idx = idx;
    }

    public BigDecimal getReduceAmount() {
        return reduceAmount;
    }

    public void setReduceAmount(BigDecimal reduceAmount) {
        this.reduceAmount = reduceAmount;
    }

    public Long getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(Long baseVersion) {
        this.baseVersion = baseVersion;
    }

    public Integer getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Integer createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Integer updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Byte getYn() {
        return yn;
    }

    public void setYn(Byte yn) {
        this.yn = yn;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public String getPromotionPref() {
        return promotionPref;
    }

    public void setPromotionPref(String promotionPref) {
        this.promotionPref = promotionPref;
    }

    public BigDecimal getPurchaseAmount() {
        return purchaseAmount;
    }

    public void setPurchaseAmount(BigDecimal purchaseAmount) {
        this.purchaseAmount = purchaseAmount;
    }

    public BigDecimal getPurchaseNumber() {
        return purchaseNumber;
    }

    public void setPurchaseNumber(BigDecimal purchaseNumber) {
        this.purchaseNumber = purchaseNumber;
    }

    public BigDecimal getAddPurchaseAmount() {
        return addPurchaseAmount;
    }

    public void setAddPurchaseAmount(BigDecimal addPurchaseAmount) {
        this.addPurchaseAmount = addPurchaseAmount;
    }

    public Integer getLadderTotalLimit() {
        return ladderTotalLimit;
    }

    public void setLadderTotalLimit(Integer ladderTotalLimit) {
        this.ladderTotalLimit = ladderTotalLimit;
    }

    public Integer getLadderDailyLimit() {
        return ladderDailyLimit;
    }

    public void setLadderDailyLimit(Integer ladderDailyLimit) {
        this.ladderDailyLimit = ladderDailyLimit;
    }

    public Integer getRemainLadderTotal() {
        return remainLadderTotal;
    }

    public void setRemainLadderTotal(Integer remainLadderTotal) {
        this.remainLadderTotal = remainLadderTotal;
    }

    public Integer getRemainLadderDaily() {
        return remainLadderDaily;
    }

    public void setRemainLadderDaily(Integer remainLadderDaily) {
        this.remainLadderDaily = remainLadderDaily;
    }
}
