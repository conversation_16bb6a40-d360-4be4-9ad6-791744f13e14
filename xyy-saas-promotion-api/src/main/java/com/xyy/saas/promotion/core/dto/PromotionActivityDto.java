package com.xyy.saas.promotion.core.dto;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.promotion.core.common.PageVo;

import java.util.Date;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Title:  PromotionActivityDTO 对象
 * @Description:
 * @date:   2019-09-10 16:43 
 * @version V1.0.0
 */
public class PromotionActivityDto extends PageVo implements Serializable {


	private static final long serialVersionUID = 8089716041116842878L;
	private Long id;
	   
	/**
	 * 促销活动编号
	 */
	private String pref;

	/**
	 * 促销父活动编号
	 */
	private String parentPref;
	   
	/**
	 * 活动名称
	 */
	private String promotionName;
	   
	/**
	 * 活动类型
	 */
	private Byte promotionType;
	   
	/**
	 * 活动方式
	 */
	private Byte promotionMode;
	   
	/**
	 * 活动状态: 1:已结束; 2:未开始，3:已暂停,4:进行中，5:待审核  7:审核驳回
	 */
	private Byte status;

	/**
	 * 开始时间
	 */
	private Date startDate;

	/**
	 * 结束时间
	 */
	private Date endDate;

	/**
	 * 药店唯一标识
	 */
	private String organSign;
	   
	/**
	 * 数据同步版本号
	 */
	private Long baseVersion;
	   
	/**
	 * 创建人
	 */
	private Integer createUser;
	   
	/**
	 * 创建时间
	 */
	private Date createTime;
	   
	/**
	 * 修改人
	 */
	private Integer updateUser;
	   
	/**
	 * 修改时间
	 */
	private Date updateTime;
	   
	/**
	 * 逻辑删除 1 有效 0 删除
	 */
	private Byte yn;

	/**
	 * 汇总报表统计
	 */
	Map<String,Object> orderdetail;

	/**
	 * 门店报表统计
	 */
	PageInfo<Map<String,Object>> storeDetail;

	private Long num; //商品数量

	private String promotionTypeName;

	private String promotionModeName;

	private String remarks;

	//用于查询排序
	private String sortParams;

	/**
	 * 适用门店
	 */
	private String targetStore;

	/**
	 * 1 门店自建 2 总店派发
	 */
	private Byte createType;

	/**
	 * 适用门店列表
	 */
	private List<String> targetStoreList;

	private List<Byte> statusList;

	/**
	 * 商品信息
	 */
	private String productQuery;

	//会员每日限量
	private Integer memberDailyLimit;
	//会员活动期间限量
	private Integer memberActivityLimit;

	/**
	 * 商品类型 0:门店创建  1:总部创建-指定商品 2:总部创建-自主商品
	 */
	private Byte productMode;

	/**
	 * 是否隐藏门店
	 */
	private Byte isDrugstoreHidden;

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public String getPromotionTypeName() {
		return promotionTypeName;
	}

	public void setPromotionTypeName(String promotionTypeName) {
		this.promotionTypeName = promotionTypeName;
	}

	public String getPromotionModeName() {
		return promotionModeName;
	}

	public void setPromotionModeName(String promotionModeName) {
		this.promotionModeName = promotionModeName;
	}

	public Map<String, Object> getOrderdetail() {
		return orderdetail;
	}

	public void setOrderdetail(Map<String, Object> orderdetail) {
		this.orderdetail = orderdetail;
	}

	public Long getNum() {
		return num;
	}

	public void setNum(Long num) {
		this.num = num;
	}

	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	
	public String getPref() {
		return pref;
	}
	public void setPref(String pref) {
		this.pref = pref;
	}
	
	public String getPromotionName() {
		return promotionName;
	}
	public void setPromotionName(String promotionName) {
		this.promotionName = promotionName;
	}
	
	public Byte getPromotionType() {
		return promotionType;
	}
	public void setPromotionType(Byte promotionType) {
		this.promotionType = promotionType;
	}
	
	public Byte getPromotionMode() {
		return promotionMode;
	}
	public void setPromotionMode(Byte promotionMode) {
		this.promotionMode = promotionMode;
	}
	
	public Byte getStatus() {
		return status;
	}
	public void setStatus(Byte status) {
		this.status = status;
	}
	
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	
	public String getOrganSign() {
		return organSign;
	}
	public void setOrganSign(String organSign) {
		this.organSign = organSign;
	}
	
	public Long getBaseVersion() {
		return baseVersion;
	}
	public void setBaseVersion(Long baseVersion) {
		this.baseVersion = baseVersion;
	}
	
	public Integer getCreateUser() {
		return createUser;
	}
	public void setCreateUser(Integer createUser) {
		this.createUser = createUser;
	}
	
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
	public Integer getUpdateUser() {
		return updateUser;
	}
	public void setUpdateUser(Integer updateUser) {
		this.updateUser = updateUser;
	}
	
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	
	public Byte getYn() {
		return yn;
	}
	public void setYn(Byte yn) {
		this.yn = yn;
	}

	public String getSortParams() {
		return sortParams;
	}

	public void setSortParams(String sortParams) {
		this.sortParams = sortParams;
	}

	public String getTargetStore() {
		return targetStore;
	}

	public void setTargetStore(String targetStore) {
		this.targetStore = targetStore;
	}

	public Byte getCreateType() {
		return createType;
	}

	public void setCreateType(Byte createType) {
		this.createType = createType;
	}

	public String getParentPref() {
		return parentPref;
	}

	public void setParentPref(String parentPref) {
		this.parentPref = parentPref;
	}

	public List<String> getTargetStoreList() {
		return targetStoreList;
	}

	public void setTargetStoreList(List<String> targetStoreList) {
		this.targetStoreList = targetStoreList;
	}

	public List<Byte> getStatusList() {
		return statusList;
	}

	public void setStatusList(List<Byte> statusList) {
		this.statusList = statusList;
	}

	public PageInfo<Map<String, Object>> getStoreDetail() {
		return storeDetail;
	}

	public void setStoreDetail(PageInfo<Map<String, Object>> storeDetail) {
		this.storeDetail = storeDetail;
	}

	public String getProductQuery() {
		return productQuery;
	}

	public void setProductQuery(String productQuery) {
		this.productQuery = productQuery;
	}

	public Integer getMemberDailyLimit() {
		return memberDailyLimit;
	}

	public void setMemberDailyLimit(Integer memberDailyLimit) {
		this.memberDailyLimit = memberDailyLimit;
	}

	public Integer getMemberActivityLimit() {
		return memberActivityLimit;
	}

	public void setMemberActivityLimit(Integer memberActivityLimit) {
		this.memberActivityLimit = memberActivityLimit;
	}

	public Byte getProductMode() {
		return productMode;
	}

	public void setProductMode(Byte productMode) {
		this.productMode = productMode;
	}

	public Byte getIsDrugstoreHidden() {
		return isDrugstoreHidden;
	}

	public void setIsDrugstoreHidden(Byte isDrugstoreHidden) {
		this.isDrugstoreHidden = isDrugstoreHidden;
	}
}