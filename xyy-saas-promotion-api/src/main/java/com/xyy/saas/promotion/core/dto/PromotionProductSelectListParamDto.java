package com.xyy.saas.promotion.core.dto;

import java.math.BigDecimal;
import java.util.List;

/**
 * 促销明细查询参数
 */
public class PromotionProductSelectListParamDto implements java.io.Serializable{

    private static final long serialVersionUID = 2835398271711898364L;
    /**
     * 1:排除特价商品
     */
    private Byte isSpecial;
    /**
     * 混合查询字段(通用名称,商品名称,助记码)
     */
    private String mixedQuery;

    /**
     * 商品7大类,填写分类id
     */
    private Integer systemTypeId;

    /**
     * 自定义分类,填写分类id
     */
    private Integer customTypeId;

    /**
     * abc分类,填写分类id
     */
    private Integer abcTypeId;

    /**
     * 货位分类,填写货位id(暂无)
     */
    private Integer positionId;

    /**
     * 最小毛利率
     */
    private BigDecimal minGrossMargin;

    /**
     * 最大毛利率
     */
    private BigDecimal maxGrossMargin;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 排除商品列表
     */
    private List<String> exclusivePrefs;

    /**
     * 包含商品列表
     */
    private List<String> includePrefs;

    /**
     * 机构唯一标识
     */
    private String organSign;

    /**
     * 每页多少条
     */
    private Integer pageSize;

    /**
     * 当前多少页
     */
    private Integer pageNum;

    /**
     * 1：不展示库存为零的商品， 0：展示库存为零的商品
     */
    private Byte stockNumberYn;

    /**
     * 1：不展示已参加活动的商品， 0：展示参加活动的商品
     */
    private Byte activityProductYn;

    //1：不展示禁用商品， 0：展示禁用商品
    private Byte usedYn;

    /**
     * 经营模式 1、单体 2、连锁 3、联营
     */
    private Byte bizModel;

    /**
     * 总部机构号
     */
    private String headquartersOrganSign;

    /**
     * 机构类型 1、门店  3、总部
     */
    private Byte organSignType;

    //是否排除商品编号(赠品查询不需要排除)
    private Boolean checkPref;

    /**
     * 标准库id
     */
    private Long standardLibraryId;

    //批准文号
    private String approvalNumber;

    /**
     * 通过excel创建活动时 导入excel至电驴生成的taskId
     */
    private  Long taskId;

    /**
     * 活动方式
     */
    private Integer promotionMode;

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Byte getIsSpecial() {
        return isSpecial;
    }

    public void setIsSpecial(Byte isSpecial) {
        this.isSpecial = isSpecial;
    }

    public String getMixedQuery() {
        return mixedQuery;
    }

    public void setMixedQuery(String mixedQuery) {
        this.mixedQuery = mixedQuery;
    }

    public Integer getSystemTypeId() {
        return systemTypeId;
    }

    public void setSystemTypeId(Integer systemTypeId) {
        this.systemTypeId = systemTypeId;
    }

    public Integer getCustomTypeId() {
        return customTypeId;
    }

    public void setCustomTypeId(Integer customTypeId) {
        this.customTypeId = customTypeId;
    }

    public Integer getAbcTypeId() {
        return abcTypeId;
    }

    public void setAbcTypeId(Integer abcTypeId) {
        this.abcTypeId = abcTypeId;
    }

    public Integer getPositionId() {
        return positionId;
    }

    public void setPositionId(Integer positionId) {
        this.positionId = positionId;
    }

    public BigDecimal getMinGrossMargin() {
        return minGrossMargin;
    }

    public void setMinGrossMargin(BigDecimal minGrossMargin) {
        this.minGrossMargin = minGrossMargin;
    }

    public BigDecimal getMaxGrossMargin() {
        return maxGrossMargin;
    }

    public void setMaxGrossMargin(BigDecimal maxGrossMargin) {
        this.maxGrossMargin = maxGrossMargin;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public List<String> getExclusivePrefs() {
        return exclusivePrefs;
    }

    public void setExclusivePrefs(List<String> exclusivePrefs) {
        this.exclusivePrefs = exclusivePrefs;
    }

    public List<String> getIncludePrefs() {
        return includePrefs;
    }

    public void setIncludePrefs(List<String> includePrefs) {
        this.includePrefs = includePrefs;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Byte getStockNumberYn() {
        return stockNumberYn;
    }

    public void setStockNumberYn(Byte stockNumberYn) {
        this.stockNumberYn = stockNumberYn;
    }

    public Byte getActivityProductYn() {
        return activityProductYn;
    }

    public void setActivityProductYn(Byte activityProductYn) {
        this.activityProductYn = activityProductYn;
    }

    public Byte getUsedYn() {
        return usedYn;
    }

    public void setUsedYn(Byte usedYn) {
        this.usedYn = usedYn;
    }

    public Byte getBizModel() {
        return bizModel;
    }

    public void setBizModel(Byte bizModel) {
        this.bizModel = bizModel;
    }

    public String getHeadquartersOrganSign() {
        return headquartersOrganSign;
    }

    public void setHeadquartersOrganSign(String headquartersOrganSign) {
        this.headquartersOrganSign = headquartersOrganSign;
    }

    public Byte getOrganSignType() {
        return organSignType;
    }

    public void setOrganSignType(Byte organSignType) {
        this.organSignType = organSignType;
    }

    public Boolean getCheckPref() {
        return checkPref;
    }

    public void setCheckPref(Boolean checkPref) {
        this.checkPref = checkPref;
    }

    public Long getStandardLibraryId() {
        return standardLibraryId;
    }

    public void setStandardLibraryId(Long standardLibraryId) {
        this.standardLibraryId = standardLibraryId;
    }

    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    public Integer getPromotionMode() {
        return promotionMode;
    }

    public void setPromotionMode(Integer promotionMode) {
        this.promotionMode = promotionMode;
    }
}
