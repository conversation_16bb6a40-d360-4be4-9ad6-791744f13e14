package com.xyy.saas.promotion.core.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 优惠券模板
 */
public class CouponTemplateDto implements Serializable {

    private static final long serialVersionUID = -3418868460945047401L;

    private Long id;

    /**
     * 模板编号
     */
    private String pref;
    /**
     * 券模式: 1下单返券, 2生日关怀, 3会员券, 4新手券
     */
    private Byte couponModel;

    /**
     * 活动名称
     */
    private String couponName;

    /**
     * 券类型: 1代金券, 2折扣券
     */
    private Byte couponType;

    /**
     * 折扣，1-100
     */
    private BigDecimal discountRate;

    /**
     * 减免金额
     */
    private BigDecimal remissionAmount;

    /**
     * 毛利率
     */
    private BigDecimal rateGross;

    /**
     * 起用券最低消费金额
     */
    private BigDecimal minMoneyToEnable;

    /**
     * 发券后几天生效,0代表当日生效
     */
    private Integer effectDelayDays;

    /**
     * 有效期(天)
     */
    private Integer validityDays;

    /**
     * 发放人群，会员等级 0:全部会员
     */
    private String targetUser;

    /**
     * 会员渠道 0全部渠道, 1:管理端注册 2:POS端注册 3: 小智会员注册
     */
    private String memberSource;

    /**
     * 总计发券
     */
    private Integer couponCount;

    /**
     * 发券限制: 0:总计发券量, 1:每个门店总数量
     */
    private Byte giveType;

    /**
     * 已发放数量
     */
    private Integer issuedCount;

    /**
     * 券剩余
     */
    private Integer couponResidue;

    /**
     * 每人限领,0代表无限制
     */
    private Integer receiveLimit;

    /**
     * 生日前几日发券
     */
    private Integer beforeBrithday;

    /**
     * 状态: 1进行中, 2暂停中, 3已结束, 4待审核, 5审核驳回
     */
    private Byte status;

    /**
     * 逻辑删除 1 有效 0 删除
     */
    private Byte yn;

    /**
     * 药店唯一标识
     */
    private String organSign;

    /**
     * 适用门店
     */
    private String targetOrganSign;

    /**
     * 发券方：1: 门店，2:总部
     */
    private Byte createType;

    /**
     * 操作版本号
     */
    private Long baseVersion;

    /**
     * 创建人
     */
    private Integer createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private Integer updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 截止时间
     */
    private Date endDate;

    private Integer pageNum;

    private Integer pageSize;

    /**
     * 父机构唯一标识
     */
    private String headquartersOrganSign;
    /**
     * organSignType 1门店 3总部
     */
    private Byte organSignType;
    /**
     * 经营模式 1、单体 2、连锁 3、联营
     */
    private Byte bizModel;

    /**
     * 发放条件
     */
    private BigDecimal minMoneyToGive;

    /**
     * 总部回显适用门店
     */
    private List<TargetStoreVo> targetStoreList;

    /**
     * 回显适用人群
     */
    private List<TargetMemberVo> targetMemberLevelList;

    /**
     * 1 全部门店 2部分门店
     */
    private Integer targetStoreType;

    /**
     * 商品限制：1:特价商品除外，2:不参与积分, 3:按零售价参与促销
     */
    private String goodsLimit;
    /**
     * 适用商品：1、全部商品 2、部分商品
     */
    private Byte couponProduct;
    /**
     * 优惠券支持商品编号列表
     */
    private List<String> productPrefList;

    /**
     * 优惠券支持会员卡号
     */
    private List<String> cartNoList;

    /**
     * 是否隐藏门店
     */
    private Byte isDrugstoreHidden;

    public String getGoodsLimit() {
        return goodsLimit;
    }

    public void setGoodsLimit(String goodsLimit) {
        this.goodsLimit = goodsLimit;
    }

    public Byte getCouponProduct() {
        return couponProduct;
    }

    public void setCouponProduct(Byte couponProduct) {
        this.couponProduct = couponProduct;
    }

    public List<String> getCartNoList() {
        return cartNoList;
    }

    public void setCartNoList(List<String> cartNoList) {
        this.cartNoList = cartNoList;
    }

    public List<String> getProductPrefList() {
        return productPrefList;
    }

    public void setProductPrefList(List<String> productPrefList) {
        this.productPrefList = productPrefList;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    public Byte getCouponModel() {
        return couponModel;
    }

    public void setCouponModel(Byte couponModel) {
        this.couponModel = couponModel;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }

    public Byte getCouponType() {
        return couponType;
    }

    public BigDecimal getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }

    public BigDecimal getRemissionAmount() {
        return remissionAmount;
    }

    public void setRemissionAmount(BigDecimal remissionAmount) {
        this.remissionAmount = remissionAmount;
    }

    public BigDecimal getMinMoneyToEnable() {
        return minMoneyToEnable;
    }

    public void setMinMoneyToEnable(BigDecimal minMoneyToEnable) {
        this.minMoneyToEnable = minMoneyToEnable;
    }

    public Integer getEffectDelayDays() {
        return effectDelayDays;
    }

    public void setEffectDelayDays(Integer effectDelayDays) {
        this.effectDelayDays = effectDelayDays;
    }

    public Integer getValidityDays() {
        return validityDays;
    }

    public void setValidityDays(Integer validityDays) {
        this.validityDays = validityDays;
    }

    public Integer getCouponCount() {
        return couponCount;
    }

    public void setCouponCount(Integer couponCount) {
        this.couponCount = couponCount;
    }

    public Integer getIssuedCount() {
        return issuedCount;
    }

    public void setIssuedCount(Integer issuedCount) {
        this.issuedCount = issuedCount;
    }

    public Integer getCouponResidue() {
        return couponResidue;
    }

    public void setCouponResidue(Integer couponResidue) {
        this.couponResidue = couponResidue;
    }

    public Integer getReceiveLimit() {
        return receiveLimit;
    }

    public void setReceiveLimit(Integer receiveLimit) {
        this.receiveLimit = receiveLimit;
    }

    public Byte getYn() {
        return yn;
    }

    public void setYn(Byte yn) {
        this.yn = yn;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public BigDecimal getRateGross() {
        return rateGross;
    }

    public void setRateGross(BigDecimal rateGross) {
        this.rateGross = rateGross;
    }

    public String getTargetUser() {
        return targetUser;
    }

    public void setTargetUser(String targetUser) {
        this.targetUser = targetUser;
    }

    public String getMemberSource() {
        return memberSource;
    }

    public void setMemberSource(String memberSource) {
        this.memberSource = memberSource;
    }

    public Byte getGiveType() {
        return giveType;
    }

    public void setGiveType(Byte giveType) {
        this.giveType = giveType;
    }

    public String getTargetOrganSign() {
        return targetOrganSign;
    }

    public void setTargetOrganSign(String targetOrganSign) {
        this.targetOrganSign = targetOrganSign;
    }

    public Byte getCreateType() {
        return createType;
    }

    public void setCreateType(Byte createType) {
        this.createType = createType;
    }

    public Long getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(Long baseVersion) {
        this.baseVersion = baseVersion;
    }

    public Integer getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Integer createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Integer updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public void setCouponType(Byte couponType) {
        this.couponType = couponType;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getBeforeBrithday() {
        return beforeBrithday;
    }

    public void setBeforeBrithday(Integer beforeBrithday) {
        this.beforeBrithday = beforeBrithday;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getHeadquartersOrganSign() {
        return headquartersOrganSign;
    }

    public void setHeadquartersOrganSign(String headquartersOrganSign) {
        this.headquartersOrganSign = headquartersOrganSign;
    }

    public Byte getOrganSignType() {
        return organSignType;
    }

    public void setOrganSignType(Byte organSignType) {
        this.organSignType = organSignType;
    }

    public BigDecimal getMinMoneyToGive() {
        return minMoneyToGive;
    }

    public void setMinMoneyToGive(BigDecimal minMoneyToGive) {
        this.minMoneyToGive = minMoneyToGive;
    }

    public List<TargetStoreVo> getTargetStoreList() {
        return targetStoreList;
    }

    public void setTargetStoreList(List<TargetStoreVo> targetStoreList) {
        this.targetStoreList = targetStoreList;
    }

    public Byte getBizModel() {
        return bizModel;
    }

    public void setBizModel(Byte bizModel) {
        this.bizModel = bizModel;
    }

    public Integer getTargetStoreType() {
        return targetStoreType;
    }

    public void setTargetStoreType(Integer targetStoreType) {
        this.targetStoreType = targetStoreType;
    }

    public List<TargetMemberVo> getTargetMemberLevelList() {
        return targetMemberLevelList;
    }

    public void setTargetMemberLevelList(List<TargetMemberVo> targetMemberLevelList) {
        this.targetMemberLevelList = targetMemberLevelList;
    }

    public Byte getIsDrugstoreHidden() {
        return isDrugstoreHidden;
    }

    public void setIsDrugstoreHidden(Byte isDrugstoreHidden) {
        this.isDrugstoreHidden = isDrugstoreHidden;
    }

}
