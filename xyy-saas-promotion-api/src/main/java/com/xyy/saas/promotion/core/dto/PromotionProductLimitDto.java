package com.xyy.saas.promotion.core.dto;

import javax.xml.crypto.Data;
import java.io.Serializable;
import java.util.Date;

public class PromotionProductLimitDto implements Serializable {
    private static final long serialVersionUID = 4694154667918010715L;

    private String memberGuid;

    private String organSign;

    private Integer remainDaily;

    private Integer remainTotal;

    private String promotionPref;

    private String productPref;

    private Date createTime;

    private Date updateTime;

    public String getMemberGuid() {
        return memberGuid;
    }

    public void setMemberGuid(String memberGuid) {
        this.memberGuid = memberGuid;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public Integer getRemainDaily() {
        return remainDaily;
    }

    public void setRemainDaily(Integer remainDaily) {
        this.remainDaily = remainDaily;
    }

    public Integer getRemainTotal() {
        return remainTotal;
    }

    public void setRemainTotal(Integer remainTotal) {
        this.remainTotal = remainTotal;
    }

    public String getPromotionPref() {
        return promotionPref;
    }

    public void setPromotionPref(String promotionPref) {
        this.promotionPref = promotionPref;
    }

    public String getProductPref() {
        return productPref;
    }

    public void setProductPref(String productPref) {
        this.productPref = productPref;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
