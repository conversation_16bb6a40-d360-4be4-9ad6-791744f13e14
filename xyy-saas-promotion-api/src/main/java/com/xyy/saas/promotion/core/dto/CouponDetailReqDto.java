package com.xyy.saas.promotion.core.dto;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 优惠明细查询Dto
 * <AUTHOR>
 * @Create 2020-11-09 17:37
 */
public class CouponDetailReqDto implements Serializable {

    private static final long serialVersionUID = -5698782246398721346L;

    /**
     * 会员信息
     */
    private String mixedQuery;
    /**
     * 券模式: 1下单返券, 2生日关怀, 3会员券, 4新手券
     */
    private Byte couponModel; //发放方式
    /**
     * 优惠券状态 1未使用, 2已使用, 3已过期,
     */
    private Byte status;
    /**
     *发券机构 （门店传）-1: 全部，0: 门店，1:总部 总部传机构号
     */
    private String giveOrganSign; //发券方
    /**
     * 适用门店 "":全部门店（仅总部需要）
     */
    private String targetOrganSign;

    private Integer pageNum; //页码

    private Integer pageSize; //每页多少条数据

    /**
     * 机构唯一标识
     */
    private String organSign;
    /**
     * 父机构唯一标识
     */
    private String headquartersOrganSign;
    /**
     * organSignType 1门店 3总部
     */
    private Byte organSignType;
    /**
     * 经营模式 1、单体 2、连锁 3、联营
     */
    private Byte bizModel;

    /**
     * giveOrganSignList 全部机构列表
     */
    private List<String> giveOrganSignList;

    private Byte CouponType;

    /**
     * 是否隐藏门店
     */
    private Byte isDrugstoreHidden;

    public String getMixedQuery() {
        return mixedQuery;
    }

    public void setMixedQuery(String mixedQuery) {
        this.mixedQuery = mixedQuery;
    }

    public Byte getCouponModel() {
        return couponModel;
    }

    public void setCouponModel(Byte couponModel) {
        this.couponModel = couponModel;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getGiveOrganSign() {
        return giveOrganSign;
    }

    public void setGiveOrganSign(String giveOrganSign) {
        this.giveOrganSign = giveOrganSign;
    }

    public String getTargetOrganSign() {
        return targetOrganSign;
    }

    public void setTargetOrganSign(String targetOrganSign) {
        this.targetOrganSign = targetOrganSign;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public String getHeadquartersOrganSign() {
        return headquartersOrganSign;
    }

    public void setHeadquartersOrganSign(String headquartersOrganSign) {
        this.headquartersOrganSign = headquartersOrganSign;
    }

    public Byte getOrganSignType() {
        return organSignType;
    }

    public void setOrganSignType(Byte organSignType) {
        this.organSignType = organSignType;
    }

    public Byte getBizModel() {
        return bizModel;
    }

    public void setBizModel(Byte bizModel) {
        this.bizModel = bizModel;
    }

    public List<String> getGiveOrganSignList() {
        return giveOrganSignList;
    }

    public void setGiveOrganSignList(List<String> giveOrganSignList) {
        this.giveOrganSignList = giveOrganSignList;
    }

    public Byte getCouponType() {
        return CouponType;
    }

    public void setCouponType(Byte couponType) {
        CouponType = couponType;
    }

    public Byte getIsDrugstoreHidden() {
        return isDrugstoreHidden;
    }

    public void setIsDrugstoreHidden(Byte isDrugstoreHidden) {
        this.isDrugstoreHidden = isDrugstoreHidden;
    }
}
