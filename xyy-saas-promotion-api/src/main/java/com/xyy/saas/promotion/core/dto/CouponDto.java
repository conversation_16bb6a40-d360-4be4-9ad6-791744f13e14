package com.xyy.saas.promotion.core.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 优惠券
 */
public class CouponDto implements Serializable {

    private static final long serialVersionUID = 781626375859530276L;

    private Long id;

    /**
     * 会员guid
     */
    private String memberGuid;

    private List<String> memberGuidList;

    /**
     * 券模板编号
     */
    private String templatePref;

    /**
     * 券模式: 1下单返券, 2生日关怀
     */
    private Byte couponModel;

    /**
     * 活动名称
     */
    private String couponName;

    /**
     * 券类型: 1代金券, 2折扣券
     */
    private Byte couponType;

    /**
     * 折扣，1-100
     */
    private BigDecimal discountRate;

    /**
     * 减免金额
     */
    private BigDecimal remissionAmount;

    /**
     * 使用条件 最低消费金额
     */
    private BigDecimal minMoneyToEnable;

    /**
     * 发放条件 消费最低金额
     */
    private BigDecimal minMoneyToGive;

    private Date validityDate;

    /**
     * 有效期起始
     */
    private Date validityStartDate;

    /**
     * 有效期终止
     */
    private Date validityEndDate;

    /**
     * 发券关联的订单号
     */
    private String fromTicketNo;

    /**
     * 使用券关联的订单号
     */
    private String usedTicketNo;

    /**
     * 状态: 1未使用, 2已使用, 3已过期, 4未启用(未发送短信)
     */
    private Byte status;

    /**
     * 券的使用时间
     */
    private Date usedTime;

    /**
     * 逻辑删除 1 有效 0 删除
     */
    private Byte yn;

    /**
     * 药店唯一标识
     */
    private String organSign;

    /**
     * 操作版本号
     */
    private Long baseVersion;

    /**
     * 创建人
     */
    private Integer createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private Integer updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 券的使用时间 开始
     */
    private Date usedTimeStart;

    /**
     * 券的使用时间 结束
     */
    private Date usedTimeEnd;

    private int pageNum = 1;

    private int pageSize = 10;

    /**
     * 毛利率
     */
    private BigDecimal rateGross;
    /**
     * 冗余会员信息
     */
    private String memberBase;

    /**
     * 会员信息混合查询
     */
    private String mixedQuery;
    /**
     * 适用机构
     */
    private String targetOrganSign;

    private List<String> prefList;

    /**
     * 商品限制：1:特价商品除外，2:不参与积分, 3:按零售价参与促销
     */
    private String goodsLimit;
    /**
     * 适用商品：1、全部商品 2、部分商品
     */
    private Byte couponProduct;

    /**
     * 优惠券支持商品编号列表
     */
    private List<String> productPrefList;

    public List<String> getProductPrefList() {
        return productPrefList;
    }

    public void setProductPrefList(List<String> productPrefList) {
        this.productPrefList = productPrefList;
    }

    public String getGoodsLimit() {
        return goodsLimit;
    }

    public void setGoodsLimit(String goodsLimit) {
        this.goodsLimit = goodsLimit;
    }

    public Byte getCouponProduct() {
        return couponProduct;
    }

    public void setCouponProduct(Byte couponProduct) {
        this.couponProduct = couponProduct;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMemberGuid() {
        return memberGuid;
    }

    public void setMemberGuid(String memberGuid) {
        this.memberGuid = memberGuid;
    }

    public String getTemplatePref() {
        return templatePref;
    }

    public void setTemplatePref(String templatePref) {
        this.templatePref = templatePref;
    }

    public Byte getCouponModel() {
        return couponModel;
    }

    public void setCouponModel(Byte couponModel) {
        this.couponModel = couponModel;
    }

    public String getCouponName() {
        return couponName;
    }

    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }

    public Byte getCouponType() {
        return couponType;
    }

    public void setCouponType(Byte couponType) {
        this.couponType = couponType;
    }

    public BigDecimal getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }

    public BigDecimal getRemissionAmount() {
        return remissionAmount;
    }

    public void setRemissionAmount(BigDecimal remissionAmount) {
        this.remissionAmount = remissionAmount;
    }

    public BigDecimal getMinMoneyToEnable() {
        return minMoneyToEnable;
    }

    public void setMinMoneyToEnable(BigDecimal minMoneyToEnable) {
        this.minMoneyToEnable = minMoneyToEnable;
    }

    public BigDecimal getMinMoneyToGive() {
        return minMoneyToGive;
    }

    public void setMinMoneyToGive(BigDecimal minMoneyToGive) {
        this.minMoneyToGive = minMoneyToGive;
    }

    public Date getValidityStartDate() {
        return validityStartDate;
    }

    public void setValidityStartDate(Date validityStartDate) {
        this.validityStartDate = validityStartDate;
    }

    public Date getValidityEndDate() {
        return validityEndDate;
    }

    public void setValidityEndDate(Date validityEndDate) {
        this.validityEndDate = validityEndDate;
    }

    public String getFromTicketNo() {
        return fromTicketNo;
    }

    public void setFromTicketNo(String fromTicketNo) {
        this.fromTicketNo = fromTicketNo;
    }

    public String getUsedTicketNo() {
        return usedTicketNo;
    }

    public void setUsedTicketNo(String usedTicketNo) {
        this.usedTicketNo = usedTicketNo;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Date getUsedTime() {
        return usedTime;
    }

    public void setUsedTime(Date usedTime) {
        this.usedTime = usedTime;
    }

    public Byte getYn() {
        return yn;
    }

    public void setYn(Byte yn) {
        this.yn = yn;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public Long getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(Long baseVersion) {
        this.baseVersion = baseVersion;
    }

    public Integer getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Integer createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Integer updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getValidityDate() {
        return validityDate;
    }

    public void setValidityDate(Date validityDate) {
        this.validityDate = validityDate;
    }

    public Date getUsedTimeStart() {
        return usedTimeStart;
    }

    public void setUsedTimeStart(Date usedTimeStart) {
        this.usedTimeStart = usedTimeStart;
    }

    public Date getUsedTimeEnd() {
        return usedTimeEnd;
    }

    public void setUsedTimeEnd(Date usedTimeEnd) {
        this.usedTimeEnd = usedTimeEnd;
    }

    public List<String> getMemberGuidList() {
        return memberGuidList;
    }

    public void setMemberGuidList(List<String> memberGuidList) {
        this.memberGuidList = memberGuidList;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public BigDecimal getRateGross() {
        return rateGross;
    }

    public void setRateGross(BigDecimal rateGross) {
        this.rateGross = rateGross;
    }

    public String getMemberBase() {
        return memberBase;
    }

    public void setMemberBase(String memberBase) {
        this.memberBase = memberBase;
    }

    public String getMixedQuery() {
        return mixedQuery;
    }

    public void setMixedQuery(String mixedQuery) {
        this.mixedQuery = mixedQuery;
    }

    public String getTargetOrganSign() {
        return targetOrganSign;
    }

    public void setTargetOrganSign(String targetOrganSign) {
        this.targetOrganSign = targetOrganSign;
    }

    public List<String> getPrefList() {
        return prefList;
    }

    public void setPrefList(List<String> prefList) {
        this.prefList = prefList;
    }
}
