package com.xyy.saas.promotion.core.api;


import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.datasync.api.DataSyncApi;
import com.xyy.saas.promotion.core.dto.*;

import java.util.List;

/**
 * @Title:  PromotionActivityGiftApi
 * @Description: 促销赠品接口
 * @date:   2019-09-10 16:43 
 * @version V1.0.0
 */
public interface PromotionActivityGiftApi extends DataSyncApi<PromotionActivityGiftDto> {

    /**
     * 查询赠品详情
     * @param dto
     * @return
     */
    PageInfo<PromotionGiftInfoDto> selectDetailList(PromotionGiftSelectListParamDto dto);

    /**
     * 查询赠品详情(分阶梯，与selectDetailList逻辑基本一致，只是最后进行分组，等逻辑统一后则可以废弃上面方法)
     * @param dto
     * @return
     */
    List<PromotionGiftInfoLadderListDto> selectDetailLadderList(PromotionGiftSelectListParamDto dto);
}