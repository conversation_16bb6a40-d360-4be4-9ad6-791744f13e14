package com.xyy.saas.promotion.core.api;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.promotion.core.dto.CouponDto;
import com.xyy.saas.promotion.core.dto.DataFlushDto;
import com.xyy.saas.promotion.core.dto.StatCouponDetailDto;
import com.xyy.saas.promotion.core.dto.StatCouponDto;

import java.util.List;
import java.util.Map;

/**
 * 优惠券
 */
public interface CouponApi {

    /**
     * 查询优惠券
     * @param couponDto
     * @return
     */
    List<CouponDto> selectCouponList(CouponDto couponDto);

    PageInfo<CouponDto> selectCouponListPage(CouponDto couponDto);

    List<CouponDto> selectCanUseCouponList(CouponDto couponDto);

    /**
     * 使用优惠券
     * @param couponDto
     * @return
     */
    int useCoupon(CouponDto couponDto);

    /**
     * 释放优惠券
     * @param couponDto
     * @return
     */
    boolean unUseCoupon(CouponDto couponDto);

    /**
     * 锁定优惠券
     * @param couponDto
     * @return
     */
    boolean lockCoupon(CouponDto couponDto);


    /**
     * 解锁优惠券
     * @param couponDto
     * @return
     */
    boolean unLockCoupon(CouponDto couponDto);

    /**
     * 根据id查询优惠券
     * @param dtoParam
     * @return
     */
    CouponDto selectCouponById(CouponDto dtoParam);


    /**
     * 统计优惠券信息
     * @param couponDto
     * @return
     */
    StatCouponDto selectStatCoupon(CouponDto couponDto);

    /**
     * 会员可用优惠券
     * @param memberGuidList
     * @return
     */
    Map<String, Integer> memberCouponCount(List<String> memberGuidList);

    /**
     * 查询优惠券的数量统计和模板信息
     * @param couponDto
     * @return
     */
    StatCouponDetailDto getCouponDetailStat(CouponDto couponDto, PageInfo pageInfo);

    /**
     * 刷新优惠券表会员基本信息
     * @param dataFlushDto
     * @return
     */
    String flushMemberBaseData(DataFlushDto dataFlushDto);
}
