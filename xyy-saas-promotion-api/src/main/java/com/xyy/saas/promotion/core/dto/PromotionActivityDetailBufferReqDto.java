package com.xyy.saas.promotion.core.dto;

import java.io.Serializable;
import java.util.List;


public class PromotionActivityDetailBufferReqDto implements Serializable {
    /**
     * 促销活动编号
     */
    private String selectKey;

    /**
     * 促销活动编号
     */
    private String promotionPref;

    /**
     * 商品编号
     */
    private List<PromotionActivityDetailBufferItemDto> promotionProductInfos;

    /**
     * 药店唯一标识
     */
    private String organSign;

    /**
     * 操作人
     */
    private String operationUser;

    /**
     * 操作时间
     */
    private String operationTime;

    /**
     * 操作类型： 1=add,2=修改,3=复制
     */
    private Integer operationType;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 页容量
     */
    private Integer pageSize;

    /**
     * 促销模式
     */
    private Integer promotionMode;

    /**
     * 查询类别 1编辑新增查看 2详情查看
     */
    private Integer queryType;

    /**
     * 查询类别 1编辑新增查看 2详情查看
     */
    private List<String> productPrefList;

    /**
     * 特价商品除外
     */
    private Integer isSpecial;

    public String getPromotionPref() {
        return promotionPref;
    }

    public void setPromotionPref(String promotionPref) {
        this.promotionPref = promotionPref;
    }

    public List<PromotionActivityDetailBufferItemDto> getPromotionProductInfos() {
        return promotionProductInfos;
    }

    public void setPromotionProductInfos(List<PromotionActivityDetailBufferItemDto> promotionProductInfos) {
        this.promotionProductInfos = promotionProductInfos;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public String getOperationUser() {
        return operationUser;
    }

    public void setOperationUser(String operationUser) {
        this.operationUser = operationUser;
    }

    public String getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(String operationTime) {
        this.operationTime = operationTime;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }


    public String getSelectKey() {
        return selectKey;
    }

    public void setSelectKey(String selectKey) {
        this.selectKey = selectKey;
    }

    public Integer getPromotionMode() {
        return promotionMode;
    }

    public void setPromotionMode(Integer promotionMode) {
        this.promotionMode = promotionMode;
    }

    public Integer getQueryType() {
        return queryType;
    }

    public void setQueryType(Integer queryType) {
        this.queryType = queryType;
    }

    public List<String> getProductPrefList() {
        return productPrefList;
    }

    public void setProductPrefList(List<String> productPrefList) {
        this.productPrefList = productPrefList;
    }

    public Integer getIsSpecial() {
        return isSpecial;
    }

    public void setIsSpecial(Integer isSpecial) {
        this.isSpecial = isSpecial;
    }
}
