package com.xyy.saas.promotion.core.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class PromotionGiftInfoDto implements Serializable {
    private static final long serialVersionUID = -5831867891839581768L;

    //商品编号,外码,用作展示
    private String pharmacyPref;
    //通用名称
    private String commonName;
    //规格型号
    private String attributeSpecification;
    //单位名称
    private String unitName;
    //单位id
    private Integer unitId;
    //剂型id
    private Integer dosageFormId;
    //剂型名称
    private String dosageFormName;
    //生产厂家
    private String manufacturer;
    //商品分类id
    private Integer systemTypeId;
    //商品分类名称
    private String systemTypeName;
    //自定义分类id
    private Integer customTypeId;
    //自定义分类名称
    private String customTypeName;
    //abc类型id
    private Integer abcTypeId;
    //abc类型名称
    private String abcTypeName;
    //库存数量
    private BigDecimal stockNumber;
    //成本价
    private BigDecimal costPrice;
    //零售价
    private BigDecimal retailPrice;
    //vip会员价
    private BigDecimal vipPrice;
    //毛利率
    private BigDecimal grossMargin;
    //产地
    private String producingArea;

    private Long id;

    /**
     * 促销活动编号
     */
    private String promotionPref;

    /**
     * 赠品编号
     */
    private String giftPref;

    /**
     * 商品编号 同赠品编号一样,满足前端要求一致
     */
    private String productPref;

    /**
     * 赠品数量
     */
    private BigDecimal giftNum;

    /**
     * 赠品限量
     */
    private Integer giftLimit;

    /**
     * 限量剩余
     */
    private Integer remainLimit;

    /**
     * 逻辑删除 1 有效 0 删除
     */
    private Byte yn;

    /**
     * 药店唯一标识
     */
    private String organSign;

    /**
     * 操作版本号
     */
    private Long baseVersion;

    /**
     * 创建人
     */
    private Integer createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private Integer updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 赠品标准库id
     */
    private Long giftStandardLibraryId;

    //价格来源
    private String priceFromOrganSignName;
    //批准文号
    private String approvalNumber;
    //条形码
    private String barCode;

    //取值于促销阶梯的排序
    private  Integer ladderId;

    private   Integer purchaseLimit;

    public String getPharmacyPref() {
        return pharmacyPref;
    }

    public void setPharmacyPref(String pharmacyPref) {
        this.pharmacyPref = pharmacyPref;
    }

    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    public String getAttributeSpecification() {
        return attributeSpecification;
    }

    public void setAttributeSpecification(String attributeSpecification) {
        this.attributeSpecification = attributeSpecification;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getDosageFormId() {
        return dosageFormId;
    }

    public void setDosageFormId(Integer dosageFormId) {
        this.dosageFormId = dosageFormId;
    }

    public String getDosageFormName() {
        return dosageFormName;
    }

    public void setDosageFormName(String dosageFormName) {
        this.dosageFormName = dosageFormName;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public Integer getSystemTypeId() {
        return systemTypeId;
    }

    public void setSystemTypeId(Integer systemTypeId) {
        this.systemTypeId = systemTypeId;
    }

    public String getSystemTypeName() {
        return systemTypeName;
    }

    public void setSystemTypeName(String systemTypeName) {
        this.systemTypeName = systemTypeName;
    }

    public Integer getCustomTypeId() {
        return customTypeId;
    }

    public void setCustomTypeId(Integer customTypeId) {
        this.customTypeId = customTypeId;
    }

    public String getCustomTypeName() {
        return customTypeName;
    }

    public void setCustomTypeName(String customTypeName) {
        this.customTypeName = customTypeName;
    }

    public Integer getAbcTypeId() {
        return abcTypeId;
    }

    public void setAbcTypeId(Integer abcTypeId) {
        this.abcTypeId = abcTypeId;
    }

    public String getAbcTypeName() {
        return abcTypeName;
    }

    public void setAbcTypeName(String abcTypeName) {
        this.abcTypeName = abcTypeName;
    }

    public BigDecimal getStockNumber() {
        return stockNumber;
    }

    public void setStockNumber(BigDecimal stockNumber) {
        this.stockNumber = stockNumber;
    }

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public BigDecimal getRetailPrice() {
        return retailPrice;
    }

    public void setRetailPrice(BigDecimal retailPrice) {
        this.retailPrice = retailPrice;
    }

    public BigDecimal getVipPrice() {
        return vipPrice;
    }

    public void setVipPrice(BigDecimal vipPrice) {
        this.vipPrice = vipPrice;
    }

    public BigDecimal getGrossMargin() {
        return grossMargin;
    }

    public void setGrossMargin(BigDecimal grossMargin) {
        this.grossMargin = grossMargin;
    }

    public String getProducingArea() {
        return producingArea;
    }

    public void setProducingArea(String producingArea) {
        this.producingArea = producingArea;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPromotionPref() {
        return promotionPref;
    }

    public void setPromotionPref(String promotionPref) {
        this.promotionPref = promotionPref;
    }

    public String getGiftPref() {
        return giftPref;
    }

    public void setGiftPref(String giftPref) {
        this.giftPref = giftPref;
    }

    public BigDecimal getGiftNum() {
        return giftNum;
    }

    public void setGiftNum(BigDecimal giftNum) {
        this.giftNum = giftNum;
    }

    public Integer getGiftLimit() {
        return giftLimit;
    }

    public void setGiftLimit(Integer giftLimit) {
        this.giftLimit = giftLimit;
    }

    public Integer getRemainLimit() {
        return remainLimit;
    }

    public void setRemainLimit(Integer remainLimit) {
        this.remainLimit = remainLimit;
    }

    public Byte getYn() {
        return yn;
    }

    public void setYn(Byte yn) {
        this.yn = yn;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public Long getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(Long baseVersion) {
        this.baseVersion = baseVersion;
    }

    public Integer getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Integer createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Integer updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getProductPref() {
        return productPref;
    }

    public void setProductPref(String productPref) {
        this.productPref = productPref;
    }

    public Long getGiftStandardLibraryId() {
        return giftStandardLibraryId;
    }

    public void setGiftStandardLibraryId(Long giftStandardLibraryId) {
        this.giftStandardLibraryId = giftStandardLibraryId;
    }

    public String getPriceFromOrganSignName() {
        return priceFromOrganSignName;
    }

    public void setPriceFromOrganSignName(String priceFromOrganSignName) {
        this.priceFromOrganSignName = priceFromOrganSignName;
    }

    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public Integer getLadderId() {
        return ladderId;
    }

    public void setLadderId(Integer ladderId) {
        this.ladderId = ladderId;
    }

    public Integer getPurchaseLimit() {
        return purchaseLimit;
    }

    public void setPurchaseLimit(Integer purchaseLimit) {
        this.purchaseLimit = purchaseLimit;
    }
}
