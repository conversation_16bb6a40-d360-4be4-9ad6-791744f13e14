package com.xyy.saas.promotion.core.api;

import com.xyy.saas.promotion.core.dto.PromotionActivityDuplicateProductsDto;
import com.xyy.saas.promotion.core.dto.PromotionActivityInfoDto;
import com.xyy.saas.promotion.core.dto.PromotionDetailInfoDto;

import java.util.List;

/**
 * 功能描述 促销活动特殊处理api
 *
 * <AUTHOR>
 * @Date 2021/6/10 14:30
 * @Email <EMAIL>
 */
public interface PromotionActivitySpecialHandleApi {

    /**
     * 移除促销活动进行中的商品信息
     * @param promotionActivityInfoDtos
     * @param organSign
     * @return
     */
    PromotionActivityDuplicateProductsDto removeDuplicateProductsFromPromotionActivity(List<PromotionDetailInfoDto> promotionActivityInfoDtos, String organSign,Integer promotionMode);
}
