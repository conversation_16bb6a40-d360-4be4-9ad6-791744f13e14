package com.xyy.saas.promotion.core.dto;

import java.io.Serializable;
import java.util.List;

public class PromotionActivityDetailBufferRespDto implements Serializable {
    //重复的商品
    private List<PromotionActivityDetailBufferItemDto> repeatDetail;

    //添加数量
    private Integer addNum;

    //促销编号
    private String promotionPref;

    public List<PromotionActivityDetailBufferItemDto> getRepeatDetail() {
        return repeatDetail;
    }

    public void setRepeatDetail(List<PromotionActivityDetailBufferItemDto> repeatDetail) {
        this.repeatDetail = repeatDetail;
    }

    public Integer getAddNum() {
        return addNum;
    }

    public void setAddNum(Integer addNum) {
        this.addNum = addNum;
    }

    public String getPromotionPref() {
        return promotionPref;
    }

    public void setPromotionPref(String promotionPref) {
        this.promotionPref = promotionPref;
    }
}
