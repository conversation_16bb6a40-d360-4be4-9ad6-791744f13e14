package com.xyy.saas.promotion.core.api;

import java.util.List;
import java.util.Map;

import com.xyy.saas.ads.api.zhl.DeviceInfo;
import com.xyy.saas.ads.api.zhl.ReportInfo;
import com.xyy.saas.ads.api.zhl.ReportInfos;
import com.xyy.saas.common.util.ResultVO;

/**
 * @Title:  AdvertisingQueryApi
 * @Description: 广告接口
 * @date:   2019-09-22 14:09 
 * @version V1.0.0
 */
public interface AdvertisingQueryApi {

    /**
     * 广告查询
     * @param deviceInfo
     */
	ResultVO queryAdvertising(DeviceInfo deviceInfo);
	
	/**
	 * 广告播放结果上报
	 * @param reportInfo
	 */
	ResultVO report(ReportInfos reportInfos);
	
	/**
     * 广告发布推送MQ
     * @param organSigns
     * @return
     */
    Map<String, Object> sendAdvertising2MQ(List<String> organSigns);

    
}