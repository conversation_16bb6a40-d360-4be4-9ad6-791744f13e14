package com.xyy.saas.promotion.core.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 优惠券报表-下方门店项
 */
public class StatisticalCouponTemplateItemDto implements Serializable {

    private static final long serialVersionUID = 9169261018795577642L;

    /**
     * 门店机构号
     */
    private String organSign;
    /**
     * 门店名称
     */
    private String organSignName;
    /**
     * 累计发放优惠券
     */
    private Integer couponGrantTotal;
    /**
     * 累计发放会员数
     */
    private Integer couponGrantMemberTotal;
    /**
     * 剩余优惠券数
     */
    private Integer couponResidueTotal;
    /**
     * 累计成交订单数
     */
    private Long orderNum;
    /**
     * 活动累计成交金额
     */
    private BigDecimal actualAmount;

    /**
     * 活动累计优惠金额
     */
    private BigDecimal couponAmount;

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public String getOrganSignName() {
        return organSignName;
    }

    public void setOrganSignName(String organSignName) {
        this.organSignName = organSignName;
    }

    public Integer getCouponGrantTotal() {
        return couponGrantTotal;
    }

    public void setCouponGrantTotal(Integer couponGrantTotal) {
        this.couponGrantTotal = couponGrantTotal;
    }

    public Integer getCouponGrantMemberTotal() {
        return couponGrantMemberTotal;
    }

    public void setCouponGrantMemberTotal(Integer couponGrantMemberTotal) {
        this.couponGrantMemberTotal = couponGrantMemberTotal;
    }

    public Integer getCouponResidueTotal() {
        return couponResidueTotal;
    }

    public void setCouponResidueTotal(Integer couponResidueTotal) {
        this.couponResidueTotal = couponResidueTotal;
    }

    public Long getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Long orderNum) {
        this.orderNum = orderNum;
    }

    public BigDecimal getActualAmount() {
        return actualAmount;
    }

    public void setActualAmount(BigDecimal actualAmount) {
        this.actualAmount = actualAmount;
    }

    public BigDecimal getCouponAmount() {
        return couponAmount;
    }

    public void setCouponAmount(BigDecimal couponAmount) {
        this.couponAmount = couponAmount;
    }
}
