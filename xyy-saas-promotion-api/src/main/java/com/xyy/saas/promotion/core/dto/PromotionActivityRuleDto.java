package com.xyy.saas.promotion.core.dto;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.List;

/**
 * @Title:  PromotionActivityRuleDTO 对象
 * @Description: 促销规则对象
 * @date:   2019-09-10 16:43 
 * @version V1.0.0
 */
public class PromotionActivityRuleDto implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;
	   
	/**
	 * 促销活动编号
	 */
	private String promotionPref;
	   
	/**
	 * 适用对象: 0: 全部用户，-1:非会员用户
	 */
	private String targetUser;
	   
	/**
	 * 商品限制：1:特价商品除外，2:不参与积分, 3:按零售价参与促销
	 */
	private String goodsLimit;
	   
	/**
	 * 促销日约束: 1: 周，2: 月
	 */
	private Byte promotionDayWay;
	   
	/**
	 * 指定具体促销日，1-31之间的一个或者多个以逗号分隔组成的数字串
	 */
	private String promotionDay;
	   
	/**
	 * 名额限制, 限6位数字
	 */
	private Integer numberLimit;
	   
	/**
	 * 剩余名额
	 */
	private Integer remainNumber;
	   
	/**
	 * 时间限制: 0:活动期间，1: 每天
	 */
	private Byte dayLimit;
	   
	/**
	 * 折扣，1-100
	 */
	private BigDecimal discountRate;
	   
	/**
	 * 促销价
	 */
	private BigDecimal promotionPrice;
	   
	/**
	 * 买满金额
	 */
	private BigDecimal buyFullAmount;
	   
	/**
	 * 买满数量
	 */
	private BigDecimal buyFullNumber;
	   
	/**
	 * 赠品数量
	 */
	private BigDecimal giftNumber;
	   
	/**
	 * 立减金额
	 */
	private BigDecimal reduceAmount;
	   
	/**
	 * 每单限量
	 */
	private Integer orderLimit;
	   
	/**
	 * 每天限量
	 */
	private Integer dailyLimit;
	   
	/**
	 * 累计限量
	 */
	private Integer totalLimit;
	   
	/**
	 * 会员折扣是否生效: 0:否 1:是
	 */
	private Byte memberDiscountYn;
	   
	/**
	 * 备注
	 */
	private String remark;
	   
	/**
	 * 药店唯一标识
	 */
	private String organSign;
	   
	/**
	 * 数据同步版本号
	 */
	private Long baseVersion;

	/**
	 * 参与方式
	 * 1:直赠 2:倍数赠
	 */
	private Integer inWay;

	/**
	 * 买的倍数
	 */
	private BigDecimal buyMultiple;

	/**
	 * 商品模式 0:单品 1:多品
	 */
	private Byte buyFullMode;
	/**
	 *  赠品模式 0:指定 1:买赠
	 */
	private Byte giftMode;

	private List<TargetStoreVo> targetStoreList;

	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	
	public String getPromotionPref() {
		return promotionPref;
	}
	public void setPromotionPref(String promotionPref) {
		this.promotionPref = promotionPref;
	}
	
	public String getTargetUser() {
		return targetUser;
	}
	public void setTargetUser(String targetUser) {
		this.targetUser = targetUser;
	}
	
	public String getGoodsLimit() {
		return goodsLimit;
	}
	public void setGoodsLimit(String goodsLimit) {
		this.goodsLimit = goodsLimit;
	}
	
	public Byte getPromotionDayWay() {
		return promotionDayWay;
	}
	public void setPromotionDayWay(Byte promotionDayWay) {
		this.promotionDayWay = promotionDayWay;
	}
	
	public String getPromotionDay() {
		return promotionDay;
	}
	public void setPromotionDay(String promotionDay) {
		this.promotionDay = promotionDay;
	}
	
	public Integer getNumberLimit() {
		return numberLimit;
	}
	public void setNumberLimit(Integer numberLimit) {
		this.numberLimit = numberLimit;
	}
	
	public Integer getRemainNumber() {
		return remainNumber;
	}
	public void setRemainNumber(Integer remainNumber) {
		this.remainNumber = remainNumber;
	}
	
	public Byte getDayLimit() {
		return dayLimit;
	}
	public void setDayLimit(Byte dayLimit) {
		this.dayLimit = dayLimit;
	}
	
	public BigDecimal getDiscountRate() {
		return discountRate;
	}
	public void setDiscountRate(BigDecimal discountRate) {
		this.discountRate = discountRate;
	}
	
	public BigDecimal getPromotionPrice() {
		return promotionPrice;
	}
	public void setPromotionPrice(BigDecimal promotionPrice) {
		this.promotionPrice = promotionPrice;
	}
	
	public BigDecimal getBuyFullAmount() {
		return buyFullAmount;
	}
	public void setBuyFullAmount(BigDecimal buyFullAmount) {
		this.buyFullAmount = buyFullAmount;
	}
	
	public BigDecimal getBuyFullNumber() {
		return buyFullNumber;
	}
	public void setBuyFullNumber(BigDecimal buyFullNumber) {
		this.buyFullNumber = buyFullNumber;
	}
	
	public BigDecimal getGiftNumber() {
		return giftNumber;
	}
	public void setGiftNumber(BigDecimal giftNumber) {
		this.giftNumber = giftNumber;
	}
	
	public BigDecimal getReduceAmount() {
		return reduceAmount;
	}
	public void setReduceAmount(BigDecimal reduceAmount) {
		this.reduceAmount = reduceAmount;
	}
	
	public Integer getOrderLimit() {
		return orderLimit;
	}
	public void setOrderLimit(Integer orderLimit) {
		this.orderLimit = orderLimit;
	}
	
	public Integer getDailyLimit() {
		return dailyLimit;
	}
	public void setDailyLimit(Integer dailyLimit) {
		this.dailyLimit = dailyLimit;
	}
	
	public Integer getTotalLimit() {
		return totalLimit;
	}
	public void setTotalLimit(Integer totalLimit) {
		this.totalLimit = totalLimit;
	}
	
	public Byte getMemberDiscountYn() {
		return memberDiscountYn;
	}
	public void setMemberDiscountYn(Byte memberDiscountYn) {
		this.memberDiscountYn = memberDiscountYn;
	}
	
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	
	public String getOrganSign() {
		return organSign;
	}
	public void setOrganSign(String organSign) {
		this.organSign = organSign;
	}
	
	public Long getBaseVersion() {
		return baseVersion;
	}
	public void setBaseVersion(Long baseVersion) {
		this.baseVersion = baseVersion;
	}

	public Integer getInWay() {
		return inWay;
	}

	public void setInWay(Integer inWay) {
		this.inWay = inWay;
	}

	public BigDecimal getBuyMultiple() {
		return buyMultiple;
	}

	public void setBuyMultiple(BigDecimal buyMultiple) {
		this.buyMultiple = buyMultiple;
	}

	public Byte getBuyFullMode() {
		return buyFullMode;
	}

	public void setBuyFullMode(Byte buyFullMode) {
		this.buyFullMode = buyFullMode;
	}

	public Byte getGiftMode() {
		return giftMode;
	}

	public void setGiftMode(Byte giftMode) {
		this.giftMode = giftMode;
	}

	public List<TargetStoreVo> getTargetStoreList() {
		return targetStoreList;
	}

	public void setTargetStoreList(List<TargetStoreVo> targetStoreList) {
		this.targetStoreList = targetStoreList;
	}
}