package com.xyy.saas.promotion.core.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 新增/编辑促销活动实体
 * <AUTHOR>
 */
public class PromotionActivitySaveDto implements Serializable {

    private static final long serialVersionUID = -1936056123260508531L;
    /**
     * 促销活动主键id
     */
    private  Long id;

    /**
     * 促销活动编号
     */
    private String pref;

    /**
     * 活动名称
     */
    private String promotionName;

    /**
     * 活动类型
     */
    private Byte promotionType;

    /**
     * 活动方式
     */
    private Byte promotionMode;

    /**
     * 活动状态: 1:已结束; 2:未开始，3:已暂停,4:进行中，
     */
    private Byte status;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 药店唯一标识
     */
    private String organSign;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 逻辑删除 1 有效 0 删除
     */
    private Byte yn;

    /**
     * 适用对象: 0: 全部用户，-1:非会员用户
     */
    private String targetUser;

    /**
     * 商品限制：1:特价商品除外，2:不参与积分, 3:按零售价参与促销
     */
    private String goodsLimit;

    /**
     * 促销日约束: 1: 周，2: 月
     */
    private Byte promotionDayWay;

    /**
     * 指定具体促销日，1-31之间的一个或者多个以逗号分隔组成的数字串
     */
    private String promotionDay;

    /**
     * 名额限制, 限6位数字
     */
    private Integer numberLimit;

    /**
     * 时间限制: 0:活动期间，1: 每天
     */
    private Byte dayLimit;

    /**
     * 折扣，1-100
     */
    private BigDecimal discountRate;

    /**
     * 促销价
     */
    private BigDecimal promotionPrice;

    /**
     * 买满金额
     */
    private BigDecimal buyFullAmount;

    /**
     * 买满数量
     */
    private BigDecimal buyFullNumber;

    /**
     * 赠品数量
     */
    private BigDecimal giftNumber;

    /**
     * 立减金额
     */
    private BigDecimal reduceAmount;

    /**
     * 每单限量
     */
    private Integer orderLimit;

    /**
     * 每天限量
     */
    private Integer dailyLimit;

    /**
     * 累计限量
     */
    private Integer totalLimit;

    /**
     * 会员折扣是否生效: 0:否 1:是
     */
    private Byte memberDiscountYn;

    /**
     * 商品明细
     */
    List<String> productPrefList;

    /**
     * 赠品编号
     */
    private String giftPref;

    /**
     * 赠品限量
     */
    private Integer giftLimit;

    /**
     * 参与方式 1:直赠, 2:倍数赠
     */
    private Integer inWay;

    /**
     * 买的倍数
     */
    private BigDecimal buyMultiple;

    /**
     * 商品模式 0:单品 1:多品
     */
    private Byte buyFullMode;
    /**
     *  赠品模式 0:指定 1:买赠
     */
    private Byte giftMode;

    /**
     * 赠品明细
     */
    private List<PromotionActivityGiftDto> giftDtoList;

    /**
     * 加价购商品明细(需要与促销阶梯顺序一一对应)
     */
    private  List<PromotionActivityPurchaseListDto> purchaseProductList;

    /**
     * 促销阶梯
     */
    private List<PromotionActivityLadderDto> ladderList;

    /**
     * 适用门店
     */
    private List<String> targetStoreList;

    /**
     * 1 全部门店 2部分门店
     */
    private Byte targetStoreType;

	//会员每日限量
    private Integer memberDailyLimit;
    //会员活动期间限量
    private Integer memberActivityLimit;

    /**
     * 商品价格明细(与商品明细一一对应)
     */
    List<BigDecimal> productPrefPriceList;

    /**
     * 商品类型 0:指定商品 1:自主商品
     */
    private Byte productMode;


    private List<String> organSignList;

    /**
     * 商品阶梯信息（组合促销专用）
     */
    private  List<PromotionCombinedProductListDto> promotionCombinedProductLists;

    /**
     * 是否全部选中 1是 2否
     */
    private Integer allSelect;

    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    public String getPromotionName() {
        return promotionName;
    }

    public void setPromotionName(String promotionName) {
        this.promotionName = promotionName;
    }

    public Byte getPromotionType() {
        return promotionType;
    }

    public void setPromotionType(Byte promotionType) {
        this.promotionType = promotionType;
    }

    public Byte getPromotionMode() {
        return promotionMode;
    }

    public void setPromotionMode(Byte promotionMode) {
        this.promotionMode = promotionMode;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Byte getYn() {
        return yn;
    }

    public void setYn(Byte yn) {
        this.yn = yn;
    }

    public List<String> getProductPrefList() {
        return productPrefList;
    }

    public void setProductPrefList(List<String> productPrefList) {
        this.productPrefList = productPrefList;
    }

    public String getGiftPref() {
        return giftPref;
    }

    public void setGiftPref(String giftPref) {
        this.giftPref = giftPref;
    }

    public Integer getGiftLimit() {
        return giftLimit;
    }

    public void setGiftLimit(Integer giftLimit) {
        this.giftLimit = giftLimit;
    }

    public List<PromotionActivityLadderDto> getLadderList() {
        return ladderList;
    }

    public void setLadderList(List<PromotionActivityLadderDto> ladderList) {
        this.ladderList = ladderList;
    }


    public List<String> getTargetStoreList() {
        return targetStoreList;
    }

    public void setTargetStoreList(List<String> targetStoreList) {
        this.targetStoreList = targetStoreList;
    }

    public Byte getTargetStoreType() {
        return targetStoreType;
    }

    public void setTargetStoreType(Byte targetStoreType) {
        this.targetStoreType = targetStoreType;
    }

    public Integer getMemberDailyLimit() {
        return memberDailyLimit;
    }

    public void setMemberDailyLimit(Integer memberDailyLimit) {
        this.memberDailyLimit = memberDailyLimit;
    }

    public Integer getMemberActivityLimit() {
        return memberActivityLimit;
    }

    public void setMemberActivityLimit(Integer memberActivityLimit) {
        this.memberActivityLimit = memberActivityLimit;
    }

    public List<BigDecimal> getProductPrefPriceList() {
        return productPrefPriceList;
    }

    public void setProductPrefPriceList(List<BigDecimal> productPrefPriceList) {
        this.productPrefPriceList = productPrefPriceList;
    }

	public Byte getProductMode() {
        return productMode;
    }

    public void setProductMode(Byte productMode) {
        this.productMode = productMode;
    }

    public List<PromotionActivityGiftDto> getGiftDtoList() {
        return giftDtoList;
    }

    public void setGiftDtoList(List<PromotionActivityGiftDto> giftDtoList) {
        this.giftDtoList = giftDtoList;
    }

    public List<String> getOrganSignList() {
        return organSignList;
    }

    public void setOrganSignList(List<String> organSignList) {
        this.organSignList = organSignList;
    }

    public String getTargetUser() {
        return targetUser;
    }

    public void setTargetUser(String targetUser) {
        this.targetUser = targetUser;
    }

    public String getGoodsLimit() {
        return goodsLimit;
    }

    public void setGoodsLimit(String goodsLimit) {
        this.goodsLimit = goodsLimit;
    }

    public Byte getPromotionDayWay() {
        return promotionDayWay;
    }

    public void setPromotionDayWay(Byte promotionDayWay) {
        this.promotionDayWay = promotionDayWay;
    }

    public String getPromotionDay() {
        return promotionDay;
    }

    public void setPromotionDay(String promotionDay) {
        this.promotionDay = promotionDay;
    }

    public Integer getNumberLimit() {
        return numberLimit;
    }

    public void setNumberLimit(Integer numberLimit) {
        this.numberLimit = numberLimit;
    }

    public Byte getDayLimit() {
        return dayLimit;
    }

    public void setDayLimit(Byte dayLimit) {
        this.dayLimit = dayLimit;
    }

    public BigDecimal getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }

    public BigDecimal getPromotionPrice() {
        return promotionPrice;
    }

    public void setPromotionPrice(BigDecimal promotionPrice) {
        this.promotionPrice = promotionPrice;
    }

    public BigDecimal getBuyFullAmount() {
        return buyFullAmount;
    }

    public void setBuyFullAmount(BigDecimal buyFullAmount) {
        this.buyFullAmount = buyFullAmount;
    }

    public BigDecimal getBuyFullNumber() {
        return buyFullNumber;
    }

    public void setBuyFullNumber(BigDecimal buyFullNumber) {
        this.buyFullNumber = buyFullNumber;
    }

    public BigDecimal getGiftNumber() {
        return giftNumber;
    }

    public void setGiftNumber(BigDecimal giftNumber) {
        this.giftNumber = giftNumber;
    }

    public BigDecimal getReduceAmount() {
        return reduceAmount;
    }

    public void setReduceAmount(BigDecimal reduceAmount) {
        this.reduceAmount = reduceAmount;
    }

    public Integer getOrderLimit() {
        return orderLimit;
    }

    public void setOrderLimit(Integer orderLimit) {
        this.orderLimit = orderLimit;
    }

    public Integer getDailyLimit() {
        return dailyLimit;
    }

    public void setDailyLimit(Integer dailyLimit) {
        this.dailyLimit = dailyLimit;
    }

    public Integer getTotalLimit() {
        return totalLimit;
    }

    public void setTotalLimit(Integer totalLimit) {
        this.totalLimit = totalLimit;
    }

    public Byte getMemberDiscountYn() {
        return memberDiscountYn;
    }

    public void setMemberDiscountYn(Byte memberDiscountYn) {
        this.memberDiscountYn = memberDiscountYn;
    }

    public Integer getInWay() {
        return inWay;
    }

    public void setInWay(Integer inWay) {
        this.inWay = inWay;
    }

    public BigDecimal getBuyMultiple() {
        return buyMultiple;
    }

    public void setBuyMultiple(BigDecimal buyMultiple) {
        this.buyMultiple = buyMultiple;
    }

    public Byte getBuyFullMode() {
        return buyFullMode;
    }

    public void setBuyFullMode(Byte buyFullMode) {
        this.buyFullMode = buyFullMode;
    }

    public Byte getGiftMode() {
        return giftMode;
    }

    public void setGiftMode(Byte giftMode) {
        this.giftMode = giftMode;
    }

    public List<PromotionActivityPurchaseListDto> getPurchaseProductList() {
        return purchaseProductList;
    }

    public void setPurchaseProductList(List<PromotionActivityPurchaseListDto> purchaseProductList) {
        this.purchaseProductList = purchaseProductList;
    }

    public List<PromotionCombinedProductListDto> getPromotionCombinedProductLists() {
        return promotionCombinedProductLists;
    }

    public void setPromotionCombinedProductLists(List<PromotionCombinedProductListDto> promotionCombinedProductLists) {
        this.promotionCombinedProductLists = promotionCombinedProductLists;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getAllSelect() {
        return allSelect;
    }

    public void setAllSelect(Integer allSelect) {
        this.allSelect = allSelect;
    }
}
