<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.xyy.saas.promotion.core</groupId>
    <artifactId>xyy-saas-promotion-api</artifactId>
    <name>xyy-saas-promotion-api</name>
    <version>0.0.2-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>com.xyy.saas.common</groupId>
            <artifactId>xyy-saas-common-datasync</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>1.2.3</version>
        </dependency>
        <!--广告api-->
        <dependency>    
		  <groupId>com.xyy.saas</groupId>
		  <artifactId>xyy-ads-api</artifactId>
		  <version>1.0-SNAPSHOT</version>
		</dependency>

        <dependency>
            <groupId>com.xyy.saas.common</groupId>
            <artifactId>xyy-saas-common-util</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.xyy.saas.inventory.core</groupId>
                    <artifactId>xyy-saas-inventory-api</artifactId>
                    <!--<version>0.0.2-SNAPSHOT</version>-->
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.xyy.saas.member.core</groupId>
            <artifactId>xyy-saas-member-api</artifactId>
            <version>0.0.3-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>xyy-saas-emule-api</artifactId>
                    <groupId>com.xyy.saas</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-databind</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
    <properties>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
    </properties>


    <build>
        <plugins>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <name>maven-releases</name>
            <url>http://maven.int.ybm100.com/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <name>maven-snapshots</name>
            <url>http://maven.int.ybm100.com/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>


</project>
