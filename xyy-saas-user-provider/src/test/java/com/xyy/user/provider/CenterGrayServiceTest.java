package com.xyy.user.provider;

import com.xyy.user.provider.center.gray.CenterGrayService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
@ActiveProfiles({"test3"})
public class CenterGrayServiceTest {

    @Autowired
    private CenterGrayService centerGrayService;

    @Test
    public void isInCenterGrayTest() {
        boolean isInCenterGray = centerGrayService.isInCenterGray("ZHL00003784");
        log.info("isInCenterGray:{}", isInCenterGray);
    }

    @Test
    public void useCenterUserQueryTest() {
        boolean useCenterUserQuery = centerGrayService.useCenterUserQuery();
        log.info("useCenterUserQuery:{}", useCenterUserQuery);
    }
}
