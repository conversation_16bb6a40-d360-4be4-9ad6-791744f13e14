package com.xyy.user.provider;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.api.RefreshDataAuthApi;
import com.xyy.user.module.dto.AddRoleDto;
import com.xyy.user.module.dto.EditRoleDto;
import com.xyy.user.module.enums.DataSwitchEnum;
import com.xyy.user.provider.module.service.DataAuthService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class RefreshDataAuthApiTest {
    @Reference(version = "0.0.1", url="dubbo://localhost:20880")
    private RefreshDataAuthApi refreshDataAuthApi;
    @Autowired
    private DataAuthService dataAuthService;


    @Test
    public void refreshSingleOrganSignTest(){
        boolean result1 = refreshDataAuthApi.refreshDataAuth("ZHL00003785");
        log.info("result1:{}",result1);
        boolean result2 = refreshDataAuthApi.refreshDataAuth("ZHL00003900");
        log.info("result2:{}",result2);
    }

    @Test
    public void refreshSingleOrganSignTest2(){
        EditRoleDto editRoleDto = new EditRoleDto();
        editRoleDto.setOrganSign("ZHL00003785");
        editRoleDto.setRoleId(1);
        editRoleDto.setDataSwitch(DataSwitchEnum.OPEN.getKey());
        dataAuthService.save(editRoleDto);

        AddRoleDto addRoleDto = new AddRoleDto();
        addRoleDto.setOrganSign("ZHL00003785");
        addRoleDto.setRoleId(9);
        addRoleDto.setDataSwitch(DataSwitchEnum.OPEN.getKey());
        dataAuthService.save(addRoleDto);
    }


    @Test
    public void refreshAllOrganSignTest(){
        ResultVO<Boolean> booleanResultVO = refreshDataAuthApi.refreshDataAuthByPage();
        log.info("booleanResultVO:{}",booleanResultVO);
    }

    @Test
    public void refreshCostAccountingByPage(){
        ResultVO<Boolean> booleanResultVO = refreshDataAuthApi.refreshCostAccountingByPage();
        log.info("booleanResultVO:{}",booleanResultVO);
    }

    @Test
    public void refreshEmployee(){
        refreshDataAuthApi.refreshEmployee();
    }
}
