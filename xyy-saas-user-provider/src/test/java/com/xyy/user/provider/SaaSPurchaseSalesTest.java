package com.xyy.user.provider;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.PurchaserSalesApi;
import com.xyy.user.module.dto.restructure.*;
import com.xyy.user.module.enums.ModifyTypeEnum;
import com.xyy.user.module.enums.PermissionStatusEnum;
import com.xyy.user.module.enums.PurchaserAuditStatusEnum;
import com.xyy.user.provider.module.entity.SaasDrugstore;
import com.xyy.user.provider.module.service.PurchaserSalesService;
import com.xyy.user.provider.module.service.PurchaserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class SaaSPurchaseSalesTest {

    @Autowired
    private PurchaserSalesApi purchaserSalesApi;

    @Test
    public void initPurchaser(){
        InitPurchaserSalesDto initPurchaserSalesDto = new InitPurchaserSalesDto();
        initPurchaserSalesDto.setHeadquartersOrganSign("ZHL00002690");
        ResultVO<String> result = purchaserSalesApi.initPurchaser(initPurchaserSalesDto);
        log.info("initPurchaser:{}", JSON.toJSONString(result));
    }

    @Test
    public void stagingPurchaser() {
        SaasPurchaserSalesDto purchaserSalesDto = new SaasPurchaserSalesDto();
        purchaserSalesDto.setHeadquartersOrganSign("ZHL00002690");
        purchaserSalesDto.setCurrentEmployeeId(114254L);
        purchaserSalesDto.setPurchaserNo("ZHL00004705");
        purchaserSalesDto.setSubmitType(PurchaserAuditStatusEnum.STAGING.getKey());

        purchaserSalesDto.setPurchaserUnitName("销售单位名称1");
        purchaserSalesDto.setMnemonicCode("助记码1");
        purchaserSalesDto.setShortName("单位简称1");
        purchaserSalesDto.setLinkman("联系人1");
        purchaserSalesDto.setContactPhone("联系电话1");
        purchaserSalesDto.setBillingYn(0); // 是否开票
        purchaserSalesDto.setLegalPerson("法定代表人1");
        purchaserSalesDto.setEnterpriseResponsiblePerson("企业负责人1");
        purchaserSalesDto.setBranchBank("开户银行1");
        purchaserSalesDto.setBusinessType(1); // 经营方式
        purchaserSalesDto.setCustomerType(1); // 客户性质
        purchaserSalesDto.setAccountName("开户名1");
        purchaserSalesDto.setBankAccount("银行账号1");
        purchaserSalesDto.setConsignee("客户收货人1");
        purchaserSalesDto.setBusinessAddress("营业执照地址1");
        purchaserSalesDto.setRegisterDate(new Date());
        purchaserSalesDto.setExpiryDate(new Date());
        purchaserSalesDto.setTaxpayerCode("纳税人识别号1");
        purchaserSalesDto.setPaymentsWay(1); // 结算方式
        purchaserSalesDto.setBillingType(1); // 发票类型
        purchaserSalesDto.setWarehouseCode("仓库地址区域编码1");
        purchaserSalesDto.setWarehouseAddress("仓库地址1");
        purchaserSalesDto.setRegisterCode("注册地址区域编码1");
        purchaserSalesDto.setRegisterAddress("注册地址1");
        purchaserSalesDto.setReceivingCode("收货地址区域编码1");
        purchaserSalesDto.setReceivingAddress("收货地址1");

        List<QualificationInfo> qualificationInfos = new ArrayList<>();
        QualificationInfo qualificationInfo1 = new QualificationInfo();
        qualificationInfo1.setCertificateType(1);
        qualificationInfo1.setCertificateNo("营业执照1");
        qualificationInfo1.setCertificateDate("2020-01-01 01:01:01");
        qualificationInfo1.setCertificateDate("2021-01-01 01:01:01");
        qualificationInfo1.setModifyType(ModifyTypeEnum.ADD.getKey());
        SaasImgUrlDto imgUrlDto1 = new SaasImgUrlDto();
        imgUrlDto1.setType(1);
        imgUrlDto1.setDel(0); // 新增
        imgUrlDto1.setImgUrl("新增1");
        SaasImgUrlDto imgUrlDto2 = new SaasImgUrlDto();
        imgUrlDto2.setType(1);
        imgUrlDto2.setDel(0); // 新增
        imgUrlDto2.setImgUrl("新增2");
        qualificationInfo1.setImgUrls(Arrays.asList(imgUrlDto1, imgUrlDto2));
        BusinessScopeDto businessScopeDto = new BusinessScopeDto();
        businessScopeDto.setId(271);
        qualificationInfo1.setBusinessScope(Arrays.asList(businessScopeDto));
        qualificationInfos.add(qualificationInfo1);

        QualificationInfo qualificationInfo2 = new QualificationInfo();
        qualificationInfo2.setCertificateType(3);
        qualificationInfo2.setCertificateNo("药品许可证号1");
        qualificationInfo2.setCertificateDate("2019-01-01 01:01:01");
        qualificationInfo2.setCertificateDate("2022-01-01 01:01:01");
        qualificationInfo2.setModifyType(ModifyTypeEnum.ADD.getKey());
        SaasImgUrlDto imgUrlDto3 = new SaasImgUrlDto();
        imgUrlDto3.setType(2);
        imgUrlDto3.setDel(0); // 新增
        imgUrlDto3.setImgUrl("新增3");
        SaasImgUrlDto imgUrlDto4 = new SaasImgUrlDto();
        imgUrlDto4.setType(2);
        imgUrlDto4.setDel(0); // 新增
        imgUrlDto4.setImgUrl("新增4");
        qualificationInfo2.setImgUrls(Arrays.asList(imgUrlDto3, imgUrlDto4));
        BusinessScopeDto businessScopeDto2 = new BusinessScopeDto();
        businessScopeDto2.setId(271);
        qualificationInfo2.setBusinessScope(Arrays.asList(businessScopeDto2));
        qualificationInfos.add(qualificationInfo2);

        purchaserSalesDto.setQualificationInfos(qualificationInfos);
        purchaserSalesApi.editPurchaser(purchaserSalesDto);
        log.info("editPurchaser");
    }

    @Test
    public void auditingPurchaser() {

    }

    @Test
    public void listPurchaser() {
        QueryPurchaserSalesDto queryPurchaserDto = new QueryPurchaserSalesDto();
        queryPurchaserDto.setHeadquartersOrganSign("ZHL00002690");
        queryPurchaserDto.setCurrentEmployeeId(114254);
        queryPurchaserDto.setPageNum(1);
        queryPurchaserDto.setPageSize(100);
//        queryPurchaserDto.setQueryCondition("销售单位名称");
        queryPurchaserDto.setPurchaserNoList(Arrays.asList("ZHL00004705"));
        ResultVO<PageInfo<SaasPurchaserSalesDto>> purchaserByPage = purchaserSalesApi.getPurchaserByPage(queryPurchaserDto);
        log.info("listPurchaser:{}", JSON.toJSONString(purchaserByPage));
    }

    @Test
    public void getPurchaser() {
        QueryPurchaserDetailDto queryPurchaserDetailDto = new QueryPurchaserDetailDto();
        queryPurchaserDetailDto.setHeadquartersOrganSign("ZHL00002924");
        queryPurchaserDetailDto.setPurchaserNo("ZHL00005641");
        ResultVO<SaasPurchaserSalesDto> purchaserByPurchaserNo = purchaserSalesApi.getPurchaserByPurchaserNo(queryPurchaserDetailDto);
        log.info("getPurchaser:{}", JSON.toJSONString(purchaserByPurchaserNo));
    }

    @Test
    public void listEnablePurchaser() {
        QueryPurchaserSalesDto queryPurchaserDto = new QueryPurchaserSalesDto();
        queryPurchaserDto.setPageNum(1);
        queryPurchaserDto.setPageSize(1);
        queryPurchaserDto.setHeadquartersOrganSign("ZHL00002690");
        queryPurchaserDto.setAuditStatus(PurchaserAuditStatusEnum.AUDIT_PASS.getKey());
        queryPurchaserDto.setStatus(PermissionStatusEnum.ENABLED.getKey());
        queryPurchaserDto.setMinExpiryDate(new Date());

        List<SaasPurchaserSalesDto> purchaserSalesDtoList = new ArrayList<>();
        for (int i = 0; i < 20; i++) {
            ResultVO<PageInfo<SaasPurchaserSalesDto>> purchaserSalesPage = purchaserSalesApi.getPurchaserSalesByPage(queryPurchaserDto);
            if (purchaserSalesPage.getCode() != ResultCodeEnum.SUCCESS.getCode()) {
                break;
            }
            PageInfo<SaasPurchaserSalesDto> purchaserSalesDtoPage = purchaserSalesPage.getResult();
            if (CollectionUtils.isEmpty(purchaserSalesDtoPage.getList())) {
                break;
            }
            purchaserSalesDtoList.addAll(purchaserSalesDtoPage.getList());
            if (purchaserSalesDtoPage.isIsLastPage()) {
                break;
            }
            queryPurchaserDto.setPageNum(purchaserSalesDtoPage.getNextPage());
        }
        log.info("listEnablePurchaser:{}", JSON.toJSONString(purchaserSalesDtoList));
    }
}
