package com.xyy.user.provider;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.user.center.api.pojo.response.EmployeeDTO;
import com.xyy.user.center.common.model.Result;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.api.EmployeefileApi;
import com.xyy.user.module.api.HelpAndServiceApi;
import com.xyy.user.module.api.UserApi;
import com.xyy.user.module.dto.*;
import com.xyy.user.module.dto.restructure.*;
import com.xyy.user.module.dto.EditEmployeeDto;
import com.xyy.user.module.dto.EmployeeListRequestModel;
import com.xyy.user.module.dto.SystemRoleQueryDto;
import com.xyy.user.module.dto.restructure.ClearFingerprintDto;
import com.xyy.user.module.dto.restructure.InputFingerprintDto;
import com.xyy.user.module.dto.restructure.PosBaoJiToolLoginDto;
import com.xyy.user.module.dto.restructure.QueryFingerprintDto;
import com.xyy.user.module.dto.result.EmployeeDto;
import com.xyy.user.module.dto.result.LoginDto;
import com.xyy.user.module.dto.result.QueryEmployeeVO;
import com.xyy.user.module.enums.DrugstoreBizModelEnum;
import com.xyy.user.module.enums.ImageTypeEnum;
import com.xyy.user.module.enums.SystemRoleCodeEnum;
import com.xyy.user.provider.center.rpc.QueryEmployeeAdapter;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class EmployeeUnitTest {

    @Reference(version = "0.0.1", url = "dubbo://localhost:20880")
    private EmployeeApi employeeApi;
    @Reference(version = "0.0.1", url = "dubbo://localhost:20880")
    private EmployeefileApi employeefileApi;
    @Reference(version = "0.0.1", url = "dubbo://localhost:20880")
    private UserApi userApi;

    @Reference(version = "0.0.1", url = "dubbo://localhost:20880")
    private HelpAndServiceApi helpAndServiceApi;


    @Autowired
    private QueryEmployeeAdapter queryEmployeeAdapter;


    @Test
    public void getEmployeeListBySystemRoleSuccess() {
        SystemRoleQueryDto queryDto = new SystemRoleQueryDto();
        queryDto.setOrganSign("ZHL00000449");
        queryDto.setSystemRoleCodeEnum(SystemRoleCodeEnum.STORE_ADMIN_ROLE);
        ResultVO<List<QueryEmployeeVO>> list = employeeApi.getEmployeeListBySystemRole(queryDto);
        System.out.println(JSONObject.toJSONString(list));
    }

    @Test
    public void getEmployeeListBySystemRoleWrongOrganSign() {
        SystemRoleQueryDto queryDto = new SystemRoleQueryDto();
        queryDto.setOrganSign("ZHL000014112");
        queryDto.setSystemRoleCodeEnum(SystemRoleCodeEnum.ENTERPRISE_OWNER_ROLE);
        ResultVO<List<QueryEmployeeVO>> list = employeeApi.getEmployeeListBySystemRole(queryDto);
        System.out.println(JSONObject.toJSONString(list));
    }

    @Test
    public void getEmployeeListBySystemRoleEmptyRole() {
        SystemRoleQueryDto queryDto = new SystemRoleQueryDto();
        queryDto.setOrganSign("ZHL00001411");
        ResultVO<List<QueryEmployeeVO>> list = employeeApi.getEmployeeListBySystemRole(queryDto);
        System.out.println(JSONObject.toJSONString(list));
    }

    @Test
    public void androidLoginTest() {
        ResultVO<LoginDto> resultVO = userApi.iosLogin("17600523188", "65ba78d97ee08ff196568faaff327326", "17600523188", "loginName", null);
        log.info("resultVO:{}", JSON.toJSONString(resultVO));
    }

    @Test
    public void posBaoJiToolLogin() {
        PosBaoJiToolLoginDto posBaoJiToolLoginDto = new PosBaoJiToolLoginDto();
        posBaoJiToolLoginDto.setBizModel((byte) DrugstoreBizModelEnum.CHAIN_STORE.getKey());
        posBaoJiToolLoginDto.setCommonRequestModel(null);
        posBaoJiToolLoginDto.setEnterprise("ZHL00002774");
        posBaoJiToolLoginDto.setLoginName("18766661118");
        posBaoJiToolLoginDto.setModel("loginName");
        posBaoJiToolLoginDto.setPassword("742d0cf468c27a1673d4fa79e93bb76d");
        ResultVO<LoginDto> resultVO = userApi.posBaoJiToolLogin(posBaoJiToolLoginDto);

        log.info("resultVO:{}", JSON.toJSONString(resultVO));
    }

    @Test
    public void editEmployeeInfo() {
        EditEmployeeDto editEmployeeDto = new EditEmployeeDto();
        editEmployeeDto.setEmployeeId(7);
        editEmployeeDto.setName("董振");
        editEmployeeDto.setJoiningTime(new Date());
        editEmployeeDto.setPhone("13451234567111");
        editEmployeeDto.setIdCard("*******************");
        ResultVO<Boolean> resultVO = employeeApi.editEmployeeInfo(editEmployeeDto);
        log.info("resultVO:{}", JSON.toJSONString(resultVO));
    }

    @Test
    public void inputFingerprintTest(){
        InputFingerprintDto inputFingerprintDto = new InputFingerprintDto();
        inputFingerprintDto.setEmployeeId(4290);
        inputFingerprintDto.setOrganSign("ZHL00001599");
        inputFingerprintDto.setDeviceId("ceshi-device-id");
        inputFingerprintDto.setFingerprintInfo("ceshi-fingerprint-info");
        inputFingerprintDto.setManufacturer("ceshi-Manufacturer");
        ResultVO<Boolean> booleanResultVO = employeeApi.inputFingerprint(inputFingerprintDto);
        log.info("booleanResultVO:{}", JSON.toJSONString(booleanResultVO));
    }

    @Test
    public void clearFingerprintTest(){
        ClearFingerprintDto clearFingerprintDto = new ClearFingerprintDto();
        clearFingerprintDto.setEmployeeId(113395);
        clearFingerprintDto.setOrganSign("ZHL00001599");
        ResultVO<Boolean> booleanResultVO = employeeApi.clearFingerprint(clearFingerprintDto);
        log.info("booleanResultVO:{}", JSON.toJSONString(booleanResultVO));
    }

    @Test
    public void queryEmployeePageTest(){
        EmployeeListRequestModel model = new EmployeeListRequestModel();
        ResultVO employeeByCondition = employeeApi.queryEmployeeByCondition(model, "ZHL00001599");
        log.info("employeeByCondition:{}", JSON.toJSONString(employeeByCondition));
    }
    @Test
    public void queryFingerprintInfoTest(){
        QueryFingerprintDto queryFingerprintDto = new QueryFingerprintDto();
        queryFingerprintDto.setEmployeeId(113395);
        queryFingerprintDto.setOrganSign("ZHL00001599");
        ResultVO fingerprintInfo = employeeApi.queryFingerprintInfo(queryFingerprintDto);
        log.info("fingerprintInfo:{}", JSON.toJSONString(fingerprintInfo));
    }


    @Test
    public void queryHeadquartersEmployeeList(){
        EmployeeListRequestModel model = new EmployeeListRequestModel();
        model.setHeadquartersOrganSign("ZHL00002635");
        model.setOrganSign("ZHLBM001326");
        model.setQueryType(1);
        model.setPageNo(1);
        model.setPageSize(50);
        ResultVO fingerprintInfo = employeeApi.queryHeadquartersEmployeeList(model);
        log.info("fingerprintInfo:{}", JSON.toJSONString(fingerprintInfo));
    }

    /**
     * 按搜索条件查询总部下员工档案列表
     * @return
     */
    @Test
    public void getEmployeeFileByConditionList(){
        EmployeefileRequestModel model = new EmployeefileRequestModel();
        model.setHeadquartersOrganSign("ZHL00003686");
        model.setQueryType(1);
        model.setPage(1);
        model.setRows(100);
        ResultVO fingerprintInfo = null;
        try {
            fingerprintInfo = employeefileApi.getEmployeeFileByConditionList(model);
        } catch (Exception e) {
            e.printStackTrace();
        }
        log.info("fingerprintInfo:{}", JSON.toJSONString(fingerprintInfo));
    }


    /**
     * 根据员工id查询员工信息(员工管理查询详情)
     * @param
     * @return
     */
    @Test
    public void queryEmployeeById(){
        ResultVO fingerprintInfo = null;
        try {
            fingerprintInfo = employeeApi.queryEmployeeById(115712);
        } catch (Exception e) {
            System.out.println(e);
        }
        log.info("fingerprintInfo:{}", JSON.toJSONString(fingerprintInfo));
    }

    /**
     * 添加员工和角色
     * @param
     * @return
     */
    @Test
    public void addEmployeeAndRoleInfo(){
        AddEmployeeDto model = new AddEmployeeDto();
        model.setLoginName("zhanghao222");
        model.setPassword("70ebc4f9c9d22827a5874d1bb6f06abd");
        model.setName("yw222");
        model.setSex((byte) 2);
        model.setJoiningTime(new Date());
        List<Integer> roIds= new ArrayList<>();
        roIds.add(2);
        model.setRoleIds(roIds);
        model.setCredit("100");

        //员工照片信息
        List<SaasImgUrlDto> creditImageUrl = new ArrayList<>();
        SaasImgUrlDto saasImgUrlDto= new SaasImgUrlDto();
        saasImgUrlDto.setType(ImageTypeEnum.EMPLOYEE_IMG.getKey());
        saasImgUrlDto.setImgUrl("https://files.test.ybm100.com/G2/M00/17/23/Cgo01F7i7B-AQrDQAAcQI35m4Wk799.png");
        creditImageUrl.add(saasImgUrlDto);
        model.setCreditImageUrl(creditImageUrl);


        String orgSan ="ZHL00001599";
        model.setOrganSign(orgSan);
        ResultVO fingerprintInfo = employeeApi.addEmployeeAndRoleInfo(model,orgSan);
        log.info("fingerprintInfo:{}", JSON.toJSONString(fingerprintInfo));
    }


    /**
     * 编辑员工信息
     * @param
     * @return
     */
    @Test
    public void editEmployeeAndRoleInfo(){
        EditEmployeeDto model = new EditEmployeeDto();

        model.setName("yw222");
        model.setSex((byte) 1);
        model.setJoiningTime(new Date());
        model.setNational("han");
        List<Integer> roIds= new ArrayList<>();
        roIds.add(2);
        model.setRoleIds(roIds);
        model.setCredit("10010");
        model.setEmployeeId(113850);

        //员工照片信息
        List<SaasImgUrlDto> creditImageUrl = new ArrayList<>();
//        SaasImgUrlDto saasImgUrlDto= new SaasImgUrlDto();
//        saasImgUrlDto.setType(ImageTypeEnum.EMPLOYEE_IMG.getKey());
//        saasImgUrlDto.setImgUrl("https://files.test.ybm100.com/G2/M00/17/23/Cgo01F7i7B-AQrDQAAcQI35m4Wk799.png");
//        saasImgUrlDto.setDel(1);
//        saasImgUrlDto.setId(2107L);
//        saasImgUrlDto.setEmployeeId(113850);
//        creditImageUrl.add(saasImgUrlDto);

        SaasImgUrlDto saasImgUrlDtoInsert= new SaasImgUrlDto();
        saasImgUrlDtoInsert.setType(ImageTypeEnum.EMPLOYEE_IMG.getKey());
        saasImgUrlDtoInsert.setImgUrl("https://files.test.ybm100.com/G2/M00/17/23/Cgo01F7i7B-AQrDQAAcQI35m4Wk798.png");
        saasImgUrlDtoInsert.setDel(0);
        creditImageUrl.add(saasImgUrlDtoInsert);



        model.setCreditImageUrl(creditImageUrl);
        String orgSan ="ZHL00001599";
        model.setOrganSign(orgSan);
        ResultVO fingerprintInfo = employeeApi.editEmployeeAndRoleInfo(model,orgSan);
        log.info("fingerprintInfo:{}", JSON.toJSONString(fingerprintInfo));
    }


    /**
     * 查询机构下的全体员工(下拉选择框)
     * @param
     * @return
     */
    @Test
    public void queryAllEmployeeSelectedByOrganSign(){
        ResultVO<List<EmployeeSelectedDto>> employeeSelectedDtoList = employeeApi.queryAllEmployeeSelectedByOrganSign("ZHL00002690");
        log.info("fingerprintInfo:{}", JSON.toJSONString(employeeSelectedDtoList));
    }

    @Test
    public void queryAllEmployeeByIds(){
        List<Integer> ids= Arrays.asList(new Integer[]{292,293,294,295,296});
        List<EmployeeDto> employeeDtoList = employeeApi.queryAllEmployeeByIds(ids);
        log.info("fingerprintInfo:{}", JSON.toJSONString(employeeDtoList));
    }

    /**
     * 根据角色编码，门店机构集合查询门店机构下绑定该角色的所有员工(排除非系统角色)
     * @param  （roleCode,OrganSignList）
     * @return
     */
    @Test
    public void getEmployeeListByRoleCodeAndOrganSignList(){
        SaasRoleDto saasRoleDto= new SaasRoleDto();
        saasRoleDto.setPageNum(2);
        saasRoleDto.setPageSize(20);
        saasRoleDto.setRoleCode("MDJS00007");
        List<String> organSignList= Arrays.asList(new String[]{"ZHL00000237", "ZHL00003434", "ZHL00003514", "ZHL00003529", "ZHL00003533", "ZHL00003542"});
        saasRoleDto.setOrganSignList(organSignList);
        ResultVO<PageInfo<EmployeeDto>> employeeListByRoleCodeAndOrganSignList = employeeApi.getEmployeeListByRoleCodeAndOrganSignList(saasRoleDto);
        log.info("fingerprintInfo:{}", JSON.toJSONString(employeeListByRoleCodeAndOrganSignList));
    }
    /**
     * 根据门店机构号获取首页帮助中常见问题
     * @param
     * @return
     */
    @Test
    public void getCommonProblem(){
        String organSign ="ZHL00000237";
        ResultVO< List<HelpAndServiceDto>> commonProblem = helpAndServiceApi.getCommonProblem(organSign);
        System.out.println("success");
    }


    @Test
    public void employeeTest(){
        String organSign = "ZHL00000271";
        EmployeeListRequestModel model = new EmployeeListRequestModel();
        ResultVO resultVO = employeeApi.queryEmployeeByCondition(model, organSign);
        log.info("result：{}",resultVO);
    }

    @Test
    public void getEmployeeById(){
        ResultVO<EmployeeDto> employeeDtoResultVO = employeeApi.queryEmployeeById(296);
        log.info("result:{}", JSON.toJSONString(employeeDtoResultVO));
    }

    @Test
    public void queryEmployeeByRoleIdAndOrganSign(){
        EmployeeAndRolesDTO employeeAndRolesDTO = new EmployeeAndRolesDTO();
        employeeAndRolesDTO.setOrganSign("ZHL00002141");
        List<Integer> roleIds = new ArrayList<>();
        roleIds.add(1);
        roleIds.add(2);
        roleIds.add(3);
        employeeAndRolesDTO.setRoleIds(roleIds);
        ResultVO<List<EmployeeDto>> employeeDtoList = null;
        try {
            employeeDtoList = employeeApi.queryEmployeeByRoleIdsAndOrganSign(employeeAndRolesDTO);
        } catch (Exception e) {
            e.printStackTrace();
        }
        log.info("result:{}", JSON.toJSONString(employeeDtoList));
    }


    @Test
    public void getEmployeeListByRoleCodes(){
        SaasRoleDto saasRoleDto= new SaasRoleDto();
        saasRoleDto.setOrganSign("ZHL00002364");
        List<String> roleCodeList = new ArrayList<>();
        roleCodeList.add("MDJS00010");
        roleCodeList.add("MDJS00002");
        roleCodeList.add("MDJS00001");
        saasRoleDto.setRoleCodeList(roleCodeList);
        ResultVO<List<EmployeeDto>> employeeListByRoleCodes = null;
        try {
            employeeListByRoleCodes = employeeApi.getEmployeeListByRoleCodes(saasRoleDto);
        } catch (Exception e) {
            e.printStackTrace();
        }
        log.info("result:{}", JSON.toJSONString(employeeListByRoleCodes));
    }
}
