package com.xyy.user.provider;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.UserApi;
import com.xyy.user.module.dto.CustomerDto;
import com.xyy.user.module.dto.LoginParamDto;
import com.xyy.user.module.dto.MallUserInfoVo;
import com.xyy.user.module.dto.QueryCustomerParam;
import com.xyy.user.module.dto.result.LoginDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class UserApiTest {
    @Autowired
    private UserApi userApi;




    @Test
    public void androidLoginTest(){
        ResultVO<LoginDto> resultVO = userApi.iosLogin("17600523188", "65ba78d97ee08ff196568faaff327326", "17600523188", "loginName", null);
        log.info("resultVO:{}", JSON.toJSONString(resultVO));
    }
    @Test
    public void queryDrugstoreManagerPhone(){
        Map<String, String> map = userApi.queryDrugstoreManagerPhone("ZHL00000269");
        log.info("resultVO:{}", JSON.toJSONString(map));
    }

    @Test
    public void pageManagerUserTest() {
        QueryCustomerParam queryDrugstoreParam = new QueryCustomerParam();
        queryDrugstoreParam.setProvinceNames(Arrays.asList("黑龙江省", "吉林省"));
        ResultVO<PageInfo> managerUserPage = userApi.getCustomerUserIdPage(queryDrugstoreParam);
        log.info("managerUserPage:{}", JSON.toJSONString(managerUserPage));
    }

    @Test
    public void selectCustomerByPageTest() {
        QueryCustomerParam queryDrugstoreParam = new QueryCustomerParam();
        queryDrugstoreParam.setProvinceNames(Arrays.asList("黑龙江省", "吉林省"));
        PageInfo<CustomerDto> pageInfo = userApi.selectCustomerByPage(queryDrugstoreParam);
        log.info("pageInfo:{}", JSON.toJSONString(pageInfo));
    }

    @Test
    public void getMallUserInfoTest() {
        ResultVO<MallUserInfoVo> mallUserInfo = userApi.getUserInfoForMallByCustomerId(0);
        log.info("mallUserInfo:{}", JSON.toJSONString(mallUserInfo));
    }

    /**
     * 总部端登录
     */
    @Test
    public void headquartersLogin() {
        String loginName ="15001129325";
        String password = "96e79218965eb72c92a549dd5a330112";
        String enterprise ="ZHL00002349";
        String model ="loginName";
        ResultVO<LoginDto> loginDtoResultVO = userApi.headquartersLogin(loginName, password, enterprise, model);
        log.info("mallUserInfo:{}", JSON.toJSONString(loginDtoResultVO));
    }

    /**
     * 总部端登录
     */
    @Test
    public void webLogin() {
        String loginName ="test";
        String password = "62c8ad0a15d9d1ca38d5dee762a16e01";
        String enterprise ="16830003169";
        String model ="loginName";
        ResultVO<LoginDto> loginDtoResultVO = userApi.login(loginName, password, enterprise, model);
        log.info("mallUserInfo:{}", JSON.toJSONString(loginDtoResultVO));
    }
    @Test
    public void testNewLogin(){
        LoginParamDto loginParamDto = new LoginParamDto();
        loginParamDto.setLoginName("15010733170");
        loginParamDto.setPassword("123");
        loginParamDto.setEnterprise("ZHL0009306");
        loginParamDto.setModel("username");
        loginParamDto.setSlideCaptchaId("a");
        loginParamDto.setSlideX("1");
        ResultVO<LoginDto> login = userApi.login(loginParamDto);
        System.out.println(JSON.toJSONString(login));
    }
}
