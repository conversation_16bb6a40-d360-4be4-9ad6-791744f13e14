package com.xyy.user.provider;


import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.api.UserApi;
import com.xyy.user.module.dto.Drugstore;
import com.xyy.user.module.dto.SaasDrugstoreDto;
import com.xyy.user.module.dto.restructure.AccountSetDto;
import com.xyy.user.module.dto.restructure.CreateAccountSMSDto;
import com.xyy.user.module.dto.restructure.CreateAccountSetDto;
import com.xyy.user.module.dto.restructure.QueryAccountSetDto;
import com.xyy.user.provider.common.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class AccountSetTest {

    @Reference(version = "0.0.1", url="dubbo://localhost:20880")
    private DrugstoreApi drugstoreApi;
    @Reference(version = "0.0.1", url="dubbo://localhost:20880")
    private UserApi userApi;

    @Test
    public void createAccountSet(){
        CreateAccountSetDto createAccountSetDto = new CreateAccountSetDto();
        createAccountSetDto.setEmployeeId(299);
        createAccountSetDto.setAccountSetName("测试账户附属账套");
        createAccountSetDto.setPassword(MD5Util.getMD5("abc123456"));
        createAccountSetDto.setConfirmPassword(MD5Util.getMD5("abc123456"));
        createAccountSetDto.setLoginName("liangzhiwei");
        createAccountSetDto.setVerifyCode("425310");
        ResultVO<Boolean> accountSet = drugstoreApi.createAccountSet(createAccountSetDto);
        log.info("accountSet:{}", JSON.toJSONString(accountSet));
    }

    @Test
    public void sendCreateAccountSetSMSTest(){
        CreateAccountSMSDto createAccountSMSDto = new CreateAccountSMSDto();
        createAccountSMSDto.setEmployeeId(299);
        createAccountSMSDto.setOrganSign("ZHL00000272");
        ResultVO<Boolean> createAccountSMS = drugstoreApi.sendCreateAccountSMS(createAccountSMSDto);
        log.info("createAccountSMS:{}", JSON.toJSONString(createAccountSMS));
    }

    @Test
    public void getAccountSetListTest(){
        QueryAccountSetDto createAccountSMSDto = new QueryAccountSetDto();
        createAccountSMSDto.setOrganSign("ZHL00003628");
        ResultVO<List<AccountSetDto>> accountSetList = drugstoreApi.getAccountSetList(createAccountSMSDto);
        log.info("accountSetList:{}", JSON.toJSONString(accountSetList));
    }

    @Test
    public void get(){
        SaasDrugstoreDto model = new SaasDrugstoreDto();
        model.setMainYn((byte) 1);
        ResultVO<PageInfo<SaasDrugstoreDto>> drugstoreByCondition = drugstoreApi.getDrugstoreByCondition(model);
        log.info("drugstoreByCondition:{}", JSON.toJSONString(drugstoreByCondition));
    }

    @Test
    public void clearDataSendSMSTest(){
        ResultVO<Boolean> booleanResultVO = userApi.clearDataSendSMS("***********", "ZHL00003799");
        log.info("booleanResultVO:{}", JSON.toJSONString(booleanResultVO));
    }
}
