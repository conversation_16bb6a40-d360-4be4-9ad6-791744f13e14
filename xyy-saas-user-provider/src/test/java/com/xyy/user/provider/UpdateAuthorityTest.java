package com.xyy.user.provider;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.rocketmq.client.A;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.api.PackageApi;
import com.xyy.user.module.api.PermissionApi;
import com.xyy.user.module.dto.AddPackageExtendDto;
import com.xyy.user.module.dto.EditEmployeeDto;
import com.xyy.user.module.dto.PermissionDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.Date;

/**
 * 智慧脸移动端 - 盘点需求测试用例
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class UpdateAuthorityTest {
    @Reference(version = "0.0.1", url = "dubbo://localhost:20880")
    private PermissionApi permissionApi;


    @Reference(version = "0.0.1", url = "dubbo://localhost:20880")
    private EmployeeApi employeeApi;

    @Reference(version = "0.0.1", url = "dubbo://localhost:20880")
    private PackageApi packageApi;


    /**
     * (5)用户自行在PC端新增/删除自定义角色的权限，
     */
    @Test
    public void getEmployeeListBySystemRoleSuccess() {
        Integer roleId = 1023;
        String permissions = "102072,103442,103449,103466,103467,103781,103450,103471,103469,102200";
        Byte bizModel = (byte) 3;
        Byte organSignType = (byte) 1;
        String str = permissionApi.saveRolePermission(roleId, permissions, bizModel, organSignType);
        System.out.println(JSONObject.toJSONString(str));
    }

    /**
     * (6)用户自行在PC端新增/删除员工的角色。
     */
    @Test
    public void editEmployeeAndRoleInfo() {
        EditEmployeeDto editEmployeeDto = new EditEmployeeDto();
        editEmployeeDto.setName("员工禁用修改姓名");
        editEmployeeDto.setSex((byte) 1);
        editEmployeeDto.setIdentity((byte) 0);
        Integer[] doleId = new Integer[]{2, 3, 4, 5, 6, 7, 8, 9, 10, 361, 362, 645, 1023};
        editEmployeeDto.setRoleIds(Arrays.asList(doleId));
        editEmployeeDto.setEmployeeId(112864);
        String organSign = "ZHL0009254";
        editEmployeeDto.setJoiningTime(new Date());
        ResultVO resultVO = employeeApi.editEmployeeAndRoleInfo(editEmployeeDto, organSign);
        System.out.println(JSONObject.toJSONString(resultVO));
    }

    /**
     * (7)用户自行在PC端禁用员工 (history)
     */
    @Test
    public void updateEmployeeStatus() {
        ResultVO resultVO = employeeApi.updateEmployeeStatus(114003, 1, null, null);
        System.out.println(JSONObject.toJSONString(resultVO));
    }

    /**
     * （8）连锁/联营门店新增/删除套餐中的权限。
     */
    @Test
    public void editPackage() {
        AddPackageExtendDto model = new AddPackageExtendDto();
        model.setId(96);
        model.setMenuList("102442,102441,102831,102832,102427,102834,102835,102428,102837,102838,102429,103302,103303," +
                "103301,103305,103306,103304,103307,103317,103319,103320,103338,103339,103557,103619,103648,103699,102426," +
                "103906,103907,103915,103916,103917,102598,102599,102600,102601,102602,102603,103872");
        ResultVO<Boolean> resultVO = packageApi.editPackage(model);
        System.out.println("success");
    }

    /**
     * （1）云平台编辑更新移动端菜单的内容
     */
    @Test
    public void saveOrUpdate() {
        PermissionDto permissionPo = new PermissionDto();
        permissionPo.setId(103776);
        permissionPo.setParentId(103610);
        permissionPo.setName("测试11");
        permissionPo.setUrl("/122");
        permissionPo.setDllName("my");
        permissionPo.setViewName("app权限");
        permissionPo.setTerminalType((byte)1);
        permissionPo.setColor("1");
        permissionPo.setIcon("333");
        permissionPo.setStatus((byte)1);
        permissionPo.setSort(11);
        permissionPo.setOrganSignType((byte)1);
        permissionPo.setBizModel((byte)1);
        int i = permissionApi.saveOrUpdate(permissionPo);
        System.out.println("success");
    }


}
