package com.xyy.user.provider;


import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.common.module.api.SaasBusinessScopeApi;
import com.xyy.common.module.dto.SaasBusinessScopeDto;
import com.xyy.common.provider.module.dao.SaasBusinessScopePoMapper;
import com.xyy.common.provider.module.entity.SaasBusinessScopePo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.CertificateBusinessScopeApi;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.dto.restructure.*;
import com.xyy.user.module.enums.BizModelEnum;
import com.xyy.user.module.enums.QueryCertificateEnum;
import com.xyy.user.module.vo.QualificationInfoVo;
import com.xyy.user.provider.module.dao.SaasCertificateBusinessScopeMapper;
import com.xyy.user.provider.module.dto.QueryCertificateConfig;
import com.xyy.user.provider.module.entity.SaasCertificateBusinessScope;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("test")
@Slf4j
public class CertificateBusinessScopeApiTest {
    @Autowired
    private CertificateBusinessScopeApi certificateBusinessScopeApi;

    @Autowired
    private SaasBusinessScopeApi saasBusinessScopeApi;

    @Value("${user.query.certificate.config}")
    protected String queryCertificateConfig;

    @Autowired
    private SaasCertificateBusinessScopeMapper certificateBusinessScopeMapper;

    @Autowired
    private SaasBusinessScopePoMapper saasBusinessScopePoMapper;

    @Autowired
    public DrugstoreApi drugstoreApi;

    @Test
    public void test1(){
        QueryCertificateTypeParam param = new QueryCertificateTypeParam();
        List<Integer> ids = new ArrayList<>();
        ids.add(835);
        ids.add(836);
        param.setIds(ids);
        param.setPageNum(1);
        param.setPageSize(50);
        ResultVO<PageInfo<CertificateBusinessScopeDto>> certificateType = certificateBusinessScopeApi.getCertificateType(param);
        log.info("certificateType:{}", JSON.toJSONString(certificateType));
    }

    @Test
    public void test2(){
        QueryBusinessScopeParam param = new QueryBusinessScopeParam();
        param.setOrganSign("ZHL00004346");
        param.setType(1);
        ResultVO<List<BusinessScopeVO>> businessScopeList = certificateBusinessScopeApi.getBusinessScopeList(param);
        log.info("businessScopeList:{}", JSON.toJSONString(businessScopeList));
    }

    @Test
    public void newBusScope(){
        String param = "[{\"imgUrls\":[],\"certificateType\":1,\"certificateNo\":\"YY0001\",\"certificateDate\":\"2021-11-01\",\"expiryDateDate\":\"2022-12-31\",\"businessScope\":null,\"businessScopeIds\":[273,275,700,701,702,703,704,705],\"modifyType\":null,\"row_index\":0},{\"imgUrls\":[{\"type\":14,\"url\":\"https://files.test.ybm100.com/G1/M00/1E/DD/Cgoz1GHBecqABTBCAADG-xXwKzk904.png\",\"id\":5444,\"del\":null,\"organSign\":\"ZHL00004346\",\"employeeId\":null,\"employeeHealthId\":null}],\"certificateType\":2,\"certificateNo\":\"SP2222\",\"certificateDate\":\"2021-12-09\",\"expiryDateDate\":\"2025-12-29\",\"businessScope\":null,\"businessScopeIds\":[265],\"modifyType\":null,\"row_index\":1},{\"imgUrls\":[{\"type\":2,\"url\":\"https://files.test.ybm100.com/G2/M00/1F/F0/Cgo002HBNTaAYQTnAAAZwBSKGwY041.png\",\"id\":5445,\"del\":null,\"organSign\":\"ZHL00004346\",\"employeeId\":null,\"employeeHealthId\":null}],\"certificateType\":3,\"certificateNo\":\"YP1111\",\"certificateDate\":\"2021-12-06\",\"expiryDateDate\":\"2022-12-31\",\"businessScope\":null,\"businessScopeIds\":[254],\"modifyType\":null,\"row_index\":2},{\"imgUrls\":[],\"certificateType\":4,\"certificateNo\":\"DEL3333\",\"certificateDate\":\"2022-01-01\",\"expiryDateDate\":\"2026-01-14\",\"businessScope\":null,\"businessScopeIds\":[733],\"modifyType\":null,\"row_index\":3},{\"imgUrls\":[],\"certificateType\":6,\"certificateNo\":\"XYY000\",\"certificateDate\":\"2021-12-04\",\"expiryDateDate\":\"2026-12-30\",\"businessScope\":null,\"businessScopeIds\":[799],\"modifyType\":null,\"row_index\":4}]";
        Long employId = 117450L;
        String organSign = "ZHL00004346";
        List<QualificationInfoVo> params = JSON.parseArray(param,QualificationInfoVo.class);
        saasBusinessScopeApi.saveOrUpdateBusinessScope(params,employId,organSign);
    }

    @Test
    public void getScope(){
        SaasBusinessScopeDto param = new SaasBusinessScopeDto();
        param.setOrganSign("ZHL00004135");
        ResultVO<List<QualificationInfoVo>> resultVO = saasBusinessScopeApi.selectNBusinessScopeNew(param);
        System.out.println(JSON.toJSONString(resultVO));
    }

    @Test
    public void refreshData(){
        QueryCertificateEnum queryCertificateEnum = QueryCertificateEnum.PURCHASER;

        List<QueryCertificateConfig> queryCertificateConfigs = JSON.parseArray(queryCertificateConfig, QueryCertificateConfig.class);
        Map<Integer, QueryCertificateConfig> certificateConfigMap = queryCertificateConfigs.stream().collect(Collectors.toMap(QueryCertificateConfig::getType, p -> p, (k1, k2) -> k1));
        int key = queryCertificateEnum.getKey();
        QueryCertificateConfig queryCertificateConfig = certificateConfigMap.get(key);
        List<Integer> ids = queryCertificateConfig.getIds();
        List<SaasCertificateBusinessScope> scopeList = certificateBusinessScopeMapper.selectByTypes(ids);
        Map<Integer, Integer> scopeAndType = scopeList.stream().collect(Collectors.toMap(SaasCertificateBusinessScope::getBusinessScope,SaasCertificateBusinessScope::getCertificateType,(k1,k2) -> k1));

        SaaSDrugstoreDto query = new SaaSDrugstoreDto();
        query.setBizModel(BizModelEnum.CHAIN_STORE.getKey());
        List<SaaSDrugstoreDto> condition = drugstoreApi.getSaaSDrugstoreByCondition(query);
        for (SaaSDrugstoreDto drugstore:condition) {
            SaasBusinessScopeDto saasBusinessScopeDto = new SaasBusinessScopeDto();
            saasBusinessScopeDto.setOrganSign(drugstore.getOrganSign());
            List<SaasBusinessScopePo> saasBusinessScopeDtos = saasBusinessScopePoMapper.selectSaasBusinessScopeList(saasBusinessScopeDto);
            System.out.println(drugstore.getOrganSign() + ":" + saasBusinessScopeDtos.size());
            if (CollectionUtils.isNotEmpty(saasBusinessScopeDtos)) {
                for (SaasBusinessScopePo item : saasBusinessScopeDtos) {
                    item.setCertificateType(null == scopeAndType.get(item.getDictId()) ? 0 : scopeAndType.get(item.getDictId()));
                    saasBusinessScopePoMapper.updateByPrimaryKeySelective(item);
                }
            }
        }

    }

    @Test
    public void deleteBussinessScope(){
        saasBusinessScopeApi.RefreshParent("ZHL00004346",null);
    }

}
