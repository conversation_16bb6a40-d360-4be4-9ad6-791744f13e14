package com.xyy.user.provider;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.common.module.api.SysConfigApi;
import com.xyy.common.module.dto.SystemConfigDto;
import com.xyy.common.module.dto.SystemConfigWEBDto;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.UserApi;
import com.xyy.user.module.dto.SubAccountLoginDto;
import com.xyy.user.module.dto.result.LoginDto;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class SystemConfigUnitTest {

    @Reference(version = "0.0.1", url="dubbo://localhost:20880")
    private SysConfigApi sysConfigApi;

    @Test
    public void selectExisted() {
        SystemConfigDto config = sysConfigApi.querySystemConfig("ZHL00001411");
        System.out.println(JSONObject.toJSONString(config));
    }

    @Test
    public void selectNonExisted() {
        ResultVO<SystemConfigWEBDto> res = sysConfigApi.getWEBSysConfig("ZHL0000abcd");
        System.out.println(JSONObject.toJSONString(res.getResult()));
    }

    @Test
    public void update() {
        String organSign = "ZHL0000abcd";
        ResultVO<SystemConfigWEBDto> res = sysConfigApi.getWEBSysConfig(organSign);
        SystemConfigWEBDto config = res.getResult();
        config.setAccountSetYn((byte) 1);
        sysConfigApi.updateForWEB(config, organSign);
        res = sysConfigApi.getWEBSysConfig(organSign);
        System.out.println(JSONObject.toJSONString(res.getResult()));
    }

}
