/*
package com.xyy.user.provider;

import com.baidu.aip.face.AipFace;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.provider.common.constants.ResultCodeMessage;
import com.xyy.user.provider.common.util.HttpUtil;
import com.xyy.user.provider.common.util.JedisUtil;
import com.xyy.user.provider.common.util.JpushClientUtil;
import com.xyy.user.provider.module.dao.SaasDrugstoreMapper;
import com.xyy.user.provider.module.entity.SaasDrugstore;
import org.json.JSONObject;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest
public class XyySaasUserProviderApplicationTests {

    //设置APPID/AK/SK
    public static final String APP_ID = "14877778";
    public static final String API_KEY = "Fm6FrpvGhdfGtrp4fxZaqZLa";
    public static final String SECRET_KEY = "yMRI7LURuEiXe3dQ3tDyq5zTGjYxQPET";

    public static void main(String[] args) throws Exception {
//        // 初始化一个AipFace
//        AipFace client = new AipFace(APP_ID, API_KEY, SECRET_KEY);
//
//        // 可选：设置网络连接参数
//        client.setConnectionTimeoutInMillis(2000);
//        client.setSocketTimeoutInMillis(60000);
//
//        JSONObject res = client.search("asdasdasd", "BASE64", "saas_group", new HashMap<>());

    }

    @Test
    public void contextLoads() throws Exception{
    }

    */
/**
     * 调用药帮忙登录接口，返回机构code
     * @param result
     * @param username
     * @param password
     * @return
     *//*

    private String remoteYBMLogin(ResultVO result, String username, String password) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            String resInfo = HttpUtil
                    .postAsString("http://dev2-www.ybm100.com/out/validate.json?name="
                            + username
                            + "&password="
                            + password);

            Map<String, Object> messageInfo = null;
            messageInfo = objectMapper.readValue(resInfo, Map.class);
            if ("success".equals(messageInfo.get("status"))) {
                return String.valueOf(messageInfo.get("token"));
            } else {
                result.setCode(ResultCodeMessage.YBM_USER_ERROR_CODE);
                result.setMsg(ResultCodeMessage.YBM_USER_ERROR_MESSAGE);
                return null;
            }
        } catch (Exception e) {
            result.setCode(ResultCodeMessage.REMOTE_YBM_ERROR_CODE);
            result.setMsg(ResultCodeMessage.REMOTE_YBM_ERROR_MESSAGE);
            return null;
        }
    }
}
*/
