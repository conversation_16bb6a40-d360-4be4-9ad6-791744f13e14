package com.xyy.user.provider;

import com.xyy.user.module.api.GrayScaleApi;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class GrayScaleUnitTest {

    @Autowired
    private GrayScaleApi grayScaleApi;

    @Test
    public void checkSuccess() {
        boolean result = grayScaleApi.checkGray("ZHL00001411");
        System.out.println(result);
        Assert.assertEquals(result, true);
    }

    @Test
    public void checkEmpty() {
        boolean result = grayScaleApi.checkGray("");
        System.out.println(result);
        Assert.assertEquals(result, false);
    }

    @Test
    public void checkSuccess2() {
        boolean result = grayScaleApi.checkGray("ZHL000014111");
        System.out.println(result);
        Assert.assertEquals(result, false);
    }
}
