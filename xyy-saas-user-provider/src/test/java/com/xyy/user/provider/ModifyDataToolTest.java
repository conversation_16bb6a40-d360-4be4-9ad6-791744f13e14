package com.xyy.user.provider;

import com.alibaba.fastjson.JSON;
import com.xyy.common.module.enums.OperateTypeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.ModifyDataToolApi;
import com.xyy.user.module.dto.restructure.ModifyDrugstoreAreaDto;
import com.xyy.user.module.dto.restructure.ModifyPhoneDto;
import com.xyy.user.module.dto.restructure.UnlockEmployeeDto;
import com.xyy.user.module.dto.restructure.VirtualDrugstoreDTO;
import com.xyy.user.provider.module.entity.SaasModifyRecord;
import com.xyy.user.provider.module.service.SaasModifyRecordService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class ModifyDataToolTest {
//    @Reference(version = "0.0.1", url="dubbo://localhost:20880")
    @Autowired
    private ModifyDataToolApi modifyDataToolApi;
    @Autowired
    private SaasModifyRecordService saasModifyRecordService;

    @Test
    public void modifyAdminPhoneTest(){
        ModifyPhoneDto modifyPhoneDto = new ModifyPhoneDto();
        modifyPhoneDto.setOrganSign("ZHL00003308");
        modifyPhoneDto.setAdminPhone("16872120103");
        modifyPhoneDto.setRelationWorker("张三");
        ResultVO<Boolean> booleanResultVO = modifyDataToolApi.modifyAdminPhone(modifyPhoneDto);
        log.info("booleanResultVO:{}", JSON.toJSONString(booleanResultVO));
    }

    @Test
    public void modifyDrugstoreArea(){
        ModifyDrugstoreAreaDto modifyPhoneDto = new ModifyDrugstoreAreaDto();
        modifyPhoneDto.setOrganSign("ZHL00001599");
        modifyPhoneDto.setProvince("湖北省");
        modifyPhoneDto.setCity("武汉市");
        modifyPhoneDto.setArea("江岸区");
        ResultVO<Boolean> booleanResultVO = modifyDataToolApi.modifyDrugstoreArea(modifyPhoneDto);
        log.info("booleanResultVO:{}", JSON.toJSONString(booleanResultVO));
    }

    @Test
    public void unLockEmployeeTest(){
        UnlockEmployeeDto unlockEmployeeDto = new UnlockEmployeeDto();
        unlockEmployeeDto.setEmployeeId(55893);
        ResultVO<Boolean> booleanResultVO = modifyDataToolApi.unLockEmployee(unlockEmployeeDto);
        log.info("booleanResultVO:{}", JSON.toJSONString(booleanResultVO));
    }

    @Test
    public void registerVirtualDrugstoresTest() {
        List<VirtualDrugstoreDTO> virtualDrugstoreList = new ArrayList<>();
        VirtualDrugstoreDTO virtualDrugstore1 = new VirtualDrugstoreDTO();
        // 1	湖北富迪实业股份有限公司大新超市	湖北省	仙桃市	91429004MA48BDWL2D	仙桃市大新路	雷凤兰	***********
        virtualDrugstore1.setPhone("***********");
        virtualDrugstore1.setPassword("abc123456");
        virtualDrugstore1.setEmployeeName("雷凤兰");
        virtualDrugstore1.setDrugstoreName("湖北富迪实业股份有限公司大新超市");
        virtualDrugstore1.setMaintainOperatorUserId(3634);
        virtualDrugstore1.setProvince("湖北省");
        virtualDrugstore1.setCity("仙桃市");
        virtualDrugstore1.setArea(null);
        virtualDrugstore1.setAddress("仙桃市大新路");
        virtualDrugstore1.setBusinessLicenseName("湖北富迪实业股份有限公司大新超市");
        virtualDrugstore1.setBusinessLicenseNumber("91429004MA48BDWL2D");
        virtualDrugstoreList.add(virtualDrugstore1);
        ResultVO<List<Map<String, String>>> listResultVO = modifyDataToolApi.registerVirtualDrugstores(virtualDrugstoreList);
        log.info("listResultVO:{}", JSON.toJSONString(listResultVO));

    }

    @Test
    public void saasModifyRecordTest(){
        SaasModifyRecord modifyRecord = new SaasModifyRecord();
        modifyRecord.setOrganSign("ZHL001");
        modifyRecord.setOperateType(OperateTypeEnum.drugstoreAdminPhone.getOperateType());
        modifyRecord.setOperatorName("wsf");
        modifyRecord.setOldData("123456");
        modifyRecord.setNewData("654321");
        saasModifyRecordService.addRecord(modifyRecord);
    }
}
