package com.xyy.user.provider;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.CertificateTypeApi;
import com.xyy.user.module.dto.restructure.CertificateTypeVO;
import com.xyy.user.module.enums.QueryCertificateEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;


@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class CertificateTypeApiTest {
    @Reference(version = "0.0.1", url="dubbo://192.168.31.61:20880")
    public CertificateTypeApi certificateTypeApi;

    @Test
    public void test1(){
        ResultVO<List<CertificateTypeVO>> certificateTypeList = certificateTypeApi.getCertificateTypeList(QueryCertificateEnum.SUPPLIER);
        log.info("certificateTypeList:{}", JSON.toJSONString(certificateTypeList));
    }
}
