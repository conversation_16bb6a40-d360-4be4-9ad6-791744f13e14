package com.xyy.user.provider;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.emule.dto.SynchDataParamDto;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.PermissionApi;
import com.xyy.user.module.api.RefreshRolePermissionApi;
import com.xyy.user.module.dto.EmployeeExportDto;
import com.xyy.user.module.dto.PermissionDto;
import com.xyy.user.module.dto.menu.PermissionRoleMenuDto;
import com.xyy.user.provider.module.api.impl.export.EmployeeUserGeneralExportImpl;
import com.xyy.user.provider.module.entity.SaasPermission;
import com.xyy.user.provider.module.service.PermissionService;
import com.xyy.user.provider.module.service.RolePermissionService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class PermissionServiceTest {
    @Autowired
    private PermissionService permissionService;

    @Autowired
    private RolePermissionService rolePermissionService;

    @Autowired
    private EmployeeUserGeneralExportImpl employeeUserGeneralExport;

    @Reference(version = "0.0.1", url="dubbo://localhost:20880")
    private RefreshRolePermissionApi refreshRolePermissionApi;

    @Reference(version = "0.0.1", url="dubbo://localhost:20880")
    private PermissionApi permissionApi;


    @Test
    public void findAllPermissionByIdsTest(){
        List<Integer> ids = new ArrayList<>();
        ids.add(103365);
        ids.add(103368);
        ids.add(103370);
        List<Integer> allPermissionByIds = permissionService.findAllPermissionIdsByIds(ids);
        log.info("allPermissionByIds:{}", JSON.toJSONString(allPermissionByIds));
    }

    @Test
    public void refreshByOrganSignTest(){
        ResultVO<Boolean> booleanResultVO = refreshRolePermissionApi.refreshByOrganSign("ZHL00002869");
        log.info("allPermissionByIds:{}", JSON.toJSONString(booleanResultVO));
    }

    @Test
    public void refreshAllOrganSignTest(){
        ResultVO<Boolean> booleanResultVO = refreshRolePermissionApi.refreshByPage();
        log.info("booleanResultVO:{}", JSON.toJSONString(booleanResultVO));
    }

    @Test
    public void getPermissionMenuByRoleId(){
        Map<String, Object> zhl00001599 = permissionService.getPermissionMenuByRoleId(282, "ZHL00002500");
        log.info("booleanResultVO:{}", JSON.toJSONString(zhl00001599));
    }

    @Test
    public void findPermissionByRoleId(){
        List<Map<String, Object>> zhl00001599 = permissionService.findPermissionByRoleId(10, "ZHL00001599");
        log.info("booleanResultVO:{}", JSON.toJSONString(zhl00001599));
    }

    @Test
    public void getRoleMenu(){
        List<PermissionRoleMenuDto> zhl00002500 = rolePermissionService.getRoleMenu(282, "ZHL00003098");
//        List<PermissionRoleMenuDto> zhl00002500 = rolePermissionService.getRoleMenu(1200, "ZHL00002349");
//        List<PermissionRoleMenuDto> zhl00002500 = rolePermissionService.getRoleMenu(920, "ZHL00002349");
        log.info("booleanResultVO:{}", JSON.toJSONString(zhl00002500));
    }

    @Test
    public void findPermissionByEmployId(){
        List<PermissionDto> employId1 = permissionApi.findPermissionByEmployId(113117);
        log.info("employId1:{}",JSON.toJSONString(employId1));
        Map<String, Boolean> employId2 = permissionApi.findAppPermissionByEmployId(113117);
        log.info("employId2:{}",JSON.toJSONString(employId2));
        List<PermissionDto> employId3 = permissionApi.getAppPermissionByEmployId(113149);
        log.info("employId3:{}",JSON.toJSONString(employId3));
    }


    @Test
    public void EmployeeUserGeneralExportImpl(){
        SynchDataParamDto synchDataParamDto = new SynchDataParamDto();
        synchDataParamDto.setPageNum(1);
        synchDataParamDto.setPageSize(20);

        EmployeeExportDto employeeExportDto = new EmployeeExportDto();
        employeeExportDto.setRoleId(1);
        employeeExportDto.setOrganSign("ZHL00002349");
        synchDataParamDto.setParamInfo(JSON.toJSONString(employeeExportDto));
        ResultVO<PageInfo<JSONObject>> pageInfoResultVO = employeeUserGeneralExport.downExportData(synchDataParamDto);
//        List<PermissionRoleMenuDto> zhl00002500 = rolePermissionService.getRoleMenu(1200, "ZHL00002349");
//        List<PermissionRoleMenuDto> zhl00002500 = rolePermissionService.getRoleMenu(920, "ZHL00002349");
        log.info("booleanResultVO:{}", JSON.toJSONString(pageInfoResultVO));
    }

    @Test
    public void testPermiss(){
        List<SaasPermission> permissions1 = permissionService.findPermissionByEmployId(115739);
        List<SaasPermission> permissions2 = permissionService.getAppPermissionByEmployId(113149);
        Map<String, Boolean> map = permissionService.findAppPermissionByEmployId(113149);
        System.out.println(1);


    }

}
