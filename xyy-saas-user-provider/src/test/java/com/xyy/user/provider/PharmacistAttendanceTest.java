package com.xyy.user.provider;


import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.emule.api.SynchExportDataApi;
import com.xyy.emule.dto.SynchDataParamDto;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.GspPharmacistAttendanceApi;
import com.xyy.user.module.api.UserApi;
import com.xyy.user.module.dto.restructure.GspPharmacistAttendanceDto;
import com.xyy.user.module.dto.restructure.GspPharmacistAttendancePo;
import com.xyy.user.module.dto.restructure.QueryFingerprintParam;
import com.xyy.user.module.dto.restructure.QueryGspPharmacistAttendanceDto;
import com.xyy.user.provider.module.api.impl.PharmacistAttendanceExportDataImpl;
import com.xyy.user.provider.module.dao.SaasEmployeeFingerprintMapper;
import com.xyy.user.provider.module.entity.SaasEmployeeFingerprint;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class PharmacistAttendanceTest {
    @Reference(version = "0.0.1", url="dubbo://localhost:20880")
    private GspPharmacistAttendanceApi attendanceApi;

    @Autowired
    PharmacistAttendanceExportDataImpl synchExportDataApi;

    @Autowired
    private SaasEmployeeFingerprintMapper fingerprintMapper;


    @Test
    public void attendanceApiPageTest(){
        QueryGspPharmacistAttendanceDto queryGspPharmacistAttendanceDto = new QueryGspPharmacistAttendanceDto();
        queryGspPharmacistAttendanceDto.setPage(1);
        queryGspPharmacistAttendanceDto.setRows(50);
        queryGspPharmacistAttendanceDto.setOrganSign("ZHL00001599");
        ResultVO<PageInfo<GspPharmacistAttendancePo>> query = attendanceApi.query(queryGspPharmacistAttendanceDto);
        log.info("query result:{}", JSON.toJSONString(query));
    }

    @Test
    public void attendanceApiTest2(){
        GspPharmacistAttendanceDto gspPharmacistAttendanceDto = new GspPharmacistAttendanceDto();
        gspPharmacistAttendanceDto.setAttendanceMode(2);
        gspPharmacistAttendanceDto.setOrganSign("ZHL00000277");
        gspPharmacistAttendanceDto.setUserId("399");
        gspPharmacistAttendanceDto.setUsername("张三");
        ResultVO resultVO = attendanceApi.clockIn(gspPharmacistAttendanceDto);
        log.info("resultVO:{}", JSON.toJSONString(resultVO));
    }

    @Test
    public void synchExportDataApiTest(){
        SynchDataParamDto synchDataParamDto = new SynchDataParamDto();
        synchDataParamDto.setBusinessType("oStoreAttenRecordPharmacistsInfo");
        synchDataParamDto.setHeadColumn("realName,profession,identity,timeStr,type,attendanceMode");
        synchDataParamDto.setPageNum(2);
        synchDataParamDto.setPageSize(20);
        synchDataParamDto.setOrganSign("ZHL00001599");
        synchDataParamDto.setParamInfo("{\"username\":\"\",\"type\":0,\"startDateStr\":\"2020-01-26 00:00:00\",\"endDateStr\":\"2020-09-26 23:59:59\"}");
        ResultVO<PageInfo<JSONObject>> pageInfoResultVO = synchExportDataApi.downExportData(synchDataParamDto);
        log.info("pageInfoResultVO:{}", JSON.toJSONString(pageInfoResultVO));
    }

    @Test
    public void selectByParamTest(){
        QueryFingerprintParam param = new QueryFingerprintParam();
        List<Long> ids = new ArrayList<>();
        ids.add(55L);
        param.setIds(ids);
        List<SaasEmployeeFingerprint> saasEmployeeFingerprints = fingerprintMapper.selectByParam(param);
        log.info("saasEmployeeFingerprints:{}", JSON.toJSONString(saasEmployeeFingerprints));
    }

    @Test
    public void attendType(){
        GspPharmacistAttendanceDto gspPharmacistAttendanceDto = new GspPharmacistAttendanceDto();
        gspPharmacistAttendanceDto.setUserId("399");
        gspPharmacistAttendanceDto.setOrganSign("ZHL00000277");
        ResultVO resultVO = attendanceApi.attendanceType(gspPharmacistAttendanceDto);
        System.out.println(JSON.toJSONString(resultVO));
    }

}
