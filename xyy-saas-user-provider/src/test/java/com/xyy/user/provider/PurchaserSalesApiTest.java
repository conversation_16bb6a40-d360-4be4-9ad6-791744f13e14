package com.xyy.user.provider;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.PurchaserApi;
import com.xyy.user.module.api.PurchaserSalesApi;
import com.xyy.user.module.dto.restructure.QueryPurchaserSalesDto;
import com.xyy.user.module.dto.restructure.SaasPurchaserSalesDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/2/23
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
@ActiveProfiles(value = "test")
public class PurchaserSalesApiTest {
    @Resource
    private PurchaserSalesApi purchaserSalesApi;
    @Resource
    private PurchaserApi purchaserApi;

    @Test
    public void getPurchaserByPageByStatusTest(){
        QueryPurchaserSalesDto query = new QueryPurchaserSalesDto();
        query.setHeadquartersOrganSign("ZHL00004000");
        query.setCurrentEmployeeId(114602);
        query.setPageSize(10);
        query.setPageNum(1);
//        query.setAuditStatus(AuditingStatusEnum.LICENSE_STATUS_PENDING_AUDIT.getId());

        ResultVO<PageInfo<SaasPurchaserSalesDto>> resultVO = purchaserSalesApi.getPurchaserByPageByStatus(query);
        System.out.println(JSON.toJSONString(resultVO));
    }

    @Test
    public void editPurchaserTest(){
        String s = "{\"shortName\":\"33333\",\"purchaserNo\":\"ZHL00004754\",\"imageUrlList\":[]}";
        SaasPurchaserSalesDto dto = JSON.parseObject(s, SaasPurchaserSalesDto.class);
        ResultVO<Boolean> resultVO = purchaserSalesApi.editPurchaser(dto);
        System.out.println(JSON.toJSONString(resultVO));
    }
}
