package com.xyy.user.provider;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.api.EmployeefileApi;
import com.xyy.user.module.dto.EmployeefileRequestModel;
import com.xyy.user.module.dto.result.EmployeeDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class EmployeeFileTest {
    @Reference(version = "0.0.1", url = "dubbo://localhost:20880")
    private EmployeefileApi employeefileApi;

    @Reference(version = "0.0.1", url = "dubbo://localhost:20880")
    EmployeeApi employeeApi;

    @Test
    public void teste(){
        ResultVO<PageInfo<EmployeeDto>> zhl00005433 = employeeApi.queryAllEmployeeAndRolesByOrganSign("ZHL00005433", 1, 300);
        System.out.println();
    }
    /**
     * 按搜索条件查询【总部】下员工档案列表
     * @return
     */
    @Test
    public void getEmployeeFileByConditionList(){
        EmployeefileRequestModel model = new EmployeefileRequestModel();
        model.setHeadquartersOrganSign("ZHL00003284");
        model.setQueryType(2);
        model.setPageNum(1);
        model.setPageSize(50);
        model.setOrganSign("");
        ResultVO fingerprintInfo = employeefileApi.getEmployeeFileByConditionList(model);
        log.info("fingerprintInfo:{}", JSON.toJSONString(fingerprintInfo));
    }

    /**
     * 按搜索条件查询【药店】下员工档案列表
     * @return
     */
    @Test
    public void getEmployeefileByCondition(){
        EmployeefileRequestModel model = new EmployeefileRequestModel();
        model.setOrganSign("ZHL00002635");
        model.setPageNum(1);
        model.setPageSize(50);
        ResultVO fingerprintInfo = employeefileApi.getEmployeefileByCondition(model);
        log.info("fingerprintInfo:{}", JSON.toJSONString(fingerprintInfo));
    }


    /**
     * 获取员工档案详情
     * @return
     */
    @Test
    public void getEmployeefileDetailById(){
        ResultVO fingerprintInfo = employeefileApi.getEmployeefileDetailById(113850,"ZHL00001599");
        log.info("fingerprintInfo:{}", JSON.toJSONString(fingerprintInfo));
    }


}
