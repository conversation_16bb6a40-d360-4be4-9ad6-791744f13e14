package com.xyy.user.provider;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.common.util.MD5Util;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.api.UserApi;
import com.xyy.user.module.dto.SubAccountLoginDto;
import com.xyy.user.module.dto.SystemRoleQueryDto;
import com.xyy.user.module.dto.result.LoginDto;
import com.xyy.user.module.dto.result.QueryEmployeeVO;
import com.xyy.user.module.enums.SystemRoleCodeEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
public class LoginUnitTest {

    @Reference(version = "0.0.1", url="dubbo://localhost:20880")
    private UserApi userApi;

    @Test
    public void subLoginCorrect() {
        SubAccountLoginDto accountLoginDto = new SubAccountLoginDto();
        accountLoginDto.setOrganSign("ZHL00001411");
        accountLoginDto.setLoginName("***********");
        accountLoginDto.setPassword(MD5Util.getMD5("123456"));
        ResultVO<LoginDto> resultVO = userApi.subLogin(accountLoginDto);
        System.out.println(JSONObject.toJSONString(resultVO));
    }

    @Test
    public void subLoginEmptyOrganSign() {
        SubAccountLoginDto accountLoginDto = new SubAccountLoginDto();
        accountLoginDto.setOrganSign("");
        accountLoginDto.setLoginName("***********");
        accountLoginDto.setPassword(MD5Util.getMD5("123456"));
        ResultVO<LoginDto> resultVO = userApi.subLogin(accountLoginDto);
        System.out.println(JSONObject.toJSONString(resultVO));
    }

    @Test
    public void subLoginEmptyLoginName() {
        SubAccountLoginDto accountLoginDto = new SubAccountLoginDto();
        accountLoginDto.setOrganSign("ZHL00001411");
        accountLoginDto.setLoginName("");
        accountLoginDto.setPassword(MD5Util.getMD5("123456"));
        ResultVO<LoginDto> resultVO = userApi.subLogin(accountLoginDto);
        System.out.println(JSONObject.toJSONString(resultVO));
    }

    @Test
    public void subLoginEmptyWrongPassword() {
        SubAccountLoginDto accountLoginDto = new SubAccountLoginDto();
        accountLoginDto.setOrganSign("ZHL00001411");
        accountLoginDto.setLoginName("***********");
        accountLoginDto.setPassword(MD5Util.getMD5("1234567"));
        ResultVO<LoginDto> resultVO = userApi.subLogin(accountLoginDto);
        System.out.println(JSONObject.toJSONString(resultVO));
    }

    @Test
    public void subLoginEmptyChangeMainAccountSet() {
        SubAccountLoginDto accountLoginDto = new SubAccountLoginDto();
        accountLoginDto.setOrganSign("ZHL00001411");
        accountLoginDto.setLoginName("***********");
        accountLoginDto.setPassword(MD5Util.getMD5("123456"));
        accountLoginDto.setToken("eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************.svYvCtTIPAhsVCklfCa5Nhz8JUMEgY5weNsaOor1PFY");
        ResultVO<LoginDto> resultVO = userApi.subLogin(accountLoginDto);
        System.out.println(JSONObject.toJSONString(resultVO));
    }

    @Test
    public void subLoginEmptyChangeSubAccountSet() {
        SubAccountLoginDto accountLoginDto = new SubAccountLoginDto();
        accountLoginDto.setOrganSign("ZHL00003776");
        accountLoginDto.setLoginName("songwei1");
        accountLoginDto.setPassword(MD5Util.getMD5("abc123456"));
        accountLoginDto.setToken("eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************.Veuml8QGq8mYYX6fdM4R6IK3YRxa2lmO_y7wZcLoedo");
        ResultVO<LoginDto> resultVO = userApi.subLogin(accountLoginDto);
        System.out.println(JSONObject.toJSONString(resultVO));
    }

}
