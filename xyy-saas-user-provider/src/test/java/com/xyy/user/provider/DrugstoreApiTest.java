package com.xyy.user.provider;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.api.PermissionApi;
import com.xyy.user.module.api.SaasPackageBindApi;
import com.xyy.user.module.dto.*;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreExtendDto;
import com.xyy.user.module.dto.result.QueryDrugstoreDto;
import com.xyy.user.module.enums.DrugstoreBizModelEnum;
import com.xyy.user.module.enums.DrugstorePackageStatusEnum;
import com.xyy.user.module.enums.DrugstoreTypeEnum;
import com.xyy.user.provider.module.dao.SaasDrugstoreMapper;
import com.xyy.user.provider.module.entity.SaasDrugstore;
import com.xyy.user.provider.module.service.CommonDrugstoreService;
import com.xyy.user.provider.module.service.DrugstoreService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class DrugstoreApiTest {
//    @Reference(version = "0.0.1", url="dubbo://localhost:20880")
    @Reference(version = "0.0.1", url = "dubbo://localhost:20880")
    private DrugstoreApi drugstoreApi;
    @Reference(version = "0.0.1", url = "dubbo://localhost:20880")
    private CommonDrugstoreService drugstoreService;

    @Reference(version = "0.0.1", url = "dubbo://localhost:20880")
    private PermissionApi permissionApi;

    @Autowired
    private SaasPackageBindApi saasPackageBindApi;

    @Test
    public void drugstoreTest(){
        SaaSDrugstoreDto saaSDrugstoreDto = new SaaSDrugstoreDto();
        List<Byte> typeList = new ArrayList<>();
        typeList.add((byte) 7);
        typeList.add((byte) 8);
        saaSDrugstoreDto.setTypeList(typeList);
        saaSDrugstoreDto.setHeadquartersOrganSign("ZHL00002334");
        List<SaaSDrugstoreDto> saaSDrugstoreByCondition = drugstoreApi.getSaaSDrugstoreByCondition(saaSDrugstoreDto);
        log.info("saaSDrugstoreByCondition:{}", JSON.toJSONString(saaSDrugstoreByCondition));
    }
    @Test
    public void drugstoreApiTest(){
        SaasDrugstoreDto drugstoreDto = new SaasDrugstoreDto();
        drugstoreDto.setBizModel((byte) DrugstoreBizModelEnum.CHAIN_STORE.getKey());
        drugstoreDto.setOrganSignType((byte) DrugstoreTypeEnum.HEADQUARTERS.getKey());
        drugstoreDto.setPageNum(1);
        drugstoreDto.setPageSize(20);
        ResultVO<PageInfo<SaasDrugstoreDto>> drugstoreByCondition = drugstoreApi.getDrugstoreByCondition(drugstoreDto);
        log.info("drugstoreByCondition:{}", JSON.toJSONString(drugstoreByCondition));
    }


    @Test
    public void test2(){
        List<SaasDrugstore> allListByPage = drugstoreService.getAllListByPage();
        log.info("allListByPage:{}", JSON.toJSONString(allListByPage));
    }

    @Test
    public void queryDrugstoreByOrganSign(){
        Long startTime =System.currentTimeMillis();
        ResultVO<QueryDrugstoreDto> queryDrugstoreDtoResultVO = drugstoreApi.queryDrugstoreByOrganSign("ZHL00003130");
        Long stopTime  =System.currentTimeMillis();
        Long time  = stopTime-startTime;
        log.info("queryDrugstoreDtoResultVO:{}", JSON.toJSONString(queryDrugstoreDtoResultVO));
    }

    @Test
    public void updateQualificationSubmission(){
        UpdateQualificationSubmissionInfoDto infoDto = new UpdateQualificationSubmissionInfoDto();
        infoDto.setInitiatorUserId(6561);
        infoDto.setBusinessLicenseName("小药药产品部");
        infoDto.setBusinessLicenseNumber("911191063252482236");
        infoDto.setManagerName("小药药");
        infoDto.setContactPhone("***********");
        infoDto.setDrugstoreName("小药药药房");
        infoDto.setProvince("北京市");
        infoDto.setCity("北京城区");
        infoDto.setArea("石景山区");
        infoDto.setType((byte)14);
        infoDto.setAddress("望京SOHO");
        infoDto.setBusinessLicenseImg("https://files.test.ybm100.com/G2/M00/13/66/Cgo0015gkUKAHp-jAAAdj3ERe8g564.png?token=ad77949b75458fb1cdcfbb03e1490344&ts=**********");
        infoDto.setPharmaceuticalTradingLicenseImg("https://files.test.ybm100.com/G2/M00/13/62/Cgo01F5gkUiACP9fAAAdj3ERe8g222.png?token=9df36abf40103d3e37110ea18590ef84&ts=**********");
        infoDto.setQualityManagementLicenseImg("https://files.test.ybm100.com/G2/M00/13/66/Cgo0015gkUuANGt0AAAdj3ERe8g639.png?token=230e425c49b5e19d7a6205caba98081a&ts=**********");
        infoDto.setBizModel((byte)2);
        infoDto.setOrganSignType((byte)3);
        infoDto.setOrganSign("ZHL00002500");
        infoDto.setStorehouseYn((byte)1);

        ResultVO<Boolean> queryDrugstoreDtoResultVO = drugstoreApi.updateQualificationSubmission(infoDto);
        log.info("queryDrugstoreDtoResultVO:{}", JSON.toJSONString(queryDrugstoreDtoResultVO));
    }


    @Test
    public void getDrugstoreAndHeadquartersAndPackageBindInfoByOrganSignNoPage(){
        SaaSDrugstoreDto saaSDrugstoreDto = new SaaSDrugstoreDto();
        saaSDrugstoreDto.setNeBizModel((byte)1);
        Long startTime =System.currentTimeMillis();
        List<SaaSDrugstoreExtendDto> drugstoreAndHeadquartersAndPackageBindInfoByOrganSignNoPage = drugstoreApi.getDrugstoreAndHeadquartersAndPackageBindInfoByOrganSignNoPage(saaSDrugstoreDto);
        Long stopTime  =System.currentTimeMillis();
        Long time  = stopTime-startTime;
        log.info("queryDrugstoreDtoResultVO:{}", JSON.toJSONString(drugstoreAndHeadquartersAndPackageBindInfoByOrganSignNoPage));
    }

    @Test
    public void findAuditPageListNoPage(){
        DrugstoreAuditDto drugstoreAuditDto = new DrugstoreAuditDto();
        Long startTime =System.currentTimeMillis();
        List<SaasAuditDrugstoreRecordDto> auditPageListNoPage = drugstoreApi.findAuditPageListNoPage(drugstoreAuditDto);
        Long stopTime  =System.currentTimeMillis();
        Long time  = stopTime-startTime;
        log.info("queryDrugstoreDtoResultVO:{}", JSON.toJSONString(auditPageListNoPage));
    }


    @Test
    public void changeStatus(){
        int yuan = drugstoreApi.changeStatus((byte) 1, "yuan", 93627);
        log.info("queryDrugstoreDtoResultVO:{}", JSON.toJSONString(yuan));
    }

    @Test
    public void testGetDrugstoreByCondition() {
        SaasDrugstoreDto drugstoreDto = new SaasDrugstoreDto();
        drugstoreDto.setPageNum(1);
        drugstoreDto.setPageSize(5000);
        ResultVO<PageInfo<SaasDrugstoreDto>> drugstoreByCondition = drugstoreApi.getDrugstoreByCondition(drugstoreDto);
        log.info("drugstoreByCondition:{}", JSON.toJSON(drugstoreByCondition));
    }

    @Test
    public void testFindDrugstoreById() {
        SaasDrugstoreDto drugstore = drugstoreApi.findDrugstoreById(93151);
        log.info("drugstore:{}", JSON.toJSONString(drugstore));

    }

    @Test
    public void queryChainByPage(){
        String data = "{\"organSign\":\"\",\"drugstoreName\":\"\",\"departmentType\":\"2\",\"pageSize\":50,\"pageNo\":1,\"headquartersOrganSign\":\"ZHL00002349\"}";
        DrugstoreQueryDto drugstoreQueryDto = JSON.parseObject(data,DrugstoreQueryDto.class);
        SaasAuditDrugstoreRecordDto saaSDrugstoreDto = new SaasAuditDrugstoreRecordDto();
        BeanUtils.copyProperties(drugstoreQueryDto, saaSDrugstoreDto);
        log.info("a:{}",JSON.toJSONString(drugstoreQueryDto));
        log.info("b:{}",JSON.toJSONString(saaSDrugstoreDto));
    }

    @Test
    public void queryPack(){
        SaaSDrugstoreDto saaSDrugstoreDto = new SaaSDrugstoreDto();
        //saaSDrugstoreDto.setBizModel((byte) 1);
        //saaSDrugstoreDto.setOrganSign("ZHL00005969");
        PageInfo<SaaSDrugstoreExtendDto> infoByOrganSign = drugstoreApi.getDrugstoreAndHeadquartersAndPackageBindInfoByOrganSign(saaSDrugstoreDto, 1, 50);
        log.info("result:{}",JSON.toJSONString(infoByOrganSign));
    }

    @Test
    public void pack(){
        String data = "{\"organSign\":\"ZHL00006126\",\"status\":\"2\"}";
        SaasPackageBindInfoDto dto = JSON.parseObject(data,SaasPackageBindInfoDto.class);
        int status = dto.getStatus();
        if (status == 1) {
            dto.setStatus((byte) DrugstorePackageStatusEnum.EFFECTIVE.getKey());
        } else if(status == 2) {
            dto.setStatus((byte) DrugstorePackageStatusEnum.TERMINATION.getKey());
        }
        ResultVO result = ResultVO.createSuccess(saasPackageBindApi.updateConditionByOrganSign(dto));
        log.info("result:{}",JSON.toJSONString(result));
    }

    @Autowired
    DrugstoreService storeService;
    @Autowired
    private SaasDrugstoreMapper drugstoreMapper;

    @Test
    public void bindDefaultPack(){
        SaasDrugstore drugstore = drugstoreMapper.selectDrugstoreByOrganSign("ZHL00007121");
        storeService.bindDefaultPack(drugstore);
    }
}
