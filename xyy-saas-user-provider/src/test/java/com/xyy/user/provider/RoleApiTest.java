package com.xyy.user.provider;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.RoleApi;
import com.xyy.user.module.api.UserApi;
import com.xyy.user.module.dto.result.RoleDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@Slf4j
public class RoleApiTest {
    @Reference(version = "0.0.1", url="dubbo://localhost:20880")
    private RoleApi roleApi;

    @Test
    public void syncDataTest(){
        Map<String, Object> map = new HashMap<>();
        map.put("organSign","ZHL00002869");
        map.put("baseVersion","0");
        List list = roleApi.syncData(map);
        log.info("list:{}", JSON.toJSONString(list));
    }

    @Test
    public void queryAllRoleList(){
//        ResultVO<List<RoleDto>> resultVO = roleApi.queryAllRoleList("ZHL00005346");
//        log.info("list:{}", JSON.toJSONString(resultVO));
    }
}
