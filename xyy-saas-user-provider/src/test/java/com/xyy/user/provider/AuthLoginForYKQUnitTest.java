package com.xyy.user.provider;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.UserApi;
import com.xyy.user.module.dto.AuthLoginDto;
import com.xyy.user.module.dto.restructure.SaaSEmployeeDto;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class AuthLoginForYKQUnitTest {

    @Reference(version = "0.0.1")
    private UserApi userApi;

    @Test
    public void success1() throws Exception{
        AuthLoginDto authLoginDto = new AuthLoginDto();
        authLoginDto.setOrganSign("药小二er1");
        authLoginDto.setLoginName("5");
        authLoginDto.setPassword("e10adc3949ba59abbe56e057f20f883e");
        ResultVO<SaaSEmployeeDto> resultVO = userApi.authLoginForYKQ(authLoginDto);
        System.out.println(JSONObject.toJSONString(resultVO));
    }

    @Test
    public void success2() throws Exception{
        AuthLoginDto authLoginDto = new AuthLoginDto();
        authLoginDto.setOrganSign("15101118625");
        authLoginDto.setLoginName("15101118625");
        authLoginDto.setPassword("e10adc3949ba59abbe56e057f20f883e");
        ResultVO<SaaSEmployeeDto> resultVO = userApi.authLoginForYKQ(authLoginDto);
        System.out.println(JSONObject.toJSONString(resultVO));
    }

    @Test
    public void wrongOrganSign() throws Exception{
        AuthLoginDto authLoginDto = new AuthLoginDto();
        authLoginDto.setOrganSign("ZHL000014111");
        authLoginDto.setLoginName("15101118625");
        authLoginDto.setPassword("e10adc3949ba59abbe56e057f20f883e");
        ResultVO<SaaSEmployeeDto> resultVO = userApi.authLoginForYKQ(authLoginDto);
        System.out.println(JSONObject.toJSONString(resultVO));
    }

    @Test
    public void wrongPassword() throws Exception{
        AuthLoginDto authLoginDto = new AuthLoginDto();
        authLoginDto.setOrganSign("ZHL000014111");
        authLoginDto.setLoginName("15101118625");
        authLoginDto.setPassword("1");
        ResultVO<SaaSEmployeeDto> resultVO = userApi.authLoginForYKQ(authLoginDto);
        System.out.println(JSONObject.toJSONString(resultVO));
    }

}
