package com.xyy.user.provider;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.EmployeeHealthfileApi;
import com.xyy.user.module.dto.healthfile.EmployeeHealthfileVO;
import com.xyy.user.module.dto.healthfile.HealthfileRequestModel;
import com.xyy.user.module.dto.restructure.SaasImgUrlDto;
import com.xyy.user.module.enums.ImageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class EmployeeHealthTest {
    @Reference(version = "0.0.1", url = "dubbo://localhost:20880")
    private EmployeeHealthfileApi employeeHealthfileApi;

    /**
     * 查询【门店下】员工健康档案列表
     * @return
     */
    @Test
    public void getEmployeeFileByConditionList(){
        HealthfileRequestModel model = new HealthfileRequestModel();
        model.setBillStartDate("2010-04-23");
        model.setBillEndDate("2020-09-23");
        model.setOrganSign("ZHL00001599");
        model.setPageNum(1);
        model.setPageSize(50);
        ResultVO fingerprintInfo = employeeHealthfileApi.getHealthfileByCondition(model);
        log.info("fingerprintInfo:{}", JSON.toJSONString(fingerprintInfo));
    }



    /**
     * 查询【总部】员工健康档案列表
     * @return
     */
    @Test
    public void getHealthFileByConditionList(){
        HealthfileRequestModel model = new HealthfileRequestModel();
        model.setBillStartDate("2021-02-23");
        model.setBillEndDate("2021-02-23");
        model.setHeadquartersOrganSign("ZHL00005399");
        model.setPageNum(1);
        model.setPageSize(50);
        model.setQueryType(1);
        ResultVO fingerprintInfo = null;
        try {
            fingerprintInfo = employeeHealthfileApi.getHealthFileByConditionList(model);
        } catch (Exception e) {
            e.printStackTrace();
        }
        log.info("fingerprintInfo:{}", JSON.toJSONString(fingerprintInfo));
    }



    /**
     * 查询【门店下】员工健康档案近效期预警信息
     * @return
     */
    @Test
    public void getHealthFileByConditionWarn(){
        HealthfileRequestModel model = new HealthfileRequestModel();
        model.setOrganSign("ZHL00000268");
        model.setPageNum(1);
        model.setPageSize(50);
        ResultVO fingerprintInfo = employeeHealthfileApi.getHealthFileByConditionWarn(model);
        log.info("fingerprintInfo:{}", JSON.toJSONString(fingerprintInfo));
    }

    /**
     * 查询【总部】员工健康档案近效期预警信息
     * @return
     */
    @Test
    public void getHealthFileByConditionWarnList(){
        HealthfileRequestModel model = new HealthfileRequestModel();
        model.setHeadquartersOrganSign("ZHL00000268");
        model.setPageNum(1);
        model.setPageSize(50);
        model.setQueryType(1);
        ResultVO fingerprintInfo = employeeHealthfileApi.getHealthFileByConditionWarnList(model);
        log.info("fingerprintInfo:{}", JSON.toJSONString(fingerprintInfo));
    }


    /**
     * 员工健康档案查询详情
     * @return
     */
    @Test
    public void getHealthFileDetailById(){
        ResultVO fingerprintInfo = employeeHealthfileApi.getHealthfileDetailById("JK4532","ZHL00005549");
        log.info("fingerprintInfo:{}", JSON.toJSONString(fingerprintInfo));
    }

//
//    {
//        "billDate": "2020-09-25",
//            "examinationDate": "2020-09-25",
//            "employeeName": "童雅芳",
//            "examinationOrg": "adoudoduodu",
//            "examinationItem": "111111",
//            "result": "1111",
//            "takeSteps": "111",
//            "remark": "11",
//            "employeeId": 4290
//    }

    /**
     * 新增
     * @return
     */
    @Test
    public void addEmployeeHealthfile(){
        EmployeeHealthfileVO employeeHealthfileVO= new EmployeeHealthfileVO();
        employeeHealthfileVO.setBillDate("2020-09-25");
        employeeHealthfileVO.setExaminationDate("2020-09-25");
        employeeHealthfileVO.setExaminationOrg("haahhaah");
        employeeHealthfileVO.setExaminationItem("222222");
        employeeHealthfileVO.setResult("222222");
        employeeHealthfileVO.setTakeSteps("222222");
        employeeHealthfileVO.setRemark("222222");
        employeeHealthfileVO.setEmployeeId(4290);
        employeeHealthfileVO.setOrganSign("ZHL00001599");
        employeeHealthfileVO.setCreateId(9742);

        //员工照片信息
        List<SaasImgUrlDto> employeeHealthImg = new ArrayList<>();
        SaasImgUrlDto saasImgUrlDto= new SaasImgUrlDto();
        saasImgUrlDto.setType(ImageTypeEnum.EMPLOYEE_HEALTH_IMG.getKey());
        saasImgUrlDto.setImgUrl("https://files.test.ybm100.com/G2/M00/17/23/Cgo01F7i7B-AQrDQAAcQI35m4Wk799.png");
        employeeHealthImg.add(saasImgUrlDto);
        employeeHealthfileVO.setEmployeeHealthImg(employeeHealthImg);


        ResultVO fingerprintInfo = employeeHealthfileApi.addEmployeeHealthfile(employeeHealthfileVO);
        log.info("fingerprintInfo:{}", JSON.toJSONString(fingerprintInfo));
    }

    /**
     * 编辑
     * @return
     */
    @Test
    public void editHealthfile(){
        EmployeeHealthfileVO employeeHealthfileVO= new EmployeeHealthfileVO();
        employeeHealthfileVO.setBillDate("2020-09-25");
        employeeHealthfileVO.setCode("JK0118");
        employeeHealthfileVO.setEmployeeId(4290);
        employeeHealthfileVO.setEmployeeName("童雅芳");
        employeeHealthfileVO.setExaminationDate("2020-09-25");
        employeeHealthfileVO.setExaminationItem("1");
        employeeHealthfileVO.setExaminationOrg("11111");
        employeeHealthfileVO.setRemark("222222");
        employeeHealthfileVO.setTakeSteps("222222");
        employeeHealthfileVO.setOrganSign("ZHL00001599");
        employeeHealthfileVO.setResult("11111111111111111");

        //员工照片信息
        List<SaasImgUrlDto> employeeHealthImg = new ArrayList<>();
//        SaasImgUrlDto saasImgUrlDto= new SaasImgUrlDto();
//        saasImgUrlDto.setType(ImageTypeEnum.EMPLOYEE_HEALTH_IMG.getKey());
//        saasImgUrlDto.setImgUrl("https://files.test.ybm100.com/G2/M00/17/23/Cgo01F7i7B-AQrDQAAcQI35m4Wk799.png");
//        saasImgUrlDto.setId(2116L);
//        saasImgUrlDto.setDel(1);
//        saasImgUrlDto.setEmployeeId(4290);
//        employeeHealthImg.add(saasImgUrlDto);

        SaasImgUrlDto saasImgUrlDto1= new SaasImgUrlDto();
        saasImgUrlDto1.setType(ImageTypeEnum.EMPLOYEE_HEALTH_IMG.getKey());
        saasImgUrlDto1.setImgUrl("https://files.test.ybm100.com/G2/M00/17/23/Cgo01F7i7B-AQrDQAAcQI35m4Wk788.png");
        saasImgUrlDto1.setDel(0);
        employeeHealthImg.add(saasImgUrlDto1);

        employeeHealthfileVO.setEmployeeHealthImg(employeeHealthImg);


        ResultVO fingerprintInfo = employeeHealthfileApi.editHealthfile(employeeHealthfileVO);
        log.info("fingerprintInfo:{}", JSON.toJSONString(fingerprintInfo));
    }
}
