package com.xyy.user.provider;


import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.RefreshGoodsAllocationApi;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class RefreshGoodsTest {

    @Reference(version = "0.0.1", url = "dubbo://localhost:20880")
    private RefreshGoodsAllocationApi refreshGoodsAllocationApi;

    @Test
    public void getEmployeeListBySystemRoleSuccess() {
        ResultVO<Boolean> booleanResultVO = refreshGoodsAllocationApi.refreshGoodsAllocation();
        System.out.println("successs");
    }


    @Test
    public void refreshOrganPackageBindInfo() {
        try {
            ResultVO<Boolean> booleanResultVO = refreshGoodsAllocationApi.refreshOrganPackageBindInfo();
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("successs");
    }

    @Test
    public void refreshOrganPackageBindInfoTime() {
        try {
            ResultVO<Boolean> booleanResultVO = refreshGoodsAllocationApi.refreshOrganPackageBindInfoTime();
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("successs");
    }

    @Test
    public void refreshOrganPackageBindInfoByOrgan() {
        try {
            ResultVO<Boolean> booleanResultVO = refreshGoodsAllocationApi.refreshOrganPackageBindInfoByOrgan("ZHL00000445");
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("successs");
    }
}
