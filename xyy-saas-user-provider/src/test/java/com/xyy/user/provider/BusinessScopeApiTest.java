package com.xyy.user.provider;

import com.alibaba.fastjson.JSON;
import com.xyy.saas.common.dto.SystemDictDto;
import com.xyy.user.provider.module.rocketmq.BusinessScopeConsumer;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class BusinessScopeApiTest {
    @Autowired
    private BusinessScopeConsumer consumer;

    @Test
    public void test1(){
        String content = "{\"organSign\":\"\",\"bussinessId\":10005,\"baseVersion\":1414401,\"description\":\"默认经营范围\",\"sort\":147,\"systemYn\":1,\"parentId\":822,\"orgTypes\":[2],\"createTime\":\"2020-10-10T05:08:02.000+0000\",\"yn\":1,\"name\":\"III-06医用成像器械\",\"createUser\":-1,\"id\":828,\"sord\":null,\"value\":null,\"sidx\":null,\"status\":1}";
        SystemDictDto systemDictDto = JSON.parseObject(content, SystemDictDto.class);
        consumer.addBusinessScopeByAllOrganSign(systemDictDto,0);
    }
}
