package com.xyy.user.provider;

import com.xyy.user.module.dto.ecMessage.JointVerifyCodeSmsDto;
import com.xyy.user.module.dto.ecMessage.RegisterFailSmsDto;
import com.xyy.user.module.dto.ecMessage.RegisterSuccesSmsDto;
import com.xyy.user.module.dto.ecMessage.ValitionCodeSmsDto;
import com.xyy.user.provider.module.service.ECMessageService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class ECMessageUnitTest {

    @Autowired
    private ECMessageService ecMessageService;

    @Test
    public void sendSMSValitionCode() {
        ValitionCodeSmsDto smsDto = new ValitionCodeSmsDto();
        smsDto.setMobile("***********");
        smsDto.setVerificationCode("123456");
        String result = ecMessageService.sendSMSValitionCode(smsDto);
        System.out.println(result);
    }

    @Test
    public void sendSMSDrugstoreRegisterSuccess() {
        RegisterSuccesSmsDto smsDto = new RegisterSuccesSmsDto();
        smsDto.setMobile("***********");
        smsDto.setAccount("songwei1");
        String result = ecMessageService.sendSMSDrugstoreRegisterSuccess(smsDto);
        System.out.println(result);
    }

    @Test
    public void sendSMSDrugstoreRegisterFail() {
        RegisterFailSmsDto smsDto = new RegisterFailSmsDto();
        smsDto.setMobile("***********");
        smsDto.setAccount("songwei1");
        smsDto.setReason("信用不足");
        String result = ecMessageService.sendSMSDrugstoreRegisterFail(smsDto);
        System.out.println(result);
    }

    @Test
    public void sendJointVerifyCodeSMS() {
        JointVerifyCodeSmsDto smsDto = new JointVerifyCodeSmsDto();
        smsDto.setMobile("***********");
        smsDto.setVerifyCode("334578");
        smsDto.setDrugstoreName("测试大药房一店");
        smsDto.setUserPhoneNumber("***********");
        String result = ecMessageService.sendJointVerifyCodeSMS(smsDto);
        System.out.println(result);
    }
}
