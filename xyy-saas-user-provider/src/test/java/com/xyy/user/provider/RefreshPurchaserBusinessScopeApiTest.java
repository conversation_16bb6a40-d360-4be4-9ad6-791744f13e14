package com.xyy.user.provider;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.RefreshPurchaserBusinessScopeApi;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class RefreshPurchaserBusinessScopeApiTest {
    @Reference(version = "0.0.1", url="dubbo://localhost:20880")
    public RefreshPurchaserBusinessScopeApi refreshPurchaserBusinessScopeApi;

    @Test
    public void test1(){
        boolean result = refreshPurchaserBusinessScopeApi.refreshBusinessScope("ZHL00004205");
        log.info("result:{}",result);
    }
    @Test
    public void test2(){
        ResultVO<Boolean> booleanResultVO = refreshPurchaserBusinessScopeApi.refreshBusinessScopeByPage();
        log.info("result:{}",booleanResultVO);
    }
}
