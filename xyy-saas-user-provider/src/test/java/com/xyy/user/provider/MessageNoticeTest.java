package com.xyy.user.provider;

import com.xyy.saas.user.center.api.pojo.request.QueryListDrugstoreDTO;
import com.xyy.saas.user.center.api.pojo.response.BaseDrugstoreDTO;
import com.xyy.user.module.dto.base.SaasDrugstoreQueryDTO;
import com.xyy.user.module.enums.DrugstoreBizModelEnum;
import com.xyy.user.module.enums.DrugstoreTypeEnum;
import com.xyy.user.provider.center.gray.CenterGrayService;
import com.xyy.user.provider.center.rpc.DrugstoreAdapter;
import com.xyy.user.provider.module.entity.base.SaasDrugstorePo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class MessageNoticeTest {

    @Autowired
    private CenterGrayService centerGrayService;
    @Autowired
    private DrugstoreAdapter drugstoreAdapter;

    @Test
    public void testSelectOrganSignList() {
        List<String> selectOrganSignList = new ArrayList<>();
        // 除了自定义的复选框
        selectOrganSignList.addAll(listDrugstores(DrugstoreBizModelEnum.DRUGSTORE, DrugstoreTypeEnum.DRUGSTORE));
        selectOrganSignList.addAll(listDrugstores(DrugstoreBizModelEnum.CHAIN_STORE, DrugstoreTypeEnum.HEADQUARTERS));
        selectOrganSignList.addAll(listDrugstores(DrugstoreBizModelEnum.CHAIN_STORE, DrugstoreTypeEnum.DRUGSTORE));
        selectOrganSignList.addAll(listDrugstores(DrugstoreBizModelEnum.JOINT_OPERATION, DrugstoreTypeEnum.HEADQUARTERS));
        selectOrganSignList.addAll(listDrugstores(DrugstoreBizModelEnum.JOINT_OPERATION, DrugstoreTypeEnum.DRUGSTORE));
        log.info("selectOrganSignList size:{}", selectOrganSignList.size());
    }

    private List<String> listDrugstores(DrugstoreBizModelEnum bizModel, DrugstoreTypeEnum organSignType) {
        QueryListDrugstoreDTO queryListDrugstore = new QueryListDrugstoreDTO();
        queryListDrugstore.setOrganSignType((byte) organSignType.getKey());
        queryListDrugstore.setBizModel((byte) bizModel.getKey());
        List<BaseDrugstoreDTO> dtoList = drugstoreAdapter.listDrugstores(queryListDrugstore);
        List<String> organSignList = dtoList
                .stream()
                .filter(drugstoreDto -> drugstoreDto.getStatus() == 1)
                .map(BaseDrugstoreDTO::getOrganSign).collect(Collectors.toList());
        return organSignList;
    }
}
