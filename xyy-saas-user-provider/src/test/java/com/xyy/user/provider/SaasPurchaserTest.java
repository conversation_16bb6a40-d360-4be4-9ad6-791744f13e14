package com.xyy.user.provider;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.XyySaasUserProviderApplication;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.dto.restructure.*;
import com.xyy.user.provider.module.dao.SaasDrugstoreMapper;
import com.xyy.user.provider.module.dao.SaasPurchaserMapper;
import com.xyy.user.provider.module.entity.SaasDrugstore;
import com.xyy.user.provider.module.entity.SaasPurchaser;
import com.xyy.user.provider.module.service.PurchaserService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
@ComponentScan(basePackages={"com.xyy.*"})
@SpringBootTest(classes = XyySaasUserProviderApplication.class)
@Slf4j
public class SaasPurchaserTest {
    @Autowired
    private SaasDrugstoreMapper saasDrugstoreMapper;
    @Autowired
    private PurchaserService purchaserService;
    @Autowired
    private SaasPurchaserMapper saasPurchaserMapper;

    @Test
    public void test(){
        SaasDrugstore saasDrugstore = saasDrugstoreMapper.selectDrugstoreByOrganSign("ZHL00002807");
        if (null != saasDrugstore){
            purchaserService.initPurchaser(saasDrugstore);
        }
        log.info("saasDrugstore:{}", JSON.toJSONString(saasDrugstore));
    }

    @Test
    public void initPurchaser(){
        SaasDrugstore saasDrugstore = saasDrugstoreMapper.selectDrugstoreByOrganSign("ZHL00001825");
        if (null != saasDrugstore){
            purchaserService.initPurchaser(saasDrugstore);
        }
        log.info("saasDrugstore:{}", JSON.toJSONString(saasDrugstore));
    }
    @Test
    public void test2(){
        QueryPurchaserDto queryPurchaserDto = new QueryPurchaserDto();
        queryPurchaserDto.setPageNum(1);
        queryPurchaserDto.setPageSize(50);
        queryPurchaserDto.setQueryType(1);
        queryPurchaserDto.setHeadquartersOrganSign("ZHL00003062");
        queryPurchaserDto.setCreateStartTime(new Date());
        queryPurchaserDto.setCreateEndTime(new Date());
        queryPurchaserDto.setRecentExpiredDay(10);
        List<SaasPurchaserResultDto> purchasersByParam = saasPurchaserMapper.getPurchasersByParam(queryPurchaserDto);
        log.info("purchasersByParam:{}", JSON.toJSONString(purchasersByParam));
    }

    @Test
    public void test3(){
        SaasPurchaserDto saasPurchaserDto = new SaasPurchaserDto();
        saasPurchaserDto.setBankAccount("*************");
        saasPurchaserDto.setPurchaserNo("ZHL00003068");
        List<Integer> scopeList = new ArrayList<>();
        scopeList.add(263);
        saasPurchaserDto.setBusinessScope("scopeList");
        boolean result = purchaserService.editPurchaser(saasPurchaserDto);
        log.info("result:{}", JSON.toJSONString(result));
    }
    @Test
    public void test4(){
        QueryPurchaserDetailDto queryPurchaserDetailDto = new QueryPurchaserDetailDto();
        queryPurchaserDetailDto.setPurchaserNo("ZHL00003068");
        ResultVO<SaasPurchaserDto> saasPurchaserDtoResultVO = purchaserService.getPurchaserByPurchaserNo(queryPurchaserDetailDto);
        log.info("saasPurchaserDtoResultVO:{}", JSON.toJSONString(saasPurchaserDtoResultVO));
    }

    @Test
    public void test5(){
        PurchaserWarnInfo warnInfo = saasPurchaserMapper.getWarnInfo("ZHL00003061", 60);
        log.info("warnInfo:{}", JSON.toJSONString(warnInfo));
    }
}
