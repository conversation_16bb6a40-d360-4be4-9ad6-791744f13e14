package com.xyy.user.provider;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.api.PermissionApi;
import com.xyy.user.module.dto.PermissionDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class AppPermissionTest {


    @Reference(version = "0.0.1", url="dubbo://localhost:20880")
    private PermissionApi permissionApi;


    @Test
    public void queryAppPermissioTest(){
        List<PermissionDto> appPermissionByEmployId = permissionApi.getAppPermissionByEmployId(113149);
        log.info("appPermissionByEmployId:{}", JSON.toJSONString(appPermissionByEmployId));
    }


}
