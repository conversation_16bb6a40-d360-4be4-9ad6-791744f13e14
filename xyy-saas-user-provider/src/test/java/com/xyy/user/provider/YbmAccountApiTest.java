package com.xyy.user.provider;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.UserApi;
import com.xyy.user.module.api.YbmAccountApi;
import com.xyy.user.module.dto.DrugstoreAndYBMListRequestModel;
import com.xyy.user.module.dto.restructure.QueryYbmAccountParam;
import com.xyy.user.module.dto.restructure.YbmAccountDto;
import com.xyy.user.provider.module.dao.SaasYbmAccountMapper;
import com.xyy.user.provider.module.entity.SaasYbmAccount;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class YbmAccountApiTest {
    @Reference(version = "0.0.1", url="dubbo://localhost:20880")
    private YbmAccountApi ybmAccountApi;
    @Autowired
    private SaasYbmAccountMapper saasYbmAccountMapper;


    @Test
    public void queryListPageTest(){
        QueryYbmAccountParam queryYbmAccountParam = new QueryYbmAccountParam();
        queryYbmAccountParam.setPageNum(1);
        queryYbmAccountParam.setPageSize(20);
        ResultVO<PageInfo<YbmAccountDto>> pageInfoResultVO = ybmAccountApi.selectYbmAccountByPage(queryYbmAccountParam);
        log.info("pageInfoResultVO:{}", JSON.toJSONString(pageInfoResultVO));
    }

    @Test
    public void testOneYbmAccount() {
        SaasYbmAccount ybmAccount = saasYbmAccountMapper.selectByOrganSign("ZHL00000519");
        log.info("ybmAccount:{}", JSON.toJSON(ybmAccount));
    }

    @Test
    public void testListYbmAccount() {
        DrugstoreAndYBMListRequestModel model = new DrugstoreAndYBMListRequestModel();
        model.setOrganSignList(Arrays.asList("ZHL00000519","ZHL00000265","ZHL00000272"));
        List<SaasYbmAccount> ybmAccountList = saasYbmAccountMapper.getDrugstoreYbmByCondition(model);
        log.info("ybmAccountList:{}", JSON.toJSON(ybmAccountList));
    }

}
