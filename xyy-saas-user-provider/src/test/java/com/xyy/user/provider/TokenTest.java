package com.xyy.user.provider;

import com.xyy.user.module.enums.ClusterSourseEnum;
import com.xyy.user.provider.common.util.JwtUtil;
import io.jsonwebtoken.Claims;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-09-04
 * @mondify
 * @copyright
 */
public class TokenTest {
    private static final Logger logger = LoggerFactory.getLogger(TokenTest.class);

    public static void main(String[] args){
        Map<String, Object> token = new HashMap<>(2);
        token.put("key", "user:login:70:ZHL0f2ce7b9:1567240839685");
        String tokenJwt = JwtUtil.createJWT(token);
        logger.info(tokenJwt);
        logger.info(tokenJwt);
        ClusterSourseEnum sourse = ClusterSourseEnum.ANDROID;
        logger.info("sourse equals android:",ClusterSourseEnum.ANDROID.equals(sourse));

        String paramToken = "eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************.9hmAw4Xzx3dZSmHJE2byXVdxZM6B-BKF92nWNCBbmqE";
        Claims cachekey = JwtUtil.parseJWT(paramToken);
        logger.info("get(key):",cachekey.get("key"));
    }
}
