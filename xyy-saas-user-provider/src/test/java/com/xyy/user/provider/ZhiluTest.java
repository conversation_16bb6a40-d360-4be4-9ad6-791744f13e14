package com.xyy.user.provider;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.XyySaasUserProviderApplication;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.zhilu.api.ZhiluUserApi;
import com.xyy.zhilu.dto.ZhiluUserBindDto;
import com.xyy.zhilu.dto.ZhiluUserQueryDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @Date 2021/10/27 19:50
 * @Version 1.0
 * 红尘轮回众生顾,因果循环有定数
 * 放下屠刀虽成佛,愿坠三途灭千魔
 */
@RunWith(SpringJUnit4ClassRunner. class)
@SpringBootTest(classes = XyySaasUserProviderApplication.class)
@ActiveProfiles("test")
@Slf4j
public class ZhiluTest {

    @Autowired
    private ZhiluUserApi zhiluUserApi;

    @Test
    public void queryList(){
        ZhiluUserQueryDto queryDto = new ZhiluUserQueryDto();
        queryDto.setPageNum(1);
        queryDto.setPageSize(10);
        ResultVO<PageInfo<ZhiluUserBindDto>> userBind = zhiluUserApi.getUserBind(queryDto);
        System.out.println(JSON.toJSONString(userBind));
    }
}
