package com.xyy.user.provider;


import com.google.common.base.Splitter;
import com.xyy.user.provider.common.util.JSONUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019-08-26
 * @mondify
 * @copyright
 */
public class CommonTest {
    private static final Logger logger = LoggerFactory.getLogger(CommonTest.class);
    public static void main(String[] args){
//        List<String> drugstoreOrganSignList = Splitter.on(",").trimResults().omitEmptyStrings().splitToList("123,345");
//        System.out.print(JSONUtils.obj2JSON(drugstoreOrganSignList));
        List<String> drugstoreOrganSignList = new ArrayList<>();
        drugstoreOrganSignList.add("1");
        drugstoreOrganSignList.add("2");
        drugstoreOrganSignList.add(null);

        drugstoreOrganSignList.add("3");
        Iterator<String> it = drugstoreOrganSignList.iterator();
        while ( it.hasNext()){
            String s = it.next();
            if (Objects.isNull(s)) {
                it.remove();
                continue;
            }
            logger.info(s);
        }

    }
}
