package com.xyy.user.provider;

import com.xyy.user.provider.center.rpc.FlushDataAdapter;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class FlushDataAdapterTest {

    @Autowired
    private FlushDataAdapter flushDataAdapter;

    @Test
    public void testInitSingleDrugData() {
        flushDataAdapter.initSingleDrugData("ZHL00002188");
        log.info("testInitSingleDrugData 成功");
    }
}
