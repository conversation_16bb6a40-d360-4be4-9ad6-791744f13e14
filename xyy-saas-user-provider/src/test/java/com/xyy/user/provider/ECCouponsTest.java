package com.xyy.user.provider;

import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.DateUtil;
import com.xyy.user.module.dto.Coupons.ECCouponsListDto;
import com.xyy.user.module.dto.Coupons.ECCouponsListRequest;
import com.xyy.user.provider.common.util.HttpUtil;
import com.xyy.user.provider.common.util.JSONUtils;
import com.xyy.user.provider.common.util.StringUtils;
import com.xyy.user.provider.module.entity.CouponsRule;
import com.xyy.user.provider.module.entity.ECCouponsList;
import com.xyy.user.provider.module.entity.ECRegisterUser;
import com.xyy.user.provider.module.service.impl.ECCouponsServiceImpl;

import com.xyy.user.provider.module.utils.CouponsListAmountComparator;
import com.xyy.user.provider.module.utils.CouponsListExpireDateComparator;
import com.xyy.user.provider.module.utils.CouponsRuleContractNoComparator;
import com.xyy.user.provider.module.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2019-09-21
 * @mondify
 * @copyright
 */
public class ECCouponsTest {
    private static Logger logger = LoggerFactory.getLogger(ECCouponsTest.class);

    static String USER_URL="http://new-admin.test.ybm100.com";

    static String YBM_AUTH_URL = "http://new-www.test.ybm100.com/";

    public static void main(String[] args){
//        ECRegisterUser user = getRemoteECRegisterUser(**********);
//        System.out.print(JSONUtils.obj2JSON(user));
//        ECCouponsListRequest couponsListRequest = new ECCouponsListRequest();
//
//        List<ECCouponsListDto> reCouponsList = getRemoteECPreReceiveVoucher(couponsListRequest,17);
//        System.out.print(JSONUtils.obj2JSON(reCouponsList));

//        couponsListRequest.setState(2);
//        PageInfo<ECCouponsListDto> remoteECCouponsPage = getRemoteECCouponsPage(couponsListRequest, 17);
//        System.out.print(JSONUtils.obj2JSON(remoteECCouponsPage));

//        Boolean aBoolean = sendRemoteECCoupons("17", 6316);
//        System.out.print(aBoolean);
//        System.out.print(DateUtil.getFirstDayOfMonth(2019, 9));
//        System.out.print(DateUtil.getLastDayOfMonth(2019, 9));
//            int a = 0;
//            int c = 30;
//        BigDecimal bigDecimal = new BigDecimal(a/100);
//        BigDecimal divide = bigDecimal.divide(new BigDecimal(c),2,BigDecimal.ROUND_HALF_UP);
//        System.out.print(divide);

//            Calendar c = Calendar.getInstance();
//            c.setTime(new Date());
//            int day = c.getActualMinimum(5);
//            c.set(5, day);
//            c.set(11, 0);
//            c.set(12, 0);
//            c.set(13, 0);
//            c.set(14, 0);
//        System.out.print(c.getTime());

//        Calendar c = Calendar.getInstance();
//        c.setTime(new Date());
//        c.add(1, 0);
//        c.add(2, 12);
//        c.add(5, 0);
//        c.add(10, 0);
//        c.add(12, 0);
//        c.add(13, 0);
//        c.add(14, 0);
//        System.out.print(c.getTime());
    	
//    	String a ="0123";
//    	System.out.print(StringUtils.isNum(a));
//
//    Date an = DateUtil.addDate(new Date(), 0, 9, 0, 0, 0, 0, 0);
//        int d = getMonthSpace(new Date(),an);
//        Calendar c = Calendar.getInstance();
//        c.setTimeInMillis(System.currentTimeMillis());//当前时间
//        c.setTime(new Date());
//        c.setFirstDayOfWeek(Calendar.MONDAY);//以周一为首日
//        c.setTimeInMillis(System.currentTimeMillis());//当前时间
//
//        c.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);//周一
//
//        c.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);//周日

//        long sub = getDaySub();
        List<ECCouponsList> expireList = new ArrayList<>();
        ECCouponsList e2 = new ECCouponsList();
        e2.setVoucherId(2L);
        e2.setExpireDate(new Date(2, 0, 0));
        expireList.add(e2);
        ECCouponsList e3 = new ECCouponsList();
        e3.setVoucherId(3L);
        e3.setExpireDate(new Date(3, 0, 0));
        expireList.add(e3);
        ECCouponsList e1 = new ECCouponsList();
        e1.setVoucherId(1L);
        e1.setExpireDate(null);
        expireList.add(e1);
        ECCouponsList e4 = new ECCouponsList();
        e4.setVoucherId(4L);
        e4.setExpireDate(new Date(4, 1, 4));
        expireList.add(e4);

        ECCouponsList e7 = new ECCouponsList();
        e7.setVoucherId(7L);
//        e7.setExpireDate(null);
        e7.setMoneyInVoucher(new BigDecimal("80"));
        expireList.add(e7);
        ECCouponsList e5 = new ECCouponsList();
        e5.setVoucherId(5L);
//        e5.setExpireDate(null);
        e5.setMoneyInVoucher(new BigDecimal("30"));
        expireList.add(e5);
        ECCouponsList e6 = new ECCouponsList();
        e6.setVoucherId(6L);
//        e6.setExpireDate(null);
        e6.setMoneyInVoucher(new BigDecimal("50"));
        expireList.add(e6);
        List list = sortPreCouponsList(expireList);
        logger.info(JSONUtils.obj2JSON(list));
        String contractCode = "LL-HN-YW-SF-00091";
        String startContractCode = "LL-HN-YW-SF-00091";
        String endContractCode = "LL-HN-YW-SF-00091";
        int crmLastIndex = contractCode.lastIndexOf("-");
        String crmConNum = crmLastIndex>-1?contractCode.substring(crmLastIndex+1):"";
        int startLastIndex = startContractCode.lastIndexOf("-");
        String startConNum = startLastIndex>-1?startContractCode.substring(startLastIndex+1):"";
        int endLastIndex = endContractCode.lastIndexOf("-");
        String endConNum = endLastIndex>-1?endContractCode.substring(endLastIndex+1):"";
        String  contractCodePre = contractCode.substring(0, crmLastIndex);
        String  startContractCodePre = startContractCode.substring(0, startLastIndex);
        String  endContractCodePre = endContractCode.substring(0, endLastIndex);
        logger.error(String.format("checkContractRule contractcode pre not equal,contractCode:%s,startContractCode:%s,endContractCode:%s",contractCode,startContractCode,endContractCode));
        if(!contractCodePre.equals(startContractCodePre) || !contractCodePre.equals(endContractCodePre)){
            logger.error(String.format("checkContractRule contractcode pre not equal,contractCode:%s,startContractCode:%s,endContractCode:%s",contractCode,startContractCode,endContractCode));

        }
        List<CouponsRule> CouponsRuleLst = new ArrayList<>();
        CouponsRule r1 = new CouponsRule();
        r1.setId(1L);
//        r1.setStarteContractCode("1");
        CouponsRuleLst.add(r1);
        CouponsRule r2 = new CouponsRule();
        r2.setId(2L);
//        r2.setStarteContractCode("1");
        CouponsRuleLst.add(r2);
        CouponsRule r3= new CouponsRule();
        r3.setId(3L);
        r3.setStarteContractCode("3");
        CouponsRuleLst.add(r3);
        Collections.sort(CouponsRuleLst, new CouponsRuleContractNoComparator());
        System.out.print(JSONUtils.obj2JSON(CouponsRuleLst));

    }

    private static List sortPreCouponsList(List<ECCouponsList> list){
        if(list==null || list.size()==0){
            return Collections.emptyList();
        }
        List<ECCouponsList> expireList = new ArrayList<>();
        List<ECCouponsList> notExpireList = new ArrayList<>();
        for(ECCouponsList coupon:list){
            if(coupon.getExpireDate()!=null){
                expireList.add(coupon);
            }else{
                notExpireList.add(coupon);
            }
        }
        Collections.sort(expireList, new CouponsListExpireDateComparator());
        Collections.sort(notExpireList, new CouponsListAmountComparator());
        expireList.addAll(notExpireList);
        return expireList;
    }

    public static  long getDaySub(){
        long sub = 86400000;
        long day=(sub)/(24*60*60*1000);
        if (sub == (24*60*60*1000)) {
            return day;
        }else{
            return day+1;
        }
    }

    public static Date getMondayStartOfWeek(Date date){
        Calendar c = Calendar.getInstance(TimeZone.getTimeZone("GMT+08:00"));
        c.setTime(new Date());
        c.setFirstDayOfWeek(Calendar.MONDAY);//以周一为首日
        c.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);//周一
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        logger.info("c.getTime():",c.getTime());
        return c.getTime();
    }

    public static Date getSaturdayEndOfWeek(Date date){
        Calendar c = Calendar.getInstance(TimeZone.getTimeZone("GMT+08:00"));
        c.setTime(new Date());
        c.setFirstDayOfWeek(Calendar.MONDAY);//以周一为首日
        c.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY);//周一
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        c.set(Calendar.MILLISECOND, 0);
        logger.info("c.getTime()",c.getTime());
        return c.getTime();
    }

    public static int getMonthSpace(Date date1, Date date2)
    {

        int result = 0;

        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();

        c1.setTime(date1);
        c2.setTime(date2);

        result = c2.get(Calendar.MONTH) - c1.get(Calendar.MONTH);

        return result == 0 ? 1 : Math.abs(result);


    }
    /**
     * 按券状态分页获取EC优惠券列表
     * @param couponsListRequest
     * @param merchantId
     * @return
     */
    public static PageInfo<ECCouponsListDto> getRemoteECCouponsPage(ECCouponsListRequest couponsListRequest, int merchantId){
        PageInfo<ECCouponsListDto> returnPage = new PageInfo<ECCouponsListDto>();
        try {
            String resInfo = HttpUtil
                    .postAsString(YBM_AUTH_URL + "/sass/voucher/findAllVoucherInfo?merchantId="
                            + merchantId
                            + "&offset=0"
                            + "&limit=100"
                            + "&state="
                            + couponsListRequest.getState());
            logger.info(String.format("药店机构：%s,YBM会员id：%s,解析前远程调用药帮忙getRemoteECPreReceiveVoucher返回数据：%s", couponsListRequest.getOrganSign(),merchantId,resInfo));
            PageInfo<ECCouponsList> page = JSONUtils.json2Obj(resInfo, new TypeReference<PageInfo<ECCouponsList>>(){});
            logger.info("解析远程调用返回数据结果，结果集为:" + page.toString());
            if (page!=null && page.getList()!=null) {
                List<ECCouponsList> list = page.getList();
                List<ECCouponsListDto> dtoList = new ArrayList<>();
                for(ECCouponsList c:list){
                    ECCouponsListDto dto = new ECCouponsListDto();
                    BeanUtils.copyProperties(c, dto);
                    dto.setExpireDateStr(DateUtil.parseDateToStr(c.getExpireDate(),DateUtil.DATE_FORMAT_POINTYYYYMMDD ));
                    dto.setValidDateStr(DateUtil.parseDateToStr(c.getValidDate(),DateUtil.DATE_FORMAT_POINTYYYYMMDD ));
                    dtoList.add(dto);
                }
                BeanUtils.copyProperties(page, returnPage);
                returnPage.setList(dtoList);
                return returnPage;
            } else {
                logger.error(String.format("药店机构：%s,YBM会员id：%s,远程调用药帮忙getRemoteECCouponsPage返回数据：%s", couponsListRequest.getOrganSign(),merchantId,resInfo));
                return null;
            }
        } catch (Exception e) {
            logger.info("getRemoteECCouponsPage远程调用接口失败或结果解析失败,异常信息:" + e);
            return null;
        }

    }

    /**
     * 获取EC待领取优惠券列表
     * @param couponsListRequest
     * @param merchantId
     * @return
     */
    public static  List<ECCouponsListDto> getRemoteECPreReceiveVoucher(ECCouponsListRequest couponsListRequest, int merchantId){
        List<ECCouponsListDto> returnList = new ArrayList<>();
        try {
            String resInfo = HttpUtil
                    .postAsString(YBM_AUTH_URL + "/sass/voucher/findPreReceiveVoucher?merchantId="
                            + merchantId);
            logger.info(String.format("药店机构：%s,YBM会员id：%s,解析前远程调用药帮忙getRemoteECPreReceiveVoucher返回数据：%s", couponsListRequest.getOrganSign(),merchantId,resInfo));
            Map<String, Object> messageInfo = JSONUtils.json2Obj(resInfo, Map.class);
            logger.info("解析远程调用返回数据结果，结果集为:" + messageInfo.toString());
            if ("success".equals(messageInfo.get("status"))) {
                Object dataObj = messageInfo.get("voucherList");
                List<ECCouponsList> list = JSONUtils.json2Obj(JSONUtils.obj2JSON(dataObj), new TypeReference<List<ECCouponsList>>(){});

                for(ECCouponsList coupon:list){
                    ECCouponsListDto dto = new ECCouponsListDto();
                    BeanUtils.copyProperties(coupon, dto);
                    dto.setExpireDateStr(coupon.getExpireDate()!=null?DateUtil.parseDateToStr(coupon.getExpireDate(),DateUtil.DATE_FORMAT_POINTYYYYMMDD ):"");
                    dto.setValidDateStr(coupon.getValidDate()!=null?DateUtil.parseDateToStr(coupon.getValidDate(),DateUtil.DATE_FORMAT_POINTYYYYMMDD ):"");
                    returnList.add(dto);
                }
                return returnList;
            } else {
//                result.setCode(messageInfo.get("errorCode"));
//                result.setMsg(messageInfo.get("errorMsg"));
                logger.error(String.format("药店机构：%s,YBM会员id：%s,远程调用药帮忙getRemoteECPreReceiveVoucher返回数据：%s", couponsListRequest.getOrganSign(),merchantId,resInfo));
                return null;
            }
        } catch (Exception e) {
            logger.info("远程调用接口失败或结果解析失败,异常信息:" , e);
//            result.setCode(ResultCodeMessage.REMOTE_YBM_ERROR_CODE);
//            result.setMsg(ResultCodeMessage.REMOTE_YBM_ERROR_MESSAGE);
            return null;
        }
    }

    /**
     * 具体执行EC发券
     * @param merchantIds
     * @param voucherTemplateId
     * @return
     */
    public static  Boolean sendRemoteECCoupons(String merchantIds,Integer voucherTemplateId){
        List<ECCouponsListDto> returnList = new ArrayList<>();
        try {
            String resInfo = HttpUtil
                    .postAsString(YBM_AUTH_URL + "/sass/voucher/receiveVoucher?merchantIds="
                            + merchantIds
                            +"&voucherTemplateId="
                            +voucherTemplateId
                            + "&token=79faf82271944fe38" );
            logger.info(String.format("YBM会员id：%s,voucherTemplateId:%s,解析前远程调用药帮忙sendRemoteECCoupons返回数据：%s",merchantIds,voucherTemplateId,resInfo));
            Map<String, Object> messageInfo = JSONUtils.json2Obj(resInfo, Map.class);
            logger.info("解析远程调用返回数据结果，结果集为:" + messageInfo.toString());
            if ("success".equals(messageInfo.get("status"))) {
                return true;
            } else {
//                result.setCode(messageInfo.get("errorCode"));
//                result.setMsg(messageInfo.get("errorMsg"));
                logger.error(String.format("YBM会员id：%s,voucherTemplateId:%s,解析前远程调用药帮忙sendRemoteECCoupons返回数据：%s", merchantIds,voucherTemplateId,resInfo));
                return false;
            }
        } catch (Exception e) {
            logger.info("sendRemoteECCoupons远程调用接口失败或结果解析失败,异常信息:" + e);
//            result.setCode(ResultCodeMessage.REMOTE_YBM_ERROR_CODE);
//            result.setMsg(ResultCodeMessage.REMOTE_YBM_ERROR_MESSAGE);
            return false;
        }
    }

    public static  ECRegisterUser getRemoteECRegisterUser(int merchantId){
        ECRegisterUser ecUser = null;
        try {
            String resInfo = HttpUtil
                    .postAsString(USER_URL + "/saas/merchantinfo/queryMerchantStatesForSaas?merchantId="
                            + merchantId);
            logger.info(String.format("YBM会员id：%s,解析前远程调用药帮忙getRemoteECRegisterUser返回数据：%s",merchantId,resInfo));
            Map<String, Object> messageInfo = JSONUtils.json2Obj(resInfo, Map.class);
            logger.info("getRemoteECRegisterUser解析远程调用返回数据结果，结果集为:" + messageInfo.toString());
            if ("success".equals(messageInfo.get("status"))) {
                Map<String, Object> dataMap = (Map<String, Object>) messageInfo.get("data");
                Byte status = Byte.parseByte(dataMap.get("status")+"");//状态：1-激活，2-未激活，3-冻结
                Byte licenseStatus = Byte.parseByte(dataMap.get("licenseStatus")+"");//资质状态：1-资质未提交,2-资质已提交,3-资质已过期,4:资质已通过
                ecUser = new ECRegisterUser();
                ecUser.setEnabled(status);
                ecUser.setQualificationStatus(licenseStatus);
            } else {
                logger.error(String.format("YBM会员id：%s,解析前远程调用药帮忙getRemoteECRegisterUser返回数据：%s",merchantId,resInfo));
            }
        } catch (Exception e) {
            logger.info("getRemoteECRegisterUser远程调用接口失败或结果解析失败,异常信息:" + e);
        }
        return ecUser;
    }
}
