package com.xyy.user.provider;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.ChannelConfigApi;
import com.xyy.user.module.api.UserApi;
import com.xyy.user.module.dto.restructure.BaseConfigDto;
import com.xyy.user.module.dto.restructure.QueryPurchaserDetailDto;
import com.xyy.user.provider.module.dao.SaasPurchaserMapper;
import com.xyy.user.provider.module.dto.BaseJZTRequestDto;
import com.xyy.user.provider.module.dto.BaseJZTResponseDto;
import com.xyy.user.provider.module.dto.CustomerInfo;
import com.xyy.user.provider.module.entity.SaasPurchaser;
import com.xyy.user.provider.module.service.RemoteJZTApiService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class RemoteJZTApiServiceTest {
    @Autowired
    private RemoteJZTApiService remoteJZTApiService;

    @Reference(version = "0.0.1", url="dubbo://localhost:20880")
    private ChannelConfigApi channelConfigApi;
    @Autowired
    private SaasPurchaserMapper purchaserMapper;

    @Test
    public void savePurchaserInfoTest(){
//        String content = "{    \"funcType\": \"saveData\",    \"callCategory\": \"dw\",    \"id\": \"\",    \"pwd\": \"123\",    \"jsondata\": [        {            \"LIUS_NO\": \"1\",            \"Ssa_Id\": \"001\",            \"Ssa_No\": \"DSFHNTXDSF001\",            \"Ssa_Name\": \"海南天祥药业有限公司\",            \"Ssa_Shortname\": \"海南天祥药业有限公司\",            \"Mnemonic_Code\": \"HNTXYYYXGS\",            \"Ssa_Addr_Phone\": \"11111\",            \"Ssa_Sign\": \"2\",            \"Is_Hospital_Customer\": \"N\",            \"Is_Active\": \"Y\",            \"Creator\": \"***************\",            \"Created_Time\": \"2017-10-24 22:28:22\",            \"Updated_Time\": \"2017-10-24 22:28:22\",            \"Status\": \"2\",            \"Yew_Staff\": \"张三\",            \"Yew_Staff_Phone\": \"***********\",            \"Address\": \"***********\",            \"Address_Shortname\": \"**************\",            \"Contact_Name\": \"张三\",            \"Contact_Phone\": \"***********\",            \"Is_Default\": \"Y\",            \"Con_Id\": \"***************\",            \"Bankaccount\": \"********\",            \"Business_Licence_No\": \"33333\",            \"Business_Licence_No_Valid_Until\": \"2019-10-24\",            \"Production_License_No\": \"44444\",            \"Production_License_No_Valid_Until\": \"2019-10-24\"        },        {            \"LIUS_NO\": \"2\",            \"Ssa_Id\": \"002\",            \"Ssa_No\": \"DSFHNTXDSF002\",            \"Ssa_Name\": \"海南天祥药业有限公司\",            \"Ssa_Shortname\": \"海南天祥药业有限公司\",            \"Mnemonic_Code\": \"HNTXYYYXGS\",            \"Ssa_Addr_Phone\": \"11111\",            \"Ssa_Sign\": \"2\",            \"Is_Hospital_Customer\": \"N\",            \"Is_Active\": \"Y\",            \"Creator\": \"***************\",            \"Created_Time\": \"2017-10-24 22:28:22\",            \"Updated_Time\": \"2017-10-24 22:28:22\",            \"Status\": \"2\",            \"Yew_Staff\": \"李四\",            \"Yew_Staff_Phone\": \"***********\",            \"Address\": \"***********\",            \"Address_Shortname\": \"**************\",            \"Contact_Name\": \"李四\",            \"Contact_Phone\": \"***********\",            \"Is_Default\": \"Y\",            \"Con_Id\": \"***************\",            \"Bankaccount\": \"********\",            \"Business_Licence_No\": \"33333\",            \"Business_Licence_No_Valid_Until\": \"2019-10-24\",            \"Production_License_No\": \"44444\",            \"Production_License_No_Valid_Until\": \"2019-10-24\"        }    ]}";
//        BaseJZTRequestDto baseJZTRequestDto = JSON.parseObject(content, new TypeReference<BaseJZTRequestDto<CustomerInfo>>(){});
//        BaseJZTResponseDto baseJZTResponseDto = remoteJZTApiService.savePurchaserInfo(baseJZTRequestDto);
//        log.info("baseJZTResponseDto:",JSON.toJSONString(baseJZTResponseDto));
        QueryPurchaserDetailDto detailDto = new QueryPurchaserDetailDto();
        detailDto.setPurchaserNo("ZHL00004149");
        SaasPurchaser saasPurchaser = purchaserMapper.selectByPurchaserNo(detailDto);
        BaseJZTResponseDto baseJZTResponseDto = remoteJZTApiService.savePurchaserInfo(saasPurchaser);
        log.info("baseJZTResponseDto:{}",JSON.toJSONString(baseJZTResponseDto));
    }

    @Test
    public void getJZTconfigTest(){
        BaseConfigDto channelConfig = channelConfigApi.getChannelConfig("ZHL00004135");
        log.info("channelConfig:{}",JSON.toJSONString(channelConfig));
    }

    @Test
    public void getChannelTypeTest(){
        ResultVO<Integer> resultVO = channelConfigApi.getChannelType("ZHL10002692");
        log.info("resultVO:{}",JSON.toJSONString(resultVO));
    }
}
