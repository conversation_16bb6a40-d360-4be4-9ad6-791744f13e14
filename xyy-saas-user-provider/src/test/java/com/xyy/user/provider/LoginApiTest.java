package com.xyy.user.provider;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.user.center.api.client.LoginApi;
import com.xyy.saas.user.center.api.client.UserApi;
import com.xyy.saas.user.center.api.pojo.request.login.AuthLoginDTO;
import com.xyy.saas.user.center.api.pojo.response.YkqLoginResponseDTO;
import com.xyy.user.center.common.model.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.test.context.ActiveProfiles;

@Slf4j
@ActiveProfiles({"test"})
public class LoginApiTest{
    @Reference(version = "1.0.0",url="dubbo://localhost:20201")
    public LoginApi loginApi;
    @Test
    public void ykqLogin(){
        AuthLoginDTO authLoginDTO = new AuthLoginDTO();
        authLoginDTO.setLoginName("renhui1");
        authLoginDTO.setPassword("e10adc3949ba59abbe56e057f20f883e");
        authLoginDTO.setOrganSign("ZHL00005733");
        Result<YkqLoginResponseDTO> result = loginApi.ykqLogin(authLoginDTO);
        log.info("result:{}", JSON.toJSONString(result));
    }

    @Reference(version = "1.0.0",url="dubbo://localhost:20201")
    public UserApi userApi;
//    @Test
//    public void plugLogin(){
//        UserLoginPlugDTO userLoginPlugDTO = new UserLoginPlugDTO();
////        userLoginPlugDTO.setType(1);
////        userLoginPlugDTO.setOrganSign("ZHL00005433");
////        userLoginPlugDTO.setEmployeeId(115739);
////        userApi.plugLogin(userLoginPlugDTO);
//        userLoginPlugDTO.setType(2);
//        userLoginPlugDTO.setOrganSign("ZHL00005433");
//        userLoginPlugDTO.setEmployeeId(115739);
//        userLoginPlugDTO.setLoginName("15926350018");
//        userApi.plugLogin(userLoginPlugDTO);
//    }
}
