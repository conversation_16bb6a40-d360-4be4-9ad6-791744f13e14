package com.xyy.user.provider;

import com.alibaba.fastjson.JSONObject;
import com.xyy.XyySaasUserProviderApplication;
import com.xyy.common.provider.module.dao.MessageNoticeEmployeeMapper;
import com.xyy.common.provider.module.entity.MessageNoticeEmployeePo;
import com.xyy.user.module.dto.ECRegisterUserDto;
import com.xyy.user.provider.module.dao.ECRegisterUserMapper;
import com.xyy.user.provider.module.entity.ECRegisterUser;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @date 2021/3/14 17:29
 */
@RunWith(SpringJUnit4ClassRunner. class)
@SpringBootTest(classes = XyySaasUserProviderApplication.class)
@ActiveProfiles("test")
@Slf4j
public class ZebraTest {
    @Autowired
    private MessageNoticeEmployeeMapper mroductBaseInfoMapper;
    @Autowired
    private ECRegisterUserMapper eCRegisterUserMapper;


    @Test
    public void select(){
        log.info("开始进入到ZebraSpringBootTest的select方法");
        ECRegisterUser ecRegisterUser = new ECRegisterUser();
        ecRegisterUser.setYbmCode("123132");
        ecRegisterUser.setYbmUserName("112233");
        ECRegisterUserDto user = eCRegisterUserMapper.selectNewUserByUserName(ecRegisterUser);
        log.info("输出user："+ JSONObject.toJSONString(user));
    }


    @Test
    public void insertCommon(){
        log.info("开始进入到ZebraSpringBootTest的insertCommon方法");
        MessageNoticeEmployeePo msg = new MessageNoticeEmployeePo();
        msg.setEmployeeId("1231323");
        msg.setCustomFlag((byte) 1);
        msg.setMessageNoticeId(System.currentTimeMillis());
        msg.setOrganSign("testOrganSign");
        msg.setState((byte)1);
        msg.setYn((byte)1);
        mroductBaseInfoMapper.insert(msg);
    }
}
