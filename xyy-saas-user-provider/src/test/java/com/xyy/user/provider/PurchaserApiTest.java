package com.xyy.user.provider;


import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.PurchaserApi;
import com.xyy.user.module.dto.restructure.QueryPurchaserDetailDto;
import com.xyy.user.module.dto.restructure.QueryPurchaserDto;
import com.xyy.user.module.dto.restructure.SaasPurchaserDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("test")
@Slf4j
public class PurchaserApiTest {
//    @Reference(version = "0.0.1", url="dubbo://localhost:20880")
    @Autowired
    private PurchaserApi purchaserApi;

    @Test
    public void test1(){
        QueryPurchaserDetailDto detailDto = new QueryPurchaserDetailDto();
        detailDto.setPurchaserNo("ZHL00002801");
        detailDto.setHeadquartersOrganSign("ZHL00002774");
        ResultVO<SaasPurchaserDto> purchaserByPurchaserNo = purchaserApi.getPurchaserByPurchaserNo(detailDto);
        log.info("purchaserByPurchaserNo:{}", JSON.toJSONString(purchaserByPurchaserNo));
    }

    @Test
    public void listAllPurchaser() {
        QueryPurchaserDto queryPurchaserDto = new QueryPurchaserDto();
        queryPurchaserDto.setHeadquartersOrganSign("ZHL00002690");
        queryPurchaserDto.setPageNum(1);
//        queryPurchaserDto.setPageSize(10000);
        queryPurchaserDto.setPurchaserUnitName("销售单位名称");
        ResultVO<PageInfo<SaasPurchaserDto>> allPurchaserByPage = purchaserApi.getAllPurchaserByPage(queryPurchaserDto);
        log.info("listAllPurchaser:{}", JSON.toJSONString(allPurchaserByPage));
    }

    @Test
    public void  editPurchaser(){
        String param = "{\"imageUrlList\":[],\"businessLicenseLongTerm\":1,\"qualificationInfos\":[{\"certificateDate\":\"2020-12-01\",\"expiryDateDate\":\"\",\"imgUrls\":[],\"businessScope\":[{\"del\":0,\"id\":275}],\"certificateNo\":\"91500107MDD311D333\",\"certificateType\":1},{\"modifyType\":1,\"certificateDate\":\"2022-01-01\",\"expiryDateDate\":\"2022-01-31\",\"imgUrls\":[],\"businessScope\":[{\"del\":0,\"id\":731},{\"del\":0,\"id\":265}],\"certificateNo\":\"1231231\",\"certificateType\":2}],\"purchaserNo\":\"ZHL00004534\"}";
        SaasPurchaserDto aa = JSON.parseObject(param,SaasPurchaserDto.class);
        purchaserApi.editPurchaser(aa);
    }
}
