package com.xyy;

import com.alibaba.dubbo.config.spring.context.annotation.EnableDubbo;
import com.alibaba.dubbo.config.spring.context.annotation.EnableDubboConfig;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
//import com.xyy.mq.rabbitmq.annotations.EnableXyyRabbitMq;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * @author: uzdz
 * @date: 2018/9/5 11:25
 * @description: main application
 */
@EnableApolloConfig
@SpringBootApplication
@EnableDubbo
@EnableDubboConfig(multiple = true)
//@EnableXyyRabbitMq(startup = true)
@ComponentScan(basePackages={"com.xyy.*"})
public class XyySaasUserProviderApplication {

    public static void main(String[] args) {
        //cat 和 apollo 配置冲突,所以将配置名称挪到代码这里
        System.setProperty("cat.appName","saas-user-provider");
        System.setProperty("dubbo.start.notiry.waiting.millis", "10000"); //默认3秒，启动时等待通知客服端时间
        System.setProperty("dubbo.shutownhook.notiry.waiting.millis", "10000"); //默认1秒，停用时等待通知客服端时间
        System.setProperty("dubbo.service.shutdown.wait", "15000"); //默认10秒，dubbo优雅停机最大停机时间
        //zebra batchupdate 使用
        System.setProperty("spring.jdbc.getParameterType.ignore","true");
        SpringApplication app = new SpringApplication(XyySaasUserProviderApplication.class);
        app.run(args);

    }
}
