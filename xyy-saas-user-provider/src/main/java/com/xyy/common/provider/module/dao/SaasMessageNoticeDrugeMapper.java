package com.xyy.common.provider.module.dao;

import com.xyy.common.provider.module.entity.SaasMessageNoticeDrugePo;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * (SaasMessageNoticeDruge)表数据库访问层
 *
 * <AUTHOR> @since 2021-09-24 16:26:11
 */
public interface SaasMessageNoticeDrugeMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    SaasMessageNoticeDrugePo queryById(Long id);

    /**
     * 查询指定行数据
     *
     * @param saasMessageNoticeDrugePo 查询条件
     * @return 对象列表
     */
    List<SaasMessageNoticeDrugePo> queryAll(SaasMessageNoticeDrugePo saasMessageNoticeDrugePo);

    /**
     * 统计总行数
     *
     * @param saasMessageNoticeDrugePo 查询条件
     * @return 总行数
     */
    long count(SaasMessageNoticeDrugePo saasMessageNoticeDrugePo);

    /**
     * 新增数据
     *
     * @param saasMessageNoticeDrugePo 实例对象
     * @return 影响行数
     */
    int insert(SaasMessageNoticeDrugePo saasMessageNoticeDrugePo);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<SaasMessageNoticeDruge> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<SaasMessageNoticeDrugePo> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<SaasMessageNoticeDruge> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<SaasMessageNoticeDrugePo> entities);

    /**
     * 修改数据
     *
     * @param saasMessageNoticeDrugePo 实例对象
     * @return 影响行数
     */
    int update(SaasMessageNoticeDrugePo saasMessageNoticeDrugePo);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 根据消息头id删除
     * @param messageNoticeId
     * @return
     */
    int realDelByMessageNoticeId(@Param("messageNoticeId") Long messageNoticeId);

}

