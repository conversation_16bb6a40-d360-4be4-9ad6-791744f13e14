package com.xyy.common.provider.module.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xyy.common.provider.module.entity.SaasMessageNoticeDrugePo;
import com.xyy.common.provider.module.dao.SaasMessageNoticeDrugeMapper;
import com.xyy.common.provider.module.service.SaasMessageNoticeDrugeService;
import com.xyy.user.module.dto.SaasDrugstoreDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * (SaasMessageNoticeDruge)表服务实现类
 *
 * <AUTHOR> @since 2021-09-24 16:26:11
 */
@Slf4j
@Service("saasMessageNoticeDrugeService")
public class SaasMessageNoticeDrugeServiceImpl implements SaasMessageNoticeDrugeService {
    @Resource
    private SaasMessageNoticeDrugeMapper saasMessageNoticeDrugeMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public SaasMessageNoticeDrugePo queryById(Long id) {
        return this.saasMessageNoticeDrugeMapper.queryById(id);
    }

    /**
     * 新增数据
     *
     * @param saasMessageNoticeDrugePo 实例对象
     * @return 实例对象
     */
    @Override
    public SaasMessageNoticeDrugePo insert(SaasMessageNoticeDrugePo saasMessageNoticeDrugePo) {
        this.saasMessageNoticeDrugeMapper.insert(saasMessageNoticeDrugePo);
        return saasMessageNoticeDrugePo;
    }

    /**
     * 修改数据
     *
     * @param saasMessageNoticeDrugePo 实例对象
     * @return 实例对象
     */
    @Override
    public SaasMessageNoticeDrugePo update(SaasMessageNoticeDrugePo saasMessageNoticeDrugePo) {
        this.saasMessageNoticeDrugeMapper.update(saasMessageNoticeDrugePo);
        return this.queryById(saasMessageNoticeDrugePo.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Long id) {
        return this.saasMessageNoticeDrugeMapper.deleteById(id) > 0;
    }

    @Override
    public PageInfo queryAll(SaasMessageNoticeDrugePo saasMessageNoticeDrugePo,PageInfo pageInfo) {
        PageInfo pageInfo1;
        try {
            PageHelper.startPage(pageInfo.getPageNum(), pageInfo.getPageSize());
            List<SaasMessageNoticeDrugePo> drugePos = saasMessageNoticeDrugeMapper.queryAll(saasMessageNoticeDrugePo);
            pageInfo1 = new PageInfo(drugePos);
            PageInfo drugstore = new PageInfo();
            BeanUtils.copyProperties(pageInfo1, drugstore);
            List<SaasDrugstoreDto> arrayList = new ArrayList<>();
            List<SaasMessageNoticeDrugePo> list = pageInfo1.getList();
            if (CollectionUtils.isNotEmpty(list)) {
                for (SaasMessageNoticeDrugePo item:drugePos) {
                    SaasDrugstoreDto saasDrugstoreDto = new SaasDrugstoreDto();
                    saasDrugstoreDto.setOrganSign(item.getOrganSign());
                    saasDrugstoreDto.setDrugstoreName(item.getDrugstoreName());
                    saasDrugstoreDto.setManagerName(item.getManagerName());
                    saasDrugstoreDto.setProvince(item.getProvince());
                    saasDrugstoreDto.setCity(item.getCity());
                    saasDrugstoreDto.setArea(item.getArea());
                    saasDrugstoreDto.setAddress(item.getAddress());
                    arrayList.add(saasDrugstoreDto);
                }

            }
            drugstore.setList(arrayList);
            return drugstore;
        }catch (Exception e){
            log.error("SaasMessageNoticeDrugeServiceImpl#queryAll 查询已选择药店分页异常:{}",e);
        }
        return new PageInfo();
    }
}
