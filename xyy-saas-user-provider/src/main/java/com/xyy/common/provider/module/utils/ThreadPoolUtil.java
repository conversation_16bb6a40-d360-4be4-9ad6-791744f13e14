package com.xyy.common.provider.module.utils;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName ThreadPoolUtil
 * @Description 线程池工具类
 * <AUTHOR>
 * @Date 2019/8/13 9:24
 **/
public class ThreadPoolUtil {
    private static ExecutorService executorService = new ThreadPoolExecutor(10, 100,
            60L, TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>(1024));

    public static ExecutorService getDefaultExecutorService(){
        return executorService;
    }
}
