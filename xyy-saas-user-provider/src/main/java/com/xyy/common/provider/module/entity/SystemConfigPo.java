package com.xyy.common.provider.module.entity;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SystemConfigPo {
    /** 主键  */
    private Integer id;
    /** 是否验证处方 1 验证 0 不验证 */
    private Byte prescriptionYn;
    /** 是否修改整单折扣 0 不修改 1修改  */
    private Byte updateOrderDiscountYn;
    /** 是否修改单品扣率 0 不修改 1修改  */
    private Byte updateProductDiscountYn;
    /** 是否修改单品价格 1 修改 0不修改 */
    private Byte updateProductPrice;
    /** 单品折扣区间起始 */
    private Integer productDiscountStart;
    /** 单品折扣区间终止 */
    private Integer productDiscountEnd;
    /** 是否低于成本价销售 0 否 1 是*/
    private Byte salesBelowCost;
    /** 是否验证身份证 0 不验证 1验证  */
    private Byte idCardYn;
    /** 是否保存上一次销售员 1 是 0 否  */
    private Byte savePreSaleYn;
    /** 是否一步入库 1 是 0 否  */
    private Byte oneSetpYn;
    /** 是否批号先进先出 1 是 0 否  */
    private Byte batchNoQueueYn;
    /** 含麻药品单笔销售数量  */
    private Integer flaxNum;
    /** 采购员  */
    private String purchaseName;
    /** 验收员  */
    private String examinerName;
    /** 是否POS显示进价 1 是 0 否  */
    private Byte posShowPurpriceYn;
    /** 是否自动审批首营商品 1 是 0 否  */
    private Byte approveProductYn;
    /** 是否自动审批首营企业 1 是 0 否  */
    private Byte approveProviderYn;
    /** 是否快捷审批首营商品 1 是 0 否  */
    private Byte approveProductQuickYn;
    /** 是否快捷审批首营企业 1 是 0 否  */
    private Byte approveProviderQuickYn;
    /** 是否自动审批处方登记 1 是 0 否  */
    private Byte approvePrescriptionYn;
    /** 企业负责人**/
    private String enterpriseOwners;
    /** 企业负责人员工**/
    private Integer enterpriseOwnersEmployeeId;
    /** 质量负责人**/
    private String qualityOwners;
    /** 质量负责人员工**/
    private Integer qualityOwnersEmployeeId;
    /** 执业药师**/
    private String licensedPharmacist;
    /** 销售数量是否默认 1 是 0 否  */
    private Byte saleAmountYn;
    /** 是否设置抹零区间*/
    private Byte smallChange;
    /** 抹零区间开*/
    private String smallChangeOpen;
    /** 抹零区间闭*/
    private String smallChangeClose;
    /** 是否显示零库存*/
    private Byte zeroInventoryDisplay;
    /** 机构标识*/
    private String organSign;
    /** 是否支持现金找零*/
    private Byte cashChange;
    /** 是否会员生日提醒*/
    private Byte memberReminder;
    /** 是否开启缺货登记*/
    private Byte shortageRegistration;
    /** 是否全员下载*/
    private Byte fullStaffDownload;
    /** 收货复核单价格校验*/
    private Integer checkPrice;
    /** 结算方式*/
    private String settlementMethod;
    /** 是否会员录入提醒*/
    private Byte memberInputReminder;
    /** 企业首营时间*/
    private String businessStartTime;
    /** 商品首营时间*/
    private String productStartTime;
    /** 中药模式 1 是 0 否  */
    private Byte medicineModel;
    /** 默认人员日期可修改 1 是 0 否  */
    private Byte defaultPersonDateModifiable;
    /**优惠券短信提醒开关 1 是 0 否*/
    private Integer couponNoteYn;

    private Integer conventionalCuringCycle;//常规养护周期 单位天
    private Integer criticalCuringPeriod;//重点养护周期 单位天
    private Integer advanceWarningCuring;//养护提前预警天数
    private Integer routineInspectionCycle;//常规检查周期 单位天
    private Integer criticalInspectionCycle;//重点检查周期 单位天
    private Integer advanceWarningInspection;//检查提前预警天数
    private Integer earlyWarningDays;//近效期提前预警天数 默认5天

    /**
     * 健康笔记开关 默认0 否, 1 是
     */
    private Byte healthNoteSwitch;
    
    /**
     * 开启or关闭远程门诊默认1开启,0关闭   
     */
    private Byte remoteInquiry;
    /**
     * WEB端储值卡是否审核  0否1是
     */
    private Byte storedValueAuditWEB;
    /**
     * pos端储值卡是否审核  0否1是
     */
    private Byte storedValueAuditPOS;

        //盘点审核角色ID
    private Integer inventoryAuditRoleId;
    /**
     * 会员储值变化短信提醒 默认0 否, 1 是
     */
    private Byte storedValueSMSRemind;

    /**
     * 促销活动优先级 1--会员日促销，2--活动促销
     */
    private Byte promotionPriority;

    /**
     * 中药验收员
     */
    private String chineseMedicineExaminerId;


    /**
     * 是否开启经营范围，0 否, 1 是，默认开启
     */
    private Byte businessScopeYn;

    /**
     *养护计划天数
     */
    private Integer maintenancePlanData;

    /**
     *实际养护日期可修改
     */
    private Byte maintenanceDataUpdateYn;

    /**
     *原单退货审核开关，0 否, 1 是，默认关闭
     */
    private Byte returnSalesYn;

    /**
     * 处方登记开关，0 否, 1 是，默认关闭
     */
    private Byte prescriptionRegisteredYn;

    /**
     * 供应商资质过期提醒
     */
    private Integer providerQualificationExpiredRemindDays;

    /**
     * 商品资质过期提醒
     */
    private Integer productQualificationExpiredRemindDays;

    /**
     * 滞销天数
     */
    private Integer deadStockWarningDays;

    /**
     * 滞销基数
     */
    private Integer deadStockWarningCount;

    /**
     * 医保进销存提醒
     */
    private Byte saleStorageYn;

    /**
     * 药监监管
     */
    private Byte drugSupervisionYn;

    /**
     * 是否开启盲交班
     */
    private Byte blindShiftHandover;

    /**
     *单据审核级数 1 一级 2 二级 3 三级
     */
    private Byte auditingLevel;

    /**
     *核对人id
     */
    private Integer checkUserId;

    /**
     *发药人id
     */
    private Integer sendDrugUserId;

    /**
     * 是否单选角色
     */
    private Byte singleSelectRoleYn;

    //含麻药品是否登记手机号0 否, 1 是 默认0否
    private Byte recordMobileForNarcoticYn;

    //含麻药品是否登记家庭地址0 否, 1 是 默认0否
    private Byte recordAddressForNarcoticYn;

    /**
     * 是否开启账套 0-否 1-是
     */
    private Byte accountSetYn;

    //默认调价比例
    private BigDecimal defaultAdjustPriceRatio;

    /**
     * 医保显示开关 是否开启账套 0-否 1-是
     */
    private Byte medicalPaySwitch;
    /**
     * 医保项目等级
     */
    private String medicalLevelInfo;

    //处方审核方式
    private Byte prescriptionAuditMode;

    //指纹密码切换
    private Byte fingerprintSwitchYn;
    /**
     * 近效期提前停售天数
     */
    private Integer beforeStopSaleDays;
    /**
     * 处方单有效期
     */
    private Integer prescriptionFormExpiredDays;
    /**
     * 处方用量天数
     */
    private Integer prescriptionProductExpiredDays;

    //购货单位预警天数
    private Integer purchaserWarningDays;
    /**
     * 药品追溯数据上传 0-否 1-是
     */
    private Byte productTraceUploadYn;
    /**
     * 器械UDI数据上传 0-否 1-是
     */
    private Byte deviceUdiSupervisionYn;

    /**
     * (非码上放心)药品追溯数据上传 0-否 1-是
     */
    private Byte productTraceUploadNonMsfxYn;

    /**
     * 同种商品分行 0-否 1-是
     */
    private Byte productSplitRows;


    /**
     * 开启实名登记 0关闭 1开启
     */
    private Byte openRealNameRegistration;

    /**
     * 应收金额精确 0-角,1-分
     */
    private Byte receivableNumber;

    /**
     * 购药赠险短信开关 0-关,1-开
     */
    private Byte adMessageSwitch;

    /**
     * 手机号登记弹窗,0-否,1-是
     */
    private Byte phoneSignSwitch;

    /**
     * 批号库存调整审核开关,0-否,1-是
     */
    private Integer inventoryLotNumAdjustSwitch;

    /**
     * 中药处方登记味数
     */
    private Integer chineseMedicineNumber;

    /**
     * 中药销售单位,1:份数,2:贴数,3:付数
     */
    private Byte chineseMedicineUnit;

    /**
     * 处方一级审核角色编码
     */
    private String auditingFirstRole;

    /**
     * 处方二级审核角色编码
     */
    private String auditingSecondRole;

    /**
     * 处方三级审核角色编码
     */
    private String auditingThirdRole;

    /**
     * POS处方药隐藏单双轨信息
     */
    private Byte hiddenTrackPrescriptionInfo;

    /**
     * 控制POS“医保匹配功能”开关
     */
    private Byte showMedicareMatchBtnYn;

    /**
     * 追溯码销售录入
     */
    private Byte traceCodeSaleEntryYn;

    /**
     * 标识码录入提示  0-  关闭   1 -  开启
     */
    private Byte identInputTipYn;

    /**
     * 恢复回收站商品时审核开关,0-关,1-开
     */
    private Byte productRecoverAuditingYn;
    /**
     * 恢复回收站商品审核角色编码
     */
    private String productRecoverAuditingRole;
    /**
     * 订单商品单价保留2位小数开关,0-关,1-开
     */
    private Byte productPriceTwoDecimalPlacesYn;
    /**
     * 商品小数销售（中药）开关,0-关,1-开
     */
    private Byte productMedicineSaleDecimalYn;
    /**
     * 商品小数销售（非中药）开关,0-关,1-开
     */
    private Byte productDrugSaleDecimalYn;
}