package com.xyy.common.provider.module.api.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSONObject;
import com.xyy.common.module.api.SystemWorkFlowApi;
import com.xyy.common.module.dto.WorkflowDto;
import com.xyy.common.module.dto.WorkflowNodeDto;
import com.xyy.common.module.dto.WorkflowQueryVo;
import com.xyy.common.module.dto.WorkflowResultDto;
import com.xyy.common.module.enums.BusinessFlowEnum;
import com.xyy.common.provider.common.util.EnumUtil;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.dynamic.config.api.client.BusinessFlowConfigApi;
import com.xyy.saas.dynamic.config.api.enums.ShowTypeEnum;
import com.xyy.saas.dynamic.config.api.pojo.Result;
import com.xyy.saas.dynamic.config.api.pojo.result.BusinessFlowConfigDto;
import com.xyy.saas.workflow.api.WorkflowConfService;
import com.xyy.saas.workflow.exception.WorkflowException;
import com.xyy.saas.workflow.model.dto.WorkflowAuditDefinitionDto;
import com.xyy.saas.workflow.model.meta.TenantIdEnum;
import com.xyy.saas.workflow.model.req.WorkFlowUpdateReqDto;
import com.xyy.saas.workflow.model.req.WorkflowCreateReqDto;
import com.xyy.saas.workflow.model.req.WorkflowNodeReqDto;
import com.xyy.saas.workflow.model.req.WorkflowQueryReqDto;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.api.RoleApi;
import com.xyy.user.module.dto.SaasRoleDto;
import com.xyy.user.module.dto.result.EmployeeDto;
import com.xyy.user.provider.module.dto.QueryCertificateConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName SystemWorkFlowApiImpl
 * @Description 系统审批流设置Api实现
 * <AUTHOR>
 * @Date 2020/2/24
 **/
@Service(version = "0.0.1")
public class SystemWorkFlowApiImpl implements SystemWorkFlowApi {
    private static final Logger logger = LoggerFactory.getLogger(SystemWorkFlowApiImpl.class);

    @Reference(version = "1.0.0")
    WorkflowConfService workflowConfService;

    @Autowired
    EmployeeApi employeeApi;

    @Autowired
    RoleApi roleApi;

    @Reference(version = "0.0.1")
    public BusinessFlowConfigApi businessFlowConfigApi;

    @Value("${workflow.showStoreRoleSwitch}")
    private String showStoreRoleSwitch;


    private static Integer FLOW_KIND_SIZE = 16;
    @Override
    public void saveSystemWorkflow(WorkflowDto workflowDto) {
        logger.info("saveSystemWorkflow reqDto:{}", JSONObject.toJSONString(workflowDto));
        try {
            if (workflowDto.getWorkflowId() == 0) {
                WorkflowCreateReqDto reqDto = new WorkflowCreateReqDto();
                reqDto.setTenantId(TenantIdEnum.SAAS.getValue());
                reqDto.setOrganSign(workflowDto.getSaasOrgSign());
                BeanUtils.copyProperties(workflowDto, reqDto);
                List<WorkflowNodeReqDto> nodes = new ArrayList();
                workflowDto.getNodes().forEach(nodeDto -> {
                    WorkflowNodeReqDto node = new WorkflowNodeReqDto();
                    node.setAuto(0); //手动审批
                    BeanUtils.copyProperties(nodeDto, node);
                    nodes.add(node);
                });
                reqDto.setNodes(nodes);
                workflowConfService.add(reqDto);
            } else {
                WorkFlowUpdateReqDto upDto = new WorkFlowUpdateReqDto();
                upDto.setTenantId(TenantIdEnum.SAAS.getValue());
                upDto.setOrganSign(workflowDto.getSaasOrgSign());
                BeanUtils.copyProperties(workflowDto, upDto);
                upDto.setId(workflowDto.getWorkflowId());
                List<WorkflowNodeReqDto> nodes = new ArrayList();
                workflowDto.getNodes().forEach(nodeDto -> {
                    WorkflowNodeReqDto node = new WorkflowNodeReqDto();
                    BeanUtils.copyProperties(nodeDto, node);
                    nodes.add(node);
                });
                upDto.setNodes(nodes);
                workflowConfService.update(upDto);
            }
        } catch (WorkflowException e) {
            logger.error("saveSystemWorkflow is error.", e);
        }
    }

    @Override
    public List<WorkflowDto> selectSystemWorkflowList(WorkflowDto workflowDto) {
        Result<List<BusinessFlowConfigDto>> listResult = businessFlowConfigApi.listAll();
        List<BusinessFlowConfigDto> businessFlowConfigDtos = new ArrayList<>();
        if (listResult.isSuccess() && !CollectionUtils.isEmpty(listResult.getResult())){
            businessFlowConfigDtos = listResult.getResult();
        }
        List<WorkflowDto> initWorkflowList = getFlowKindIds(businessFlowConfigDtos);
        Map<Integer,BusinessFlowConfigDto> businessFlowConfigMap = businessFlowConfigDtos.stream().collect(Collectors.toMap(BusinessFlowConfigDto::getType, p -> p, (k1, k2) -> k1));
        try {
            WorkflowQueryReqDto queryReqDto = new WorkflowQueryReqDto();
            queryReqDto.setTenantId(TenantIdEnum.SAAS.getValue());
            queryReqDto.setOrganSign(workflowDto.getSaasOrgSign());
            List<WorkflowAuditDefinitionDto> workflowDtoList = workflowConfService.queryWorkflow(queryReqDto);
            if (workflowDtoList != null && !workflowDtoList.isEmpty()) {
                for (WorkflowDto workflow : initWorkflowList) {
                    for (WorkflowAuditDefinitionDto workflowAuditDto : workflowDtoList) {
                        if (workflow.getBusinessScene() == workflowAuditDto.getBusinessScene()) {
                            BeanUtils.copyProperties(workflowAuditDto, workflow);
                            BusinessFlowConfigDto businessFlowConfigDto = businessFlowConfigMap.get(Integer.valueOf(workflow.getBusinessScene()));
                            if (null != businessFlowConfigDto){
                                workflow.setName(businessFlowConfigDto.getDescription());
                            }
                            List<WorkflowNodeDto> workflowNodeList = getWorkflowNodeList(workflowAuditDto.getNodes(), workflowDto.getSaasOrgSign());
                            workflow.setNodes(workflowNodeList);
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("selectSystemWorkflowList is error.", e);
        }
        return initWorkflowList;
    }

    /**
     * 初始化审批业务流程列表
     * @return
     */
    private List<WorkflowDto> getFlowKindIds(List<BusinessFlowConfigDto> flowConfigDtos) {
        List<WorkflowDto> kinds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(flowConfigDtos)){
            for (BusinessFlowConfigDto em : flowConfigDtos) {
                if (em.getShowType().intValue() == ShowTypeEnum.HEAD_AUDIT_CONF.getCode().intValue()){
                    WorkflowDto workflow = new WorkflowDto();
                    workflow.setBusinessScene(em.getType());
                    workflow.setName(em.getDescription());
                    kinds.add(workflow);
                }
            }
        }
        return kinds;
    }

    private List<WorkflowDto> getOnLineFlowKindIds(List<BusinessFlowConfigDto> flowConfigDtos) {
        List<WorkflowDto> kinds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(flowConfigDtos)){
            for (BusinessFlowConfigDto em : flowConfigDtos) {
                if (em.getShowType().intValue() == ShowTypeEnum.HEAD_SYS_CONF.getCode().intValue()){
                    WorkflowDto workflow = new WorkflowDto();
                    workflow.setBusinessScene(em.getType());
                    workflow.setName(em.getDescription());
                    kinds.add(workflow);
                }
            }
        }
        return kinds;
    }

    private List<WorkflowNodeDto> getWorkflowNodeList(List<com.xyy.saas.workflow.model.dto.WorkflowNodeDto> nodes, String organSign) {
        List<WorkflowNodeDto> workflowNodeList = new ArrayList<>();
        if (nodes != null && !nodes.isEmpty()) {
            Set<Integer> roleIds = new HashSet<>();
            Set<Integer> employeeIds = new HashSet<>();
            for (com.xyy.saas.workflow.model.dto.WorkflowNodeDto workflowNode : nodes) {
                if (workflowNode.getSaasRoleId() > 0) {
                    roleIds.add((int)workflowNode.getSaasRoleId());
                }
                if (workflowNode.getSaasUserId() > 0) {
                    employeeIds.add((int)workflowNode.getSaasUserId());
                }
            }
            Map<Integer, String> roleMap = new HashMap<>();
            Map<Integer, String> employeeMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(roleIds)) {
                List<SaasRoleDto> roleList = roleApi.getRoleByIdList(new ArrayList<>(roleIds));
                if (!CollectionUtils.isEmpty(roleList)) {
                    roleMap = roleList.stream().collect(Collectors.toMap(SaasRoleDto::getId, p -> p.getName()));
                }
            }
            if (!CollectionUtils.isEmpty(employeeIds)) {
                List<EmployeeDto> employeeList = employeeApi.queryEmployeeByIds(new ArrayList<>(employeeIds));
                if (!CollectionUtils.isEmpty(employeeList)) {
                    employeeMap = employeeList.stream().collect(Collectors.toMap(EmployeeDto::getId, p -> p.getName()));
                }
            }
            for (com.xyy.saas.workflow.model.dto.WorkflowNodeDto workflowNode : nodes) {
                WorkflowNodeDto WorkflowNodeDto = new WorkflowNodeDto();
                BeanUtils.copyProperties(workflowNode, WorkflowNodeDto);
                if (workflowNode.getSaasRoleId() > 0 && !roleMap.isEmpty()) {
                   WorkflowNodeDto.setName(roleMap.get((int)workflowNode.getSaasRoleId()));
                }
                if (workflowNode.getSaasUserId() > 0 && !employeeMap.isEmpty()) {
                   WorkflowNodeDto.setName(employeeMap.get((int)workflowNode.getSaasUserId()));
                }
                workflowNodeList.add(WorkflowNodeDto);
            }
        }
        logger.info("getWorkflowList organSign:{}, workflowList:{}", organSign, JSONObject.toJSONString(workflowNodeList));
        return workflowNodeList;
    }

    @Override
    public List<WorkflowNodeDto> getSystemWorkflowById(WorkflowQueryVo queryVo) {
        List<WorkflowNodeDto> workflowNodeList = new ArrayList<>();
        try {
            WorkflowAuditDefinitionDto workflowAuditDto = workflowConfService.query(queryVo.getWorkflowId(), TenantIdEnum.SAAS.getValue());
            logger.info("getSystemWorkflowById workflowId:{}, result:{}", queryVo.getWorkflowId(), JSONObject.toJSONString(workflowAuditDto));
            if (workflowAuditDto != null && !workflowAuditDto.getNodes().isEmpty()) {
                workflowNodeList = getWorkflowNodeList(workflowAuditDto.getNodes(), workflowAuditDto.getSaasOrgSign());
            }
        } catch (Exception e) {
            logger.error("getSystemWorkflowById is error.", e);
        }
        return workflowNodeList;
    }

    @Override
    public WorkflowResultDto getSystemWorkflowByOrganSign(WorkflowQueryVo queryVo) {
        WorkflowResultDto workflowResult = new WorkflowResultDto();
        try {
            WorkflowQueryReqDto queryReqDto = new WorkflowQueryReqDto();
            queryReqDto.setTenantId(TenantIdEnum.SAAS.getValue());
            queryReqDto.setOrganSign(queryVo.getOrganSign());
            queryReqDto.setBusinessScene(queryVo.getBusinessScene());
            List<WorkflowAuditDefinitionDto> workflowDtoList = workflowConfService.queryWorkflow(queryReqDto);
            if (!CollectionUtils.isEmpty(workflowDtoList)) {
                WorkflowAuditDefinitionDto workflowAuditDto = workflowDtoList.get(0);
                workflowResult.setExist(true);
                workflowResult.setAuto(true);
                List<Integer> employeeIds = new ArrayList<>();
                if (!CollectionUtils.isEmpty(workflowAuditDto.getNodes())) {
                    for (com.xyy.saas.workflow.model.dto.WorkflowNodeDto node : workflowAuditDto.getNodes()) {
                        if (node.getAuto() == 0) {
                            //手动审批
                            workflowResult.setAuto(false);
                        } else {
                            employeeIds.add((int)node.getSaasUserId());
                        }
                    }
                }
                //查询审批人是否已禁用
                if (queryVo.isEmployeeFlag() && !CollectionUtils.isEmpty(employeeIds)) {
                    List<EmployeeDto> employeeList = employeeApi.queryEmployeeByIds(employeeIds);
                    logger.info("getSystemWorkflowByOrganSign organSign:{}, employeeIds:{}, result:{}", queryVo.getOrganSign(),
                            JSONObject.toJSON(employeeIds), JSONObject.toJSONString(employeeList));
                    if (!CollectionUtils.isEmpty(employeeList)) {
                        List<String> disabledName = new ArrayList<>();
                        employeeList.forEach(item -> {
                            if (item.getIsDisabled() == 1) {
                                //员工被禁用
                                disabledName.add(item.getName());
                            }
                        });
                        workflowResult.setEmployeeList(disabledName);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("getSystemWorkflowByOrganSign is error.", e);
        }
        return workflowResult;
    }

    @Override
    public List<WorkflowDto> selectOnlineSystemWorkflowList(WorkflowDto workflowDto) {
        Result<List<BusinessFlowConfigDto>> listResult = businessFlowConfigApi.listAll();
        List<BusinessFlowConfigDto> businessFlowConfigDtos = new ArrayList<>();
        if (listResult.isSuccess() && !CollectionUtils.isEmpty(listResult.getResult())){
            businessFlowConfigDtos = listResult.getResult();
        }
        List<WorkflowDto> initWorkflowList = getOnLineFlowKindIds(businessFlowConfigDtos);
        Map<Integer,BusinessFlowConfigDto> businessFlowConfigMap = businessFlowConfigDtos.stream().collect(Collectors.toMap(BusinessFlowConfigDto::getType, p -> p, (k1, k2) -> k1));
        try {
            WorkflowQueryReqDto queryReqDto = new WorkflowQueryReqDto();
            queryReqDto.setTenantId(TenantIdEnum.SAAS.getValue());
            queryReqDto.setOrganSign(workflowDto.getSaasOrgSign());
            List<WorkflowAuditDefinitionDto> workflowDtoList = workflowConfService.queryWorkflow(queryReqDto);
            if (workflowDtoList != null && !workflowDtoList.isEmpty()) {
                for (WorkflowDto workflow : initWorkflowList) {
                    for (WorkflowAuditDefinitionDto workflowAuditDto : workflowDtoList) {
                        if (workflow.getBusinessScene() == workflowAuditDto.getBusinessScene()) {
                            BeanUtils.copyProperties(workflowAuditDto, workflow);
                            BusinessFlowConfigDto businessFlowConfigDto = businessFlowConfigMap.get(workflow.getBusinessScene());
                            workflow.setName(businessFlowConfigDto.getDescription());
                            List<WorkflowNodeDto> workflowNodeList = getWorkflowNodeList(workflowAuditDto.getNodes(), workflowDto.getSaasOrgSign());
                            workflow.setNodes(workflowNodeList);
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("selectSystemWorkflowList is error.", e);
        }
        return initWorkflowList;
    }

    @Override
    public ResultVO<String[]> getShowStoreRoleSwitch() {
        String s = showStoreRoleSwitch;
        String[] ss = s.split(",");
        return ResultVO.createSuccess(ss);
    }
}