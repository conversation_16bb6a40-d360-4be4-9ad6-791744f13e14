package com.xyy.common.provider.module.dao;

import com.dianping.zebra.dao.datasource.ZebraRouting;
import com.xyy.common.module.dto.MessageNoticeDto;
import com.xyy.common.provider.module.entity.MessageLatestPo;
import com.xyy.common.provider.module.entity.MessageNoticeEmployeePo;
import com.xyy.common.provider.module.entity.MessageNoticePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@ZebraRouting("common")
public interface MessageNoticeMapper {

    Integer insert(MessageNoticePo messageNoticePo);

    Integer update(MessageNoticePo messageNoticePo);

    MessageNoticePo selectById(Long id);

    List<MessageNoticePo> getMessageNoticeListPager(MessageNoticePo messageNoticePo);

    List<MessageNoticePo> getMessageNoticeByEmployeeIdPager(@Param("list") List<MessageNoticeEmployeePo> list, @Param("messageNotice") MessageNoticeDto messageNotice);

    Integer updateState(MessageNoticePo messageNoticePo);

    List<String> selectReleasePeopleId();

    MessageLatestPo getMessageNoticeIdLatest(MessageLatestPo bean);

    /**
     * 根据条件查noticeId
     * @param noticePo
     * @return
     */
    List<MessageNoticePo> getMessageNoticeIds(MessageLatestPo noticePo);
}