package com.xyy.common.provider.module.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xyy.common.module.dto.SaasBusinessScopeDto;
import com.xyy.common.provider.common.constant.RedisConstants;
import com.xyy.common.provider.common.constant.SystemConstans;
import com.xyy.common.provider.common.util.SendMqMsgUtils;
import com.xyy.common.provider.module.dao.SaasBusinessScopePoMapper;
import com.xyy.common.provider.module.entity.SaasBusinessScopePo;
import com.xyy.common.provider.module.service.SaasBusinessScopeService;
import com.xyy.saas.common.dto.QueryDictByOrgTypeDto;
import com.xyy.saas.common.dto.SystemDictDto;
import com.xyy.saas.common.dto.SystemDictQueryDto;
import com.xyy.saas.common.util.DateUtil;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.dto.restructure.BusinessScopeDto;
import com.xyy.user.module.dto.restructure.QualificationInfo;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import com.xyy.user.module.dto.restructure.SaasImgUrlDto;
import com.xyy.user.module.enums.*;
import com.xyy.user.module.vo.ImgUrlVo;
import com.xyy.user.module.vo.QualificationInfoVo;
import com.xyy.user.provider.common.util.JedisUtil;
import com.xyy.user.provider.module.dao.SaasCertificateBusinessScopeMapper;
import com.xyy.user.provider.module.dao.SaasCertificateTypeMapper;
import com.xyy.user.provider.module.dao.SaasImgUrlMapper;
import com.xyy.user.provider.module.dao.SaasPurchaserQualificationMapper;
import com.xyy.user.provider.module.dto.QueryCertificateConfig;
import com.xyy.user.provider.module.entity.*;
import com.xyy.user.provider.module.service.CommonSystemDictService;
import com.xyy.user.provider.module.utils.StringUtil;
import com.xyy.user.provider.module.vo.QueryImageUrlVo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.xyy.user.module.enums.CertificateTypeEnum.getCertificateTypeEnum;

@Component
public class SaasBusinessScopeServiceImpl implements SaasBusinessScopeService {

    private static final Logger logger = LoggerFactory.getLogger(SaasBusinessScopeServiceImpl.class);

    @Autowired
    private SaasBusinessScopePoMapper saasBusinessScopePoMapper;

    @Autowired
    private CommonSystemDictService commonSystemDictService;

    @Autowired
    private JedisUtil jedisUtil;

    @Autowired
    private DrugstoreApi drugstoreApi;

    @Value("${user.query.certificate.config}")
    protected String queryCertificateConfig;

    @Autowired
    private SaasPurchaserQualificationMapper saasPurchaserQualificationMapper;

    @Autowired
    protected SaasCertificateTypeMapper certificateTypeMapper;

    @Autowired
    private SaasImgUrlMapper saasImgUrlMapper;

    @Autowired
    private SaasCertificateBusinessScopeMapper certificateBusinessScopeMapper;

    @Autowired
    private SendMqMsgUtils sendMqMsgUtils;

    @Override
    public List<SaasBusinessScopePo> selectSaasBusinessScopeList(SaasBusinessScopeDto saasBusinessScopeDto) {

        return saasBusinessScopePoMapper.selectSaasBusinessScopeList(saasBusinessScopeDto);
    }

    @Override
    public SaasBusinessScopePo selectSaasBusinessScope(SaasBusinessScopeDto saasBusinessScopeDto) {

        return saasBusinessScopePoMapper.selectSaasBusinessScope(saasBusinessScopeDto);
    }

    @Override
    public int updateSaasBusinessScope(SaasBusinessScopeDto saasBusinessScopeDto) {
        SaasBusinessScopePo po = new SaasBusinessScopePo();
        BeanUtils.copyProperties(saasBusinessScopeDto, po);
        po.setBaseVersion(jedisUtil.incr(RedisConstants.SAAS_BUSINESS_SCOPE_BASE_VERSION + saasBusinessScopeDto.getOrganSign()) + "");
        return saasBusinessScopePoMapper.updateByPrimaryKeySelective(po);
    }

    @Override
    public void updateSaasBusinessScopeData(SystemDictDto systemDictPo,Integer cerType) {
        logger.info("systemDictPo:" + JSONObject.toJSONString(systemDictPo));
        SaasBusinessScopeDto saasBusinessScopeDto = new SaasBusinessScopeDto();
        saasBusinessScopeDto.setDictId(systemDictPo.getId());
        saasBusinessScopeDto.setOrganSign(systemDictPo.getOrganSign());
        SaasBusinessScopePo saasBusinessScopePo = saasBusinessScopePoMapper.selectSaasBusinessScope(saasBusinessScopeDto);
        SaasBusinessScopePo po = new SaasBusinessScopePo();
        BeanUtils.copyProperties(systemDictPo, po);
        po.setDictId(systemDictPo.getId());
        if (saasBusinessScopePo != null) {//更新
            po.setCertificateType(cerType);
            if (systemDictPo.getCreateUser() == null) {
                po.setUpdateUser("");
            } else {
                po.setUpdateUser(String.valueOf(systemDictPo.getCreateUser()));
            }
            if (systemDictPo.getStatus() != null && systemDictPo.getStatus() == 0) {
                po.setStatus(new Byte(SystemConstans.BUSINESS_SCOPE_YN_NO));
            } else if (systemDictPo.getStatus() != null && systemDictPo.getStatus() == 1) {
                po.setStatus(new Byte(SystemConstans.BUSINESS_SCOPE_YN_YES));
            }
            if (systemDictPo.getYn() != null && systemDictPo.getYn() == 0) {
                po.setYn(new Byte(SystemConstans.BUSINESS_SCOPE_YN_NO));
            } else if (systemDictPo.getYn() != null && systemDictPo.getYn() == 1) {
                po.setYn(new Byte(SystemConstans.BUSINESS_SCOPE_YN_YES));
            }
            po.setUpdateTime(new Date());
            po.setBaseVersion(jedisUtil.incr(RedisConstants.SAAS_BUSINESS_SCOPE_BASE_VERSION + systemDictPo.getOrganSign()) + "");
            saasBusinessScopePoMapper.updateByPrimaryKeySelective(po);
        } else {//新增
            po.setCertificateType(cerType);
            po.setEnableYn(SystemConstans.BUSINESS_SCOPE_YN_NO);
            po.setUpdateUser("");
            po.setUpdateTime(new Date());
            po.setBaseVersion(jedisUtil.incr(RedisConstants.SAAS_BUSINESS_SCOPE_BASE_VERSION + systemDictPo.getOrganSign()) + "");
            saasBusinessScopePoMapper.insertSelective(po);
        }
    }

    @Override
    public int updateEnableYn(List<SaasBusinessScopeDto> saasBusinessScopeDto, String organSign) {
        for (SaasBusinessScopeDto dto : saasBusinessScopeDto) {
            dto.setBaseVersion(jedisUtil.incr(RedisConstants.SAAS_BUSINESS_SCOPE_BASE_VERSION + organSign) + "");
        }
        logger.info("批量修改禁用启用：" + JSONObject.toJSONString(saasBusinessScopeDto));
        return saasBusinessScopePoMapper.updateEnableYnById(saasBusinessScopeDto);
    }

    @Override
    public List<SaasBusinessScopePo> syncData(SaasBusinessScopeDto dto) {
        return saasBusinessScopePoMapper.syncData(dto);
    }

    @Override
    public void initData(SaasBusinessScopeDto saasBusinessScopeDto) {
        //兼容连锁门店
        String organSign=saasBusinessScopeDto.getOrganSign();
        SaaSDrugstoreDto drugstore = drugstoreApi.getDrugstoreByOrganSign(saasBusinessScopeDto.getOrganSign());
        if(DrugstoreTypeEnum.DRUGSTORE.toEquals(drugstore.getOrganSignType()) && DrugstoreBizModelEnum.CHAIN_STORE.toEquals(drugstore.getBizModel()) ){
            organSign=drugstore.getHeadquartersOrganSign();
        }
        List<String> stringList= new ArrayList<>();
//        List<SystemDictDto> systemDicts = systemDictApi.findSystemDictDtoByBussinessId(SystemConstans.BUSINESS_SCOPE_ID, organSign, null);
        QueryDictByOrgTypeDto queryDto = new QueryDictByOrgTypeDto();
        queryDto.setBusinessId(""+SystemConstans.BUSINESS_SCOPE_ID);
        queryDto.setYn((byte) 1);
        queryDto.setOrganSign(organSign);
        queryDto.setOrgType(drugstore.getBizModel());
        List<SystemDictDto> systemDicts = commonSystemDictService.queryDictByOrgType(queryDto);
        List<SaasBusinessScopePo> pos = new ArrayList<>();
        if( DrugstoreBizModelEnum.CHAIN_STORE.toEquals(drugstore.getBizModel()) ){
             stringList = commonSystemDictService.ignoreSystemDictIds();
        }

        //key:经营范围,value:资质
        Map<Integer, Integer> scopeAndType = getType();
        if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(drugstore.getBizModel())){
           List<SaaSDrugstoreDto> drugstoreList = drugstoreApi.getDrugstoreByHeadquartersOrganSign(organSign);
           for(SaaSDrugstoreDto saaSDrugstoreDto :drugstoreList) {
               SaasBusinessScopeDto saasBusinessScopeDto1 = new SaasBusinessScopeDto();
               saasBusinessScopeDto1.setOrganSign(saaSDrugstoreDto.getOrganSign());
               List<SaasBusinessScopePo> saasBusinessScopeDtos = selectSaasBusinessScopeList(saasBusinessScopeDto1);
               if (saasBusinessScopeDtos == null || saasBusinessScopeDtos.size() == 0) {
                   for (SystemDictDto systemDict : systemDicts) {
                       if (stringList.contains(systemDict.getId().toString()) || null == scopeAndType.get(systemDict.getId()) && null == scopeAndType.get(systemDict.getParentId())) {
                           continue;
                       }
                       SaasBusinessScopePo po = new SaasBusinessScopePo();
                       BeanUtils.copyProperties(systemDict, po);
                       po.setOrganSign(saaSDrugstoreDto.getOrganSign());
                       po.setDictId(systemDict.getId());
                       //po.setEnableYn(systemDict.getStatus());
                       po.setEnableYn(SystemConstans.BUSINESS_SCOPE_YN_NO);
                       po.setCreateTime(new Date());
                       po.setStatus(systemDict.getStatus());
                       po.setBaseVersion(jedisUtil.incr(RedisConstants.SAAS_BUSINESS_SCOPE_BASE_VERSION + saaSDrugstoreDto.getOrganSign()) + "");
                       po.setCreateUser(saasBusinessScopeDto.getCreateUser() == null ? "" : saasBusinessScopeDto.getCreateUser());
                       po.setUpdateUser(saasBusinessScopeDto.getUpdateUser() == null ? "" : saasBusinessScopeDto.getUpdateUser());
                       po.setUpdateTime(new Date());
                       po.setCertificateType(null == scopeAndType.get(systemDict.getId())? scopeAndType.get(systemDict.getParentId()):scopeAndType.get(systemDict.getId()));
                       pos.add(po);
                   }
               }
           }
       }
        SaasBusinessScopeDto saasBusinessScopeDto2 = new SaasBusinessScopeDto();
        saasBusinessScopeDto2.setOrganSign(organSign);
        List<SaasBusinessScopePo> saasBusinessScopeDtos = selectSaasBusinessScopeList(saasBusinessScopeDto2);
        if (saasBusinessScopeDtos == null || saasBusinessScopeDtos.size() == 0) {
            for (SystemDictDto systemDict : systemDicts) {
                if (stringList.contains(systemDict.getId().toString())) {
                    continue;
                }
                //连锁的经营范围绑定资质,不在资质内的经营范围不需要
                if (BizModelEnum.CHAIN_STORE.toEquals(drugstore.getBizModel()) && null == scopeAndType.get(systemDict.getId()) && null == scopeAndType.get(systemDict.getParentId())){
                    continue;
                }
                SaasBusinessScopePo po = new SaasBusinessScopePo();
                BeanUtils.copyProperties(systemDict, po);
                po.setOrganSign(organSign);
                po.setDictId(systemDict.getId());
                //po.setEnableYn(systemDict.getStatus());
                if (DrugstoreBizModelEnum.CHAIN_STORE.toEquals(drugstore.getBizModel())) {
                    po.setEnableYn(SystemConstans.BUSINESS_SCOPE_YN_NO);
                }else {
                    po.setEnableYn(systemDict.getStatus());
                }
                po.setCreateTime(new Date());
                po.setStatus(systemDict.getStatus());
                po.setBaseVersion(jedisUtil.incr(RedisConstants.SAAS_BUSINESS_SCOPE_BASE_VERSION + organSign) + "");
                po.setCreateUser(saasBusinessScopeDto.getCreateUser() == null ? "" : saasBusinessScopeDto.getCreateUser());
                po.setUpdateUser(saasBusinessScopeDto.getUpdateUser() == null ? "" : saasBusinessScopeDto.getUpdateUser());
                po.setUpdateTime(new Date());
                po.setCertificateType(null == scopeAndType.get(systemDict.getId())? scopeAndType.get(systemDict.getParentId()):scopeAndType.get(systemDict.getId()));
                pos.add(po);
            }
        }
        if(pos.size()>0) {
            saasBusinessScopePoMapper.batchInsert(pos);
        }
    }

    @Override
    public ResultVO<List<QualificationInfoVo>> selectNBusinessScopeNew(SaasBusinessScopeDto saasBusinessScopeDto) {
        QueryCertificateEnum queryCertificateEnum = QueryCertificateEnum.PURCHASER;
        List<QualificationInfoVo> qualificationInfos = new ArrayList<>();

        List<SaasCertificateType> saasCertificateTypes = new ArrayList<>();
        List<QueryCertificateConfig> queryCertificateConfigs = JSON.parseArray(queryCertificateConfig, QueryCertificateConfig.class);
        Map<Integer, QueryCertificateConfig> certificateConfigMap = queryCertificateConfigs.stream().collect(Collectors.toMap(QueryCertificateConfig::getType, p -> p, (k1, k2) -> k1));

        int key = queryCertificateEnum.getKey();
        QueryCertificateConfig queryCertificateConfig = certificateConfigMap.get(key);
        List<SaasCertificateType> certificateTypes = certificateTypeMapper.selectAll();
        for (SaasCertificateType saasCertificateType : certificateTypes) {
            if (queryCertificateConfig.getIds().contains(saasCertificateType.getType())){
                saasCertificateTypes.add(saasCertificateType);
            }
        }

        String organSign = saasBusinessScopeDto.getOrganSign();
        //查询资质配置的经营范围
        List<SaasBusinessScopePo> scopePos = saasBusinessScopePoMapper.selectSaasBusinessScopeList(saasBusinessScopeDto);
        Map<Integer, List<SaasBusinessScopePo>> groupByMap = scopePos.stream().collect(Collectors.groupingBy(SaasBusinessScopePo::getCertificateType));
        if (!CollectionUtils.isEmpty(saasCertificateTypes)){
            for (SaasCertificateType saasCertificateType : saasCertificateTypes) {
                QualificationInfoVo qualificationInfo = new QualificationInfoVo();
                qualificationInfo.setCertificateType(saasCertificateType.getType());
                List<Integer> businessScope = new ArrayList<>();
                List<ImgUrlVo> imgUrlDtos = new ArrayList<>();
                Integer type = saasCertificateType.getType();
                ImageTypeEnum imageTypeEnum = ImageTypeEnum.certificateTypeToImageType(type);
                if (null != imageTypeEnum){
                    QueryImageUrlVo imageUrlVo = new QueryImageUrlVo();
                    imageUrlVo.setOrganSign(organSign);
                    imageUrlVo.setType(imageTypeEnum.getKey());
                    List<SaasImgUrlDto> saasImgUrlDtos = saasImgUrlMapper.selectImageUrlByOrganSignAndType(imageUrlVo);
                    if (!CollectionUtils.isEmpty(saasImgUrlDtos)){
                        for (SaasImgUrlDto item:saasImgUrlDtos) {
                            ImgUrlVo vo = new ImgUrlVo();
                            vo.setUrl(item.getImgUrl());
                            vo.setType(item.getType());
                            vo.setOrganSign(item.getOrganSign());
                            vo.setId(item.getId());
                            imgUrlDtos.add(vo);
                        }
                    }
                }
                List<SaasBusinessScopePo> list = groupByMap.get(saasCertificateType.getType());
                if (!CollectionUtils.isEmpty(list)){
                    for (SaasBusinessScopePo businessScopePo : list) {
                        businessScope.add(businessScopePo.getDictId());
                    }
                }
                qualificationInfo.setBusinessScopeIds(businessScope);
                qualificationInfo.setImgUrls(imgUrlDtos);
                //根据机构号查询资质过期信息
                SaasPurchaserQualification qualification = saasPurchaserQualificationMapper.selectByNoAndType(organSign, saasCertificateType.getType().byteValue());
                if (null != qualification){
                    qualificationInfo.setCertificateNo(qualification.getCertificateNo());
                    if (null != qualification.getCertificateDate()){
                        String date = DateUtil.parseDateToStr(qualification.getCertificateDate(), DateUtil.DATE_FORMAT_YYYY_MM_DD);
                        qualificationInfo.setCertificateDate(date);
                    }
                    if (null != qualification.getExpiryDateDate()){
                        String date = DateUtil.parseDateToStr(qualification.getExpiryDateDate(), DateUtil.DATE_FORMAT_YYYY_MM_DD);
                        qualificationInfo.setExpiryDateDate(date);
                    }
                }

                if (!CollectionUtils.isEmpty(qualificationInfo.getImgUrls())||!CollectionUtils.isEmpty(qualificationInfo.getBusinessScopeIds())
                        ||!StringUtil.isEmpty(qualificationInfo.getCertificateDate()) || !StringUtil.isEmpty(qualificationInfo.getExpiryDateDate()) ||
                        !StringUtil.isEmpty(qualificationInfo.getCertificateNo()) ){
                    qualificationInfos.add(qualificationInfo);
                }
            }

        }
        return ResultVO.createSuccess(qualificationInfos);
    }

    @Override
    public int deleteByParam(SaasBusinessScopePo po) {
        return saasBusinessScopePoMapper.deleteByParam(po);
    }

    @Override
    public int deleteByOrgansAndDicts(List<String> organSigns,List<Integer> dicts) {
        return saasBusinessScopePoMapper.deleteByOrgansAndDicts(organSigns, dicts);
    }

    @Override
    public void chainInsert(List<Integer> dtos,Long employeeId,String organSign,Integer certificateType){
        Map<Integer, String> businessScopeToMap = commonSystemDictService.getBusinessScopeToMap(organSign);
        List<SaasBusinessScopePo> businessScopePos = convertBusinessScope(dtos, employeeId, organSign, certificateType,businessScopeToMap);
        if (!CollectionUtils.isEmpty(businessScopePos)){
            saasBusinessScopePoMapper.batchInsert(businessScopePos);
        }
    }

    private List<SaasBusinessScopePo> convertBusinessScope(List<Integer> dtos,Long employeeId,String organSign,Integer certificateType,Map<Integer, String> businessScopeToMap){
        if (!CollectionUtils.isEmpty(dtos)){
            List<SaasBusinessScopePo> businessScopePos = new ArrayList<>();
            for (Integer dto:dtos) {
                if (null == dto || null == businessScopeToMap.get(dto)){
                    continue;
                }
                SaasBusinessScopePo po = new SaasBusinessScopePo();
                po.setCertificateType(certificateType);
                po.setCreateTime(new Date());
                po.setUpdateTime(new Date());
                po.setCreateUser(null == employeeId? "zio":String.valueOf(employeeId));
                po.setUpdateUser(null == employeeId? "zio":String.valueOf(employeeId));
                po.setEnableYn((byte) 1);
                po.setYn((byte) 1);
                po.setStatus((byte) 1);
                po.setSort(0);
                po.setDictId(dto);
                po.setName(businessScopeToMap.get(dto));
                po.setBaseVersion(String.valueOf(jedisUtil.incr(RedisConstants.SAAS_BUSINESS_SCOPE_BASE_VERSION + organSign)));
                po.setOrganSign(organSign);
                businessScopePos.add(po);
            }
            return businessScopePos;
        }else {
            return null;
        }
    }

    @Override
    public List<SaasBusinessScopePo> getBusinessScopeByOrgan(String organSign){
        SaasBusinessScopeDto saasBusinessScopeDto = new SaasBusinessScopeDto();
        saasBusinessScopeDto.setOrganSign(organSign);
        saasBusinessScopeDto.setEnableYn((byte) 1);
        //查询资质配置的经营范围
        return saasBusinessScopePoMapper.selectSaasBusinessScopeList(saasBusinessScopeDto);
    }

    @Override
    public Map<Integer, Boolean> selectInBizScopeFlagMap(List<Integer> businessScopeList, String organSign) {
        if (CollectionUtils.isEmpty(businessScopeList) || StringUtils.isEmpty(organSign)) {
            return new HashMap<>();
        }
        Map<Integer, Boolean> resultMap = new HashMap<>(businessScopeList.size());
        for (Integer bizScope : businessScopeList) {
            resultMap.put(bizScope, false);
        }

        SaaSDrugstoreDto drugstore = drugstoreApi.getDrugstoreByOrganSign(organSign);

        SaasBusinessScopeDto queryDto = new SaasBusinessScopeDto();
        queryDto.setOrganSign(organSign);
        queryDto.setStatus((byte) 1);
        queryDto.setEnableYn((byte) 1);
        if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(drugstore.getBizModel())) {
            queryDto.setChainStoreFlag(true);
        }
        List<Integer> validBizScopeIdList = saasBusinessScopePoMapper.selectSaasBusinessScopeIdList(queryDto);
        for (Integer bizScope : validBizScopeIdList) {
            if (resultMap.get(bizScope) != null) {
                resultMap.put(bizScope, true);
            }
        }
        return resultMap;
    }

    @Override
    public Map<String, Integer> getNameAndId(String organSign){
        SaaSDrugstoreDto drugstore = drugstoreApi.getDrugstoreByOrganSign(organSign);
        SaasBusinessScopeDto queryDto = new SaasBusinessScopeDto();
        queryDto.setOrganSign(organSign);
        queryDto.setStatus((byte) 1);
        queryDto.setEnableYn((byte) 1);
        if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(drugstore.getBizModel())) {
            queryDto.setChainStoreFlag(true);
        }
        List<SaasBusinessScopePo> businessScopePos = saasBusinessScopePoMapper.selectSaasBusinessScopeList(queryDto);
        return businessScopePos.stream().collect(Collectors.toMap(SaasBusinessScopePo::getName,SaasBusinessScopePo::getDictId,(key1,key2)->key2));
    }

    @Override
    public void RefreshBusinessScopeRepeate(String organSign) {
        List<SaasBusinessScopePo> noRepeateId = saasBusinessScopePoMapper.getNoRepeateId(organSign);
        logger.info("SaasBusinessScopeServiceImpl#RefreshBusinessScopeRepeate current:{},size:{}",organSign,noRepeateId.size());
        if (!CollectionUtils.isEmpty(noRepeateId)) {
            List<Long> ids = noRepeateId.stream().map(item -> Long.valueOf(item.getId())).collect(Collectors.toList());
            saasBusinessScopePoMapper.deleteNotMax(organSign, ids);
        }
    }

    @Override
    public void RefreshType(String organSign){
        Map<Integer, Integer> scopeAndType = getType();

        SaasBusinessScopeDto saasBusinessScopeDto = new SaasBusinessScopeDto();
        saasBusinessScopeDto.setOrganSign(organSign);
        List<SaasBusinessScopePo> saasBusinessScopeDtos = saasBusinessScopePoMapper.selectSaasBusinessScopeList(saasBusinessScopeDto);
        logger.info("SaasBusinessScopeServiceImpl#RefreshType organ:"+organSign + ":" + saasBusinessScopeDtos.size());
        if (!CollectionUtils.isEmpty(saasBusinessScopeDtos)) {
            for (SaasBusinessScopePo item : saasBusinessScopeDtos) {
                item.setCertificateType(null == scopeAndType.get(item.getDictId()) ? 0 : scopeAndType.get(item.getDictId()));
                saasBusinessScopePoMapper.updateByPrimaryKeySelective(item);
            }
        }
    }

    @Override
    public int deleteByDictId(String organSign,List<Long> list){
        return saasBusinessScopePoMapper.deleteByDictId(organSign,list);
    }

    //获取要点绑定的所有
    @Override
    public Map<Integer, Integer> getType(){
        QueryCertificateEnum queryCertificateEnum = QueryCertificateEnum.PURCHASER;

        List<QueryCertificateConfig> queryCertificateConfigs = JSON.parseArray(queryCertificateConfig, QueryCertificateConfig.class);
        Map<Integer, QueryCertificateConfig> certificateConfigMap = queryCertificateConfigs.stream().collect(Collectors.toMap(QueryCertificateConfig::getType, p -> p, (k1, k2) -> k1));
        int key = queryCertificateEnum.getKey();
        QueryCertificateConfig queryCertificateConfig = certificateConfigMap.get(key);
        List<Integer> ids = queryCertificateConfig.getIds();
        List<SaasCertificateBusinessScope> scopeList = certificateBusinessScopeMapper.selectByTypes(ids);
        Map<Integer, Integer> scopeAndType = scopeList.stream().collect(Collectors.toMap(SaasCertificateBusinessScope::getBusinessScope,SaasCertificateBusinessScope::getCertificateType,(k1,k2) -> k1));
        return scopeAndType;
    }

    @Override
    public int RefreshCustomBS(String organSign,List<Long> ids){
        int customBS = saasBusinessScopePoMapper.refreshCustomBS(organSign, ids);
        return customBS;
    }

    @Override
    public void updateEnableYnByDict(List<Integer> dict,String organSign,Long employeeId){
        List<SaasBusinessScopeDto> param = new ArrayList<>();
        //从数据库取的是全量数据
        List<SaasBusinessScopePo> scopeByOrgan = this.getBusinessScopeByOrgan(organSign);

        //对比前端传的判断有哪些禁用哪些启用
        Map<Integer, Integer> paramScope = dict.stream().collect(Collectors.toMap(Function.identity(),Function.identity(),(k1, k2)->k2));

        //筛选出取消勾选的
        for (SaasBusinessScopePo po:scopeByOrgan) {
            if (null == paramScope.get(po.getDictId())){
                SaasBusinessScopeDto item = new SaasBusinessScopeDto();
                item.setId(po.getId());
                item.setUpdateUser(null == employeeId? "system":String.valueOf(employeeId));
                item.setEnableYn(SystemConstans.BUSINESS_SCOPE_YN_NO);
                item.setDictId(po.getDictId());
                paramScope.remove(po.getDictId());
                param.add(item);
            }
        }

        SaaSDrugstoreDto drugstoreDto = drugstoreApi.getDrugstoreByOrganSign(organSign);
        //此处说明有需要取消勾选的经营范围,门店也要同步取消勾选
        if (!CollectionUtils.isEmpty(param) && OrganSignTypeEnum.HEADQUARTERS.toEquals(drugstoreDto.getOrganSignType())){
            List<SaaSDrugstoreDto> drugstore = drugstoreApi.getDrugstoreByHeadquartersOrganSign(organSign);
            List<String> organSigns = drugstore.stream().map(SaaSDrugstoreDto::getOrganSign).collect(Collectors.toList());
            for (String subOrganSign : organSigns) {
                this.updateEnableYnWithDict(param, subOrganSign);

                //发消息通知POS同步数据
                sendMqMsgUtils.pushMessToMQ(subOrganSign, SystemConstans.TABLE_NAME_SAAS_BUSINESS_SCOPE);
            }
        }

        //剩下的都是勾选的
        for (Integer dictId: paramScope.values()) {
            SaasBusinessScopeDto item = new SaasBusinessScopeDto();
            item.setUpdateUser(null == employeeId? "system":String.valueOf(employeeId));
            item.setEnableYn((byte) 1);
            item.setDictId(dictId);
            param.add(item);
        }

        if (!CollectionUtils.isEmpty(param)){
            this.updateEnableYnWithDict(param,organSign);
        }
    }

    @Override
    public int updateEnableYnWithDict(List<SaasBusinessScopeDto> saasBusinessScopeDto,String organSign) {
        for (SaasBusinessScopeDto dto : saasBusinessScopeDto) {
            dto.setBaseVersion(jedisUtil.incr(RedisConstants.SAAS_BUSINESS_SCOPE_BASE_VERSION + organSign) + "");
        }
        logger.info("updateEnableYnWithDict：" + JSONObject.toJSONString(saasBusinessScopeDto));
        return saasBusinessScopePoMapper.updateEnableYnByDict(saasBusinessScopeDto,organSign);
    }

    @Override
    public void RefreshinitData(SaasBusinessScopeDto saasBusinessScopeDto,SaasDrugstore initDrug) {
        //兼容连锁门店
        String organSign=saasBusinessScopeDto.getOrganSign();
        SaaSDrugstoreDto drugstore = null;
        //init不为null表示是系统初始化那边调用过来的,不需要查表,因为表里此时可能没有数据
        if (null != initDrug){
            drugstore = new SaaSDrugstoreDto();
            drugstore.setOrganSign(initDrug.getOrganSign());
            drugstore.setHeadquartersOrganSign(initDrug.getHeadquartersOrganSign());
            drugstore.setBizModel(initDrug.getBizModel());
            drugstore.setOrganSignType(initDrug.getOrganSignType());
        }else {
            drugstore = drugstoreApi.getDrugstoreByOrganSign(saasBusinessScopeDto.getOrganSign());
        }
        if(DrugstoreTypeEnum.DRUGSTORE.toEquals(drugstore.getOrganSignType()) && DrugstoreBizModelEnum.CHAIN_STORE.toEquals(drugstore.getBizModel()) ){
            organSign=drugstore.getHeadquartersOrganSign();
        }
        List<String> stringList= new ArrayList<>();
        QueryDictByOrgTypeDto queryDto = new QueryDictByOrgTypeDto();
        queryDto.setBusinessId(""+SystemConstans.BUSINESS_SCOPE_ID);
        queryDto.setYn((byte) 1);
        queryDto.setOrganSign(organSign);
        queryDto.setOrgType(drugstore.getBizModel());
        List<SystemDictDto> systemDicts = commonSystemDictService.queryDictByOrgType(queryDto);
        List<SaasBusinessScopePo> pos = new ArrayList<>();
        if( DrugstoreBizModelEnum.CHAIN_STORE.toEquals(drugstore.getBizModel()) ){
            stringList = commonSystemDictService.ignoreSystemDictIds();
        }

        //key:经营范围,value:资质
        Map<Integer, Integer> scopeAndType = getType();
        if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(drugstore.getBizModel()) && OrganSignTypeEnum.HEADQUARTERS.toEquals(drugstore.getOrganSignType())){
            List<SaaSDrugstoreDto> drugstoreList = drugstoreApi.getDrugstoreByHeadquartersOrganSign(organSign);
            for(SaaSDrugstoreDto saaSDrugstoreDto :drugstoreList) {
                SaasBusinessScopeDto saasBusinessScopeDto1 = new SaasBusinessScopeDto();
                saasBusinessScopeDto1.setOrganSign(saaSDrugstoreDto.getOrganSign());
                    for (SystemDictDto systemDict : systemDicts) {
                        if (stringList.contains(systemDict.getId().toString()) || null == scopeAndType.get(systemDict.getId()) && null == scopeAndType.get(systemDict.getParentId())) {
                            continue;
                        }
                        SaasBusinessScopePo po = new SaasBusinessScopePo();
                        BeanUtils.copyProperties(systemDict, po);
                        po.setOrganSign(saaSDrugstoreDto.getOrganSign());
                        po.setDictId(systemDict.getId());
                        //po.setEnableYn(systemDict.getStatus());
                        po.setEnableYn(SystemConstans.BUSINESS_SCOPE_YN_NO);
                        po.setCreateTime(new Date());
                        po.setStatus(systemDict.getStatus());
                        po.setBaseVersion(jedisUtil.incr(RedisConstants.SAAS_BUSINESS_SCOPE_BASE_VERSION + saaSDrugstoreDto.getOrganSign()) + "");
                        po.setCreateUser(saasBusinessScopeDto.getCreateUser() == null ? "" : saasBusinessScopeDto.getCreateUser());
                        po.setUpdateUser(saasBusinessScopeDto.getUpdateUser() == null ? "" : saasBusinessScopeDto.getUpdateUser());
                        po.setUpdateTime(new Date());
                        po.setCertificateType(null == scopeAndType.get(systemDict.getId())? scopeAndType.get(systemDict.getParentId()):scopeAndType.get(systemDict.getId()));
                        pos.add(po);
                    }
            }
        }
        SaasBusinessScopeDto saasBusinessScopeDto2 = new SaasBusinessScopeDto();
        saasBusinessScopeDto2.setOrganSign(drugstore.getOrganSign());
            for (SystemDictDto systemDict : systemDicts) {
                if (stringList.contains(systemDict.getId().toString())) {
                    continue;
                }
                //连锁的经营范围绑定资质,不在资质内的经营范围不需要
                if (BizModelEnum.CHAIN_STORE.toEquals(drugstore.getBizModel()) && null == scopeAndType.get(systemDict.getId()) && null == scopeAndType.get(systemDict.getParentId())){
                    continue;
                }
                SaasBusinessScopePo po = new SaasBusinessScopePo();
                BeanUtils.copyProperties(systemDict, po);
                po.setOrganSign(drugstore.getOrganSign());
                po.setDictId(systemDict.getId());
                po.setEnableYn(systemDict.getStatus());
                po.setCreateTime(new Date());
                //po.setEnableYn(systemDict.getStatus());
                po.setEnableYn(SystemConstans.BUSINESS_SCOPE_YN_NO);
                po.setBaseVersion(jedisUtil.incr(RedisConstants.SAAS_BUSINESS_SCOPE_BASE_VERSION + drugstore.getOrganSign()) + "");
                po.setCreateUser(saasBusinessScopeDto.getCreateUser() == null ? "" : saasBusinessScopeDto.getCreateUser());
                po.setUpdateUser(saasBusinessScopeDto.getUpdateUser() == null ? "" : saasBusinessScopeDto.getUpdateUser());
                po.setUpdateTime(new Date());
                po.setCertificateType(null == scopeAndType.get(systemDict.getId())? scopeAndType.get(systemDict.getParentId()):scopeAndType.get(systemDict.getId()));
                pos.add(po);
            }
        if(pos.size()>0) {
            saasBusinessScopePoMapper.batchInsert(pos);
        }
    }

    @Override
    public int refreshParent(List<String> organSigns) {
        return saasBusinessScopePoMapper.refreshParent(organSigns);
    }

    @Override
    public int updateVersion(String organSign){
        List<SaasBusinessScopePo> scopes = saasBusinessScopePoMapper.getAllIdScopeByOrgan(organSign);
        scopes.forEach(item->{
            item.setBaseVersion(jedisUtil.incr(RedisConstants.SAAS_BUSINESS_SCOPE_BASE_VERSION + organSign) + "");
        });
        return saasBusinessScopePoMapper.updateVersionById(scopes);
    }
}
