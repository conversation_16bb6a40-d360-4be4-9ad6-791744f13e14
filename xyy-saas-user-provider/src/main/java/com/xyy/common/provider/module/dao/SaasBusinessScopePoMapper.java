package com.xyy.common.provider.module.dao;

import com.dianping.zebra.dao.datasource.ZebraRouting;
import com.xyy.common.module.dto.SaasBusinessScopeDto;
import com.xyy.common.provider.module.entity.SaasBusinessScopePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
@ZebraRouting("common")
public interface SaasBusinessScopePoMapper {

    List<SaasBusinessScopePo> selectSaasBusinessScopeList(SaasBusinessScopeDto po);

    List<Integer> selectSaasBusinessScopeIdList(SaasBusinessScopeDto po);

    SaasBusinessScopePo selectSaasBusinessScope(SaasBusinessScopeDto po);

    void batchInsert(@Param("list") List<SaasBusinessScopePo> pos);

    void insertSelective(SaasBusinessScopePo po);

    int updateByPrimaryKeySelective(SaasBusinessScopePo po);

    int updateEnableYnById(@Param("list") List<SaasBusinessScopeDto> saasBusinessScopeDto);

    int updateEnableYnByDict(@Param("list") List<SaasBusinessScopeDto> saasBusinessScopeDto,@Param("organSign")String organSign);

    SaasBusinessScopePo selectMaxBaseVersion(SaasBusinessScopePo po);

    List<SaasBusinessScopePo> syncData(SaasBusinessScopeDto dto);

    int deleteByParam(SaasBusinessScopePo po);

    int deleteByOrgansAndDicts(@Param("organSigns")List<String> organSign,@Param("dicts")List<Integer> dicts);

    List<SaasBusinessScopePo> getNoRepeateId(@Param("organSign")String organSign);

    int deleteNotMax(@Param("organSign")String organSign,@Param("list")List<Long> list);

    int deleteById(@Param("organSign")String organSign,@Param("list")List<Long> list);

    int deleteByDictId(@Param("organSign")String organSign,@Param("list")List<Long> list);

    int refreshCustomBS(@Param("organSign")String organSign,@Param("ids")List<Long> ids);

    int refreshParent(@Param("organSigns")List<String> organSigns);

    List<SaasBusinessScopePo> getAllIdScopeByOrgan(@Param("organSign")String organSign);

    int updateVersionById(@Param("list") List<SaasBusinessScopePo> SaasBusinessScopePo);
}