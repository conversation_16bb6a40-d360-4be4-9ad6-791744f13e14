package com.xyy.common.provider.module.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Joiner;
import com.google.gson.Gson;
import com.xyy.common.module.dto.MessageNoticeDto;
import com.xyy.common.provider.module.dao.MessageNoticeEmployeeMapper;
import com.xyy.common.provider.module.dao.MessageNoticeMapper;
import com.xyy.common.provider.module.dao.SaasMessageNoticeDrugeMapper;
import com.xyy.common.provider.module.entity.MessageLatestPo;
import com.xyy.common.provider.module.entity.MessageNoticeEmployeePo;
import com.xyy.common.provider.module.entity.MessageNoticePo;
import com.xyy.common.provider.module.entity.SaasMessageNoticeDrugePo;
import com.xyy.common.provider.module.service.MessageDrugstoreSelectorService;
import com.xyy.common.provider.module.service.MessageNoticeService;
import com.xyy.common.provider.module.utils.ThreadPoolUtil;
import com.xyy.saas.common.api.FileUploadApi;
import com.xyy.saas.common.api.SaasAreaApi;
import com.xyy.saas.common.dto.SaasRegionBusinessDto;
import com.xyy.saas.common.dto.XyySaasRegionParamsDto;
import com.xyy.saas.common.util.CommonHttpClientUtil;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.dto.EmployeeListRequestModel;
import com.xyy.user.module.dto.SaasDrugstoreDto;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import com.xyy.user.module.dto.restructure.SaaSEmployeeDto;
import com.xyy.user.module.dto.result.EmployeeDto;
import com.xyy.user.module.dto.result.QueryDrugstoreDto;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Lookup;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class MessageNoticeServiceImpl implements MessageNoticeService {

    private static final Logger logger = LogManager.getLogger(MessageNoticeServiceImpl.class);

    @Autowired
    private MessageNoticeEmployeeMapper messageNoticeEmployeeMapper;

    @Autowired
    private MessageNoticeMapper messageNoticeMapper;

    @Autowired
    private EmployeeApi employeeApi;

    @Autowired
    private DrugstoreApi drugstoreApi;

    @Value("${saas.cloud.url}")
    private final String CLOUD_URL = null;

    @Reference(version = "0.0.1")
    private FileUploadApi fileUploadApi;

    @Reference(version = "0.0.1")
    private SaasAreaApi saasAreaApi;

    @Autowired
    private SaasMessageNoticeDrugeMapper saasMessageNoticeDrugeMapper;

    @Lookup
    protected MessageDrugstoreSelectorService getMessageDrugstoreSelectorService(){
        return null;
    }

    @Override
//    @Transactional(value = "commonTransactionManager", rollbackFor = RuntimeException.class)
    public Boolean insert(MessageNoticePo messageNoticePo) {
        // 统一落库删除防盗链token
        messageNoticePo.setContent(fileUploadApi.filterDeleteTokenInRichtext(messageNoticePo.getContent()));
        logger.info("insert|messageNoticePo:{}", JSON.toJSON(messageNoticePo));
        Boolean flag = false;
        try {
            //1.保存 消息通知
            Integer insert1 = messageNoticeMapper.insert(messageNoticePo);
            logger.info("新增/编辑消息通知管理PO为:"+messageNoticePo);
            if (insert1 != 1) {
                return flag;
            }

            Map<String, List<String>> groupArea = groupArea(messageNoticePo.getAreaCode());
            ExecutorService defaultExecutorService = ThreadPoolUtil.getDefaultExecutorService();
            defaultExecutorService.execute(new Runnable() {
                @Override
                public void run() {
                    List<MessageNoticeEmployeePo> employeePos = new ArrayList<>();
                    try {
                        MessageDrugstoreSelectorService messageDrugstoreSelectorService = getMessageDrugstoreSelectorService();
                        messageDrugstoreSelectorService.setAreaCode(groupArea);
                        messageDrugstoreSelectorService.init(null, messageNoticePo.getAllDrugstore(), messageNoticePo.getAddOrganSign());
                        List<String> selectOrganSignList = messageDrugstoreSelectorService.listSelectOrganSign();
                        //区域投放,存储给员工发送的消息信息
                        employeePos = selectDrugstoreEmployee(selectOrganSignList, messageNoticePo.getId());

                        //精准投放
                        List<String> customOrganSignList = messageDrugstoreSelectorService.listCustomOrganSign();
                        List<MessageNoticeEmployeePo> customEmployeePos = selectDrugstoreEmployee(customOrganSignList, messageNoticePo.getId());
                        customEmployeePos.forEach(customEmployeePo -> {
                            customEmployeePo.setCustomFlag((byte) 1);
                        });
                        employeePos.addAll(customEmployeePos);
                        if (employeePos != null && employeePos.size() > 0) {
                            addBatchMessageNoticeEmployeeList(employeePos);
                        }
                        selectOrganSignList.addAll(customOrganSignList);
                        //存储机构信息供界面查询
                        Set<String> provinceStr = saveDrugInfo(selectOrganSignList, messageNoticePo.getId());
                        MessageNoticePo extendInfo = new MessageNoticePo();
                        extendInfo.setId(messageNoticePo.getId());
                        extendInfo.setProvince(Joiner.on(",").join(provinceStr));
                        extendInfo.setStoreNum(selectOrganSignList.size());
                        extendInfo.setAreaCode(messageNoticePo.getAreaCode());
                        extendInfo.setLabel(messageNoticePo.getLabel());
                        messageNoticeMapper.update(extendInfo);
                    } catch (Exception e) {
                        logger.error("批量插入消息通知药店员工异常:", e);
                    }
                }
            });
        } catch (Exception e) {
            logger.error("保存消息通知异常: " + messageNoticePo, e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return flag;
        }
        return true;
    }

    //将区域分类,得到区域名称用于机构信息的查询
    private Map<String,List<String>> groupArea(String areaCodeStr){
        Map<String,List<String>> areaMap = new HashMap<>();

        if (!StringUtils.isEmpty(areaCodeStr)) {
            List<String> codeStrList = Arrays.asList(areaCodeStr.split(","));
            List<Integer> areaCodes = new ArrayList<>();
            for (String codeStr : codeStrList) {
                areaCodes.add(Integer.valueOf(codeStr));
            }
            XyySaasRegionParamsDto xyySaasRegionParamsDto = new XyySaasRegionParamsDto();
            xyySaasRegionParamsDto.setAreaCodes(areaCodes);
            List<SaasRegionBusinessDto> saasRegionBusinessDtos = saasAreaApi.queryRegionByAreaCodeList(xyySaasRegionParamsDto);

            List<String> province = new ArrayList<>();
            List<String> city = new ArrayList<>();
            List<String> area = new ArrayList<>();
            saasRegionBusinessDtos.forEach(item->{
                if (item.getLevel() == 1){
                    province.add(item.getAreaName());
                }

                if (item.getLevel() == 2){
                    city.add(item.getAreaName());
                }

                if (item.getLevel() == 3){
                    area.add(item.getAreaName());
                }
            });
            if (CollectionUtils.isNotEmpty(province)){
                areaMap.put("province",province);
            }

            if (CollectionUtils.isNotEmpty(city)){
                areaMap.put("city",city);
            }

            if (CollectionUtils.isNotEmpty(area)){
                areaMap.put("area",area);
            }
        }
        return areaMap;
    }

    @Override
//    @Transactional(value = "commonTransactionManager", rollbackFor = RuntimeException.class)
    public Boolean update(MessageNoticePo messageNoticePo) {
        logger.info("update|messageNoticePo:{}", JSON.toJSON(messageNoticePo));
        Boolean flag = false;
        try {
            //1.查询原选择药店的状态
            MessageNoticePo noticePo = messageNoticeMapper.selectById(messageNoticePo.getId());
            //2.更新消息通知
            Integer update1 = messageNoticeMapper.update(messageNoticePo);
            if (update1 != 1) {
                return flag;
            }

            Map<String, List<String>> groupArea = groupArea(messageNoticePo.getAreaCode());
            ExecutorService defaultExecutorService = ThreadPoolUtil.getDefaultExecutorService();
            defaultExecutorService.execute(new Runnable() {
                @Override
                public void run() {
                    List<MessageNoticeEmployeePo> employeePos = new ArrayList<>();
                    try {
                        MessageDrugstoreSelectorService messageDrugstoreSelectorService = getMessageDrugstoreSelectorService();
                        messageDrugstoreSelectorService.setAreaCode(groupArea);
                        messageDrugstoreSelectorService.init(messageNoticePo.getId(), messageNoticePo.getAllDrugstore(), messageNoticePo.getAddOrganSign());
                        messageNoticeEmployeeMapper.realDelByMessageNoticeId(messageNoticePo.getId());
                        saasMessageNoticeDrugeMapper.realDelByMessageNoticeId(messageNoticePo.getId());
                        //区域投放
                        List<String> selectOrganSignList = messageDrugstoreSelectorService.listSelectOrganSign();
                        employeePos = selectDrugstoreEmployee(selectOrganSignList, messageNoticePo.getId());
                        //精准投放
                        List<String> customOrganSignList = messageDrugstoreSelectorService.listCustomOrganSign();
                        //存储精确投放的员工消息
                        List<MessageNoticeEmployeePo> customEmployeePos = selectDrugstoreEmployee(customOrganSignList, messageNoticePo.getId());
                        customEmployeePos.forEach(customEmployeePo -> {
                            customEmployeePo.setCustomFlag((byte) 1);
                        });
                        employeePos.addAll(customEmployeePos);
                        if (employeePos != null && employeePos.size() > 0) {
                            addBatchMessageNoticeEmployeeList(employeePos);
                        }
                        //存储所有的机构号信息
                        selectOrganSignList.addAll(customOrganSignList);
                        Set<String> provinceStr = saveDrugInfo(selectOrganSignList, messageNoticePo.getId());
                        MessageNoticePo extendInfo = new MessageNoticePo();
                        extendInfo.setId(messageNoticePo.getId());
                        extendInfo.setProvince(Joiner.on(",").join(provinceStr));
                        extendInfo.setStoreNum(selectOrganSignList.size());
                        extendInfo.setAreaCode(messageNoticePo.getAreaCode());
                        extendInfo.setLabel(messageNoticePo.getLabel());
                        messageNoticeMapper.update(extendInfo);
                    } catch (Exception e) {
                        logger.error("批量修改消息通知药店员工异常:", e);
                    }
                }
            });
        } catch (Exception e) {
            logger.error("批量修改消息通知异常: " + messageNoticePo, e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return flag;
        }
        return true;
    }

    @Override
    public MessageNoticeDto selectById(Long id) {
        MessageNoticeDto dto = new MessageNoticeDto();
        MessageNoticePo model = messageNoticeMapper.selectById(id);
        if (model != null) {
            String releasePeopleId = model.getReleasePeopleId();
            if (releasePeopleId != null && !StringUtils.isEmpty(releasePeopleId)) {
                Map<String, Object> map = selectUserName(Integer.valueOf(releasePeopleId));
                if(map==null){
                    dto.setReleasePeopleName("未知");
                }else{
                    dto.setReleasePeopleName((String) map.get("realName") == null ? "未知":(String) map.get("realName"));
                }
            }
            //增加传入页面时地址先删+token
            if(model!=null&&!StringUtils.isEmpty(model.getContent())){
                String richtext = fileUploadApi.filterDeleteTokenInRichtext(model.getContent());
                model.setContent(fileUploadApi.filterAddTokenInRichtext(richtext));
            }
            BeanUtils.copyProperties(model, dto);
            dto.setAllDrugstore(String.format("%06d", model.getAllDrugstore()));
        }
        return dto;
    }

    @Override
    public Boolean updateState(MessageNoticePo messageNoticePo) {
        Integer updateState = messageNoticeMapper.updateState(messageNoticePo);
        if (updateState != 1) {
            return false;
        }
        return true;
    }

    /**
     * 分页查询消息通知
     *
     * @param pageInfo
     * @param bean
     * @return
     */
    @Override
    public PageInfo getMessageNoticeListPager(PageInfo pageInfo, MessageNoticePo bean) {
        List<MessageNoticePo> list = new ArrayList<>();
        PageInfo pageInfo1;
        try {
            PageHelper.startPage(pageInfo.getPageNum(), pageInfo.getPageSize());
            list = messageNoticeMapper.getMessageNoticeListPager(bean);
            pageInfo1 = new PageInfo(list);
            PageInfo mn = new PageInfo();
            BeanUtils.copyProperties(pageInfo1, mn);
            List<MessageNoticeDto> arrayList = new ArrayList<>();
            List<MessageNoticePo> list1 = pageInfo1.getList();
            if (list1 != null && list1.size() > 0) {
                for (MessageNoticePo model : list1) {
                    MessageNoticeDto dto = new MessageNoticeDto();
                    BeanUtils.copyProperties(model, dto);
                    arrayList.add(dto);
                }
            }
            mn.setList(arrayList);
            return mn;
        } catch (Exception e) {
            logger.error("分页查询消息通知异常", e);
        }
        return new PageInfo();
    }

    /**
     * 查询已选择药店分页
     */
    @Override
    public PageInfo getMessageNoticeDrugstoreListPager(PageInfo pageInfo, Long messageNoticeId, Integer customFlag) {
        List<String> organSigns = new ArrayList<>();
        PageInfo pageInfo1;
        try {
            PageHelper.startPage(pageInfo.getPageNum(), pageInfo.getPageSize());
            organSigns = messageNoticeEmployeeMapper.getMessageNoticeDrugstoreList(messageNoticeId, customFlag);
            pageInfo1 = new PageInfo(organSigns);
            PageInfo drugstore = new PageInfo();
            BeanUtils.copyProperties(pageInfo1, drugstore);
            List<SaasDrugstoreDto> arrayList = new ArrayList<>();
            List<String> list = pageInfo1.getList();
            if (list != null && list.size() > 0) {
                for (String organSign : list) {
                    SaasDrugstoreDto model = new SaasDrugstoreDto();
                    model.setOrganSign(organSign);
                    model.setPageSize(100000);
                    model.setPageNum(1);
                    ResultVO<PageInfo<SaasDrugstoreDto>> byCondition = drugstoreApi.getDrugstoreByCondition(model);
                    List<SaasDrugstoreDto> resultList = byCondition.getResult().getList();
                    if(resultList!=null && resultList.size()>0){
                        arrayList.add(resultList.get(0));
                    }else{
                        SaasDrugstoreDto saasDrugstoreDto = new SaasDrugstoreDto();
                        saasDrugstoreDto.setOrganSign(organSign);
                        saasDrugstoreDto.setDrugstoreName("该药店处于异常状态");
                        saasDrugstoreDto.setManagerName("未知");
                        saasDrugstoreDto.setProvince("未知");
                        saasDrugstoreDto.setCity(".");
                        saasDrugstoreDto.setArea(".");
                        saasDrugstoreDto.setAddress(".");
                        arrayList.add(saasDrugstoreDto);
                    }
                }
            }
            drugstore.setList(arrayList);
            return drugstore;
        } catch (Exception e) {
            logger.error("查询已选择药店分页异常", e);
        }
        return new PageInfo();
    }

    /**
     * 查询已选择药店数量
     */
    @Override
    public List<String> getMessageNoticeDrugstoreList(Long messageNoticeId) {
        List<String> organSigns = new ArrayList();
        try {
            organSigns = messageNoticeEmployeeMapper.getMessageNoticeDrugstoreList(messageNoticeId, null);
        } catch (Exception e) {
            logger.error("查询已选择药店数量异常", e);
        }
        return organSigns;
    }

    @Override
    public MessageNoticeDto getLatestMessge(MessageNoticeDto bean) {
        MessageNoticeDto dto = null;
        try {
//            MessageLatestPo noticePo = new MessageLatestPo();
//            noticePo.setClassification(bean.getClassification());
//            noticePo.setEmployeeId(bean.getEmployeeId());
//            noticePo.setOrganSign(bean.getDrugstoreOrganSign());
//            noticePo.setState(bean.getState());
//            noticePo.setExpireTime(new Date());
//            MessageNoticePo latest = messageNoticeMapper.getMessageNoticeIdLatest(noticePo);

            // 先查noticIds
            MessageLatestPo noticePo = new MessageLatestPo();
            noticePo.setClassification(bean.getClassification());
            noticePo.setState((byte)1);
            noticePo.setExpireTime(new Date());
            List<MessageNoticePo> messageNoticeIds = messageNoticeMapper.getMessageNoticeIds(noticePo);
            if(CollectionUtils.isEmpty(messageNoticeIds)){
                return dto;
            }
            noticePo.setMessageNoticeIds(messageNoticeIds.stream().map(MessageNoticePo::getId).collect(Collectors.toList()));
            noticePo.setEmployeeId(bean.getEmployeeId());
            noticePo.setOrganSign(bean.getDrugstoreOrganSign());
            // 遗留bug 前端传的是 readState ，但是后端一直用的是state = null ,所以只能取最后一条未读的消息，历史未读的还是未读
            // 不过刚好兼容了 发布两条，只取最后一条给弹框，历史的不给弹框的情况
            noticePo.setState(bean.getState());
            // 查询最后一条消息
            MessageLatestPo latestPo = messageNoticeMapper.getMessageNoticeIdLatest(noticePo);
            logger.info("getLatestMessge,入参:{},出参:{}",JSON.toJSONString(noticePo),JSON.toJSONString(latestPo));
            // 找到符合的消息
            MessageNoticePo latest = latestPo == null ? null : messageNoticeIds.stream().filter(m -> Objects.equals(m.getId(), latestPo.getMessageNoticeId())).findAny().orElse(null);

            //增加传入页面时地址+token  先删除token再加
            if(latest!=null && org.apache.commons.lang3.StringUtils.isNotBlank(latest.getContent())){
                String richtext = fileUploadApi.filterDeleteTokenInRichtext(latest.getContent());
                latest.setContent(fileUploadApi.filterAddTokenInRichtext(richtext));
            }
            if (null != latest && latestPo.getState() == 1){
                dto = new MessageNoticeDto();
                BeanUtils.copyProperties(latest, dto);
                dto.setReadState(String.valueOf(latestPo.getState()));
            }
        }catch (Exception e){
            logger.error("MessageNoticeServiceImpl#getLatestMessge error:{}",e);
        }
        return dto;
    }

    /**
     * 根据已读未读状态分页查询消息通知接口
     */
    @Override
    public PageInfo getMessageNoticeByEmployeeIdPager(PageInfo pageInfo, MessageNoticeDto bean) {
        List<MessageNoticePo> list = new ArrayList<>();
        PageInfo pageInfo1;
        try {
            //先查询员工
            MessageNoticePo messageNoticePo = new MessageNoticePo();
            BeanUtils.copyProperties(bean, messageNoticePo);
            List<MessageNoticeEmployeePo> messageNoticeIdList = messageNoticeEmployeeMapper.getMessageNoticeIdList(messageNoticePo);
            if(messageNoticeIdList!=null && messageNoticeIdList.size()>0){
                // 不指定分类，默认查1、2、3，兼容老逻辑
                if (bean.getClassification() == null) {
                    bean.setClassificationList(Arrays.asList((byte) 1, (byte) 2, (byte) 3, (byte) 4, (byte) 5));
                }
                if (null == bean.getClassification() || (4 != bean.getClassification() && 5 != bean.getClassification())) {
                    bean.setExpireTime(new Date());
                }
                PageHelper.startPage(pageInfo.getPageNum(), pageInfo.getPageSize());
                list = messageNoticeMapper.getMessageNoticeByEmployeeIdPager(messageNoticeIdList,bean);
                pageInfo1 = new PageInfo(list);
                PageInfo mn = new PageInfo();
                BeanUtils.copyProperties(pageInfo1, mn);
                List<MessageNoticeDto> arrayList = new ArrayList<>();
                List<MessageNoticePo> listPo = pageInfo1.getList();
                if (listPo != null && listPo.size() > 0) {
                    for (MessageNoticePo model : listPo) {
                        MessageNoticeDto dto = new MessageNoticeDto();
                        BeanUtils.copyProperties(model, dto);
                        for (MessageNoticeEmployeePo po : messageNoticeIdList) {
                            if (model.getId().equals(po.getMessageNoticeId())) {
                                dto.setReadState(po.getState()+"");
                            }
                        }
                        arrayList.add(dto);
                    }
                }
                mn.setList(arrayList);
                return mn;
            }
        } catch (Exception e) {
            logger.error("根据已读未读状态分页查询消息通知异常", e);
        }
        return new PageInfo();
    }

    /**
     * 将消息通知更新为已读
     */
    @Override
    public Boolean updateMessageNoticeByEmployeeId(String organSign, String employeeId, List<Long> messageNoticeIds) {
        messageNoticeEmployeeMapper.updateMessageNoticeByEmployeeId(organSign, employeeId, messageNoticeIds);
        return true;
    }


    /**
     * 发布人接口
     *
     * @return
     */
    @Override
    public List<String> selectReleasePeopleNameList() {
        //查询所有已发布人的id
        return messageNoticeMapper.selectReleasePeopleId();
    }


    /**
     * 根据运营后台员工id返回员工名称
     *
     * @param userId
     * @return
     */
    private Map<String, Object> selectUserName(Integer userId) {
        try {
            Map<String, Object> result = new HashMap<>();
            String resInfo = CommonHttpClientUtil.httpPost(CLOUD_URL + "/saas/findUserById?userId="
                    + userId,null);
            logger.info("调用运营后台查询员工信息完毕，返回结果" + resInfo);
            if (!StringUtils.isEmpty(resInfo)) {
                Gson gson = new Gson();
                Map<String, Object> map = new HashMap<>();
                map = gson.fromJson(resInfo, map.getClass());
                result = (Map<String, Object>) map.get("result");
                return result;
            } else {
                return result;
            }
        } catch (Exception e) {
            logger.info("远程调用接口失败或结果解析失败,异常信息:", e);
            return new HashMap<>();
        }
    }

    /**
     * 不选择全部药店时，根据organSign查询药店员工
     *
     * @param organSignList
     * @param messageNoticeId
     * @return
     */
    private List<MessageNoticeEmployeePo> selectDrugstoreEmployee(List<String> organSignList, Long messageNoticeId) {
        List<MessageNoticeEmployeePo> employeeList = new ArrayList<>();
        if (organSignList != null && organSignList.size() > 0) {
            EmployeeListRequestModel model = new EmployeeListRequestModel();
            model.setWorkingState((byte) 1);
            for (String organSign : organSignList) {
                model.setOrganSign(organSign);
                model.setRealOrganSign(organSign);
                List<SaaSEmployeeDto> employees = employeeApi.listDepartmentAndBranchStoreEmployeeByOrganSign(model);
                if (employees != null && employees.size() > 0) {
                    for (SaaSEmployeeDto employeeDto : employees) {
                        MessageNoticeEmployeePo emp = new MessageNoticeEmployeePo();
                        emp.setEmployeeId(employeeDto.getId() + "");
                        emp.setMessageNoticeId(messageNoticeId);
                        emp.setOrganSign(organSign);
                        emp.setYn((byte) 1);
                        emp.setState((byte) 1);
                        emp.setCustomFlag((byte) 0);
                        employeeList.add(emp);
                    }
                }else{
                    logger.info("机构号为:"+organSign+"的药店，查询到相关药店的员工数量为0。");
                }
            }
        }
        return employeeList;
    }


    /**
     * 选择全部药店时，根据药店organSign查询药店员工
     *
     * @param drugstoreDtoList
     * @param messageNoticeId
     * @return
     */
    private List<MessageNoticeEmployeePo> selectEmployeeByDrugstoreDtoList(List<QueryDrugstoreDto> drugstoreDtoList, Long messageNoticeId) {
        List<MessageNoticeEmployeePo> employeeList = new ArrayList<>();
        if (drugstoreDtoList != null && drugstoreDtoList.size() > 0) {
            for (QueryDrugstoreDto drugstoreDto : drugstoreDtoList) {
                //添加药店状态为未冻结的员工
                if (1 == drugstoreDto.getStatus()) {
                    List<EmployeeDto> employees = employeeApi.queryAllEmployeeByOrganSign(drugstoreDto.getOrganSign());
                    if (employees != null && employees.size() > 0) {
                        for (EmployeeDto employeeDto : employees) {
                            MessageNoticeEmployeePo emp = new MessageNoticeEmployeePo();
                            emp.setEmployeeId(employeeDto.getId() + "");
                            emp.setMessageNoticeId(messageNoticeId);
                            emp.setOrganSign(drugstoreDto.getOrganSign());
                            emp.setYn((byte) 1);
                            emp.setState((byte) 1);
                            employeeList.add(emp);
                        }
                    }else{
                        logger.info("机构号为:"+drugstoreDto.getOrganSign()+"的药店，其药店状态为:"+drugstoreDto.getStatus()+",查询到相关药店的员工数量为0。");
                    }
                }

            }
        }
        return employeeList;
    }

    /**
     * 批量添加药店员工数据
     * @param list
     * @return
     */
    private int addBatchMessageNoticeEmployeeList(List<MessageNoticeEmployeePo> list ){
        int batchCount = 200;
        int batchLastIndex = batchCount;
        for (int index = 0; index < list.size();) {
            if(batchLastIndex >= list.size()){
                batchLastIndex = list.size();
                messageNoticeEmployeeMapper.batchInsert(list.subList(index, batchLastIndex));
                logger.info("addBatchMessageNoticeEmployeeList:list:index>>>>>"+index+"batchLastIndex>>>>>"+batchLastIndex);
                break ;	//数据插入完毕，退出循环
            }else{
                messageNoticeEmployeeMapper.batchInsert(list.subList(index, batchLastIndex));
                logger.info("addBatchMessageNoticeEmployeeList:list:index>>>>>"+index+"batchLastIndex>>>>>"+batchLastIndex);
                index = batchLastIndex;	//设置下一批下标
                batchLastIndex = index + (batchCount - 1);
            }
        }
        return list.size();
    }

    /**
     * 批量存储机构信息
     * @param organSigns
     */
    private Set<String> saveDrugInfo(List<String> organSigns,Long messageId){
        int batchCount = 200;
        int batchLastIndex = batchCount;
        //统计省份
        Set<String> province = new HashSet<>();
        for (int index = 0; index < organSigns.size();) {
            if(batchLastIndex >= organSigns.size()){
                batchLastIndex = organSigns.size();
                List<SaaSDrugstoreDto> drugstore = drugstoreApi.getDrugstoreByMultipleOrganSign(organSigns.subList(index, batchLastIndex));
                Set<String> provinceSub = insertDrugInfo(drugstore, messageId);
                if (CollectionUtils.isNotEmpty(provinceSub)){
                    province.addAll(provinceSub);
                }
                logger.info("saveDrugInfo:organSigns:index>>>>>"+index+"batchLastIndex>>>>>"+batchLastIndex);
                break ;	//数据插入完毕，退出循环
            }else {
                List<SaaSDrugstoreDto> drugstore = drugstoreApi.getDrugstoreByMultipleOrganSign(organSigns.subList(index, batchLastIndex));
                Set<String> provinceSub = insertDrugInfo(drugstore,messageId);
                if (CollectionUtils.isNotEmpty(provinceSub)){
                    province.addAll(provinceSub);
                }
                logger.info("saveDrugInfo:organSigns:index>>>>>"+index+"batchLastIndex>>>>>"+batchLastIndex);
                index = batchLastIndex;	//设置下一批下标
                batchLastIndex = index + (batchCount - 1);
            }
        }
        return province;
    }

    private Set<String> insertDrugInfo(List<SaaSDrugstoreDto> drugstore,Long messageId){
        Set<String> province = new HashSet<>();
        if (CollectionUtils.isNotEmpty(drugstore)){
            List<SaasMessageNoticeDrugePo> pos = new ArrayList<>();
            for (SaaSDrugstoreDto item:drugstore) {
                SaasMessageNoticeDrugePo po = new SaasMessageNoticeDrugePo();
                po.setMessageNoticeId(messageId);
                po.setDrugstoreName(item.getDrugstoreName());
                po.setArea(item.getArea());
                po.setCity(item.getCity());
                po.setProvince(item.getProvince());
                po.setOrganSign(item.getOrganSign());
                po.setManagerName(item.getManagerName());
                po.setCreateTime(new Date());
                po.setUpdateTime(new Date());
                pos.add(po);
                province.add(item.getProvince());
            }
            if (CollectionUtils.isNotEmpty(pos)) {
                saasMessageNoticeDrugeMapper.insertBatch(pos);
            }
        }
        return province;
    }

}