package com.xyy.common.provider.module.api.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.xyy.common.module.api.SysConfigApi;
import com.xyy.common.module.dto.SystemConfigDto;
import com.xyy.common.module.dto.SystemConfigPOSDto;
import com.xyy.common.module.dto.SystemConfigWEBDto;
import com.xyy.common.provider.common.constant.SystemConstans;
import com.xyy.common.provider.module.entity.SystemConfigPo;
import com.xyy.common.provider.module.service.SysConfigService;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.rabbitmq.core.api.MessagePushApi;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import com.xyy.user.module.dto.result.EmployeeDto;
import com.xyy.user.module.enums.DrugstoreBizModelEnum;
import com.xyy.user.module.enums.DrugstoreTypeEnum;
import com.xyy.user.module.enums.RoleEnum;
import com.xyy.user.module.enums.SystemRoleCodeEnum;
import com.xyy.user.provider.module.utils.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 控制面板
 * <AUTHOR>
 */
@Service(version = "0.0.1")
public class SysConfigApiImpl implements SysConfigApi {


	private static final Logger logger = LoggerFactory.getLogger(SysConfigApiImpl.class);

	@Autowired
	private SysConfigService sysConfigService;

	@Reference(version = "0.0.1")
	private MessagePushApi messagePushApi;

	@Autowired
	private DrugstoreApi drugstoreApi;

	@Autowired
	private EmployeeApi employeeApi;

	// 显示医保进销存提醒地区控制 - 湖南
	@Value("${user.show.sale.storage.config:}")
	private String showSaleStorageConfig;

	// 显示辽宁丹东入网信息
	@Value("${user.show.area.drug.supervision.config:}")
	private String showAreaDrugSupervisionConfig;


	@Override
	public boolean updateSystemConfig(SystemConfigDto config) {
		SystemConfigPo systemConfigPo = new SystemConfigPo();
		BeanUtils.copyProperties(config, systemConfigPo);
		SystemConfigDto c = this.querySystemConfig(config.getOrganSign());
		if (c == null) {
			processDefaultSystemConfig(systemConfigPo);
			return sysConfigService.initSystemConfig(systemConfigPo);
		}
		return sysConfigService.updateSystemConfig(systemConfigPo);
	}

	@Override
	public ResultVO<Boolean> updateSystemConfigNew(SystemConfigDto config) {
		SystemConfigPo systemConfigPo = new SystemConfigPo();
		BeanUtils.copyProperties(config, systemConfigPo);
		SystemConfigDto c = this.querySystemConfig(config.getOrganSign());
		if (c == null) {
			processDefaultSystemConfig(systemConfigPo);
			return new ResultVO<>(sysConfigService.initSystemConfig(systemConfigPo));
		}
		return sysConfigService.updateSystemConfigNew(systemConfigPo);
	}

	private void processDefaultSystemConfig(SystemConfigPo systemConfigPo){
		if(systemConfigPo.getPrescriptionYn()==null){
			systemConfigPo.setPrescriptionYn(new Byte("1"));
		}if(systemConfigPo.getUpdateOrderDiscountYn()==null){
			systemConfigPo.setUpdateOrderDiscountYn(new Byte("1"));
		}if(systemConfigPo.getUpdateProductDiscountYn()==null){
			systemConfigPo.setUpdateProductDiscountYn(new Byte("1"));
		}if(systemConfigPo.getUpdateProductPrice()==null){
			systemConfigPo.setUpdateProductPrice(new Byte("1"));
		}if(systemConfigPo.getSalesBelowCost()==null){
			systemConfigPo.setSalesBelowCost(new Byte("1"));
		}if(systemConfigPo.getIdCardYn()==null){
			systemConfigPo.setIdCardYn(new Byte("1"));
		}if(systemConfigPo.getSavePreSaleYn()==null){
			systemConfigPo.setSavePreSaleYn(new Byte("1"));
		}if(systemConfigPo.getOneSetpYn()==null){
			systemConfigPo.setOneSetpYn(new Byte("0"));
		}if(systemConfigPo.getBatchNoQueueYn()==null){
			systemConfigPo.setBatchNoQueueYn(new Byte("0"));
		}if(systemConfigPo.getFlaxNum()==null){
			systemConfigPo.setFlaxNum(2);
		}if(systemConfigPo.getPurchaseName()==null){
			systemConfigPo.setPurchaseName("");
		}if(systemConfigPo.getExaminerName()==null){
			systemConfigPo.setExaminerName("");
		}if(systemConfigPo.getPosShowPurpriceYn()==null){
			systemConfigPo.setPosShowPurpriceYn(new Byte("1"));
		}if(systemConfigPo.getApproveProductYn()==null){
			systemConfigPo.setApproveProductYn(new Byte("0"));
		}if(systemConfigPo.getPrescriptionYn()==null){
			systemConfigPo.setPrescriptionYn(new Byte("0"));
		}if(systemConfigPo.getApproveProductQuickYn()==null){
			systemConfigPo.setApproveProductQuickYn(new Byte("0"));
		}if(systemConfigPo.getApproveProviderQuickYn()==null){
			systemConfigPo.setApproveProviderQuickYn(new Byte("0"));
		}if(systemConfigPo.getApprovePrescriptionYn()==null){
			systemConfigPo.setApprovePrescriptionYn(new Byte("0"));
		}if(systemConfigPo.getEnterpriseOwners()==null){
			systemConfigPo.setEnterpriseOwners("");
		}if(systemConfigPo.getEnterpriseOwnersEmployeeId()==null){
			systemConfigPo.setEnterpriseOwnersEmployeeId(0);
		}if(systemConfigPo.getQualityOwners()==null){
			systemConfigPo.setQualityOwners("");
		}if(systemConfigPo.getQualityOwnersEmployeeId()==null){
			systemConfigPo.setQualityOwnersEmployeeId(0);
		}if(StringUtil.isEmpty(systemConfigPo.getLicensedPharmacist())){
			systemConfigPo.setLicensedPharmacist(initLicensedPharmacist(systemConfigPo.getOrganSign()));
		}if(systemConfigPo.getSaleAmountYn()==null){
			systemConfigPo.setSaleAmountYn(new Byte("0"));
		}if(systemConfigPo.getSmallChange()==null){
			systemConfigPo.setSmallChange(new Byte("0"));
		}if(systemConfigPo.getSmallChangeOpen()==null){
			systemConfigPo.setSmallChangeOpen("0");
		}if(systemConfigPo.getSmallChangeClose()==null){
			systemConfigPo.setSmallChangeClose("0");
		}if(systemConfigPo.getCashChange()==null){
			systemConfigPo.setCashChange(new Byte("0"));
		}if(systemConfigPo.getMemberReminder()==null){
			systemConfigPo.setMemberReminder(new Byte("0"));
		}if(systemConfigPo.getShortageRegistration()==null){
			systemConfigPo.setShortageRegistration(new Byte("1"));
		}if(systemConfigPo.getFullStaffDownload()==null){
			systemConfigPo.setFullStaffDownload(new Byte("0"));
		}if(systemConfigPo.getSettlementMethod()==null){
			systemConfigPo.setSettlementMethod("");
		}if(systemConfigPo.getMemberInputReminder()==null){
			systemConfigPo.setMemberInputReminder(new Byte("0"));
		}if(systemConfigPo.getBusinessStartTime()==null){
			systemConfigPo.setBusinessStartTime("-1");
		}if(systemConfigPo.getProductStartTime()==null){
			systemConfigPo.setProductStartTime("-1");
		}if(systemConfigPo.getConventionalCuringCycle()==null){
			systemConfigPo.setConventionalCuringCycle(30);
		}if(systemConfigPo.getCriticalCuringPeriod()==null){
			systemConfigPo.setCriticalCuringPeriod(15);
		}if(systemConfigPo.getAdvanceWarningCuring()==null){
			systemConfigPo.setAdvanceWarningCuring(3);
		}if(systemConfigPo.getAdvanceWarningInspection()==null){
			systemConfigPo.setAdvanceWarningInspection(3);
		}if(systemConfigPo.getRoutineInspectionCycle()==null){
			systemConfigPo.setRoutineInspectionCycle(30);
		}if(systemConfigPo.getCriticalInspectionCycle()==null){
			systemConfigPo.setCriticalInspectionCycle(15);
		}if(systemConfigPo.getEarlyWarningDays()==null){
			systemConfigPo.setEarlyWarningDays(getDefaultEarlyWarningDays(systemConfigPo.getOrganSign()));
		}if(systemConfigPo.getHealthNoteSwitch()==null){
			systemConfigPo.setHealthNoteSwitch(new Byte("0"));
		}if(systemConfigPo.getRemoteInquiry()==null){
			systemConfigPo.setRemoteInquiry(new Byte("1"));
		}if(systemConfigPo.getStoredValueAuditPOS()==null){
			systemConfigPo.setStoredValueAuditPOS(new Byte("1"));
		}if(systemConfigPo.getStoredValueAuditWEB()==null){
			systemConfigPo.setStoredValueAuditWEB(new Byte("1"));
		}if(systemConfigPo.getStoredValueSMSRemind()==null){
			systemConfigPo.setStoredValueSMSRemind(new Byte("0"));
		}if(systemConfigPo.getPromotionPriority()==null){
			systemConfigPo.setPromotionPriority(new Byte("1"));
		}if(systemConfigPo.getCouponNoteYn()==null){
			systemConfigPo.setCouponNoteYn(SystemConstans.COUPON_NOTE_YN_YES);
		}if(systemConfigPo.getBusinessScopeYn()==null){
			systemConfigPo.setBusinessScopeYn(new Byte(SystemConstans.BUSINESS_SCOPE_YN_YES));
		}if(systemConfigPo.getMaintenancePlanData()==null){
			systemConfigPo.setMaintenancePlanData(SystemConstans.MAINTENANCE_PLAN_DATA);
		}if(systemConfigPo.getMaintenanceDataUpdateYn()==null){
			systemConfigPo.setMaintenanceDataUpdateYn(SystemConstans.MAINTENANCE_DATA_UPDATE_YN_NO);
		}if(systemConfigPo.getReturnSalesYn()==null){
			systemConfigPo.setReturnSalesYn(SystemConstans.RETURN_SALES_YN_NO);
		}if(systemConfigPo.getPrescriptionRegisteredYn()==null){
			systemConfigPo.setPrescriptionRegisteredYn(SystemConstans.PRESCRIPTION_REGISTERED_YN);
		}if(systemConfigPo.getSaleStorageYn()==null){
			systemConfigPo.setSaleStorageYn(SystemConstans.SALE_STORAGE_NO);
		}if(systemConfigPo.getProviderQualificationExpiredRemindDays() == null){
			systemConfigPo.setProviderQualificationExpiredRemindDays(SystemConstans.PROVIDER_QUALIFICATION_EXPIRED_REMIND_DAYS);
		}if(systemConfigPo.getProductQualificationExpiredRemindDays() == null){
			systemConfigPo.setProductQualificationExpiredRemindDays(SystemConstans.PRODUCT_QUALIFICATION_EXPIRED_REMIND_DAYS);
		}if(systemConfigPo.getDeadStockWarningDays() == null){
			systemConfigPo.setDeadStockWarningDays(SystemConstans.DEAD_STOCK_WARNING_DAYS);
		}if(systemConfigPo.getDeadStockWarningCount() == null){
			systemConfigPo.setDeadStockWarningCount(SystemConstans.DEAD_STOCK_WARNING_COUNT);
		}if(systemConfigPo.getAccountSetYn() == null){
			systemConfigPo.setAccountSetYn(new Byte("0"));
		}if (systemConfigPo.getReceivableNumber() == null){
			systemConfigPo.setReceivableNumber(new Byte("1"));
		}if (systemConfigPo.getPhoneSignSwitch() == null){
			systemConfigPo.setPhoneSignSwitch(new Byte("0"));
		}if (systemConfigPo.getInventoryLotNumAdjustSwitch() == null){
			systemConfigPo.setInventoryLotNumAdjustSwitch(0);
		}if (systemConfigPo.getChineseMedicineNumber() == null){
			systemConfigPo.setChineseMedicineNumber(99);
		}if (systemConfigPo.getChineseMedicineUnit() == null){
			systemConfigPo.setChineseMedicineUnit(new Byte("1"));
		}if (systemConfigPo.getAuditingFirstRole() == null){
			systemConfigPo.setAuditingFirstRole(SystemRoleCodeEnum.LICENSED_PHARMACIST_ROLE.getCode());
		}if (systemConfigPo.getAuditingSecondRole() == null){
			systemConfigPo.setAuditingSecondRole(SystemRoleCodeEnum.CEHCKER_ROLE.getCode());
		}if (systemConfigPo.getAuditingThirdRole() == null){
			systemConfigPo.setAuditingThirdRole(SystemRoleCodeEnum.DRUG_GIVER_ROLE.getCode());
		}if (systemConfigPo.getHiddenTrackPrescriptionInfo() == null){
			systemConfigPo.setHiddenTrackPrescriptionInfo(new Byte("0"));
		}if (systemConfigPo.getDeviceUdiSupervisionYn() == null){
			systemConfigPo.setDeviceUdiSupervisionYn(new Byte("0"));
		}
		// 控制POS“医保匹配功能”开关, 默认展示
		if (systemConfigPo.getShowMedicareMatchBtnYn() ==null){
			systemConfigPo.setShowMedicareMatchBtnYn(new Byte("1"));
		}
		//追溯码销售录入
		systemConfigPo.setTraceCodeSaleEntryYn(Optional.ofNullable(systemConfigPo.getTraceCodeSaleEntryYn()).orElse(SystemConstans.TRACE_CODE_SALE_ENTRY_YN_NO));
		systemConfigPo.setDrugSupervisionYn(Optional.ofNullable(systemConfigPo.getDrugSupervisionYn()).orElse(SystemConstans.DRUG_SUPERVISION_NO));
		// 默认【恢复回收站商品时审核】为否
		systemConfigPo.setProductRecoverAuditingYn(Optional.ofNullable(systemConfigPo.getProductRecoverAuditingYn()).orElse(new Byte("0")));
		// 【恢复回收站商品时审核】为是时，【商品恢复审核人】必填，选择角色-药店负责人、企业负责人、质量负责人
		systemConfigPo.setProductRecoverAuditingRole(Optional.ofNullable(systemConfigPo.getProductRecoverAuditingRole()).orElse(""));

		systemConfigPo.setProductPriceTwoDecimalPlacesYn(Optional.ofNullable(systemConfigPo.getProductPriceTwoDecimalPlacesYn()).orElse(new Byte("0")));
		systemConfigPo.setProductMedicineSaleDecimalYn(Optional.ofNullable(systemConfigPo.getProductMedicineSaleDecimalYn()).orElse(new Byte("1")));
		systemConfigPo.setProductDrugSaleDecimalYn(Optional.ofNullable(systemConfigPo.getProductDrugSaleDecimalYn()).orElse(new Byte("1")));
	}

	/**
	 * 职业药师：如果是单体、连锁门店、联营门店 则初始化成药店负责人，否则设置为空
	 * @param organSign
	 * @return
	 */
	private String initLicensedPharmacist(String organSign) {
		logger.info("初始化执业药师");
		SaaSDrugstoreDto drugstore = drugstoreApi.getDrugstoreByOrganSign(organSign);
		if (drugstore!=null && DrugstoreTypeEnum.DRUGSTORE.getKey() == Integer.valueOf(drugstore.getOrganSignType())) {
			List<EmployeeDto> employeeDtoList = employeeApi.queryEmployeeByRoleIdAndOrganSign(RoleEnum.STORE_ADMIN_ROLE.getKey(), organSign);
			if (CollectionUtils.isNotEmpty(employeeDtoList)) {
				EmployeeDto employeeDto = employeeDtoList.get(0);
				return employeeDto==null ? "" : String.valueOf(employeeDto.getId());
			}
		}
		return "";
	}

	@Override
	public boolean initSystemConfig(String organSign) {
		logger.info("初始化系统设置，机构号:{}", organSign);
		return init(organSign, initLicensedPharmacist(organSign));
	}

	private boolean init(String organSign, String licensedPharmacist) {
		SystemConfigPo systemConfig = sysConfigService.queryInitSystemConfig(organSign);
		if(systemConfig != null){
			return true;
		}
		SystemConfigPo systemConfigPo = new SystemConfigPo();
		systemConfigPo.setLicensedPharmacist(licensedPharmacist);
		systemConfigPo.setPrescriptionYn(new Byte("1"));
		systemConfigPo.setUpdateOrderDiscountYn(new Byte("1"));
		systemConfigPo.setUpdateProductDiscountYn(new Byte("1"));
		systemConfigPo.setUpdateProductPrice(new Byte("1"));
		systemConfigPo.setSalesBelowCost(new Byte("1"));
		systemConfigPo.setIdCardYn(new Byte("1"));
		systemConfigPo.setSavePreSaleYn(new Byte("1"));
		systemConfigPo.setOneSetpYn(new Byte("0"));
		systemConfigPo.setBatchNoQueueYn(new Byte("0"));
		systemConfigPo.setFlaxNum(2);
		systemConfigPo.setPurchaseName("");
		systemConfigPo.setExaminerName("");
		systemConfigPo.setPosShowPurpriceYn(new Byte("1"));
		systemConfigPo.setApproveProductYn(new Byte("0"));
		systemConfigPo.setApproveProviderYn(new Byte("0"));
		systemConfigPo.setApproveProductQuickYn(new Byte("0"));
		systemConfigPo.setApproveProviderQuickYn(new Byte("0"));
		systemConfigPo.setApprovePrescriptionYn(new Byte("0"));
		systemConfigPo.setEnterpriseOwners("");
		systemConfigPo.setEnterpriseOwnersEmployeeId(0);
		systemConfigPo.setQualityOwners("");
		systemConfigPo.setQualityOwnersEmployeeId(0);
		systemConfigPo.setSaleAmountYn(new Byte("0"));
		systemConfigPo.setSmallChange(new Byte("0"));
		systemConfigPo.setSmallChangeOpen("0");
		systemConfigPo.setSmallChangeClose("0");
		systemConfigPo.setOrganSign(organSign);
		systemConfigPo.setCashChange(new Byte("0"));
		systemConfigPo.setMemberReminder(new Byte("0"));
		systemConfigPo.setShortageRegistration(new Byte("1"));
		systemConfigPo.setFullStaffDownload(new Byte("0"));
		systemConfigPo.setSettlementMethod("");
		systemConfigPo.setMemberInputReminder(new Byte("0"));
		systemConfigPo.setBusinessStartTime("-1");
		systemConfigPo.setProductStartTime("-1");
		systemConfigPo.setHealthNoteSwitch(new Byte("0"));
		systemConfigPo.setConventionalCuringCycle(30);
		systemConfigPo.setCriticalCuringPeriod(15);
		systemConfigPo.setAdvanceWarningCuring(3);
		systemConfigPo.setAdvanceWarningInspection(3);
		systemConfigPo.setRoutineInspectionCycle(30);
		systemConfigPo.setCriticalInspectionCycle(15);
		systemConfigPo.setEarlyWarningDays(getDefaultEarlyWarningDays(systemConfigPo.getOrganSign()));
		systemConfigPo.setHealthNoteSwitch(new Byte("0"));
		systemConfigPo.setRemoteInquiry(new Byte("1"));
		systemConfigPo.setStoredValueAuditPOS(new Byte("1"));
		systemConfigPo.setStoredValueAuditWEB(new Byte("1"));
		systemConfigPo.setInventoryAuditRoleId(0);
		systemConfigPo.setStoredValueSMSRemind(new Byte("0"));
		systemConfigPo.setPromotionPriority(new Byte("1"));
		systemConfigPo.setCouponNoteYn(SystemConstans.COUPON_NOTE_YN_YES);
		systemConfigPo.setBusinessScopeYn(new Byte(SystemConstans.BUSINESS_SCOPE_YN_YES));
		systemConfigPo.setMaintenancePlanData(SystemConstans.MAINTENANCE_PLAN_DATA);
		systemConfigPo.setMaintenanceDataUpdateYn(SystemConstans.MAINTENANCE_DATA_UPDATE_YN_NO);
		systemConfigPo.setReturnSalesYn(SystemConstans.RETURN_SALES_YN_NO);
		systemConfigPo.setPrescriptionRegisteredYn(SystemConstans.PRESCRIPTION_REGISTERED_YN);
		systemConfigPo.setSaleStorageYn(SystemConstans.SALE_STORAGE_NO);
		systemConfigPo.setDrugSupervisionYn(SystemConstans.DRUG_SUPERVISION_NO);
		systemConfigPo.setProviderQualificationExpiredRemindDays(SystemConstans.PROVIDER_QUALIFICATION_EXPIRED_REMIND_DAYS);
		systemConfigPo.setProductQualificationExpiredRemindDays(SystemConstans.PRODUCT_QUALIFICATION_EXPIRED_REMIND_DAYS);
		systemConfigPo.setDeadStockWarningDays(SystemConstans.DEAD_STOCK_WARNING_DAYS);
		systemConfigPo.setDeadStockWarningCount(SystemConstans.DEAD_STOCK_WARNING_COUNT);
		systemConfigPo.setBlindShiftHandover(SystemConstans.BLIND_SHIFT_HANDOVER);
		systemConfigPo.setRecordMobileForNarcoticYn(SystemConstans.RECORD_MOBILE_FOR_NARCOTIC_NO);
		systemConfigPo.setAccountSetYn(new Byte("0"));
		systemConfigPo.setReceivableNumber(new Byte("1"));
		systemConfigPo.setPhoneSignSwitch(new Byte("0"));
		systemConfigPo.setInventoryLotNumAdjustSwitch(0);
		systemConfigPo.setChineseMedicineNumber(99);
		systemConfigPo.setChineseMedicineUnit(new Byte("1"));
		//默认一级审核角色执业药师、二级审核角色核对人、三级审核角色发药人
		systemConfigPo.setAuditingFirstRole(SystemRoleCodeEnum.LICENSED_PHARMACIST_ROLE.getCode());
		systemConfigPo.setAuditingSecondRole(SystemRoleCodeEnum.CEHCKER_ROLE.getCode());
		systemConfigPo.setAuditingThirdRole(SystemRoleCodeEnum.DRUG_GIVER_ROLE.getCode());
		systemConfigPo.setHiddenTrackPrescriptionInfo(new Byte("0"));
		// 控制POS“医保匹配功能”开关, 默认展示
		systemConfigPo.setShowMedicareMatchBtnYn(new Byte("1"));
		// 默认【恢复回收站商品时审核】为否
		systemConfigPo.setProductRecoverAuditingYn(new Byte("0"));
		// 【恢复回收站商品时审核】为是时，【商品恢复审核人】必填，选择角色-药店负责人、企业负责人、质量负责人
		systemConfigPo.setProductRecoverAuditingRole("");

		systemConfigPo.setProductPriceTwoDecimalPlacesYn(new Byte("0"));
		systemConfigPo.setProductMedicineSaleDecimalYn(new Byte("1"));
		systemConfigPo.setProductDrugSaleDecimalYn(new Byte("1"));
		return sysConfigService.initSystemConfig(systemConfigPo);
	}

	@Override
	public boolean initSystemConfig(String organSign, Integer employeeId) {
		logger.info("初始化系统设置，机构号:{}:employeeId:{}", organSign, employeeId);
		return init(organSign, String.valueOf(employeeId));
	}

	public SystemConfigDto getDefaultSystemConfig(String organSign) {
		SystemConfigDto dto = new SystemConfigDto();
		dto.setPrescriptionYn(new Byte("1"));
		dto.setUpdateOrderDiscountYn(new Byte("1"));
		dto.setUpdateProductDiscountYn(new Byte("1"));
		dto.setUpdateProductPrice(new Byte("1"));
		dto.setSalesBelowCost(new Byte("1"));
		dto.setIdCardYn(new Byte("1"));
		dto.setSavePreSaleYn(new Byte("1"));
		dto.setOneSetpYn(new Byte("0"));
		dto.setBatchNoQueueYn(new Byte("0"));
		dto.setFlaxNum(2);
		dto.setPurchaseName("");
		dto.setExaminerName("");
		dto.setPosShowPurpriceYn(new Byte("1"));
		dto.setApproveProductYn(new Byte("0"));
		dto.setApproveProviderYn(new Byte("0"));
		dto.setApproveProductQuickYn(new Byte("0"));
		dto.setApproveProviderQuickYn(new Byte("0"));
		dto.setApprovePrescriptionYn(new Byte("0"));
		dto.setEnterpriseOwners("");
		dto.setEnterpriseOwnersEmployeeId(0);
		dto.setQualityOwners("");
		dto.setQualityOwnersEmployeeId(0);
		dto.setLicensedPharmacist(initLicensedPharmacist(organSign));
		dto.setSaleAmountYn(new Byte("0"));
		dto.setSmallChange(new Byte("0"));
		dto.setSmallChangeOpen("0");
		dto.setSmallChangeClose("0");
		dto.setOrganSign(organSign);
		dto.setCashChange(new Byte("0"));
		dto.setMemberReminder(new Byte("0"));
		dto.setShortageRegistration(new Byte("1"));
		dto.setFullStaffDownload(new Byte("0"));
		dto.setSettlementMethod("");
		dto.setMemberInputReminder(new Byte("0"));
		dto.setBusinessStartTime("-1");
		dto.setProductStartTime("-1");
		dto.setConventionalCuringCycle(30);
		dto.setCriticalCuringPeriod(15);
		dto.setAdvanceWarningCuring(3);
		dto.setAdvanceWarningInspection(3);
		dto.setRoutineInspectionCycle(30);
		dto.setCriticalInspectionCycle(15);
		dto.setEarlyWarningDays(getDefaultEarlyWarningDays(dto.getOrganSign()));
		dto.setHealthNoteSwitch(new Byte("0"));
		dto.setRemoteInquiry(new Byte("1"));
		dto.setStoredValueAuditPOS(new Byte("1"));
		dto.setStoredValueAuditWEB(new Byte("1"));
		dto.setInventoryAuditRoleId(0);
		dto.setStoredValueSMSRemind(new Byte("0"));
		dto.setPromotionPriority(new Byte("1"));
		dto.setCouponNoteYn(SystemConstans.COUPON_NOTE_YN_YES);
		dto.setBusinessScopeYn(new Byte(SystemConstans.BUSINESS_SCOPE_YN_YES));
		dto.setMaintenancePlanData(SystemConstans.MAINTENANCE_PLAN_DATA);
		dto.setMaintenanceDataUpdateYn(SystemConstans.MAINTENANCE_DATA_UPDATE_YN_NO);
		dto.setReturnSalesYn(SystemConstans.RETURN_SALES_YN_NO);
		dto.setPrescriptionRegisteredYn(SystemConstans.PRESCRIPTION_REGISTERED_YN);
		dto.setSaleStorageYn(SystemConstans.SALE_STORAGE_NO);
		dto.setDrugSupervisionYn(SystemConstans.DRUG_SUPERVISION_NO);
		dto.setProviderQualificationExpiredRemindDays(SystemConstans.PROVIDER_QUALIFICATION_EXPIRED_REMIND_DAYS);
		dto.setProductQualificationExpiredRemindDays(SystemConstans.PRODUCT_QUALIFICATION_EXPIRED_REMIND_DAYS);
		dto.setDeadStockWarningDays(SystemConstans.DEAD_STOCK_WARNING_DAYS);
		dto.setDeadStockWarningCount(SystemConstans.DEAD_STOCK_WARNING_COUNT);
		dto.setBlindShiftHandover(SystemConstans.BLIND_SHIFT_HANDOVER);
		dto.setAccountSetYn(new Byte("0"));
		dto.setReceivableNumber(new Byte("1"));
		dto.setPhoneSignSwitch(new Byte("0"));
		dto.setInventoryLotNumAdjustSwitch(0);
		dto.setChineseMedicineNumber(99);
		dto.setChineseMedicineUnit(new Byte("1"));
		//默认一级审核角色执业药师、二级审核角色核对人、三级审核角色发药人
		dto.setAuditingFirstRole(SystemRoleCodeEnum.LICENSED_PHARMACIST_ROLE.getCode());
		dto.setAuditingSecondRole(SystemRoleCodeEnum.CEHCKER_ROLE.getCode());
		dto.setAuditingThirdRole(SystemRoleCodeEnum.DRUG_GIVER_ROLE.getCode());
		dto.setProductTraceUploadNonMsfxYn(new Byte("0"));
		dto.setHiddenTrackPrescriptionInfo(new Byte("0"));
		// 控制POS“医保匹配功能”开关, 默认展示
		dto.setShowMedicareMatchBtnYn(new Byte("1"));
		//追溯码销售录入
		dto.setTraceCodeSaleEntryYn(new Byte("0"));
		dto.setDeviceUdiSupervisionYn(new Byte("0"));
		// 默认【恢复回收站商品时审核】为否
		dto.setProductRecoverAuditingYn(new Byte("0"));
		// 【恢复回收站商品时审核】为是时，【商品恢复审核人】必填，选择角色-药店负责人、企业负责人、质量负责人
		dto.setProductRecoverAuditingRole("");

		dto.setProductPriceTwoDecimalPlacesYn(new Byte("0"));
		dto.setProductMedicineSaleDecimalYn(new Byte("1"));
		dto.setProductDrugSaleDecimalYn(new Byte("1"));
		return dto;
	}

	@Override
	public SystemConfigDto querySystemConfig(String organSign) {
		return sysConfigService.querySystemConfig(organSign);
	}

	@Override
	public ResultVO<SystemConfigPOSDto> getPOSSysConfig(String organSign) {
		SystemConfigPOSDto posDto = new SystemConfigPOSDto();
		SystemConfigDto all= getAllSysConfig(organSign);
		BeanUtils.copyProperties(all,posDto);
		return ResultVO.createSuccess(posDto);
	}
	public SystemConfigDto getAllSysConfig(String organSign) {
		SystemConfigDto config = this.querySystemConfig(organSign);
		if (config == null) {
			return this.getDefaultSystemConfig(organSign);
		}
		return config;
	}
	@Override
	public ResultVO<SystemConfigWEBDto> getWEBSysConfig(String organSign) {
		SystemConfigWEBDto webDto = new SystemConfigWEBDto();
		SystemConfigDto all= getAllSysConfig(organSign);
		BeanUtils.copyProperties(all,webDto);
		convertConfig(webDto);
		// 显示医保进销存提醒地区控制 - 湖南
		SaaSDrugstoreDto drugstore = drugstoreApi.getDrugstoreByOrganSign(organSign);
		if(queryShowSaleStorageAreaConfig().stream().anyMatch(c -> StringUtils.contains(drugstore.getAreaCode(), c))){
			webDto.setAreaSaleStorageYn((byte)1);
		}else{
			webDto.setAreaSaleStorageYn((byte)0);
		}
		// 辽宁丹东
		if(queryShowAreaDrugSupervisionConfig().stream().anyMatch(c -> StringUtils.contains(drugstore.getAreaCode(), c))){
			webDto.setAreaDrugSupervisionYn((byte)1);
		}else{
			webDto.setAreaDrugSupervisionYn((byte)0);
		}
		return ResultVO.createSuccess(webDto);
	}


	public List<String> queryShowSaleStorageAreaConfig() {
		List<String> senderAccountAreaConfigList = new ArrayList<>();
		if(StringUtils.isBlank(showSaleStorageConfig)){
			return senderAccountAreaConfigList;
		}
		try {
			List<String> configs = JSON.parseArray(showSaleStorageConfig, String.class);
			senderAccountAreaConfigList.addAll(configs);
		}catch (Exception e){
		}
		return senderAccountAreaConfigList;
	}

	public List<String> queryShowAreaDrugSupervisionConfig() {
		List<String> senderList = new ArrayList<>();
		if(StringUtils.isBlank(showAreaDrugSupervisionConfig)){
			return senderList;
		}
		try {
			List<String> configs = JSON.parseArray(showAreaDrugSupervisionConfig, String.class);
			senderList.addAll(configs);
		}catch (Exception e){
		}
		return senderList;
	}

	private void convertConfig(SystemConfigWEBDto webDto) {
		if (webDto != null) {
			if (!StringUtil.isEmpty(webDto.getAuditingFirstRole())){
				switch (webDto.getAuditingFirstRole()) {
					//调剂人
					case "MDJS03476" :
						webDto.setAdjuster(StringUtil.isEmpty(webDto.getLicensedPharmacist()) ? "":webDto.getLicensedPharmacist());
						break;
					//发药人
					case "MDJS02168" :
						webDto.setDispenser(StringUtil.isEmpty(webDto.getLicensedPharmacist()) ? "":webDto.getLicensedPharmacist());
						break;
					//核对人
					case "MDJS02166" :
						webDto.setCollator(StringUtil.isEmpty(webDto.getLicensedPharmacist()) ? "":webDto.getLicensedPharmacist());
						break;
					//审核人:MDJS00009
					default:
						webDto.setApprovalUser(StringUtil.isEmpty(webDto.getLicensedPharmacist()) ? "":webDto.getLicensedPharmacist());
				}
			}
			if (!StringUtil.isEmpty(webDto.getAuditingSecondRole())){
				switch (webDto.getAuditingSecondRole()) {
					//调剂人
					case "MDJS03476" :
						webDto.setAdjuster(webDto.getCheckUserId() == null || webDto.getCheckUserId() == 0 ? "":webDto.getCheckUserId().toString());
						break;
						//发药人
					case "MDJS02168" :
						webDto.setDispenser(webDto.getCheckUserId() == null || webDto.getCheckUserId() == 0 ? "":webDto.getCheckUserId().toString());
						break;
						//核对人
					case "MDJS02166" :
						webDto.setCollator(webDto.getCheckUserId() == null || webDto.getCheckUserId() == 0 ? "":webDto.getCheckUserId().toString());
						break;
						//审核人:MDJS00009
					default:
						webDto.setApprovalUser(webDto.getCheckUserId() == null || webDto.getCheckUserId() == 0 ? "":webDto.getCheckUserId().toString());
				}
			}
			if (!StringUtil.isEmpty(webDto.getAuditingThirdRole())){
				switch (webDto.getAuditingThirdRole()) {
					//调剂人
					case "MDJS03476" :
						webDto.setAdjuster(webDto.getSendDrugUserId() == null || webDto.getSendDrugUserId() == 0 ? "":webDto.getSendDrugUserId().toString());
						break;
						//发药人
					case "MDJS02168" :
						webDto.setDispenser(webDto.getSendDrugUserId() == null || webDto.getSendDrugUserId() == 0 ? "":webDto.getSendDrugUserId().toString());
						break;
						//核对人
					case "MDJS02166" :
						webDto.setCollator(webDto.getSendDrugUserId() == null || webDto.getSendDrugUserId() == 0 ? "":webDto.getSendDrugUserId().toString());
						break;
						//审核人:MDJS00009
					default:
						webDto.setApprovalUser(webDto.getSendDrugUserId() == null || webDto.getSendDrugUserId() == 0 ? "":webDto.getSendDrugUserId().toString());
				}
			}
		}
	}

	@Override
	public ResultVO<Boolean> updateForPOS(SystemConfigPOSDto posDto,String organSign) {
		if(posDto==null) {
			return new ResultVO(ResultCodeEnum.ERROR, null);
		}
		try{
			posDto.setOrganSign(organSign);
			SystemConfigDto config = new SystemConfigDto();
			BeanUtils.copyProperties(posDto,config);
			if (config.getProductDiscountStart() != null && config.getProductDiscountEnd() != null) {
				if (config.getProductDiscountEnd() > 200){
					return ResultVO.createError(ResultCodeEnum.ERROR, "单品折扣上限不能超过200%");
				}
				if (config.getProductDiscountStart() > config.getProductDiscountEnd()){
					return ResultVO.createError(ResultCodeEnum.ERROR,"单品折扣下限不可高于折扣上限");
				}
				if (config.getProductDiscountStart() < 1){
					return ResultVO.createError(ResultCodeEnum.ERROR,"单品折扣下限不可低于1%");
				}
			}

			if (config.getProductDiscountStart() == null){
				config.setProductDiscountStart(0);
			}

			if (config.getProductDiscountEnd() == null){
				config.setProductDiscountEnd(0);
			}
			boolean bool = this.updateSystemConfig(config);
			if (bool) {
				Map<String, String> data = new HashMap<>(1);
				data.put("code", "systemConfig");
				try {
					messagePushApi.sendMsgByOrganSign(config.getOrganSign(), JSONUtils.obj2JSON(data));
				} catch (Exception e) {
					logger.error("控制面板pos Json转换异常，异常信息:" + e.getMessage());
				}
				return ResultVO.createSuccess(null);
			} else {
				return new ResultVO(ResultCodeEnum.ERROR, null);
			}
		}catch(Exception e){
			logger.error("updateForPOS error",e);
			return ResultVO.createError(ResultCodeEnum.ERROR, "服务端处理异常");
		}
	}

	@Override
	public ResultVO<Boolean> updateForWEB(SystemConfigWEBDto webDto,String organSign) {
		if(webDto==null) {
			return new ResultVO(ResultCodeEnum.ERROR, "参数为空");
		}

		if(webDto.getProductTraceUploadYn()!=null&&webDto.getProductTraceUploadNonMsfxYn()!=null&&webDto.getProductTraceUploadYn().equals((byte)1)&&webDto.getProductTraceUploadNonMsfxYn().equals((byte)1)) {
			return new ResultVO(ResultCodeEnum.ERROR, "【码上放心】追溯码开关和【非码上放心追溯码】开关不能同时开启");
		}

		try{
			webDto.setOrganSign(organSign);
			//储值审核状态 当系统设置储值卡审核为是时，pos端同步；pos端设置时，系统设置位置保持原有设置不变
//			if(new Byte("1").equals(webDto.getStoredValueAuditWEB())){
//				webDto.setStoredValueAuditPOS(new Byte("1"));
//			}
			SystemConfigDto config = new SystemConfigDto();
			BeanUtils.copyProperties(webDto,config);
			ResultVO<Boolean> booleanResultVO = this.updateSystemConfigNew(config);
			if (booleanResultVO.getResult()) {
				Map<String, String> data = new HashMap<>(1);
				data.put("code", "systemConfig");
				try {
					messagePushApi.sendMsgByOrganSign(config.getOrganSign(), JSONUtils.obj2JSON(data));
				} catch (Exception e) {
					logger.error("控制面板web Json转换异常，异常信息:" + e.getMessage());
				}
				return ResultVO.createSuccess(null);
			} else {
				return new ResultVO(ResultCodeEnum.ERROR, booleanResultVO.getMsg());
			}
		}catch(Exception e){
			logger.error("updateForWEB error",e);
			return ResultVO.createError(ResultCodeEnum.ERROR, "服务端处理异常");
		}
	}

	// 获取近效期默认值
	private Integer getDefaultEarlyWarningDays(String organSign) {
		SaaSDrugstoreDto drugstore = drugstoreApi.getDrugstoreByOrganSign(organSign);
		// 连锁总部
		if (drugstore != null && drugstore.getBizModel() == DrugstoreBizModelEnum.CHAIN_STORE.getKey()
				&& drugstore.getOrganSignType() == DrugstoreTypeEnum.HEADQUARTERS.getKey()) {
			return SystemConstans.EARLY_WARNING_DAYS_HEADQUARTERS;
		}
		// 其余情况
		return SystemConstans.EARLY_WARNING_DAYS;
	}

	/**
	 * 刷数据
	 * 需求地址：https://wiki.int.ybm100.com/pages/viewpage.action?pageId=311855419
	 * 将单体、连锁门店、联营门店 系统设置里的 执业药师为空 的刷成药店负责人id
	 * @return
	 */
	@Override
	public ResultVO updateSysConfigLicensedPharmacist() {
		//查询系统设置里执业药师为空的机构号列表
		List<String> emptyLicensedPharmacistOrgList = sysConfigService.queryEmptyLicensedPharmacistOrgList();
		if (CollectionUtils.isEmpty(emptyLicensedPharmacistOrgList)) {
			return ResultVO.createSuccess();
		}
		List<SaaSDrugstoreDto> drugstoreDtoList = drugstoreApi.queryDrugstoreListByOrganSignList(emptyLicensedPharmacistOrgList);
		if (CollectionUtils.isNotEmpty(drugstoreDtoList)) {
			//单体、连锁门店、联营门店机构号
			List<String> storeOrgList = drugstoreDtoList.stream().filter(d -> DrugstoreTypeEnum.DRUGSTORE.getKey() == Integer.valueOf(d.getOrganSignType())).map(SaaSDrugstoreDto::getOrganSign).collect(Collectors.toList());
			if (CollectionUtils.isNotEmpty(storeOrgList)) {
				//查询单体、连锁门店、联营门店 对应的执业药师,更新系统设置的执业药师字段
				List<EmployeeDto> licensedPharmacists = employeeApi.getEmployeeListByRoleIdAndOrganSignList(RoleEnum.STORE_ADMIN_ROLE.getKey(), storeOrgList);
				Map<String,Integer> licensedPharmacistsMap = licensedPharmacists.stream().collect(Collectors.toMap(p -> p.getOrganSign(), p -> p.getId(), (v1, v2) -> v2));
				storeOrgList.stream().forEach(org -> {
					Integer emp = licensedPharmacistsMap.get(org);
					if (emp != null) {
						sysConfigService.updateEmptyLicensedPharmacist(org, String.valueOf(emp));
					}
				});
			}
		}
		return ResultVO.createSuccess();
	}

	@Override
	public List<String> getOpenTraceCodeSalesOrganSignList() {
		return sysConfigService.getOpenTraceCodeSalesOrganSignList();
	}
}
