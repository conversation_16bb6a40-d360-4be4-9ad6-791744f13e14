package com.xyy.common.provider.module.service.impl;

import com.xyy.common.module.api.SysConfigApi;
import com.xyy.common.module.dto.SystemConfigDto;
import com.xyy.common.module.dto.SystemConfigWEBDto;
import com.xyy.common.provider.common.constant.CacheConstants;
import com.xyy.common.provider.module.dao.SystemConfigMapper;
import com.xyy.common.provider.module.entity.SystemConfigPo;
import com.xyy.common.provider.module.service.SysConfigService;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.api.RoleApi;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import com.xyy.user.module.dto.result.EmployeeDto;
import com.xyy.user.module.dto.result.QueryDrugstoreDto;
import com.xyy.user.module.dto.result.QueryEmployeeVO;
import com.xyy.user.provider.module.utils.AreaCodeUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 控制面板
 */
@Service
public class SysConfigServiceImpl implements SysConfigService {
	private static final Logger logger = LoggerFactory.getLogger(SysConfigServiceImpl.class);
	@Autowired
	private SystemConfigMapper systemConfigMapper;

    @Autowired
    private EmployeeApi employeeApi;

	@Autowired
	private SysConfigApi sysConfigApi;

    @Autowired
    private DrugstoreApi drugstoreApi;

    @Autowired
    public RoleApi roleApi;

	@CacheEvict(value = CacheConstants.CACHE_ORGAN_SYSCONFIG, allEntries = true)
	@Override
    public boolean updateSystemConfig(SystemConfigPo config) {

        if (!StringUtils.isEmpty(config.getEnterpriseOwners())
        		&& config.getEnterpriseOwnersEmployeeId() != null && config.getEnterpriseOwnersEmployeeId() != 0) {
            ResultVO<EmployeeDto> employeeDtoResultVO = employeeApi.queryEmployeeById(config.getEnterpriseOwnersEmployeeId());
            if (employeeDtoResultVO.getCode() != 0) {
				logger.error("updateSystemConfig cannot set EnterpriseOwnersEmployeeId ", JSONUtils.obj2JSON(config));
                return false;
            }
            config.setEnterpriseOwners(employeeDtoResultVO.getResult().getName());
            config.setEnterpriseOwnersEmployeeId(employeeDtoResultVO.getResult().getId());
        }

        if (!StringUtils.isEmpty(config.getQualityOwners())
        		&& config.getQualityOwnersEmployeeId()!=null && config.getQualityOwnersEmployeeId() != 0) {
            ResultVO<EmployeeDto> employeeDtoResultVO = employeeApi.queryEmployeeById(config.getQualityOwnersEmployeeId());
            if (employeeDtoResultVO.getCode() != 0) {
				logger.error("updateSystemConfig cannot set QualityOwnersEmployeeId ", JSONUtils.obj2JSON(config));
                return false;
            }
            config.setQualityOwners(employeeDtoResultVO.getResult().getName());
            config.setQualityOwnersEmployeeId(employeeDtoResultVO.getResult().getId());
        }

        //判断当前机构号是否绑定要帮忙，如果是，开启健康笔记开关，否，不能开启
        if(config.getHealthNoteSwitch()!=null && new Byte("1").equals(config.getHealthNoteSwitch())){
        	ResultVO<Boolean> ybmResult = drugstoreApi.queryYbmBindOrNotBindByOrganSign(config.getOrganSign());
            if(!ybmResult.getResult()){
				logger.error("updateSystemConfig cannot set HealthNoteSwitch ", JSONUtils.obj2JSON(config));
            	return false;
            }
        }
        int update = systemConfigMapper.update(config);
        if (update >= 1) {
            return true;
        }
        return false;
    }

	@CacheEvict(value = CacheConstants.CACHE_ORGAN_SYSCONFIG, allEntries = true)
	@Override
	public ResultVO<Boolean> updateSystemConfigNew(SystemConfigPo config) {

		if (!StringUtils.isEmpty(config.getEnterpriseOwners())
				&& config.getEnterpriseOwnersEmployeeId() != null && config.getEnterpriseOwnersEmployeeId() != 0) {
			ResultVO<EmployeeDto> employeeDtoResultVO = employeeApi.queryEmployeeById(config.getEnterpriseOwnersEmployeeId());
			if (employeeDtoResultVO.getCode() != 0) {
				logger.error("updateSystemConfig cannot set EnterpriseOwnersEmployeeId ", JSONUtils.obj2JSON(config));
				return new ResultVO<>(ResultCodeEnum.ERROR,"离职员工，无法设置",false);
			}
			config.setEnterpriseOwners(employeeDtoResultVO.getResult().getName());
			config.setEnterpriseOwnersEmployeeId(employeeDtoResultVO.getResult().getId());
		}

		if (!StringUtils.isEmpty(config.getQualityOwners())
				&& config.getQualityOwnersEmployeeId()!=null && config.getQualityOwnersEmployeeId() != 0) {
			ResultVO<EmployeeDto> employeeDtoResultVO = employeeApi.queryEmployeeById(config.getQualityOwnersEmployeeId());
			if (employeeDtoResultVO.getCode() != 0) {
				logger.error("updateSystemConfig cannot set QualityOwnersEmployeeId ", JSONUtils.obj2JSON(config));
				return new ResultVO<>(ResultCodeEnum.ERROR,"离职员工，无法设置",false);
			}
			config.setQualityOwners(employeeDtoResultVO.getResult().getName());
			config.setQualityOwnersEmployeeId(employeeDtoResultVO.getResult().getId());
		}

		//判断当前机构号是否绑定要帮忙，如果是，开启健康笔记开关，否，不能开启
		if(config.getHealthNoteSwitch()!=null && new Byte("1").equals(config.getHealthNoteSwitch())){
			ResultVO<Boolean> ybmResult = drugstoreApi.queryYbmBindOrNotBindByOrganSign(config.getOrganSign());
			if(!ybmResult.getResult()){
				logger.error("updateSystemConfig cannot set HealthNoteSwitch ", JSONUtils.obj2JSON(config));
				return new ResultVO<>(ResultCodeEnum.ERROR,"当前账号未绑定药帮忙账号，请将云医笔记开关关闭，再提交",false);
			}
		}

		if (config.getApprovePrescriptionYn() != null){
			if (StringUtils.isEmpty(config.getLicensedPharmacist())){
				config.setLicensedPharmacist("");
			}
			if (config.getCheckUserId() == null){
				config.setCheckUserId(0);
			}
			if (config.getSendDrugUserId() == null){
				config.setSendDrugUserId(0);
			}
		}

		// 校验追溯码开关
		String errorMsg = checkTraceCodeSwitchYn(config);
		if (org.apache.commons.lang3.StringUtils.isNotBlank(errorMsg)){
			logger.error("updateSystemConfigNew -> errorMsg: {}", JSONUtils.obj2JSON(errorMsg));
			return new ResultVO<>(ResultCodeEnum.ERROR,errorMsg,false);
		}

		if(config.getProductTraceUploadYn() != null){
			//码上放心标识不为空时处理码上放心业务
			handleMsfx(config);
		}

		// 开启药监监管按钮 辽宁丹东地区校验
		String errorTips = handleDrugSupervision(config);
		if (org.apache.commons.lang3.StringUtils.isNotBlank(errorTips)){
			logger.error("updateSystemConfigNew -> errorTips: {}", JSONUtils.obj2JSON(errorTips));
			return new ResultVO<>(ResultCodeEnum.ERROR,errorTips,false);
		}

		//开启器械UDI数据上传
		if (config.getDeviceUdiSupervisionYn() != null && config.getDeviceUdiSupervisionYn() == 1){
			SaaSDrugstoreDto drugstore = drugstoreApi.getDrugstoreByOrganSign(config.getOrganSign());
			logger.info("河北地区药店信息：{}",JSONUtils.obj2JSON(drugstore));
			//若为河北地区的药店，判断是否填写入网ID
			String areaCode = drugstore.getAreaCode();
			String provinceCode = "";
			if (!StringUtils.isEmpty(areaCode)) {
				provinceCode = AreaCodeUtil.getProvinceCode(areaCode);
			}
			if (!ObjectUtils.isEmpty(drugstore) && provinceCode.equals("130000")){
				//判断是否填写器械监管授权码
				//如果未填写，提示“请填写入网ID后开始数据上传”；若设置入网ID，则正常开启
				if (org.apache.commons.lang3.StringUtils.isBlank(drugstore.getDeviceSupervisionCode())){
					return new ResultVO<>(ResultCodeEnum.ERROR,"请填写器械监管入网ID后开始数据上传",false);
				}
			}
		}

		int update = systemConfigMapper.update(config);
		if (update >= 1) {
			return  new ResultVO<>(true);
		}
		return new ResultVO<>(false);
	}

	private String handleDrugSupervision(SystemConfigPo config) {
		if (config.getDrugSupervisionYn() != null && config.getDrugSupervisionYn() == 1){
			ResultVO<QueryDrugstoreDto> queryDrugstoreDtoResultVO = drugstoreApi.queryDrugstoreByOrganSign(config.getOrganSign());
			if (queryDrugstoreDtoResultVO.getCode() != ResultCodeEnum.SUCCESS.getCode()) {
				return "";
			}
			QueryDrugstoreDto drugstore = queryDrugstoreDtoResultVO.getResult();
			if (drugstore == null) {
				return "";
			}
			logger.info("药店信息：{}",JSONUtils.obj2JSON(drugstore));
			ResultVO<SystemConfigWEBDto> webSysConfig = sysConfigApi.getWEBSysConfig(config.getOrganSign());
			if (webSysConfig.getCode() != ResultCodeEnum.SUCCESS.getCode()) {
				return "";
			}
			if (Objects.equals(webSysConfig.getResult().getAreaDrugSupervisionYn(),(byte)0)){
				return "";
			}
			// 若为辽宁丹东地区的药店，判断是否填写入网ID
			if(org.apache.commons.lang3.StringUtils.isBlank(drugstore.getSenderId()) ||
					org.apache.commons.lang3.StringUtils.isBlank(drugstore.getSecretKey()) ||
					org.apache.commons.lang3.StringUtils.isBlank(drugstore.getSenderExt()) ||
					org.apache.commons.lang3.StringUtils.isBlank(drugstore.getSenderAccount())){
				return "请前往【机构管理-药监授权码】填写入网信息";
			}
		}
		return "";
	}

	private String checkTraceCodeSwitchYn(SystemConfigPo config) {
		if (config == null) {
			return "";
		}
		// 获取各个开关的状态
		boolean isTraceCodeSaleEntryEnabled = isSwitchEnabled(config.getTraceCodeSaleEntryYn());
		boolean isProductTraceUploadEnabled = isSwitchEnabled(config.getProductTraceUploadYn());
		boolean isProductTraceUploadNonMsfxEnabled = isSwitchEnabled(config.getProductTraceUploadNonMsfxYn());
		if (isProductTraceUploadEnabled
				&& isProductTraceUploadNonMsfxEnabled
				&& isTraceCodeSaleEntryEnabled) {
			return "【药品追溯数据上传】和【非码上放心追溯码】和【追溯码销售录入】开关不能同时开启！";
		}
		// 判断多个开关同时开启的情况
		if (isProductTraceUploadEnabled && isTraceCodeSaleEntryEnabled) {
			return "【药品追溯数据上传】和【追溯码销售录入】开关不能同时开启！";
		}
		if (isProductTraceUploadEnabled && isProductTraceUploadNonMsfxEnabled) {
			return "【药品追溯数据上传】和【药品追溯码（非码上放心）】开关不能同时开启！";
		}
		if (isProductTraceUploadNonMsfxEnabled && isTraceCodeSaleEntryEnabled) {
			return "【药品追溯码（非码上放心）】和【追溯码销售录入】开关不能同时开启！";
		}
		// 如果没有冲突，返回空字符串
		return "";
	}

	private boolean isSwitchEnabled(Byte value) {
		return value != null && value == 1;
	}

	private void handleMsfx(SystemConfigPo config) {
		//开启情况下不处理
		if(config.getProductTraceUploadYn() == 1){
			return;
		}
		SystemConfigPo configPo = systemConfigMapper.selectSystemConfigByOrganSign(config.getOrganSign());
		if(ObjectUtils.isEmpty(configPo) || configPo.getProductTraceUploadYn() == null){
			return;
		}
		//没有变更则不处理
		if(config.getProductTraceUploadYn() == configPo.getProductTraceUploadYn()){
			return;
		}
		//置空码上放心标识
		SaaSDrugstoreDto dto = new SaaSDrugstoreDto();
		dto.setOrganSign(config.getOrganSign());
		dto.setMsfxSign("");
		drugstoreApi.updateDrugstoreByOrganSign(dto);
	}

	@Override
	@CacheEvict(value = CacheConstants.CACHE_ORGAN_SYSCONFIG, allEntries = true)
	public boolean initSystemConfig(SystemConfigPo systemConfigPo) {
        int insert = systemConfigMapper.insert(systemConfigPo);
        if (insert >= 1) {
            return true;
        }
        return false;
	}

	@Override
	public SystemConfigPo queryInitSystemConfig(String organSign) {
		SystemConfigPo systemConfigPo = systemConfigMapper.selectSystemConfigByOrganSign(organSign);
		if (systemConfigPo != null){
			if (systemConfigPo.getProductDiscountStart() == 0){
				systemConfigPo.setProductDiscountStart(1);
			}
			if (systemConfigPo.getProductDiscountEnd() == 0){
				systemConfigPo.setProductDiscountEnd(100);
			}
		}
		return systemConfigMapper.selectSystemConfigByOrganSign(organSign);
	}

	@Override
	@Cacheable(value = CacheConstants.CACHE_ORGAN_SYSCONFIG, key = "#organSign.concat('querySystemConfig')", condition = "#organSign != null")
	public SystemConfigDto querySystemConfig(String organSign) {
        SystemConfigDto systemConfigDto = new SystemConfigDto();
        SystemConfigPo systemConfigPo = systemConfigMapper.selectSystemConfigByOrganSign(organSign);
        if (systemConfigPo == null) {
            return null;
        }else {
        	if (systemConfigPo.getProductDiscountStart() == 0){
				systemConfigPo.setProductDiscountStart(1);
			}
        	if (systemConfigPo.getProductDiscountEnd() == 0){
        		systemConfigPo.setProductDiscountEnd(100);
			}
		}
        processSystemConfig(systemConfigPo,organSign);
        BeanUtils.copyProperties(systemConfigPo, systemConfigDto);
        return systemConfigDto;
    }

	private final static List<Integer> roleIds = Stream.of(2, 3, 4, 5, 9).collect(Collectors.toList());


	private void processSystemConfig(SystemConfigPo systemConfigPo,String organSign){

//		logger.info("querySystemConfig.processSystemConfig,organSign:{}",organSign);
		List<Integer> employeeIds = Stream.of(NumberUtils.toInt(systemConfigPo.getPurchaseName(),-1)
				, NumberUtils.toInt(systemConfigPo.getExaminerName(),-1)
				, systemConfigPo.getQualityOwnersEmployeeId()
				, systemConfigPo.getEnterpriseOwnersEmployeeId()
				, NumberUtils.toInt(systemConfigPo.getLicensedPharmacist(),-1)
		).filter(e->e!=null && !Objects.equals(e,-1)).collect(Collectors.toList());

		if(CollectionUtils.isEmpty(employeeIds)){
			return;
		}

		// 查员工和角色
		ResultVO<List<QueryEmployeeVO>> listResultVO = roleApi.queryByEmployeeIdRoleId(employeeIds, roleIds, organSign);

		if(listResultVO.getCode() != 0 || listResultVO.getResult() == null){
			return;
		}

		Map<Integer, List<Integer>> roleEmpListMap = listResultVO.getResult().stream()
				.collect(Collectors.groupingBy(QueryEmployeeVO::getRoleId, Collectors.mapping(QueryEmployeeVO::getEmployeeId, Collectors.toList())));

		List<Integer> purchase = roleEmpListMap.get(4);
		if(CollectionUtils.isEmpty(purchase) || !purchase.contains(NumberUtils.toInt(systemConfigPo.getPurchaseName(),-1))){
			systemConfigPo.setPurchaseName("");
		}

		List<Integer> examiner = roleEmpListMap.get(5);
		if(CollectionUtils.isEmpty(examiner) || !examiner.contains(NumberUtils.toInt(systemConfigPo.getExaminerName(),-1))){
			systemConfigPo.setExaminerName("");
		}

		List<Integer> quality = roleEmpListMap.get(3);
		if(CollectionUtils.isEmpty(quality) || !quality.contains(systemConfigPo.getQualityOwnersEmployeeId())){
			systemConfigPo.setQualityOwnersEmployeeId(0);
		}

//		List<Integer> enterprise = roleEmpListMap.get(2);
//		if(CollectionUtils.isEmpty(enterprise) || !enterprise.contains(systemConfigPo.getEnterpriseOwnersEmployeeId())){
//			systemConfigPo.setEnterpriseOwnersEmployeeId(0);
//		}
//
//		List<Integer> licensed = roleEmpListMap.get(9);
//		if(CollectionUtils.isEmpty(licensed) || !licensed.contains(NumberUtils.toInt(systemConfigPo.getLicensedPharmacist()))){
//			systemConfigPo.setLicensedPharmacist("");
//		}


//    	ResultVO<List<QueryEmployeeVO>> purchase = roleApi.queryAllEmployeeByRoleId(4, organSign);
//        if(purchase.getCode() == 0){
//        	boolean isflag = true;
//        	List<QueryEmployeeVO> purchaseLst = purchase.getResult();
//        	if(purchaseLst!=null&&!purchaseLst.isEmpty()){
//        		for(QueryEmployeeVO vo:purchaseLst){
//        			if(String.valueOf(vo.getEmployeeId()).equals(systemConfigPo.getPurchaseName())){
//        				isflag = false;
//        				break;
//        			}
//        		}
//        	}
//        	if(isflag){
//    			systemConfigPo.setPurchaseName("");
//    		}
//        }
//        ResultVO<List<QueryEmployeeVO>> examiner = roleApi.queryAllEmployeeByRoleId(5, organSign);
//        if(examiner.getCode() == 0){
//        	boolean isflag = true;
//        	List<QueryEmployeeVO> examinerLst = examiner.getResult();
//        	if(examinerLst!=null&&!examinerLst.isEmpty()){
//
//        		for(QueryEmployeeVO vo:examinerLst){
//        			if(String.valueOf(vo.getEmployeeId()).equals(systemConfigPo.getExaminerName())){
//        				isflag = false;
//        				break;
//        			}
//        		}
//        	}
//        	if(isflag){
//    			systemConfigPo.setExaminerName("");
//    		}
//        }
//        ResultVO<List<QueryEmployeeVO>> quality = roleApi.queryAllEmployeeByRoleId(3, organSign);
//        if(quality.getCode() == 0){
//        	boolean isflag = true;
//        	List<QueryEmployeeVO> qualityLst = quality.getResult();
//        	if(qualityLst!=null&&!qualityLst.isEmpty()){
//        		for(QueryEmployeeVO vo:qualityLst){
//        			if(vo.getEmployeeId().equals(systemConfigPo.getQualityOwnersEmployeeId())){
//        				isflag = false;
//        				break;
//        			}
//        		}
//        	}
//        	if(isflag){
//    			systemConfigPo.setQualityOwnersEmployeeId(0);;
//    		}
//        }
//        ResultVO<List<QueryEmployeeVO>> enterprise = roleApi.queryAllEmployeeByRoleId(2, organSign);
//        if(purchase.getCode() == 0){
//        	boolean isflag = true;
//        	List<QueryEmployeeVO> enterpriseLst = enterprise.getResult();
//        	if(enterpriseLst!=null&&!enterpriseLst.isEmpty()){
//        		for(QueryEmployeeVO vo:enterpriseLst){
//        			if(vo.getEmployeeId().equals(systemConfigPo.getEnterpriseOwnersEmployeeId())){
//        				isflag = false;
//        				break;
//        			}
//        		}
//        	}
////        	if(isflag){
////    			systemConfigPo.setEnterpriseOwnersEmployeeId(0);
////    		}
//        }
//        ResultVO<List<QueryEmployeeVO>> licensed = roleApi.queryAllEmployeeByRoleId(9, organSign);
//        if(purchase.getCode() == 0){
//        	boolean isflag = true;
//        	List<QueryEmployeeVO> licensedLst = licensed.getResult();
//        	if(licensedLst!=null&&!licensedLst.isEmpty()){
//        		for(QueryEmployeeVO vo:licensedLst){
//        			if(String.valueOf(vo.getEmployeeId()).equals(systemConfigPo.getLicensedPharmacist())){
//        				isflag = false;
//        				break;
//        			}
//        		}
//        	}
////        	if(isflag){
////    			systemConfigPo.setLicensedPharmacist("");;
////    		}
//        }
    }

	/**
	 * 查询执业药师为空的机构号
	 * @return
	 */
	@Override
	public List<String> queryEmptyLicensedPharmacistOrgList() {
		return systemConfigMapper.queryEmptyLicensedPharmacistOrgList();
	}

	/**
	 * 更新执业药师字段
	 * @param organSign
	 * @param licensedPharmacist
	 */
	@Override
	@CacheEvict(value = CacheConstants.CACHE_ORGAN_SYSCONFIG, allEntries = true)
	public void updateEmptyLicensedPharmacist(String organSign, String licensedPharmacist) {
		systemConfigMapper.updateEmptyLicensedPharmacist(organSign, licensedPharmacist);
	}

	@Override
	public List<String> getOpenTraceCodeSalesOrganSignList() {
		return systemConfigMapper.getOpenTraceCodeSalesOrganSignList();
	}
}
