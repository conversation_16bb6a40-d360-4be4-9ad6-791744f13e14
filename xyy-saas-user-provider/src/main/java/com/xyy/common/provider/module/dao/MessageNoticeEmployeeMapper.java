package com.xyy.common.provider.module.dao;

import com.dianping.zebra.dao.datasource.ZebraRouting;
import com.xyy.common.provider.module.entity.MessageNoticeEmployeePo;
import com.xyy.common.provider.module.entity.MessageNoticePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@ZebraRouting("common")
public interface MessageNoticeEmployeeMapper {

    Integer insert(MessageNoticeEmployeePo messageNoticeEmployeePo);

    Integer batchInsert(@Param("list") List<MessageNoticeEmployeePo> list);

    List<String> getMessageNoticeDrugstoreList(@Param("messageNoticeId") Long messageNoticeId, @Param("customFlag")Integer customFlag);

    Integer delByOrganSign(@Param("messageNoticeId") Long messageNoticeId, @Param("organSign") String organSign);

    Integer realDelByMessageNoticeId(@Param("messageNoticeId") Long messageNoticeId);

    Integer updateMessageNoticeByEmployeeId(@Param("organSign") String organSign, @Param("employeeId") String employeeId, @Param("list") List<Long> list);

    List<MessageNoticeEmployeePo> getMessageNoticeIdList(MessageNoticePo bean);

}