package com.xyy.common.provider.module.api.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.emule.api.SynchDataInfoApi;
import com.xyy.emule.dto.InitSubTaskDto;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.dto.SaasDrugstore;
import com.xyy.user.module.dto.SaasDrugstoreDto;
import com.xyy.user.provider.common.util.JedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/9/27 16:58
 * @Version 1.0
 * 红尘轮回众生顾,因果循环有定数
 * 放下屠刀虽成佛,愿坠三途灭千魔
 */
@Service(version = "1.0.0", group = "cloudMessageDrugImport", timeout = 20000)
@Slf4j
public class MessageNoticeImportImpl implements SynchDataInfoApi {

    @Autowired
    private JedisUtil jedisUtil;

    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;

    private final static String IMPORT_PREF = "CLOUD:MESSAGE:IMPORT:";

    private final static Integer PAGE_SIZE = 300;


    @Override
    public List<InitSubTaskDto> synchImportData(List<InitSubTaskDto> list) {
        log.info("MessageNoticeImportImpl#synchImportData 导入参数:{}", JSON.toJSONString(list.get(0)));
        if (CollectionUtils.isEmpty(list)) {
            log.warn("MessageNoticeImportImpl#synchImportData list is empty");
            return list;
        }
        List<String> jsonList = new ArrayList<>();
        //结果集
        List<InitSubTaskDto> initSubTaskResults = new ArrayList<>();
        String redisKey = IMPORT_PREF+list.get(0).getTaskId();
        try {
            Map<String, SaasDrugstoreDto> drugstoreDtoMap = getSaasDrugstoreDtoMap(list);

            for (InitSubTaskDto initSubTask : list) {
                //获取listinfo就是一行的数据
                List<String> listInfo = JSONArray.parseArray(initSubTask.getTaskInfo(), String.class);
                String organSign = listInfo.get(0);
                if (StringUtils.isEmpty(organSign)){
                    initSubTask.setStatus(2);
                    initSubTask.setErrorInfo("机构号必填");
                    initSubTaskResults.add(initSubTask);
                    continue;
                }

                SaasDrugstoreDto drugstoreDto = drugstoreDtoMap.get(organSign);
                if(drugstoreDto == null ){
                    initSubTask.setStatus(2);
                    initSubTask.setErrorInfo("不存在的机构号");
                    initSubTaskResults.add(initSubTask);
                    continue;
                }

                Map<String, Object> map = new HashMap<>();
                map.put("organSign", organSign);
                map.put("drugstoreName",  StringUtils.defaultString(drugstoreDto.getDrugstoreName()));
                map.put("managerName", StringUtils.defaultString(drugstoreDto.getManagerName()));
                map.put("area", StringUtils.defaultString(drugstoreDto.getProvince()) + StringUtils.defaultString(drugstoreDto.getCity()) + StringUtils.defaultString(drugstoreDto.getArea()));
                map.put("status",drugstoreDto.getStatus());
                jsonList.add(JSON.toJSONString(map));

                log.info("MessageNoticeImportImpl#synchImportData map:{}",JSON.toJSONString(map));
                initSubTask.setStatus(1);
                initSubTaskResults.add(initSubTask);
            }

            if (!CollectionUtils.isEmpty(jsonList)){
                jedisUtil.setListNoDel(redisKey,jsonList,1000*60*30);
            }

        }catch (Exception e){
            log.error("MessageNoticeImportImpl#synchImportData error:{}", e.getMessage(),e);
            for (InitSubTaskDto initSubTask : list) {
                initSubTask.setStatus(2);
                initSubTask.setErrorInfo("系统异常");
                initSubTaskResults.add(initSubTask);
            }
            return initSubTaskResults;
        }
        return initSubTaskResults;
    }

    private Map<String, SaasDrugstoreDto> getSaasDrugstoreDtoMap(List<InitSubTaskDto> list) {
        // 先筛选机构号
        List<String> organSignList = list.stream().map(t -> {
            try {
                return JSONArray.parseArray(t.getTaskInfo(), String.class).get(0);
            } catch (Exception ignore) { return ""; }
        }).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());


        List<SaasDrugstoreDto> drugstoreDtos = new ArrayList<>(organSignList.size());
        SaasDrugstoreDto model = new SaasDrugstoreDto();
        model.setPageSize(PAGE_SIZE);
        model.setPageNum(1);

        // 批量分段查询机构数据
        for (List<String> organSigns : Lists.partition(organSignList, PAGE_SIZE)) {
            model.setOrganSignList(organSigns);
            ResultVO<PageInfo<SaasDrugstoreDto>> byCondition = drugstoreApi.getDrugstoreByCondition(model);
            if(byCondition!=null && !CollectionUtils.isEmpty(byCondition.getResult().getList())){
                drugstoreDtos.addAll(byCondition.getResult().getList());
            }
        }

        return drugstoreDtos.stream().collect(Collectors.toMap(SaasDrugstore::getOrganSign, Function.identity(), (a, b) -> a));
    }

}
