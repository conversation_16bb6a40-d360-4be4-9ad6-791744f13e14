package com.xyy.common.provider.module.service;

import com.xyy.common.module.dto.SaasBusinessScopeDto;
import com.xyy.common.provider.module.entity.SaasBusinessScopePo;
import com.xyy.saas.common.dto.SystemDictDto;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.dto.restructure.BusinessScopeDto;
import com.xyy.user.module.dto.restructure.QualificationInfo;
import com.xyy.user.module.vo.QualificationInfoVo;
import com.xyy.user.provider.module.entity.SaasDrugstore;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


public interface SaasBusinessScopeService {

    /**
     * 查询全部列表
     */
    List<SaasBusinessScopePo> selectSaasBusinessScopeList(SaasBusinessScopeDto saasBusinessScopePo);

    /**
     * 查询单个
     */
    SaasBusinessScopePo selectSaasBusinessScope(SaasBusinessScopeDto saasBusinessScopeDto);

    /**
     * 初始化数据
     */
    void updateSaasBusinessScopeData(SystemDictDto saasBusinessScopeDto,Integer cerType);

    int updateSaasBusinessScope(SaasBusinessScopeDto saasBusinessScopeDto);

    int updateEnableYn(List<SaasBusinessScopeDto> saasBusinessScopeDto, String organSign);

    List<SaasBusinessScopePo> syncData(SaasBusinessScopeDto dto);

    void initData(SaasBusinessScopeDto saasBusinessScopeDto);

    ResultVO<List<QualificationInfoVo>> selectNBusinessScopeNew(SaasBusinessScopeDto saasBusinessScopeDto);

    int deleteByParam(SaasBusinessScopePo po);

    /**
     * 根据机构号和经营范围删除数据
     * @param organSigns
     * @param dicts
     * @return
     */
    int deleteByOrgansAndDicts(List<String> organSigns,List<Integer> dicts);

    void chainInsert(List<Integer> dtos, Long employeeId, String organSign, Integer certificateType);

    /**
     * 根据机构号查询经营范围
     * @param organSign
     * @return
     */
    List<SaasBusinessScopePo> getBusinessScopeByOrgan(String organSign);

    Map<Integer, Boolean> selectInBizScopeFlagMap(List<Integer> businessScopeList, String organSign);

    /**
     * 根据机构号获取新的经营范围name:id
     * @param organSign
     * @return
     */
    Map<String, Integer> getNameAndId(String organSign);

    /**
     * 刷数据去重
     * @param organSign
     */
    void RefreshBusinessScopeRepeate(String organSign);

    /**
     * 将资质类型刷到scpe
     * @param organSign
     */
    void RefreshType(String organSign);

    int deleteByDictId(String organSign,List<Long> list);

    /**
     * 获取对应资质
     * @return
     */
    Map<Integer, Integer> getType();

    int RefreshCustomBS(String organSign,List<Long> ids);

    /**
     * 根据机构号和经营范围修改状态
     * @param dict
     * @param organSign
     */
    void updateEnableYnByDict(List<Integer> dict,String organSign,Long employeeId);

    int updateEnableYnWithDict(List<SaasBusinessScopeDto> saasBusinessScopeDto,String organSign);

    void RefreshinitData(SaasBusinessScopeDto saasBusinessScopeDto, SaasDrugstore drugstore);

    int refreshParent(List<String> organSigns);

    /**
     * 更新资质时刷新经营范围的version
     * 让POS可以同步数据
     * @param organSign
     * @return
     */
    int updateVersion(String organSign);
}
