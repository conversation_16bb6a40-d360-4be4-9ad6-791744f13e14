package com.xyy.common.provider.module.service;

import com.github.pagehelper.PageInfo;
import com.xyy.common.provider.module.entity.SaasMessageNoticeDrugePo;

import java.util.List;

/**
 * (SaasMessageNoticeDruge)表服务接口
 *
 * <AUTHOR> @since 2021-09-24 16:26:11
 */
public interface SaasMessageNoticeDrugeService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    SaasMessageNoticeDrugePo queryById(Long id);


    /**
     * 新增数据
     *
     * @param saasMessageNoticeDrugePo 实例对象
     * @return 实例对象
     */
    SaasMessageNoticeDrugePo insert(SaasMessageNoticeDrugePo saasMessageNoticeDrugePo);

    /**
     * 修改数据
     *
     * @param saasMessageNoticeDrugePo 实例对象
     * @return 实例对象
     */
    SaasMessageNoticeDrugePo update(SaasMessageNoticeDrugePo saasMessageNoticeDrugePo);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Long id);

    /**
     * 查询指定行数据
     *
     * @param saasMessageNoticeDrugePo 查询条件
     * @return 对象列表
     */
    PageInfo queryAll(SaasMessageNoticeDrugePo saasMessageNoticeDrugePo,PageInfo pageInfo);

}
