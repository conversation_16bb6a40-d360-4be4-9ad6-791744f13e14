package com.xyy.common.provider.module.api.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.xyy.common.module.api.SysConfigShowApi;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.dto.restructure.OpenFingerprintFunctionConfig;
import com.xyy.user.module.dto.restructure.OpenMsfxSwitchConfig;
import com.xyy.user.module.dto.restructure.SysConfigShowCommonDTO;
import com.xyy.user.provider.module.api.impl.DrugstoreApiImpl;
import com.xyy.user.provider.module.dao.SaasDrugstoreMapper;
import com.xyy.user.provider.module.entity.SaasDrugstore;
import com.xyy.user.provider.module.utils.AreaCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.List;

@Slf4j
@Service(version = "0.0.1")
public class SysConfigShowApiImpl implements SysConfigShowApi {

    @Value("${sysConfigShow.showBeforeStopSaleDaysConfig}")
    private String showBeforeStopSaleDaysConfig;

    @Value("${sysConfigShow.showProductSplitRowsConfig}")
    private String showProductSplitRowsConfig;

    @Value("${sysConfigShow.showMsfxConfig}")
    private String showMsfxConfig;

    @Autowired
    private SaasDrugstoreMapper drugstoreMapper;

    @Override
    public ResultVO<Boolean> isShowBeforeStopSaleDays(String organSign) {
        try {
            SysConfigShowCommonDTO showConfig = JSON.parseObject(showBeforeStopSaleDaysConfig, SysConfigShowCommonDTO.class);
            if (showConfig.isAllUserFlag()) {
                return ResultVO.createSuccess(true);
            }
            if (CollectionUtils.isNotEmpty(showConfig.getOrganSigns()) && showConfig.getOrganSigns().contains(organSign)) {
                return ResultVO.createSuccess(true);
            } else {
                return ResultVO.createSuccess(false);
            }
        } catch (Exception e) {
            log.error("isShowBeforeStopSaleDays error", e);
            return ResultVO.createError(e.getMessage());
        }
    }

    @Override
    public ResultVO<Boolean> isShowProductSplitRows(String organSign) {
        try {
            SysConfigShowCommonDTO showConfig = JSON.parseObject(showProductSplitRowsConfig, SysConfigShowCommonDTO.class);
            if (showConfig.isAllUserFlag()) {
                return ResultVO.createSuccess(true);
            }
            if (CollectionUtils.isNotEmpty(showConfig.getOrganSigns()) && showConfig.getOrganSigns().contains(organSign)) {
                return ResultVO.createSuccess(true);
            } else {
                return ResultVO.createSuccess(false);
            }
        } catch (Exception e) {
            log.error("isShowProductSplitRows error", e);
            return ResultVO.createError(e.getMessage());
        }
    }

    @Override
    public ResultVO<Boolean> isShowMsfxConfig(String organSign) {
        SaasDrugstore saasDrugstore = drugstoreMapper.selectDrugstoreByOrganSign(organSign);
        if (null == saasDrugstore){
            return ResultVO.createSuccess(false);
        }
        try {
            String areaCode = saasDrugstore.getAreaCode();
            String cityCode = AreaCodeUtil.getCityCodeString(areaCode);
            String provinceCode = AreaCodeUtil.getProvinceCode(areaCode);
            OpenMsfxSwitchConfig functionConfig = JSON.parseObject(showMsfxConfig, OpenMsfxSwitchConfig.class);
            boolean allUserFlag = functionConfig.isAllUserFlag();
            if (allUserFlag){
                return ResultVO.createSuccess(true);
            }
            List<String> areaCodes = functionConfig.getAreaCodes();

            if (!org.springframework.util.CollectionUtils.isEmpty(areaCodes) && areaCodes.contains(provinceCode)) {
                return ResultVO.createSuccess(true);
            }
            if (!org.springframework.util.CollectionUtils.isEmpty(areaCodes) && areaCodes.contains(cityCode)) {
                return ResultVO.createSuccess(true);
            }
        }catch (Exception e){
            log.error("开启码上放心功能配置解析异常 e ：",e);
        }
        return ResultVO.createSuccess(false);
    }
}
