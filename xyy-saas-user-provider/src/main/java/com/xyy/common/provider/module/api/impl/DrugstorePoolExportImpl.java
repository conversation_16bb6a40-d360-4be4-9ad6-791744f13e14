package com.xyy.common.provider.module.api.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.emule.api.SynchExportDataApi;
import com.xyy.emule.dto.SynchDataParamDto;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.dto.VersionPoolDrugstoreDto;
import com.xyy.user.module.dto.result.QueryDrugstoreDto;
import com.xyy.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

@Service(version = "1.0.0", group = "drugstorePoolExport")
@Slf4j
public class DrugstorePoolExportImpl implements SynchExportDataApi {

    @Autowired
    private DrugstoreApi drugstoreApi;

    @Override
    public ResultVO<PageInfo<JSONObject>> downExportData(SynchDataParamDto synchDataParamDto) {
        log.info("DrugstorePoolExportImpl#downExportData 导出参数:{}", JSON.toJSONString(synchDataParamDto));
        PageInfo<JSONObject> result = new PageInfo<>();
        List<JSONObject> jsonList = new ArrayList<>();

        try {
            VersionPoolDrugstoreDto param = JSONObject.parseObject(synchDataParamDto.getParamInfo(), VersionPoolDrugstoreDto.class);
            PageInfo<QueryDrugstoreDto> queryDrugstoreDtoPageInfo = drugstoreApi.queryVersionPoolDrugstore(param);
            List<QueryDrugstoreDto> lists = queryDrugstoreDtoPageInfo.getList();
            for (QueryDrugstoreDto item: lists) {
                item.setProvince(StringUtil.isEmpty(item.getProvince()) ? "" : item.getProvince() +
                        (StringUtil.isEmpty(item.getCity()) ? "" : item.getCity()) + (StringUtil.isEmpty(item.getArea()) ? "" : item.getArea()));
                jsonList.add(JSON.parseObject(JSON.toJSONString(item)));
            }
        }catch (Exception e){
            log.error("DrugstorePoolExportImpl#downExportData 导出异常:{}",e);
            return ResultVO.createError("系统异常");
        }
        result.setList(jsonList);
        return ResultVO.createSuccess(result);
    }
}
