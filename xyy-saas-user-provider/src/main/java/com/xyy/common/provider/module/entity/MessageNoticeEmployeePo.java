package com.xyy.common.provider.module.entity;

import lombok.Data;

import java.io.Serializable;

@Data
public class MessageNoticeEmployeePo implements Serializable {

    private static final long serialVersionUID = 5306526397722428234L;

    private Long id;

    /**
     * 药店机构标识
     */
    private String organSign;

    /**
     * 消息通知id
     */
    private Long messageNoticeId;

    /**
     * 员工id
     */
    private String employeeId;

    /**
     * 是否删除 1 有效 0 删除
     */
    private Byte yn;

    /**
     * 读取状态 0 未读，1已读
     */
    private Byte state;

    /**
     * 自定义标识 1 自定义 0 非自定义
     */
    private Byte customFlag;

}