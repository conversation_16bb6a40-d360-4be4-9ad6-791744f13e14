package com.xyy.common.provider.module.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class MessageNoticePo implements Serializable {

    private static final long serialVersionUID = -4137893702310635438L;

    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 标签
     */
    private String label;

    /**
     * 定时发布时间
     */
    private Date scheduledReleaseTime;

    /**
     * 分类：（1版本发布，2版本预告，3消息通知，4首页滚动通知，5首页登录弹窗）
     */
    private Byte classification;

    /**
     * 状态：0待发布，1已发布，2已撤销，3发布失败
     */
    private Byte state;


    /**
     * 发布人id
     */
    private String releasePeopleId;


    /**
     * 创建时间
     */
    private Date createTime;


    /**
     * 更新时间
     */
    private Date updateTime;


    /**
     * 是否删除 1 有效 0 删除
     */
    private Byte yn;

    /**
     * 发布时间
     */
    private Date releaseTime;

    /**
     * 创建者id
     */
    private String createUser;


    /**
     * 更新者id
     */
    private String updateUser;


    /**
     * 新增药店机构标识集合
     */
    private List<String> addOrganSign;

    /**
     * 删除药店机构标识集合
     */
    private List<String> delOrganSign;

    /**
     * 选择全部药店  1 全选  0 不全选
     */
    private Integer allDrugstore;

    /**
     * 发布开始时间
     */
    private String startReleaseTime;

    /**
     * 发布结束时间
     */
    private String endReleaseTime;

    /**
     * 员工读取状态： 1 未读 0已读
     */
    private String  readState;

    /**
     * 已发送药店机构标识
     */
    private String drugstoreOrganSign;

    /**
     * 员工id
     */
    private String employeeId;

    /**
     * 过期时间
     */
    private Date expireTime;

    /**
     * 所有的省名
     */
    private String province;

    /**
     * 门店数
     */
    private Integer storeNum;

    /**
     * 区域投放选择的区域
     */
    private String areaCode;

}