package com.xyy.common.provider.module.api.impl;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xyy.common.module.api.SaasBusinessScopeApi;
import com.xyy.common.module.dto.SaasBusinessScopeDto;
import com.xyy.common.module.dto.SaasBusinessScopeListVO;
import com.xyy.common.module.dto.SaasBusinessScopeVO;
import com.xyy.common.module.dto.SystemConfigDto;
import com.xyy.common.provider.common.constant.RedisConstants;
import com.xyy.common.provider.common.constant.SystemConstans;
import com.xyy.common.provider.common.util.SendMqMsgUtils;
import com.xyy.common.provider.module.entity.SaasBusinessScopePo;
import com.xyy.common.provider.module.service.SaasBusinessScopeService;
import com.xyy.common.provider.module.service.SysConfigService;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.dto.restructure.*;
import com.xyy.user.module.enums.*;
import com.xyy.user.module.vo.ImgUrlVo;
import com.xyy.user.module.vo.QualificationInfoVo;
import com.xyy.user.provider.module.dao.SaasImgUrlMapper;
import com.xyy.user.provider.module.dao.SaasPurchaserQualificationMapper;
import com.xyy.user.provider.module.dto.DeletePurchaserQualificationDTO;
import com.xyy.user.provider.module.entity.SaasImgUrl;
import com.xyy.user.provider.module.entity.SaasPurchaserBusinessScope;
import com.xyy.user.provider.module.entity.SaasPurchaserQualification;
import com.xyy.user.provider.module.service.CommonSystemDictService;
import com.xyy.user.provider.module.service.PurchaserService;
import com.xyy.user.provider.module.service.SaasImgUrlService;
import com.xyy.user.provider.module.utils.DateUtils;
import com.xyy.user.provider.module.vo.DeleteBusinessScopeVo;
import com.xyy.user.provider.module.vo.DeleteImageUrlVo;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.ParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service(version = "0.0.1")
public class SaasBusinessScopeApiImpl implements SaasBusinessScopeApi {

    private static final Logger logger = LoggerFactory.getLogger(SaasBusinessScopeApiImpl.class);

    @Autowired
    private SaasBusinessScopeService saasBusinessScopeService;

    @Autowired
    private SysConfigService sysConfigService;

    @Autowired
    private SendMqMsgUtils sendMqMsgUtils;

    @Autowired
    public DrugstoreApi drugstoreApi;

    @Autowired
    protected SaasImgUrlMapper saasImgUrlMapper;

    @Autowired
    protected SaasImgUrlService saasImgUrlService;

    @Autowired
    private SaasPurchaserQualificationMapper saasPurchaserQualificationMapper;

    @Autowired
    private CommonSystemDictService commonSystemDictService;

    @Autowired
    private PurchaserService purchaserService;

    @Override
    public List<SaasBusinessScopeVO> selectSaasBusinessScopeList(SaasBusinessScopeDto saasBusinessScopeDto) {
        SaaSDrugstoreDto drugstore = drugstoreApi.getDrugstoreByOrganSign(saasBusinessScopeDto.getOrganSign());
        if (BizModelEnum.CHAIN_STORE.toEquals(drugstore.getBizModel())){
            saasBusinessScopeDto.setChainStoreFlag(true);
        }

        List<SaasBusinessScopePo> saasBusinessScopeDtos = saasBusinessScopeService.selectSaasBusinessScopeList(saasBusinessScopeDto);
        //如果没查到数据，初始化
        if (saasBusinessScopeDtos == null || saasBusinessScopeDtos.size() == 0) {
            saasBusinessScopeService.initData(saasBusinessScopeDto);
            sendMqMsgUtils.pushMessToMQChain(saasBusinessScopeDto.getOrganSign(), SystemConstans.TABLE_NAME_SAAS_BUSINESS_SCOPE);
        }
        saasBusinessScopeDtos = saasBusinessScopeService.selectSaasBusinessScopeList(saasBusinessScopeDto);
        List<SaasBusinessScopeVO> vos = new ArrayList<>();
        saasBusinessScopeDtos.forEach(item -> {
            SaasBusinessScopeVO vo = new SaasBusinessScopeVO();

            BeanUtils.copyProperties(item, vo);

            vos.add(vo);
        });

        return vos;
    }

    @Override
    public SaasBusinessScopeVO selectSaasBusinessScope(SaasBusinessScopeDto saasBusinessScopeDto) {

        SaasBusinessScopePo saasBusinessScopeDtos = saasBusinessScopeService.selectSaasBusinessScope(saasBusinessScopeDto);

        SaasBusinessScopeVO vo = new SaasBusinessScopeVO();

        BeanUtils.copyProperties(saasBusinessScopeDtos, vo);

        return vo;
    }

    @Override
    public int updateSaasBusinessScope(SaasBusinessScopeDto saasBusinessScopeDto) {
        int row = saasBusinessScopeService.updateSaasBusinessScope(saasBusinessScopeDto);
        sendMqMsgUtils.pushMessToMQ(saasBusinessScopeDto.getOrganSign(), SystemConstans.TABLE_NAME_SAAS_BUSINESS_SCOPE);
        return row;
    }

    @Override
    public SaasBusinessScopeListVO selectChainBusinessScope(SaasBusinessScopeDto saasBusinessScopeDto) {
        SaasBusinessScopeListVO saasBusinessScopeListVO = new SaasBusinessScopeListVO();

            List<SaasBusinessScopePo> saasBusinessScopeDtos = saasBusinessScopeService.selectSaasBusinessScopeList(saasBusinessScopeDto);
            //如果没查到数据，初始化
            if (saasBusinessScopeDtos == null || saasBusinessScopeDtos.size() == 0) {
                saasBusinessScopeService.initData(saasBusinessScopeDto);
                sendMqMsgUtils.pushMessToMQChain(saasBusinessScopeDto.getOrganSign(), SystemConstans.TABLE_NAME_SAAS_BUSINESS_SCOPE);
            }
            saasBusinessScopeDtos = saasBusinessScopeService.selectSaasBusinessScopeList(saasBusinessScopeDto);
            List<SaasBusinessScopeVO> vos = new ArrayList<>();
            saasBusinessScopeDtos.forEach(item -> {
                SaasBusinessScopeVO vo = new SaasBusinessScopeVO();

                BeanUtils.copyProperties(item, vo);

                vos.add(vo);
            });
            saasBusinessScopeListVO.setList(vos);

        return saasBusinessScopeListVO;
    }

    @Override
    public List<SaasBusinessScopeVO> syncData(Map<String, Object> paramMap) {
        logger.info("SaasBusinessScopeApi#syncData param:{}",JSON.toJSONString(paramMap));
        SaasBusinessScopeDto dto = new SaasBusinessScopeDto();
        String baseVersion = String.valueOf(paramMap.get("baseVersion"));
        String organSign = (String) paramMap.get("organSign");
        String count = String.valueOf(paramMap.get("count"));
        Integer syncCount = SystemConstans.SYNC_COUNT;
        if (!StringUtils.isEmpty(count)) {
            syncCount = Integer.valueOf(count);
        }
        // 如果是门店获取对应的总部机构号
//        String headquartersOrg = getHeadquartersOrg(organSign);
//        if (!org.springframework.util.StringUtils.isEmpty(headquartersOrg)) {
//            logger.info("经营范围同步:当前机构为门店机构 organSign:{},同步的总部机构为 headquartersOrg:{}",organSign,headquartersOrg);
//            organSign = headquartersOrg;
//        }
        dto.setBaseVersion(baseVersion);
        dto.setOrganSign(organSign);
        dto.setCount(syncCount);
        List<SaasBusinessScopePo> saasBusinessScopeDtos = saasBusinessScopeService.syncData(dto);
        List<SaasBusinessScopeVO> vos = new ArrayList<>();

        //判断资质的启用状态
        QueryPurchaserDetailDto detailDto = new QueryPurchaserDetailDto();
        detailDto.setPurchaserNo(organSign);
        ResultVO<SaasPurchaserDto> purchaser = purchaserService.getPurchaser(detailDto);
        Boolean enableFlag = true;
        if (null != purchaser.getResult() && PurchaserUsageStatusEnum.DISABLE.getKey() == purchaser.getResult().getStatus()){
            enableFlag = false;
        }
        for (SaasBusinessScopePo item:saasBusinessScopeDtos) {
            SaasBusinessScopeVO vo = new SaasBusinessScopeVO();

            BeanUtils.copyProperties(item, vo);
            //如果资质状态为禁用,则直接算所有经营范围未启用
            if (!enableFlag){
                vo.setStatus(SystemConstans.BUSINESS_SCOPE_YN_NO);
            }
            vos.add(vo);
        }
        logger.info("数据同步返回：" + JSONObject.toJSONString(vos));
        return vos;
    }

    /**
     * 如果是门店机构号，则获取对应的总部机构号
     *
     * @param organSign
     * @return
     */
    private String getHeadquartersOrg(String organSign) {
        String headquartersOrg = "";
        SaaSDrugstoreDto drugstore = drugstoreApi.getDrugstoreByOrganSign(organSign);
        // 兼容连锁门店，根据连锁门店获取总部的机构号
        if (drugstore != null && drugstore.getBizModel() == DrugstoreBizModelEnum.CHAIN_STORE.getKey()
                && drugstore.getOrganSignType() == DrugstoreTypeEnum.DRUGSTORE.getKey()) {
            headquartersOrg = drugstore.getHeadquartersOrganSign();
        }
        return headquartersOrg;
    }

    @Override
    public int updateEnableYn(List<SaasBusinessScopeDto> saasBusinessScopeDto, String organSign) {
        int row = saasBusinessScopeService.updateEnableYn(saasBusinessScopeDto, organSign);
        logger.info("MQ:" + organSign);
        sendMqMsgUtils.pushMessToMQ(organSign, SystemConstans.TABLE_NAME_SAAS_BUSINESS_SCOPE);
        return row;
    }

    @Override
    public ResultVO<List<QualificationInfoVo>> selectNBusinessScopeNew(SaasBusinessScopeDto saasBusinessScopeDto) {
        saasBusinessScopeDto.setEnableYn((byte) 1);
        return saasBusinessScopeService.selectNBusinessScopeNew(saasBusinessScopeDto);
    }

    @Override
    public ResultVO saveOrUpdateBusinessScope(List<QualificationInfoVo> params,Long employeeId,String organSign) {
        try {
            if (CollectionUtils.isNotEmpty(params) && !StringUtils.isEmpty(organSign)) {
                //筛选出需要删除的数据,把门店的数据删除
                //filteDelete(organSign,params);
                List<SaasPurchaserQualification> qualificationList = new ArrayList<>();
                ///删除总部数据
                deleteImg(organSign);
                List<Integer> businessScopeIds = new ArrayList<>();
                for (QualificationInfoVo qualificationInfoVo : params) {
                    //过滤不需要加资质信息的
                    if (!inCerType(qualificationInfoVo.getCertificateType())){
                        SaasPurchaserQualification qualification = this.convertToQualification(qualificationInfoVo, organSign);
                        qualificationList.add(qualification);
                    }

                    saveImgs(qualificationInfoVo, employeeId, organSign);
                    businessScopeIds.addAll(qualificationInfoVo.getBusinessScopeIds());
                }
                saveScopes(businessScopeIds, employeeId, organSign);
                DeletePurchaserQualificationDTO delete = new DeletePurchaserQualificationDTO();
                delete.setPurchaserNo(organSign);
                saasPurchaserQualificationMapper.delByPurchaserNo(delete);
                saasPurchaserQualificationMapper.batchInsert(qualificationList);
                //发消息通知POS同步数据
                sendMqMsgUtils.pushMessToMQ(organSign, SystemConstans.TABLE_NAME_SAAS_BUSINESS_SCOPE);
                return ResultVO.createSuccess();
            } else {
                return ResultVO.createError("未读取到参数");
            }
        }catch (Exception e){
            logger.error("SaasBusinessScopeApi#saveOrUpdateBusinessScope error e:{}",e);
            return ResultVO.createError("系统异常");
        }
    }

    private void filteDelete(String organSign,List<QualificationInfoVo> newBS){
        SaasBusinessScopeDto queryDto = new SaasBusinessScopeDto();
        queryDto.setOrganSign(organSign);
        queryDto.setEnableYn((byte) 1);
        List<SaasBusinessScopePo> headBS = saasBusinessScopeService.selectSaasBusinessScopeList(queryDto);
        List<Integer> headDict = headBS.stream().map(item -> item.getDictId()).collect(Collectors.toList());

        List<Integer> subDicts = new ArrayList<>();
        Set<Integer> deleteIds = new HashSet<>();
        for (QualificationInfoVo qualificationInfoVo:newBS) {
            subDicts.addAll(qualificationInfoVo.getBusinessScopeIds());
        }
        Map<Integer, Integer> subBS = subDicts.stream().collect(Collectors.toMap(Integer::intValue, Function.identity(),(key1,key2)->key2));
        headDict.forEach(item->{
            if (null == subBS.get(item)){
                deleteIds.add(item);
            }
        });

        if (CollectionUtils.isNotEmpty(deleteIds)) {
            List<SaaSDrugstoreDto> drugstore = drugstoreApi.getDrugstoreByHeadquartersOrganSign(organSign);
            List<String> organSigns = drugstore.stream().map(SaaSDrugstoreDto::getOrganSign).collect(Collectors.toList());
            List<SaasBusinessScopeDto> params = new ArrayList<>();
            for (Integer id:deleteIds) {
                SaasBusinessScopeDto param = new SaasBusinessScopeDto();
                param.setEnableYn((byte) 0);
                param.setUpdateUser("head");
                param.setDictId(id);
                params.add(param);
            }
            for (String subOrganSign:organSigns) {
                saasBusinessScopeService.updateEnableYnWithDict(params,subOrganSign);

                //发消息通知POS同步数据
                sendMqMsgUtils.pushMessToMQ(subOrganSign, SystemConstans.TABLE_NAME_SAAS_BUSINESS_SCOPE);
            }
        }

    }

    private void deleteImg(String organSign){
        DeleteImageUrlVo imageUrlVo = new DeleteImageUrlVo();
        imageUrlVo.setOrganSign(organSign);
        List<Integer> purchaserTypeList = ImageTypeEnum.getPurchaserTypeList();
        imageUrlVo.setTypeList(purchaserTypeList);
        saasImgUrlMapper.deleteImageUrlByOrganSign(imageUrlVo);
    }

    private SaasPurchaserQualification convertToQualification(QualificationInfoVo qualificationInfoVo,String organSign) throws ParseException {
        SaasPurchaserQualification qualification = saasPurchaserQualificationMapper.selectByNoAndType(organSign, qualificationInfoVo.getCertificateType().byteValue());

        if (null == qualification){
            qualification = new SaasPurchaserQualification();
            qualification.setCreateTime(new Date());
        }else {
            qualification.setCreateTime(qualification.getCreateTime());
        }

        qualification.setCertificateDate(StringUtils.isEmpty(qualificationInfoVo.getCertificateDate())? null:DateUtils.dateFormat(qualificationInfoVo.getCertificateDate()));
        qualification.setCertificateNo(qualificationInfoVo.getCertificateNo());
        qualification.setCertificateType(qualificationInfoVo.getCertificateType());
        qualification.setExpiryDateDate(DateUtils.dateFormat(qualificationInfoVo.getExpiryDateDate()));
        qualification.setPurchaserNo(organSign);
        qualification.setUpdateTime(new Date());

        return qualification;
    }

    private void saveImgs(QualificationInfoVo qualificationInfoVo,Long employeeId,String organSign){
        if (employeeId != null){
            List<SaasImgUrl> saasImgUrlList = convertImg(qualificationInfoVo,organSign);
            if (CollectionUtils.isNotEmpty(saasImgUrlList)) {
                saasImgUrlMapper.batchInsert(saasImgUrlList);
            }
        }else if(employeeId == null){
            List<ImgUrlVo> imgUrls = qualificationInfoVo.getImgUrls();
            if (!CollectionUtils.isNotEmpty(imgUrls)){
                for (ImgUrlVo imgUrlVo : imgUrls) {
                    Integer del = imgUrlVo.getDel();
                    Long id = imgUrlVo.getId();
                    if (del != null && del == 0){
                        SaasImgUrl saasImgUrl = saasImgUrlService.buildSaasImageUrl(organSign, imgUrlVo.getType(), imgUrlVo.getUrl());
                        saasImgUrlMapper.insert(saasImgUrl);
                    }else {
                        if (null != id){
                            saasImgUrlMapper.deleteByPrimaryKey(id);
                        }
                    }
                }
            }
        }
    }

    //逻辑删除
    private void saveScopes(List<Integer> businessScope,Long employeeId,String organSign){

        //将以前的数据与现在的数据做对比,得出修改的数据
        saasBusinessScopeService.updateEnableYnByDict(businessScope,organSign,employeeId);
    }



    private List<SaasImgUrl> convertImg(QualificationInfoVo qualificationInfoVo,String organSign){
        List<ImgUrlVo> imgUrlVos = qualificationInfoVo.getImgUrls();
        if (CollectionUtils.isNotEmpty(imgUrlVos)){
            List<SaasImgUrl> saasImgUrlList = new ArrayList<>();
            for (ImgUrlVo vo:imgUrlVos) {
                SaasImgUrl saasImgUrl = saasImgUrlService.buildSaasImageUrl(organSign, vo.getType(), vo.getUrl());
                saasImgUrlList.add(saasImgUrl);
            }
            return saasImgUrlList;
        }else {
            return null;
        }
    }

    //判断是否是可以修改的
    private Boolean inCerType(Integer cerType){
        if (CertificateTypeEnum.ACCOUNT_OPENING_PERMIT.toEquals(cerType) || CertificateTypeEnum.OTHER.toEquals(cerType)){
            return true;
        }else {
            return false;
        }
    }

    @Override
    public SaasBusinessScopeListVO selectBusinessScope(SaasBusinessScopeDto saasBusinessScopeDto) {
        SaasBusinessScopeListVO saasBusinessScopeListVO = new SaasBusinessScopeListVO();

        SystemConfigDto systemConfigDto = sysConfigService.querySystemConfig(saasBusinessScopeDto.getOrganSign());
        saasBusinessScopeListVO.setBusinessScopeYn(systemConfigDto.getBusinessScopeYn());
        if (systemConfigDto.getBusinessScopeYn().equals(SystemConstans.BUSINESS_SCOPE_YN_YES)) {
            List<SaasBusinessScopePo> saasBusinessScopeDtos = saasBusinessScopeService.selectSaasBusinessScopeList(saasBusinessScopeDto);
            //如果没查到数据，初始化
            if (saasBusinessScopeDtos == null || saasBusinessScopeDtos.size() == 0) {
                saasBusinessScopeService.initData(saasBusinessScopeDto);
                sendMqMsgUtils.pushMessToMQ(saasBusinessScopeDto.getOrganSign(), SystemConstans.TABLE_NAME_SAAS_BUSINESS_SCOPE);
            }
            saasBusinessScopeDtos = saasBusinessScopeService.selectSaasBusinessScopeList(saasBusinessScopeDto);
            List<SaasBusinessScopeVO> vos = new ArrayList<>();
            saasBusinessScopeDtos.forEach(item -> {
                SaasBusinessScopeVO vo = new SaasBusinessScopeVO();

                BeanUtils.copyProperties(item, vo);

                vos.add(vo);
            });
            saasBusinessScopeListVO.setList(vos);
        }
        return saasBusinessScopeListVO;
    }

    @Override
    public Map<Integer, Boolean> selectInBizScopeFlagMap(List<Integer> businessScopeList, String organSign) {
        //判断资质的启用状态
        QueryPurchaserDetailDto detailDto = new QueryPurchaserDetailDto();
        detailDto.setPurchaserNo(organSign);
        ResultVO<SaasPurchaserDto> purchaser = purchaserService.getPurchaser(detailDto);
        if (null != purchaser.getResult() && PurchaserUsageStatusEnum.DISABLE.getKey() == purchaser.getResult().getStatus()){
            logger.info("SaasBusinessScopeApiImpl#selectInBizScopeFlagMap 资质不是启用状态,organSign:{}",organSign);
            Map<Integer, Boolean> result = new HashMap<>();
            for (Integer item:businessScopeList) {
                result.put(item,false);
            }
            return result;
        }
        return saasBusinessScopeService.selectInBizScopeFlagMap(businessScopeList, organSign);
    }

    @Override
    public Map<String, Integer> getNameAndId(String organSign) {
        return saasBusinessScopeService.getNameAndId(organSign);
    }

    @Override
    public void RefreshBusinessScopeRepeate(String organSign) {
        Long start = System.currentTimeMillis();
        logger.info("SaasBusinessScopeApiImpl#RefreshBusinessScopeRepeate start..... organSign:{}",organSign);
        if (StringUtils.isEmpty(organSign)){
            SaaSDrugstoreDto query = new SaaSDrugstoreDto();
            query.setBizModel(BizModelEnum.CHAIN_STORE.getKey());
            List<SaaSDrugstoreDto> condition = drugstoreApi.getSaaSDrugstoreByCondition(query);
            for (SaaSDrugstoreDto drugstore:condition) {
                saasBusinessScopeService.RefreshBusinessScopeRepeate(drugstore.getOrganSign());
            }
        }else {
            SaaSDrugstoreDto drugstoreDto = drugstoreApi.getDrugstoreByOrganSign(organSign);
            if (OrganSignTypeEnum.HEADQUARTERS.toEquals(drugstoreDto.getOrganSignType())){
                List<SaaSDrugstoreDto> condition = drugstoreApi.getDrugstoreByHeadquartersOrganSign(drugstoreDto.getOrganSign());
                for (SaaSDrugstoreDto drugstore:condition) {
                    saasBusinessScopeService.RefreshBusinessScopeRepeate(drugstore.getOrganSign());
                }
                saasBusinessScopeService.RefreshBusinessScopeRepeate(organSign);
            }else {
                saasBusinessScopeService.RefreshBusinessScopeRepeate(organSign);
            }
        }
        Long end = System.currentTimeMillis();
        logger.info("SaasBusinessScopeApiImpl#RefreshBusinessScopeRepeate end 耗时:{}秒",(end - start)/1000);
    }

    @Override
    public void RefreshType(String organSign){
        Long start = System.currentTimeMillis();
        logger.info("SaasBusinessScopeApiImpl#RefreshType start..... organSign:{}",organSign);
        if (StringUtils.isEmpty(organSign)){
            SaaSDrugstoreDto query = new SaaSDrugstoreDto();
            query.setBizModel(BizModelEnum.CHAIN_STORE.getKey());
            List<SaaSDrugstoreDto> condition = drugstoreApi.getSaaSDrugstoreByCondition(query);
            for (SaaSDrugstoreDto drugstore:condition) {
                saasBusinessScopeService.RefreshType(drugstore.getOrganSign());
            }
        }else {
            saasBusinessScopeService.RefreshType(organSign);
        }
        Long end = System.currentTimeMillis();
        logger.info("SaasBusinessScopeApiImpl#RefreshType end 耗时:{}秒",(end - start)/1000);
    }

    @Override
    public void RefreshNotInHead(String organSign){
        Long start = System.currentTimeMillis();
        logger.info("SaasBusinessScopeApiImpl#RefreshNotInHead start..... organSign:{}",organSign);
        List<SaaSDrugstoreDto> headOrgan = null;
        if (StringUtils.isEmpty(organSign)){
            SaaSDrugstoreDto query = new SaaSDrugstoreDto();
            query.setBizModel(BizModelEnum.CHAIN_STORE.getKey());
            query.setOrganSignType((byte) OrganSignTypeEnum.HEADQUARTERS.getKey());
            headOrgan = drugstoreApi.getSaaSDrugstoreByCondition(query);
        }else {
            headOrgan = new ArrayList<>();
            SaaSDrugstoreDto single = new SaaSDrugstoreDto();
            single.setOrganSign(organSign);
            headOrgan.add(single);
        }

        if (CollectionUtils.isNotEmpty(headOrgan)){
            for (SaaSDrugstoreDto head:headOrgan) {
                List<SaaSDrugstoreDto> subOrgans = drugstoreApi.getDrugstoreByHeadquartersOrganSign(head.getOrganSign());
                SaasBusinessScopeDto queryDto = new SaasBusinessScopeDto();
                queryDto.setOrganSign(head.getOrganSign());
                queryDto.setEnableYn((byte) 1);
                if (CollectionUtils.isEmpty(subOrgans)){
                    subOrgans.add(headOrgan.get(0));
                    SaaSDrugstoreDto byOrganSign = drugstoreApi.getDrugstoreByOrganSign(head.getOrganSign());
                    queryDto.setOrganSign(byOrganSign.getHeadquartersOrganSign());
                }
                List<SaasBusinessScopePo> headBS = saasBusinessScopeService.selectSaasBusinessScopeList(queryDto);
                if (CollectionUtils.isNotEmpty(headBS) && CollectionUtils.isNotEmpty(subOrgans)) {
                    List<SaasBusinessScopeDto> updateParam = new ArrayList<>();
                    Map<Integer, Integer> headMap = headBS.stream().collect(Collectors.toMap(SaasBusinessScopePo::getDictId,SaasBusinessScopePo::getDictId,(k1,k2)->k2));
                    for (SaaSDrugstoreDto subOrgan : subOrgans) {
                        logger.info("SaasBusinessScopeApiImpl#RefreshNotInHead now organ:{}", subOrgan.getOrganSign());
                        queryDto.setOrganSign(subOrgan.getOrganSign());
                        List<SaasBusinessScopePo> subBSs = saasBusinessScopeService.selectSaasBusinessScopeList(queryDto);
                        if (CollectionUtils.isNotEmpty(subBSs)) {
                            for (SaasBusinessScopePo subBS : subBSs) {
                                if (null == headMap.get(subBS.getDictId())) {
                                    SaasBusinessScopeDto item = new SaasBusinessScopeDto();
                                    item.setId(subBS.getId());
                                    item.setUpdateUser(StringUtils.isEmpty(subBS.getUpdateUser())? "system":subBS.getUpdateUser());
                                    item.setEnableYn((byte) 0);
                                    updateParam.add(item);
                                }
                            }
                            if (CollectionUtils.isNotEmpty(updateParam)) {
                                saasBusinessScopeService.updateEnableYn(updateParam, subOrgan.getOrganSign());
                                sendMqMsgUtils.pushMessToMQ(subOrgan.getOrganSign(), SystemConstans.TABLE_NAME_SAAS_BUSINESS_SCOPE);
                            }
                        }
                    }
                }
            }
        }
        Long end = System.currentTimeMillis();
        logger.info("SaasBusinessScopeApiImpl#RefreshNotInHead end 耗时:{}秒",(end - start)/1000);
    }

    //刷自定义经营范围
    @Override
    public void RefreshCustom(String organSign){
        Long start = System.currentTimeMillis();
        logger.info("SaasBusinessScopeApiImpl#RefreshCustom start..... organSign:{}",organSign);
        List<SaaSDrugstoreDto> headOrgan = null;
        if (StringUtils.isEmpty(organSign)){
            SaaSDrugstoreDto query = new SaaSDrugstoreDto();
            query.setBizModel(BizModelEnum.CHAIN_STORE.getKey());
            query.setOrganSignType((byte) OrganSignTypeEnum.HEADQUARTERS.getKey());
            headOrgan = drugstoreApi.getSaaSDrugstoreByCondition(query);
        }else {
            headOrgan = new ArrayList<>();
            SaaSDrugstoreDto single = new SaaSDrugstoreDto();
            single.setOrganSign(organSign);
            headOrgan.add(single);
        }

        if (CollectionUtils.isNotEmpty(headOrgan)){
            for (SaaSDrugstoreDto dto:headOrgan) {
                logger.info("SaasBusinessScopeApiImpl#RefreshCustom organSign:{}",dto.getOrganSign());
                //查询字典表的自定义经营范围
                List<Long> customBS = commonSystemDictService.getCustomBS(dto.getOrganSign());
                if (CollectionUtils.isNotEmpty(customBS)) {
                    List<SaasBusinessScopePo> scopeByOrgan = saasBusinessScopeService.getBusinessScopeByOrgan(dto.getOrganSign());
                    Map<Integer, Integer> dictIdMap = scopeByOrgan.stream().collect(Collectors.toMap(SaasBusinessScopePo::getDictId,SaasBusinessScopePo::getId));
                    List<Long> update = new ArrayList<>();
                    List<Integer> insert = new ArrayList<>();
                    for (Long dictId:customBS) {
                        if (null != dictIdMap.get(dictId.intValue())){
                            update.add(dictId);
                        }else {
                            insert.add(dictId.intValue());
                        }
                    }

                    if (CollectionUtils.isNotEmpty(update)) {
                        //更新business_scope表的cerType
                        saasBusinessScopeService.RefreshCustomBS(dto.getOrganSign(), customBS);
                    }

                    if (CollectionUtils.isNotEmpty(insert)){
                        saasBusinessScopeService.chainInsert(insert,null,dto.getOrganSign(),15);
                        sendMqMsgUtils.pushMessToMQ(dto.getOrganSign(), SystemConstans.TABLE_NAME_SAAS_BUSINESS_SCOPE);
                    }
                }
            }
        }

        Long end = System.currentTimeMillis();
        logger.info("SaasBusinessScopeApiImpl#RefreshCustom end 耗时:{}秒",(end - start)/1000);
    }

    @Override
    public void initBS(String organSign){
        Long start = System.currentTimeMillis();
        logger.info("SaasBusinessScopeApiImpl#initBS start..... organSign:{}",organSign);
        List<SaaSDrugstoreDto> headOrgan = null;
        if (StringUtils.isEmpty(organSign)){
            SaaSDrugstoreDto query = new SaaSDrugstoreDto();
            query.setBizModel(BizModelEnum.CHAIN_STORE.getKey());
            query.setOrganSignType((byte) OrganSignTypeEnum.HEADQUARTERS.getKey());
            headOrgan = drugstoreApi.getSaaSDrugstoreByCondition(query);
        }else {
            headOrgan = new ArrayList<>();
            SaaSDrugstoreDto single = new SaaSDrugstoreDto();
            single.setOrganSign(organSign);
            headOrgan.add(single);
        }

        for (SaaSDrugstoreDto drugstoreDto:headOrgan) {
            SaasBusinessScopeDto param = new SaasBusinessScopeDto();
            param.setOrganSign(drugstoreDto.getOrganSign());
            saasBusinessScopeService.RefreshinitData(param,null);
        }

        Long end = System.currentTimeMillis();
        logger.info("SaasBusinessScopeApiImpl#initBS end 耗时:{}秒",(end - start)/1000);
    }

    @Override
    public void RefreshParent(String organSign,String posSend){
        Long start = System.currentTimeMillis();
        logger.info("SaasBusinessScopeApiImpl#RefreshParent start..... organSign:{}",organSign);
        List<SaaSDrugstoreDto> headOrgan = null;
        if (StringUtils.isEmpty(organSign)){
            SaaSDrugstoreDto query = new SaaSDrugstoreDto();
            query.setBizModel(BizModelEnum.CHAIN_STORE.getKey());
            headOrgan = drugstoreApi.getSaaSDrugstoreByCondition(query);
        }else {
            headOrgan = new ArrayList<>();
            SaaSDrugstoreDto drugstore = drugstoreApi.getDrugstoreByOrganSign(organSign);
            if (OrganSignTypeEnum.HEADQUARTERS.toEquals(drugstore.getOrganSignType())){
                List<SaaSDrugstoreDto> drugstoreDtos = drugstoreApi.getDrugstoreByHeadquartersOrganSign(drugstore.getOrganSign());
                if (CollectionUtils.isNotEmpty(drugstoreDtos)) {
                    headOrgan = drugstoreDtos;
                }
            }

            SaaSDrugstoreDto single = new SaaSDrugstoreDto();
            single.setOrganSign(organSign);
            headOrgan.add(single);
        }

        List<String> organSigns = headOrgan.stream().map(SaaSDrugstoreDto::getOrganSign).collect(Collectors.toList());
        saasBusinessScopeService.refreshParent(organSigns);
        if (null != posSend && "yes".equals(posSend)){
            for (String item:organSigns) {
                sendMqMsgUtils.pushMessToMQ(item, SystemConstans.TABLE_NAME_SAAS_BUSINESS_SCOPE);
            }
        }
        Long end = System.currentTimeMillis();
        logger.info("SaasBusinessScopeApiImpl#RefreshParent end 耗时:{}秒",(end - start)/1000);
    }
}
