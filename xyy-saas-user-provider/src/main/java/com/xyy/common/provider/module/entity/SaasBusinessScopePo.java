package com.xyy.common.provider.module.entity;

import lombok.Data;

import java.util.Date;

@Data
public class SaasBusinessScopePo {

    private Integer id;

    /**
     * saas_system_dict表的主键
     */
    private Integer dictId;

    /**
     * 经营范围名称
     */
    private String name;

    /**
     * 经营范围排序字段
     */
    private Integer sort;

    /**
     * 是否删除 1 有效、0 删除
     */
    private Byte yn;

    /**
     * 是否禁用 1 有效、0 禁用
     */
    private Byte status;

    /**
     * 数据同步版本号
     */
    private String baseVersion;

    /**
     * 药店机构标识
     */
    private String organSign;

    /**
     * 资质类型
     */
    private Integer certificateType;

    /**
     * 是否启用 1 是、0 否
     */
    private Byte enableYn;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createUser;


    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 修改人
     */
    private String updateUser;
}
