package com.xyy.common.provider.module.service;


import com.github.pagehelper.PageInfo;
import com.xyy.common.module.dto.MessageNoticeDto;
import com.xyy.common.provider.module.entity.MessageNoticePo;

import java.util.List;

public interface MessageNoticeService {

    Boolean insert(MessageNoticePo messageNoticePo);

    Boolean update(MessageNoticePo messageNoticePo);

    MessageNoticeDto selectById(Long id);

    Boolean updateState(MessageNoticePo messageNoticePo);

    PageInfo getMessageNoticeListPager(PageInfo pageInfo, MessageNoticePo bean);

    PageInfo getMessageNoticeByEmployeeIdPager(PageInfo pageInfo, MessageNoticeDto bean);

    Boolean updateMessageNoticeByEmployeeId(String organSign, String employeeId, List<Long> messageNoticeIds);

    PageInfo getMessageNoticeDrugstoreListPager(PageInfo pageInfo, Long messageNoticeId, Integer customFlag);

    List<String> selectReleasePeopleNameList();

    List<String> getMessageNoticeDrugstoreList(Long messageNoticeId);

    /**
     * 查询最新一条的弹窗和滚动消息
     */
    MessageNoticeDto getLatestMessge(MessageNoticeDto bean);

}
