package com.xyy.common.provider.module.api.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.xyy.common.module.api.SaasDisableBusinessApi;
import com.xyy.common.module.dto.SaasDisableBusinessDto;
import com.xyy.common.provider.module.entity.SaasDisableBusinessPo;
import com.xyy.common.provider.module.service.SaasDisbleBusinessService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/1/12 17:24
 * @Version 1.0
 * 红尘轮回众生顾,因果循环有定数
 * 放下屠刀虽成佛,愿坠三途灭千魔
 */
@Service(version = "0.0.1")
public class SaasDisableBusinessApiImpl implements SaasDisableBusinessApi {

    @Autowired
    private SaasDisbleBusinessService saasDisbleBusinessService;

    @Override
    public List<SaasDisableBusinessDto> getDisableBusinessByOrgan(SaasDisableBusinessDto dto) {
        List<SaasDisableBusinessPo> pos = saasDisbleBusinessService.queryByOrgan(dto.getOrganSign());
        return convertPoToDto(pos);
    }

    @Override
    public Integer updateDisableBusiness(List<Long> businessIds, SaasDisableBusinessDto dto) {
        String organSign = dto.getOrganSign();
        String employeeId = dto.getUpdateUser();
        Integer count = 0;
        if (StringUtils.isNotEmpty(organSign)) {
            saasDisbleBusinessService.deleteByOrgan(organSign);

            if (CollectionUtils.isNotEmpty(businessIds)){
                List<SaasDisableBusinessPo> pos = new ArrayList<>();
                for (Long businessId:businessIds) {
                    SaasDisableBusinessPo po = new SaasDisableBusinessPo();
                    po.setOrganSign(organSign);
                    po.setBusinessScope(businessId);
                    po.setUpdateUser(employeeId);
                    po.setCreateTime(new Date());
                    po.setUpdateTime(new Date());
                    pos.add(po);
                }
                count = saasDisbleBusinessService.insertOrUpdateBatch(pos);
            }
        }
        return count;
    }

    private List<SaasDisableBusinessDto> convertPoToDto(List<SaasDisableBusinessPo> pos){
        List<SaasDisableBusinessDto> dtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pos)){
            for (SaasDisableBusinessPo po:pos) {
                SaasDisableBusinessDto dto = new SaasDisableBusinessDto();
                BeanUtils.copyProperties(po,dto);
                dtos.add(dto);
            }
        }

        return dtos;
    }
}
