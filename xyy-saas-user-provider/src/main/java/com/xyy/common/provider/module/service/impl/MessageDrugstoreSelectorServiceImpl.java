package com.xyy.common.provider.module.service.impl;

import com.xyy.common.provider.module.dao.MessageNoticeEmployeeMapper;
import com.xyy.common.provider.module.service.MessageDrugstoreSelectorService;
import com.xyy.saas.user.center.api.pojo.request.QueryListDrugAreaDTO;
import com.xyy.saas.user.center.api.pojo.request.QueryListDrugstoreDTO;
import com.xyy.saas.user.center.api.pojo.response.BaseDrugstoreDTO;
import com.xyy.user.module.dto.base.DrugstoreAreaQueryDto;
import com.xyy.user.module.dto.base.SaasDrugstoreQueryDTO;
import com.xyy.user.module.enums.DrugstoreBizModelEnum;
import com.xyy.user.module.enums.DrugstoreTypeEnum;
import com.xyy.user.provider.center.gray.CenterGrayService;
import com.xyy.user.provider.center.rpc.DrugstoreAdapter;
import com.xyy.user.provider.module.dao.base.BaseDrugstoreMapper;
import com.xyy.user.provider.module.entity.base.SaasDrugstorePo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class MessageDrugstoreSelectorServiceImpl implements MessageDrugstoreSelectorService {

    @Autowired
    private BaseDrugstoreMapper drugstoreMapper;
    @Autowired
    private MessageNoticeEmployeeMapper messageNoticeEmployeeMapper;
    @Autowired
    private CenterGrayService centerGrayService;
    @Autowired
    private DrugstoreAdapter drugstoreAdapter;

    /**
     * 消息id
     */
    private Long messageNoticeId;
    /**
     * 选择药店 使用6位数字 每位代表一个复选框 1: 选中  0: 不选中
     */
    private String allDrugstore;
    /**
     * 已经初始化机构
     */
    private boolean isInitOrganSignList;
    /**
     * 自定义的机构
     */
    private List<String> customOrganSignList = new ArrayList<>();
    /**
     * 新增药店机构标识集合
     */
    private List<String> addOrganSign = new ArrayList<>();
    /**
     * 删除药店机构标识集合
     */
    private List<String> delOrganSign = new ArrayList<>();
    /**
     * 勾选的机构
     */
    private List<String> selectOrganSignList = new ArrayList<>();

    private Map<String, List<String>> groupArea = new HashMap<>();


    /**
     * 单体药店
     */
    private boolean drugstoreChecked;
    /**
     * 连锁总部
     */
    private boolean chainStoreHeadquartersChecked;
    /**
     * 连锁门店
     */
    private boolean chainStoreDrugstoreChecked;
    /**
     * 联营总部
     */
    private boolean jointOperationHeadquartersChecked;
    /**
     * 联营门店
     */
    private boolean jointOperationDrugstoreChecked;
    /**
     * 自定义
     */
    private boolean customChecked;

    private static final char UNCHECKED = '0';
    private static final char CHECKED = '1';

    public MessageDrugstoreSelectorServiceImpl() {
        log.info("MessageDrugstoreSelectorServiceImpl create...");
    }

    @Override
    public void init(Long messageNoticeId, Integer allDrugstore, List<String> customOrganSignList) {
        this.messageNoticeId = messageNoticeId;
        this.allDrugstore = String.format("%06d", allDrugstore);
        if (CollectionUtils.isNotEmpty(customOrganSignList)) {
            this.customOrganSignList = customOrganSignList;
        }
        initChecked();
        initOrganSignList();
    }

    private void initChecked() {
        drugstoreChecked = isChecked(0);
        chainStoreDrugstoreChecked = isChecked(1);
        chainStoreHeadquartersChecked = isChecked(2);
        jointOperationDrugstoreChecked = isChecked(3);
        jointOperationHeadquartersChecked = isChecked(4);
        customChecked = isChecked(5);
    }

    private boolean isChecked(int index) {
        return CHECKED == allDrugstore.charAt(index);
    }

    private void initOrganSignList() {
        if (isInitOrganSignList) {
            return;
        }
        // 自定义
        Set<String> customOrganSignSet;
        if(customChecked) {
            customOrganSignSet = new HashSet<>(customOrganSignList);
            customOrganSignList.clear();
            // 更新
            if (messageNoticeId != null) {
                customOrganSignList = messageNoticeEmployeeMapper.getMessageNoticeDrugstoreList(messageNoticeId, 1);
            }

            addOrganSign = customOrganSignSet.stream()
                    .filter(organSign -> !customOrganSignList.contains(organSign))
                    .collect(Collectors.toList());
            delOrganSign = customOrganSignSet.stream()
                    .filter(organSign -> customOrganSignList.contains(organSign))
                    .collect(Collectors.toList());
            customOrganSignList.addAll(addOrganSign);
            customOrganSignList.removeIf(organSign -> delOrganSign.contains(organSign));
            customOrganSignSet.addAll(addOrganSign);
            customOrganSignSet.removeIf(organSign -> delOrganSign.contains(organSign));
        } else {
            customOrganSignSet = new HashSet<>();
        }
        // 除了自定义的复选框
        if(drugstoreChecked) {
            selectOrganSignList.addAll(listDrugstores(DrugstoreBizModelEnum.DRUGSTORE, DrugstoreTypeEnum.DRUGSTORE));
        }
        if(chainStoreHeadquartersChecked) {
            selectOrganSignList.addAll(listDrugstores(DrugstoreBizModelEnum.CHAIN_STORE, DrugstoreTypeEnum.HEADQUARTERS));
        }
        if(chainStoreDrugstoreChecked) {
            selectOrganSignList.addAll(listDrugstores(DrugstoreBizModelEnum.CHAIN_STORE, DrugstoreTypeEnum.DRUGSTORE));
        }
        if(jointOperationHeadquartersChecked) {
            selectOrganSignList.addAll(listDrugstores(DrugstoreBizModelEnum.JOINT_OPERATION, DrugstoreTypeEnum.HEADQUARTERS));
        }
        if(jointOperationDrugstoreChecked) {
            selectOrganSignList.addAll(listDrugstores(DrugstoreBizModelEnum.JOINT_OPERATION, DrugstoreTypeEnum.DRUGSTORE));
        }
        if (CollectionUtils.isNotEmpty(customOrganSignSet)) {
            // 将自定义中的删除
            selectOrganSignList.removeIf(organSign -> customOrganSignSet.contains(organSign));
        }
        isInitOrganSignList = true;
    }

    private List<String> listDrugstores(DrugstoreBizModelEnum bizModel, DrugstoreTypeEnum organSignType) {
        List<String> organSignTotal = new ArrayList<>();
        for (Map.Entry<String,List<String>> entry:groupArea.entrySet()) {
            if (centerGrayService.useCenterUserQuery()) {
                QueryListDrugAreaDTO queryListDrugstore = new QueryListDrugAreaDTO();
                queryListDrugstore.setOrganSignType((byte) organSignType.getKey());
                queryListDrugstore.setBizModel((byte) bizModel.getKey());
                queryListDrugstore.setStatus((byte) 1);
                if ("province".equals(entry.getKey())){
                    queryListDrugstore.setProvince(entry.getValue());
                }
                if ("city".equals(entry.getKey())){
                    queryListDrugstore.setCity(entry.getValue());
                }
                if ("area".equals(entry.getKey())){
                    queryListDrugstore.setArea(entry.getValue());
                }
                List<String> organSignSub = drugstoreAdapter.listDrugstoresWhithArea(queryListDrugstore);
                if (CollectionUtils.isNotEmpty(organSignSub)){
                    organSignTotal.addAll(organSignSub);
                }
            }else {
                DrugstoreAreaQueryDto drugstoreQueryDto = new DrugstoreAreaQueryDto();
                drugstoreQueryDto.setOrganSignType((byte) organSignType.getKey());
                drugstoreQueryDto.setBizModel((byte) bizModel.getKey());
                drugstoreQueryDto.setStatus((byte) 1);
                if ("province".equals(entry.getKey())){
                    drugstoreQueryDto.setProvince(entry.getValue());
                }
                if ("city".equals(entry.getKey())){
                    drugstoreQueryDto.setCity(entry.getValue());
                }
                if ("area".equals(entry.getKey())){
                    drugstoreQueryDto.setArea(entry.getValue());
                }
                List<String> organSignSub = drugstoreMapper.listDrugstoresWhithArea(drugstoreQueryDto);
                if (CollectionUtils.isNotEmpty(organSignSub)){
                    organSignTotal.addAll(organSignSub);
                }
            }


        }
        return organSignTotal;
    }

    @Override
    public List<String> listSelectOrganSign() {
        if (!isInitOrganSignList) {
            initOrganSignList();
        }
        log.info("listSelectOrganSign|messageNoticeId={},allDrugstore={},selectOrganSignList size={}", messageNoticeId, allDrugstore, selectOrganSignList.size());
        return selectOrganSignList;
    }

    @Override
    public List<String> listCustomOrganSign() {
        if (!isInitOrganSignList) {
            initOrganSignList();
        }
        log.info("listCustomOrganSign|messageNoticeId={},allDrugstore={},selectOrganSignList size={}", messageNoticeId, allDrugstore, customOrganSignList.size());
        return customOrganSignList;
    }

    @Override
    public void setAreaCode(Map<String, List<String>> groupArea) {
        this.groupArea = groupArea;
    }

    public static void main(String[] args) {
        LocalTime now = LocalTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        String formatStr = now.format(formatter);
        System.out.println(now.getHour());
        System.out.println(now.getMinute());
        System.out.println(now.getSecond());
        System.out.println(formatStr);
    }
}
