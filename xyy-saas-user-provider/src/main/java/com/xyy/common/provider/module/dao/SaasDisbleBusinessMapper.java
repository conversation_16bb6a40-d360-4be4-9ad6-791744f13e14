package com.xyy.common.provider.module.dao;

import com.xyy.common.provider.module.entity.SaasDisableBusinessPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 禁采所属范围配置表(SaasDisbleBusiness)表数据库访问层
 *
 * <AUTHOR> @since 2022-01-12 16:56:57
 */
public interface SaasDisbleBusinessMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param organSign 机构号
     * @return 实例对象
     */
    List<SaasDisableBusinessPo> queryByOrgan(String organSign);

    SaasDisableBusinessPo queryById(Long id);

    /**
     * 统计总行数
     *
     * @param saasDisableBusinessPo 查询条件
     * @return 总行数
     */
    long count(SaasDisableBusinessPo saasDisableBusinessPo);

    /**
     * 新增数据
     *
     * @param saasDisableBusinessPo 实例对象
     * @return 影响行数
     */
    int insert(SaasDisableBusinessPo saasDisableBusinessPo);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<SaasDisbleBusiness> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<SaasDisableBusinessPo> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<SaasDisbleBusiness> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<SaasDisableBusinessPo> entities);

    /**
     * 修改数据
     *
     * @param saasDisableBusinessPo 实例对象
     * @return 影响行数
     */
    int update(SaasDisableBusinessPo saasDisableBusinessPo);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 根据机构号删除
     * @param organSign
     * @return
     */
    int deleteByOrgan(String organSign);

}

