package com.xyy.common.provider.module.api.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.common.module.api.MessageNoticeApi;
import com.xyy.common.module.dto.MessageNoticeDto;
import com.xyy.common.provider.module.entity.MessageNoticePo;
import com.xyy.common.provider.module.entity.SaasMessageNoticeDrugePo;
import com.xyy.common.provider.module.service.MessageNoticeService;
import com.xyy.common.provider.module.service.SaasMessageNoticeDrugeService;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.provider.common.util.JedisUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

@Service(version = "0.0.1")
public class MessageNoticeApiImpl implements MessageNoticeApi {


    private static final Logger logger = LoggerFactory.getLogger(MessageNoticeApiImpl.class);

    @Autowired
    private MessageNoticeService messageNoticeService;

    @Autowired
    private SaasMessageNoticeDrugeService saasMessageNoticeDrugeService;

    @Autowired
    private JedisUtil jedisUtil;


    @Override
    public Boolean insert(MessageNoticeDto messageNoticeDto) {
        MessageNoticePo po = dtoToPo(messageNoticeDto);
        return messageNoticeService.insert(po);
    }

    @Override
    public Boolean update(MessageNoticeDto messageNoticeDto) {
        MessageNoticePo po = dtoToPo(messageNoticeDto);
        return messageNoticeService.update(po);
    }

    @Override
    public MessageNoticeDto selectById(Long id) {
        return messageNoticeService.selectById(id);
    }

    @Override
    public Boolean updateState(MessageNoticeDto messageNoticeDto) {
        MessageNoticePo po = new MessageNoticePo();
        BeanUtils.copyProperties(messageNoticeDto, po);
        return messageNoticeService.updateState(po);
    }

    @Override
    public PageInfo<MessageNoticeDto> getMessageNoticeListPager(PageInfo pageInfo, MessageNoticeDto bean) {
        MessageNoticePo po = new MessageNoticePo();
        BeanUtils.copyProperties(bean, po);
        return messageNoticeService.getMessageNoticeListPager(pageInfo, po);
    }

    @Override
    public PageInfo getMessageNoticeDrugstoreListPager(PageInfo pageInfo, Long messageNoticeId,String organSign, String drugstoreName, String province,String city,String area) {
        SaasMessageNoticeDrugePo po = new SaasMessageNoticeDrugePo();
        po.setOrganSign(organSign);
        po.setMessageNoticeId(messageNoticeId);
        po.setProvince(province);
        po.setCity(city);
        po.setArea(area);
        po.setDrugstoreName(drugstoreName);
        PageInfo info = saasMessageNoticeDrugeService.queryAll(po, pageInfo);
        return info;
        //return messageNoticeService.getMessageNoticeDrugstoreListPager(pageInfo, messageNoticeId, null);
    }

    @Override
    public PageInfo getMessageNoticeDrugstoreListPager(PageInfo pageInfo, Long messageNoticeId, Integer customFlag) {

        return messageNoticeService.getMessageNoticeDrugstoreListPager(pageInfo, messageNoticeId, customFlag);
    }

    @Override
    public List<String> getMessageNoticeDrugstoreList(Long messageNoticeId){
        return messageNoticeService.getMessageNoticeDrugstoreList(messageNoticeId);
    }


    @Override
    public PageInfo<MessageNoticeDto> getMessageNoticeByEmployeeIdPager(PageInfo pageInfo, MessageNoticeDto bean) {
        return messageNoticeService.getMessageNoticeByEmployeeIdPager(pageInfo, bean);
    }


    @Override
    public Boolean updateMessageNoticeByEmployeeId(String organSign, String employeeId, List<Long> messageNoticeIds) {
        return messageNoticeService.updateMessageNoticeByEmployeeId(organSign, employeeId, messageNoticeIds);
    }

    @Override
    public List<String> selectReleasePeopleNameList() {
        return messageNoticeService.selectReleasePeopleNameList();
    }

    @Override
    public Boolean copyMessage(Long id,String userId,String content) {
        Boolean flag = false;
        MessageNoticeDto source = messageNoticeService.selectById(id);
        if (null != source){
            source.setCreateTime(new Date());
            source.setUpdateTime(new Date());
            source.setUpdateUser(userId);
            source.setCreateUser(userId);
            source.setState((byte) 0);
            source.setId(null);
            source.setTitle("[复制]"+source.getTitle());
            String allDrugstore = source.getAllDrugstore();
            if ("1".equals(allDrugstore.substring(allDrugstore.length()-1))){
                source.setAllDrugstore("0");
            }
            if(StringUtils.isNotBlank(content)){
                source.setContent(content);
            }
            MessageNoticePo po = dtoToPo(source);
            Boolean insert = messageNoticeService.insert(po);
            flag = insert;
        }
        return flag;
    }

    @Override
    public MessageNoticeDto getLatestMessge(MessageNoticeDto bean) {
        return messageNoticeService.getLatestMessge(bean);
    }

    @Override
    public ResultVO getImportData(Long taskId) {
        if (null == taskId){
            return new ResultVO(ResultCodeEnum.ERROR, "未获取任务id");
        }
        String importPref = "CLOUD:MESSAGE:IMPORT:";
        String redisKey = importPref+taskId;
        if (!jedisUtil.exists(redisKey)){
            return new ResultVO(ResultCodeEnum.ERROR, "未查询到导入信息");
        }
        List<String> lrange = jedisUtil.getList(redisKey);
        Map<String, Object> resultData = new HashMap<>();
        List<Map<String, Object>> excelData = new ArrayList<>();
        Integer statusCount = 0;
        for (String item:lrange) {
            Map<String, Object> map = JSONObject.parseObject(item);
            if (((byte) 1) == Byte.valueOf(map.get("status").toString())) {
                excelData.add(map);
            }else {
                statusCount++;
            }
        }
        //组装数据
        List<Map<String, Object>> statusData = new ArrayList<>();
        Map<String, Object> status = new HashMap<>();
        if (statusCount > 0) {
            status.put("status", 2);
            status.put("size", statusCount);
        } else {
            status.put("status", 1);
            status.put("size", 0);
        }
        statusData.add(status);
        resultData.put("excelData", excelData);
        resultData.put("status", statusData);
        return new ResultVO<Map<String, Object>>(ResultCodeEnum.SUCCESS, resultData);
    }

    /**
     * dto转化po
     *
     * @param dto
     * @return
     */
    private MessageNoticePo dtoToPo(MessageNoticeDto dto) {
        MessageNoticePo po = new MessageNoticePo();
        BeanUtils.copyProperties(dto, po);
        po.setAllDrugstore(Integer.valueOf(dto.getAllDrugstore()));
        return po;
    }

}
