package com.xyy.common.provider.module.api.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.common.module.api.MessageNoticeApi;
import com.xyy.emule.api.SynchExportDataApi;
import com.xyy.emule.dto.SynchDataParamDto;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.user.module.dto.SaasDrugstoreDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/28 14:40
 * @Version 1.0
 * 红尘轮回众生顾,因果循环有定数
 * 放下屠刀虽成佛,愿坠三途灭千魔
 */
@Service(version = "1.0.0", group = "cloudMessageDrugExport")
@Slf4j
public class MessageNoticeExportImpl implements SynchExportDataApi {

    @Autowired
    private MessageNoticeApi messageNoticeApi;

    @Override
    public ResultVO<PageInfo<JSONObject>> downExportData(SynchDataParamDto synchDataParamDto) {
        log.info("MessageNoticeExportImpl#downExportData 导出参数:{}", JSON.toJSONString(synchDataParamDto));
        PageInfo<JSONObject> result = new PageInfo<>();
        List<JSONObject> jsonList = new ArrayList<>();

        JSONObject jo = JSON.parseObject(synchDataParamDto.getParamInfo());
        try {
            Long messageNoticeId = Long.valueOf(jo.getString("messageNoticeId"));
            Integer page = synchDataParamDto.getPageNum();
            Integer rows = synchDataParamDto.getPageSize();
            String organSign = jo.getString("organSign");
            String drugstoreName = jo.getString("drugstoreName");
            String province = jo.getString("province");
            String city = jo.getString("city");
            String area = jo.getString("area");
            PageInfo pageInfo = new PageInfo();
            pageInfo.setPageNum(page == null ? 1 : page);
            pageInfo.setPageSize(rows == null ? 50 : rows);
            PageInfo messagePag = messageNoticeApi.getMessageNoticeDrugstoreListPager(pageInfo, messageNoticeId, organSign, drugstoreName, province, city, area);
            List<SaasDrugstoreDto> drugstoreDtos = messagePag.getList();
            for (SaasDrugstoreDto item:drugstoreDtos) {
                item.setArea(item.getProvince()+item.getCity()+item.getArea());
                jsonList.add(JSON.parseObject(JSON.toJSONString(item)));
            }
        }catch (Exception e){
            log.error("MessageNoticeExportImpl#downExportData 导出异常:{}",e);
            return ResultVO.createError("系统异常");
        }
        result.setList(jsonList);
        return ResultVO.createSuccess(result);
    }
}