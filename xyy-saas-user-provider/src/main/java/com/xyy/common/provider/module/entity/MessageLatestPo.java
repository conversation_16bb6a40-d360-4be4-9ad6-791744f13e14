package com.xyy.common.provider.module.entity;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/9 14:43
 * @Version 1.0
 * 红尘轮回众生顾,因果循环有定数
 * 放下屠刀虽成佛,愿坠三途灭千魔
 */
@Data
public class MessageLatestPo {
    /**
     * 药店机构标识
     */
    private String organSign;

    /**
     * 消息通知id
     */
    private Long messageNoticeId;

    /**
     * 消息通知ids
     */
    private List<Long> messageNoticeIds;

    /**
     * 员工id
     */
    private String employeeId;

    /**
     *
     */
    private Byte classification;

    /**
     * 过期时间
     */
    private Date expireTime;

    /**
     * 读取状态 0 未读，1已读
     */
    private Byte state;



}
