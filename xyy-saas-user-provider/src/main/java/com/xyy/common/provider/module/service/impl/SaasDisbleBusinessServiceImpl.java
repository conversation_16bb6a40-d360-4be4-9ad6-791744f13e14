package com.xyy.common.provider.module.service.impl;

import com.xyy.common.provider.module.dao.SaasDisbleBusinessMapper;
import com.xyy.common.provider.module.entity.SaasDisableBusinessPo;
import com.xyy.common.provider.module.service.SaasDisbleBusinessService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 禁采所属范围配置表(SaasDisbleBusiness)表服务实现类
 *
 * <AUTHOR> @since 2022-01-12 16:57:15
 */
@Service("saasDisbleBusinessService")
public class SaasDisbleBusinessServiceImpl implements SaasDisbleBusinessService {
    @Resource
    private SaasDisbleBusinessMapper saasDisbleBusinessMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public List<SaasDisableBusinessPo> queryByOrgan(String organSign) {
        return this.saasDisbleBusinessMapper.queryByOrgan(organSign);
    }

    @Override
    public SaasDisableBusinessPo queryById(Long id) {
        return this.saasDisbleBusinessMapper.queryById(id);
    }


    /**
     * 新增数据
     *
     * @param saasDisableBusinessPo 实例对象
     * @return 实例对象
     */
    @Override
    public SaasDisableBusinessPo insert(SaasDisableBusinessPo saasDisableBusinessPo) {
        this.saasDisbleBusinessMapper.insert(saasDisableBusinessPo);
        return saasDisableBusinessPo;
    }

    /**
     * 修改数据
     *
     * @param saasDisableBusinessPo 实例对象
     * @return 实例对象
     */
    @Override
    public SaasDisableBusinessPo update(SaasDisableBusinessPo saasDisableBusinessPo) {
        this.saasDisbleBusinessMapper.update(saasDisableBusinessPo);
        return this.queryById(saasDisableBusinessPo.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Long id) {
        return this.saasDisbleBusinessMapper.deleteById(id) > 0;
    }

    @Override
    public int deleteByOrgan(String organSign) {
        return saasDisbleBusinessMapper.deleteByOrgan(organSign);
    }

    @Override
    public int insertOrUpdateBatch(List<SaasDisableBusinessPo> entities) {
        return saasDisbleBusinessMapper.insertOrUpdateBatch(entities);
    }
}
