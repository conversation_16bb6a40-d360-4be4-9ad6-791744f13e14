package com.xyy.common.provider.module.service;


import com.xyy.common.provider.module.entity.SaasDisableBusinessPo;

import java.util.List;

/**
 * 禁采所属范围配置表(SaasDisbleBusiness)表服务接口
 *
 * <AUTHOR> @since 2022-01-12 16:57:15
 */
public interface SaasDisbleBusinessService {

    /**
     * 通过ID查询单条数据
     *
     * @param organSign 主键
     * @return 实例对象
     */
    List<SaasDisableBusinessPo> queryByOrgan(String organSign);

    SaasDisableBusinessPo queryById(Long id);

    /**
     * 新增数据
     *
     * @param saasDisableBusinessPo 实例对象
     * @return 实例对象
     */
    SaasDisableBusinessPo insert(SaasDisableBusinessPo saasDisableBusinessPo);

    /**
     * 修改数据
     *
     * @param saasDisableBusinessPo 实例对象
     * @return 实例对象
     */
    SaasDisableBusinessPo update(SaasDisableBusinessPo saasDisableBusinessPo);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Long id);

    int deleteByOrgan(String organSign);

    int insertOrUpdateBatch(List<SaasDisableBusinessPo> entities);

}
