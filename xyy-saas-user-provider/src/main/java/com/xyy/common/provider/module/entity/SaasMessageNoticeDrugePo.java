package com.xyy.common.provider.module.entity;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * (SaasMessageNoticeDruge)实体类
 *
 * <AUTHOR> @since 2021-09-24 16:26:11
 */
@Data
public class SaasMessageNoticeDrugePo implements Serializable {
    private static final long serialVersionUID = 788495996800455829L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 消息体id
     */
    private Long messageNoticeId;
    /**
     * 机构号
     */
    private String organSign;
    /**
     * 机构名称
     */
    private String drugstoreName;
    /**
     * 联系人
     */
    private String managerName;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 地区
     */
    private String area;

    /**
     * 详细地址
     */
    private String address;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

}

