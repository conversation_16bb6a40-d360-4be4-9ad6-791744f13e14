package com.xyy.common.provider.module.service;

import com.xyy.common.module.dto.SystemConfigDto;
import com.xyy.common.provider.module.entity.SystemConfigPo;
import com.xyy.saas.common.util.ResultVO;

import java.util.List;

/**
 * <AUTHOR> on 2018年9月11日
 */
public interface SysConfigService {
	/**
	 * 更新控制面板
	 * @param config
	 * @return
	 */
	boolean updateSystemConfig(SystemConfigPo config);


	ResultVO<Boolean> updateSystemConfigNew(SystemConfigPo config);

	/**
	 * 机构控制面板初始化
	 * @param systemConfigPo
	 * @return
	 */
	boolean initSystemConfig(SystemConfigPo systemConfigPo);

	/**
	 * 根据机构标识，查询机构控制面板
	 * @param organSign
	 * @return
	 */
	SystemConfigDto querySystemConfig(String organSign);

	/**
	 * 根据机构标识，查询机构控制面板
	 * @param organSign
	 * @return
	 */
	SystemConfigPo queryInitSystemConfig(String organSign);

	/**
	 * 查询执业药师为空的机构号
	 * @return
	 */
	List<String> queryEmptyLicensedPharmacistOrgList();

	/**
	 * 更新执业药师字段
	 * @param organSign
	 * @param licensedPharmacist
	 */
	void updateEmptyLicensedPharmacist(String organSign, String licensedPharmacist);

    List<String> getOpenTraceCodeSalesOrganSignList();
}
