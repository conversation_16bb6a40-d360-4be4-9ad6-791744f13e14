package com.xyy.common.provider.module.dao;

import com.dianping.zebra.dao.datasource.ZebraRouting;
import com.xyy.common.provider.module.entity.SystemConfigPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@ZebraRouting("common")
public interface SystemConfigMapper {

	/**
	 * 初始化控制面板
	 * @param record
	 * @return
	 */
	int insert(SystemConfigPo record);

	/**
	 * 更新控制面板
	 * @param record
	 * @return
	 */
	int update(SystemConfigPo record);

	/**
	 * 根据机构标识，查询机构控制面板
	 * @param organSign
	 * @return
	 */
	SystemConfigPo selectSystemConfigByOrganSign(@Param("organSign") String organSign);

	/**
	 * 查询执业药师为空的机构号
	 * @return
	 */
    List<String> queryEmptyLicensedPharmacistOrgList();

	/**
	 * 更新执业药师字段
	 * @param organSign
	 * @param licensedPharmacist
	 */
	void updateEmptyLicensedPharmacist(@Param("organSign") String organSign, @Param("licensedPharmacist") String licensedPharmacist);

    List<String> getOpenTraceCodeSalesOrganSignList();
}