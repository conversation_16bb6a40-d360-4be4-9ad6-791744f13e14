package com.xyy.common.provider.common.constant;

/**
 * <AUTHOR>
 * @annotation:
 * @create 2019-06-26 16:40
 **/
public class SystemConstans {
    //广告管理中默认广告值
    public static final Integer ADVERTPICTURE_DEFAULT=1;
    //广告管理中默认广告值
    public static final Integer ADVERTPICTURE_DEFAULT_NO=0;
    //广告管理中未删除状态
    public static final Integer ADVERTPICTURE_Y=0;

    //数据同步条数上限
    public static final Integer SYNC_DATA_COUNT_LIMIT=1000;

    //用药提醒是否已使用
    public static final Integer DRUG_REMMIND_IN_USEING = -1;

    //用药提醒 禁用
    public static final Byte DRUG_REMMIND_DISABLED = 0;

    //已经存在列表
    public static final Integer DRUG_REMMIND_ALREADY_EXIST = -1;

    //全部失败  用药提醒
    public static final int DRUG_REMMIND_ALL_EXCEPTION = -2;

    //部分失败  用药提醒
    public static final int DRUG_REMMIND_PART_EXCEPTION = -3;

    //常量0
    public static final Integer CONSTANT_ZERO = 0;

    //,号字符串
    public static final String STRING_COMMA = ",";

    //线程分割基数
    public static final int THREAD_PARTITION_SIZE = 100;

    //报表查询跨度默认时间
    public static final Integer REPORT_QUERY_DATE_SPAN = 92;

    //报表查询跨度 不开启机构查询
    public static final int REPORT_QUERY_ORGANSIGN_ENABLED = 0;

    //查询ec地图信息
    public static final int SAAS_AREA_FLAG = 3;

    //优惠券短信提醒开关 1 是 0 否
    public static final int COUPON_NOTE_YN_NO = 0;

    //优惠券短信提醒开关 1 是 0 否
    public static final int COUPON_NOTE_YN_YES = 1;

    //启用
    public static final int SYSTEM_YN_YES = 1;

    //禁用
    public static final int SYSTEM_YN_NO = 0;

    //同步实名认证表名
    public static final String SYNC_SAAS_REAL_NAME_REGISTRATION = "saas_real_name_registration";

    //同步药品限购表
    public static final String SYNC_SAAS_PRODUCT_LIMIT = "saas_product_limit";


    //经验范围系统id
    public static final Integer BUSINESS_SCOPE_ID = 10005;

    //是否开启经营范围，0 否, 1 是
    public static final byte BUSINESS_SCOPE_YN_YES = 1;

    //是否开启经营范围，0 否, 1 是
    public static final byte BUSINESS_SCOPE_YN_NO = 0;

    //是否禁用，0 否, 1 是
    public static final byte BUSINESS_SCOPE_STATUS_YES = 1;

    //是否禁用，0 否, 1 是
    public static final byte BUSINESS_SCOPE_STATUS_NO = 0;

    //同步数据表
    public static final String TABLE_NAME_SAAS_BUSINESS_SCOPE = "saas_business_scope";

    // 同字典表
    public static final String TABLE_NAME_SAAS_SYSTEM_DICT = "saas_system_dict";

    // 同步用户表
    public static final String TABLE_NAME_SAAS_USER = "saas_user";

    // 同步用户员工表
    public static final String TABLE_NAME_SAAS_EMPLOYEE = "saas_employee";

    // 同步门店机构表
    public static final String TABLE_NAME_SAAS_DRUGSTORE = "saas_drugstore";

    //同步数据条数
    public static final Integer SYNC_COUNT = 1000;

    //养护计划天数
    public static final Integer MAINTENANCE_PLAN_DATA = 3;

    //实际养护日期可修改，0 否, 1 是，默认关闭
    public static final byte MAINTENANCE_DATA_UPDATE_YN_NO = 0;

    /**
     * 导入字符串截取长度
     */
    public static final Integer SUBSTRING_LENGTH = 4000;

    /**
     * 导入字符串截取长度
     */
    public static final String SUBSTRING_OMIT = "...";

    //原单退货审核开关，0 否, 1 是，默认关闭
    public static final byte RETURN_SALES_YN_NO = 0;

    //处方登记开关，0 否, 1 是，默认关闭
    public static final byte PRESCRIPTION_REGISTERED_YN = 0;

    //供应商资质过期提醒
    public static final Integer PROVIDER_QUALIFICATION_EXPIRED_REMIND_DAYS = 30;

    //商品资质过期提醒
    public static final Integer PRODUCT_QUALIFICATION_EXPIRED_REMIND_DAYS = 30;

    //商品近效期提醒-单体门店
    public static final Integer EARLY_WARNING_DAYS = 5;

    //商品近效期提醒-连锁总部
    public static final Integer EARLY_WARNING_DAYS_HEADQUARTERS = 180;

    //滞销天数
    public static final Integer DEAD_STOCK_WARNING_DAYS = 30;

    //滞销基数
    public static final Integer DEAD_STOCK_WARNING_COUNT = 100;

    /**
     * 医保进销存提醒
     */
    public static final byte SALE_STORAGE_NO = 0;

    /**
     * 药监监管-默认否
     */
    public static final byte DRUG_SUPERVISION_NO = 0;

    /**
     * 盲交班-默认否
     */
    public static final byte BLIND_SHIFT_HANDOVER = 0;

    public static final byte RECORD_MOBILE_FOR_NARCOTIC_YES = 1;

    public static final byte RECORD_MOBILE_FOR_NARCOTIC_NO = 0;

    /**
     * 追溯码销售录入-默认否
     */
    public static final byte TRACE_CODE_SALE_ENTRY_YN_NO = 0;
}

