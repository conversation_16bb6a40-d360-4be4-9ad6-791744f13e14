server:
  port: 9091

# dubbo配置
dubbo:
  application:
    name: user
    owner: uzdz
    version: 1.0.0
  protocol: # 用dubbo协议在22880端口暴露服务
    id: dubbo
    name: dubbo
    port: 20880
    threadpool: fixed
    threads: 300
    # 请求以及响应数据包限制最大32M
    payload: 33554432
  consumer:
    timeout: 60000
  provider:
    timeout: 60000
    retries: -1
  registries:
    middlestage:
      id: middlestage
      protocol: zookeeper
      address: zk11-base-prod.zookeeper.ybm100.top:2181,zk12-base-prod.zookeeper.ybm100.top:2181,zk13-base-prod.zookeeper.ybm100.top:2181,zk14-base-prod.zookeeper.ybm100.top:2181,zk15-base-prod.zookeeper.ybm100.top:2181
      check: false
      default: false
  registry: # 连接zookeeper注册中心,并指定ip端口
    protocol: zookeeper
    address: zk01-ec-prod.zookeeper.ybm100.top:2181,zk02-ec-prod.zookeeper.ybm100.top:2181,zk03-ec-prod.zookeeper.ybm100.top:2181,zk04-ec-prod.zookeeper.ybm100.top:2181,zk05-ec-prod.zookeeper.ybm100.top:2181|zk6-base-prod.zookeeper.ybm100.top:2181,zk7-base-prod.zookeeper.ybm100.top:2181,zk8-base-prod.zookeeper.ybm100.top:2181,zk9-base-prod.zookeeper.ybm100.top:2181,zk10-base-prod.zookeeper.ybm100.top:2181
  service:
    delay: 5000

management:
  security:
    enabled: false
  health:
    dubbo:
      status:
        extras: load,threadpool

# spring配置
spring:
  http:
    multipart:
      enabled: true
      max-file-size: 3MB
      max-request-size: 3MB
  datasource:
    druid:
      filters: config,stat
      max-active: 50
      initial-size: 5
      max-wait: 18000
      min-idle: 5
      time-between-eviction-runs-millis: 2000
      min-evictable-idle-time-millis: 300000
      validation-query: select 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      keep-alive: true
#  datasource:
#    url: *****************************************************************************************************************************************************************************************
#    # username: app_user_w
#    # password: hPGDE563$2dj&2
#    type: com.alibaba.druid.pool.DruidDataSource
#    driverClassName: com.mysql.jdbc.Driver
#    druid:
#      filters: config,stat
#      connection-properties: config.decrypt=true;config.decrypt.key=${spring.datasource.key}
#      max-active: 50
#      initial-size: 5
#      max-wait: 18000
#      min-idle: 5
#      time-between-eviction-runs-millis: 2000
#      min-evictable-idle-time-millis: 300000
#      validation-query: select 1
#      test-while-idle: true
#      test-on-borrow: false
#      test-on-return: false
#      keep-alive: true
  mvc:
    static-path-pattern: /static/**
  redis:
    # redis数据库索引（默认为0）
    database: 0
    # redis服务器地址（默认为localhost）
    host: db21-saas-prod.redis.ybm100.top
    # redis端口（默认为6379）
    port: 6379
    # redis访问密码（默认为空）
    password: vTqWVtL69TkKqZlJ
    # redis连接超时时间（单位为毫秒）
    timeout: 10000
    # redis连接池配置
    pool:
      # 最大可用连接数（默认为8，负数表示无限）
      max-active: -1
      # 最大空闲连接数（默认为8，负数表示无限）
      max-idle: -1
      # 最小空闲连接数（默认为0，该值只有为正数才有作用）
      min-idle: 0
      # 从连接池中获取连接最大等待时间（默认为-1，单位为毫秒，负数表示无限）
      max-wait: -1

# 日志
logging:
  config: classpath:logback-spring.xml
  file: saas-user-provider

# mybatis DAO
mybatis:
  mapper-locations: classpath:/mapper/*.xml
  configuration:
    mapUnderscoreToCamelCase: true
    log-prefix: dao.

# pagehelper 分页
pagehelper:
  autoDialect: true
  closeConn: true
  reasonable: true

rabbitmq:
  config:
    address: rabbitmq1-saas-prod.rabbitmq.ybm100.top,rabbitmq2-saas-prod.rabbitmq.ybm100.top,rabbitmq3-saas-prod.rabbitmq.ybm100.top,rabbitmq4-saas-prod.rabbitmq.ybm100.top,rabbitmq5-saas-prod.rabbitmq.ybm100.top,rabbitmq6-saas-prod.rabbitmq.ybm100.top
    port: 5672
    username: admin
    password: ck2MG5wCIwczpODT
    virtualHost: /
    connectionTimeout: 6000
  host: mq.saas.ybm100.com
  port: 49529
  username: admin
  password: ck2MG5wCIwczpODT
  virtualHost: /
  MESSAGE_PUSH_USER: message.push.user
  MESSAGE_PUSH_ORGANIZATION: message.push.organization
  MESSAGE_PUSH_MULTICAST: message.push.multicast

rabbitmqssl:
  host: mq.saas.ybm100.com
  sslport: 49528
  username: admin
  password: ck2MG5wCIwczpODT
  virtualHost: /
  sslCertPassword: SaaS3.0RabbitMQCA
  MESSAGE_PUSH_USER: message.push.user
  MESSAGE_PUSH_ORGANIZATION: message.push.organization
  MESSAGE_PUSH_MULTICAST: message.push.multicast

ybm100:
  auth_url: https://www.ybm100.com/
  sms_url: http://www.ybm100.com/
  user_url: http://admin.ybm100.com/
  ticket_url: http://admin.ybm100.com/
  voucher_url: http://www.ybm100.com/
  voucher_url_intranet: http://www.prod.ybm100.com/

# 发送短信验证码配置
xyy:
  saas:
    jointmanagement:
        host: http://www.ybm100.com/
        uri: crm/sms/send/dtly/verificationCode
  spring:
    zebra:
      jdbcRef: user,common
      defaultJdbcRef: user
      configManagerType: local
      mapperLocations: mapper/**/*.xml
      user:
        poolType: druid
      common:
        poolType: druid
      group:
        user:
          name[0]: user-n1
          writeWeight[0]: 0
          readWeight[0]: -1
          name[1]: user-n2
          writeWeight[1]: -1
          readWeight[1]: 1
        common:
          name[0]: common-n1
          writeWeight[0]: 0
          readWeight[0]: -1
          name[1]: common-n2
          writeWeight[1]: -1
          readWeight[1]: 1
      ds:
        user:
          url[0]: ***********************************************************************************************************************************************************************************************
          driverClass[0]: com.mysql.jdbc.Driver
          active[0]: true
          username[0]: app_user_zebra_w
          url[1]: ***********************************************************************************************************************************************************************************************
          driverClass[1]: com.mysql.jdbc.Driver
          active[1]: true
          username[1]: app_user_zebra_w
        common:
          url[0]: ********************************************************************************************************************************************************
          driverClass[0]: com.mysql.jdbc.Driver
          active[0]: true
          username[0]: app_common_zebra_w
          url[1]: ********************************************************************************************************************************************************
          driverClass[1]: com.mysql.jdbc.Driver
          active[1]: true
          username[1]: app_common_zebra_w
saas:
  cloud:
    url: http://saas-cloud.ybm100.com/

login:
  image:
    yituIp: "http://staging.yitutech.com"
    pemPath: "stagingpublic.pem"
    accessId: "15028"
    accessKey : "3248a652a28eca04b0250cf12dd24df2"
    userDefinedContent: "saas"

cat:
  appName: saas-user-provider

ftp:
  serverName: ftp.prod.ybm100.top
  port: 2121
  username: ftp-saas-saasexcel
  password: UJEv9NPbIensF0sP
  remotePath: /saas
  downLoadUrl: https://downloads.ybm100.com/saas/saas3.0-excel

#common:
#  datasource:
#    url: *************************************************************************************************************************************************
#    username: app_common_w
##    password: 87HwGkww%d1ldLP
#    type: com.alibaba.druid.pool.DruidDataSource
#    driverClassName: com.mysql.jdbc.Driver
#    druid:
#      max-active: 50
#      initial-size: 5
#      max-wait: 60000
#      min-idle: 5
#      keep-alive: true
#      time-between-eviction-runs-millis: 2000
#      min-evictable-idle-time-millis: 300000
#      validation-query: select 1
#      test-while-idle: true
#      test-on-borrow: false
#      test-on-return: false
#      filters: config
#      connection-properties: config.decrypt=true;config.decrypt.key=${spring.datasource.key}

user:
  profile: prod
  face:
    database:
      env: saas_group_prod
  apollo:
    grayscale:
      onoff: 0
  checkpassword:
    limit: 10
  checkphone:
    limit: 11
  checkidcard:
    limit: 18
  checkname:
    limit: 15
  checknational:
    limit: 10
#  datasource:
#    url: *****************************************************************************************************************************************************************************************
#    # username: app_user_w
#    # password: hPGDE563$2dj&2
#    type: com.alibaba.druid.pool.DruidDataSource
#    driverClassName: com.mysql.jdbc.Driver
#    druid:
#      filters: config,stat
#      connection-properties: config.decrypt=true;config.decrypt.key=${spring.datasource.key}
#      max-active: 50
#      initial-size: 5
#      max-wait: 18000
#      min-idle: 5
#      time-between-eviction-runs-millis: 2000
#      min-evictable-idle-time-millis: 300000
#      validation-query: select 1
#      test-while-idle: true
#      test-on-borrow: false
#      test-on-return: false
#      keep-alive: true

rocketmq:
  name-server: mq01-saas-prod.rocketmq.ybm100.top:9876;mq02-saas-prod.rocketmq.ybm100.top:9876;mq03-saas-prod.rocketmq.ybm100.top:9876;mq04-saas-prod.rocketmq.ybm100.top:9876
  sendMessageTimeout: 3000
  #name-server: 127.0.0.1:9876
  #name-server: mq1-dev.rocketmq.ybm100.top:9876;mq2-dev.rocketmq.ybm100.top:9876;mq3-dev.rocketmq.ybm100.top:9876;mq4-dev.rocketmq.ybm100.top:9876
  producer:
    group: PID_saas_ykq_user_ms
    topic: saas-ykq_user-ms
    memberSmsSensitiveGroup: PID_member_sms_sensitive_group
    memberSmsSensitiveTopic: member_sms_sensitive_topic
  consumer:
    group: CID_saas_ykq_user_ms
    topic: saas-ykq_user-ms
rocketmq-employee:
  name-server: mq01-saas-prod.rocketmq.ybm100.top:9876;mq02-saas-prod.rocketmq.ybm100.top:9876;mq03-saas-prod.rocketmq.ybm100.top:9876;mq04-saas-prod.rocketmq.ybm100.top:9876
  sendMessageTimeout: 3000
  producer:
    group: PID_prod_employee_group
    topic: prod_employee_topic
  consumer:
    group: CID_prod_employee_group
    topic: prod_employee_topic
rocketmq-medicalInsurance:
  name-server: mq01-saas-prod.rocketmq.ybm100.top:9876;mq02-saas-prod.rocketmq.ybm100.top:9876;mq03-saas-prod.rocketmq.ybm100.top:9876;mq04-saas-prod.rocketmq.ybm100.top:9876
  sendMessageTimeout: 3000
  producer:
    group: PID_prod_medical_insurance_group
    topic: prod_medical_insurance_topic
  consumer:
    group: CID_prod_medical_insurance_group
    topic: prod_medical_insurance_topic
rocketmq-changeauthority:
  name-server: mq01-saas-prod.rocketmq.ybm100.top:9876;mq02-saas-prod.rocketmq.ybm100.top:9876;mq03-saas-prod.rocketmq.ybm100.top:9876;mq04-saas-prod.rocketmq.ybm100.top:9876
  sendMessageTimeout: 3000
  producer:
    group: PID_prod_changeauthority_group
    topic: prod_changeauthority_topic
  consumer:
    group: CID_prod_changeauthority_group
    topic: prod_changeauthority_topic
rocketmq-packageoperationlog:
  name-server: mq01-saas-prod.rocketmq.ybm100.top:9876;mq02-saas-prod.rocketmq.ybm100.top:9876;mq03-saas-prod.rocketmq.ybm100.top:9876;mq04-saas-prod.rocketmq.ybm100.top:9876
  sendMessageTimeout: 3000
  producer:
    group: PID_prod_packageoperationlog_group
    topic: prod_packageoperationlog_topic
  consumer:
    group: CID_prod_packageoperationlog_group
    topic: prod_packageoperationlog_topic
rocketmq-crm:
  name-server: mq01-saas-prod.rocketmq.ybm100.top:9876;mq02-saas-prod.rocketmq.ybm100.top:9876;mq03-saas-prod.rocketmq.ybm100.top:9876;mq04-saas-prod.rocketmq.ybm100.top:9876
  sendMessageTimeout: 3000
  producer:
    group: PID_prod_crm_contract_group
    topic: prod_crm_contract_topic
  consumer:
    group: CID_prod_crm_contract_group
    topic: prod_crm_contract_topic
rocketmq-business:
  name-server: mq01-saas-prod.rocketmq.ybm100.top:9876;mq02-saas-prod.rocketmq.ybm100.top:9876;mq03-saas-prod.rocketmq.ybm100.top:9876;mq04-saas-prod.rocketmq.ybm100.top:9876
  sendMessageTimeout: 3000
  producer:
    group: PID_prod_businessScope_group
    topic: prod_businessScope_topic
  consumer:
    group: CID_prod_businessScope_group
    topic: prod_businessScope_topic
rocketmq-user:
  name-server: mq01-saas-prod.rocketmq.ybm100.top:9876;mq02-saas-prod.rocketmq.ybm100.top:9876;mq03-saas-prod.rocketmq.ybm100.top:9876;mq04-saas-prod.rocketmq.ybm100.top:9876
  sendMessageTimeout: 3000
  producer:
    sync-assa-ybm-account:
      topic: prod_sync-saas-ybm-account_topic
    group: PID_prod_user_init_group
    topic: prod_user_init_topic
    update-user:
      topic: prod_crm_update_customer_topic
    bind-ybm:
      topic: prod_bind_ybm_topic
    drugstore-init:
      topic: prod_drugstore_init_topic
      group: CID_prod_drugstore_init_user_group
    binding-ybm-finish:
      topic: prod_binding_ybm_finish_topic
      group: CID_prod_binding_ybm_finish_user_group
    add-center-gray-menu:
      topic: prod_auto_add_gray_menu_topic
      group: CID_prod_auto_add_gray_menu_user_group
    drugstore-edit:
      topic: prod_drugstore_eidt_sync_purchaser_topic
      group: prod_CID_drugstore_eidt_sync_purchaser_group
rocketmq-ykq:
  name-server: mq01-saas-prod.rocketmq.ybm100.top:9876;mq02-saas-prod.rocketmq.ybm100.top:9876;mq03-saas-prod.rocketmq.ybm100.top:9876;mq04-saas-prod.rocketmq.ybm100.top:9876
  sendMessageTimeout: 3000
  #name-server: 127.0.0.1:9876
  #name-server: mq1-dev.rocketmq.ybm100.top:9876;mq2-dev.rocketmq.ybm100.top:9876;mq3-dev.rocketmq.ybm100.top:9876;mq4-dev.rocketmq.ybm100.top:9876
  producer:
    group: PID_saas_ykq_user_ms
    topic: saas-ykq_user-ms
rocketmq-auto-add-config:
  name-server: mq01-saas-prod.rocketmq.ybm100.top:9876;mq02-saas-prod.rocketmq.ybm100.top:9876;mq03-saas-prod.rocketmq.ybm100.top:9876;mq04-saas-prod.rocketmq.ybm100.top:9876
  sendMessageTimeout: 3000
  producer:
    group: PID_prod_auto_add_config_group
    topic: prod_auto_add_config_topic
audit:
  name-server: mq01-saas-prod.rocketmq.ybm100.top:9876;mq02-saas-prod.rocketmq.ybm100.top:9876;mq03-saas-prod.rocketmq.ybm100.top:9876;mq04-saas-prod.rocketmq.ybm100.top:9876
  sendMessageTimeout: 3000
  topic: t-xyy-workflow-eventlog_prod
  group: c-saas-workflow-eventlog-user_prod
app:
  id: xyy-saas-user-provider
apollo:
  bootstrap:
    enabled: true
  meta: http://node01-prod.appolo.ybm100.top:8080,http://node02-prod.appolo.ybm100.top:8080,http://node03-prod.appolo.ybm100.top:8080

mqtt:
  instanceId: mqtt-cn-4591jc1kt08
  addr: mqtt-cn-4591jc1kt08.mqtt.aliyuncs.com
  port: 1883
  ssladdr: mqtt-cn-4591jc1kt08.mqtt.aliyuncs.com
  sslport: 8883
  userName:
  pwd:
  clientId:
  groupId: GID_Drugstore_Prod
  accessKey: LTAI4FwpDDmZBh7sYWgUN1ZE
  multicast: SAAS-Drugstore-Prod/multicast
  org: SAAS-Drugstore-Prod/push2org/
  user: SAAS-Drugstore-Prod/push2user/

ecSms:
  privateKey: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAMtSrOXl5ZFvhoDqOilrD70oUFus1E0lLsabWuyvhFqivx+14a7cW6tFbAXAT+/afNrBa2j4OJnb4W7jlKmXpqA3ybHRLFG2/JIpDRv2HnvVX/OCUpZfu6gRq+nM6IVcgPe6ajmpm+x32w7ljDRgZ/Vfx48o5i5BlcNpOdqq1s/HAgMBAAECgYBxvmuZJTZ7WQ++2Mn217zzTjU+Q/50teNoGgsQs9PwPHF9VOvPPdlOhES9JuvRO8JzQpdLHunTMQ35Ufmdsof0xDmf3vq6dG+fxRKQoJTKFijMVQIuGI7jY+FNgVYkZs/yiTMYoxAmchukqJd/Sj/vdLj1dKhD4rOvRqMVDP2LKQJBAOvIbOinpwP2WAP/jmuxWLwfXicLGqH80lRpvSaBXghntBrQFG2gARa9Sx/YivTOTN1CcVbyfbvzo9U712qTJ+MCQQDcwbwNwHZHyUoNk0xQ5sSToPBP6kQbOHMkCxRlME7LRFDgD3BbrBW0oGUxRDGYWX952g67zF/KVleeFiHq8NXNAkA+eWPKHaoWVa6qmItPI7WkWxdidwG+jDPa5A2XZ03YIkpJlHrlNTE0qkF+laz0ArnBIrrPU/9Wzs7JUEra1VULAkAkVq/CA8B0qKnL+kdBufr54jAst39mi7BIapp83arawvAdMe1o6bbY+N4vce5ICdN2F3L9bvMFt5jMqRAEQIixAkEAj3k0CBoaRRvkaASxZCq41mYKmQWsWDmB2Isf+IC2s8im433ABls2lKhhsKBfcZil+fQMdxrfs68LjPkrq0b5Rg==
  accessKey: zhl
  valitionCode:
    templateCode: TP95
    sceneCode: TP11019
    ybmTemplateCode: TP121000
    ybmSceneCode: TP11019
  registerMerchant:
    templateCode: TP96
    sceneCode: TP11019
  merchantAuditingNotPass:
    templateCode: TP97
    sceneCode: TP11019
  jointVerifyCode:
    templateCode: TP95
    sceneCode: TP1227
#荷叶会员域名
lotus:
  host: https://saasheye-web.ybm100.com