<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.user.provider.module.dao.SaasKpiItemMapper">
    <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasKpiItemPo">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="code" property="code" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="unit" property="unit" jdbcType="VARCHAR"/>

        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="yn" property="yn" jdbcType="TINYINT"/>
    </resultMap>

    <insert id="insert" parameterType="com.xyy.user.provider.module.entity.SaasKpiItemPo" useGeneratedKeys="true"
            keyProperty="id">
    insert into saas_kpi_item (code, name, type, unit, create_user, create_time, update_user, update_time, yn)
    values (#{code,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{unit, jdbcType=VARCHAR},
    #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{yn,jdbcType=TINYINT})
  </insert>

    <update id="updateById" parameterType="com.xyy.user.provider.module.entity.SaasKpiItemPo">
        update saas_kpi_item
        <set>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="unit != null">
                unit = #{unit,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="yn != null">
                yn = #{yn,jdbcType=TINYINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectById" resultMap="BaseResultMap" parameterType="java.lang.String">
        select id, code, name, type, unit, create_user, create_time, update_user, update_time, yn
        from saas_kpi_item
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectSaasKpiItemList" resultMap="BaseResultMap">
        select id, code, name, type, unit, create_user, create_time, update_user, update_time, yn
        from saas_kpi_item where yn = 1
    </select>

</mapper>