<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.user.provider.module.dao.SaasPackageMapper">
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasPackage">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="biz_model" jdbcType="INTEGER" property="bizModel" />
    <result column="package_name" jdbcType="VARCHAR" property="packageName" />
    <result column="package_content" jdbcType="VARCHAR" property="packageContent" />
    <result column="create_user" jdbcType="INTEGER" property="createUser" />
    <result column="yn" jdbcType="TINYINT" property="yn" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="base_version" jdbcType="INTEGER" property="baseVersion" />
  </resultMap>

  <insert id="insert" keyProperty="id" useGeneratedKeys="true" parameterType="com.xyy.user.provider.module.entity.SaasPackage">
    insert into saas_package (biz_model, package_name, package_content, 
      create_user, yn, create_time)
    values (#{bizModel,jdbcType=INTEGER}, #{packageName,jdbcType=VARCHAR}, #{packageContent,jdbcType=VARCHAR},
      #{createUser,jdbcType=INTEGER}, #{yn,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP})
  </insert>

  <update id="updateByPrimaryKey" parameterType="com.xyy.user.provider.module.entity.SaasPackage">
    update saas_package
    <set>
      <if test="bizModel != null">
        biz_model = #{bizModel,jdbcType=INTEGER},
      </if>
      <if test="packageName != null">
        package_name = #{packageName,jdbcType=VARCHAR},
      </if>
      <if test="packageContent != null">
        package_content = #{packageContent,jdbcType=VARCHAR},
      </if>
      <if test="yn != null">
        yn = #{yn,jdbcType=TINYINT},
      </if>
      <if test="baseVersion != null">
        base_version = #{baseVersion,jdbcType=INTEGER},
      </if>
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select id, biz_model, package_name, package_content, create_user, yn, create_time, update_time, base_version
    from saas_package
    where id = #{id,jdbcType=INTEGER}
  </select>

  <select id="getPackageByName" parameterType="java.lang.String" resultMap="BaseResultMap">
    select id, biz_model, package_name, package_content, create_user, yn, create_time, update_time, base_version
    from saas_package
    where package_name = #{package_name,jdbcType=VARCHAR} limit 1
  </select>

  <select id="selectSaasPackageList" parameterType="com.xyy.user.provider.module.entity.SaasPackage" resultMap="BaseResultMap">
    select id, biz_model, package_name, package_content, create_user, yn, create_time, update_time
    from saas_package
    where yn = 1
    <if test="bizModel != null">
       and biz_model = #{bizModel,jdbcType=INTEGER}
    </if>
    <if test="createUser != null">
      and create_user = #{createUser,jdbcType=INTEGER}
    </if>

  </select>

  <select id="getPackageListByPage" parameterType="com.xyy.user.module.dto.SaasPackageListExtendDto" resultMap="BaseResultMap">
    select id, biz_model, package_name, package_content, create_user, yn, create_time, update_time
    from saas_package
    where yn = 1
    <if test="bizModel != null">
      and biz_model = #{bizModel,jdbcType=INTEGER}
    </if>
    <if test="createUser != null">
      and create_user = #{createUser,jdbcType=INTEGER}
    </if>
    <if test="packageName != null and packageName != '' ">
      and package_name like CONCAT(#{packageName},"%")
    </if>
    <if test="createUserIdList != null and createUserIdList.size>0">
      and create_user in
      <foreach collection="createUserIdList" item="id" index="index" open="(" separator="," close=")">
        #{id}
      </foreach>
    </if>
    <if test="startTime!= null and startTime!=''and endTime!=null and endTime!=''">
      AND create_time between #{startTime} and #{endTime}
    </if>
    order by create_time desc
  </select>
    <select id="getPackageByIds" resultType="com.xyy.user.provider.module.entity.SaasPackage">
      select id, biz_model, package_name, package_content, create_user, yn, create_time, update_time
      from saas_package
      where yn = 1 and id in
      <foreach collection="list" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </select>
</mapper>