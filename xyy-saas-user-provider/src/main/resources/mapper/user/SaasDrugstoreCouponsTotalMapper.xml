<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.user.provider.module.dao.SaasDrugstoreCouponsTotalMapper">
    <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasDrugstoreCouponsTotal">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="organ_sign" property="organSign" jdbcType="VARCHAR"/>
        <result column="contract_code" property="contractCode" jdbcType="VARCHAR"/>
        <result column="total_coupons_number" property="totalCouponsNumber" jdbcType="INTEGER"/>
        <result column="has_standards_number" property="hasStandardsNumber" jdbcType="INTEGER"/>
        <result column="has_coupons_number" property="hasCouponsNumber" jdbcType="INTEGER"/>
        <result column="residue_coupons_number" property="residueCouponsNumber" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="yn" property="yn" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
    `id`, `organ_sign`, `contract_code`, `total_coupons_number`, `has_standards_number`, `has_coupons_number`, `residue_coupons_number`, `create_time`, `update_time`
    </sql>

    <insert id="add" parameterType="com.xyy.user.provider.module.entity.SaasDrugstoreCouponsTotal"
            useGeneratedKeys="true" keyProperty="id">
    insert into saas_drugstore_coupons_total
    (
    organ_sign,
    contract_code,
    total_coupons_number,
    has_standards_number,
    has_coupons_number,
    residue_coupons_number,
    create_time
    )
    values
    (
    #{organSign, jdbcType=VARCHAR},
    #{contractCode, jdbcType=VARCHAR},
    #{totalCouponsNumber, jdbcType=INTEGER},
    #{hasStandardsNumber, jdbcType=DECIMAL},
    #{hasCouponsNumber, jdbcType=INTEGER},
    #{residueCouponsNumber, jdbcType=INTEGER},
    #{createTime, jdbcType=TIMESTAMP}
    )
  </insert>

    <select id="getRecordDetail" resultMap="BaseResultMap" parameterType="java.util.Map">
    select * from saas_drugstore_coupons_total
    where organ_sign =  #{organSign, jdbcType=VARCHAR} and  contract_code = #{contractCode, jdbcType=VARCHAR}
    and yn = 0
  </select>

    <select id="getRecordByCondition" parameterType="java.util.Map" resultMap="BaseResultMap">
        select *
        from saas_drugstore_coupons_total
        <where>
            <if test="organSign != null and organSign!=''">
                organ_sign = #{organSign, jdbcType=VARCHAR}
            </if>
            <if test="contractCode != null and contractCode!=''">
                and contract_code = #{contractCode, jdbcType=VARCHAR}
            </if>
            and yn = 0
        </where>
        order by organ_sign
    </select>

    <update id="edit">
        update saas_drugstore_coupons_total
        <set>
            <if test="newContractCode != null and newContractCode !=''">
                contract_code = #{newContractCode, jdbcType=VARCHAR},
            </if>
            <if test="totalCouponsNumber != null">
                total_coupons_number = #{totalCouponsNumber, jdbcType=INTEGER},
            </if>
            <if test="hasStandardsNumber != null">
                has_standards_number = #{hasStandardsNumber, jdbcType=INTEGER},
            </if>
            <if test="hasCouponsNumber != null">
                has_coupons_number = #{hasCouponsNumber, jdbcType=INTEGER},
            </if>
            <if test="residueCouponsNumber != null ">
                residue_coupons_number = #{residueCouponsNumber, jdbcType=INTEGER}
            </if>
        </set>
        where organ_sign = #{organSign, jdbcType=VARCHAR}
        <if test="contractCode != null and contractCode !=''">
            and contract_code = #{contractCode, jdbcType=VARCHAR}
        </if>
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select * from saas_drugstore_coupons_total
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectSaasDrugstoreCouponsTotalsByOrganSigns" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select * from saas_drugstore_coupons_total
        where yn = 0 and organ_sign in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <update id="updatePOByPrimaryKey" parameterType="com.xyy.user.provider.module.entity.SaasDrugstoreCouponsTotal">
        update saas_drugstore_coupons_total
        <set>
            <if test="hasCouponsNumber != null">
                has_coupons_number = #{hasCouponsNumber, jdbcType=INTEGER},
            </if>
            <if test="residueCouponsNumber != null ">
                residue_coupons_number = #{residueCouponsNumber, jdbcType=INTEGER}
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="editContractCode">
        update saas_drugstore_coupons_total
        <set>
            contract_code = #{contractCode, jdbcType=VARCHAR}
        </set>
        where organ_sign = #{organSign, jdbcType=VARCHAR}
    </update>

    <update id="updateYnDisabled">
        update saas_drugstore_coupons_total
        set yn = 1
        <where>
            organ_sign = #{organSign, jdbcType=VARCHAR}
            <if test="contractCode != null and contractCode!=''">
                and contract_code = #{contractCode, jdbcType=VARCHAR}
            </if>
        </where>
    </update>
    <select id="getRecordByOrganSignAndContactCodeForceMaster" resultMap="BaseResultMap">
      /*FORCE_MASTER*/
      SELECT <include refid="Base_Column_List"/>
      FROM saas_drugstore_coupons_total
      WHERE organ_sign = #{organSign,jdbcType=VARCHAR} AND contract_code = #{contractCode,jdbcType=VARCHAR} AND yn = 0
    </select>

</mapper>