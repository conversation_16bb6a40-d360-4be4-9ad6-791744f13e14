<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.user.provider.module.dao.SaasCouponsDataMapper" >
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasCouponsData" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="province_code" property="provinceCode" jdbcType="VARCHAR" />
    <result column="province_name" property="provinceName" jdbcType="VARCHAR" />
    <result column="template_id" property="templateId" jdbcType="BIGINT" />
    <result column="voucher_type" property="voucherType" jdbcType="TINYINT" />
    <result column="receive_type" property="receiveType" jdbcType="TINYINT" />
    <result column="merchant_type" property="merchantType" jdbcType="TINYINT" />
    <result column="min_money_to_enable" property="minMoneyToEnable" jdbcType="DECIMAL" />
    <result column="moneyIn_voucher" property="moneyInVoucher" jdbcType="DECIMAL" />
    <result column="voucher_title" property="voucherTitle" jdbcType="VARCHAR" />
    <result column="total_limit_qty" property="totalLimitQty" jdbcType="INTEGER" />
    <result column="valid_days" property="validDays" jdbcType="INTEGER" />
  </resultMap>

  <select id="getDetail" resultMap="BaseResultMap" parameterType="java.util.Map" >
    select * from saas_coupons_data
    <where>
      <if test="provinceCode!=null">
        province_code =  #{provinceCode, jdbcType=INTEGER}
      </if><if test="provinceName!=null and provinceName!=''">
      and  province_name LIKE CONCAT(#{provinceName, jdbcType=VARCHAR},'%')
    </if>

    </where> limit 1
  </select>


</mapper>