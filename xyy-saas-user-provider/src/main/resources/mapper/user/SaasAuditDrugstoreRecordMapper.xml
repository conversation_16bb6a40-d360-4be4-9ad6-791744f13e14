<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.user.provider.module.dao.SaasAuditDrugstoreRecordMapper" >
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasAuditDrugstoreRecord" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="organ_sign" property="organSign" jdbcType="VARCHAR" />
    <result column="initiator_user_id" property="initiatorUserId" jdbcType="INTEGER" />
    <result column="drugstore_name" property="drugstoreName" jdbcType="VARCHAR" />
    <result column="manager_name" property="managerName" jdbcType="VARCHAR" />
    <result column="contact_phone" property="contactPhone" jdbcType="VARCHAR" />
    <result column="province" property="province" jdbcType="VARCHAR" />
    <result column="city" property="city" jdbcType="VARCHAR" />
    <result column="area" property="area" jdbcType="VARCHAR" />
    <result column="area_code" property="areaCode" jdbcType="VARCHAR" />
    <result column="address" property="address" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="TINYINT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="audit_type" property="auditType" jdbcType="TINYINT" />
    <result column="audit_user_id" property="auditUserId" jdbcType="INTEGER" />
    <result column="audit_real_name" property="auditRealName" jdbcType="VARCHAR" />
    <result column="audit_status" property="auditStatus" jdbcType="TINYINT" />
    <result column="audit_remark" property="auditRemark" jdbcType="VARCHAR" />
    <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP" />
    <result column="business_license_name" property="businessLicenseName" jdbcType="VARCHAR" />
    <result column="business_license_number" property="businessLicenseNumber" jdbcType="VARCHAR" />
    <result column="business_license_img" property="businessLicenseImg" jdbcType="VARCHAR" />
    <result column="pharmaceutical_trading_license_img" property="pharmaceuticalTradingLicenseImg" jdbcType="VARCHAR" />
    <result column="quality_management_license_img" property="qualityManagementLicenseImg" jdbcType="VARCHAR" />
    <result column="operator_user_id" property="operatorUserId" jdbcType="INTEGER" />
    <result column="organ_sign_type" property="organSignType" jdbcType="TINYINT" />
    <result column="biz_model" property="bizModel" jdbcType="TINYINT" />
    <result column="headquarters_organ_sign" property="headquartersOrganSign" jdbcType="VARCHAR" />
    <result column="storehouse_yn" property="storehouseYn" jdbcType="TINYINT" />
  </resultMap>


  <select id="getLatestAuditRecordList" parameterType="com.xyy.user.module.dto.SaasAuditDrugstoreRecordDto" resultType="com.xyy.user.module.dto.SaasAuditDrugstoreRecordDto">
    select a.* from saas_audit_drugstore_record a, (select max(id) mid from saas_audit_drugstore_record group by organ_sign order by id desc) b 
    where a.id = b.mid
    <if test="headquartersOrganSign != null and headquartersOrganSign != ''">
      and a.headquarters_organ_sign = #{headquartersOrganSign,jdbcType=VARCHAR}
    </if>
    <if test="organSign != null and organSign != ''">
      and a.organ_sign = #{organSign,jdbcType=VARCHAR}
    </if>
    <if test="organSignList != null  and organSignList.size > 0">
      and a.organ_sign in
      <foreach collection="organSignList" item="item" index="index" open="(" separator="," close=")">
        #{item,jdbcType=VARCHAR}
      </foreach>
    </if>
    <if test="drugstoreName != null and drugstoreName != ''">
      and a.drugstore_name LIKE CONCAT('%',#{drugstoreName, jdbcType=VARCHAR},'%')
    </if>
    <if test="auditStatus != null">
      and a.audit_status != #{auditStatus}
    </if>
    order by a.create_time desc 
  </select>
  
  <select id="getLatestAuditRecord" parameterType="com.xyy.user.module.dto.DrugstoreAuditDto" resultMap="BaseResultMap">
    select d.*, m.operator_user_id
    from saas_audit_drugstore_record d
    LEFT JOIN saas_account_maintain m on m.user_id = d.initiator_user_id 
    <where>
      1 = 1 
      <if test="organSign != null and organSign != ''">
        and d.organ_sign = #{organSign,jdbcType=VARCHAR}
      </if>
      <if test="initiatorUserId != null">
        and d.initiator_user_id = #{initiatorUserId,jdbcType=INTEGER}
      </if>
      <if test="drugstoreName != null and drugstoreName != ''">
        and d.drugstore_name = #{drugstoreName,jdbcType=VARCHAR}
      </if>
    </where>
      order by id desc limit 1
  </select>
  
  <select id="getAuditDrugstoreList" parameterType="com.xyy.user.module.dto.SaasAuditDrugstoreRecordDto" resultType="com.xyy.user.module.dto.SaasAuditDrugstoreRecordDto">
    select d.*, m.operator_user_id, m.referral_code 
    from saas_audit_drugstore_record d
    LEFT JOIN saas_account_maintain m on m.user_id = d.initiator_user_id
    <where>
      <if test="id != null">
        and d.id = #{id, jdbcType=INTEGER} 
      </if>
      <if test="contactPhone != null and contactPhone!=''">
        and d.contact_phone like concat(concat('%',#{contactPhone}),'%')
      </if>
      <if test="auditStatus != null and auditStatus!=-1">
        and d.audit_status=#{auditStatus}
      </if>
      <if test="organSign != null and organSign != ''">
        and d.organ_sign = #{organSign,jdbcType=VARCHAR}
      </if>
      <if test="drugstoreName != null and drugstoreName!=''">
        and d.drugstore_name LIKE CONCAT('%',#{drugstoreName, jdbcType=VARCHAR},'%')
      </if>
	  <if test="headquartersOrganSign != null and headquartersOrganSign != ''">
         and d.headquarters_organ_sign LIKE CONCAT('%',#{headquartersOrganSign, jdbcType=VARCHAR},'%')
      </if>
      <if test="organSignType != null">
         and d.organ_sign_type = #{organSignType, jdbcType=TINYINT}
      </if>
      <if test="bizModel != null">
         and d.biz_model = #{bizModel, jdbcType=TINYINT}
      </if>
      <if test="neBizModel != null">
         and d.biz_model != #{neBizModel, jdbcType=TINYINT}
      </if>
      <if test="auditType != null and auditType!=-1">
         and d.audit_type=#{auditType}
      </if>
      <if test="createTimeStartStr != null and createTimeStartStr !=''">
         and d.create_time >= #{createTimeStartStr}
      </if>
      <if test="createTimeEndStr != null and createTimeEndStr !=''">
         and d.create_time &lt;= #{createTimeEndStr}
      </if>
      <if test="auditTimeStartStr != null and auditTimeStartStr !=''">
         and d.audit_time >= #{auditTimeStartStr}
      </if>
      <if test="auditTimeEndStr != null and auditTimeEndStr !=''">
         and d.audit_time &lt;= #{auditTimeEndStr}
      </if>
      <if test="storehouseYn != null">
        and d.storehouse_yn = #{storehouseYn, jdbcType=TINYINT}
      </if>
    </where>
      order by d.create_time desc
  </select>

<!-- 分割线分割线分割线分割线分割线分割线分割线分割线分割线分割线  华丽的分割线，分隔过去和将来  分割线分割线分割线分割线分割线分割线分割线分割线分割线分割线分割线分割线 -->

  <insert id="insert" parameterType="com.xyy.user.provider.module.entity.SaasAuditDrugstoreRecord" useGeneratedKeys="true" keyProperty="id" >
    insert into saas_audit_drugstore_record
    (
    organ_sign,
    initiator_user_id,
    drugstore_name,
    manager_name,
    contact_phone,
    province,
    city,
    area,
    area_code,
    address,
    type,
    create_time,
    audit_type,
    audit_user_id,
    audit_real_name,
    audit_status,
    audit_remark,
    audit_time,
    business_license_name,
    business_license_number,
    business_license_img,
    pharmaceutical_trading_license_img,
    quality_management_license_img,
    organ_sign_type,
    biz_model,
    headquarters_organ_sign,
    storehouse_yn
    )
    values
    (
    #{organSign,jdbcType=VARCHAR},
    #{initiatorUserId,jdbcType=INTEGER},
    #{drugstoreName,jdbcType=VARCHAR},
    #{managerName,jdbcType=VARCHAR},
    #{contactPhone,jdbcType=VARCHAR},
    #{province,jdbcType=VARCHAR},
    #{city,jdbcType=VARCHAR},
    #{area,jdbcType=VARCHAR},
    #{areaCode,jdbcType=VARCHAR},
    #{address,jdbcType=VARCHAR},
    #{type,jdbcType=TINYINT},
    #{createTime,jdbcType=TIMESTAMP},
    #{auditType,jdbcType=TINYINT},
    #{auditUserId,jdbcType=INTEGER},
    #{auditRealName,jdbcType=VARCHAR},
    #{auditStatus,jdbcType=TINYINT},
    #{auditRemark,jdbcType=VARCHAR},
    #{auditTime,jdbcType=TIMESTAMP},
    #{businessLicenseName,jdbcType=VARCHAR},
    #{businessLicenseNumber,jdbcType=VARCHAR},
    #{businessLicenseImg,jdbcType=VARCHAR},
    #{pharmaceuticalTradingLicenseImg,jdbcType=VARCHAR},
    #{qualityManagementLicenseImg,jdbcType=VARCHAR},
    #{organSignType, jdbcType=TINYINT},
    #{bizModel, jdbcType=TINYINT},
    #{headquartersOrganSign, jdbcType=VARCHAR},
    #{storehouseYn, jdbcType=TINYINT}
    )
  </insert>
  <update id="updateRecordAuditStatus" parameterType="com.xyy.user.provider.module.entity.SaasAuditDrugstoreRecord" >
    update saas_audit_drugstore_record set
      audit_user_id = #{info.auditUserId, jdbcType=INTEGER},
      audit_real_name = #{info.auditRealName, jdbcType=VARCHAR},
      audit_status = #{info.auditStatus, jdbcType=TINYINT},
      audit_remark = #{info.auditRemark, jdbcType=VARCHAR},
      audit_time = #{info.auditTime, jdbcType=TIMESTAMP}
    where id = #{info.recordId, jdbcType=INTEGER}
  </update>

  <update id="updateDrugstoreRecord" parameterType="com.xyy.user.provider.module.entity.SaasAuditDrugstoreRecord" >
    update saas_audit_drugstore_record set
      manager_name = #{managerName, jdbcType=VARCHAR},
      drugstore_name = #{drugstoreName, jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      area = #{area,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      audit_status = 1,
      business_license_img = #{businessLicenseImg,jdbcType=VARCHAR},
      pharmaceutical_trading_license_img = #{pharmaceuticalTradingLicenseImg,jdbcType=VARCHAR},
      quality_management_license_img = #{qualityManagementLicenseImg,jdbcType=VARCHAR}
    where id = #{recordId, jdbcType=INTEGER}
  </update>

  <select id="selectAuditDrugstoreRecordById" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select id, organ_sign, initiator_user_id, drugstore_name, manager_name, contact_phone, 
    province, city, area, address, type, create_time, audit_type,
    audit_user_id, audit_real_name, audit_status, audit_remark, audit_time, business_license_name, 
    business_license_number, business_license_img, pharmaceutical_trading_license_img, quality_management_license_img, organ_sign_type,
    biz_model,headquarters_organ_sign,
    quality_management_license_img,area_code,storehouse_yn
    from saas_audit_drugstore_record
    where id = #{id,jdbcType=INTEGER}
  </select>

  <select id="countRecordAuditing" parameterType="java.lang.String" resultType="java.lang.Integer">
    select IFNULL(count(0), 0) from saas_audit_drugstore_record where organ_sign = #{organSign, jdbcType=VARCHAR} and audit_status = 1
  </select>

  <select id="selectRecordsByUserId" parameterType="java.lang.Integer" resultType="com.xyy.user.module.dto.result.QueryAuditRecordVO">
    select id as recordId, create_time as createTime,
    case when audit_type = 1 then '资质更新'
    when audit_type = 2 then '资质提交'
    when audit_type = 3 then '药帮忙资质同步' END AS audit_type,
    case when audit_status = 1 then '审核中'
    when audit_status = 2 then '审核通过'
    when audit_status = 3 then '审核不通过' END AS auditStatus,
    IFNULL(audit_remark, '') as auditRemark
    from saas_audit_drugstore_record where initiator_user_id = #{userId, jdbcType=INTEGER}
  </select>

  <select id="selectBusinessLicenseNumberAuditing" parameterType="java.lang.String" resultType="com.xyy.user.provider.module.entity.SaasAuditDrugstoreRecord">
    select * from saas_audit_drugstore_record where business_license_number = #{number, jdbcType=VARCHAR} and audit_status = 1
  </select>

  <select id="countRecordAuditingByUserId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
    select IFNULL(count(0), 0) from saas_audit_drugstore_record where initiator_user_id = #{userId, jdbcType=INTEGER} and audit_status = 1
  </select>

  <select id="findAuditPageList" parameterType="com.xyy.user.provider.module.vo.DrugstoreAuditVO" resultMap="BaseResultMap">
    select d.*, m.operator_user_id
    from saas_audit_drugstore_record d
    LEFT JOIN saas_account_maintain m on m.user_id = d.initiator_user_id
    <where>
       (d.biz_model = 1 or d.biz_model is null) 
      <if test="contactPhone != null and contactPhone!=''">
        and d.contact_phone like concat(concat('%',#{contactPhone}),'%')
      </if>
      <if test="auditStatus != null and auditStatus!=-1">
        and d.audit_status=#{auditStatus}
      </if>
      <if test="drugstoreName != null and drugstoreName!=''">
        and d.drugstore_name like CONCAT("%", #{drugstoreName},"%")
      </if>
      <if test="province != null and province!=''">
        and d.province like  CONCAT("%", #{province},"%")
      </if>
      <if test="provinceList!=null and provinceList.size() > 0 ">
        and d.province in
        <foreach collection="provinceList" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="auditType != null and auditType!=-1">
        and d.audit_type=#{auditType}
      </if>
      <if test="startTime != null and startTime!=''">
        <![CDATA[ and d.create_time >= #{startTime}]]>
      </if>
      <if test="endTime != null and endTime!=''">
        <![CDATA[ and d.create_time <= #{endTime}]]>
      </if>
    </where>
      order by d.create_time desc
  </select>
  <select id="findAuditDrugstoreRecordById" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select d.*
    from saas_audit_drugstore_record d
    where d.id=#{id}
  </select>

  <select id="selectLastRecordByManagerId" parameterType="java.lang.Integer" resultType="com.xyy.user.provider.module.entity.SaasAuditDrugstoreRecord">
    select * from saas_audit_drugstore_record where initiator_user_id = #{initiatorUserId, jdbcType=INTEGER} order by create_time desc limit 0, 1
  </select>
</mapper>