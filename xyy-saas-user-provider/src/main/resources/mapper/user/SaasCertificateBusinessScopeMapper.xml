<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.user.provider.module.dao.SaasCertificateBusinessScopeMapper" >
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasCertificateBusinessScope" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="certificate_type" property="certificateType" jdbcType="INTEGER" />
    <result column="business_scope" property="businessScope" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from saas_certificate_business_scope
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.xyy.user.provider.module.entity.SaasCertificateBusinessScope" useGeneratedKeys="true" keyProperty="id" >
    insert into saas_certificate_business_scope (certificate_type, business_scope, create_time, 
      update_time)
    values (#{certificateType,jdbcType=INTEGER}, #{businessScope,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.xyy.user.provider.module.entity.SaasCertificateBusinessScope" >
    update saas_certificate_business_scope
    set certificate_type = #{certificateType,jdbcType=INTEGER},
      business_scope = #{businessScope,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select id, certificate_type, business_scope, create_time, update_time
    from saas_certificate_business_scope
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectAll" resultMap="BaseResultMap" >
    select id, certificate_type, business_scope, create_time, update_time
    from saas_certificate_business_scope
  </select>
  <select id="selectByIds" resultMap="BaseResultMap">
    select id, certificate_type, business_scope, create_time, update_time
    from saas_certificate_business_scope
    where business_scope in
    <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
    <if test="types != null and types.size >0 ">
      and certificate_type in
      <foreach collection="types" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>

  <select id="selectByTypes" resultMap="BaseResultMap">
    select id, certificate_type, business_scope, create_time, update_time
    from saas_certificate_business_scope
    where certificate_type in
    <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
</mapper>