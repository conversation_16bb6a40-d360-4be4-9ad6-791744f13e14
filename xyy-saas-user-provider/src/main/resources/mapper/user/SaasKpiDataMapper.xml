<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.user.provider.module.dao.SaasKpiDataMapper" >
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasKpiDataPo" >
    <result column="organ_sign" property="organSign" jdbcType="VARCHAR" />
    <result column="order_month_count" property="orderMonthCount" jdbcType="INTEGER" />
    <result column="cycle" property="cycle" jdbcType="VARCHAR" />
    <result column="produce_time" property="produceTime" jdbcType="BIGINT" />

    <result column="create_user" property="createUser" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="BIGINT" />
    <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="BIGINT" />
    <result column="yn" property="yn" jdbcType="TINYINT" />
  </resultMap>

  <sql id="Base_Column_List">
    organ_sign, order_month_count, cycle,
    create_user, create_time, update_user, update_time, yn
  </sql>

  <insert id="insert" parameterType="com.xyy.user.provider.module.entity.SaasKpiDataPo" useGeneratedKeys="true" keyProperty="id" >
    insert into saas_kpi_data (organ_sign, order_month_count, val_type, cycle, produce_time, create_user, create_time, update_user, update_time, yn)
    values (#{organSign,jdbcType=VARCHAR}, #{orderMonthCount,jdbcType=VARCHAR}, #{valType,jdbcType=VARCHAR}, #{cycle, jdbcType=VARCHAR}, #{produceTime, jdbcType=BIGINT},
    #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=BIGINT}, #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=BIGINT}, #{yn,jdbcType=TINYINT})
  </insert>

  <select id="selectKpiDataRangeList" resultMap="BaseResultMap" parameterType="java.util.Map" >
    select
    <include refid="Base_Column_List"/>
    from saas_kpi_data
    <where>
      <if test="organSign != null">
        and organ_sign = #{organSign}
      </if>
      <if test="orderMonthCount != null">
        and order_month_count = #{orderMonthCount}
      </if>
      <if test="cycle != null">
        and cycle = #{cycle}
      </if>
      <if test="createTimeStart != null">
        and create_time >= #{createTimeStart}
      </if>
      <if test="yn != null">
        and yn = #{yn}
      </if>
    </where>
  </select>

</mapper>