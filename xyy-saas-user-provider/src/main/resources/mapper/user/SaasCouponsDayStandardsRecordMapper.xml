<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.user.provider.module.dao.SaasCouponsDayStandardsRecordMapper" >
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasCouponsDayStandardsRecord" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="date" property="date" jdbcType="VARCHAR" />
    <result column="organ_sign" property="organSign" jdbcType="VARCHAR" />
    <result column="contract_code" property="contractCode" jdbcType="VARCHAR" />
    <result column="day_order_num" property="dayOrderNum" jdbcType="INTEGER" />
    <result column="day_order_amount" property="dayOrderAmount" jdbcType="DECIMAL" />
    <result column="day_purchase_num" property="dayPurchaseNum" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="data_time" property="dataTime" jdbcType="TIMESTAMP" />
  </resultMap>
  
  <insert id="add" parameterType="com.xyy.user.provider.module.entity.SaasCouponsDayStandardsRecord" useGeneratedKeys="true" keyProperty="id" >
    insert into saas_coupons_day_standards_record
    (
    date,
    organ_sign,
    contract_code,
    day_order_num,
    day_order_amount,
    day_purchase_num,
    create_time,
    data_time
    )
    values
    (
    #{date, jdbcType=VARCHAR},
    #{organSign, jdbcType=VARCHAR},
    #{contractCode, jdbcType=VARCHAR},
    #{dayOrderNum, jdbcType=INTEGER},
    #{dayOrderAmount, jdbcType=DECIMAL},
    #{dayPurchaseNum, jdbcType=INTEGER},
    #{createTime, jdbcType=TIMESTAMP},
    #{dataTime, jdbcType=TIMESTAMP}
    )
  </insert>

  <select id="getCouponsDayStandardsRecord" resultMap="BaseResultMap" parameterType="java.util.Map" >
    select * from saas_coupons_day_standards_record
    where organ_sign =  #{organSign, jdbcType=VARCHAR} and  contract_code = #{contractCode, jdbcType=VARCHAR} and date = #{date, jdbcType=VARCHAR}
  </select>
  <!--有效天数-->
  <select id="getCountCouponsDayNum" resultType="java.lang.Integer" parameterType="java.util.Map" >
    SELECT count(day_order_num) dayNum from saas_coupons_day_standards_record where organ_sign =  #{organSign, jdbcType=VARCHAR} and  contract_code = #{contractCode, jdbcType=VARCHAR} and day_order_num >= #{dayNum, jdbcType=INTEGER} and
     data_time BETWEEN #{startTime, jdbcType=TIMESTAMP} and #{endTime, jdbcType=TIMESTAMP}
  </select>

  <!--有效总天数-->
  <select id="getSumCouponsDayTotal" resultType="java.lang.Integer" parameterType="java.util.Map" >
    SELECT sum(day_order_num) dayTotal   from saas_coupons_day_standards_record where organ_sign =  #{organSign, jdbcType=VARCHAR} and  contract_code = #{contractCode, jdbcType=VARCHAR}  and
     data_time BETWEEN #{startTime, jdbcType=TIMESTAMP} and #{endTime, jdbcType=TIMESTAMP}
  </select>

  <!--订单总金额，总入库单-->
  <select id="getSumOrderAmountAndPurchaseTotal" resultType="java.util.Map" parameterType="java.util.Map" >
    SELECT sum(day_order_amount) orderAmountTotal,sum(day_purchase_num)  purchaseTotal from saas_coupons_day_standards_record where organ_sign =  #{organSign, jdbcType=VARCHAR} and  contract_code = #{contractCode, jdbcType=VARCHAR}  and
     data_time BETWEEN #{startTime, jdbcType=TIMESTAMP} and #{endTime, jdbcType=TIMESTAMP}
  </select>

  <select id="getDayStandardsRecordByCondition" parameterType="java.util.Map" resultMap="BaseResultMap">
    select t.* 
    from saas_coupons_day_standards_record  t
    <where>
      1 = 1
      <if test="employeeId != null and employeeId!=''">
        AND t.employee_id = #{employeeId, jdbcType=INTEGER} 
      </if>
      <if test="billStartDate != null and billStartDate != ''">
       <![CDATA[  AND t.bill_date  >= #{billStartDate, jdbcType=VARCHAR} ]]> 
      </if>
      <if test="billEndDate != null and billEndDate != ''">
      <![CDATA[  AND t.bill_date  <= #{billEndDate, jdbcType=VARCHAR} ]]>
      </if>
      and t.organ_sign = #{organSign, jdbcType=VARCHAR} and t.is_del = 0
    </where>
  </select>

   <update id="edit">
    update saas_coupons_day_standards_record
    set
    day_order_num = #{dayOrderNum, jdbcType=INTEGER},
    day_order_amount = #{dayOrderAmount, jdbcType=DECIMAL},
    day_purchase_num = #{dayPurchaseNum, jdbcType=INTEGER}
    where organ_sign =  #{organSign, jdbcType=VARCHAR} and  contract_code = #{contractCode, jdbcType=VARCHAR} and date = #{date, jdbcType=VARCHAR}
  </update>

</mapper>