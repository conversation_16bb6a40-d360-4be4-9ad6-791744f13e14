<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.user.provider.module.dao.sms.SaasSmsRecordTaskRelationDao">

    <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.sms.SaasSmsRecordTaskRelation">
        <!--@Table saas_sms_record_task_relation-->
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="smsRecordId" column="sms_record_id" jdbcType="INTEGER"/>
        <result property="smsChannelId" column="sms_channel_id" jdbcType="INTEGER"/>
        <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="organSign" column="organ_sign" jdbcType="VARCHAR"/>
        <result property="telephone" column="telephone" jdbcType="VARCHAR"/>
        <result property="memberId" column="member_id" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
        select
          id, sms_record_id, sms_channel_id, task_id, status, organ_sign, telephone, member_id, create_time
        from xyy_saas_user.saas_sms_record_task_relation
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BaseResultMap">
        select
          id, sms_record_id, sms_channel_id, task_id, status, organ_sign, telephone, member_id, create_time
        from xyy_saas_user.saas_sms_record_task_relation
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" parameterType="com.xyy.user.provider.module.entity.sms.SaasSmsRecordTaskRelation" resultMap="BaseResultMap">
        select
          id, sms_record_id, sms_channel_id, task_id, status, organ_sign, telephone, member_id, create_time
        from xyy_saas_user.saas_sms_record_task_relation
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="smsRecordId != null">
                and sms_record_id = #{smsRecordId}
            </if>
            <if test="smsChannelId != null">
                and sms_channel_id = #{smsChannelId}
            </if>
            <if test="taskId != null and taskId != ''">
                and task_id = #{taskId}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="organSign != null and organSign != ''">
                and organ_sign = #{organSign}
            </if>
            <if test="telephone != null and telephone != ''">
                and telephone = #{telephone}
            </if>
            <if test="memberId != null">
                and member_id = #{memberId}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
        </where>
    </select>

    <select id="queryByParam" parameterType="com.xyy.user.module.dto.sms.SaasSmsRecordTaskRelationParamDto" resultMap="BaseResultMap">
        select
          id, sms_record_id, sms_channel_id, task_id, status, organ_sign, telephone, member_id, create_time
        from xyy_saas_user.saas_sms_record_task_relation
        <where>
            <if test="smsChannelId != null">
                and sms_channel_id = #{smsChannelId}
            </if>
            <if test="taskIdList != null and taskIdList.size > 0">
                and task_id in (
                <foreach collection="taskIdList" item="taskId" separator=",">
                    #{taskId}
                </foreach>
                )
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into xyy_saas_user.saas_sms_record_task_relation(sms_record_id, sms_channel_id, task_id, status, organ_sign, telephone, member_id, create_time)
        values (#{smsRecordId}, #{smsChannelId}, #{taskId}, #{status}, #{organSign}, #{telephone}, #{memberId}, #{createTime})
    </insert>

    <insert id="batchInsertOrUpdate" keyProperty="id" useGeneratedKeys="true">
        insert into xyy_saas_user.saas_sms_record_task_relation(sms_record_id, sms_channel_id, task_id, status, organ_sign, telephone, member_id, create_time)
        values
        <foreach collection="list" item="record" separator=",">
            (#{record.smsRecordId}, #{record.smsChannelId}, #{record.taskId}, #{record.status}, #{record.organSign}, #{record.telephone}, #{record.memberId}, #{record.createTime})
        </foreach>
        ON DUPLICATE KEY UPDATE
        sms_record_id = VALUES(sms_record_id),
        sms_channel_id = VALUES(sms_channel_id),
        task_id = VALUES(task_id),
        status = VALUES(status),
        organ_sign = VALUES(organ_sign),
        telephone = VALUES(telephone),
        member_id = VALUES(member_id),
        create_time = VALUES(create_time)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update xyy_saas_user.saas_sms_record_task_relation
        <set>
            <if test="smsRecordId != null">
                sms_record_id = #{smsRecordId},
            </if>
            <if test="smsChannelId != null">
                sms_channel_id = #{smsChannelId},
            </if>
            <if test="taskId != null and taskId != ''">
                task_id = #{taskId},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="organSign != null and organSign != ''">
                organ_sign = #{organSign},
            </if>
            <if test="telephone != null and telephone != ''">
                telephone = #{telephone},
            </if>
            <if test="memberId != null">
                member_id = #{memberId},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from xyy_saas_user.saas_sms_record_task_relation where id = #{id}
    </delete>

</mapper>