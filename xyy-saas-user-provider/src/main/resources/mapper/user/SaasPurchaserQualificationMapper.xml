<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.user.provider.module.dao.SaasPurchaserQualificationMapper">
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasPurchaserQualification">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="purchaser_no" jdbcType="VARCHAR" property="purchaserNo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="certificate_type" jdbcType="TINYINT" property="certificateType" />
    <result column="certificate_no" jdbcType="VARCHAR" property="certificateNo" />
    <result column="certificate_date" jdbcType="TIMESTAMP" property="certificateDate" />
    <result column="expiry_date_date" jdbcType="TIMESTAMP" property="expiryDateDate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, purchaser_no, create_time, update_time, certificate_type, certificate_no, certificate_date, 
    expiry_date_date
  </sql>
  <sql id="DuplicateKey_Update_Values">
    purchaser_no = values (purchaser_no),
    create_time = values (create_time),
    update_time = values (update_time),
    certificate_type = values (certificate_type),
    certificate_no = values (certificate_no),
    certificate_date = values (certificate_date),
    expiry_date_date = values (expiry_date_date)
  </sql>

  <insert id="batchInsert">
    insert into saas_purchaser_qualification (purchaser_no, create_time, update_time,
    certificate_type, certificate_no, certificate_date, expiry_date_date)
    values
    <foreach collection="records" item="item" index="index" separator=",">
      (#{item.purchaserNo,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP},
      #{item.certificateType,jdbcType=TINYINT}, #{item.certificateNo,jdbcType=VARCHAR}, #{item.certificateDate,jdbcType=TIMESTAMP},
      #{item.expiryDateDate,jdbcType=TIMESTAMP})
    </foreach>

    ON DUPLICATE KEY UPDATE
    <include refid="DuplicateKey_Update_Values"/>
  </insert>

  <delete id="delByPurchaserNo">
    delete from saas_purchaser_qualification
    where purchaser_no = #{record.purchaserNo,jdbcType=VARCHAR}
  </delete>

  <select id="selectByPurchaserNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from saas_purchaser_qualification
    where purchaser_no = #{purchaserNo}
  </select>

  <select id="selectByPurchaserByNoList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from saas_purchaser_qualification
    where purchaser_no in
    <foreach collection="purchaserNos" item="purchaserNo" open="(" close=")" separator=",">
      #{purchaserNo}
    </foreach>
  </select>
  
  <select id="selectByNoAndType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from saas_purchaser_qualification
    where purchaser_no = #{purchaserNo} and certificate_type = #{certificateType}
  </select>
</mapper>