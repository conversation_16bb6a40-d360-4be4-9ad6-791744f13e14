<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.user.provider.module.dao.SaasPurchaserBusinessScopeMapper" >
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasPurchaserBusinessScope" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="purchaser_no" property="purchaserNo" jdbcType="VARCHAR" />
    <result column="certificate_type" property="certificateType" jdbcType="INTEGER" />
    <result column="business_scope_id" property="businessScopeId" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="DuplicateKey_Update_Values">
    purchaser_no = VALUES(purchaser_no),
    certificate_type = values (certificate_type),
    business_scope_id = values (business_scope_id),
    create_time = values (create_time),
    update_time = values (update_time)
  </sql>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from saas_purchaser_business_scope
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="delByIds">
    delete from saas_purchaser_business_scope
    where id in
    <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>
  <delete id="delByPurchaserNo">
    delete from saas_purchaser_business_scope
    where purchaser_no = #{record.purchaserNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.xyy.user.provider.module.entity.SaasPurchaserBusinessScope" useGeneratedKeys="true" keyProperty="id" >
    insert into saas_purchaser_business_scope (purchaser_no,certificate_type, business_scope_id, create_time,
      update_time)
    values (#{purchaserNo,jdbcType=VARCHAR}, #{certificateType,jdbcType=INTEGER} ,#{businessScopeId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>

  <update id="updateByPrimaryKey" parameterType="com.xyy.user.provider.module.entity.SaasPurchaserBusinessScope" >
    update saas_purchaser_business_scope
    set purchaser_no = #{purchaserNo,jdbcType=VARCHAR},
    certificate_type = #{certificateType,jdbcType=INTEGER},
      business_scope_id = #{businessScopeId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select id, purchaser_no, certificate_type,business_scope_id, create_time, update_time
    from saas_purchaser_business_scope
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectAll" resultMap="BaseResultMap" >
    select id, purchaser_no, certificate_type,business_scope_id, create_time, update_time
    from saas_purchaser_business_scope
  </select>
  <insert id="batchInsert">
    insert into saas_purchaser_business_scope
    (purchaser_no, certificate_type,business_scope_id, create_time,
    update_time)
    values
    <foreach collection="records" item="item" index="index" separator=",">
      (#{item.purchaserNo,jdbcType=VARCHAR}, #{item.certificateType,jdbcType=INTEGER}, #{item.businessScopeId,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP},
      #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>

    ON DUPLICATE KEY UPDATE
    <include refid="DuplicateKey_Update_Values"/>
  </insert>

  <select id="selectByPurchaserNo" resultMap="BaseResultMap">
    select id, purchaser_no,certificate_type, business_scope_id, create_time, update_time
    from saas_purchaser_business_scope
    where
    purchaser_no = #{purchaserNo,jdbcType=VARCHAR}
  </select>
</mapper>