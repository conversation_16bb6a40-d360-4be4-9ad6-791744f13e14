<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.user.provider.module.dao.SaasUserMapper" >
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasUser" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="account" property="account" jdbcType="VARCHAR" />
    <result column="phone" property="phone" jdbcType="VARCHAR" />
    <result column="password" property="password" jdbcType="VARCHAR" />
    <result column="email" property="email" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="organ_sign_type" property="organSignType" jdbcType="TINYINT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>
  
  <select id="getUserByMultipleId" parameterType="java.lang.Integer" resultType="com.xyy.user.provider.module.entity.SaasUser">
	select * from saas_user 
	where id in 
    <foreach collection="userIds" item="id" open="(" close=")" separator=",">
	  #{id}
  	</foreach>
  </select>

  <select id="selectUserByAccount" resultType="com.xyy.user.provider.module.entity.SaasUser" parameterType="java.lang.String" >
    select * from saas_user where account = #{account, jdbcType=VARCHAR}
  </select>
  
  <select id="selectUserById" resultType="com.xyy.user.provider.module.entity.SaasUser" parameterType="java.lang.Integer" >
    select * from saas_user where id = #{userId, jdbcType=INTEGER}
  </select>

  <select id="selectAll" resultType="com.xyy.user.provider.module.entity.SaasUser" >
    select * from saas_user
  </select>

  <insert id="insertUserRegister" useGeneratedKeys="true" keyProperty="id" parameterType="com.xyy.user.provider.module.entity.SaasUser">
    INSERT INTO saas_user
    (
    account,
    phone,
    password,
    email,
    status,
    organ_sign_type,
    create_time
    )
    VALUES
    (
    #{account, jdbcType=VARCHAR},
    #{phone, jdbcType=VARCHAR},
    #{password, jdbcType=VARCHAR},
    #{email, jdbcType=VARCHAR},
    #{status, jdbcType=INTEGER},
    #{organSignType, jdbcType=TINYINT},
    #{createTime, jdbcType=TIMESTAMP}
    )
  </insert>

  <select id="selectUserByPhone" parameterType="java.lang.String" resultType="com.xyy.user.provider.module.entity.SaasUser">
    select * from saas_user where phone = #{phone, jdbcType=VARCHAR}  or  account = #{phone, jdbcType=VARCHAR}
  </select>

  <select id="selectUserByPhoneAndPassword" resultType="com.xyy.user.provider.module.entity.SaasUser" parameterType="java.lang.String">
    select * from saas_user where phone = #{phone, jdbcType=VARCHAR} and password = #{password, jdbcType=VARCHAR}
  </select>

  <update id="updateAccountPassword">
    UPDATE saas_user set
    <if test="password != null">
      password = #{password, jdbcType=VARCHAR}
    </if>
    where id = #{userId, jdbcType=INTEGER}
  </update>

  <update id="updateAccountPhone">
    UPDATE saas_user set
    <if test="phone != null">
      phone = #{phone, jdbcType=VARCHAR}
    </if>
    where id = #{userId, jdbcType=INTEGER}
  </update>

  <select id="findUserList" parameterType="java.lang.String" resultType="com.xyy.user.provider.module.vo.SaasUserVO">
    select a.id, a.account, a.phone, a.status, a.create_time createTime, a.organ_sign_type, e.name roleName, c.drugstore_name drugStoreName,
    c.status drugStoreStatus,e.id roleId
    from saas_user a
    left join saas_employee b on a.id = b.user_id and b.working_state=1
    left join saas_drugstore c on b.organ_sign = c.organ_sign
    left join saas_employee_role d on b.id = d.employee_id
    left join saas_role e on e.id = d.role_id
    <where>
      <if test="phone !=null and phone !='' ">
        a.phone = #{phone}
      </if>
    </where>
    order by a.create_time desc
  </select>

  <select id="findUserByIdAndRoleId" parameterType="java.lang.Integer" resultType="com.xyy.user.provider.module.vo.SaasUserVO">
    select a.id, a.account, a.phone, a.status, a.create_time createTime, a.organ_sign_type, e.name roleName, c.drugstore_name drugStoreName, c.status drugStoreStatus,c.organ_sign organSign
    from saas_user a
    left join saas_employee b on a.id = b.user_id and b.working_state=1
    left join saas_drugstore c on b.organ_sign = c.organ_sign
    left join saas_employee_role d on b.id = d.employee_id
    left join saas_role e on e.id = d.role_id
    <where>
      a.id=#{id}
      <if test="roleId!=null and roleId != -1">
        and e.id=#{roleId}
      </if>
    </where>
    limit 1
  </select>

  <update id="updateStatusById" parameterType="java.util.Map" >
    update saas_user set status=#{status}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="findDrugstoreManagerPhone" parameterType="java.lang.String" resultType="com.xyy.user.provider.module.entity.SaasUser" >
        select * from saas_user u join saas_drugstore d on u.id=d.manager_user_id
        where d.organ_sign=#{organSign};
    </select>

  <update id="updateUserAccount">
    UPDATE saas_user set
    <if test="account != null">
      account = #{account, jdbcType=VARCHAR}
    </if>
    where id = #{userId, jdbcType=INTEGER}
  </update>

  <select id="refreshData" parameterType="java.lang.Integer" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
    SELECT id,organ_sign as organSign,manager_user_id as managerUserId FROM saas_drugstore
    <where>
      1=1
      and  id > #{userId}
    </where>
    order by id
    limit #{pageSize}
  </select>
</mapper>