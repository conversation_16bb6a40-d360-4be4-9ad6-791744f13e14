<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.user.provider.module.dao.SaasUpgradeMapper" >
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasUpgrade" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="version_name" property="versionName" jdbcType="VARCHAR" />
    <result column="version_code" property="versionCode" jdbcType="INTEGER" />
    <result column="min_ver_support" property="minVerSupport" jdbcType="INTEGER" />
    <result column="version_info" property="versionInfo" jdbcType="VARCHAR" />
    <result column="download_url" property="downloadUrl" jdbcType="VARCHAR" />
    <result column="client" property="client" jdbcType="TINYINT" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="delete_status" property="deleteStatus" jdbcType="TINYINT" />
  </resultMap>

  <insert id="insert" parameterType="com.xyy.user.provider.module.entity.SaasUpgrade" useGeneratedKeys="true" keyProperty="id" >
    insert into saas_upgrade (version_name, version_code, min_ver_support,
      version_info, download_url, client,
      update_time, delete_status)
    values (#{versionName,jdbcType=VARCHAR}, #{versionCode,jdbcType=INTEGER}, #{minVerSupport,jdbcType=INTEGER}, 
      #{versionInfo,jdbcType=VARCHAR}, #{downloadUrl,jdbcType=VARCHAR}, #{client,jdbcType=TINYINT},
      #{updateTime,jdbcType=TIMESTAMP}, #{deleteStatus,jdbcType=TINYINT})
  </insert>

  <update id="updateByPrimaryKey" parameterType="com.xyy.user.provider.module.entity.SaasUpgrade" >
    update saas_upgrade
    set version_name = #{record.versionName,jdbcType=VARCHAR},
      version_code = #{record.versionCode,jdbcType=INTEGER},
      min_ver_support = #{record.minVerSupport,jdbcType=INTEGER},
      version_info = #{record.versionInfo,jdbcType=VARCHAR},
      download_url = #{record.downloadUrl,jdbcType=VARCHAR},
      client = #{record.client,jdbcType=TINYINT},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      delete_status = #{record.deleteStatus,jdbcType=TINYINT}
    where id = #{record.id,jdbcType=INTEGER}
  </update>

  <select id="selectUpgradeByClient" resultMap="BaseResultMap" >
    select id, version_name, version_code, min_ver_support, version_info, download_url,
    client, update_time, delete_status
    from saas_upgrade
    <where>
      1 = 1
      <if test="client != null">
        AND client = #{client, jdbcType=TINYINT}
      </if>
      <if test="delete != null">
        and delete_status = #{delete, jdbcType=TINYINT}
      </if>
      <if test="startTime != null">
        <![CDATA[ and update_time >= #{startTime, jdbcType=TIMESTAMP}]]>
      </if>
      <if test="endTime != null">
        <![CDATA[ and update_time <= #{endTime, jdbcType=TIMESTAMP}]]>
      </if>
    </where>
    order by version_code desc
  </select>

  <update id="updateUpgradeDeleteStatus" >
    update saas_upgrade
    set delete_status = #{status,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="updateNewestClientUpgrade" parameterType="java.lang.Byte" resultMap="BaseResultMap" >
    select id, version_name, version_code, min_ver_support, version_info, download_url,
    client, update_time, delete_status
    from saas_upgrade
    where delete_status = 0 and client = #{client,jdbcType=TINYINT} order by version_code desc limit 1
  </select>

  <select id="selectUpgradeById" parameterType="java.lang.Integer"
          resultType="com.xyy.user.provider.module.entity.SaasUpgrade">
    select * from saas_upgrade where id = #{id, jdbcType=INTEGER}
  </select>
</mapper>