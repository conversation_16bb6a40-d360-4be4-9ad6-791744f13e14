<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.user.provider.module.dao.ECVoucherMapper">

    <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.ECVoucherPo">
        <id     column="id"                              property="id" jdbcType="BIGINT"/>
        <result column="template_id"                    property="templateId" jdbcType="BIGINT"/>
        <result column="drugstore_name"                property="drugstoreName" jdbcType="VARCHAR"/>
        <result column="organ_sign"                    property="organSign" jdbcType="VARCHAR"/>
        <result column="standards_rule_version"       property="standardsRuleVersion" jdbcType="TINYINT" />
        <result column="sync_no"                        property="syncNo" jdbcType="VARCHAR"/>
        <result column="merchant_id"                   property="merchantId" jdbcType="BIGINT"/>
        <result column="province"                      property="province" jdbcType="VARCHAR"/>
        <result column="city"                           property="city" jdbcType="VARCHAR"/>
        <result column="district"                      property="district" jdbcType="VARCHAR"/>
        <result column="voucher_id"                    property="voucherId" jdbcType="BIGINT"/>
        <result column="create_time"                   property="createTime" jdbcType="TIMESTAMP" />
        <result column="valid_date"                    property="validDate" jdbcType="TIMESTAMP" />
        <result column="expire_date"                   property="expireDate" jdbcType="TIMESTAMP" />
        <result column="state"                          property="state" jdbcType="TINYINT" />
        <result column="update_time"                   property="updateTime" jdbcType="TIMESTAMP" />
        <result column="order_no"                      property="orderNo" jdbcType="VARCHAR"/>
        <result column="order_total_amount"           property="orderTotalAmount" jdbcType="DECIMAL"/>
        <result column="voucher_sku_price_sum"        property="voucherSkuPriceSum" jdbcType="DECIMAL"/>
        <result column="order_discount_amount"        property="orderDiscountAmount" jdbcType="DECIMAL"/>
        <result column="drugstore_data_source"        property="drugstoreDataSource" jdbcType="TINYINT" />
    </resultMap>

    <sql id="Base_Column_List">
    id, template_id, drugstore_name, organ_sign, standards_rule_version, sync_no, merchant_id, province, city,
    district, voucher_id, create_time,valid_date, expire_date, state, update_time, order_no, order_total_amount, voucher_sku_price_sum, order_discount_amount,drugstore_data_source
    </sql>

    <select id="selectECVoucherList" resultType="com.xyy.user.module.dto.ECVoucherDto">
        SELECT
        <include refid="Base_Column_List"/>
        FROM saas_ybm_voucher
        WHERE 1 = 1
        <include refid="Base_Where_Condition"/>
        order by create_time  DESC
    </select>

    <select id="selectECVoucherSummary" resultType="com.xyy.user.module.dto.ECVoucherSummaryDto">
        SELECT
        sum(order_total_amount) as orderTotalAmount ,
        sum(voucher_sku_price_sum) as voucherSkuPriceSum ,
        sum(order_discount_amount) as orderDiscountAmount
        FROM saas_ybm_voucher
        WHERE 1 = 1
        <if test="list != null and list.size>0">
            and voucher_id in(
            <foreach collection="list" item="item" index="index" separator="," >
                #{item.voucherId}
            </foreach >
            )
        </if>
    </select>

    <select id="selectECVoucherTemplateId" resultType="long">
        SELECT
        DISTINCT template_id AS templateId
        FROM saas_ybm_voucher
        WHERE 1 = 1
        <include refid="Base_Where_Condition"/>
    </select>

    <select id="selectECVoucherSummaryOrderNo" resultType="com.xyy.user.module.dto.ECVoucherDto">
        SELECT
        order_no AS orderNo ,voucher_id AS voucherId
        FROM saas_ybm_voucher
        WHERE 1 = 1
        <include refid="Base_Where_Condition"/>
        GROUP BY order_no
    </select>

    <sql id="Base_Where_Condition">
        <if test="drugstoreName != null and drugstoreName !=''">
            and drugstore_name like CONCAT("%", #{drugstoreName},"%")
        </if>
        <if test="drugstoreDataSource != null">
            and drugstore_data_source = #{drugstoreDataSource}
        </if>
        <if test="province != null and province !=''">
            and province like CONCAT("%", #{province},"%")
        </if>
        <if test="city != null and city !=''">
            and city like CONCAT("%", #{city},"%")
        </if>
        <if test="district != null and district !=''">
            and district like CONCAT("%", #{district},"%")
        </if>
        <if test="organSign != null and organSign !=''">
            and organ_sign = #{organSign}
        </if>
        <if test="standardsRuleVersion != null">
            and standards_rule_version = #{standardsRuleVersion}
        </if>
        <if test="state != null">
            and state = #{state}
        </if>
        <if test="orderNo != null and orderNo !=''">
            and order_no = #{orderNo}
        </if>
        <if test="startCreateDate != null and endCreateDate == null ">
            <![CDATA[ and create_time >= #{startCreateDate} ]]>
        </if>
        <if test="startCreateDate == null and endCreateDate != null ">
            <![CDATA[ and  #{endCreateDate} >=create_time ]]>
        </if>
        <if test="startCreateDate != null and endCreateDate != null ">
            <![CDATA[ and create_time >= #{startCreateDate} ]]>
            <![CDATA[ and  #{endCreateDate} >=create_time ]]>
        </if>
        <if test="startUpdateTime != null and endUpdateTime == null ">
            <![CDATA[ and update_time >= #{startUpdateTime} ]]>
        </if>
        <if test="startUpdateTime == null and endUpdateTime != null ">
            <![CDATA[ and  #{endUpdateTime} >=update_time ]]>
        </if>
        <if test="startUpdateTime != null and endUpdateTime != null ">
            <![CDATA[ and update_time >= #{startUpdateTime} ]]>
            <![CDATA[ and  #{endUpdateTime} >=update_time ]]>
        </if>
        <if test="templateIdList != null and templateIdList.size>0">
            and template_id in(
            <foreach collection="templateIdList" item="item" index="index" separator=",">
                #{item}
            </foreach>
            )
        </if>
    </sql>
</mapper>