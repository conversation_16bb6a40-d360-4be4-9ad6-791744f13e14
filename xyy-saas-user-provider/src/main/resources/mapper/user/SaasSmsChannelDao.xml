<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.user.provider.module.dao.sms.SaasSmsChannelDao">

    <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.sms.SaasSmsChannel">
        <!--@Table saas_sms_channel-->
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="manageType" column="manage_type" jdbcType="INTEGER"/>
        <result property="smsProviderId" column="sms_provider_id" jdbcType="VARCHAR"/>
        <result property="smsProviderName" column="sms_provider_name" jdbcType="VARCHAR"/>
        <result property="smsProviderUrl" column="sms_provider_url" jdbcType="VARCHAR"/>
        <result property="companyId" column="company_id" jdbcType="VARCHAR"/>
        <result property="account" column="account" jdbcType="VARCHAR"/>
        <result property="password" column="password" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="smsSendCount" column="sms_send_count" jdbcType="INTEGER"/>
        <result property="smsSendSuccCount" column="sms_send_succ_count" jdbcType="INTEGER"/>
        <result property="smsSendTimes" column="sms_send_times" jdbcType="INTEGER"/>
        <result property="smsAvgSuccPercent" column="sms_avg_succ_percent" jdbcType="OTHER"/>
        <result property="smsSuccPercent" column="sms_succ_percent" jdbcType="OTHER"/>
        <result property="smsPackageLeftCount" column="sms_package_left_count" jdbcType="INTEGER"/>
        <result property="smsHandler" column="sms_handler" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, manage_type, sms_provider_id, sms_provider_name, sms_provider_url, company_id, account, password, status, remark, sms_send_count, sms_send_succ_count, sms_send_times, sms_avg_succ_percent, sms_succ_percent, sms_package_left_count, sms_handler, create_time, update_time
    </sql>

    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
        select
          <include refid="Base_Column_List"/>
        from xyy_saas_user.saas_sms_channel
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BaseResultMap">
        select
          <include refid="Base_Column_List"/>
        from xyy_saas_user.saas_sms_channel
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" parameterType="com.xyy.user.provider.module.entity.sms.SaasSmsChannel" resultMap="BaseResultMap">
        select
          <include refid="Base_Column_List"/>
        from xyy_saas_user.saas_sms_channel
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="manageType != null">
                and manage_type = #{manageType}
            </if>
            <if test="smsProviderId != null and smsProviderId != ''">
                and sms_provider_id = #{smsProviderId}
            </if>
            <if test="smsProviderName != null and smsProviderName != ''">
                and sms_provider_name = #{smsProviderName}
            </if>
            <if test="companyId != null and companyId != ''">
                and company_id = #{companyId}
            </if>
            <if test="account != null and account != ''">
                and account = #{account}
            </if>
            <if test="password != null and password != ''">
                and password = #{password}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="smsSendCount != null">
                and sms_send_count = #{smsSendCount}
            </if>
            <if test="smsSendSuccCount != null">
                and sms_send_succ_count = #{smsSendSuccCount}
            </if>
            <if test="smsSendTimes != null">
                and sms_send_times = #{smsSendTimes}
            </if>
            <if test="smsAvgSuccPercent != null">
                and sms_avg_succ_percent = #{smsAvgSuccPercent}
            </if>
            <if test="smsSuccPercent != null">
                and sms_succ_percent = #{smsSuccPercent}
            </if>
            <if test="smsPackageLeftCount != null">
                and sms_package_left_count = #{smsPackageLeftCount}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into xyy_saas_user.saas_sms_channel(manage_type, sms_provider_id, sms_provider_name, sms_provider_url, company_id, account, password, status, remark, sms_send_count, sms_send_succ_count, sms_send_times, sms_avg_succ_percent, sms_succ_percent, sms_package_left_count, sms_handler, create_time, update_time)
        values (#{manageType}, #{smsProviderId}, #{smsProviderName}, #{smsProviderUrl}, #{companyId}, #{account}, #{password}, #{status}, #{remark}, #{smsSendCount}, #{smsSendSuccCount}, #{smsSendTimes}, #{smsAvgSuccPercent}, #{smsSuccPercent}, #{smsPackageLeftCount}, #{smsHandler}, #{createTime}, #{updateTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update xyy_saas_user.saas_sms_channel
        <set>
            <if test="manageType != null">
                manage_type = #{manageType},
            </if>
            <if test="smsProviderId != null and smsProviderId != ''">
                sms_provider_id = #{smsProviderId},
            </if>
            <if test="smsProviderName != null and smsProviderName != ''">
                sms_provider_name = #{smsProviderName},
            </if>
            <if test="smsProviderUrl != null and smsProviderUrl != ''">
                sms_provider_url = #{smsProviderUrl},
            </if>
            <if test="companyId != null and companyId != ''">
                company_id = #{companyId},
            </if>
            <if test="account != null and account != ''">
                account = #{account},
            </if>
            <if test="password != null and password != ''">
                password = #{password},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="smsSendCount != null">
                sms_send_count = #{smsSendCount},
            </if>
            <if test="smsSendSuccCount != null">
                sms_send_succ_count = #{smsSendSuccCount},
            </if>
            <if test="smsSendTimes != null">
                sms_send_times = #{smsSendTimes},
            </if>
            <if test="smsAvgSuccPercent != null">
                sms_avg_succ_percent = #{smsAvgSuccPercent},
            </if>
            <if test="smsSuccPercent != null">
                sms_succ_percent = #{smsSuccPercent},
            </if>
            <if test="smsPackageLeftCount != null">
                sms_package_left_count = #{smsPackageLeftCount},
            </if>
            <if test="smsHandler != null">
                sms_handler = #{smsHandler},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from xyy_saas_user.saas_sms_channel where id = #{id}
    </delete>

</mapper>