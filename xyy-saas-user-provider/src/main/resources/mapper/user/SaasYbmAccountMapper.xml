<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.user.provider.module.dao.SaasYbmAccountMapper" >
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasYbmAccount" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="username" property="username" jdbcType="VARCHAR" />
    <result column="password" property="password" jdbcType="VARCHAR" />
    <result column="commodity_order_tax" property="commodityOrderTax" jdbcType="TINYINT" />
    <result column="bind_code" property="bindCode" jdbcType="VARCHAR" />
    <result column="bind_status" property="bindStatus" jdbcType="TINYINT" />
    <result column="bind_time" property="bindTime" jdbcType="TIMESTAMP" />
    <result column="untie_time" property="untieTime" jdbcType="TIMESTAMP" />
    <result column="organ_sign" property="organSign" jdbcType="VARCHAR" />
    <result column="order_push_time" property="orderPushTime" jdbcType="TIMESTAMP" />
  </resultMap>

  <resultMap id="DrugstoreFullInfoMap" type="com.xyy.user.module.dto.result.DrugstoreYbmFullInfoDto" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="organ_sign" property="organSign" jdbcType="VARCHAR" />
    <result column="userName" property="userName" jdbcType="VARCHAR" />
    <result column="bind_code" property="bindCode" jdbcType="VARCHAR" />
    <result column="bind_time" property="bindTime" jdbcType="TIMESTAMP" />
    <result column="bind_status" property="bindStatus" jdbcType="TINYINT" />
    <result column="drugstore_name" property="drugstoreName" jdbcType="VARCHAR" />
    <result column="manager_name" property="managerName" jdbcType="VARCHAR" />
    <result column="province" property="province" jdbcType="VARCHAR" />
    <result column="city" property="city" jdbcType="VARCHAR" />
    <result column="area" property="area" jdbcType="VARCHAR" />
    <result column="address" property="address" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="TINYINT" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>

  <insert id="insert" parameterType="com.xyy.user.provider.module.entity.SaasYbmAccount" useGeneratedKeys="true" keyProperty="id" >
    insert into saas_ybm_account (username, password, commodity_order_tax,order_push_time,
      bind_code, bind_status, bind_time,
      untie_time, organ_sign)
    values (#{username,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR}, #{commodityOrderTax,jdbcType=TINYINT}, #{orderPushTime, jdbcType=TIMESTAMP},
      #{bindCode,jdbcType=VARCHAR}, #{bindStatus,jdbcType=TINYINT}, #{bindTime,jdbcType=TIMESTAMP},
      #{untieTime,jdbcType=TIMESTAMP}, #{organSign,jdbcType=VARCHAR})
  </insert>

  <update id="updateAccountByOrganSin" parameterType="com.xyy.user.provider.module.entity.SaasYbmAccount" >
    update saas_ybm_account
    set username = #{username, jdbcType=VARCHAR},
      password = #{password, jdbcType=VARCHAR},
      commodity_order_tax = #{commodityOrderTax, jdbcType=TINYINT},
      order_push_time = #{orderPushTime, jdbcType=TIMESTAMP},
      bind_code = #{bindCode, jdbcType=VARCHAR},
      bind_status = #{bindStatus, jdbcType=TINYINT},
      bind_time = #{bindTime, jdbcType=TIMESTAMP},
      untie_time = #{untieTime, jdbcType=TIMESTAMP},
      organ_sign = #{organSign, jdbcType=VARCHAR}
    where organ_sign = #{organSign, jdbcType=VARCHAR}
  </update>

  <select id="selectByOrganSign" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select id, username, password, commodity_order_tax, bind_code, bind_status, bind_time,
    untie_time, organ_sign,order_push_time
    from saas_ybm_account
    where organ_sign = #{organSign, jdbcType=VARCHAR} and bind_status = 1
  </select>

  <select id="selectRecordByOrganSign" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select id, username, password, commodity_order_tax, bind_code, bind_status, bind_time,
    untie_time, organ_sign
    from saas_ybm_account
    where organ_sign = #{organSign, jdbcType=VARCHAR}
  </select>

  <select id="selectAllYbmAccount" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select id, username, password, commodity_order_tax, bind_code, bind_status, bind_time,
    untie_time, organ_sign
    from saas_ybm_account
    where bind_status = 1
  </select>

  <update id="untieYbmBind" parameterType="java.lang.String" >
    update saas_ybm_account
    set bind_status = 2,
      untie_time = NOW()
    where organ_sign = #{organSign, jdbcType=VARCHAR} and bind_status = 1
  </update>

  <delete id="deleteYbmAccountByOrganSign" parameterType="java.lang.String">
    delete from saas_ybm_account where organ_sign = #{organSign, jdbcType=VARCHAR}
  </delete>

  <select id="selectYbmAccountByUsernameAndPassword" parameterType="java.lang.String" resultMap="BaseResultMap">
    select id, username, password, commodity_order_tax, bind_code, bind_status, bind_time,
    untie_time, organ_sign
    from saas_ybm_account
    where username = #{username, jdbcType=VARCHAR} and bind_status = 1
  </select>

  <select id="selectYbmAccountByBindCode" parameterType="java.lang.String" resultMap="BaseResultMap">
    select id, username, password, commodity_order_tax, bind_code, bind_status, bind_time,
    untie_time, organ_sign
    from saas_ybm_account
    where bind_code = #{bindCode, jdbcType=VARCHAR} and bind_status = 1 limit 1
  </select>
    <select id="getDrugstoreYbmByCondition" resultMap="BaseResultMap">
        select id, username, commodity_order_tax, bind_code, bind_time,
    untie_time, organ_sign
    from saas_ybm_account
    <where>
      bind_status = 1
      <if test="organSignList != null  and organSignList.size > 0">
        and organ_sign in
        <foreach collection="organSignList" item="item" index="index" open="(" separator="," close=")">
          #{item,jdbcType=VARCHAR}
        </foreach>
      </if>
    </where>
    </select>

  <select id="getCountByCondition" resultType="Integer">
        select count(*)
    from saas_ybm_account
    <where>
      1 = 1
      <if test="bindStatus != null ">
        and bind_status =#{bindStatus, jdbcType=VARCHAR}
      </if>
      <if test="organSign != null  and organSign != ''">
        and organ_sign =#{organSign, jdbcType=VARCHAR}
      </if>

    </where>
    </select>

  <select id="getLastUnBindYBMAccount" resultMap="BaseResultMap">
    select *
    from saas_ybm_account
    <where>
      organ_sign =#{organSign, jdbcType=VARCHAR}
      <if test="bindStatus != null ">
        and bind_status =#{bindStatus, jdbcType=VARCHAR}
      </if>

    </where>
    order by untie_time desc limit 1
  </select>
  <select id="selectYbmAccountByBindCodeAndTime" resultMap="BaseResultMap">
    select id, username, password, commodity_order_tax, bind_code, bind_status, bind_time,
    untie_time, organ_sign
    from saas_ybm_account
    <where>
       bind_status =#{bindStatus, jdbcType=VARCHAR}
      <if test="bindCode != null ">
        and bind_code =#{bindCode, jdbcType=VARCHAR}
      </if>
      <if test="bindTime != null ">
        <![CDATA[AND bind_time <= #{bindTime}]]>
      </if>
      <if test="untieTime != null ">
        <![CDATA[AND untie_time >= #{untieTime}]]>
      </if>
    </where>
--     order by untie_time desc
    limit 1
  </select>


  <sql id="drugstoreFullInfoSQL">
      select a.id,a.organ_sign,a.userName,a.bind_code,a.bind_time,a.bind_status,d.drugstore_name,
      d.manager_name,d.province,d.city,d.area,d.address,d.type,d.status,d.create_time,d.update_time
      from saas_ybm_account a left join saas_drugstore d
      on a.organ_sign = d.organ_sign
  </sql>
  <select id="syncDrugstoreFullInfo" resultMap="DrugstoreFullInfoMap">
    <include refid="drugstoreFullInfoSQL"/>
  </select>

  <select id="queryDrugstoreFullInfo" resultMap="DrugstoreFullInfoMap">
    <include refid="drugstoreFullInfoSQL"/>
    <where>
      <if test="organSigns != null and organSigns.size() > 0">
        and a.organ_sign in
        <foreach collection="organSigns" item="organSign" open="(" close=")" separator=",">
          #{organSign}
        </foreach>
      </if>
    </where>
  </select>
  <update id="updateYBMAccount" parameterType="com.xyy.user.module.dto.UpdateYBMAccountDto" >
    update saas_ybm_account
    set  password = #{newPassword, jdbcType=VARCHAR}
    where organ_sign = #{organSign, jdbcType=VARCHAR} and username = #{username, jdbcType=VARCHAR}
  </update>
</mapper>