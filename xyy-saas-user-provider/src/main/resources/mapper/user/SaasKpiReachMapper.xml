<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.user.provider.module.dao.SaasKpiReachMapper" >
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasKpiReachPo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="organ_sign" property="organSign" jdbcType="VARCHAR" />
    <result column="contract_code" property="contractCode" jdbcType="VARCHAR" />
    <result column="version_id" property="versionId" jdbcType="BIGINT" />
    <result column="is_reach" property="isReach" jdbcType="TINYINT" />
    <result column="cycle" property="cycle" jdbcType="VARCHAR" />
    <result column="coupons_list" property="couponsList" jdbcType="VARCHAR" />
    <result column="coupons_list_fail" property="couponsListFail" jdbcType="VARCHAR" />
    <result column="send_status" property="sendStatus" jdbcType="TINYINT" />
    <result column="send_fail_msg" property="sendFailMsg" jdbcType="VARCHAR" />
    <result column="merchant_ids" property="merchantIds" jdbcType="VARCHAR" />
    <result column="username" property="username" jdbcType="VARCHAR" />

    <result column="yn" property="yn" jdbcType="TINYINT" />
    <result column="send_time" property="sendTime" jdbcType="TIMESTAMP" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
  </resultMap>

  <sql id="BaseColumns">
    `id`,
    `organ_sign`,
    `contract_code`,
    `version_id`,
    `is_reach`,
    `cycle`,
    `coupons_list`,
    `coupons_list_fail`,
    `send_status`,
    `send_fail_msg`,
    `merchant_ids`,
    `username`,
    `yn`,
    `send_time`,
    `create_time`,
    `update_time`
  </sql>

  <insert id="insert" parameterType="com.xyy.user.provider.module.entity.SaasKpiReachPo" useGeneratedKeys="true" keyProperty="id" >
    insert into saas_kpi_reach
    <trim prefix='(' suffix=')' suffixOverrides=',' >
      <if test='id!= null'>
        `id`,
      </if>
      <if test='organSign!= null'>
        `organ_sign`,
      </if>
      <if test='contractCode!= null'>
        `contract_code`,
      </if>
      <if test='versionId!= null'>
        `version_id`,
      </if>
      <if test='isReach!= null'>
        `is_reach`,
      </if>
      <if test='cycle!= null'>
        `cycle`,
      </if>
      <if test='couponsList!= null'>
        `coupons_list`,
      </if>
      <if test='couponsListFail!= null'>
        `coupons_list_fail`,
      </if>
      <if test='sendStatus!= null'>
        `send_status`,
      </if>
      <if test='sendFailMsg!= null'>
        `send_fail_msg`,
      </if>
      <if test='merchantIds!= null'>
        `merchant_ids`,
      </if>
      <if test='username!= null'>
        `username`,
      </if>
      <if test='yn!= null'>
        `yn`,
      </if>
      <if test='sendTime!= null'>
        `send_time`,
      </if>
      <if test='createTime!= null'>
        `create_time`,
      </if>
      <if test='updateTime!= null'>
        `update_time`,
      </if>
    </trim>
    <trim prefix='values (' suffix=')' suffixOverrides=',' >
      <if test='id != null'>
        #{id},
      </if>
      <if test='organSign != null'>
        #{organSign},
      </if>
      <if test='contractCode!= null'>
        #{contractCode},
      </if>
      <if test='versionId != null'>
        #{versionId},
      </if>
      <if test='isReach != null'>
        #{isReach},
      </if>
      <if test='cycle != null'>
        #{cycle},
      </if>
      <if test='couponsList != null'>
        #{couponsList},
      </if>
      <if test='couponsListFail != null'>
        #{couponsListFail},
      </if>
      <if test='sendStatus != null'>
        #{sendStatus},
      </if>
      <if test='sendFailMsg != null'>
        #{sendFailMsg},
      </if>
      <if test='merchantIds != null'>
        #{merchantIds},
      </if>
      <if test='username != null'>
        #{username},
      </if>
      <if test='yn != null'>
        #{yn},
      </if>
      <if test='sendTime!= null'>
        #{sendTime},
      </if>
      <if test='createTime != null'>
        #{createTime},
      </if>
      <if test='updateTime != null'>
        #{updateTime},
      </if>
    </trim>
  </insert>

  <update id="update" parameterType="com.xyy.user.provider.module.entity.SaasKpiReachPo">
    update saas_kpi_reach
    <set>
      <if test='organSign != null'>
        `organ_sign` = #{organSign},
      </if>
      <if test='contractCode!= null'>
        `contract_code` = #{contractCode},
      </if>
      <if test='versionId != null'>
        `version_id` = #{versionId},
      </if>
      <if test='isReach != null'>
        `is_reach` = #{isReach},
      </if>
      <if test='cycle != null'>
        `cycle` = #{cycle},
      </if>
      <if test='couponsList != null'>
        `coupons_list` = #{couponsList},
      </if>
      <if test='couponsListFail != null'>
        `coupons_list_fail` = #{couponsListFail},
      </if>
      <if test='sendStatus != null'>
        `send_status` = #{sendStatus},
      </if>
      <if test='sendFailMsg != null'>
        `send_fail_msg` = #{sendFailMsg},
      </if>
      <if test='merchantIds != null'>
        `merchant_ids` = #{merchantIds},
      </if>
      <if test='username != null'>
        `username` = #{username},
      </if>
      <if test='yn != null'>
        `yn` = #{yn},
      </if>
      <if test='sendTime!= null'>
        `send_time` = #{sendTime},
      </if>
      <if test='createTime != null'>
        `create_time` = #{createTime},
      </if>
      <if test='updateTime != null'>
        `update_time` = #{updateTime},
      </if>
    </set>
    where id=#{id}
  </update>

  <select id="selectList" resultMap="BaseResultMap" >
    select <include refid="BaseColumns"></include>
    from saas_kpi_reach
    <where>
      <if test='id != null'>
        and `id` = #{id}
      </if>
      <if test='organSign != null'>
        and `organ_sign` = #{organSign}
      </if>
      <if test='contractCode!= null'>
        and `contract_code` = #{contractCode}
      </if>
      <if test='versionId != null'>
        and `version_id` = #{versionId}
      </if>
      <if test='isReach != null'>
        and `is_reach` = #{isReach}
      </if>
      <if test='cycle != null'>
        and `cycle` = #{cycle}
      </if>
      <if test='couponsList != null'>
        and `coupons_list` = #{couponsList}
      </if>
      <if test='sendStatus != null'>
        and `send_status` = #{sendStatus}
      </if>
      <if test='sendFailMsg != null'>
        and `send_fail_msg` = #{sendFailMsg}
      </if>
      <if test='merchantIds != null'>
        and `merchant_ids` = #{merchantIds}
      </if>
      <if test='username != null'>
        and `username` = #{username}
      </if>
      <if test='yn != null'>
        and `yn` = #{yn}
      </if>
      <if test='createTime != null'>
        and `create_time` = #{createTime}
      </if>
      <if test='updateTime != null'>
        and `update_time` = #{updateTime}
      </if>
    </where>
  </select>

  <select id="selectListByParamsMap" resultMap="BaseResultMap" >
    select <include refid="BaseColumns"></include>
    from saas_kpi_reach
    <where>
      <if test="organSignList != null and organSignList.size() > 0">
        and t.organ_sign in
        <foreach collection="organSignList" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test='contractCode!= null'>
        and `contract_code` = #{contractCode}
      </if>
      <if test='versionId != null'>
        and `version_id` = #{versionId}
      </if>
      <if test='isReach != null'>
        and `is_reach` = #{isReach}
      </if>
      <if test='cycle != null'>
        and `cycle` = #{cycle}
      </if>
      <if test='couponsList != null'>
        and `coupons_list` = #{couponsList}
      </if>
      <if test='sendStatus != null'>
        and `send_status` = #{sendStatus}
      </if>
      <if test='sendFailMsg != null'>
        and `send_fail_msg` = #{sendFailMsg}
      </if>
      <if test='merchantIds != null'>
        and `merchant_ids` = #{merchantIds}
      </if>
      <if test='username != null'>
        and `username` = #{username}
      </if>
      <if test='yn != null'>
        and `yn` = #{yn}
      </if>
      <if test='createTime != null'>
        and `create_time` = #{createTime}
      </if>
      <if test='updateTime != null'>
        and `update_time` = #{updateTime}
      </if>
    </where>
  </select>


</mapper>