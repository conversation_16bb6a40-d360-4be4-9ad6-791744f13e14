<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.user.provider.module.dao.sms.SaasSmsReplyDao">

    <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.sms.SaasSmsReply">
        <!--@Table saas_sms_reply-->
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="smsChannelId" column="sms_channel_id" jdbcType="INTEGER"/>
        <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
        <result property="organSign" column="organ_sign" jdbcType="VARCHAR"/>
        <result property="organSignName" column="organ_sign_name" jdbcType="VARCHAR"/>
        <result property="rootOrganSign" column="root_organ_sign" jdbcType="VARCHAR"/>
        <result property="telephone" column="telephone" jdbcType="VARCHAR"/>
        <result property="memberId" column="member_id" jdbcType="INTEGER"/>
        <result property="replyContent" column="reply_content" jdbcType="VARCHAR"/>
        <result property="replyTime" column="reply_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    
    <sql id="Base_Column_List">
        id, sms_channel_id, task_id, organ_sign, organ_sign_name, root_organ_sign, telephone, member_id, reply_content, reply_time, create_time
    </sql>

    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
        select
          <include refid="Base_Column_List"/>
        from xyy_saas_user.saas_sms_reply
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BaseResultMap">
        select
          <include refid="Base_Column_List"/>
        from xyy_saas_user.saas_sms_reply
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" parameterType="com.xyy.user.provider.module.entity.sms.SaasSmsReply" resultMap="BaseResultMap">
        select
          <include refid="Base_Column_List"/>
        from xyy_saas_user.saas_sms_reply
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="smsChannelId != null">
                and sms_channel_id = #{smsChannelId}
            </if>
            <if test="taskId != null and taskId != ''">
                and task_id = #{taskId}
            </if>
            <if test="organSign != null and organSign != ''">
                and organ_sign = #{organSign}
            </if>
            <if test="organSignName != null and organSignName != ''">
                and organ_sign_name = #{organSignName}
            </if>
            <if test="rootOrganSign != null and rootOrganSign != ''">
                and root_organ_sign = #{rootOrganSign}
            </if>
            <if test="telephone != null and telephone != ''">
                and telephone = #{telephone}
            </if>
            <if test="memberId != null">
                and member_id = #{memberId}
            </if>
            <if test="replyContent != null and replyContent != ''">
                and reply_content = #{replyContent}
            </if>
            <if test="replyTime != null">
                and reply_time = #{replyTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
        </where>
    </select>

    <select id="pageSearch" parameterType="com.xyy.user.module.dto.sms.SaasSmsReplyParamDto" resultMap="BaseResultMap">
        select
          <include refid="Base_Column_List"/>
        from xyy_saas_user.saas_sms_reply
        <where>
            root_organ_sign = #{rootOrganSign}
            <if test="telephone != null and telephone != ''">
                and telephone like concat('%',#{telephone},'%')
            </if>
            <if test="replyContent != null and replyContent != ''">
                and reply_content like concat('%',#{replyContent},'%')
            </if>
            <if test="startTime != null">
                and reply_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and reply_time &lt;= #{endTime}
            </if>
        </where>
        order by reply_time desc
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into xyy_saas_user.saas_sms_reply(sms_channel_id, task_id, organ_sign, organ_sign_name, root_organ_sign, telephone, member_id, reply_content, reply_time, create_time)
        values (#{smsChannelId}, #{taskId}, #{organSign}, #{organSignName}, #{rootOrganSign}, #{telephone}, #{memberId}, #{replyContent}, #{replyTime}, #{createTime})
    </insert>

    <insert id="batchInsertOrUpdate" keyProperty="id" useGeneratedKeys="true">
        insert into xyy_saas_user.saas_sms_reply(sms_channel_id, task_id, organ_sign, organ_sign_name, root_organ_sign, telephone, member_id, reply_content, reply_time, create_time)
        values
        <foreach collection="list" item="record" separator=",">
            (#{record.smsChannelId}, #{record.taskId}, #{record.organSign}, #{record.organSignName}, #{record.rootOrganSign}, #{record.telephone}, #{record.memberId}, #{record.replyContent}, #{record.replyTime}, #{record.createTime})
        </foreach>
        ON DUPLICATE KEY UPDATE
        sms_channel_id = VALUES(sms_channel_id),
        task_id = VALUES(task_id),
        organ_sign = VALUES(organ_sign),
        organ_sign_name = VALUES(organ_sign_name),
        root_organ_sign = VALUES(root_organ_sign),
        telephone = VALUES(telephone),
        member_id = VALUES(member_id),
        reply_content = VALUES(reply_content),
        reply_time = VALUES(reply_time),
        create_time = VALUES(create_time)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update xyy_saas_user.saas_sms_reply
        <set>
            <if test="smsChannelId != null">
                sms_channel_id = #{smsChannelId},
            </if>
            <if test="taskId != null and taskId != ''">
                task_id = #{taskId},
            </if>
            <if test="organSign != null and organSign != ''">
                organ_sign = #{organSign},
            </if>
            <if test="organSignName != null and organSignName != ''">
                organ_sign_name = #{organSignName},
            </if>
            <if test="rootOrganSign != null and rootOrganSign != ''">
                root_organ_sign = #{rootOrganSign},
            </if>
            <if test="telephone != null and telephone != ''">
                telephone = #{telephone},
            </if>
            <if test="memberId != null">
                member_id = #{memberId},
            </if>
            <if test="replyContent != null and replyContent != ''">
                reply_content = #{replyContent},
            </if>
            <if test="replyTime != null">
                reply_time = #{replyTime},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from xyy_saas_user.saas_sms_reply where id = #{id}
    </delete>

</mapper>