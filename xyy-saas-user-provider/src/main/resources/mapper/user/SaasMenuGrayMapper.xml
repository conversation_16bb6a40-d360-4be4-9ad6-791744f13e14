<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.user.provider.module.dao.SaasMenuGrayMapper">
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasMenuGrayPO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="strategy_pref" jdbcType="VARCHAR" property="strategyPref" />
    <result column="permission_id" jdbcType="INTEGER" property="permissionId" />
    <result column="menu_source" jdbcType="TINYINT" property="menuSource" />
    <result column="menu_operation_type" jdbcType="TINYINT" property="menuOperationType" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="yn" jdbcType="TINYINT" property="yn" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, strategy_pref, permission_id, menu_source, menu_operation_type, name, url,
    yn, create_user, create_time, update_user, update_time
  </sql>

  <select id="selectGrayMenuList" parameterType="com.xyy.user.module.dto.gray.SaasMenuGrayDTO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from saas_menu_gray
    <where>
      <if test="strategyPref != null">
        strategy_pref = #{strategyPref}
      </if>
      <if test="strategyPrefList != null and strategyPrefList.size > 0">
        and strategy_pref in (
        <foreach collection="strategyPrefList" item="strategyPref" separator=",">
          #{strategyPref}
        </foreach>
        )
      </if>
      <if test="yn != null">
        and yn = #{yn}
      </if>
    </where>
  </select>

  <insert id="batchInsert" parameterType="java.util.List">
    <foreach collection="list" item="record" separator=";">
      insert into saas_menu_gray (strategy_pref, permission_id, menu_source, menu_operation_type, name, url, yn,
      create_user, create_time, update_user, update_time)
      values (#{record.strategyPref,jdbcType=VARCHAR}, #{record.permissionId,jdbcType=INTEGER}, #{record.menuSource,jdbcType=TINYINT},
      #{record.menuOperationType,jdbcType=TINYINT}, #{record.name,jdbcType=VARCHAR}, #{record.url,jdbcType=VARCHAR},
      #{record.yn,jdbcType=TINYINT}, #{record.createUser,jdbcType=VARCHAR}, #{record.createTime,jdbcType=TIMESTAMP},
      #{record.updateUser,jdbcType=VARCHAR}, #{record.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <update id="batchUpdate" parameterType="java.util.List">
    <foreach collection="list" item="record" separator=";">
      update saas_menu_gray
      <set>
        <if test="record.menuSource != null">
          menu_source = #{record.menuSource,jdbcType=TINYINT},
        </if>
        <if test="record.menuOperationType != null">
          menu_operation_type = #{record.menuOperationType,jdbcType=TINYINT},
        </if>
        <if test="record.name != null">
          name = #{record.name,jdbcType=VARCHAR},
        </if>
        <if test="record.url != null">
          url = #{record.url,jdbcType=VARCHAR},
        </if>
        <if test="record.yn != null">
          yn = #{record.yn,jdbcType=TINYINT},
        </if>
        <if test="record.createUser != null">
          create_user = #{record.createUser,jdbcType=VARCHAR},
        </if>
        <if test="record.createTime != null">
          create_time = #{record.createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="record.updateUser != null">
          update_user = #{record.updateUser,jdbcType=VARCHAR},
        </if>
        <if test="record.updateTime != null">
          update_time = #{record.updateTime,jdbcType=TIMESTAMP},
        </if>
      </set>
      where strategy_pref = #{record.strategyPref}
      and permission_id = #{record.permissionId}
    </foreach>
  </update>

</mapper>