<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.user.provider.module.dao.SaasEmployeeFingerprintMapper" >
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasEmployeeFingerprint" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="employee_id" property="employeeId" jdbcType="INTEGER" />
    <result column="organ_sign" property="organSign" jdbcType="VARCHAR" />
    <result column="fingerprint_info" property="fingerprintInfo" jdbcType="VARCHAR" />
    <result column="manufacturer" property="manufacturer" jdbcType="VARCHAR" />
    <result column="device_id" property="deviceId" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="yn" property="yn" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Base_Column_List">
   id,employee_id, organ_sign,fingerprint_info, manufacturer, device_id, create_time, update_time,yn
   </sql>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from saas_employee_fingerprint
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByEmployeeId">
    delete from saas_employee_fingerprint
    where employee_id = #{employeeId,jdbcType=INTEGER}
    and organ_sign = #{organSign,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.xyy.user.provider.module.entity.SaasEmployeeFingerprint" useGeneratedKeys="true" keyProperty="id" >
    insert into saas_employee_fingerprint (employee_id, organ_sign,fingerprint_info, manufacturer,
      device_id, create_time, update_time, 
      yn)
    values (#{employeeId,jdbcType=INTEGER}, #{organSign,jdbcType=VARCHAR}, #{fingerprintInfo,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR},
      #{deviceId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{yn,jdbcType=TINYINT})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.xyy.user.provider.module.entity.SaasEmployeeFingerprint" >
    update saas_employee_fingerprint
    set employee_id = #{employeeId,jdbcType=INTEGER},
      organ_sign = #{organSign,jdbcType=VARCHAR},
      fingerprint_info = #{fingerprintInfo,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      device_id = #{deviceId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      yn = #{yn,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select id, employee_id,organ_sign, fingerprint_info, manufacturer, device_id, create_time, update_time,
    yn
    from saas_employee_fingerprint
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectAll" resultMap="BaseResultMap" >
    select id, employee_id,organ_sign, fingerprint_info, manufacturer, device_id, create_time, update_time,
    yn
    from saas_employee_fingerprint
  </select>
  <select id="selectByParam" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from saas_employee_fingerprint
    where
    1 = 1
    <if test="employeeId != null">
      and employee_id = #{employeeId,jdbcType=INTEGER}
    </if>
    <if test="organSign != null and organSign != ''">
      and organ_sign = #{organSign,jdbcType=VARCHAR}
    </if>
    <if test="fingerprintInfo != null" >
      and fingerprint_info = #{fingerprintInfo,jdbcType=VARCHAR}
    </if>
    <if test="manufacturer != null" >
      and manufacturer = #{manufacturer,jdbcType=VARCHAR}
    </if>
    <if test="deviceId != null" >
      and device_id = #{deviceId,jdbcType=VARCHAR}
    </if>
    <if test="ids != null and ids.size > 0" >
      and id in
      <foreach collection="ids" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </select>
  <select id="selectOne" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from saas_employee_fingerprint
    where
    employee_id = #{employeeId,jdbcType=INTEGER}
      and fingerprint_info = #{fingerprintInfo,jdbcType=VARCHAR}
      and manufacturer = #{manufacturer,jdbcType=VARCHAR}
      and device_id = #{deviceId,jdbcType=VARCHAR}
      and organ_sign = #{organSign,jdbcType=VARCHAR}
  </select>
  <select id="selectFingerprintCountGroupByOrganSign"
          resultType="com.xyy.user.module.dto.result.FingerprintGroupInfoVO">
    select organ_sign as organSign, employee_id as employeeId, count(employee_id) as fingerprintCount
    from saas_employee_fingerprint
    where yn = 1 and organ_sign = #{organSign,jdbcType=VARCHAR} GROUP BY organ_sign,employee_id
  </select>
  <select id="selectFingerprintCountGroupByEmployeeId"
          resultType="com.xyy.user.module.dto.result.FingerprintGroupInfoVO">
     select organ_sign as organSign, employee_id as employeeId, count(employee_id) as fingerprintCount
    from saas_employee_fingerprint
    where yn = 1 and organ_sign = #{organSign,jdbcType=VARCHAR}
    and  employee_id = #{employeeId,jdbcType=INTEGER}
  </select>
</mapper>