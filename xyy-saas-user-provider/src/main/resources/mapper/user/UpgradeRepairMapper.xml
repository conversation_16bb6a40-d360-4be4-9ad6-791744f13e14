<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.user.provider.module.dao.UpgradeRepairMapper">

    <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.UpgradeRepairPo">
        <id column="id"  property="id" jdbcType="INTEGER" />
        <result column="version_name" property="versionName" jdbcType="VARCHAR"/>
        <result column="version_code" property="versionCode" jdbcType="INTEGER"/>
        <result column="repair_code" property="repairCode" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="download_url" property="downloadUrl" jdbcType="VARCHAR"/>
        <result column="yn" property="yn" jdbcType="TINYINT"/>
        </resultMap>


    <sql id="Base_Column_List">
      id,version_name,version_code,repair_code,create_time,downloadUrl,yn
   </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
      select
        <include refid="Base_Column_List" />
        from saas_upgrade_repair
      where id = #{id,jdbcType=INTEGER}

    </select>


    <select id="selectByVersionCode" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
          id,version_name,version_code,repair_code,create_time,download_url,yn
        from saas_upgrade_repair
        where yn=1 and version_code=#{versionCode} order by create_time desc limit 1
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
     delete from saas_upgrade_repair
     where id = #{id,jdbcType=INTEGER}
   </delete>

    <insert id="insert" parameterType="com.xyy.user.provider.module.entity.UpgradeRepairPo" >
        insert into saas_upgrade_repair
        (version_name,version_code,download_url,create_time,yn,repair_code)
        values(#{versionName,jdbcType=VARCHAR},#{versionCode,jdbcType=INTEGER},
      #{downloadUrl,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP},#{yn,jdbcType=TINYINT},#{repairCode,jdbcType=INTEGER})
    </insert>


    <update id="updateByPrimaryKeySelective" parameterType="com.xyy.user.provider.module.entity.UpgradeRepairPo">
        update saas_upgrade_repair
        <set>
         <if test="versionName != null">
            version_name = #{versionName,jdbcType=VARCHAR},
         </if>
         <if test="versionCode != null">
            version_code = #{versionCode,jdbcType=INTEGER},
         </if>

         <if test="repairCode != null">
            repair_code = #{repairCode,jdbcType=INTEGER},
         </if>
         <if test="createTime != null">
            create_time = #{createTime,jdbcType=TIMESTAMP},
         </if>
         <if test="downloadUrl != null">
             download_url = #{downloadUrl,jdbcType=VARCHAR},
         </if>
        <if test="yn != null">
            yn = #{yn,jdbcType=TINYINT},
        </if>
        </set>
        where  id = #{id,jdbcType=INTEGER}
     </update>

    <update id="updateByPrimaryKey" parameterType="com.xyy.user.provider.module.entity.UpgradeRepairPo">
        update saas_upgrade_repair
            set

            version_name = #{versionName,jdbcType=VARCHAR},
            version_code = #{versionCode,jdbcType=INTEGER},
            repair_code = #{repairCode,jdbcType=INTEGER},
            download_url=#{downloadUrl,jdbcType=VARCHAR},
            yn=#{yn,jdbcType=TINYINT},
            create_time=#{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
   </update>


</mapper>