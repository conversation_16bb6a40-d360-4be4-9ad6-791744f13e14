<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.user.provider.module.dao.SaasTrainingMapper" >
  <resultMap id="BaseResultMap" type="com.xyy.user.module.dto.training.TrainingRecordDto" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="topic" property="topic" jdbcType="VARCHAR" />
    <result column="purpose" property="purpose" jdbcType="VARCHAR" />
    <result column="organ_sign" property="organSign" jdbcType="VARCHAR" />
    <result column="training_method" property="trainingMethod" jdbcType="VARCHAR" />
    <result column="training_content" property="trainingContent" jdbcType="TINYINT" />
    <result column="training_num" property="trainingNum" jdbcType="TINYINT" />
    <result column="training_agency" property="trainingAgency" jdbcType="VARCHAR" />
    <result column="planned_date" property="plannedDate" jdbcType="TIMESTAMP" />
    <result column="start_date" property="startDate" jdbcType="TIMESTAMP" />
    <result column="end_date" property="endDate" jdbcType="TIMESTAMP" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="training_type" property="trainingType" jdbcType="INTEGER" />
    <result column="training_plan_id" property="trainingPlanId" jdbcType="INTEGER" />
    <result column="training_person" property="trainingPerson" jdbcType="VARCHAR" />
  </resultMap>
  
  <insert id="addTraining" parameterType="com.xyy.user.module.dto.training.TrainingRecordDto" useGeneratedKeys="true" keyProperty="id" >
    insert into saas_training
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="topic != null" >
        topic,
      </if>
      <if test="purpose != null" >
        purpose,
      </if>
      <if test="organSign != null" >
        organ_sign,
      </if>
      <if test="trainingMethod != null" >
        training_method,
      </if>
      <if test="trainingContent != null" >
        training_content,
      </if>
      <if test="trainingNum != null" >
        training_num,
      </if>
      <if test="trainingAgency != null" >
        training_agency,
      </if>
      <if test="plannedDate != null" >
        planned_date,
      </if>
      <if test="startDate != null" >
        start_date,
      </if>
      <if test="endDate != null" >
        end_date,
      </if>
      <if test="createId != null" >
        create_id,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="trainingPlanId != null" >
        training_plan_id,
      </if>
      <if test="trainingPerson != null" >
        training_person,
      </if>
      <if test="trainingType != null" >
        training_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="topic != null" >
        #{topic, jdbcType=VARCHAR},
      </if>
      <if test="purpose != null" >
        #{purpose, jdbcType=VARCHAR},
      </if>
      <if test="organSign != null" >
        #{organSign, jdbcType=VARCHAR},
      </if>
      <if test="trainingMethod != null" >
        #{trainingMethod, jdbcType=VARCHAR},
      </if>
      <if test="trainingContent != null" >
        #{trainingContent, jdbcType=VARCHAR},
      </if>
      <if test="trainingNum != null" >
        #{trainingNum, jdbcType=TINYINT},
      </if>
      <if test="trainingAgency != null" >
        #{trainingAgency, jdbcType=VARCHAR},
      </if>
      <if test="plannedDate != null" >
        #{plannedDate, jdbcType=TIMESTAMP},
      </if>
      <if test="startDate != null" >
        #{startDate, jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null" >
        #{endDate, jdbcType=TIMESTAMP},
      </if>
      <if test="createId != null" >
        #{createId, jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        #{status, jdbcType=TINYINT},
      </if>
      <if test="trainingPlanId != null" >
        #{trainingPlanId, jdbcType=INTEGER},
      </if>
      <if test="trainingPerson != null" >
        #{trainingPerson},
      </if>
      <if test="trainingType != null" >
        #{trainingType},
      </if>
    </trim>
  </insert>

  <select id="getTrainingById" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select * from saas_training 
    where id = #{id, jdbcType=INTEGER} and is_del = 0 
  </select>

  
  <select id="getTrainingByCondition" parameterType="com.xyy.user.module.dto.training.TrainingRequestModel" resultMap="BaseResultMap">
    select DISTINCT t.id ,t.topic,
    t.purpose,
    t.organ_sign,
    t.training_method,
    t.training_content,
    t.training_num,
    t.training_agency,
    t.planned_date,
    t.start_date,
    t.end_date,
    t.status,
    training_type,
    training_plan_id
    from saas_training  t 
    left join  saas_employee_training e on t.id=e.training_id 
    <where>
      1 = 1
      <if test="employeeId != null and employeeId != ''">
        AND e.employee_id = #{employeeId, jdbcType=INTEGER} 
      </if>
      <if test="topic != null and topic != ''">
        AND t.topic LIKE CONCAT('%',#{topic, jdbcType=VARCHAR},'%')
      </if>
      <if test="status != null ">
        AND t.status = #{status}
      </if>
      <if test="plannedStartDate != null and plannedStartDate!=''">
       <![CDATA[  AND t.planned_date  >= #{plannedStartDate, jdbcType=VARCHAR} ]]> 
      </if>
      <if test="plannedEndDate != null and plannedEndDate!=''">
      <![CDATA[  AND t.planned_date  <= #{plannedEndDate, jdbcType=VARCHAR} ]]>
      </if>
      <if test="ids != null and ids.size>0">
        and id in(
        <foreach collection="ids" item="idItem" index="index" separator="," >
          #{idItem}
        </foreach >
        )
      </if>
      and t.organ_sign = #{organSign, jdbcType=VARCHAR} and t.is_del = 0
    </where>
    order by t.planned_date desc,t.id desc
  </select>

  <update id="delTrainingById">
    UPDATE saas_training
    <set>
      is_del = 1,
      update_date = NOW(),
      update_id = #{updateId, jdbcType=INTEGER}
    </set>
    WHERE id = #{id, jdbcType=INTEGER} 
  </update>

  <update id="editTraining">
    update saas_training
    <set >
      <if test="topic != null" >
        topic = #{topic, jdbcType=VARCHAR},
      </if>
      <if test="purpose != null" >
        purpose = #{purpose, jdbcType=VARCHAR},
      </if>
      <if test="organSign != null" >
        organ_sign = #{organSign, jdbcType=VARCHAR},
      </if>
      <if test="trainingMethod != null" >
        training_method = #{trainingMethod, jdbcType=VARCHAR},
      </if>
      <if test="trainingContent != null" >
        training_content = #{trainingContent, jdbcType=VARCHAR},
      </if>
      <if test="trainingNum != null" >
        training_num = #{trainingNum, jdbcType=INTEGER},
      </if>
      <if test="trainingAgency != null" >
        training_agency = #{trainingAgency, jdbcType=VARCHAR},
      </if>
      <if test="plannedDate != null" >
        planned_date = #{plannedDate, jdbcType=TIMESTAMP},
      </if>
      <if test="startDate != null" >
        start_date = #{startDate, jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null" >
        end_date = #{endDate, jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null" >
        update_id = #{updateId, jdbcType=INTEGER},
      </if>
        update_date = NOW(),
      <if test="status != null" >
        status = #{status, jdbcType=TINYINT},
      </if>
      <if test="trainingPerson != null" >
        training_person = #{trainingPerson, jdbcType=VARCHAR},
      </if>
      <if test="trainingType != null" >
        training_type = #{trainingType},
      </if>
    </set>
    where id = #{id, jdbcType=INTEGER}
  </update>

</mapper>