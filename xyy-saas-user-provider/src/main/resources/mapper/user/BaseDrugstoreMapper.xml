<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.user.provider.module.dao.base.BaseDrugstoreMapper">
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.base.SaasDrugstorePo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="manager_user_id" jdbcType="INTEGER" property="managerUserId" />
    <result column="drugstore_name" jdbcType="VARCHAR" property="drugstoreName" />
    <result column="manager_name" jdbcType="VARCHAR" property="managerName" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="organ_sign" jdbcType="VARCHAR" property="organSign" />
    <result column="headquarters_organ_sign" jdbcType="VARCHAR" property="headquartersOrganSign" />
    <result column="sender_id" jdbcType="VARCHAR" property="senderId" />
    <result column="organ_sign_type" jdbcType="TINYINT" property="organSignType" />
    <result column="biz_model" jdbcType="TINYINT" property="bizModel" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="audit_record_id" jdbcType="INTEGER" property="auditRecordId" />
    <result column="business_license_name" jdbcType="VARCHAR" property="businessLicenseName" />
    <result column="business_license_number" jdbcType="VARCHAR" property="businessLicenseNumber" />
    <result column="business_license_img" jdbcType="VARCHAR" property="businessLicenseImg" />
    <result column="pharmaceutical_trading_license_img" jdbcType="VARCHAR" property="pharmaceuticalTradingLicenseImg" />
    <result column="quality_management_license_img" jdbcType="VARCHAR" property="qualityManagementLicenseImg" />
    <result column="referral_code" jdbcType="VARCHAR" property="referralCode" />
    <result column="sign_status" jdbcType="TINYINT" property="signStatus" />
    <result column="terminate_reason" jdbcType="VARCHAR" property="terminateReason" />
    <result column="terminate_time" jdbcType="TIMESTAMP" property="terminateTime" />
    <result column="congeal_time" jdbcType="TIMESTAMP" property="congealTime" />
    <result column="congeal_person" jdbcType="VARCHAR" property="congealPerson" />
    <result column="storehouse_yn" jdbcType="TINYINT" property="storehouseYn" />
    <result column="account_set_name" jdbcType="VARCHAR" property="accountSetName" />
    <result column="main_yn" jdbcType="TINYINT" property="mainYn" />
    <result column="main_organ_sign" jdbcType="VARCHAR" property="mainOrganSign" />
    <result column="msfx_sign" jdbcType="VARCHAR" property="msfxSign" />
  </resultMap>

  <sql id="Base_Column_List">
    id, manager_user_id, drugstore_name, manager_name, contact_phone, organ_sign, headquarters_organ_sign,
    sender_id, organ_sign_type, biz_model, province, city, area, area_code, address,
    type, status, create_time, update_time, audit_record_id, business_license_name, business_license_number,
    business_license_img, pharmaceutical_trading_license_img, quality_management_license_img,
    referral_code, sign_status, terminate_reason, terminate_time, congeal_time, congeal_person,
    storehouse_yn, account_set_name, main_yn, main_organ_sign, msfx_sign
  </sql>

  <select id="listDrugstores" parameterType="com.xyy.user.module.dto.base.SaasDrugstoreQueryDTO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from saas_drugstore
    <where>
      <if test="organSign != null and organSign != ''">
        organ_sign = #{organSign}
      </if>
      <if test="drugstoreName != null and drugstoreName != ''">
        and drugstore_name LIKE CONCAT('%',#{drugstoreName},'%')
      </if>
      <if test="organSignType != null">
        and organ_sign_type = #{organSignType, jdbcType=TINYINT}
      </if>
      <if test="bizModel != null">
        and biz_model = #{bizModel, jdbcType=TINYINT}
      </if>
      <if test="status != null">
        and status =  #{status, jdbcType=TINYINT}
      </if>
    </where>
    <if test="orderByClause != null and orderByClause != ''">
      order by ${orderByClause}
    </if>
  </select>

  <select id="getDrugstore" parameterType="com.xyy.user.module.dto.base.SaasDrugstoreQueryDTO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from saas_drugstore
    <where>
      <if test="organSign != null and organSign != ''">
        organ_sign = #{organSign}
      </if>
    </where>
  </select>

  <update id="updateDrugstore" parameterType="com.xyy.user.provider.module.entity.base.SaasDrugstorePo" >
    update saas_drugstore
    <set>
      <if test="msfxSign != null" >
        msfx_sign = #{msfxSign},
      </if>
    </set>
    where organ_sign = #{organSign}
  </update>

  <select id="listDrugstoresWhithArea" parameterType="com.xyy.user.module.dto.base.DrugstoreAreaQueryDto" resultType="string">
    select
    organ_sign
    from saas_drugstore
    <where>
      <if test="organSign != null and organSign != ''">
        organ_sign = #{organSign}
      </if>
      <if test="organSignType != null">
        and organ_sign_type = #{organSignType, jdbcType=TINYINT}
      </if>
      <if test="bizModel != null">
        and biz_model = #{bizModel, jdbcType=TINYINT}
      </if>
      <if test="status != null">
        and status =  #{status, jdbcType=TINYINT}
      </if>
      <if test="excludeStatus != null">
        and status !=  #{excludeStatus, jdbcType=TINYINT}
      </if>
      <if test="province != null and province.size > 0">
        and province in
        <foreach collection="province" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="city != null and city.size > 0">
        and city in
        <foreach collection="city" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="area != null and area.size > 0">
        and area in
        <foreach collection="area" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
    <if test="orderByClause != null and orderByClause != ''">
      order by ${orderByClause}
    </if>
  </select>

</mapper>