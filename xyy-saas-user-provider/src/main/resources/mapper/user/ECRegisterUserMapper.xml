<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.user.provider.module.dao.ECRegisterUserMapper" >
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.ECRegisterUser" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="ybm_user_id" property="ybmUserId" jdbcType="INTEGER" />
    <result column="source_path" property="sourcePath" jdbcType="VARCHAR" />
    <result column="organ_sign" property="organSign" jdbcType="VARCHAR" />
    <result column="ybm_user_name" property="ybmUserName" jdbcType="VARCHAR" />
    <result column="ybm_password" property="ybmPassword" jdbcType="VARCHAR" />
    <result column="ybm_code" property="ybmCode" jdbcType="VARCHAR" />
    <result column="ybm_register_time" property="ybmRegisterTime" jdbcType="INTEGER" />
    <result column="enabled" property="enabled" jdbcType="TINYINT" />
    <result column="qualification_status" property="qualificationStatus" jdbcType="TINYINT" />
  </resultMap>
  <insert id="addECRegisterUser" parameterType="com.xyy.user.provider.module.entity.ECRegisterUser" useGeneratedKeys="true" keyProperty="id" >
    insert into saas_ec_register_user
    (
    organ_sign,
    source_path,
    ybm_user_id,
    ybm_user_name,
    ybm_password,
    ybm_code,
    ybm_register_time,
    enabled,
    qualification_status
    )
    values
    (
    #{organSign, jdbcType=VARCHAR},
    #{sourcePath, jdbcType=VARCHAR},
    #{ybmUserId, jdbcType=INTEGER},
    #{ybmUserName, jdbcType=VARCHAR},
    #{ybmPassword, jdbcType=VARCHAR},
      #{ybmCode, jdbcType=VARCHAR},
      #{ybmRegisterTime, jdbcType=INTEGER},
      #{enabled, jdbcType=TINYINT},
      #{qualificationStatus, jdbcType=TINYINT}
    )
  </insert>

  <!-- 返券规则列表查询 -->
  <select id="ybmNewUserList" parameterType="com.xyy.user.provider.module.entity.ECRegisterUser" resultType="com.xyy.user.module.dto.ECRegisterUserDto" >
    select
    seru.id as id,
    seru.organ_sign as organSign,
    seru.source_path as sourcePath,
    seru.ybm_user_name as ybmUserName,
    seru.ybm_password as ybmPassword,
    seru.ybm_code as ybmCode,
    seru.update_time as updateTime,
    seru.ybm_register_time as ybmRegisterTime,
    seru.enabled as enabled,
    seru.qualification_status as qualificationStatus,
    from saas_ec_register_user seru
    WHERE 1=1
    <if test="organSign!= null and organSign!=''">
      AND  seru.organ_sign = #{organSign}
    </if>
    <if test="startTime!= null and startTime!=''and endTime!=null and endTime!=''">
      AND seru.ybm_register_time between #{startTime} and #{endTime}
    </if>
    <if test="ybmUserName!= null and ybmUserName!=''">
      AND  seru.ybm_user_name = #{ybmUserName}
    </if>
    <if test="ybmPassword!= null and ybmPassword!=''">
      AND  seru.ybm_password = #{ybmPassword}
    </if>
    <if test="ybmCode!= null and ybmCode!=''">
      AND  seru.ybm_code = #{ybmCode}
    </if>
    <if test="updateTime!= null and updateTime!=''">
      AND  seru.update_time = #{updateTime}
    </if>
    <if test="ybmRegisterTime!= null and ybmRegisterTime!=''">
      AND  seru.ybm_register_time = #{ybmRegisterTime}
    </if>
    <if test="enabled!= null and enabled!=''">
      AND  seru.enabled = #{enabled}
    </if>
    <if test="qualificationStatus!= null and qualificationStatus!=''">
      AND  seru.qualification_status = #{qualificationStatus}
    </if>
    order by seru.ybm_register_time desc
  </select>


  <select id="getECRegisterDetail"  resultMap="BaseResultMap">
   select t.*
    from saas_ec_register_user  t
   <where>
     <if test="organSign!= null and organSign!=''">
       AND  organ_sign = #{organSign}
     </if>
     <if test="ybmUserName!= null and ybmUserName!=''">
       AND  ybm_user_name = #{ybmUserName}
     </if>
    </where>
      order by ybm_register_time desc
  </select>

    <update id="updateYBMNewUser" parameterType="com.xyy.user.provider.module.entity.ECRegisterUser">
        UPDATE saas_ec_register_user
        <set>
            <if test="updateTime !=null and updateTime !='' ">update_time=#{updateTime},</if>
            <if test="enabled !=null and enabled !='' ">enabled=#{enabled},</if>
            <if test="qualificationStatus !=null and qualificationStatus !='' ">qualification_status=#{qualificationStatus},</if>
        </set>
        WHERE organ_sign = #{organSign} and ybm_user_name=#{ybmUserName}
    </update>


    <select id="selectNewUserByUserName" parameterType="com.xyy.user.provider.module.entity.ECRegisterUser" resultType="com.xyy.user.module.dto.ECRegisterUserDto" >
        select seru.*
        from saas_ec_register_user seru
        WHERE 1=1
        <if test="organSign!= null and organSign!=''">
            AND  seru.organ_sign = #{organSign}
        </if>
        <if test="startTime!= null and startTime!=''and endTime!=null and endTime!=''">
            AND seru.ybm_register_time between #{startTime} and #{endTime}
        </if>
        <if test="ybmUserName!= null and ybmUserName!=''">
            AND  seru.ybm_user_name = #{ybmUserName}
        </if>
        <if test="ybmPassword!= null and ybmPassword!=''">
            AND  seru.ybm_password = #{ybmPassword}
        </if>
        <if test="ybmCode!= null and ybmCode!=''">
            AND  seru.ybm_code = #{ybmCode}
        </if>
        <if test="updateTime!= null and updateTime!=''">
            AND  seru.update_time = #{updateTime}
        </if>
        <if test="ybmRegisterTime!= null and ybmRegisterTime!=''">
            AND  seru.ybm_register_time = #{ybmRegisterTime}
        </if>
        <if test="enabled!= null and enabled!=''">
            AND  seru.enabled = #{enabled}
        </if>
        <if test="qualificationStatus!= null and qualificationStatus!=''">
            AND  seru.qualification_status = #{qualificationStatus}
        </if>
    </select>
</mapper>