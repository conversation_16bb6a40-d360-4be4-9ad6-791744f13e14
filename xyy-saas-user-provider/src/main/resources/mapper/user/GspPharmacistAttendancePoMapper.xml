<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.user.provider.module.dao.GspPharmacistAttendancePoMapper" >
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.GspPharmacistAttendancePo" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="username" property="username" jdbcType="VARCHAR" />
    <result column="attendance_mode" property="attendanceMode" jdbcType="TINYINT" />
    <result column="profession" property="profession" jdbcType="VARCHAR" />
    <result column="identity" property="identity" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="create_user" property="createUser" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="yn" property="yn" jdbcType="TINYINT" />
    <result column="type_name" property="typeName" jdbcType="VARCHAR" />
    <result column="real_name" property="realName" jdbcType="VARCHAR" />
    <result column="organSign" property="organSign" jdbcType="VARCHAR" />
  </resultMap>

  <resultMap id="BasePageResultMap" type="com.xyy.user.module.dto.restructure.GspPharmacistAttendancePo" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="username" property="username" jdbcType="VARCHAR" />
    <result column="attendance_mode" property="attendanceMode" jdbcType="TINYINT" />
    <result column="profession" property="profession" jdbcType="VARCHAR" />
    <result column="identity" property="identity" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="create_user" property="createUser" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="yn" property="yn" jdbcType="TINYINT" />
    <result column="type_name" property="typeName" jdbcType="VARCHAR" />
    <result column="real_name" property="realName" jdbcType="VARCHAR" />
    <result column="organSign" property="organSign" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    e.id, e.username, e.profession, e.identity, e.type, e.create_user,
    e.create_time, e.update_user, e.update_time, e.yn,e.organSign,attendance_mode
  </sql>
  <select id="findGspPharmacistAttendancePo" resultMap="BasePageResultMap" parameterType="com.xyy.user.module.dto.restructure.GspPharmacistAttendancePo" >
    select
    <include refid="Base_Column_List" />
    from saas_pharmacist_attendance e
    where e.yn = 1
      and e.organSign = #{organSign}
      <if test="username != null and username != ''" >
       and e.username like  concat(concat('%',#{username}),'%')
      </if>
      <if test="type == 1 or type == 2" >
       and e.type = #{type,jdbcType=INTEGER}
      </if>
      <if test="createTime !=null" >
       and e.create_time>=date(now()) and e.create_time &lt; DATE_ADD(date(now()),INTERVAL 1 DAY)
      </if>
      <if test="createStartTime != null">
        <![CDATA[ and e.create_time >= #{createStartTime} ]]>
      </if>
      <if test="createEndTime != null ">
        <![CDATA[ and #{createEndTime} >= e.create_time ]]>
      </if>
      order by e.create_time desc
  </select>
  <select id="findGspPharmacistAttendancePoList" parameterType="com.xyy.user.provider.module.vo.GspPharmacistAttendanceVO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from saas_pharmacist_attendance e
    where e.yn = 1
    and e.organSign = #{organSign}
    <if test="userId != null and userId != ''" >
      and e.create_user= #{userId}
    </if>
    <![CDATA[  and e.create_time >= DATE_FORMAT(now(),'%Y-%m-%d')]]>

  </select>
  <insert id="insert" parameterType="com.xyy.user.module.dto.restructure.GspPharmacistAttendancePo" >
    insert into saas_pharmacist_attendance
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="username != null" >
        username,
      </if>
      <if test="attendanceMode != null" >
        attendance_mode,
      </if>
      <if test="profession != null" >
        profession,
      </if>
      <if test="identity != null" >
        identity,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="createUser != null" >
        create_user,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateUser != null" >
        update_user,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="yn != null" >
        yn,
      </if>
      <if test="organSign != null" >
        organSign,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="username != null" >
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="attendanceMode != null" >
        #{attendanceMode,jdbcType=TINYINT},
      </if>
      <if test="profession != null" >
        #{profession,jdbcType=VARCHAR},
      </if>
      <if test="identity != null" >
        #{identity,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=INTEGER},
      </if>
      <if test="createUser != null" >
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        now(),
      </if>
      <if test="updateUser != null" >
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        now(),
      </if>
      <if test="yn != null" >
        1,
      </if>
      <if test="organSign != null" >
        #{organSign},
      </if>
    </trim>
  </insert>
  <update id="delete" parameterType="com.xyy.user.provider.module.entity.GspPharmacistAttendancePo" >
    update saas_pharmacist_attendance SET yn= 0,update_time=NOW() where id = #{id,jdbcType=INTEGER} and organSign = #{organSign}
  </update>
  <select id="selectLastRecord" parameterType="com.xyy.user.provider.module.vo.GspPharmacistAttendanceVO" resultMap="BasePageResultMap">
    select
    <include refid="Base_Column_List" />
    from saas_pharmacist_attendance e
    where e.yn = 1
    and e.organSign = #{organSign}
    and e.create_user= #{userId}
    <![CDATA[  and e.create_time >= DATE_FORMAT(now(),'%Y-%m-%d')]]>
    order by id desc limit 1
  </select>

  <update id="updateRecord" parameterType="com.xyy.user.module.dto.restructure.GspPharmacistAttendancePo">
    update saas_pharmacist_attendance
    <set>
      <if test="username != null" >
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null" >
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="attendanceMode != null" >
        attendance_mode = #{attendanceMode,jdbcType=TINYINT},
      </if>
      <if test="yn != null" >
        yn = #{yn,jdbcType=TINYINT},
      </if>
      <if test="updateUser != null" >
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

</mapper>