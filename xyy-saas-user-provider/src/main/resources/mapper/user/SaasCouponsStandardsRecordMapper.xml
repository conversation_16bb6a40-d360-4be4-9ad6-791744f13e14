<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.user.provider.module.dao.SaasCouponsStandardsRecordMapper" >
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasCouponsStandardsRecord" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="organ_sign" property="organSign" jdbcType="VARCHAR" />
    <result column="contract_code" property="contractCode" jdbcType="VARCHAR" />
    <result column="month" property="month" jdbcType="INTEGER" />
    <result column="assessmen_month" property="assessmenMonth" jdbcType="VARCHAR" />
    <result column="assessmen_week" property="assessmenWeek" jdbcType="INTEGER" />
    <result column="standards_status" property="standardsStatus" jdbcType="TINYINT" />
    <result column="standards_type" property="standardsType" jdbcType="TINYINT" />
    <result column="week_user_day" property="weekUserDay" jdbcType="INTEGER" />
    <result column="week_user_sum" property="weekUserSum" jdbcType="INTEGER" />
    <result column="month_user_day" property="monthUserDay" jdbcType="INTEGER" />
    <result column="month_order_amount" property="monthOrderAmount" jdbcType="DECIMAL" />
    <result column="month_purchase_num" property="monthPurchaseNum" jdbcType="INTEGER" />
    <result column="average_day_amount" property="averageDayAmount" jdbcType="DECIMAL" />
    <result column="standards_coupons_list" property="standardsCouponsList" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="yn" property="yn" jdbcType="TINYINT" />
  </resultMap>

  <sql id="Base_Column_List">
`id`, `organ_sign`, `contract_code`, `month`, `assessmen_month`, `assessmen_week`, `standards_type`, `standards_status`, `week_user_day`, `week_user_sum`, `month_user_day`, `month_order_amount`, `month_purchase_num`, `average_day_amount`, `standards_coupons_list`, `create_time`, `update_time`
 </sql>

  <insert id="add" parameterType="com.xyy.user.provider.module.entity.SaasCouponsStandardsRecord" useGeneratedKeys="true" keyProperty="id" >
    insert into saas_coupons_standards_record
    (
    organ_sign,
    contract_code,
    month,assessmen_month,assessmen_week,standards_status,
    standards_type,
    week_user_day,
    week_user_sum,
    month_user_day,
    month_order_amount,
    month_purchase_num,
    average_day_amount,
    create_time,
    standards_coupons_list
    )
    values
    (
    #{drugstoreCouponsRecord.organSign, jdbcType=VARCHAR},
    #{drugstoreCouponsRecord.contractCode, jdbcType=VARCHAR},
    #{drugstoreCouponsRecord.month, jdbcType=INTEGER},
    #{drugstoreCouponsRecord.assessmenMonth, jdbcType=VARCHAR},
    #{drugstoreCouponsRecord.assessmenWeek, jdbcType=INTEGER},
    #{drugstoreCouponsRecord.standardsStatus, jdbcType=TINYINT},
    #{drugstoreCouponsRecord.standardsType, jdbcType=TINYINT},
    #{drugstoreCouponsRecord.weekUserDay, jdbcType=INTEGER},
    #{drugstoreCouponsRecord.weekUserSum, jdbcType=INTEGER},
    #{drugstoreCouponsRecord.monthUserDay, jdbcType=INTEGER},
      #{drugstoreCouponsRecord.monthOrderAmount, jdbcType=DECIMAL},
      #{drugstoreCouponsRecord.monthPurchaseNum, jdbcType=INTEGER},
      #{drugstoreCouponsRecord.averageDayAmount, jdbcType=DECIMAL},
      #{drugstoreCouponsRecord.createTime, jdbcType=TIMESTAMP},
      #{drugstoreCouponsRecord.standardsCouponsList, jdbcType=VARCHAR}
    )
  </insert>
  <insert id="addAndGetKey" parameterType="com.xyy.user.provider.module.entity.SaasCouponsStandardsRecord" useGeneratedKeys="true" keyProperty="id">
  insert into saas_coupons_standards_record
    (
    organ_sign,
    contract_code,
    month,assessmen_month,assessmen_week,standards_status,
    standards_type,
    week_user_day,
    week_user_sum,
    month_user_day,
    month_order_amount,
    month_purchase_num,
    average_day_amount,
    create_time,
    standards_coupons_list
    )
    values
    (
    #{organSign, jdbcType=VARCHAR},
    #{contractCode, jdbcType=VARCHAR},
    #{month, jdbcType=INTEGER},
    #{assessmenMonth, jdbcType=VARCHAR},
    #{assessmenWeek, jdbcType=INTEGER},
    #{standardsStatus, jdbcType=TINYINT},
    #{standardsType, jdbcType=TINYINT},
    #{weekUserDay, jdbcType=INTEGER},
    #{weekUserSum, jdbcType=INTEGER},
    #{monthUserDay, jdbcType=INTEGER},
    #{monthOrderAmount, jdbcType=DECIMAL},
    #{monthPurchaseNum, jdbcType=INTEGER},
    #{averageDayAmount, jdbcType=DECIMAL},
    #{createTime, jdbcType=TIMESTAMP},
    #{standardsCouponsList, jdbcType=VARCHAR}
    )
  </insert>

  
  <select id="getCouponsRecordListByMonth"  resultMap="BaseResultMap" parameterType="java.util.Map">
    select *
    from saas_coupons_standards_record
    <where>
      organ_sign = #{organSign, jdbcType=VARCHAR} and contract_code = #{contractCode, jdbcType=VARCHAR} and standards_type=2
      and yn = 0
      <if test="assessmenWeek!=null">
        and assessmen_week = #{assessmenWeek, jdbcType=INTEGER}
      </if><if test="assessmenMonth!=null and assessmenMonth!=''">
      and assessmen_month = #{assessmenMonth, jdbcType=VARCHAR}
    </if><if test="standardsStatus!=null">
      and standards_status = #{standardsStatus, jdbcType=TINYINT}
    </if>
    </where>
    order by assessmen_month desc limit 2
  </select>

  <select id="getCouponsRecordListByWeek"  resultMap="BaseResultMap" parameterType="java.util.Map">
    select *
    from saas_coupons_standards_record
    <where>
      organ_sign = #{organSign, jdbcType=VARCHAR} and contract_code = #{contractCode, jdbcType=VARCHAR}  and standards_type=1
      and yn = 0
      <if test="assessmenWeek!=null ">
          and assessmen_week = #{assessmenWeek, jdbcType=INTEGER}
      </if><if test="assessmenMonth!=null and assessmenMonth!=''">
      and assessmen_month = #{assessmenMonth, jdbcType=VARCHAR}
    </if>
      <if test="standardsStatus!=null">
        and standards_status = #{standardsStatus, jdbcType=TINYINT}
      </if>
    </where>
    order by assessmen_week desc limit 5
  </select>

    <update id="update" parameterType="com.xyy.user.provider.module.entity.SaasCouponsStandardsRecord">
    update saas_coupons_standards_record
    <set>
      month = #{record.month, jdbcType=INTEGER},
      assessmen_month=#{record.assessmenMonth, jdbcType=VARCHAR},
      assessmen_week=#{record.assessmenWeek, jdbcType=INTEGER},
      standards_status=#{record.standardsStatus, jdbcType=TINYINT},
      standards_type=#{record.standardsType, jdbcType=TINYINT},
      <!--<if test="record.weekUserDay!=null and record.weekUserDay!=''">-->
          week_user_day = #{record.weekUserDay, jdbcType=INTEGER},
      <!--</if>
      <if test="record.weekUserSum!=null and record.weekUserSum!=''">-->
          week_user_sum = #{record.weekUserSum, jdbcType=INTEGER},
      <!--</if>
      <if test="record.monthUserDay!=null and record.monthUserDay!=''">-->
          month_user_day = #{record.monthUserDay, jdbcType=INTEGER},
      <!--</if><if test="record.monthOrderAmount!=null and record.monthOrderAmount!=''">-->
        month_order_amount = #{record.monthOrderAmount, jdbcType=DECIMAL},
    <!--</if><if test="record.monthPurchaseNum!=null and record.monthPurchaseNum!=''">-->
        month_purchase_num = #{record.monthPurchaseNum, jdbcType=INTEGER},
    <!--</if><if test="record.averageDayAmount!=null and record.averageDayAmount!=''">-->
        average_day_amount = #{record.averageDayAmount, jdbcType=DECIMAL}
    <!--</if>-->
    </set>
    WHERE organ_sign = #{record.organSign, jdbcType=VARCHAR} and contract_code = #{record.contractCode, jdbcType=VARCHAR} and assessmen_month = #{record.assessmenMonth, jdbcType=VARCHAR}
  </update>

  <select id="getCouponsRecordListByCondition"  resultMap="BaseResultMap" parameterType="java.util.Map">
    select *
    from saas_coupons_standards_record
    <where>
      <if test="organSign!=null and organSign!=''">
        organ_sign = #{organSign, jdbcType=VARCHAR}
      </if>
      <if test="contractCode!=null and contractCode!=''">
        and contract_code = #{contractCode, jdbcType=VARCHAR}
      </if>
      <if test="assessmenWeek!=null ">
        and assessmen_week = #{assessmenWeek, jdbcType=INTEGER}
      </if>
      <if test="assessmenMonth!=null and assessmenMonth!=''">
      and assessmen_month = #{assessmenMonth, jdbcType=VARCHAR}
      </if>
      <if test="standardsStatus!=null">
        and standards_status = #{standardsStatus, jdbcType=TINYINT}
      </if>
      <if test="standardsType!=null ">
        and standards_type = #{standardsType, jdbcType=TINYINT}
      </if>
      <if test="organSignList != null and organSignList.size() > 0">
        and organ_sign in
        <foreach collection="organSignList" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      and yn = 0
    </where>
    order by create_time desc
  </select>

  <select id="getCouponsStandardsWeekRecordList"  resultMap="BaseResultMap" parameterType="java.util.Map">
    select distinct organ_sign ,contract_code
    from saas_coupons_standards_record
    where
         standards_type = 1
        and standards_status = 0
        and yn = 0
    order by create_time desc
  </select>

  <select id="getById" resultMap="BaseResultMap">
    select *
    from saas_coupons_standards_record where id = #{id}
  </select>

  <update id="updateYnDisabled">
    update saas_coupons_standards_record
    set yn = 1
    <where>
      organ_sign = #{organSign, jdbcType=VARCHAR}
      <if test="contractCode!=null and contractCode!=''">
        and contract_code = #{contractCode, jdbcType=VARCHAR}
      </if>
      <if test="assessmenWeek!=null ">
        and assessmen_week = #{assessmenWeek, jdbcType=INTEGER}
      </if>
      <if test="assessmenMonth!=null and assessmenMonth!=''">
        and assessmen_month = #{assessmenMonth, jdbcType=VARCHAR}
      </if>
      <if test="standardsStatus!=null">
        and standards_status = #{standardsStatus, jdbcType=TINYINT}
      </if>
      <if test="standardsType!=null ">
        and standards_type = #{standardsType, jdbcType=TINYINT}
      </if>
    </where>
  </update>

  <update id="updateAssessmenMonth">
      update saas_coupons_standards_record set assessmen_month = #{assessmenMonth,jdbcType=VARCHAR} where id = #{id,jdbcType=BIGINT}
   </update>

  <select id="getListByOrganSignAndContactCodeForceMaster" resultMap="BaseResultMap">
    /*FORCE_MASTER*/
    SELECT <include refid="Base_Column_List"/>
    FROM saas_coupons_standards_record
    WHERE organ_sign = #{organSign,jdbcType=VARCHAR}
    AND contract_code = #{contractCode,jdbcType=TINYINT}
    AND yn = 0
  </select>

</mapper>