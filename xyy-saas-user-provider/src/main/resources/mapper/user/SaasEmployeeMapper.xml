<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.user.provider.module.dao.SaasEmployeeMapper" >
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasEmployee" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="login_name" property="loginName" jdbcType="VARCHAR" />
    <result column="password" property="password" jdbcType="VARCHAR" />
    <result column="organ_sign" property="organSign" jdbcType="VARCHAR" />
    <result column="organ_sign_type" property="organSignType" jdbcType="TINYINT" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="sex" property="sex" jdbcType="TINYINT" />
    <result column="national" property="national" jdbcType="VARCHAR" />
    <result column="id_card" property="idCard" jdbcType="VARCHAR" />
    <result column="working_state" property="workingState" jdbcType="TINYINT" />
    <result column="joining_time" property="joiningTime" jdbcType="TIMESTAMP" />
    <result column="resignation_time" property="resignationTime" jdbcType="TIMESTAMP" />
    <result column="identity" property="identity" jdbcType="TINYINT" />
    <result column="credit" property="credit" jdbcType="VARCHAR" />
  </resultMap>
  
  <resultMap id="employeefileMap" type="com.xyy.user.module.dto.result.EmployeeDto" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="login_name" property="loginName" jdbcType="VARCHAR" />
    <result column="organ_sign" property="organSign" jdbcType="VARCHAR" />
    <result column="organ_sign_type" property="organSignType" jdbcType="TINYINT" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="sex" property="sex" jdbcType="TINYINT" />
    <result column="national" property="national" jdbcType="VARCHAR" />
    <result column="id_card" property="idCard" jdbcType="VARCHAR" />
    <result column="working_state" property="workingState" jdbcType="TINYINT" />
    <result column="joining_time" property="joiningTime" jdbcType="TIMESTAMP" />
    <result column="resignation_time" property="resignationTime" jdbcType="TIMESTAMP" />
    <result column="identity" property="identity" jdbcType="TINYINT" />
    <result column="school" property="school" jdbcType="VARCHAR" />
    <result column="major" property="major" jdbcType="VARCHAR" />
    <result column="level" property="level" jdbcType="TINYINT" />
    <result column="certification_no" property="certificationNo" jdbcType="VARCHAR" />
    <result column="registration_no" property="registrationNo" jdbcType="VARCHAR" />
    <result column="expire_date" property="expireDate" jdbcType="TIMESTAMP" />
    <result column="degree" property="degree" jdbcType="TINYINT" />
    <result column="technical_titles" property="technicalTitles" jdbcType="TINYINT" />
    <result column="is_disabled" property="isDisabled" jdbcType="TINYINT" />
    <result column="role_id" property="rolePriId" jdbcType="TINYINT" />
    <result column="credit" property="credit" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="employee_base_where" >
    <if test="id != null">
      and id = #{id, jdbcType=INTEGER}
    </if>
    <if test="loginName != null and loginName != ''">
      and login_name = #{loginName, jdbcType=VARCHAR}
    </if>
    <if test="phone != null and phone != ''">
      and phone LIKE CONCAT('%',#{phone, jdbcType=VARCHAR},'%')
    </if>
    <if test="organSign != null and organSign != ''">
      and organ_sign = #{organSign, jdbcType=VARCHAR}
    </if>
    <if test="organSignType != null">
      and organ_sign_type = #{organSignType, jdbcType=TINYINT}
    </if>
    <if test="workingState != null">
      and working_state = #{workingState, jdbcType=TINYINT}
    </if>
    <if test="userBindId != null">
      and user_bind_id = #{userBindId, jdbcType=INTEGER}
    </if>

  </sql>

  <sql id="employee_name_equals_where" >
  	  <if test="name != null and name != ''">
         and name = #{name, jdbcType=VARCHAR}
      </if>
  </sql>
  
  <sql id="employee_name_like_where" >
   	  <if test="name != null and name != ''">
         and name LIKE CONCAT('%',#{name, jdbcType=VARCHAR},'%')
      </if>
  </sql>
  
  <sql id="employee_multiple_organsign_where" >
	<if test="multipleOrganSign != null">
	    and organ_sign in 
		<foreach collection="multipleOrganSign" item="org" open="(" close=")" separator=",">
		  #{org}
	  	</foreach>
	</if>
  </sql>
  
  <update id="updateManagementLoginNameById" parameterType="com.xyy.user.module.dto.restructure.SaaSEmployeeDto">
    update saas_employee 
    set 
    login_name = #{loginName, jdbcType=VARCHAR}, 
    base_version = (select IFNULL(employee.version, 0) from (select max(base_version) as version from saas_employee where organ_sign= #{organSign, jdbcType=VARCHAR}) as employee) + 1
    where id = #{id, jdbcType=INTEGER}
  </update>
  
  <select id="getEmployeeByLoginName" parameterType="java.lang.String" resultType="com.xyy.user.module.dto.restructure.SaaSEmployeeDto">
	select * from saas_employee where login_name = #{loginName, jdbcType=VARCHAR} and working_state = 1
  </select>
  
  <select id="getEmployeeByConditionAndMultipleOrganSign" parameterType="com.xyy.user.module.dto.restructure.SaaSEmployeeExtendDto" resultType="com.xyy.user.module.dto.restructure.SaaSEmployeeDto"> 
	select * from saas_employee 
	<where>
	  1 = 1 
	  <include refid="employee_multiple_organsign_where" />
	  <include refid="employee_name_equals_where" />
      <if test="workingState != null">
        AND working_state = #{workingState, jdbcType=TINYINT}
      </if>
    </where>
  </select>
  
  <select id="getSaaSEmployeeByCondition" parameterType="com.xyy.user.module.dto.restructure.SaaSEmployeeDto" resultType="com.xyy.user.module.dto.restructure.SaaSEmployeeDto">
	select * from saas_employee
    <where>
      1 = 1 
      <include refid="employee_base_where" />
      <include refid="employee_name_like_where" />
    </where>
    order by id desc
  </select>
  
  <!-- 分割线分割线分割线分割线分割线分割线分割线分割线分割线分割线  华丽的分割线，分隔过去和将来  分割线分割线分割线分割线分割线分割线分割线分割线分割线分割线分割线分割线 -->
  
  <insert id="addEmployee" parameterType="com.xyy.user.provider.module.entity.SaasEmployee" useGeneratedKeys="true" keyProperty="id" >
    insert into saas_employee
    (
    login_name,
    password,
    organ_sign,
    organ_sign_type,
    name,
    phone,
    sex,
    national,
    id_card,
    working_state,
    joining_time,
    resignation_time,
    identity,
    base_version,
    user_id,
    school,major
    ,level
    ,certification_no
    ,registration_no,expire_date,degree,technical_titles,account,credit
    ,country_code
    )
    values
    (
    #{loginName, jdbcType=VARCHAR},
    #{password, jdbcType=VARCHAR},
    #{organSign, jdbcType=VARCHAR},
    #{organSignType, jdbcType=TINYINT},
    #{name, jdbcType=VARCHAR},
    #{phone, jdbcType=VARCHAR},
    #{sex, jdbcType=TINYINT},
    #{national, jdbcType=VARCHAR},
    #{idCard, jdbcType=VARCHAR},
    #{workingState, jdbcType=TINYINT},
    #{joiningTime, jdbcType=TIMESTAMP},
    #{resignationTime, jdbcType=TIMESTAMP},
    #{identity, jdbcType=TINYINT},
    (select IFNULL(employee.version, 0) from (select max(base_version) as version from saas_employee where organ_sign= #{organSign, jdbcType=VARCHAR}) as employee) + 1,
    (select IFNULL(employee.version, 0) from (select max(base_version) as version from saas_employee where organ_sign= #{organSign, jdbcType=VARCHAR}) as employee) + 1,
    #{school, jdbcType=VARCHAR}, #{major, jdbcType=VARCHAR}, #{level, jdbcType=TINYINT}, #{certificationNo, jdbcType=VARCHAR}, #{registrationNo, jdbcType=VARCHAR},
     #{expireDate, jdbcType=VARCHAR}, #{degree, jdbcType=TINYINT}, #{technicalTitles, jdbcType=TINYINT}, #{account, jdbcType=VARCHAR},#{credit, jdbcType=VARCHAR},
     #{countryCode, jdbcType=VARCHAR}
     )
  </insert>

  <select id="selectEmployeeById" resultType="com.xyy.user.provider.module.entity.SaasEmployee" parameterType="java.lang.Integer" >
    select * from saas_employee
    where id = #{employeeId, jdbcType=INTEGER} and working_state = 1 
  </select>

  <select id="selectEmployeeByIds" resultType="com.xyy.user.provider.module.entity.SaasEmployee"  >
    select * from saas_employee
    where id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id,jdbcType=INTEGER}
    </foreach>
  </select>

  <select id="selectEmployeeByIdWithOutState" resultType="com.xyy.user.provider.module.entity.SaasEmployee" parameterType="java.lang.Integer" >
    select * from saas_employee
    where id = #{employeeId, jdbcType=INTEGER}
  </select>


  <select id="selectEmployeeByIdWithOutStateList" resultType="com.xyy.user.provider.module.entity.SaasEmployee" parameterType="java.lang.Integer" >
    select * from saas_employee
    where id in
    <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
  
  <select id="getEmployeefileById" resultType="com.xyy.user.module.dto.result.EmployeeDto" parameterType="com.xyy.user.module.dto.EmployeefileRequestModel" >
    select * from saas_employee
    where id = #{employeeId, jdbcType=INTEGER} 
    <if test="isDisabled != null">
        AND e.is_disabled = #{isDisabled, jdbcType=TINYINT} 
      </if>
      <if test="workingState != null">
        AND e.working_state = #{workingState, jdbcType=TINYINT} 
      </if>
  </select>
  
  <select id="getEmployeeDetail" resultType="com.xyy.user.provider.module.entity.SaasEmployee"  >
    select login_name loginName,organ_sign organSign,organ_sign_type,name,sex,national,id_card idCard,working_state workingState,joining_time joiningTime,resignation_time resignationTime,identity,credit
    from saas_employee
    where id = #{employeeId, jdbcType=INTEGER} 
    <if test="workingState != null">
        AND working_state = #{workingState, jdbcType=TINYINT}
      </if>
  </select>
  
  <select id="getEmployeeDetailByIds" resultType="com.xyy.user.provider.module.entity.SaasEmployee"  >
    select id,phone,login_name loginName,organ_sign organSign,organ_sign_type,name,sex,national,id_card idCard,working_state workingState,joining_time joiningTime,resignation_time resignationTime,identity,
    technical_titles technicalTitles,credit from saas_employee
    where organ_sign = #{organSign, jdbcType=VARCHAR}
    <if test="workingState != null">
        AND working_state = #{workingState, jdbcType=TINYINT}
      </if>
      <if test="disabled != null">
        AND is_disabled = #{disabled, jdbcType=TINYINT} 
      </if>
       and id in
        <foreach collection="employeeIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
  </select>

  <select id="getEmployeeDetailByIdsV2" resultType="com.xyy.user.provider.module.entity.SaasEmployee"  >
    select id,phone,login_name loginName,organ_sign organSign,organ_sign_type,name,sex,national,id_card idCard,working_state workingState,joining_time joiningTime,resignation_time resignationTime,identity,
    technical_titles technicalTitles,credit from saas_employee
    <where>
      id in
      <foreach collection="employeeIds" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    <if test="workingState != null">
        AND working_state = #{workingState, jdbcType=TINYINT}
      </if>
      <if test="disabled != null">
        AND is_disabled = #{disabled, jdbcType=TINYINT}
      </if>
    </where>
  </select>

  <select id="selectAllEmployeeByOrganSign" resultType="com.xyy.user.provider.module.entity.SaasEmployee" parameterType="java.lang.String" >
    select * from saas_employee
    where organ_sign = #{organSign, jdbcType=VARCHAR} and working_state = 1 and is_disabled = 0 
  </select>

  <select id="selectEmployeeByLoginNameAndOrganSign" resultType="com.xyy.user.provider.module.entity.SaasEmployee">
    select * from saas_employee
    where (login_name = #{loginName, jdbcType=VARCHAR} or  account = #{loginName, jdbcType=VARCHAR}) and organ_sign = #{organSign, jdbcType=VARCHAR} and working_state = 1
  </select>

  <select id="selectEmployeeByLoginNameAndPassword" resultType="com.xyy.user.provider.module.entity.SaasEmployee">
    select * from saas_employee
    where login_name = #{loginName, jdbcType=VARCHAR} and password = #{password, jdbcType=VARCHAR} and working_state = 1
  </select>

  <select id="selectEmployeesByLoginName" resultType="com.xyy.user.provider.module.entity.SaasEmployee">
    select * from saas_employee
    where (login_name = #{loginName, jdbcType=VARCHAR} or account = #{loginName, jdbcType=VARCHAR}) and working_state = 1
  </select>


  <select id="selectEmployeeByIdAndOrganSign" resultType="com.xyy.user.provider.module.entity.SaasEmployee">
    select * from saas_employee where id = #{employeeId, jdbcType=INTEGER} and organ_sign = #{organSign, jdbcType=VARCHAR}
  </select>

  <select id="selectEmployeeByOrganSign" parameterType="java.lang.String" resultType="com.xyy.user.module.dto.result.QueryEmployeeVO">
    select e.id as employeeId, e.phone as phone, e.login_name as username, e.name as name, r.name as roleName, e.identity,e.is_disabled as isDisabled,e.organ_sign_type as organSignType 
    from saas_employee e
    left join saas_employee_role er on e.id = er.employee_id and er.yn = 0
    left join saas_role r on r.id = er.role_id and r.yn = 0
    <where>
      1 = 1
      <if test="name != null and name!=''">
        AND e.name LIKE CONCAT('%',#{name, jdbcType=VARCHAR},'%')
      </if>
      <if test="phone != null and phone!=''">
        AND e.phone LIKE CONCAT('%',#{phone, jdbcType=VARCHAR},'%')
      </if>
      <if test="roleName != null  and roleName!=''">
        AND r.name LIKE CONCAT('%',#{roleName, jdbcType=VARCHAR},'%')
      </if>
      and e.organ_sign = #{organSign, jdbcType=VARCHAR} and e.working_state = 1
    </where>
  </select>
  
  <select id="queryEmployeeByCondition"  resultType="com.xyy.user.module.dto.result.QueryEmployeeVO">
    select e.id as employeeId, e.phone as phone, e.login_name as username, e.name as name, r.name as roleName, e.identity,e.is_disabled as isDisabled, e.organ_sign_type as organSignType,er.role_id as roleId
    from saas_employee e
    left join saas_employee_role er on e.id = er.employee_id and er.yn = 0
    left join saas_role r on r.id = er.role_id and r.yn = 0
    <where>
      1 = 1
      <if test="name != null and name!=''">
        AND e.name LIKE CONCAT('%',#{name, jdbcType=VARCHAR},'%')
      </if>
      <if test="phone != null and phone!=''">
        AND e.phone LIKE CONCAT('%',#{phone, jdbcType=VARCHAR},'%')
      </if>
      <if test="loginName != null and loginName!=''">
        AND e.login_name LIKE CONCAT('%',#{loginName, jdbcType=VARCHAR},'%')
      </if>
      <if test="roleId != null and roleId!=''">
        AND er.role_id = #{roleId, jdbcType=INTEGER}
      </if>
      <if test="roleName != null  and roleName!=''">
        AND r.name LIKE CONCAT('%',#{roleName, jdbcType=VARCHAR},'%')
      </if>
      <if test="isDisabled != null">
        AND e.is_disabled = #{isDisabled, jdbcType=TINYINT} 
      </if>
      <if test="workingState != null ">
        AND e.working_state = #{workingState, jdbcType=TINYINT} 
      </if>
      <if test="exclusiveIds != null and exclusiveIds.size>0">
        and e.id not in
        <foreach collection="exclusiveIds" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      and e.organ_sign = #{organSign, jdbcType=VARCHAR}
    </where>
  </select>

  <select id="queryHeadquartersEmployeeByCondition"  resultType="com.xyy.user.module.dto.result.QueryEmployeeVO">
    select e.id as employeeId, e.phone as phone, e.login_name as username, e.name as name, r.name as roleName, r.id as roleId, e.identity,
    e.organ_sign as organSign, e.is_disabled as isDisabled, e.organ_sign_type as organSignType, e.user_id as userId, sex
    from saas_employee e
    left join saas_employee_role er on e.id = er.employee_id and er.yn = 0
    left join saas_role r on r.id = er.role_id and r.yn = 0
    left join saas_drugstore s on e.organ_sign = s.organ_sign
    <where>
      1 = 1
<!--       and (d.headquarters_organ_sign = #{headquartersOrganSign} or s.headquarters_organ_sign = #{headquartersOrganSign}) -->
      <if test="name != null and name!=''">
        AND e.name LIKE CONCAT('%',#{name, jdbcType=VARCHAR},'%')
      </if>
      <if test="phone != null and phone!=''">
        AND e.phone LIKE CONCAT('%',#{phone, jdbcType=VARCHAR},'%')
      </if>
      <if test="loginName != null and loginName!=''">
        AND e.login_name LIKE CONCAT('%',#{loginName, jdbcType=VARCHAR},'%')
      </if>
      <if test="organSign != null and organSign !=''">
        AND e.organ_sign = #{organSign, jdbcType=VARCHAR}
      </if>
      <if test="isDisabled != null">
        AND e.is_disabled = #{isDisabled, jdbcType=TINYINT}
      </if>
      <if test="workingState != null ">
        AND e.working_state = #{workingState, jdbcType=TINYINT}
      </if>
      <if test="organSigns != null and organSigns.size() > 0">
        and e.organ_sign in
        <foreach collection="organSigns" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

  <select id="getEmployeefileByCondition" parameterType="com.xyy.user.module.dto.EmployeefileRequestModel" resultMap="employeefileMap">
    select e.id , e.phone , e.login_name ,e.organ_sign, e.name , e.sex,e.id_card,e.national,e.joining_time,e.identity,e.school,
    e.major,e.level,e.certification_no,e.registration_no,e.expire_date,e.degree,e.technical_titles,e.is_disabled,e.organ_sign_type,e.credit
    from saas_employee e 
    <where>
      1 = 1
      <if test="employeeName != null and employeeName != ''">
        AND e.name LIKE CONCAT( #{employeeName, jdbcType=VARCHAR},'%')
      </if>
      <if test="phone != null and phone != ''">
        AND e.phone LIKE CONCAT( #{phone, jdbcType=VARCHAR},'%')
      </if>
      <if test="isDisabled != null">
        AND e.is_disabled = #{isDisabled, jdbcType=TINYINT} 
      </if>
      <if test="workingState != null ">
        AND e.working_state = #{workingState, jdbcType=TINYINT} 
      </if>
      and e.organ_sign = #{organSign, jdbcType=VARCHAR} 
    </where>
  </select>


  <select id="getEmployeeFileByConditionList" parameterType="com.xyy.user.module.dto.EmployeefileRequestModel" resultMap="employeefileMap">
    select e.id , e.phone , e.login_name ,e.organ_sign, e.name , e.sex,e.id_card,e.national,e.joining_time,e.identity,e.school,
    e.major,e.level,e.certification_no,e.registration_no,e.expire_date,e.degree,e.technical_titles,e.is_disabled,e.organ_sign_type,e.credit
    from saas_employee e
    <where>
      1 = 1
      <if test="employeeName != null and employeeName != ''">
        AND e.name LIKE CONCAT( #{employeeName, jdbcType=VARCHAR},'%')
      </if>
      <if test="phone != null and phone != ''">
        AND e.phone LIKE CONCAT( #{phone, jdbcType=VARCHAR},'%')
      </if>
      <if test="isDisabled != null">
        AND e.is_disabled = #{isDisabled, jdbcType=TINYINT}
      </if>
      <if test="organSign != null and organSign != ''">
        AND e.organ_sign = #{organSign, jdbcType=VARCHAR}
      </if>
      <if test="workingState != null ">
        AND e.working_state = #{workingState, jdbcType=TINYINT}
      </if>
      <if test="organSigns != null and organSigns.size() > 0">
        and e.organ_sign in
        <foreach collection="organSigns" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
    </where>
  </select>
  
  <select id="getEmployeeIdsByCondition" parameterType="com.xyy.user.module.dto.EmployeeListRequestModel" resultType="java.lang.Integer">
      select DISTINCT e.id  id
    from saas_employee e
    join saas_employee_role er on e.id = er.employee_id and er.yn = 0
    <where>
      1 = 1
      <if test="name != null and name!=''">
        AND e.name LIKE CONCAT('%',#{name, jdbcType=VARCHAR},'%')
      </if>
      <if test="phone != null and phone!=''">
        AND e.phone LIKE CONCAT('%',#{phone, jdbcType=VARCHAR},'%')
      </if>
      <if test="roleId != null and roleId!=''">
        AND er.role_id = #{roleId, jdbcType=INTEGER}
      </if>
      <if test="roleIdList!=null and roleIdList.size() > 0 ">
        and er.role_id in
        <foreach collection="roleIdList" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="isDisabled != null">
        AND e.is_disabled = #{isDisabled, jdbcType=TINYINT} 
      </if>
      <if test="workingState != null ">
        AND e.working_state = #{workingState, jdbcType=TINYINT} 
      </if>
      and e.organ_sign = #{organSign, jdbcType=VARCHAR} 
    </where>
  </select>

  <update id="updateEmployeeUnbind">
    UPDATE saas_employee
    <set>
      working_state = 2,
      is_disabled = 1,
      resignation_time = NOW(),
      base_version = (select IFNULL(employee.version, 0) from (select max(base_version) as version from saas_employee where organ_sign= #{organSign, jdbcType=VARCHAR}) as employee) + 1
    </set>
    WHERE id = #{employeeId, jdbcType=INTEGER} and working_state = 1
  </update>
  
  <update id="updateEmployeeBind">
    UPDATE saas_employee
    <set>
      working_state = 1,
      resignation_time = NOW(),
      base_version = (select IFNULL(employee.version, 0) from (select max(base_version) as version from saas_employee where organ_sign= #{organSign, jdbcType=VARCHAR}) as employee) + 1
    </set>
    WHERE id = #{employeeId, jdbcType=INTEGER} and working_state = 2
  </update>
  
   <update id="updateEmployeeStatus">
    UPDATE saas_employee
    <set>
      is_disabled = #{isDisabled, jdbcType=TINYINT},
      base_version = (select IFNULL(employee.version, 0) from (select max(base_version) as version from saas_employee where organ_sign= #{organSign, jdbcType=VARCHAR}) as employee) + 1
    </set>
    WHERE id = #{employeeId, jdbcType=INTEGER} 
  </update>

  <update id="updateEmployeeInfo">
    update saas_employee
    set
    name = #{employee.name, jdbcType=VARCHAR},
    phone = #{employee.phone, jdbcType=VARCHAR},
    sex = #{employee.sex, jdbcType=TINYINT},
    national = #{employee.national, jdbcType=VARCHAR},
    id_card = #{employee.idCard, jdbcType=VARCHAR},
    joining_time = #{employee.joiningTime, jdbcType=TIMESTAMP},
    identity = #{employee.identity, jdbcType=TINYINT},
    base_version = (select IFNULL(employee.version, 0) from (select max(base_version) as version from saas_employee where organ_sign= #{organSign, jdbcType=VARCHAR}) as employee) + 1,
    school = #{employee.school, jdbcType=VARCHAR},
     major = #{employee.major, jdbcType=VARCHAR},
     level =  #{employee.level, jdbcType=TINYINT}, 
     certification_no =  #{employee.certificationNo, jdbcType=VARCHAR},
      registration_no =  #{employee.registrationNo, jdbcType=VARCHAR},
     expire_date =#{employee.expireDate, jdbcType=VARCHAR},
     <if test="organSign != null and organSign != ''">
       organ_sign = #{organSign, jdbcType=VARCHAR},
     </if>
     degree = #{employee.degree, jdbcType=TINYINT},
     technical_titles =  #{employee.technicalTitles, jdbcType=TINYINT},
     credit =  #{employee.credit, jdbcType=VARCHAR},
     country_code = #{employee.countryCode, jdbcType=VARCHAR}
    where id = #{employee.employeeId, jdbcType=INTEGER}
  </update>

  <select id="selectEmployeeByRoleId" resultType="com.xyy.user.module.dto.result.QueryEmployeeVO">
    select e.id as employeeId, e.phone as phone, e.login_name as username, e.name as name, r.name as roleName, e.identity,e.organ_sign_type 
    from saas_employee e
    inner join saas_employee_role er on e.id = er.employee_id and er.yn = 0
    inner join saas_role r on r.id = er.role_id and r.yn = 0
    <where>
      1 = 1
      <if test="roleId != null">
        AND r.id = #{roleId, jdbcType=INTEGER}
      </if>
      and e.working_state = 1 and e.is_disabled =0 and e.organ_sign = #{organSign, jdbcType=VARCHAR}
    </where>
  </select>

  <select id="selectByOrganSign" resultType="com.xyy.user.module.dto.result.QueryEmployeeVO">
    select e.id as employeeId, e.phone as phone, e.login_name as username, e.name as name, r.name as roleName, e.identity,e.organ_sign_type
    from saas_employee e
    inner join saas_employee_role er on e.id = er.employee_id and er.yn = 0
    inner join saas_role r on r.id = er.role_id and r.yn = 0
    where e.working_state = 1 and e.is_disabled =0 and e.organ_sign = #{organSign, jdbcType=VARCHAR}
  </select>

  <select id="selectEmployeeByNameAndOrganSign" resultType="com.xyy.user.provider.module.entity.SaasEmployee">
    select * from saas_employee
    <where>
      1 = 1
      <if test="name != null">
        AND name LIKE CONCAT('%',#{name, jdbcType=VARCHAR},'%')
      </if>
      and organ_sign = #{organSign, jdbcType=VARCHAR} and working_state = 1 
    </where>
  </select>

  <select id="selectEmployeeByAccurateNameAndOrganSign" resultType="com.xyy.user.provider.module.entity.SaasEmployee">
    select * from saas_employee
    <where>
      1 = 1
      <if test="name != null">
        AND name = #{name, jdbcType=VARCHAR}
      </if>
      and organ_sign = #{organSign, jdbcType=VARCHAR} and working_state = 1 and is_disabled =0 
    </where>
  </select>

  <select id="selectEmployeeByExcludeNameAndOrganSign" resultType="com.xyy.user.provider.module.entity.SaasEmployee">
    select * from saas_employee
    <where>
      1 = 1
      <if test="name != null">
        AND name != #{name, jdbcType=VARCHAR}
      </if>
      and organ_sign = #{organSign, jdbcType=VARCHAR} and working_state = 1 and is_disabled =0 
    </where>
  </select>

  <select id="selectEmployeeSyncByBaseVersion" resultType="com.xyy.user.provider.module.entity.SaasEmployee">
    /*FORCE_MASTER*/
    select * from saas_employee
    where organ_sign = #{organSign, jdbcType=VARCHAR} and base_version > #{baseVersion, jdbcType=INTEGER}
    order by base_version
    <if test="counts != null">
      limit 0, #{counts, jdbcType=INTEGER}
    </if>
  </select>

  <select id="selectYkqEmployeeByOrganSign" parameterType="java.lang.String" resultType="com.xyy.user.module.dto.result.YkqQueryEmployeeVO">
    select e.phone as phone, e.login_name as username, e.name as name, r.name as roleName,e.organ_sign_type 
    from saas_employee e
    left join saas_employee_role er on e.id = er.employee_id and er.yn = 0
    left join saas_role r on r.id = er.role_id and r.yn = 0
    where e.organ_sign = #{organSign, jdbcType=VARCHAR} and e.working_state = 1 and e.is_disabled =0 
    group by e.phone
  </select>

    <select id="getEmployeeListByNameAndRoleId" resultType="com.xyy.user.module.dto.result.EmployeeDto">
      SELECT
	    *
      FROM
	    saas_employee
      WHERE 1=1
      <if test="organSign != null">
        and organ_sign = #{organSign}
      </if>
      <if test="name != null and type != null and type == 1">
        and name = #{name}
      </if>
      <if test="name != null and type != null and type == 2">
        and name like CONCAT("%", #{name},"%")
      </if>
      <if test="name != null and type != null and type == 3">
        and name != #{name}
      </if>
    </select>
    <select id="selectIsDisabledEmployeeByNameAndOrganSign"
            resultType="com.xyy.user.provider.module.entity.SaasEmployee">
      select * from saas_employee
      <where>
        1 = 1 and is_disabled=0
        <if test="name != null">
          AND name LIKE CONCAT('%',#{name, jdbcType=VARCHAR},'%')
        </if>
        and organ_sign = #{organSign, jdbcType=VARCHAR} and working_state = 1
      </where>

    </select>

    <update id="updateEmployeePassword">
    update saas_employee
    set
    password = #{newPassword, jdbcType=VARCHAR},
    update_pwd_time = now(),
    base_version = (select IFNULL(employee.version, 0) from (select max(base_version) as version from saas_employee where organ_sign= #{organSign, jdbcType=VARCHAR}) as employee) + 1
    where id = #{employeeId, jdbcType=INTEGER}
  </update>


  <update id="updateEmployeePhone">
    update saas_employee
    set
      phone = #{newPhone, jdbcType=VARCHAR},
      base_version = (select IFNULL(employee.version, 0) from (select max(base_version) as version from saas_employee where organ_sign= #{organSign, jdbcType=VARCHAR}) as employee) + 1
    where id = #{employeeId, jdbcType=INTEGER}
  </update>

  <update id="updateEmployeeFaceRegister">
    UPDATE saas_employee
    <set>
      face_register = #{faceRegister, jdbcType=INTEGER}
    </set>
    WHERE id = #{employeeId, jdbcType=INTEGER}
  </update>
  
  <update id="updateEmployeeLoginNameById" parameterType="com.xyy.user.provider.module.entity.SaasEmployee">
    update saas_employee 
    set 
    login_name = #{loginName, jdbcType=VARCHAR} 
    where id = #{id, jdbcType=INTEGER}
  </update>

  <update id="updateEmployeeAccountById"  parameterType="com.xyy.user.provider.module.entity.SaasEmployee">
    UPDATE saas_employee
    <set>
      account = #{account, jdbcType=VARCHAR},
      base_version = (select IFNULL(employee.version, 0) from (select max(base_version) as version from saas_employee where organ_sign= #{organSign, jdbcType=VARCHAR}) as employee) + 1
    </set>
    WHERE id = #{id, jdbcType=INTEGER}
  </update>
    <select id="getEmployeeListByOrganSigns" resultType="com.xyy.user.module.dto.restructure.SaaSEmployeeDto">
    select * from saas_employee
    where
    organ_sign in
    <foreach collection="organSigns" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
    <if test="employeeName != null and employeeNameLike == true">
      AND `name` LIKE CONCAT('%',#{employeeName, jdbcType=VARCHAR},'%')
    </if>
    <if test="employeeName != null and employeeNameLike == false">
      AND `name` = #{employeeName, jdbcType=VARCHAR}
    </if>
  </select>

  <select id="getEmployeeListByRoleIdAndOrganSignList" resultMap="BaseResultMap">
    select e.id,e.login_name, er.organ_sign,e.name
    from saas_employee e inner join saas_employee_role er on e.id = er.employee_id and er.yn = 0
    where er.role_id = #{roleId, jdbcType=INTEGER}  and e.working_state = 1 and e.is_disabled = 0
    <if test="organSignList != null and organSignList.size() > 0">
      and e.organ_sign in
      <foreach collection="organSignList" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    order by er.binding_time asc
</select>

  <select id="getEmployeeListByRoleCodeAndOrganSignList" resultMap="BaseResultMap">
    select e.id,e.login_name, er.organ_sign,e.name
    from saas_employee e inner join saas_employee_role er on e.id = er.employee_id and er.yn = 0
    where er.role_id = #{id, jdbcType=INTEGER}  and e.working_state = 1 and e.is_disabled = 0
    <if test="organSignList != null and organSignList.size() > 0">
      and e.organ_sign in
      <foreach collection="organSignList" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    order by er.binding_time asc
  </select>

  <select id="queryEmployeeListByCondition"  resultType="com.xyy.user.module.dto.result.QueryEmployeeVO">
    select e.id as employeeId,  e.name as name, e.identity,e.is_disabled as isDisabled, e.organ_sign_type as organSignType
    from saas_employee e
    <where>
      1 = 1
      <if test="isDisabled != null">
        AND e.is_disabled = #{isDisabled, jdbcType=TINYINT}
      </if>
      <if test="workingState != null ">
        AND e.working_state = #{workingState, jdbcType=TINYINT}
      </if>
      <if test="exclusiveIds != null and exclusiveIds.size>0">
        and e.id not in
        <foreach collection="exclusiveIds" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      and e.organ_sign = #{organSign, jdbcType=VARCHAR}
    </where>
  </select>
  <select id="selectEmployeeByModel" resultType="com.xyy.user.provider.module.entity.SaasEmployee">
    select * from saas_employee
    where (login_name = #{loginName, jdbcType=VARCHAR} or  account = #{loginName, jdbcType=VARCHAR})
    and organ_sign in
    <foreach collection="organSigns" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
     and working_state = 1
  </select>
  <select id="getEmployeeByRoleIds" resultType="com.xyy.user.provider.module.entity.SaasEmployee">
     select e.* from saas_employee e
     inner join saas_employee_role er on e.id = er.employee_id
     <where>
     <if test="organSigns != null and organSigns.size > 0">
      and er.organ_sign in
       <foreach collection="organSigns" item="item" open="(" close=")" separator=",">
         #{item}
       </foreach>
     </if>
     <if test="roleIds != null and roleIds.size > 0">
       and er.role_id in
       <foreach collection="roleIds" item="item" open="(" close=")" separator=",">
         #{item}
       </foreach>
     </if>
     <if test="organSign != null and organSign != ''">
       and e.organ_sign = #{organSign, jdbcType=VARCHAR}
     </if>
     <if test="employeeId != null">
       and er.employee_id = #{employeeId, jdbcType=INTEGER}
     </if>
     and er.yn = 0 and e.working_state = 1
     </where>
  </select>

  <update id="batchUpdateEmployee" parameterType="java.util.List">
    <foreach collection="list" item="employee" index="index" open="" close="" separator=";">
      update saas_employee
      <set>
        <if test="employee.name != null">
        name = #{employee.name, jdbcType=VARCHAR},
        </if>
        <if test="employee.loginName != null">
          login_name = #{employee.loginName, jdbcType=VARCHAR},
        </if>
        <if test="employee.phone != null">
          phone = #{employee.phone, jdbcType=VARCHAR},
        </if>
        <if test="employee.sex != null">
          sex = #{employee.sex, jdbcType=TINYINT},
        </if>
        <if test="employee.national!= null">
          national = #{employee.national, jdbcType=VARCHAR},
        </if>
        <if test="employee.idCard != null">
          id_card = #{employee.idCard, jdbcType=VARCHAR},
        </if>
        <if test="employee.joiningTime != null">
          joining_time = #{employee.joiningTime, jdbcType=TIMESTAMP},
        </if>
        <if test="employee.identity != null">
          identity = #{employee.identity, jdbcType=TINYINT},
        </if>
        <if test="employee.organSign != null">
          base_version = (select IFNULL(employee.version, 0) from (select max(base_version) as version from saas_employee where organ_sign= #{employee.organSign, jdbcType=VARCHAR}) as employee) + 1,
        </if>
        <if test="employee.school != null">
          school = #{employee.school, jdbcType=VARCHAR},
        </if>
        <if test="employee.major != null">
          major = #{employee.major, jdbcType=VARCHAR},
        </if>
        <if test="employee.level != null">
          level =  #{employee.level, jdbcType=TINYINT},
        </if>
        <if test="employee.certificationNo != null">
          certification_no =  #{employee.certificationNo, jdbcType=VARCHAR},
        </if>
        <if test="employee.registrationNo != null">
          registration_no =  #{employee.registrationNo, jdbcType=VARCHAR},
        </if>
        <if test="employee.expireDate != null">
          expire_date =#{employee.expireDate, jdbcType=VARCHAR},
        </if>
        <if test="employee.degree != null">
          degree = #{employee.degree, jdbcType=TINYINT},
        </if>
        <if test="employee.technicalTitles != null">
          technical_titles =  #{employee.technicalTitles, jdbcType=TINYINT}
        </if>

      </set>
      where id = #{employee.id,jdbcType=INTEGER}
    </foreach>
  </update>

  <select id="selectEmployeeIdByPhoneAndOrganSign" parameterType="string" resultType="integer">
    select id from saas_employee where phone = #{phone} and organ_sign = #{organSign}
  </select>
  
  <select id="getIdByOrganization"  parameterType="string" resultType="integer">
    select employee_id from saas_employee_role where organ_sign = #{organSign} and role_id in
    <foreach collection="roleIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

  <update id="refreshBindId" parameterType="java.lang.Integer">
    update saas_employee set user_bind_id = #{userId} where id = ${employeeId}
  </update>

    <sql id="JOIN_Base_Column_List">
        e.id, e.root_organ_sign, e.source_type, e.login_name, e.password, oe.organ_sign, e.organ_sign_type,
    e.name, e.phone, e.sex, e.national, e.id_card, e.working_state, e.joining_time, e.resignation_time,
    e.identity, e.base_version, e.user_id, e.school, e.major, e.level, e.certification_no, e.registration_no,
    e.expire_date, e.degree, e.technical_titles, e.is_disabled, e.face_register, e.account, e.credit ,e.country_code,e.user_bind_id,e.employee_job_number
    </sql>

    <select id="selectOne" resultMap="BaseResultMap">
        select
        <include refid="JOIN_Base_Column_List" />
        from saas_employee e
        inner join saas_organization_employee oe on e.id = oe.employee_id and oe.yn = 0
        <where>
            1 = 1
            <if test="id != null ">
                AND e.id = #{id,jdbcType=INTEGER}
            </if>
            <if test="loginName != null and loginName!=''">
                AND e.login_name = #{loginName, jdbcType=VARCHAR}
            </if>
            <if test="phone != null and phone!=''">
                AND e.phone = #{phone, jdbcType=VARCHAR}
            </if>
            <if test="currentOrganSign != null and currentOrganSign !=''">
                AND oe.organ_sign = #{currentOrganSign, jdbcType=VARCHAR}
            </if>
            <if test="rootOrganSign != null and rootOrganSign!=''">
                AND e.root_organ_sign = #{rootOrganSign, jdbcType=VARCHAR}
            </if>
            <if test="isDisabled != null">
                AND e.is_disabled = #{isDisabled, jdbcType=TINYINT}
            </if>
            <if test="workingState != null ">
                AND e.working_state = #{workingState, jdbcType=TINYINT}
            </if>
        </where>
        union
        select
        <include refid="JOIN_Base_Column_List" />
        from saas_employee e
        inner join saas_organization_employee oe on e.id = oe.employee_id and oe.yn = 0
        <where>
            1 = 1
            <if test="id != null ">
                AND e.id = #{id,jdbcType=INTEGER}
            </if>
            <if test="loginName != null and loginName!=''">
                AND e.account = #{loginName, jdbcType=VARCHAR}
            </if>
            <if test="currentOrganSign != null and currentOrganSign !=''">
                AND oe.organ_sign = #{currentOrganSign, jdbcType=VARCHAR}
            </if>
            <if test="rootOrganSign != null and rootOrganSign!=''">
                AND e.root_organ_sign = #{rootOrganSign, jdbcType=VARCHAR}
            </if>
            <if test="isDisabled != null">
                AND e.is_disabled = #{isDisabled, jdbcType=TINYINT}
            </if>
            <if test="workingState != null ">
                AND e.working_state = #{workingState, jdbcType=TINYINT}
            </if>
        </where>
    </select>

</mapper>
