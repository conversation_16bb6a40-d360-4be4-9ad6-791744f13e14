<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.user.provider.module.dao.SaasMenuGroupMapper">
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasMenuGroupPO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="pref" jdbcType="VARCHAR" property="pref" />
    <result column="menu_group_name" jdbcType="VARCHAR" property="menuGroupName" />
    <result column="biz_model" jdbcType="TINYINT" property="bizModel" />
    <result column="organ_sign_type" jdbcType="TINYINT" property="organSignType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="yn" jdbcType="TINYINT" property="yn" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
        id, pref, menu_group_name, biz_model, organ_sign_type, remark, yn,
    create_user, create_time, update_user, update_time
  </sql>

  <select id="selectMenuGroupList" parameterType="com.xyy.user.provider.module.entity.SaasMenuGroupPO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from saas_menu_group
    <where>
      <if test="yn != null">
        yn = #{yn}
      </if>
    </where>
  </select>
</mapper>