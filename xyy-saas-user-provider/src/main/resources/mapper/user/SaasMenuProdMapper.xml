<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.user.provider.module.dao.SaasMenuProdMapper">
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasMenuProdPO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="menu_group_pref" jdbcType="VARCHAR" property="menuGroupPref" />
    <result column="permission_id" jdbcType="INTEGER" property="permissionId" />
    <result column="yn" jdbcType="TINYINT" property="yn" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, menu_group_pref, permission_id, yn, create_user, create_time, update_user, update_time
  </sql>

  <select id="selectProdMenuList" parameterType="com.xyy.user.provider.module.entity.SaasMenuProdPO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from saas_menu_prod
    <where>
      <if test="menuGroupPref != null">
        menu_group_pref = #{menuGroupPref}
      </if>
      <if test="yn != null">
        and yn = #{yn}
      </if>
    </where>
  </select>

  <insert id="batchInsert" parameterType="java.util.List">
    <foreach collection="list" item="record" separator=";">
      insert into saas_menu_prod (menu_group_pref, permission_id, yn, create_user, create_time, update_user, update_time)
      values (#{record.menuGroupPref,jdbcType=VARCHAR}, #{record.permissionId,jdbcType=INTEGER}, #{record.yn,jdbcType=TINYINT},
      #{record.createUser,jdbcType=VARCHAR}, #{record.createTime,jdbcType=TIMESTAMP}, #{record.updateUser,jdbcType=VARCHAR},
      #{record.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <update id="batchUpdate" parameterType="java.util.List">
    <foreach collection="list" item="record" separator=";">
      update saas_menu_prod
      <set>
        <if test="record.yn != null">
          yn = #{record.yn,jdbcType=TINYINT},
        </if>
        <if test="record.createUser != null">
          create_user = #{record.createUser,jdbcType=VARCHAR},
        </if>
        <if test="record.createTime != null">
          create_time = #{record.createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="record.updateUser != null">
          update_user = #{record.updateUser,jdbcType=VARCHAR},
        </if>
        <if test="record.updateTime != null">
          update_time = #{record.updateTime,jdbcType=TIMESTAMP},
        </if>
      </set>
      where menu_group_pref = #{record.menuGroupPref}
      and permission_id = #{record.permissionId}
    </foreach>
  </update>

</mapper>