<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.user.provider.module.dao.SaasDrugstoreMapper" >
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasDrugstore" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="manager_user_id" property="managerUserId" jdbcType="INTEGER" />
    <result column="drugstore_name" property="drugstoreName" jdbcType="VARCHAR" />
    <result column="manager_name" property="managerName" jdbcType="VARCHAR" />
    <result column="contact_phone" property="contactPhone" jdbcType="VARCHAR" />
    <result column="organ_sign" property="organSign" jdbcType="VARCHAR" />
    <result column="root_organ_sign" property="rootOrganSign" jdbcType="VARCHAR" />
    <result column="province" property="province" jdbcType="VARCHAR" />
    <result column="city" property="city" jdbcType="VARCHAR" />
    <result column="area" property="area" jdbcType="VARCHAR" />
    <result column="area_code" property="areaCode" jdbcType="VARCHAR" />
    <result column="address" property="address" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="TINYINT" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="audit_record_id" property="auditRecordId" jdbcType="INTEGER" />
    <result column="business_license_name" property="businessLicenseName" jdbcType="VARCHAR" />
    <result column="business_license_number" property="businessLicenseNumber" jdbcType="VARCHAR" />
    <result column="business_license_img" property="businessLicenseImg" jdbcType="VARCHAR" />
    <result column="pharmaceutical_trading_license_img" property="pharmaceuticalTradingLicenseImg" jdbcType="VARCHAR" />
    <result column="quality_management_license_img" property="qualityManagementLicenseImg" jdbcType="VARCHAR" />
    <result column="organ_sign_type" property="organSignType" jdbcType="TINYINT" />
    <result column="biz_model" property="bizModel" jdbcType="TINYINT" />
    <result column="headquarters_organ_sign" property="headquartersOrganSign" jdbcType="VARCHAR" />
    <result column="sender_id" property="senderId" jdbcType="VARCHAR" />
    <result column="secret_key" property="secretKey" jdbcType="VARCHAR" />
    <result column="storehouse_yn" property="storehouseYn" jdbcType="TINYINT" />
    <result column="account_set_name" property="accountSetName" jdbcType="VARCHAR" />
    <result column="main_yn" property="mainYn" jdbcType="TINYINT" />
    <result column="main_organ_sign" property="mainOrganSign" jdbcType="VARCHAR" />
    <result column="medical_insurance_status" property="medicalInsuranceStatus" jdbcType="TINYINT" />
    <result column="medical_merchant_no" property="medicalMerchantNo" jdbcType="VARCHAR" />
    <result column="sender_account" property="senderAccount" jdbcType="VARCHAR" />
    <result column="sender_ext" property="senderExt" jdbcType="VARCHAR" />
    <result column="device_supervision_code" property="deviceSupervisionCode" jdbcType="VARCHAR" />
    <result column="device_supervision_key" property="deviceSupervisionKey" jdbcType="VARCHAR" />
    <result column="app_id" property="appId" jdbcType="VARCHAR" />
    <result column="app_secret" property="appSecret" jdbcType="VARCHAR" />
  </resultMap>

  <resultMap id="drugstoreVoMap" type="com.xyy.user.provider.module.vo.SaasDrugstoreVO" extends="BaseResultMap">
      <result column="contact_phone" property="contactPhone" jdbcType="VARCHAR" />
    <result column="phone" property="phone" jdbcType="VARCHAR" />
    <result column="audit_type" property="auditType" jdbcType="TINYINT" />
    <result column="audit_status" property="auditStatus" jdbcType="TINYINT" />
    <result column="audit_user" property="auditUser" jdbcType="TINYINT" />
    <result column="recordUpdateTime" property="recordUpdateTime" jdbcType="TIMESTAMP" />
    <result column="operator_user_id" property="operatorUserId" jdbcType="INTEGER" />
    <result column="implementer_operator_user_id" property="implementerOperatorUserId" jdbcType="INTEGER" />
    <result column="province" property="province" jdbcType="VARCHAR" />
    <result column="referral_user" property="referralUser" jdbcType="VARCHAR" />
    <result column="referral_code" property="referralCode" jdbcType="VARCHAR" />
    <result column="binding_time" property="bindingTime" jdbcType="TIMESTAMP" />
    <result column="activity_time" property="activateTime" jdbcType="TIMESTAMP" />
    <result column="activity_user_id" property="activateUserId" jdbcType="INTEGER" />
    <result column="activity_state" property="activityStatus" jdbcType="TINYINT" />
    <result column="sign_status" property="signStatus" jdbcType="TINYINT" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="terminate_reason" property="terminateReason" jdbcType="VARCHAR" />
    <result column="terminate_time" property="terminateTime" jdbcType="TIMESTAMP" />
    <result column="congeal_time" property="congealTime" jdbcType="TIMESTAMP" />
    <result column="congeal_person" property="congealPerson" jdbcType="VARCHAR" />
    <result column="organ_sign_type" property="organSignType" jdbcType="TINYINT" />
    <result column="biz_model" property="bizModel" jdbcType="TINYINT" />
    <result column="account_set_name" property="accountSetName" jdbcType="VARCHAR" />
    <result column="main_yn" property="mainYn" jdbcType="TINYINT" />
    <result column="main_organ_sign" property="mainOrganSign" jdbcType="VARCHAR" />
  </resultMap>
   <sql id="Base_Column_List">
   id,manager_user_id,drugstore_name,manager_name,contact_phone,organ_sign,headquarters_organ_sign,sender_id,secret_key,organ_sign_type,biz_model,province,city,area,area_code,address,type,status,create_time,update_time,audit_record_id,business_license_name,business_license_number,business_license_img,pharmaceutical_trading_license_img,quality_management_license_img,referral_code,sign_status,terminate_reason,terminate_time,congeal_time,congeal_person,storehouse_yn,
   account_set_name,main_yn,main_organ_sign,medical_merchant_no,sender_account,sender_ext,device_supervision_code
   </sql>

  <select id="getDrugstoreByMultipleOrganSign" parameterType="java.lang.String" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
	select * from saas_drugstore 
	where organ_sign in 
    <foreach collection="orgs" item="org" open="(" close=")" separator=",">
	  #{org}
  	</foreach>
  </select>

  <select id="getDrugstoreByHeadquartersOrganSign" parameterType="java.lang.String" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
	select * from saas_drugstore where headquarters_organ_sign = #{headquartersOrganSign, jdbcType=VARCHAR}
  </select>

  <select id="getDrugstoreByHeadquartersOrganSignAndIsDrugstoreHidden" parameterType="java.lang.String" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
	select * from saas_drugstore where headquarters_organ_sign = #{headquartersOrganSign, jdbcType=VARCHAR} and is_drugstore_hidden = #{isDrugstoreHidden, jdbcType=TINYINT}
  </select>

    <select id="getDrugstoreListForPage" parameterType="com.xyy.user.module.dto.restructure.SaaSDrugstoreDto" resultType="com.xyy.user.module.dto.restructure.SaaSDrugstoreDto">
	 select * from saas_drugstore a where a.headquarters_organ_sign = #{headquartersOrganSign, jdbcType=VARCHAR}
        <if test="drugstoreName != null and drugstoreName != ''">
            and a.drugstore_name LIKE CONCAT('%',#{drugstoreName, jdbcType=VARCHAR},'%')
        </if>
        <if test="organSign != null and organSign != ''">
            and a.organ_sign = #{organSign, jdbcType=VARCHAR}
        </if>
  </select>

  <select id="getDrugstoreById" parameterType="java.lang.Integer" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
	select * from saas_drugstore where id = #{id, jdbcType=INTEGER}
  </select>
  
  <select id="getSaaSDrugstoreByCondition" parameterType="com.xyy.user.module.dto.restructure.SaaSDrugstoreDto" resultType="com.xyy.user.module.dto.restructure.SaaSDrugstoreDto">
    select a.*,a.area_code areaCode, b.status packageStatus,  c.package_name packageName from saas_drugstore a left outer join saas_package_bind_info b on
    <choose>
        <when test="bizModel == 2 and organSignType == 3">
            a.organ_sign = b.organ_sign
        </when>
        <otherwise>
            a.headquarters_organ_sign = b.organ_sign or a.organ_sign = b.organ_sign
        </otherwise>
    </choose>
     LEFT JOIN saas_package c on c.id = b.package_id
      <where>
      1 = 1 
      <if test="managerUserId != null">
        and a.manager_user_id = #{managerUserId, jdbcType=INTEGER}
      </if>
      <if test="managerUserIds != null and managerUserIds.size >0 ">
          and a.manager_user_id in
          <foreach collection="managerUserIds" item="item" index="index" open="(" separator="," close=")">
              #{item, jdbcType=INTEGER}
          </foreach>
      </if>
      <if test="drugstoreName != null and drugstoreName!=''">
        and a.drugstore_name LIKE CONCAT('%',#{drugstoreName, jdbcType=VARCHAR},'%')
      </if>
      <if test="managerName != null and managerName != ''">
         and a.manager_name LIKE CONCAT('%',#{managerName, jdbcType=VARCHAR},'%')
      </if>
      <if test="contactPhone != null and contactPhone != ''">
         and a.contact_phone LIKE CONCAT('%',#{contactPhone, jdbcType=VARCHAR},'%')
      </if>
      <if test="organSign != null and organSign != ''">
          and a.organ_sign = #{organSign, jdbcType=VARCHAR}
      </if>
      <if test="headquartersOrganSign != null and headquartersOrganSign != ''">
         and a.headquarters_organ_sign LIKE CONCAT('%',#{headquartersOrganSign, jdbcType=VARCHAR},'%')
      </if>
      <if test="organSignType != null">
         and a.organ_sign_type = #{organSignType, jdbcType=TINYINT}
      </if>
      <if test="bizModel != null">
         and a.biz_model = #{bizModel, jdbcType=TINYINT}
      </if>
      <if test="neBizModel != null">
         and a.biz_model != #{neBizModel, jdbcType=TINYINT}
      </if>
      <if test="createTimeStartStr != null  and createTimeStartStr != ''">
         and a.create_time >= #{createTimeStartStr, jdbcType=TIMESTAMP}
      </if>
      <if test="createTimeEndStr != null and createTimeEndStr != ''">
         and a.create_time &lt;= #{createTimeEndStr, jdbcType=TIMESTAMP}
      </if>
      <if test="province != null and province!='' ">
         and a.province like  CONCAT("%", #{province},"%")
      </if>
      <if test="packageName != null and packageName!=''">
          and c.package_name like  CONCAT("%", #{packageName},"%")
      </if>
      <if test="auditRecordId != null">
         and a.audit_record_id = #{auditRecordId, jdbcType=INTEGER}
      </if>
      <if test="status != null">
         and a.status = #{status, jdbcType=TINYINT}
      </if>
      <if test="packageStatus != null">
         and b.status = #{packageStatus, jdbcType=TINYINT}
      </if>
      <if test="type != null">
         and a.type = #{type, jdbcType=TINYINT}
      </if>
      <if test="typeList != null and typeList.size > 0">
          and a.type in
          <foreach collection="typeList" item="item" index="index" open="(" separator="," close=")">
              #{item, jdbcType=TINYINT}
          </foreach>
      </if>
      <if test="provinceNames != null and provinceNames.size > 0">
          and a.province in
          <foreach collection="provinceNames" item="item" index="index" open="(" separator="," close=")">
              #{item, jdbcType=TINYINT}
          </foreach>
      </if>
      <if test="storehouseYn != null">
         and a.storehouse_yn = #{storehouseYn, jdbcType=TINYINT}
      </if>
    </where>
    order by create_time desc
  </select>

    <select id="getSaaSDrugstoreByConditionWithNoOr" parameterType="com.xyy.user.module.dto.restructure.SaaSDrugstoreDto" resultType="com.xyy.user.module.dto.restructure.SaaSDrugstoreDto">
        select a.*,a.area_code areaCode, b.status packageStatus,  c.package_name packageName from saas_drugstore a left outer join saas_package_bind_info b on a.root_organ_sign = b.organ_sign
        LEFT  JOIN saas_package c on c.id = b.package_id
        <where>
            1 = 1
            <if test="managerUserId != null">
                and a.manager_user_id = #{managerUserId, jdbcType=INTEGER}
            </if>
            <if test="managerUserIds != null and managerUserIds.size >0 ">
                and a.manager_user_id in
                <foreach collection="managerUserIds" item="item" index="index" open="(" separator="," close=")">
                    #{item, jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="drugstoreName != null and drugstoreName!=''">
                and a.drugstore_name LIKE CONCAT('%',#{drugstoreName, jdbcType=VARCHAR},'%')
            </if>
            <if test="managerName != null and managerName != ''">
                and a.manager_name LIKE CONCAT('%',#{managerName, jdbcType=VARCHAR},'%')
            </if>
            <if test="contactPhone != null and contactPhone != ''">
                and a.contact_phone LIKE CONCAT('%',#{contactPhone, jdbcType=VARCHAR},'%')
            </if>
            <if test="organSign != null and organSign != ''">
                and a.organ_sign = #{organSign, jdbcType=VARCHAR}
            </if>
            <if test="organSigns != null and organSigns.size >0 ">
                and a.organ_sign in
                <foreach collection="organSigns" item="item" index="index" open="(" separator="," close=")">
                    #{item, jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="notInOrganSigns != null and notInOrganSigns.size >0 ">
                and a.organ_sign not in
                <foreach collection="notInOrganSigns" item="item" index="index" open="(" separator="," close=")">
                    #{item, jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="headquartersOrganSign != null and headquartersOrganSign != ''">
                and a.headquarters_organ_sign LIKE CONCAT('%',#{headquartersOrganSign, jdbcType=VARCHAR},'%')
            </if>
            <if test="organSignType != null">
                and a.organ_sign_type = #{organSignType, jdbcType=TINYINT}
            </if>
            <if test="bizModel != null">
                and a.biz_model = #{bizModel, jdbcType=TINYINT}
            </if>
            <if test="neBizModel != null">
                and a.biz_model != #{neBizModel, jdbcType=TINYINT}
            </if>
            <if test="createTimeStartStr != null  and createTimeStartStr != ''">
                and a.create_time >= #{createTimeStartStr, jdbcType=TIMESTAMP}
            </if>
            <if test="createTimeEndStr != null and createTimeEndStr != ''">
                and a.create_time &lt;= #{createTimeEndStr, jdbcType=TIMESTAMP}
            </if>
            <if test="province != null and province!='' ">
                and a.province like  CONCAT("%", #{province},"%")
            </if>
            <if test="packageName != null and packageName!=''">
                and c.package_name like  CONCAT("%", #{packageName},"%")
            </if>
            <if test="auditRecordId != null">
                and a.audit_record_id = #{auditRecordId, jdbcType=INTEGER}
            </if>
            <if test="status != null">
                and a.status = #{status, jdbcType=TINYINT}
            </if>
            <if test="packageStatus != null">
                and b.status = #{packageStatus, jdbcType=TINYINT}
            </if>
            <if test="endTimeStr != null and endTimeStr != ''">
                and b.end_time &lt;= #{endTimeStr}
            </if>
            <if test="type != null">
                and a.type = #{type, jdbcType=TINYINT}
            </if>
            <if test="typeList != null and typeList.size > 0">
                and a.type in
                <foreach collection="typeList" item="item" index="index" open="(" separator="," close=")">
                    #{item, jdbcType=TINYINT}
                </foreach>
            </if>
            <if test="provinceNames != null and provinceNames.size > 0">
                and a.province in
                <foreach collection="provinceNames" item="item" index="index" open="(" separator="," close=")">
                    #{item, jdbcType=TINYINT}
                </foreach>
            </if>
            <if test="storehouseYn != null">
                and a.storehouse_yn = #{storehouseYn, jdbcType=TINYINT}
            </if>
            <if test="isDrugstoreHidden != null">
                and a.is_drugstore_hidden = #{isDrugstoreHidden, jdbcType=TINYINT}
            </if>
        </where>
        order by create_time desc
    </select>
  
  
<!-- 分割线分割线分割线分割线分割线分割线分割线分割线分割线分割线  华丽的分割线，分隔过去和将来  分割线分割线分割线分割线分割线分割线分割线分割线分割线分割线分割线分割线 -->

  <insert id="insert" parameterType="com.xyy.user.provider.module.entity.SaasDrugstore" useGeneratedKeys="true" keyProperty="id" >
    insert into saas_drugstore
    <trim prefix="(" suffix=")" suffixOverrides=",">
	    <if test="managerUserId != null" >
	      manager_user_id,
	    </if>
	    <if test="drugstoreName != null" >
	      drugstore_name,
	    </if>
	    <if test="managerName != null" >
	      manager_name,
	    </if>
	    <if test="contactPhone != null" >
	      contact_phone,
	    </if>
	    <if test="organSign != null" >
	      organ_sign,
	    </if>
	    <if test="province != null" >
	      province,
	    </if>
	    <if test="city != null" >
	      city,
	    </if>
	    <if test="area != null" >
	      area,
	    </if>
        <if test="areaCode != null" >
            area_code,
	    </if>
	    <if test="address != null" >
	      address,
	    </if>
        <if test="longitude != null" >
            longitude,
        </if>
        <if test="latitude != null" >
            latitude,
        </if>
	    <if test="type != null" >
	      type,
	    </if>
	    <if test="status != null" >
	      status,
	    </if>
	    <if test="createTime != null" >
	      create_time,
	    </if>
	    <if test="updateTime != null" >
	      update_time,
	    </if>
	    <if test="auditRecordId != null" >
	      audit_record_id,
	    </if>
	    <if test="businessLicenseName != null" >
	      business_license_name,
	    </if>
	    <if test="businessLicenseNumber != null" >
	      business_license_number,
	    </if>
	    <if test="businessLicenseImg != null" >
	      business_license_img,
	    </if>
	    <if test="pharmaceuticalTradingLicenseImg != null" >
	      pharmaceutical_trading_license_img,
	    </if>
	    <if test="qualityManagementLicenseImg != null" >
	      quality_management_license_img,
	    </if>
	    <if test="referralCode != null" >
	      referral_code,
	    </if>
	    <if test="organSignType != null" >
	      organ_sign_type,
	    </if>
	    <if test="bizModel != null" >
	      biz_model,
	    </if>
	    <if test="headquartersOrganSign != null" >
	      headquarters_organ_sign,
	    </if>
	    <if test="storehouseYn != null" >
            storehouse_yn,
	    </if>
        <if test="accountSetName != null" >
            account_set_name,
	    </if>
        <if test="mainYn != null" >
            main_yn,
	    </if>
        <if test="mainOrganSign != null" >
            main_organ_sign,
        </if>
        <if test="medicalMerchantNo != null and medicalMerchantNo != ''" >
            medical_merchant_no,
        </if>
        <if test="senderAccount != null and senderAccount != ''" >
            sender_account,
        </if>
        <if test="senderExt != null and senderExt != ''" >
            sender_ext,
        </if>
        <if test="deviceSupervisionCode != null and deviceSupervisionCode != ''">
            device_supervision_code,
        </if>
        <if test="deviceSupervisionKey != null and deviceSupervisionKey != ''">
            device_supervision_key,
        </if>
        <if test="appId != null and appId != ''">
            app_id,
        </if>
        <if test="appSecret != null and appSecret != ''">
            app_secret,
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
	    <if test="managerUserId != null" >
	      #{managerUserId, jdbcType=INTEGER},
	    </if>
	    <if test="drugstoreName != null" >
	      #{drugstoreName, jdbcType=VARCHAR},
	    </if>
	    <if test="managerName != null" >
	      #{managerName, jdbcType=VARCHAR},
	    </if>
	    <if test="contactPhone != null" >
	      #{contactPhone, jdbcType=VARCHAR},
	    </if>
	    <if test="organSign != null" >
	      #{organSign, jdbcType=VARCHAR},
	    </if>
	    <if test="province != null" >
	      #{province, jdbcType=VARCHAR},
	    </if>
	    <if test="city != null" >
	      #{city, jdbcType=VARCHAR},
	    </if>
	    <if test="area != null" >
	      #{area, jdbcType=VARCHAR},
	    </if>
        <if test="areaCode != null" >
	      #{areaCode, jdbcType=VARCHAR},
	    </if>
	    <if test="address != null" >
	      #{address, jdbcType=VARCHAR},
	    </if>
        <if test="longitude != null" >
            #{longitude, jdbcType=DECIMAL},
        </if>
        <if test="latitude != null" >
            #{latitude, jdbcType=DECIMAL},
        </if>
	    <if test="type != null" >
	      #{type, jdbcType=TINYINT},
	    </if>
	    <if test="status != null" >
	      #{status, jdbcType=TINYINT},
	    </if>
	    <if test="createTime != null" >
	      #{createTime, jdbcType=TIMESTAMP},
	    </if>
	    <if test="updateTime != null" >
	      #{updateTime, jdbcType=TIMESTAMP},
	    </if>
	    <if test="auditRecordId != null" >
	      #{auditRecordId, jdbcType=INTEGER},
	    </if>
	    <if test="businessLicenseName != null" >
	      #{businessLicenseName, jdbcType=VARCHAR},
	    </if>
	    <if test="businessLicenseNumber != null" >
	      #{businessLicenseNumber, jdbcType=VARCHAR},
	    </if>
	    <if test="businessLicenseImg != null" >
	      #{businessLicenseImg, jdbcType=VARCHAR},
	    </if>
	    <if test="pharmaceuticalTradingLicenseImg != null" >
	      #{pharmaceuticalTradingLicenseImg, jdbcType=VARCHAR},
	    </if>
	    <if test="qualityManagementLicenseImg != null" >
	      #{qualityManagementLicenseImg, jdbcType=VARCHAR},
	    </if>
	    <if test="referralCode != null" >
	      #{referralCode, jdbcType=VARCHAR},
	    </if>
	    <if test="organSignType != null" >
	      #{organSignType, jdbcType=TINYINT},
	    </if>
	    <if test="bizModel != null" >
	      #{bizModel, jdbcType=TINYINT},
	    </if>
	    <if test="headquartersOrganSign != null" >
	      #{headquartersOrganSign, jdbcType=VARCHAR},
	    </if>
	    <if test="storehouseYn != null" >
	      #{storehouseYn, jdbcType=TINYINT},
	    </if>
        <if test="accountSetName != null" >
            #{accountSetName, jdbcType=VARCHAR},
        </if>
        <if test="mainYn != null" >
            #{mainYn, jdbcType=TINYINT},
        </if>
        <if test="mainOrganSign != null" >
            #{mainOrganSign, jdbcType=VARCHAR},
        </if>
        <if test="medicalMerchantNo != null and medicalMerchantNo != ''" >
            #{medicalMerchantNo, jdbcType=VARCHAR},
        </if>
        <if test="senderAccount != null and senderAccount != ''" >
            #{senderAccount, jdbcType=VARCHAR},
        </if>
        <if test="senderExt != null and senderExt != ''" >
            #{senderExt, jdbcType=VARCHAR},
        </if>
        <if test="deviceSupervisionCode != null and deviceSupervisionCode != ''">
            #{deviceSupervisionCode,jdbcType=VARCHAR},
        </if>
        <if test="deviceSupervisionKey != null and deviceSupervisionKey != ''">
            #{deviceSupervisionKey, jdbcType=VARCHAR},
        </if>
        <if test="appId != null and appId != ''">
            #{appId, jdbcType=VARCHAR},
        </if>
        <if test="appSecret != null and appSecret != ''">
            #{appSecret, jdbcType=VARCHAR},
        </if>
    </trim>
  </insert>

  <select id="selectDrugstoreByOrganSign" parameterType="java.lang.String" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
    select * from saas_drugstore where organ_sign = #{organSign, jdbcType=VARCHAR}
  </select>

  <select id="selectDrugstoreByVagueOrganSign" parameterType="java.lang.String" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
    select * from saas_drugstore
    <where>
      1 = 1
      <if test="organSign != null and organSign != '' ">
        and organ_sign like CONCAT('%',#{organSign, jdbcType=VARCHAR},'%')
      </if>
    </where>
  </select>
  
  <update id="updateDrugstoreByOrganSign" parameterType="com.xyy.user.provider.module.entity.SaasDrugstore" >
    update saas_drugstore 
    <set>
        <if test="managerUserId != null" >
	      manager_user_id = #{managerUserId, jdbcType=INTEGER},
	    </if>
	    <if test="drugstoreName != null" >
	      drugstore_name = #{drugstoreName, jdbcType=VARCHAR},
	    </if>
	    <if test="managerName != null" >
	      manager_name = #{managerName, jdbcType=VARCHAR},
	    </if>
	    <if test="contactPhone != null" >
	      contact_phone = #{contactPhone, jdbcType=VARCHAR},
	    </if>
<!-- 	    <if test="organSign != null" > -->
<!-- 	      #{organSign, jdbcType=VARCHAR}, -->
<!-- 	    </if> -->
	    <if test="province != null" >
	      province = #{province, jdbcType=VARCHAR},
	    </if>
	    <if test="city != null" >
	      city = #{city, jdbcType=VARCHAR},
	    </if>
	    <if test="area != null" >
	      area = #{area, jdbcType=VARCHAR},
	    </if>
	    <if test="areaCode != null and areaCode != ''" >
            area_code = #{areaCode, jdbcType=VARCHAR},
	    </if>
	    <if test="address != null" >
	      address = #{address, jdbcType=VARCHAR},
	    </if>
	    <if test="type != null" >
	      type = #{type, jdbcType=TINYINT},
	    </if>
	    <if test="status != null" >
	      status = #{status, jdbcType=TINYINT},
	    </if>
	    <if test="updateTime != null" >
	      update_time = #{updateTime, jdbcType=TIMESTAMP},
	    </if>
	    <if test="auditRecordId != null" >
	      audit_record_id = #{auditRecordId, jdbcType=INTEGER},
	    </if>
	    <if test="businessLicenseName != null" >
	      business_license_name = #{businessLicenseName, jdbcType=VARCHAR},
	    </if>
	    <if test="businessLicenseNumber != null and businessLicenseNumber != '' " >
	      business_license_number = #{businessLicenseNumber, jdbcType=VARCHAR},
	    </if>
	    <if test="businessLicenseImg != null" >
	      business_license_img = #{businessLicenseImg, jdbcType=VARCHAR},
	    </if>
	    <if test="pharmaceuticalTradingLicenseImg != null" >
	      pharmaceutical_trading_license_img = #{pharmaceuticalTradingLicenseImg, jdbcType=VARCHAR},
	    </if>
	    <if test="qualityManagementLicenseImg != null" >
	      quality_management_license_img = #{qualityManagementLicenseImg, jdbcType=VARCHAR},
	    </if>
	    <if test="organSignType != null" >
	      organ_sign_type = #{organSignType, jdbcType=TINYINT},
	    </if>
	    <if test="bizModel != null" >
	      biz_model = #{bizModel, jdbcType=TINYINT},
	    </if>
	    <if test="headquartersOrganSign != null" >
	      headquarters_organ_sign = #{headquartersOrganSign, jdbcType=VARCHAR},
	    </if>
        <if test="msfxSign != null" >
            msfx_sign = #{msfxSign},
        </if>
        <if test="medicalMerchantNo != null and medicalMerchantNo != ''" >
            medical_merchant_no = #{medicalMerchantNo},
        </if>
        <if test="senderId != null" >
            sender_id = #{senderId},
        </if>
        <if test="senderAccount != null and senderAccount != ''" >
            sender_account = #{senderAccount},
        </if>
        <if test="senderExt != null and senderExt != ''" >
            sender_ext = #{senderExt},
        </if>
        <if test="deviceSupervisionCode != null and deviceSupervisionCode != ''">
            device_supervision_code = #{deviceSupervisionCode},
        </if>
        <if test="deviceSupervisionKey != null and deviceSupervisionKey != ''">
            device_supervision_key = #{deviceSupervisionKey, jdbcType=VARCHAR},
        </if>
        <if test="appId != null and appId != ''">
            app_id = #{appId, jdbcType=VARCHAR},
        </if>
        <if test="appSecret != null and appSecret != ''">
            app_secret = #{appSecret, jdbcType=VARCHAR},
        </if>
    </set>
     where organ_sign = #{organSign, jdbcType=VARCHAR}
  </update>

  <update id="updateDrugstore" parameterType="com.xyy.user.provider.module.entity.SaasDrugstore" >
    update saas_drugstore
    <set>
        <if test="record.managerUserId != null" >
            manager_user_id = #{record.managerUserId, jdbcType=INTEGER},
        </if>
        <if test="record.drugstoreName != null" >
        drugstore_name = #{record.drugstoreName, jdbcType=VARCHAR},
        </if>
        <if test="record.managerName != null" >
            manager_name = #{record.managerName, jdbcType=VARCHAR},
        </if>
        <if test="record.contactPhone != null" >
            contact_phone = #{record.contactPhone, jdbcType=VARCHAR},
        </if>
<!--       is_headquarters = #{record.isHeadquarters, jdbcType=TINYINT}, -->
<!--       biz_model = #{record.bizModel, jdbcType=TINYINT}, -->
        <if test="record.headquartersOrganSign != null" >
            headquarters_organ_sign = #{record.headquartersOrganSign, jdbcType=VARCHAR},
        </if>
        <if test="record.senderId != null" >
            sender_id = #{record.senderId, jdbcType=VARCHAR},
        </if>
        <if test="record.secretKey != null" >
            secret_key = #{record.secretKey, jdbcType=VARCHAR},
        </if>
        <if test="record.province != null" >
            province = #{record.province, jdbcType=VARCHAR},
        </if>
        <if test="record.city != null" >
            city = #{record.city, jdbcType=VARCHAR},
        </if>
        <if test="record.area != null" >
            area = #{record.area, jdbcType=VARCHAR},
        </if>
        <if test="record.areaCode != null" >
            area_code = #{record.areaCode, jdbcType=VARCHAR},
        </if>
        <if test="record.address != null" >
            address = #{record.address, jdbcType=VARCHAR},
        </if>
        <if test="record.longitude != null" >
            longitude = #{record.longitude, jdbcType=DECIMAL},
        </if>
        <if test="record.latitude != null" >
            latitude = #{record.latitude, jdbcType=DECIMAL},
        </if>
        <if test="record.type != null" >
            type = #{record.type, jdbcType=TINYINT},
        </if>
        <if test="record.updateTime != null" >
            update_time = #{record.updateTime, jdbcType=TIMESTAMP},
        </if>
        <if test="record.auditRecordId != null" >
            audit_record_id = #{record.auditRecordId, jdbcType=INTEGER},
        </if>
        <if test="record.auditRecordId != null" >
            business_license_name = #{record.businessLicenseName, jdbcType=VARCHAR},
        </if>
        <if test="record.businessLicenseNumber != null" >
            business_license_number = #{record.businessLicenseNumber, jdbcType=VARCHAR},
        </if>
        <if test="record.businessLicenseImg != null" >
            business_license_img = #{record.businessLicenseImg, jdbcType=VARCHAR},
        </if>
        <if test="record.pharmaceuticalTradingLicenseImg != null" >
            pharmaceutical_trading_license_img = #{record.pharmaceuticalTradingLicenseImg, jdbcType=VARCHAR},
        </if>
        <if test="record.qualityManagementLicenseImg != null" >
        quality_management_license_img = #{record.qualityManagementLicenseImg, jdbcType=VARCHAR},
        </if>
        <if test="record.medicalMerchantNo != null and record.medicalMerchantNo != ''" >
            medical_merchant_no = #{record.medicalMerchantNo},
        </if>
        <if test="record.senderAccount != null and record.senderAccount != ''" >
            sender_account = #{record.senderAccount},
        </if>
        <if test="record.senderExt != null and record.senderExt != ''" >
            sender_ext = #{record.senderExt},
        </if>
        <if test="record.deviceSupervisionCode != null and record.deviceSupervisionCode != ''">
            device_supervision_code = #{record.deviceSupervisionCode, jdbcType=VARCHAR},
        </if>
        <if test="record.deviceSupervisionKey != null and record.deviceSupervisionKey != ''">
            device_supervision_key = #{record.deviceSupervisionKey, jdbcType=VARCHAR},
        </if>
        <if test="record.appId != null and record.appId != ''">
            app_id = #{record.appId, jdbcType=VARCHAR},
        </if>
        <if test="record.appSecret != null and record.appSecret != ''">
            app_secret = #{record.appSecret, jdbcType=VARCHAR},
        </if>
    </set>
    where organ_sign = #{record.organSign, jdbcType=VARCHAR}
  </update>

  <select id="findPageList" parameterType="com.xyy.user.provider.module.vo.SaasDrugstoreVO" resultMap="drugstoreVoMap">
    select a.id,c.phone, a.drugstore_name, a.organ_sign, a.type, a.status,a.create_time,m.operator_user_id as operator_user_id, i.operator_user_id as implementer_operator_user_id,
    a.business_license_name , a.manager_name, a.province, a.city,a.area,a.address
    from saas_drugstore a
    LEFT JOIN saas_user c ON a.manager_user_id = c.id
    LEFT JOIN saas_employee b ON a.organ_sign = b.organ_sign AND b.user_id = c.id
    LEFT JOIN saas_ybm_account y on a.organ_sign = y.organ_sign
    LEFT JOIN saas_account_maintain m on m.user_id = c.id
    LEFT JOIN saas_account_implementer i on i.user_id = c.id
    <where>
      1 = 1
      <if test="phone != null and phone!=''">
        and c.phone LIKE CONCAT('%',#{phone, jdbcType=VARCHAR},'%')
      </if>
      <if test="ybmUsername != null and ybmUsername!=''">
        and y.username LIKE CONCAT('%',#{ybmUsername, jdbcType=VARCHAR},'%')
      </if>
      <if test="drugstoreName != null and drugstoreName!=''">
        and a.drugstore_name LIKE CONCAT('%',#{drugstoreName, jdbcType=VARCHAR},'%')
      </if>
      <if test="organSign != null and organSign!=''">
        and a.organ_sign LIKE CONCAT('%',#{organSign, jdbcType=VARCHAR},'%')
      </if>
      <if test="startTime != null">
        <![CDATA[ and a.create_time >= #{startTime, jdbcType=TIMESTAMP}]]>
      </if>
      <if test="endTime != null">
        <![CDATA[ and a.create_time <= #{endTime, jdbcType=TIMESTAMP}]]>
      </if>
      <if test="operatorIds!=null">
        and (
        m.operator_user_id in
        <foreach collection="operatorIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach> or
        i.operator_user_id in
        <foreach collection="operatorIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach> )
      </if>
    </where>
    order by a.create_time desc
  </select>

<select id="getDrugstoreByCondition" parameterType="com.xyy.user.module.dto.SaasDrugstoreDto" resultMap="drugstoreVoMap">
    select a.id,c.phone, a.drugstore_name, a.organ_sign, a.type, a.status,a.create_time,m.operator_user_id as operator_user_id, i.operator_user_id as implementer_operator_user_id,i.binding_time,
    i.activity_time,i.activity_user_id,i.activity_state,
    a.business_license_name, a.manager_name, a.province, a.city,a.area,a.address,m.referral_code ,a.drugstore_name referral_user,a.sign_status,a.terminate_reason,a.terminate_time,a.congeal_time,a.congeal_person,
    a.organ_sign_type,a.biz_model,a.account_set_name,a.main_yn,a.main_organ_sign,a.business_license_number,a.contact_phone
    from saas_drugstore a
    LEFT JOIN saas_user c ON a.manager_user_id = c.id
    LEFT JOIN saas_ybm_account y on a.organ_sign = y.organ_sign
    LEFT JOIN saas_account_maintain m on m.user_id = c.id
    LEFT JOIN saas_account_implementer i on i.user_id = c.id
    <where>
      1 = 1
      <if test="phone != null and phone!=''">
        and c.phone LIKE CONCAT('%',#{phone, jdbcType=VARCHAR},'%')
      </if>
      <if test="ybmUsername != null and ybmUsername!=''">
        and y.username LIKE CONCAT('%',#{ybmUsername, jdbcType=VARCHAR},'%')
      </if>
      <if test="drugstoreName != null and drugstoreName!=''">
        and a.drugstore_name LIKE CONCAT('%',#{drugstoreName, jdbcType=VARCHAR},'%')
      </if>
      <if test="organSign != null and organSign!=''">
        and a.organ_sign LIKE CONCAT('%',#{organSign, jdbcType=VARCHAR},'%')
      </if>
      <if test="signStatus != null">
           and a.sign_status = #{signStatus, jdbcType=TINYINT}
      </if>
      <if test="type != null">
           and a.type = #{type, jdbcType=TINYINT}
      </if>
        <if test="mainYn != null">
            and a.main_yn = #{mainYn, jdbcType=TINYINT}
        </if>
      <if test="startTime != null">
        <![CDATA[ and a.create_time >= #{startTime, jdbcType=TIMESTAMP}]]>
      </if>
      <if test="endTime != null">
        <![CDATA[ and a.create_time <= #{endTime, jdbcType=TIMESTAMP}]]>
      </if>
      <if test="operatorIds!=null">
        and (
        m.operator_user_id in
        <foreach collection="operatorIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach> or
        i.operator_user_id in
        <foreach collection="operatorIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach> )
      </if>
      <if test="province != null">
        and a.province like  CONCAT("%", #{province},"%")
      </if>
      <if test="provinceList!=null">
        and a.province in
        <foreach collection="provinceList" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
        <if test="city != null">
            and a.city like  CONCAT("%", #{city},"%")
        </if>
        <if test="area != null">
            and a.area like  CONCAT("%", #{area},"%")
        </if>
        <if test="status != null">
            and a.status =  #{status, jdbcType=TINYINT}
        </if>
        <if test="organSignType != null">
            and a.organ_sign_type = #{organSignType, jdbcType=TINYINT}
        </if>
        <if test="bizModel != null">
            and a.biz_model = #{bizModel, jdbcType=TINYINT}
        </if>
    </where>
    order by a.create_time desc
  </select>

  <select id="findDrugstoreById" parameterType="java.lang.Integer" resultMap="drugstoreVoMap" >
    select m.referral_code as referral_code, a.*, c.phone,d.audit_type,d.audit_status
    from saas_drugstore a
    LEFT JOIN saas_user c ON a.manager_user_id = c.id
    LEFT JOIN saas_employee b ON a.organ_sign = b.organ_sign AND b.user_id = c.id
    left join saas_audit_drugstore_record d on d.id = a.audit_record_id
    LEFT JOIN saas_account_maintain m on m.user_id = c.id
    where a.id=#{id}
    limit 1
  </select>

  <select id="selectDrugstoreByBusinessLicenseNumber" parameterType="java.lang.String" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
    select * from saas_drugstore where business_license_number = #{number, jdbcType=VARCHAR} and business_license_number != ''
  </select>

  <select id="selectDrugstoreByBusinessLicenseNumberIngoreOrganSign" parameterType="java.lang.String" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
    select * from saas_drugstore where business_license_number = #{number, jdbcType=VARCHAR} and business_license_number != ''
    and organ_sign != #{organSign, jdbcType=VARCHAR}
  </select>
  <select id="findDrugstoreByManagerUserIdAndOrganSign" parameterType="java.util.Map" resultMap="BaseResultMap">
    select * from saas_drugstore where manager_user_id = #{id} and organ_sign=#{organSign}
  </select>

  <update id="updateStatusById" parameterType="java.util.Map" >
    update saas_drugstore
      <set>
          congeal_time = #{congealTime, jdbcType=TIMESTAMP},
          <if test="status != null" >
           status=#{status,jdbcType=TINYINT},
          </if>
          <if test="currentUserName != null" >
              congeal_person = #{currentUserName, jdbcType=VARCHAR}
          </if>

      </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

    <update id="updateStatusByIdList" >
        update saas_drugstore
        <set>
            congeal_time = #{congealTime, jdbcType=TIMESTAMP},
            <if test="status != null" >
                status=#{status,jdbcType=TINYINT},
            </if>
            <if test="currentUserName != null" >
                congeal_person = #{currentUserName, jdbcType=VARCHAR}
            </if>
        </set>
        where id  in
        <foreach collection="idList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>
  
  <update id="updateStatusByOrganSign" parameterType="com.xyy.user.provider.module.entity.SaasDrugstore" >
    update saas_drugstore set status=#{status}
    where organ_sign = #{organSign, jdbcType=VARCHAR}
  </update>

  <update id="updateDrugstoreNameByOrganSign" parameterType="java.util.Map" >
    update saas_drugstore set drugstore_name=#{record.drugstoreName}
    where organ_sign=#{record.organSign}
  </update>

  <select id="selectDrugstoreByManagerUserId" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
    select * from saas_drugstore where manager_user_id = #{managerUserId, jdbcType=INTEGER}
    and manager_user_id > 0
  </select>

  <select id="selectDrugstoreByDrugstoreName" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
    select * from saas_drugstore where drugstore_name = #{drugstoreName, jdbcType=VARCHAR}
  </select>
  
  <select id="selectDrugstoreListByDrugstoreName" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
    select * from saas_drugstore where drugstore_name = #{drugstoreName, jdbcType=VARCHAR}
  </select>

    <select id="selectDrugstoreByBusinessLicenseName" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
    select * from saas_drugstore where business_license_name = #{businessLicenseName, jdbcType=VARCHAR}
  </select>

    <select id="queryAllDrugstoreByName" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
        select * from saas_drugstore where 1 = 1
        <if test="drugstoreName != null">
            AND drugstore_name LIKE CONCAT('%',#{drugstoreName, jdbcType=VARCHAR},'%')
        </if>
    </select>

    <select id="queryAllDrugstoreByNameFilter" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
        select * from saas_drugstore where 1 = 1 and main_yn = 1
        <if test="drugstoreName != null">
            AND drugstore_name LIKE CONCAT('%',#{drugstoreName, jdbcType=VARCHAR},'%')
        </if>
        <if test="organSigns != null and organSigns.size > 0">
            AND organ_sign not in
            <foreach collection="organSigns" item="organSign" open="(" close=")" separator=",">
                #{organSign}
            </foreach>
        </if>
    </select>

    <select id="queryAllDrugstoreByNameIncludeYbm" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
        select sd.* from saas_drugstore sd inner join saas_ybm_account sya on sya.organ_sign = sd.organ_sign and sya.bind_status = 1
        where 1 = 1
        <if test="drugstoreName != null">
            AND sd.drugstore_name LIKE CONCAT('%',#{drugstoreName, jdbcType=VARCHAR},'%')
        </if>
    </select>

    <select id="selectOrganSignByDrugstoreName" parameterType="java.lang.String" resultType="java.lang.String">
    select organ_sign from saas_drugstore
    where drugstore_name LIKE CONCAT('%',#{drugstoreName, jdbcType=VARCHAR},'%')
  </select>

    <select id="selectAllOrganSign" resultType="java.lang.String">
    select organ_sign from saas_drugstore
  </select>

    <select id="selectDrugstoreByManagerPhoneOrOrganSignOrDrugstoreName" resultType="com.xyy.user.provider.module.entity.SaasDrugstore" parameterType="java.lang.String">
    select * from saas_drugstore as drugstore
    left join saas_user as user on user.id = drugstore.manager_user_id
    where drugstore.organ_sign = #{enterprise, jdbcType=VARCHAR} or user.phone = #{enterprise, jdbcType=VARCHAR} or drugstore.business_license_name = #{enterprise, jdbcType=VARCHAR} or drugstore.drugstore_name = #{enterprise, jdbcType=VARCHAR}
  </select>

    <select id="getDrugstoreByOrganSign" resultType="com.xyy.user.provider.module.entity.SaasDrugstore" parameterType="java.lang.String">
        select * from saas_drugstore as drugstore
                          left join saas_user as user on user.id = drugstore.manager_user_id
        where drugstore.organ_sign = #{enterprise, jdbcType=VARCHAR}
    </select>
  
  <select id="selectDrugstoreEqUserPhoneEqOrganSignLikeDrugstoreName" resultType="com.xyy.user.module.dto.SaasDrugstoreDto" parameterType="com.xyy.user.module.dto.SaasDrugstoreDto">
    select a.*, b.phone from saas_drugstore as a
    inner join saas_user as b on b.id = a.manager_user_id
    where 1 = 1 
    <if test="phone != null and phone != ''">
      and b.phone = #{phone, jdbcType=VARCHAR} 
    </if>
    <if test="drugstoreName != null and drugstoreName != ''">
      and a.drugstore_name LIKE CONCAT('%',#{drugstoreName, jdbcType=VARCHAR},'%')
    </if>
    <if test="organSign != null and organSign != ''">
      and a.organ_sign = #{organSign, jdbcType=VARCHAR}
    </if>
  </select>

    <select id="getOrganSignListByProvince" resultType="java.lang.String">
        select organ_sign from saas_drugstore where status=1
        <if test="province != null || province != ''">
            and province = #{province, jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectOrganSignByReferralCode" resultType="java.lang.String">
      select organ_sign from saas_drugstore where referral_code = #{referralCode}

    </select>
    <select id="selectDrugstoreByYbmAccount" resultType="com.xyy.user.provider.module.entity.SaasDrugstore" >
        SELECT b.province,b.organ_sign from saas_ybm_account a
        LEFT JOIN saas_drugstore b on a.organ_sign = b.organ_sign
        WHERE a.bind_status =1
    </select>
    <select id="selectByOrganSignList" resultType="com.xyy.user.provider.module.entity.SaasDrugstore" >
        select * from saas_drugstore
        where organ_sign in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="selectDrugstoreByProvinces" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
        select
        *
        from saas_drugstore where status=1
        <if test="provinces != null and  provinces.size>0">
            and province in
            <foreach collection="provinces" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="getOrganSignInfos" parameterType="java.lang.String" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
    select * from saas_drugstore where province = #{organSign, jdbcType=VARCHAR} and status = 1
        <if test="organSignMix != null and organSignMix !=''">
            AND instr(organ_sign,#{organSignMix})>0
        </if>
        <if test="organSignName != null and organSignName !=''">
            AND instr(drugstore_name,#{organSignName})>0
        </if>
        <if test="startTime != null and startTime !=''">
            <![CDATA[ and create_time >= #{startTime, jdbcType=TIMESTAMP}]]>
        </if>
        <if test="endTime != null and endTime !=''">
            <![CDATA[ and create_time <= #{endTime, jdbcType=TIMESTAMP}]]>
        </if>
  </select>

    <select id="selectDrugstoreListByOrganSignList" resultType="com.xyy.user.provider.module.entity.SaasDrugstore" >
        select organ_sign,drugstore_name from saas_drugstore
        where organ_sign in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getDrugstoreByHeadquartersOrganSignExclusionSelf" parameterType="java.lang.String" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
	select * from saas_drugstore where headquarters_organ_sign = #{headquartersOrganSign, jdbcType=VARCHAR} and organ_sign !=#{organSign, jdbcType=VARCHAR}
  </select>
    <update id="updateCrmStatusByOrganSign" parameterType="com.xyy.user.provider.module.entity.SaasDrugstore" >
    update saas_drugstore
    <set>
        terminate_time = #{terminateTime, jdbcType=TIMESTAMP},
        <if test="signStatus != null" >
            sign_status = #{signStatus, jdbcType=TINYINT},
        </if>
        <if test="terminateReason != null" >
            terminate_reason = #{terminateReason, jdbcType=VARCHAR},
        </if>
        <if test="congealPerson != null" >
            congeal_person = #{congealPerson, jdbcType=VARCHAR},
        </if>
        <if test="congealTime != null" >
            congeal_time = #{congealTime, jdbcType=TIMESTAMP},
        </if>
        <if test="status != null" >
            status = #{status, jdbcType=TINYINT}
        </if>
    </set>
    where organ_sign=#{organSign, jdbcType=VARCHAR}
  </update>

  <select id="getDrugstoreByIdScope" resultMap="BaseResultMap">
     select a.* from saas_drugstore a
      <where>
            1 = 1
        <if test="id != null">
            and a.id > #{id, jdbcType=INTEGER}
        </if>
      </where>
      order by a.id

      limit #{pageSize, jdbcType=INTEGER}
  </select>
    <select id="getDrugstoreByIdNoJoint" resultMap="BaseResultMap">
        select a.* from saas_drugstore a
        <where>
            1 = 1
            and organ_sign_type != 3
            <if test="id != null">
                and a.id > #{id, jdbcType=INTEGER}
            </if>
        </where>
        order by a.id

        limit #{pageSize, jdbcType=INTEGER}
    </select>
    <select id="getManagerUserIdPage" resultType="java.lang.Integer">
        select manager_user_id  from saas_drugstore
        where manager_user_id > 0
        order by id
    </select>

    <select id="getManagerUserIdPageWithCondition" resultType="java.lang.Integer" parameterType="com.xyy.user.module.dto.QueryCustomerParam">
        select manager_user_id  from saas_drugstore
        where manager_user_id > 0
        <if test="provinceNames != null and provinceNames.size>0">
            and province in
            <foreach collection="provinceNames" item="provinceName" open="(" close=")" separator=",">
                #{provinceName}
            </foreach>
        </if>
        order by id
    </select>

    <update id="batchUpdateAreaCode" parameterType="java.util.List">
        <foreach collection="list" item="bean" index="index" open="" close="" separator=";">
            update saas_drugstore
            <set>
                <if test="bean.areaCode != null">
                    area_code = #{bean.areaCode,jdbcType=VARCHAR}
                </if>
            </set>
            where id = #{bean.id,jdbcType=INTEGER}
        </foreach>
    </update>
    <update id="batchUpdateDrugstores" parameterType="java.util.List">
        <foreach collection="list" item="bean" index="index" open="" close="" separator=";">
            update saas_drugstore
            <set>
                <if test="bean.managerUserId != null" >
                    manager_user_id = #{bean.managerUserId, jdbcType=INTEGER},
                </if>
                <if test="bean.drugstoreName != null" >
                    drugstore_name = #{bean.drugstoreName, jdbcType=VARCHAR},
                </if>
                <if test="bean.managerName != null" >
                    manager_name = #{bean.managerName, jdbcType=VARCHAR},
                </if>
                <if test="bean.contactPhone != null" >
                    contact_phone = #{bean.contactPhone, jdbcType=VARCHAR},
                </if>
                <!-- 	    <if test="organSign != null" > -->
                <!-- 	      #{organSign, jdbcType=VARCHAR}, -->
                <!-- 	    </if> -->
                <if test="bean.province != null" >
                    province = #{bean.province, jdbcType=VARCHAR},
                </if>
                <if test="bean.city != null" >
                    city = #{bean.city, jdbcType=VARCHAR},
                </if>
                <if test="bean.area != null" >
                    area = #{bean.area, jdbcType=VARCHAR},
                </if>
                <if test="bean.address != null" >
                    address = #{bean.address, jdbcType=VARCHAR},
                </if>
                <if test="bean.type != null" >
                    type = #{bean.type, jdbcType=TINYINT},
                </if>
                <if test="bean.status != null" >
                    status = #{bean.status, jdbcType=TINYINT},
                </if>
                <if test="bean.updateTime != null" >
                    update_time = #{bean.updateTime, jdbcType=TIMESTAMP},
                </if>
                <if test="bean.auditRecordId != null" >
                    audit_record_id = #{bean.auditRecordId, jdbcType=INTEGER},
                </if>
                <if test="bean.businessLicenseName != null" >
                    business_license_name = #{bean.businessLicenseName, jdbcType=VARCHAR},
                </if>
                <if test="bean.businessLicenseNumber != null" >
                    business_license_number = #{bean.businessLicenseNumber, jdbcType=VARCHAR},
                </if>
                <if test="bean.businessLicenseImg != null" >
                    business_license_img = #{bean.businessLicenseImg, jdbcType=VARCHAR},
                </if>
                <if test="bean.pharmaceuticalTradingLicenseImg != null" >
                    pharmaceutical_trading_license_img = #{bean.pharmaceuticalTradingLicenseImg, jdbcType=VARCHAR},
                </if>
                <if test="bean.qualityManagementLicenseImg != null" >
                    quality_management_license_img = #{bean.qualityManagementLicenseImg, jdbcType=VARCHAR},
                </if>
                <if test="bean.organSignType != null" >
                    organ_sign_type = #{bean.organSignType, jdbcType=TINYINT},
                </if>
                <if test="bean.bizModel != null" >
                    biz_model = #{bean.bizModel, jdbcType=TINYINT},
                </if>
    <!--            <if test="bean.headquartersOrganSign != null" >-->
    <!--                headquarters_organ_sign = #{bean.headquartersOrganSign, jdbcType=VARCHAR}-->
    <!--            </if>-->
                <if test="bean.msfxSign != null" >
                    msfx_sign = #{bean.msfxSign},
                </if>
                <if test="medicalMerchantNo != null and medicalMerchantNo != ''" >
                    medical_merchant_no = #{medicalMerchantNo},
                </if>
                <if test="senderAccount != null and senderAccount != ''" >
                    sender_account = #{senderAccount},
                </if>
                <if test="senderExt != null and senderExt != ''" >
                    sender_ext = #{senderExt},
                </if>
            </set>
            where organ_sign = #{bean.organSign,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="queryOrganSignByOrganSign" resultType="java.lang.String">
        select organ_sign
        from saas_drugstore
        where organ_sign=#{organSign, jdbcType=VARCHAR}
        <if test="containSubStore != null and containSubStore == true" >
            or headquarters_organ_sign = #{organSign, jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectDrugstoreBySenderId" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
        select * from saas_drugstore where sender_id = #{senderId, jdbcType=VARCHAR} limit 1
    </select>

    <select id="queryDrugstoreListByOrganSignList"
            resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
        select * from saas_drugstore
        where organ_sign in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getDrugstore" parameterType="string" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
        SELECT
            d.id AS id,
            d.organ_sign AS organSign,
            d.drugstore_name AS drugstoreName,
            d.biz_model as bizModel,
            CASE
            WHEN d.province = '重庆' THEN
                '重庆市'
            ELSE
                d.province
            END AS province,
            d.city AS city,
            d.area AS area
        FROM
            saas_drugstore d
        WHERE
            organ_sign = #{organSign}
    </select>
    <select id="getAccountSetListByMainOrganSign" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from saas_drugstore
        where organ_sign = #{organSign} and main_yn = 1
        union
        select
        <include refid="Base_Column_List" />
        from saas_drugstore
        where main_organ_sign =  #{organSign}
    </select>
    <select id="selectDrugstoreByMainOrganSign" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from saas_drugstore
        where main_organ_sign =  #{organSign}
    </select>

    <select id="queryAllDrugByHeadquarters" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
        select * from saas_drugstore where 1 = 1 and main_yn = 1
        AND headquarters_organ_sign  in
        <foreach collection="headquartersOrganSignList" item="organSign" open="(" close=")" separator=",">
            #{organSign}
        </foreach>
    </select>


    <select id="selectAllDrugStore" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
      select <include refid="Base_Column_List" />
      from saas_drugstore
      <where>
          <if test="organSign != null and organSign != '' ">
              and organ_sign = #{organSign, jdbcType=VARCHAR}
          </if>
          <if test="organSignType != null">
              and organ_sign_type = #{organSignType, jdbcType=TINYINT}
          </if>
          <if test="bizModel != null">
              and biz_model = #{bizModel, jdbcType=TINYINT}
          </if>
      </where>
    </select>

    <select id="queryAuthorizationDrugstoreByOrganSignOrName" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
        select <include refid="Base_Column_List" />
        from saas_drugstore
        <where>
            organ_sign_type = 1 and status = 1
            <if test="organSignOrName != null">
                and (organ_sign like CONCAT("%", #{organSignOrName},"%")
                or drugstore_name like CONCAT("%", #{organSignOrName},"%"))
            </if>
            <if test="oaId != null and oaId != ''">
                and organ_sign in (
                    select organ_sign from zhilu_auth_user_drugstore where yn = 1 and user_id = #{oaId}
                )
            </if>
        </where>
        limit 100
    </select>

    <update id="updateDrugstoreMedicalInsurance">
        update saas_drugstore set medical_insurance_status = #{openStatus}
        where organ_sign = #{organSign, jdbcType=VARCHAR}
    </update>

    <select id="queryVersionPoolDrugstore" resultType="com.xyy.user.module.dto.result.QueryDrugstoreDto">
        select d.drugstore_name as drugstoreName,
               d.organ_sign as organSign,
               p1.version_name as versionName,
               p2.version_name as lastVersionName,
               p3.version_name as actualVersionName,
               ifnull(o1.last_month_order_num, 0) as lastMonthOrderNum,
               d.biz_model as bizModel,
               d.medical_insurance_status as medicalInsuranceStatus,
               d.province,
               d.city,
               d.area,
               vd.pool_id as poolId
        from saas_drugstore d
        left join saas_version_pool_drugstore vd on d.organ_sign = vd.organ_sign
        left join saas_drugstore_version_log l on d.organ_sign = l.organ_sign
        left join saas_version_pool p1 on vd.pool_id = p1.id
        left join saas_version_pool p2 on l.last_pool_id = p2.id
        left join saas_version_pool p3 on l.actual_pool_id = p3.id
        left join (
            select organ_sign, cast(sum(order_num) as signed) as last_month_order_num from saas_bigdata_drugstore_order_num
            where order_time &gt;= #{lastMonthStart}
            and order_time &lt;= #{lastMonthEnd}
            group by organ_sign
        ) o1 on o1.organ_sign = d.organ_sign
        <if test="activeDays != null and activeDays != ''">
            right join(
                select organ_sign, cast(sum(order_num) as signed) as active_days_order_num from saas_bigdata_drugstore_order_num
                where order_time &gt;= date_sub(curdate(), interval #{activeDays} day)
                group by organ_sign
            ) o2 on o2.organ_sign = d.organ_sign
        </if>
        <where>
            <if test="organSign != null and organSign != ''">
                and d.organ_sign like concat('%', #{organSign}, '%')
            </if>
            <if test="drugstoreName != null and drugstoreName != ''">
                and d.drugstore_name like concat('%', #{drugstoreName}, '%')
            </if>
            <if test="bizModel != null">
                and d.biz_model = #{bizModel}
            </if>
            <if test="medicalInsuranceStatus != null">
                and d.medical_insurance_status = #{medicalInsuranceStatus}
            </if>
            <if test="province != null and province != ''">
                and d.province = #{province}
            </if>
            <if test="city != null and city != ''">
                and d.city = #{city}
            </if>
            <if test="area != null and area != ''">
                and d.area = #{area}
            </if>
            <if test="versionName != null and versionName != ''">
                and p1.version_name like concat('%', #{versionName}, '%')
            </if>
            <if test="lastVersionName != null and lastVersionName != ''">
                and p2.version_name like concat('%', #{lastVersionName}, '%')
            </if>
            <if test="actualVersionName != null and actualVersionName != ''">
                and p3.version_name like concat('%', #{actualVersionName}, '%')
            </if>
            <if test="lastMonthOrderNum != null">
                and (o1.last_month_order_num &lt;= #{lastMonthOrderNum} or o1.last_month_order_num is null)
            </if>
        </where>
        order by o1.last_month_order_num desc
    </select>
    <select id="selectNoPackBindOrganSign" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
        select
        <include refid="Base_Column_List" />
        from saas_drugstore a where a.biz_model = #{bizModel} and a.organ_sign_type = #{organSignType}
        and a.status = 1
        and not exists (select * from saas_package_bind_info where a.organ_sign = organ_sign)
    </select>


    <select id="selectDrugstoreAndPackageBindInfoByOrgansign"  parameterType="java.util.List" resultType="com.xyy.user.module.dto.DrugstoreAndPackageBindInfoDto">
        select a.organ_sign organSign ,a.organ_sign_type organSignType ,a.biz_model  bizModel ,b.begin_time beginTime ,b.end_time endTime ,a.status status,b.package_id  packageId from saas_drugstore a
        left outer join saas_package_bind_info b on a.organ_sign = b.organ_sign
        where a.organ_sign in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="selectDrugstoreByUserPhone" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
        select drugstore.* from saas_drugstore as drugstore
        left join saas_user as user on user.id = drugstore.manager_user_id
        where  user.phone = #{enterprise, jdbcType=VARCHAR}
    </select>
    <select id="selectsDrugstoreByDrugstoreName"
            resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
         select * from saas_drugstore where drugstore_name = #{drugstoreName, jdbcType=VARCHAR}
    </select>
    <select id="selectsDrugstoreByBusinessLicenseName"
            resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
        select * from saas_drugstore where business_license_name = #{enterprise, jdbcType=VARCHAR}
    </select>
    <select id="selectDrugstoreByCode" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
        select * from saas_drugstore where device_supervision_code = #{deviceSupervisionCode, jdbcType=VARCHAR} limit 1
    </select>
    <select id="selectDrugstoreByAppId" resultType="com.xyy.user.provider.module.entity.SaasDrugstore">
        select * from saas_drugstore where app_id = #{appId, jdbcType=VARCHAR} limit 1
    </select>
</mapper>
