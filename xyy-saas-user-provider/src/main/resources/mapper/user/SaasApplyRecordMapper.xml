<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.user.provider.module.dao.SaasApplyRecordMapper">
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasApplyRecordPo">

    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="organ_sign" jdbcType="VARCHAR" property="organSign" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="state" jdbcType="TINYINT" property="state" />
    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
     <result column="data" jdbcType="LONGVARCHAR" property="data" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="BaseResultDtoMap" type="com.xyy.user.module.dto.SaasApplyRecordDto">

  </resultMap>


  <sql id="Base_Column_List">
    id, organ_sign, type, state, send_time, remarks, create_time, create_user,data

  </sql>

  
  <insert id="insert" parameterType="com.xyy.user.provider.module.entity.SaasApplyRecordPo">
    insert into saas_apply_record (id, organ_sign, type, 
      state, send_time, remarks, 
      create_time, create_user, data
      )
    values (#{id,jdbcType=BIGINT}, #{organSign,jdbcType=VARCHAR}, #{type,jdbcType=TINYINT}, 
      #{state,jdbcType=TINYINT}, #{sendTime,jdbcType=TIMESTAMP}, #{remarks,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=VARCHAR}, #{data,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.xyy.user.provider.module.entity.SaasApplyRecordPo">
    insert into saas_apply_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="organSign != null">
        organ_sign,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="sendTime != null">
        send_time,
      </if>
      <if test="remarks != null">
        remarks,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="data != null">
        data,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="organSign != null">
        #{organSign,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="state != null">
        #{state,jdbcType=TINYINT},
      </if>
      <if test="sendTime != null">
        #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="data != null">
        #{data,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xyy.user.provider.module.entity.SaasApplyRecordPo">
    update saas_apply_record
    <set>
     
      <if test="organSign != null">
        organ_sign = #{organSign,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=TINYINT},
      </if>
      <if test="sendTime != null">
        send_time = #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remarks != null">
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="data != null">
        data = #{data,jdbcType=LONGVARCHAR}
      </if>
    </set>
where id =  #{id,jdbcType=BIGINT}
  </update>

  <update id="batchUpdateSendState" >
    update saas_apply_record
    <set>
        state = #{state,jdbcType=TINYINT},
        send_time = #{sendTime,jdbcType=TIMESTAMP}
    </set>
    where id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id,jdbcType=BIGINT}
    </foreach>



  </update>

  <select id="selectList" resultMap="BaseResultDtoMap" parameterType="com.xyy.user.module.dto.SaasApplyRecordDto">
    select
    <include refid="Base_Column_List"/>
    from saas_apply_record
    where create_time >=  #{createTimeBegin,jdbcType=TIMESTAMP}
    and  create_time &lt;  #{createTimeEnd,jdbcType=TIMESTAMP}
  </select>


</mapper>