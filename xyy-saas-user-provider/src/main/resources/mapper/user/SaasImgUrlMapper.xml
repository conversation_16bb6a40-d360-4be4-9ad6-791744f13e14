<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.user.provider.module.dao.SaasImgUrlMapper" >
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasImgUrl" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="relation_pref" property="relationPref" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="create_user" property="createUser" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="img_url" property="imgUrl" jdbcType="VARCHAR" />
    <result column="base_version" property="baseVersion" jdbcType="VARCHAR" />
    <result column="organ_sign" property="organSign" jdbcType="VARCHAR" />
    <result column="sort" property="sort" jdbcType="TINYINT" />
    <result column="employee_id" property="employeeId" jdbcType="INTEGER" />
    <result column="employee_health_id" property="employeeHealthId" jdbcType="INTEGER" />
  </resultMap>

  <resultMap id="ResultVoMap" type="com.xyy.user.module.dto.restructure.SaasImgUrlDto" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="img_url" property="imgUrl" jdbcType="VARCHAR" />
    <result column="organ_sign" property="organSign" jdbcType="VARCHAR" />
    <result column="employee_id" property="employeeId" jdbcType="INTEGER" />
    <result column="employee_health_id" property="employeeHealthId" jdbcType="INTEGER" />
  </resultMap>

  <sql id="Base_Column_List">
      id,relation_pref, type, create_user,
      create_time, update_user, update_time,
      img_url, base_version, organ_sign,
      sort,employee_id,employee_health_id
  </sql>


  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from saas_img_url
    where id = #{id,jdbcType=BIGINT}
  </delete>


  <insert id="insert" parameterType="com.xyy.user.provider.module.entity.SaasImgUrl" useGeneratedKeys="true" keyProperty="id" >
    insert into saas_img_url (relation_pref, type, create_user, 
      create_time, update_user, update_time, 
      img_url, base_version, organ_sign, 
      sort,employee_id,employee_health_id)
    values (#{relationPref,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, #{createUser,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{imgUrl,jdbcType=VARCHAR}, #{baseVersion,jdbcType=VARCHAR}, #{organSign,jdbcType=VARCHAR}, 
      #{sort,jdbcType=TINYINT},#{employeeId,jdbcType=INTEGER},#{employeeHealthId,jdbcType=INTEGER})
  </insert>

  <update id="updateByPrimaryKey" parameterType="com.xyy.user.provider.module.entity.SaasImgUrl" >
    update saas_img_url
    set relation_pref = #{relationPref,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      img_url = #{imgUrl,jdbcType=VARCHAR},
      base_version = #{baseVersion,jdbcType=VARCHAR},
      organ_sign = #{organSign,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=TINYINT},
      employee_id = #{employeeId,jdbcType=INTEGER},
      employee_health_id = #{employeeHealthId,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select id, relation_pref, type, create_user, create_time, update_user, update_time, 
    img_url, base_version, organ_sign, sort
    from saas_img_url
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectAll" resultMap="BaseResultMap" >
    select id, relation_pref, type, create_user, create_time, update_user, update_time, 
    img_url, base_version, organ_sign, sort ,employee_id,employee_health_id
    from saas_img_url
  </select>

  <insert id="batchInsert">
    insert into saas_img_url
    (relation_pref, type, create_user, create_time, update_user, update_time,
    img_url, base_version, organ_sign, sort , employee_id,employee_health_id)
    values
    <foreach collection="records" item="record" index="index" separator=",">
      (#{record.relationPref,jdbcType=VARCHAR}, #{record.type,jdbcType=INTEGER}, #{record.createUser,jdbcType=VARCHAR},
      #{record.createTime,jdbcType=TIMESTAMP}, #{record.updateUser,jdbcType=VARCHAR}, #{record.updateTime,jdbcType=TIMESTAMP},
      #{record.imgUrl,jdbcType=VARCHAR}, #{record.baseVersion,jdbcType=VARCHAR}, #{record.organSign,jdbcType=VARCHAR},
      #{record.sort,jdbcType=TINYINT},#{record.employeeId,jdbcType=INTEGER},#{record.employeeHealthId,jdbcType=INTEGER})
    </foreach>
  </insert>

  <delete id="deleteImageUrlByOrganSign">
    delete from saas_img_url
    where organ_sign = #{record.organSign,jdbcType=VARCHAR}
    and type in
    <foreach collection="record.typeList" item="type" open="(" close=")" separator=",">
      #{type,jdbcType=INTEGER}
    </foreach>
  </delete>

  <delete id="delByIds">
    delete from saas_img_url
    where id in
    <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </delete>

  <delete id="deleteImageUrlByEmployeeId">
    delete from saas_img_url
    where employee_id = #{record.employeeId,jdbcType=VARCHAR}
    and type in
    <foreach collection="record.typeList" item="type" open="(" close=")" separator=",">
      #{type,jdbcType=INTEGER}
    </foreach>
  </delete>

  <select id="selectImageUrlByParam" resultMap="ResultVoMap">
    select <include refid="Base_Column_List" />
    from saas_img_url
    where
    1 = 1
    <if test="record.organSign != null and record.organSign != ''">
    and organ_sign = #{record.organSign,jdbcType=VARCHAR}
    </if>
    <if test="record.employeeId != null and record.employeeId != ''">
      and employee_id = #{record.employeeId,jdbcType=INTEGER}
    </if>
    <if test="record.employeeHealthId != null and record.employeeHealthId != ''">
      and employee_health_id = #{record.employeeHealthId,jdbcType=INTEGER}
    </if>
    and type in
    <foreach collection="record.typeList" item="type" open="(" close=")" separator=",">
      #{type,jdbcType=INTEGER}
    </foreach>

    <if test="record.organSigns != null and record.organSigns.size() > 0">
      and organ_sign in
      <foreach collection="record.organSigns" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>

  <update id="updateOrganSignByPrimaryKey" parameterType="com.xyy.user.provider.module.entity.SaasImgUrl" >
    update saas_img_url
    set
      organ_sign = #{organSign,jdbcType=VARCHAR}
    where id in
    <foreach collection="idList" item="id" index="index" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </update>

  <select id="selectImageUrlByOrganSignAndType" resultMap="ResultVoMap">
    select <include refid="Base_Column_List" />
    from saas_img_url
    where
    1 = 1
    and organ_sign = #{organSign,jdbcType=VARCHAR}
    and type = #{type,jdbcType=INTEGER}

  </select>
</mapper>