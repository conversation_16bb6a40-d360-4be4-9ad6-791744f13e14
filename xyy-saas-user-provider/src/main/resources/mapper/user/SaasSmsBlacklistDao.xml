<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.user.provider.module.dao.sms.SaasSmsBlacklistDao">

    <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.sms.SaasSmsBlacklist">
        <!--@Table saas_sms_blacklist-->
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="organSign" column="organ_sign" jdbcType="VARCHAR"/>
        <result property="organSignName" column="organ_sign_name" jdbcType="VARCHAR"/>
        <result property="rootOrganSign" column="root_organ_sign" jdbcType="VARCHAR"/>
        <result property="telephone" column="telephone" jdbcType="VARCHAR"/>
        <result property="memberId" column="member_id" jdbcType="INTEGER"/>
        <result property="createUserId" column="create_user_id" jdbcType="INTEGER"/>
        <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, organ_sign, organ_sign_name, root_organ_sign, telephone, member_id, create_user_id, create_user_name, create_time
    </sql>

    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
        select
          <include refid="Base_Column_List"/>
        from xyy_saas_user.saas_sms_blacklist
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BaseResultMap">
        select
          <include refid="Base_Column_List"/>
        from xyy_saas_user.saas_sms_blacklist
        limit #{offset}, #{limit}
    </select>

    <select id="queryByMemberIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xyy_saas_user.saas_sms_blacklist
        where member_id in(
        <foreach collection="memberIdList" item="memberId" separator=",">
            #{memberId}
        </foreach>
        )
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" parameterType="com.xyy.user.provider.module.entity.sms.SaasSmsBlacklist" resultMap="BaseResultMap">
        select
          <include refid="Base_Column_List"/>
        from xyy_saas_user.saas_sms_blacklist
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="organSign != null and organSign != ''">
                and organ_sign = #{organSign}
            </if>
            <if test="organSignName != null and organSignName != ''">
                and organ_sign_name = #{organSignName}
            </if>
            <if test="rootOrganSign != null and rootOrganSign != ''">
                and root_organ_sign = #{rootOrganSign}
            </if>
            <if test="telephone != null and telephone != ''">
                and telephone = #{telephone}
            </if>
            <if test="memberId != null">
                and member_id = #{memberId}
            </if>
            <if test="createUserId != null">
                and create_user_id = #{createUserId}
            </if>
            <if test="createUserName != null and createUserName != ''">
                and create_user_name = #{createUserName}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
        </where>
    </select>

    <select id="pageSearch" parameterType="com.xyy.user.module.dto.sms.SaasSmsBlacklistParamDto" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from xyy_saas_user.saas_sms_blacklist
        <where>
            <if test="rootOrganSign != null and rootOrganSign != ''">
                and root_organ_sign = #{rootOrganSign}
            </if>
            <if test="organSign != null and organSign != ''">
                and (instr(organ_sign, #{organSign})>0 or instr(organ_sign_name, #{organSign})>0)
            </if>
            <if test="telephone != null and telephone != ''">
                and telephone like concat('%',#{telephone},'%')
            </if>
            <if test="createUserName != null and createUserName != ''">
                and create_user_name like concat('%',#{createUserName},'%')
            </if>
            <if test="startTime != null">
                and create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and create_time &lt;= #{endTime}
            </if>
        </where>
        order by id desc
    </select>

    <select id="getBlacklistTels" parameterType="com.xyy.user.module.dto.sms.SaasSmsBlacklistParamDto" resultType="java.lang.String">
        select telephone
        from xyy_saas_user.saas_sms_blacklist
        <where>
            <if test="organSign != null and organSign != ''">
                and organ_sign = #{organSign}
            </if>
            <if test="rootOrganSign != null and rootOrganSign != ''">
                and root_organ_sign = #{rootOrganSign}
            </if>
            <if test="telephone != null and telephone != ''">
                and telephone like concat('%',#{telephone},'%')
            </if>
            <if test="createUserName != null and createUserName != ''">
                and create_user_name like concat('%',#{createUserName},'%')
            </if>
            <if test="startTime != null">
                and create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and create_time &lt;= #{endTime}
            </if>
        </where>
        order by id desc
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into xyy_saas_user.saas_sms_blacklist(organ_sign, organ_sign_name, root_organ_sign, telephone, member_id, create_user_id, create_user_name, create_time)
        values (#{organSign}, #{organSignName}, #{rootOrganSign}, #{telephone}, #{memberId}, #{createUserId}, #{createUserName}, #{createTime})
    </insert>

    <insert id="batchInsertOrUpdate" keyProperty="id" useGeneratedKeys="true">
        insert into xyy_saas_user.saas_sms_blacklist(organ_sign, organ_sign_name, root_organ_sign, telephone, member_id, create_user_id, create_user_name, create_time)
        values
        <foreach collection="list" item="record" separator=",">
            (#{record.organSign}, #{record.organSignName}, #{record.rootOrganSign}, #{record.telephone}, #{record.memberId}, #{record.createUserId}, #{record.createUserName}, #{record.createTime})
        </foreach>
        ON DUPLICATE KEY UPDATE
        organ_sign = VALUES(organ_sign),
        member_id = VALUES(member_id),
        telephone = VALUES(telephone)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update xyy_saas_user.saas_sms_blacklist
        <set>
            <if test="organSign != null and organSign != ''">
                organ_sign = #{organSign},
            </if>
            <if test="organSignName != null and organSignName != ''">
                organ_sign_name = #{organSignName},
            </if>
            <if test="rootOrganSign != null and rootOrganSign != ''">
                root_organ_sign = #{rootOrganSign},
            </if>
            <if test="telephone != null and telephone != ''">
                telephone = #{telephone},
            </if>
            <if test="memberId != null">
                member_id = #{memberId},
            </if>
            <if test="createUserId != null">
                create_user_id = #{createUserId},
            </if>
            <if test="createUserName != null and createUserName != ''">
                create_user_name = #{createUserName},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from xyy_saas_user.saas_sms_blacklist where id = #{id}
    </delete>

    <delete id="deleteByIdList">
        delete from xyy_saas_user.saas_sms_blacklist
        where id in(
        <foreach collection="idList" item="item" index="index" separator=",">
            #{item}
        </foreach>
        )
    </delete>

    <delete id="deleteByMemberIdAndTelephone">
        delete from xyy_saas_user.saas_sms_blacklist
        where member_id = #{memberId}
        and telephone = #{telephone}
    </delete>

</mapper>