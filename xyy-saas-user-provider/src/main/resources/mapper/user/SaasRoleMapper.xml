<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.user.provider.module.dao.SaasRoleMapper" >
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasRole" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="sort" property="sort" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="organ_sign" property="organSign" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="system_role" property="systemRole" jdbcType="TINYINT" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="yn" property="yn" jdbcType="TINYINT" />
    <result column="biz_model" property="bizModel" jdbcType="TINYINT" />
    <result column="organ_sign_type" property="organSignType" jdbcType="TINYINT" />
    <result column="role_code" property="roleCode" jdbcType="VARCHAR" />
  </resultMap>
  
  <select id="getRoleByIdList" resultMap="BaseResultMap">
    select id, name, sort, status, organ_sign, create_time, system_role, remark, yn,biz_model,organ_sign_type
    from saas_role
    where  
      id in
        <foreach collection="roleIdList" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
    and yn = 0
  </select>

  <insert id="addChainRole" parameterType="com.xyy.user.provider.module.entity.SaasRole" useGeneratedKeys="true" keyProperty="id" >
    insert into saas_role
    (
    name,
    sort,
    status,
    organ_sign,
    biz_model,
    organ_sign_type,
    create_time,
    system_role,
    remark,
    role_code,
    base_version
    )
    values
    (
    #{name, jdbcType=VARCHAR},
    #{sort, jdbcType=INTEGER},
    #{status, jdbcType=TINYINT},
    #{organSign, jdbcType=VARCHAR},
    #{bizModel, jdbcType=TINYINT},
    #{organSignType, jdbcType=TINYINT},
    #{createTime, jdbcType=TIMESTAMP},
    #{systemRole, jdbcType=TINYINT},
    #{remark, jdbcType=VARCHAR},
    #{roleCode, jdbcType=VARCHAR},
    (select IFNULL(role.version, 0) from (select max(base_version) as version from saas_role where organ_sign= #{organSign, jdbcType=VARCHAR}) as role) + 1)
  </insert>

  <update id="updateChainRole" parameterType="com.xyy.user.provider.module.entity.SaasRole" >
    update saas_role
    set
    name = #{name, jdbcType=VARCHAR},
    base_version = (select IFNULL(role.version, 0) from (select max(base_version) as version from saas_role where organ_sign= #{organSign, jdbcType=VARCHAR}) as role) + 1
    where id = #{id, jdbcType=INTEGER} and organ_sign = #{organSign, jdbcType=VARCHAR} and yn = 0
  </update>

  <update id="updateChainOrganRoleCode" parameterType="com.xyy.user.provider.module.entity.SaasRole" >
    update saas_role
    set
    role_code = #{roleCode, jdbcType=VARCHAR}
    where id = #{id, jdbcType=INTEGER} and organ_sign = #{organSign, jdbcType=VARCHAR} and yn = 0
  </update>

  <update id="updateRole" parameterType="com.xyy.user.provider.module.entity.SaasRole" >
    update saas_role
    set
    name = #{role.name, jdbcType=VARCHAR},
    sort = #{role.sort, jdbcType=INTEGER},
    status = #{role.status, jdbcType=TINYINT},
    remark = #{role.remark, jdbcType=VARCHAR},
    base_version = (select IFNULL(role.version, 0) from (select max(base_version) as version from saas_role where organ_sign= #{role.organSign, jdbcType=VARCHAR}) as role) + 1
    where id = #{role.roleId, jdbcType=INTEGER} and organ_sign = #{role.organSign, jdbcType=VARCHAR} and yn = 0
  </update>

  <update id="updateBizModelAndTypeById" parameterType="com.xyy.user.provider.module.entity.SaasRole" >
    update saas_role
    set
    biz_model = #{role.bizModel, jdbcType=VARCHAR},
    <if test="role.organSignType != null">
      organ_sign_type = #{role.organSignType, jdbcType=INTEGER},
    </if>
    base_version = (select IFNULL(role.version, 0) from (select max(base_version) as version from saas_role where organ_sign= #{role.organSign, jdbcType=VARCHAR}) as role) + 1
    where id = #{role.id, jdbcType=INTEGER} and yn = 0
  </update>

  <select id="selectRoleByOrganSign" parameterType="java.lang.String" resultType="com.xyy.user.provider.module.entity.SaasRole">
    select * from saas_role where (organ_sign = #{organSign, jdbcType=VARCHAR} or system_role = 1) and yn = 0 and biz_model= 1  order by sort asc
  </select>

  <select id="selectSystemRole" parameterType="java.lang.String" resultType="com.xyy.user.provider.module.entity.SaasRole">
    select * from saas_role where system_role = 1 and yn = 0 and biz_model= 1  order by sort asc
  </select>

  <select id="selectAllSystemRole"  resultType="com.xyy.user.provider.module.entity.SaasRole">
    select * from saas_role where system_role = 1 and yn = 0
  </select>

  <select id="getSystemRoleByOrganSignType" resultMap="BaseResultMap">
    select * from saas_role where system_role = 1 and yn = 0
    <if test="bizModel != '' and bizModel != 1 and organSignType == 1">
      and biz_model= 1
    </if>
    <if test="bizModel != '' and bizModel != 1 and organSignType == 3">
      and biz_model= #{bizModel, jdbcType=TINYINT}
    </if>
    and organ_sign_type = #{organSignType, jdbcType=TINYINT}
  </select>
  <select id="selectRoleById" resultMap="BaseResultMap">
    select id, name, sort, status, organ_sign, create_time, system_role, remark, yn,biz_model,organ_sign_type,role_code
    from saas_role
    where id = #{id, jdbcType=INTEGER} and yn = 0
  </select>
  
  <select id="getRoleListByIdList" resultMap="BaseResultMap">
    select id, name, sort, status, organ_sign, create_time, system_role, remark, yn,biz_model,organ_sign_type
    from saas_role
    where  
      id in
        <foreach collection="roleIdList" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
    and yn = 0
  </select>


  <select id="getRoleListByIdListNoSystem" resultMap="BaseResultMap">
    select id, name, sort, status, organ_sign, create_time, system_role, remark, yn,biz_model,organ_sign_type
    from saas_role
    where
    id in
    <foreach collection="roleIdList" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
    and yn = 0 and system_role = 0
  </select>

  <update id="removeRoleByIdAndOrganSign">
    update saas_role set
    yn = 1,
    base_version = (select IFNULL(role.version, 0) from (select max(base_version) as version from saas_role where organ_sign= #{organSign, jdbcType=VARCHAR}) as role) + 1
    WHERE id = #{roleId, jdbcType=INTEGER} and yn = 0
  </update>

  <select id="queryRoleByName" resultType="com.xyy.user.provider.module.entity.SaasRole">
    select * from saas_role where name = #{name, jdbcType=VARCHAR} and (organ_sign = #{organSign, jdbcType=VARCHAR} or organ_sign = '0') and yn = 0 and biz_model= 1 order by create_time asc limit 1
  </select>

  <select id="queryRolesByName" resultType="com.xyy.user.provider.module.entity.SaasRole">
    select * from saas_role where name = #{name, jdbcType=VARCHAR} and (organ_sign = #{organSign, jdbcType=VARCHAR} or organ_sign = '0') and yn = 0 and biz_model= 1
  </select>

  <select id="getChainRoleByName" resultType="com.xyy.user.provider.module.entity.SaasRole">
    select * from saas_role where name = #{name, jdbcType=VARCHAR} and (organ_sign = #{organSign, jdbcType=VARCHAR} or organ_sign = '0') and yn = 0
              and biz_model= #{bizModel, jdbcType=TINYINT} and organ_sign_type = #{organSignType, jdbcType=TINYINT}
  </select>


  <select id="selectRolesSyncByBaseVersion" resultType="com.xyy.user.provider.module.entity.SaasRole">
    /*FORCE_MASTER*/
    select * from saas_role
    where organ_sign = #{organSign, jdbcType=VARCHAR} and biz_model= 1 and base_version > #{baseVersion, jdbcType=INTEGER}
    order by base_version
    <if test="counts != null">
      limit 0, #{counts, jdbcType=INTEGER}
    </if>
  </select>
  <select id="getRoleListByPage" resultMap="BaseResultMap" parameterType="com.xyy.user.module.dto.ChainRoleListExtendDto">
    select * from saas_role
    <where>
      1 = 1
      <if test="organSign != null and organSign != ''">
        and (organ_sign = #{organSign, jdbcType=VARCHAR} or system_role = 1)
      </if>
      <if test="bizModel != null and bizModel != 1">
        and (biz_model= #{bizModel, jdbcType=TINYINT} or biz_model = 1)
      </if>
      <if test="bizModel != null and bizModel == 1">
        and biz_model= #{bizModel, jdbcType=TINYINT}
      </if>
      <if test="organSignType != null">
        and organ_sign_type= #{organSignType, jdbcType=TINYINT}
      </if>
      <if test="systemRole != null">
        and system_role= #{systemRole, jdbcType=TINYINT}
      </if>
      <if test="organSigns != null and organSigns.size() > 0">
        and (organ_sign in
        <foreach collection="organSigns" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
        or system_role = 1)
      </if>
    and yn = 0 and status = 1
    </where>
    order by create_time asc
  </select>
  <select id="getRoleByRoleCode" parameterType="java.lang.String" resultType="com.xyy.user.provider.module.entity.SaasRole">
    select * from saas_role where role_code = #{roleCode, jdbcType=VARCHAR} and yn = 0
  </select>
  <select id="selectRoleByIds" resultMap="BaseResultMap">
    select id, name, sort, status, organ_sign, create_time, system_role, remark, yn,biz_model,organ_sign_type,role_code
    from saas_role
    where id in
    <foreach collection="roleIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
    and yn = 0
  </select>
  <select id="getRoleByRoleCodes" resultType="com.xyy.user.provider.module.entity.SaasRole">
    select * from saas_role where role_code in
    <foreach collection="roleCodes" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
    and yn = 0
  </select>
  <select id="selectRolesSyncByParam" resultType="com.xyy.user.provider.module.entity.SaasRole">
    /*FORCE_MASTER*/
    select * from saas_role
    where organ_sign = #{organSign, jdbcType=VARCHAR} and biz_model= #{bizModel, jdbcType=TINYINT}
    and organ_sign_type = #{organSignType, jdbcType=TINYINT} and base_version > #{baseVersion, jdbcType=INTEGER}
    order by base_version
    <if test="counts != null">
      limit 0, #{counts, jdbcType=INTEGER}
    </if>
  </select>

</mapper>
