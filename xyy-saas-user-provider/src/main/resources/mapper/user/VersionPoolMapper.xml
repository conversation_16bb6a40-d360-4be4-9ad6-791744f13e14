<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.user.provider.module.dao.VersionPoolMapper">

    <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.VersionPoolPo">
        <id column="id"  property="id" jdbcType="INTEGER" />
        <result column="pool_name" property="poolName" jdbcType="VARCHAR"/>
        <result column="version_name" property="versionName" jdbcType="VARCHAR"/>
        <result column="version_code" property="versionCode" jdbcType="INTEGER"/>
        <result column="version_info" property="versionInfo" jdbcType="VARCHAR"/>
        <result column="download_url" property="downloadUrl" jdbcType="VARCHAR"/>
        <result column="yn" property="yn" jdbcType="TINYINT"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="platform" property="platform" jdbcType="TINYINT"/>
        </resultMap>


    <sql id="Base_Column_List">
      id,pool_name,version_name,version_code,version_info,download_url,
      yn,status,platform
   </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
      select
        <include refid="Base_Column_List" />
        from saas_version_pool
      where id = #{id,jdbcType=INTEGER} and yn=1 

    </select>

    <select id="selectPoolById" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from saas_version_pool
        where id = #{id,jdbcType=INTEGER} and yn=1 and status=1
    </select>


    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
     delete from saas_version_pool
     where id = #{id,jdbcType=INTEGER}
   </delete>

    <insert id="insert" parameterType="com.xyy.user.provider.module.entity.VersionPoolPo">
        insert into saas_version_pool
        (pool_name,version_name,version_code,version_info,download_url,
         yn,status,platform)
        values(#{poolName,jdbcType=VARCHAR},#{versionName,jdbcType=VARCHAR},
        #{versionCode,jdbcType=VARCHAR},#{versionInfo,jdbcType=VARCHAR},#{downloadUrl,jdbcType=VARCHAR},
        #{yn,jdbcType=TINYINT},#{status,jdbcType=TINYINT},#{platform,jdbcType=TINYINT})
    </insert>

    <select id="getMaxVersionCode" resultType="java.lang.Integer">
      select max(version_code) as maxCode from saas_version_pool
    </select>

    <select id="list" parameterType="com.xyy.user.provider.module.entity.VersionPoolPo"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from saas_version_pool
        where yn=1
        <if test="poolName != null and poolName != ''">
            and pool_name= #{poolName}
        </if>
        <if test="versionName != null and versionName != ''">
            and version_name= #{versionName}
        </if>
        <if test="status != null and status != ''">
            and status= #{status}
        </if>
        <if test="platform != null and platform != ''">
            and platform= #{platform}
        </if>

    </select>

    <update id="updateByPrimaryKeySelective" parameterType="com.xyy.user.provider.module.entity.VersionPoolPo">
        update saas_version_pool
        <set>
         <if test="poolName != null">
            pool_name = #{poolName,jdbcType=VARCHAR},
         </if>
         <if test="versionName != null">
            version_name = #{versionName,jdbcType=VARCHAR},
         </if>
         <if test="versionCode != null">
            version_code = #{versionCode,jdbcType=INTEGER},
         </if>
         <if test="versionInfo != null">
            version_info = #{versionInfo,jdbcType=VARCHAR},
         </if>
         <if test="downloadUrl != null">
            download_url = #{downloadUrl,jdbcType=VARCHAR},
         </if>
         <if test="yn != null">
            yn = #{yn,jdbcType=TINYINT},
         </if>
         <if test="status != null">
            status = #{status,jdbcType=TINYINT},
         </if>
         <if test="platform != null">
            platform = #{platform,jdbcType=TINYINT},
         </if>
        </set>
        where  id = #{id,jdbcType=INTEGER}
     </update>

    <update id="updateByPrimaryKey" parameterType="com.xyy.user.provider.module.entity.VersionPoolPo">
        update saas_version_pool
            set
            pool_name = #{poolName,jdbcType=VARCHAR},
            version_name = #{versionName,jdbcType=VARCHAR},
            version_code = #{versionCode,jdbcType=INTEGER},
            version_info = #{versionInfo,jdbcType=VARCHAR},
            download_url = #{downloadUrl,jdbcType=VARCHAR},
            yn = #{yn,jdbcType=TINYINT},
            status = #{status,jdbcType=TINYINT},
            platform=#{platform,jdbcType=TINYINT}
        where id = #{id,jdbcType=INTEGER}
   </update>


</mapper>