<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.user.provider.module.dao.SaasDrugstoreCrmContractInfoMapper" >
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasDrugstoreCrmContractInfo" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="organ_sign" property="organSign" jdbcType="VARCHAR" />
    <result column="contract_code" property="contractCode" jdbcType="VARCHAR" />
    <result column="standards_rule_version" property="standardsRuleVersion" jdbcType="TINYINT" />
    <result column="contract_type" property="contractType" jdbcType="TINYINT" />
    <result column="contract_start_time" property="contractStartTime" jdbcType="TIMESTAMP" />
    <result column="contract_end_time" property="contractEndTime" jdbcType="TIMESTAMP" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="approval_time" property="approvalTime" jdbcType="TIMESTAMP" />
    <result column="assessment_start_time" property="assessmentStartTime" jdbcType="TIMESTAMP" />
    <result column="term" property="term" jdbcType="VARCHAR" />
    <result column="soft_amount" property="softAmount" jdbcType="VARCHAR" />
  </resultMap>
  
  <sql id="Base_Column_List">
  `id`, `organ_sign`, `contract_code`, `standards_rule_version`, `contract_start_time`, `contract_end_time`, `contract_type`, `create_time`, `update_time`, `approval_time`, `assessment_start_time`, `term`, `soft_amount`
  </sql>

    <sql id="T_Base_Column_List">
  t.id, t.organ_sign, t.contract_code, t.standards_rule_version, t.contract_start_time, t.contract_end_time, t.contract_type, t.create_time, t.update_time, t.approval_time, t.assessment_start_time, t.term, t.soft_amount
  </sql>
  
  <insert id="addCrmContract" parameterType="com.xyy.user.provider.module.entity.SaasDrugstoreCrmContractInfo" useGeneratedKeys="true" keyProperty="id" >
    insert into saas_drugstore_crm_contract_info
    (
    organ_sign,
    contract_code,
    standards_rule_version,
    contract_type,
    contract_start_time,
    contract_end_time,
    create_time,
    approval_time,
    assessment_start_time,
    term,
    soft_amount
    )
    values
    (
    #{crmContract.organSign, jdbcType=VARCHAR},
    #{crmContract.contractCode, jdbcType=VARCHAR},
    #{crmContract.standardsRuleVersion, jdbcType=TINYINT},
    #{crmContract.contractType, jdbcType=TINYINT},
      #{crmContract.contractStartTime, jdbcType=TIMESTAMP},
      #{crmContract.contractEndTime, jdbcType=TIMESTAMP},
      #{crmContract.createTime, jdbcType=TIMESTAMP},
      #{crmContract.approvalTime, jdbcType=TIMESTAMP},
       #{crmContract.assessmentStartTime, jdbcType=TIMESTAMP},
    #{crmContract.term, jdbcType=VARCHAR},
    #{crmContract.softAmount, jdbcType=VARCHAR}
    )
  </insert>

  
  <select id="getCrmContractDetail"  resultMap="BaseResultMap" parameterType="java.util.Map">
    select t.* 
    from saas_drugstore_crm_contract_info  t
    where
    t.organ_sign = #{organSign, jdbcType=VARCHAR}
    <if test="contractCode != null and contractCode != ''">
      and t.contract_code = #{contractCode, jdbcType=VARCHAR}
    </if>
    and t.yn = 0

  </select>

  <select id="getCrmContractDetailByCondition"  resultMap="BaseResultMap" parameterType="com.xyy.user.provider.module.entity.SaasDrugstoreCrmContractInfo">
    select t.*
    from saas_drugstore_crm_contract_info  t
    <where>
      <if test="organSign != null and organSign != ''">
        t.organ_sign = #{organSign, jdbcType=VARCHAR}
      </if>
      <if test="contractCode != null and contractCode != ''">
        and t.contract_code = #{contractCode, jdbcType=VARCHAR}
      </if>
      <if test="standardsRuleVersion != null and standardsRuleVersion != ''">
        and t.standards_rule_version = #{standardsRuleVersion, jdbcType=TINYINT}
      </if>
      <if test="organSignList != null and organSignList.size() > 0">
        and t.organ_sign in
        <foreach collection="organSignList" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="standardsRuleVersionStart != null">
        and t.standards_rule_version >= #{standardsRuleVersionStart, jdbcType=TINYINT}
      </if>
      and t.yn = 0
    </where>
  </select>

  <update id="updateCrmContract">
    update saas_drugstore_crm_contract_info
    <set>
      standards_rule_version = #{crmContract.standardsRuleVersion, jdbcType=TINYINT},
      contract_type = #{crmContract.contractType, jdbcType=TINYINT},
      contract_start_time = #{crmContract.contractStartTime, jdbcType=TIMESTAMP},
      contract_end_time = #{crmContract.contractEndTime, jdbcType=TIMESTAMP},
      approval_time = #{crmContract.approvalTime, jdbcType=TIMESTAMP},
      assessment_start_time = #{crmContract.assessmentStartTime, jdbcType=TIMESTAMP},
      <if test="crmContract.term != null">
        term = #{crmContract.term},
      </if>
      <if test="crmContract.softAmount != null">
        soft_amount = #{crmContract.softAmount},
      </if>
    </set>
    WHERE id = #{crmContract.id}
  </update>

  <update id="update">
    update saas_drugstore_crm_contract_info
    <set>
      standards_rule_version = #{crmContract.standardsRuleVersion, jdbcType=TINYINT},
      contract_type = #{crmContract.contractType, jdbcType=TINYINT},
      contract_start_time = #{crmContract.contractStartTime, jdbcType=TIMESTAMP},
      contract_end_time = #{crmContract.contractEndTime, jdbcType=TIMESTAMP},
      approval_time = #{crmContract.approvalTime, jdbcType=TIMESTAMP},
      assessment_start_time = #{crmContract.assessmentStartTime, jdbcType=TIMESTAMP},
      <if test="crmContract.term != null">
        term = #{crmContract.term},
      </if>
      <if test="crmContract.softAmount != null">
        soft_amount = #{crmContract.softAmount},
      </if>
      <if test="crmContract.contractCode != null">
        contract_code = #{crmContract.contractCode},
      </if>
    </set>
    WHERE id = #{crmContract.id}
  </update>

  <update id="updateYnDisabled">
    update saas_drugstore_crm_contract_info
    set yn = 1
    <where>
      organ_sign = #{organSign, jdbcType=VARCHAR}
      <if test="contractCode != null and contractCode!=''">
        and contract_code = #{contractCode, jdbcType=VARCHAR}
      </if>
    </where>
  </update>

  <select id="selectDrugstoreCrmContractByOrganSigns" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select * from saas_drugstore_crm_contract_info
    where yn = 0 and organ_sign in
    <foreach collection="list" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="getCrmContractByOrganSignAndRuleVersion" resultType="com.xyy.user.provider.module.entity.SaasDrugstoreCrmContractInfo">
    SELECT <include refid="Base_Column_List"/>
    FROM saas_drugstore_crm_contract_info
    WHERE organ_sign = #{organSign}
    AND standards_rule_version = #{standardsRuleVersion}
    AND yn = 0
   </select>

   <select id="getNeedSendCouponCrmContract" resultType="com.xyy.user.provider.module.entity.SaasDrugstoreCrmContractInfo">
    SELECT <include refid="T_Base_Column_List"/>
    FROM saas_drugstore_crm_contract_info t
    LEFT JOIN saas_drugstore_coupons_total r
    ON t.organ_sign = r.organ_sign AND t.contract_code = r.contract_code
    WHERE t.standards_rule_version = #{standardsRuleVersion}
    AND (ISNULL(r.total_coupons_number) OR r.residue_coupons_number > 0)
    AND t.yn = 0
     <if test="id != null">
        AND t.id > #{id,jdbcType=BIGINT}
     </if>
     <if test="organSign != null and organSign != ''">
        AND t.organ_sign = #{organSign,jdbcType=VARCHAR}
     </if>
     ORDER BY t.id ASC LIMIT 0,#{pageSize,jdbcType=INTEGER}
   </select>

</mapper>