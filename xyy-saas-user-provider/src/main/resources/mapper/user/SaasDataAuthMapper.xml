<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.user.provider.module.dao.SaasDataAuthMapper" >
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasDataAuth" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="organ_sign" property="organSign" jdbcType="VARCHAR" />
    <result column="role_id" property="roleId" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="yn" property="yn" jdbcType="TINYINT" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from saas_data_auth
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.xyy.user.provider.module.entity.SaasDataAuth" useGeneratedKeys="true" keyProperty="id" >
    insert into saas_data_auth (organ_sign, role_id, create_time, 
      update_time, yn)
    values (#{organSign,jdbcType=VARCHAR}, #{roleId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{yn,jdbcType=TINYINT})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.xyy.user.provider.module.entity.SaasDataAuth" >
    update saas_data_auth
    set organ_sign = #{organSign,jdbcType=VARCHAR},
      role_id = #{roleId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      yn = #{yn,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select id, organ_sign, role_id, create_time, update_time, yn
    from saas_data_auth
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectAll" resultMap="BaseResultMap" >
    select id, organ_sign, role_id, create_time, update_time, yn
    from saas_data_auth
  </select>
  <update id="updateYnByOrganSign">
    update saas_data_auth
    set yn = #{record.newYn,jdbcType=TINYINT},
    update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    where organ_sign = #{record.organSign,jdbcType=VARCHAR}
    and role_id = #{record.roleId,jdbcType=INTEGER}
    and yn = #{record.oldYn,jdbcType=TINYINT}
  </update>
  <select id="selectDataAuth"  resultMap="BaseResultMap">
     select id, organ_sign, role_id, create_time, update_time, yn
     from saas_data_auth
    where organ_sign = #{record.organSign,jdbcType=VARCHAR}
    and role_id = #{record.roleId,jdbcType=INTEGER}
    and yn = #{record.yn,jdbcType=TINYINT}
  </select>

  <select id="selectDataAuthList"  resultMap="BaseResultMap">
     select id, organ_sign, role_id, create_time, update_time, yn
     from saas_data_auth
    where organ_sign = #{record.organSign,jdbcType=VARCHAR}
    and role_id in
    <foreach collection="record.roleIds" item="item" open="(" close=")" separator=",">
      #{item,jdbcType=INTEGER}
    </foreach>
    and yn = #{record.yn,jdbcType=TINYINT}
  </select>
  <insert id="batchInsertDataAuth">
    insert into saas_data_auth
    (organ_sign, role_id, create_time,
    update_time, yn)
    values
    <foreach collection="records" item="item" index="index" separator=",">
      (#{item.organSign,jdbcType=VARCHAR}, #{item.roleId,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP},
      #{item.updateTime,jdbcType=TIMESTAMP}, #{item.yn,jdbcType=TINYINT})
    </foreach>
  </insert>

  <delete id="deleteByIds">
    delete
    from saas_data_auth
    where id in
    <foreach collection="ids" item="item" open="(" close=")" separator=",">
      #{item,jdbcType=INTEGER}
    </foreach>
  </delete>
  <delete id="deleteByOrganSignAndRoleId">
    delete
    from saas_data_auth
    where organ_sign = #{organSign,jdbcType=VARCHAR}
    and role_id =  #{roleId,jdbcType=INTEGER}
  </delete>
</mapper>