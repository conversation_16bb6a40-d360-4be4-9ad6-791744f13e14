<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.user.provider.module.dao.SaasStandardVersionKpiSubMapper" >
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasStandardVersionKpiSubPo" >
    <id column="id" property="id" jdbcType="BIGINT" />

    <result column="standard_version_kpi_id" property="standardVersionKpiId" jdbcType="BIGINT" />
    <result column="version_id" property="versionId" jdbcType="BIGINT" />
    <result column="kpi_item_code" property="kpiItemCode" jdbcType="VARCHAR" />
    <result column="compare_sym" property="compareSym" jdbcType="INTEGER" />
    <result column="compare_val" property="compareVal" jdbcType="DECIMAL" />
    <result column="unitName" property="unitName" jdbcType="VARCHAR" />
    <result column="kpiItemName" property="kpiItemName" jdbcType="VARCHAR" />

    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="yn" property="yn" jdbcType="TINYINT" />
  </resultMap>

  <sql id="Base_Column_List">
    id, standard_version_kpi_id, version_id, kpi_item_code, compare_sym, compare_val,
    create_time, update_time, yn
  </sql>

  <insert id="insert" parameterType="com.xyy.user.provider.module.entity.SaasStandardVersionKpiSubPo" useGeneratedKeys="true" keyProperty="id" >
    insert into saas_standard_version_kpi_sub (standard_version_kpi_id, version_id, kpi_item_code, compare_sym, compare_val,
        create_time, update_time, yn)
    values (#{standardVersionKpiId,jdbcType=BIGINT}, #{versionId,jdbcType=BIGINT}, #{kpiItemCode,jdbcType=VARCHAR}, #{compareSym,jdbcType=INTEGER}, #{compareVal, jdbcType=DECIMAL},
    #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 1)
  </insert>

  <update id="updateById" parameterType="com.xyy.user.provider.module.entity.SaasStandardVersionKpiSubPo">
    update saas_standard_version_kpi_sub
    <set>
      <if test="standardVersionKpiId != null">
        standard_version_kpi_id = #{standardVersionKpiId,jdbcType=BIGINT},
      </if>
      <if test="versionId != null">
        version_id = #{versionId,jdbcType=BIGINT},
      </if>
      <if test="kpiItemCode != null">
        kpi_item_code = #{kpiItemCode,jdbcType=VARCHAR},
      </if>
      <if test="compareSym != null">
        compare_sym = #{compareSym,jdbcType=INTEGER},
      </if>
      <if test="compareVal != null">
        compare_val = #{compareVal,jdbcType=DECIMAL},
      </if>

      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="yn != null">
        yn = #{yn,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="upForDeleteByVersionId" parameterType="com.xyy.user.provider.module.entity.SaasStandardVersionKpiSubPo">
    update saas_standard_version_kpi_sub set yn = 0
    where version_id = #{versionId,jdbcType=BIGINT}
  </update>

  <select id="selectListForView" parameterType="com.xyy.user.provider.module.entity.SaasStandardVersionKpiSubPo" resultMap="BaseResultMap">
    select
    sub.id, sub.standard_version_kpi_id, sub.version_id, sub.kpi_item_code, sub.compare_sym, sub.compare_val, item.unit as unitName, item.name as kpiItemName
    from saas_standard_version_kpi_sub sub left join saas_kpi_item item on sub.kpi_item_code = item.code
    <where>
      sub.yn = 1
      <if test="standardVersionKpiId != null">
        and standard_version_kpi_id = #{standardVersionKpiId}
      </if>
    </where>
  </select>

  <select id="selectById" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List"/>
    from saas_standard_version_kpi_sub
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectList" parameterType="com.xyy.user.provider.module.entity.SaasStandardVersionKpiSubPo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from saas_standard_version_kpi_sub
    <where>
      <if test="standardVersionKpiId != null">
        and standard_version_kpi_id = #{standardVersionKpiId}
      </if>
      <if test="versionId != null">
        and version_id = #{versionId}
      </if>
      <if test="kpiItemCode != null">
        and kpi_item_code = #{kpiItemCode}
      </if>
      <if test="compareSym != null">
        and compare_sym = #{compareSym}
      </if>
      <if test="compareVal != null">
        and compare_val = #{compareVal}
      </if>
      <if test="updateTime != null">
        and update_time = #{updateTime}
      </if>
      <if test="yn != null">
        and yn = #{yn}
      </if>
    </where>
  </select>

</mapper>