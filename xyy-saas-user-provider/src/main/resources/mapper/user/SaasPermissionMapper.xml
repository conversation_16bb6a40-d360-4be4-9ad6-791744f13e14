<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.user.provider.module.dao.SaasPermissionMapper" >
  <resultMap id="BaseResultMap" type="com.xyy.user.provider.module.entity.SaasPermission" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="parent_id" property="parentId" jdbcType="INTEGER" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="url" property="url" jdbcType="VARCHAR" />
    <result column="dll_name" property="dllName" jdbcType="VARCHAR" />
    <result column="view_name" property="viewName" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="sort" property="sort" jdbcType="INTEGER" />
    <result column="color" property="color" jdbcType="VARCHAR" />
    <result column="icon" property="icon" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="yn" property="yn" jdbcType="TINYINT" />
    <result column="tiers" property="tiers" jdbcType="INTEGER" />
    <result column="gray_level" property="grayLevel" jdbcType="INTEGER" />
    <result column="menu_type" property="menuType" jdbcType="TINYINT" />
    <result column="biz_model" property="bizModel" jdbcType="TINYINT" />
    <result column="organ_sign_type" property="organSignType" jdbcType="TINYINT" />
    <result column="terminal_type" property="terminalType" jdbcType="TINYINT" />
    <result column="business_type" property="businessType" jdbcType="TINYINT"/>

  </resultMap>
  <insert id="insert" parameterType="com.xyy.user.provider.module.entity.SaasPermission" useGeneratedKeys="true" keyProperty="id" >
    insert into saas_permission (parent_id, name, url, 
      dll_name, view_name, create_time, 
      sort, color, icon, 
      status, remark, yn, 
      tiers,menu_type,biz_model,organ_sign_type,terminal_type,business_type)
    values (#{parentId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR}, 
      #{dllName,jdbcType=VARCHAR}, #{viewName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{sort,jdbcType=INTEGER}, #{color,jdbcType=VARCHAR}, #{icon,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, #{yn,jdbcType=TINYINT}, 
      #{tiers,jdbcType=INTEGER}, #{menuType,jdbcType=TINYINT}, #{bizModel,jdbcType=TINYINT}, #{organSignType,jdbcType=TINYINT}, #{terminalType,jdbcType=TINYINT},#{businessType,jdbcType=TINYINT})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.xyy.user.provider.module.entity.SaasPermission" >
    update saas_permission
    set parent_id = #{parentId,jdbcType=INTEGER},
      name = #{name,jdbcType=VARCHAR},
      url = #{url,jdbcType=VARCHAR},
      dll_name = #{dllName,jdbcType=VARCHAR},
      view_name = #{viewName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      sort = #{sort,jdbcType=INTEGER},
      color = #{color,jdbcType=VARCHAR},
      icon = #{icon,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      yn = #{yn,jdbcType=TINYINT},
      tiers = #{tiers,jdbcType=INTEGER},
      terminal_type = #{terminalType,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select id, parent_id, name, url, dll_name, view_name, create_time, sort, color, icon, 
    status, remark, yn, tiers,menu_type,biz_model,organ_sign_type,terminal_type
    from saas_permission
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectAll" resultMap="BaseResultMap" >
    select id, parent_id, name, url, dll_name, view_name, create_time, sort, color, icon, 
    status, remark, yn, tiers,menu_type,biz_model,organ_sign_type,terminal_type
    from saas_permission where menu_type = 0
  </select>


  <select id="findPermissionByAll" resultMap="BaseResultMap">
        SELECT * FROM saas_permission where yn=0 and menu_type = 0 ORDER BY parent_id,sort
    </select>

  <select id="findById" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT * FROM saas_permission where id=#{id} and yn = 0
    </select>

  <update id="updatePermission" parameterType="com.xyy.user.provider.module.entity.SaasPermission" >
    update saas_permission
    <set >
      <if test="parentId != null" >
        parent_id = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="name != null" >
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="dllName != null" >
        dll_name = #{dllName,jdbcType=VARCHAR},
      </if>
      <if test="url != null" >
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="tiers != null" >
        tiers = #{tiers,jdbcType=INTEGER},
      </if>
      <if test="viewName != null" >
        view_name = #{viewName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sort != null" >
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="color != null" >
        color = #{color,jdbcType=VARCHAR},
      </if>
      <if test="icon != null" >
        icon = #{icon,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="yn != null" >
        yn = #{yn,jdbcType=TINYINT},
      </if>
      <if test="terminalType != null" >
        terminal_type = #{terminalType,jdbcType=TINYINT},
      </if>
      <if test="businessType != null" >
        business_type = #{businessType,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="findByName" parameterType="com.xyy.user.provider.module.entity.SaasPermission" resultMap="BaseResultMap">
        SELECT * FROM saas_permission where yn=0 and name=#{name} and menu_type = 0
    </select>

<!--  select DISTINCT a.* FROM saas_permission a where a.yn=0 and a.status=1 and a.menu_type = 0-->
<!--  and (a.view_name != 'app权限' or a.view_name is null) and a.terminal_type = 1-->
<!--  and a.biz_model=#{bizModel,jdbcType=TINYINT} and a.organ_sign_type= #{organSignType,jdbcType=TINYINT}-->
<!--  and exists-->
<!--  (select c.premission_id from saas_role b inner join saas_role_premission c on b.id = c.role_id-->
<!--  where a.id = c.premission_id and  b.status = 1  and b.yn = 0 and c.yn = 0  and b.id in-->
<!--  (select DISTINCT d.role_id from saas_employee_role d inner join saas_employee e on d.employee_id = e.id where d.employee_id = #{employId} and d.yn = 0))-->
<!--  ORDER BY a.parent_id, a.sort-->
    <select id="findPermissionByIds"  resultMap="BaseResultMap">
        select  * FROM saas_permission  where
        id in
        <foreach collection="permissionIdList" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
        and yn=0 and status=1 and menu_type = 0
        and (view_name != 'app权限' or view_name is null) and terminal_type = 1
        and biz_model=#{bizModel,jdbcType=TINYINT} and organ_sign_type= #{organSignType,jdbcType=TINYINT}
         ORDER BY parent_id, sort
    </select>

<!--  select DISTINCT a.* FROM saas_permission a where a.yn=0 and a.status=1 and a.menu_type = 0
        and a.view_name = 'app权限' and a.biz_model=#{bizModel,jdbcType=TINYINT}
        and a.organ_sign_type= #{organSignType,jdbcType=TINYINT}
        and exists
        (select c.premission_id from saas_role b inner join saas_role_premission c on b.id = c.role_id
        where  a.id = c.premission_id and b.status = 1  and b.yn = 0 and c.yn = 0  and b.id in
        (select DISTINCT d.role_id from saas_employee_role d inner join saas_employee e on d.employee_id = e.id where d.employee_id = #{employId} and d.yn = 0))
        ORDER BY a.parent_id, a.sort -->
  <select id="findAppPermissionByEmployId"  resultMap="BaseResultMap">
      select  * FROM saas_permission  where
      id in
      <foreach collection="permissionIdList" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
      and yn=0 and status=1 and menu_type = 0 and view_name = 'app权限'
      and biz_model=#{bizModel,jdbcType=TINYINT} and organ_sign_type= #{organSignType,jdbcType=TINYINT}
      ORDER BY parent_id, sort
    </select>

  <select id="getPermissionListByCondition" resultMap="BaseResultMap" parameterType="com.xyy.user.provider.module.entity.SaasPermission" >
    SELECT * FROM saas_permission
    <where>
      <if test="parentId != null" >
        parent_id = #{parentId,jdbcType=INTEGER}
      </if>
      <if test="menuType != null" >
        and menu_type = #{menuType,jdbcType=INTEGER}
      </if>
      <if test="bizModel != null" >
        and biz_model = #{bizModel,jdbcType=TINYINT}
      </if>
      <if test="organSignType != null" >
        and organ_sign_type = #{organSignType,jdbcType=TINYINT}
      </if>
      <if test="terminalType != null" >
        and terminal_type = #{terminalType,jdbcType=TINYINT}
      </if>
      <if test="status != null" >
        and status = #{status,jdbcType=TINYINT}
      </if>
      <if test="terminalType != null" >
        and terminal_type = #{terminalType,jdbcType=TINYINT}
      </if>
      <if test="businessType != null">
        and business_type = #{businessType,jdbcType=TINYINT}
      </if>
    </where>
    and yn=0 ORDER BY parent_id,sort
  </select>

  <select id="getEnabledPermissionListByCondition" resultMap="BaseResultMap" parameterType="com.xyy.user.provider.module.entity.SaasPermission" >
    SELECT <include refid="detailRef"/> FROM saas_permission
    <where>
      <if test="parentId != null" >
        parent_id = #{parentId,jdbcType=INTEGER}
      </if>
      <if test="menuType != null" >
        and menu_type = #{menuType,jdbcType=INTEGER}
      </if>
      <if test="bizModel != null" >
        and biz_model = #{bizModel,jdbcType=TINYINT}
      </if>
      <if test="organSignType != null" >
        and organ_sign_type = #{organSignType,jdbcType=TINYINT}
      </if>
      <if test="terminalType != null" >
        and terminal_type = #{terminalType,jdbcType=TINYINT}
      </if>
      <if test="businessType != null">
        and business_type = #{businessType,jdbcType=TINYINT}
      </if>
    </where>
    and yn=0 and status=1  ORDER BY parent_id,sort
  </select>
  
  <sql id="detailRef">
    id, parent_id, name, url, dll_name, view_name,  sort,  icon,menu_type,biz_model,organ_sign_type,terminal_type
  </sql>
  <select id="getPermissionList" resultType="com.xyy.user.provider.module.entity.SaasPermission">
      SELECT <include refid="detailRef"/>  FROM saas_permission
      where  yn=0 and status=1 and
        id in
        <foreach collection="permissionIdList" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
        ORDER BY parent_id, sort
    </select>
  <select id="getPermissionListByIds" resultType="com.xyy.user.provider.module.entity.SaasPermission">
    SELECT <include refid="detailRef"/>  FROM saas_permission
    where  yn=0 and status=1 and
    id in
    <foreach collection="permissionIdList" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

<!-- select DISTINCT a.* FROM saas_permission a where a.yn=0 and a.status=1 and a.menu_type = 0
        and a.view_name  like CONCAT('%app权限%') and a.terminal_type = #{terminalType,jdbcType=TINYINT}
        and a.biz_model=#{bizModel,jdbcType=TINYINT} and a.organ_sign_type= #{organSignType,jdbcType=TINYINT}
        and exists
        (select c.premission_id from saas_role b inner join saas_role_premission c on b.id = c.role_id
        where  a.id = c.premission_id and b.status = 1  and b.yn = 0 and c.yn = 0  and b.id in
        (select DISTINCT d.role_id from saas_employee_role d inner join saas_employee e on d.employee_id = e.id where d.employee_id = #{employeeId} and d.yn = 0))
        ORDER BY a.parent_id, a.sort -->

  <select id="getPermissionByEmployId" resultType="com.xyy.user.provider.module.entity.SaasPermission">
    select  * FROM saas_permission  where
    id in
    <foreach collection="permissionIdList" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
    and yn=0 and status=1 and menu_type = 0 and view_name like CONCAT('%app权限%')
    and terminal_type = #{terminalType,jdbcType=TINYINT}
    and biz_model=#{bizModel,jdbcType=TINYINT} and organ_sign_type= #{organSignType,jdbcType=TINYINT}
    ORDER BY parent_id, sort


  </select>
  <select id="getPermissionListByParam" resultMap="BaseResultMap">
    SELECT  * FROM saas_permission
    where 1 = 1
    <if test="bizModel != null" >
      and biz_model = #{bizModel,jdbcType=TINYINT}
    </if>
    <if test="organSignType != null" >
      and organ_sign_type = #{organSignType,jdbcType=TINYINT}
    </if>
    <if test="terminalType != null" >
      and terminal_type = #{terminalType,jdbcType=TINYINT}
    </if>
    <if test="yn != null" >
      and yn = #{yn,jdbcType=TINYINT}
    </if>
    <if test="viewName != null" >
      and view_name = #{viewName,jdbcType=VARCHAR}
    </if>
    <if test="businessType != null">
      and business_type = #{businessType,jdbcType=TINYINT}
    </if>
    <if test="names != null and names.size() > 0">
    and `name` in
      <foreach collection="names" item="name" open="(" close=")" separator=",">
        #{name,jdbcType=VARCHAR}
      </foreach>
    </if>
  </select>


  <select id="selectByPrimaryParentId" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select id, parent_id, name, url, dll_name, view_name, create_time, sort, color, icon,
    status, remark, yn, tiers,menu_type,biz_model,organ_sign_type,terminal_type
    from saas_permission
    where parent_id = #{parentId,jdbcType=INTEGER}
  </select>

</mapper>