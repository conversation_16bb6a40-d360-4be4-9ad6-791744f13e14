<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.common.provider.module.dao.SaasMessageNoticeDrugeMapper">

    <resultMap type="com.xyy.common.provider.module.entity.SaasMessageNoticeDrugePo" id="SaasMessageNoticeDrugeMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="messageNoticeId" column="message_notice_id" jdbcType="INTEGER"/>
        <result property="organSign" column="organ_sign" jdbcType="VARCHAR"/>
        <result property="drugstoreName" column="drugstore_name" jdbcType="VARCHAR"/>
        <result property="managerName" column="manager_name" jdbcType="VARCHAR"/>
        <result property="province" column="province" jdbcType="VARCHAR"/>
        <result property="city" column="city" jdbcType="VARCHAR"/>
        <result property="area" column="area" jdbcType="VARCHAR"/>
        <result property="address" column="address" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="SaasMessageNoticeDrugeMap">
        select
          id, message_notice_id, organ_sign, drugstore_name, manager_name, province, city, area, address, create_time, update_time
        from saas_message_notice_druge
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAll" resultMap="SaasMessageNoticeDrugeMap">
        select
          id, message_notice_id, organ_sign, drugstore_name, manager_name, province, city, area, address, create_time, update_time
        from saas_message_notice_druge
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="messageNoticeId != null">
                and message_notice_id = #{messageNoticeId}
            </if>
            <if test="organSign != null and organSign != ''">
                and organ_sign = #{organSign}
            </if>
            <if test="drugstoreName != null and drugstoreName != ''">
                and drugstore_name = #{drugstoreName}
            </if>
            <if test="managerName != null and managerName != ''">
                and manager_name = #{managerName}
            </if>
            <if test="province != null and province != ''">
                and province = #{province}
            </if>
            <if test="city != null and city != ''">
                and city = #{city}
            </if>
            <if test="area != null and area != ''">
                and area = #{area}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from saas_message_notice_druge
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="messageNoticeId != null">
                and message_notice_id = #{messageNoticeId}
            </if>
            <if test="organSign != null and organSign != ''">
                and organ_sign = #{organSign}
            </if>
            <if test="drugstoreName != null and drugstoreName != ''">
                and drugstore_name = #{drugstoreName}
            </if>
            <if test="managerName != null and managerName != ''">
                and manager_name = #{managerName}
            </if>
            <if test="province != null and province != ''">
                and province = #{province}
            </if>
            <if test="city != null and city != ''">
                and city = #{city}
            </if>
            <if test="area != null and area != ''">
                and area = #{area}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into saas_message_notice_druge(message_notice_id, organ_sign, drugstore_name, manager_name, province, city, area, address, create_time, update_time)
        values (#{messageNoticeId}, #{organSign}, #{drugstoreName}, #{managerName}, #{province}, #{city}, #{area}, #{address}, #{createTime}, #{updateTime})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into saas_message_notice_druge(message_notice_id, organ_sign, drugstore_name, manager_name, province, city, area, address, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.messageNoticeId}, #{entity.organSign}, #{entity.drugstoreName}, #{entity.managerName}, #{entity.province}, #{entity.city}, #{entity.area}, #{entity.address}, #{entity.createTime}, #{entity.updateTime})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into saas_message_notice_druge(message_notice_id, organ_sign, drugstore_name, manager_name, province, city, area, address, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.messageNoticeId}, #{entity.organSign}, #{entity.drugstoreName}, #{entity.managerName}, #{entity.province}, #{entity.city}, #{entity.area}, #{entity.address}, #{entity.createTime}, #{entity.updateTime})
        </foreach>
        on duplicate key update
        message_notice_id = values(message_notice_id),
        organ_sign = values(organ_sign),
        drugstore_name = values(drugstore_name),
        manager_name = values(manager_name),
        province = values(province),
        city = values(city),
        area = values(area),
        address = values(address),
        create_time = values(create_time),
        update_time = values(update_time)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update saas_message_notice_druge
        <set>
            <if test="messageNoticeId != null">
                message_notice_id = #{messageNoticeId},
            </if>
            <if test="organSign != null and organSign != ''">
                organ_sign = #{organSign},
            </if>
            <if test="drugstoreName != null and drugstoreName != ''">
                drugstore_name = #{drugstoreName},
            </if>
            <if test="managerName != null and managerName != ''">
                manager_name = #{managerName},
            </if>
            <if test="province != null and province != ''">
                province = #{province},
            </if>
            <if test="city != null and city != ''">
                city = #{city},
            </if>
            <if test="area != null and area != ''">
                area = #{area},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from saas_message_notice_druge where id = #{id}
    </delete>

    <delete id="realDelByMessageNoticeId">
        delete from saas_message_notice_druge
        where message_notice_id = #{messageNoticeId}
    </delete>

</mapper>

