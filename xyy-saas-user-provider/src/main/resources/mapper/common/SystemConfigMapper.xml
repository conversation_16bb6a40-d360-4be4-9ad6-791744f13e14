<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.common.provider.module.dao.SystemConfigMapper" >

  <insert id="insert" parameterType="com.xyy.common.provider.module.entity.SystemConfigPo" >
    insert into saas_system_config
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="prescriptionYn != null" >
        prescription_yn,
      </if>
      <if test="updateOrderDiscountYn != null" >
        update_order_discount_yn,
      </if>
      <if test="updateProductDiscountYn != null" >
        update_product_discount_yn,
      </if>
      <if test="productDiscountStart != null" >
        product_discount_start,
      </if>
      <if test="productDiscountEnd != null" >
        product_discount_end,
      </if>
      <if test="updateProductPrice != null" >
        update_product_price,
      </if>
      <if test="salesBelowCost != null" >
        sales_below_cost,
      </if>
      <if test="idCardYn != null" >
        id_card_yn,
      </if>
      <if test="savePreSaleYn != null" >
        save_pre_sale_yn,
      </if>
      <if test="oneSetpYn != null" >
        one_setp_yn,
      </if>
      <if test="batchNoQueueYn != null" >
        batch_no_queue_yn,
      </if>
      <if test="flaxNum != null" >
        flax_num,
      </if>
      <if test="purchaseName != null" >
        purchase_name,
      </if>
      <if test="examinerName != null" >
        examiner_name,
      </if>
      <if test="chineseMedicineExaminerId != null" >
        chinese_medicine_examiner_id,
      </if>
      <if test="posShowPurpriceYn != null" >
        pos_show_purprice_yn,
      </if>
      <if test="approveProductYn != null" >
        approve_product_yn,
      </if>
      <if test="approveProviderYn != null" >
        approve_provider_yn,
      </if>
      <if test="approveProductQuickYn != null" >
        approve_product_quick_yn,
      </if>
      <if test="approveProviderQuickYn != null" >
        approve_provider_quick_yn,
      </if>
      <if test="approvePrescriptionYn != null" >
        approve_prescription_yn,
      </if>
      <if test="enterpriseOwners != null">
        enterprise_owners,
      </if>
      <if test="enterpriseOwnersEmployeeId != null">
        enterprise_owners_employee_id,
      </if>
      <if test="qualityOwners != null">
        quality_owners,
      </if>
      <if test="qualityOwnersEmployeeId != null">
        quality_owners_employee_id,
      </if>
      <if test="licensedPharmacist != null">
        licensed_pharmacist,
      </if>
      <if test="saleAmountYn != null" >
        sale_amount_yn,
      </if>
      <if test="smallChange != null" >
        small_change,
      </if>
      <if test="smallChangeOpen != null" >
        small_change_open,
      </if>
      <if test="smallChangeClose != null" >
        small_change_close,
      </if>
      <if test="zeroInventoryDisplay != null" >
        zero_inventory_display,
      </if>
      <if test="cashChange != null" >
        cash_change,
      </if>
      <if test="memberReminder != null" >
        member_reminder,
      </if>
      <if test="shortageRegistration != null" >
        shortage_registration,
      </if>
      <if test="fullStaffDownload != null" >
        full_staff_download,
      </if>
      <if test="checkPrice != null" >
        check_price,
      </if>
      <if test="settlementMethod != null" >
        settlement_method,
      </if>
      <if test="memberInputReminder != null" >
        member_input_reminder,
      </if>
      <if test="businessStartTime != null" >
        business_start_time,
      </if>
      <if test="productStartTime != null" >
        product_start_time,
      </if>
      <if test="healthNoteSwitch != null">
        health_note_switch ,
      </if>
      <if test="organSign != null" >
        organ_sign,
      </if>
      <if test="conventionalCuringCycle != null" >
        conventional_curing_cycle,
      </if>
      <if test="criticalCuringPeriod != null" >
        critical_curing_period,
      </if>
      <if test="advanceWarningCuring != null" >
        advance_warning_curing,
      </if>
      <if test="routineInspectionCycle != null" >
        routine_inspection_cycle,
      </if>
      <if test="criticalInspectionCycle != null" >
        critical_inspection_cycle,
      </if>
      <if test="advanceWarningInspection != null" >
        advance_warning_inspection,
      </if>
      <if test="earlyWarningDays != null" >
        early_warning_days,
      </if>
      <if test="remoteInquiry != null" >
        remote_inquiry,
      </if>
      <if test="storedValueAuditWEB != null" >
        stored_value_audit_WEB,
      </if>
      <if test="storedValueAuditPOS != null" >
        stored_value_audit_POS,
      </if>
      <if test="medicineModel != null" >
        medicine_model,
      </if>
      <if test="inventoryAuditRoleId != null" >
        inventory_audit_role_id,
      </if>
      <if test="storedValueSMSRemind != null" >
        stored_value_SMS_remind,
      </if>
      <if test="promotionPriority != null" >
        promotion_priority,
      </if>
      <if test="defaultPersonDateModifiable != null" >
        default_person_dateModifiable,
      </if>
      <if test="businessScopeYn != null">
        business_scope_yn,
      </if>
      <if test="couponNoteYn != null" >
        coupon_note_yn,
      </if>
      <if test="maintenancePlanData != null" >
        maintenance_plan_data,
      </if>
      <if test="maintenanceDataUpdateYn != null" >
        maintenance_data_update_yn,
      </if>
      <if test="returnSalesYn != null" >
        return_sales_yn,
      </if>
      <if test="prescriptionRegisteredYn != null" >
        prescription_registered_yn,
      </if>
      <if test="saleStorageYn != null" >
        sale_storage_yn,
      </if>
      <if test="drugSupervisionYn != null" >
        drug_supervision_yn,
      </if>
      <if test="providerQualificationExpiredRemindDays != null" >
        provider_qualification_expired_remind_days,
      </if>
      <if test="productQualificationExpiredRemindDays != null" >
        product_qualification_expired_remind_days,
      </if>
      <if test="deadStockWarningDays != null" >
        dead_stock_warning_days,
      </if>
      <if test="deadStockWarningCount != null" >
        dead_stock_warning_count,
      </if>
      <if test="blindShiftHandover != null" >
        blind_shift_handover,
      </if>
      <if test="auditingLevel != null" >
        auditing_level,
      </if>
      <if test="checkUserId != null" >
        check_user_id,
      </if>
      <if test="sendDrugUserId != null" >
        send_drug_user_id,
      </if>
      <if test="singleSelectRoleYn != null" >
        single_select_role_yn,
      </if>
      <if test="recordMobileForNarcoticYn != null" >
        record_mobile_for_narcotic_yn,
      </if>
      <if test="accountSetYn != null" >
        account_set_yn,
      </if>
      <if test="defaultAdjustPriceRatio != null" >
        default_adjust_price_ratio,
      </if>
      <if test="medicalPaySwitch != null" >
        medical_pay_switch,
      </if>
      <if test="medicalLevelInfo != null" >
        medical_level_info,
      </if>
      <if test="prescriptionAuditMode != null" >
        prescription_audit_mode,
      </if>
      <if test="fingerprintSwitchYn != null" >
        fingerprint_switch_yn,
      </if>
      <if test="purchaserWarningDays != null" >
        purchaser_warning_days,
      </if>
      <if test="beforeStopSaleDays != null" >
        before_stop_sale_days,
      </if>
      <if test="prescriptionFormExpiredDays != null" >
        prescription_form_expired_days,
      </if>
      <if test="prescriptionProductExpiredDays != null" >
        prescription_product_expired_days,
      </if>
      <if test="productTraceUploadYn != null" >
        product_trace_upload_yn,
      </if>
      <if test="productTraceUploadNonMsfxYn != null" >
        product_trace_upload_non_msfx_yn,
      </if>

      <if test="deviceUdiSupervisionYn != null" >
        device_udi_supervision_yn,
      </if>
      <if test="productSplitRows != null" >
        product_split_rows,
      </if>
      <if test="openRealNameRegistration != null" >
        open_real_name_registration,
      </if>
      <if test="receivableNumber != null" >
        receivable_number,
      </if>
      <if test="adMessageSwitch != null">
        ad_message_switch,
      </if>
      <if test="phoneSignSwitch != null">
        phone_sign_switch,
      </if>
      <if test="inventoryLotNumAdjustSwitch != null">
        inventory_lot_num_adjust_switch,
      </if>
      <if test="chineseMedicineNumber != null">
        chinese_medicine_number,
      </if>
      <if test="chineseMedicineUnit != null">
        chinese_medicine_unit,
      </if>
      <if test="auditingFirstRole != null">
        auditing_first_role,
      </if>
      <if test="auditingSecondRole != null">
        auditing_second_role,
      </if>
      <if test="auditingThirdRole != null">
        auditing_third_role,
      </if>
      <if test="hiddenTrackPrescriptionInfo != null">
        hidden_track_prescription_info,
      </if>
      <if test="showMedicareMatchBtnYn != null">
        show_medicare_match_btn_yn,
      </if>
      <if test="traceCodeSaleEntryYn != null">
        trace_code_sale_entry_yn,
      </if>
      <if test="recordAddressForNarcoticYn != null">
        record_address_for_narcotic_yn,
      </if>
      <if test="identInputTipYn != null">
        ident_input_tip_yn,
      </if>
      <if test="productRecoverAuditingYn != null">
        product_recover_auditing_yn,
      </if>
      <if test="productRecoverAuditingRole != null">
        product_recover_auditing_role,
      </if>
      <if test="productPriceTwoDecimalPlacesYn != null">
        product_price_two_decimal_places_yn,
      </if>
      <if test="productMedicineSaleDecimalYn != null">
        product_medicine_sale_decimal_yn,
      </if>
      <if test="productDrugSaleDecimalYn != null">
        product_drug_sale_decimal_yn,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="prescriptionYn != null" >
        #{prescriptionYn,jdbcType=TINYINT},
      </if>
      <if test="updateOrderDiscountYn != null" >
        #{updateOrderDiscountYn,jdbcType=TINYINT},
      </if>
      <if test="updateProductDiscountYn != null" >
        #{updateProductDiscountYn,jdbcType=TINYINT},
      </if>
      <if test="productDiscountStart != null" >
        #{productDiscountStart,jdbcType=INTEGER},
      </if>
      <if test="productDiscountEnd != null" >
        #{productDiscountEnd,jdbcType=INTEGER},
      </if>
      <if test="updateProductPrice != null" >
        #{updateProductPrice,jdbcType=TINYINT},
      </if>
      <if test="salesBelowCost != null" >
        #{salesBelowCost,jdbcType=TINYINT},
      </if>
      <if test="idCardYn != null" >
        #{idCardYn,jdbcType=TINYINT},
      </if>
      <if test="savePreSaleYn != null" >
        #{savePreSaleYn,jdbcType=TINYINT},
      </if>
      <if test="oneSetpYn != null" >
        #{oneSetpYn,jdbcType=TINYINT},
      </if>
      <if test="batchNoQueueYn != null" >
        #{batchNoQueueYn,jdbcType=TINYINT},
      </if>
      <if test="flaxNum != null" >
        #{flaxNum,jdbcType=INTEGER},
      </if>
      <if test="purchaseName != null" >
        #{purchaseName,jdbcType=VARCHAR},
      </if>
      <if test="examinerName != null" >
        #{examinerName,jdbcType=VARCHAR},
      </if>
      <if test="chineseMedicineExaminerId != null" >
        #{chineseMedicineExaminerId,jdbcType=VARCHAR},
      </if>
      <if test="posShowPurpriceYn != null" >
        #{posShowPurpriceYn,jdbcType=TINYINT},
      </if>
      <if test="approveProductYn != null" >
        #{approveProductYn,jdbcType=TINYINT},
      </if>
      <if test="approveProviderYn != null" >
        #{approveProviderYn,jdbcType=TINYINT},
      </if>
      <if test="approveProductQuickYn != null" >
        #{approveProductQuickYn,jdbcType=TINYINT},
      </if>
      <if test="approveProviderQuickYn != null" >
        #{approveProviderQuickYn,jdbcType=TINYINT},
      </if>
      <if test="approvePrescriptionYn != null" >
        #{approvePrescriptionYn,jdbcType=TINYINT},
      </if>
      <if test="enterpriseOwners != null">
        #{enterpriseOwners,jdbcType=VARCHAR},
      </if>
      <if test="enterpriseOwnersEmployeeId != null">
        #{enterpriseOwnersEmployeeId,jdbcType=INTEGER},
      </if>
      <if test="qualityOwners != null">
        #{qualityOwners,jdbcType=VARCHAR},
      </if>
      <if test="qualityOwnersEmployeeId != null">
        #{qualityOwnersEmployeeId,jdbcType=INTEGER},
      </if>
      <if test="licensedPharmacist != null">
        #{licensedPharmacist,jdbcType=VARCHAR},
      </if>
      <if test="saleAmountYn != null">
        #{saleAmountYn,jdbcType=TINYINT},
      </if>
      <if test="smallChange != null">
        #{smallChange,jdbcType=TINYINT},
      </if>
      <if test="smallChangeOpen != null">
        #{smallChangeOpen,jdbcType=VARCHAR},
      </if>
      <if test="smallChangeClose != null">
        #{smallChangeClose,jdbcType=VARCHAR},
      </if>
      <if test="zeroInventoryDisplay != null">
        #{zeroInventoryDisplay,jdbcType=TINYINT},
      </if>
      <if test="cashChange != null">
        #{cashChange,jdbcType=TINYINT},
      </if>
      <if test="memberReminder != null">
        #{memberReminder,jdbcType=TINYINT},
      </if>
      <if test="shortageRegistration != null">
        #{shortageRegistration,jdbcType=TINYINT},
      </if>
      <if test="fullStaffDownload != null">
        #{fullStaffDownload,jdbcType=TINYINT},
      </if>
      <if test="checkPrice != null">
        #{checkPrice,jdbcType=INTEGER},
      </if>
      <if test="settlementMethod != null">
        #{settlementMethod,jdbcType=VARCHAR},
      </if>
      <if test="memberInputReminder != null">
        #{memberInputReminder,jdbcType=TINYINT},
      </if>
      <if test="businessStartTime != null" >
        #{businessStartTime,jdbcType=VARCHAR},
      </if>
      <if test="productStartTime != null" >
        #{productStartTime,jdbcType=VARCHAR},
      </if>
      <if test="healthNoteSwitch != null">
        #{healthNoteSwitch,jdbcType=TINYINT},
      </if>
      <if test="organSign != null">
        #{organSign, jdbcType=VARCHAR},
      </if>
      <if test="conventionalCuringCycle != null" >
        #{conventionalCuringCycle,jdbcType=INTEGER},
      </if>
      <if test="criticalCuringPeriod != null" >
        #{criticalCuringPeriod,jdbcType=INTEGER},
      </if>
      <if test="advanceWarningCuring != null" >
        #{advanceWarningCuring,jdbcType=INTEGER},
      </if>
      <if test="routineInspectionCycle != null" >
        #{routineInspectionCycle,jdbcType=INTEGER},
      </if>
      <if test="criticalInspectionCycle != null" >
        #{criticalInspectionCycle,jdbcType=INTEGER},
      </if>
      <if test="advanceWarningInspection != null" >
        #{advanceWarningInspection,jdbcType=INTEGER},
      </if>
      <if test="earlyWarningDays != null" >
        #{earlyWarningDays,jdbcType=INTEGER},
      </if>
      <if test="remoteInquiry != null" >
        #{remoteInquiry,jdbcType=INTEGER},
      </if>
      <if test="storedValueAuditWEB != null" >
        #{storedValueAuditWEB,jdbcType=INTEGER},
      </if>
      <if test="storedValueAuditPOS != null" >
        #{storedValueAuditPOS,jdbcType=INTEGER},
      </if>
      <if test="medicineModel != null" >
        #{medicineModel,jdbcType=INTEGER},
      </if>
      <if test="inventoryAuditRoleId != null" >
        #{inventoryAuditRoleId,jdbcType=INTEGER},
      </if>
      <if test="storedValueSMSRemind != null" >
        #{storedValueSMSRemind,jdbcType=INTEGER},
      </if>
      <if test="promotionPriority != null" >
        #{promotionPriority,jdbcType=INTEGER},
      </if>
      <if test="defaultPersonDateModifiable != null" >
        #{defaultPersonDateModifiable,jdbcType=INTEGER},
      </if>
      <if test="businessScopeYn != null">
        #{businessScopeYn},
      </if>
      <if test="couponNoteYn != null" >
        #{couponNoteYn},
      </if>
      <if test="maintenancePlanData != null" >
        #{maintenancePlanData},
      </if>
      <if test="maintenanceDataUpdateYn != null" >
        #{maintenanceDataUpdateYn},
      </if>
      <if test="returnSalesYn != null" >
        #{returnSalesYn},
      </if>
      <if test="prescriptionRegisteredYn != null" >
        #{prescriptionRegisteredYn},
      </if>
      <if test="saleStorageYn != null" >
        #{saleStorageYn},
      </if>
      <if test="drugSupervisionYn != null" >
        #{drugSupervisionYn},
      </if>
      <if test="providerQualificationExpiredRemindDays != null" >
        #{providerQualificationExpiredRemindDays},
      </if>
      <if test="productQualificationExpiredRemindDays != null" >
        #{productQualificationExpiredRemindDays},
      </if>
      <if test="deadStockWarningDays != null" >
        #{deadStockWarningDays},
      </if>
      <if test="deadStockWarningCount != null" >
        #{deadStockWarningCount},
      </if>
      <if test="blindShiftHandover != null" >
        #{blindShiftHandover},
      </if>
      <if test="auditingLevel != null" >
        #{auditingLevel},
      </if>
      <if test="checkUserId != null" >
        #{checkUserId},
      </if>
      <if test="sendDrugUserId != null" >
        #{sendDrugUserId},
      </if>
      <if test="singleSelectRoleYn != null" >
        #{singleSelectRoleYn},
      </if>
      <if test="recordMobileForNarcoticYn != null" >
        #{recordMobileForNarcoticYn},
      </if>
      <if test="accountSetYn != null" >
        #{accountSetYn},
      </if>
      <if test="defaultAdjustPriceRatio != null" >
        #{defaultAdjustPriceRatio},
      </if>
      <if test="medicalPaySwitch != null" >
        #{medicalPaySwitch},
      </if>
      <if test="medicalLevelInfo != null" >
        #{medicalLevelInfo},
      </if>
      <if test="prescriptionAuditMode != null" >
        #{prescriptionAuditMode},
      </if>
      <if test="fingerprintSwitchYn != null" >
        #{fingerprintSwitchYn},
      </if>
      <if test="purchaserWarningDays != null" >
        #{purchaserWarningDays},
      </if>
      <if test="beforeStopSaleDays != null" >
        #{beforeStopSaleDays},
      </if>
      <if test="prescriptionFormExpiredDays != null" >
        #{prescriptionFormExpiredDays},
      </if>
      <if test="prescriptionProductExpiredDays != null" >
        #{prescriptionProductExpiredDays},
      </if>
      <if test="productTraceUploadYn != null" >
        #{productTraceUploadYn},
      </if>
      <if test="productTraceUploadNonMsfxYn != null" >
        #{productTraceUploadNonMsfxYn},
      </if>
      <if test="deviceUdiSupervisionYn != null" >
        #{deviceUdiSupervisionYn},
      </if>
      <if test="productSplitRows != null" >
        #{productSplitRows},
      </if>
      <if test="openRealNameRegistration != null" >
        #{openRealNameRegistration},
      </if>
      <if test="receivableNumber != null" >
        #{receivableNumber},
      </if>
      <if test="adMessageSwitch != null">
        #{adMessageSwitch},
      </if>
      <if test="phoneSignSwitch != null">
        #{phoneSignSwitch},
      </if>
      <if test="inventoryLotNumAdjustSwitch != null">
        #{inventoryLotNumAdjustSwitch},
      </if>
      <if test="chineseMedicineNumber != null">
        #{chineseMedicineNumber},
      </if>
      <if test="chineseMedicineUnit != null">
        #{chineseMedicineUnit},
      </if>
      <if test="auditingFirstRole != null">
        #{auditingFirstRole},
      </if>
      <if test="auditingSecondRole != null">
        #{auditingSecondRole},
      </if>
      <if test="auditingThirdRole != null">
        #{auditingThirdRole},
      </if>
      <if test="hiddenTrackPrescriptionInfo != null">
        #{hiddenTrackPrescriptionInfo},
      </if>
      <if test="showMedicareMatchBtnYn != null">
        #{showMedicareMatchBtnYn},
      </if>
      <if test="traceCodeSaleEntryYn != null">
        #{traceCodeSaleEntryYn},
      </if>
      <if test="recordAddressForNarcoticYn != null">
        #{recordAddressForNarcoticYn},
      </if>
      <if test="identInputTipYn != null">
        #{identInputTipYn},
      </if>
      <if test="productRecoverAuditingYn != null">
        #{productRecoverAuditingYn},
      </if>
      <if test="productRecoverAuditingRole != null">
        #{productRecoverAuditingRole},
      </if>
      <if test="productPriceTwoDecimalPlacesYn != null">
        #{productPriceTwoDecimalPlacesYn},
      </if>
      <if test="productMedicineSaleDecimalYn != null">
        #{productMedicineSaleDecimalYn},
      </if>
      <if test="productDrugSaleDecimalYn != null">
        #{productDrugSaleDecimalYn},
      </if>
    </trim>
  </insert>

  <update id="update" parameterType="com.xyy.common.provider.module.entity.SystemConfigPo" >
    update saas_system_config
    <set >
      <if test="prescriptionYn != null" >
        prescription_yn = #{prescriptionYn,jdbcType=TINYINT},
      </if>
      <if test="updateOrderDiscountYn != null" >
        update_order_discount_yn = #{updateOrderDiscountYn,jdbcType=TINYINT},
      </if>
      <if test="updateProductDiscountYn != null" >
        update_product_discount_yn = #{updateProductDiscountYn,jdbcType=TINYINT},
      </if>
      <if test="productDiscountStart != null" >
        product_discount_start = #{productDiscountStart,jdbcType=INTEGER},
      </if>
      <if test="productDiscountEnd != null" >
        product_discount_end = #{productDiscountEnd,jdbcType=INTEGER},
      </if>
      <if test="updateProductPrice != null" >
        update_product_price = #{updateProductPrice,jdbcType=TINYINT},
      </if>
      <if test="salesBelowCost != null" >
        sales_below_cost = #{salesBelowCost,jdbcType=TINYINT},
      </if>
      <if test="idCardYn != null" >
        id_card_yn = #{idCardYn,jdbcType=TINYINT},
      </if>
      <if test="savePreSaleYn != null" >
        save_pre_sale_yn = #{savePreSaleYn,jdbcType=TINYINT},
      </if>
      <if test="oneSetpYn != null" >
        one_setp_yn = #{oneSetpYn,jdbcType=TINYINT},
      </if>
      <if test="batchNoQueueYn != null" >
        batch_no_queue_yn = #{batchNoQueueYn,jdbcType=TINYINT},
      </if>
      <if test="flaxNum != null" >
        flax_num = #{flaxNum,jdbcType=INTEGER},
      </if>
      <if test="purchaseName != null and purchaseName !=''" >
        purchase_name = #{purchaseName,jdbcType=VARCHAR},
      </if>
      <if test="examinerName != null and examinerName !=''" >
        examiner_name = #{examinerName,jdbcType=VARCHAR},
      </if>
      <if test="chineseMedicineExaminerId != null and chineseMedicineExaminerId !=''" >
        chinese_medicine_examiner_id = #{chineseMedicineExaminerId,jdbcType=VARCHAR},
      </if>
      <if test="posShowPurpriceYn != null" >
        pos_show_purprice_yn = #{posShowPurpriceYn,jdbcType=TINYINT},
      </if>
      <if test="approveProductYn != null" >
        approve_product_yn = #{approveProductYn,jdbcType=TINYINT},
      </if>
      <if test="approveProviderYn != null" >
        approve_provider_yn = #{approveProviderYn,jdbcType=TINYINT},
      </if>
      <if test="approveProductQuickYn != null" >
        approve_product_quick_yn = #{approveProductQuickYn,jdbcType=TINYINT},
      </if>
      <if test="approveProviderQuickYn != null" >
        approve_provider_quick_yn = #{approveProviderQuickYn,jdbcType=TINYINT},
      </if>
      <if test="approvePrescriptionYn != null" >
        approve_prescription_yn = #{approvePrescriptionYn,jdbcType=TINYINT},
      </if>
      <if test="enterpriseOwners != null">
        enterprise_owners =  #{enterpriseOwners,jdbcType=VARCHAR},
      </if>
      <if test="enterpriseOwnersEmployeeId != null">
        enterprise_owners_employee_id =  #{enterpriseOwnersEmployeeId,jdbcType=INTEGER},
      </if>
      <if test="qualityOwners != null">
        quality_owners =  #{qualityOwners,jdbcType=VARCHAR},
      </if>
      <if test="qualityOwnersEmployeeId != null">
        quality_owners_employee_id =  #{qualityOwnersEmployeeId,jdbcType=INTEGER},
      </if>
      <if test="licensedPharmacist != null">
        licensed_pharmacist =  #{licensedPharmacist,jdbcType=VARCHAR},
      </if>
      <if test="saleAmountYn != null">
        sale_amount_yn=#{saleAmountYn,jdbcType=TINYINT},
      </if>
      <if test="smallChange != null">
        small_change=#{smallChange,jdbcType=TINYINT},
      </if>
      <if test="smallChangeOpen != null">
        small_change_open=#{smallChangeOpen,jdbcType=VARCHAR},
      </if>
      <if test="smallChangeClose != null">
        small_change_close=#{smallChangeClose,jdbcType=VARCHAR},
      </if>
      <if test="zeroInventoryDisplay != null">
        zero_inventory_display=#{zeroInventoryDisplay,jdbcType=TINYINT},
      </if>
      <if test="memberReminder != null">
        member_reminder=#{memberReminder,jdbcType=TINYINT},
      </if>
      <if test="shortageRegistration != null">
        shortage_registration=#{shortageRegistration,jdbcType=TINYINT},
      </if>
      <if test="fullStaffDownload != null">
        full_staff_download=#{fullStaffDownload,jdbcType=TINYINT},
      </if>
      <if test="checkPrice != null">
        check_price=#{checkPrice,jdbcType=INTEGER},
      </if>
      <if test="cashChange != null">
        cash_change=#{cashChange,jdbcType=TINYINT},
      </if>
      <if test="settlementMethod != null">
        settlement_method=#{settlementMethod,jdbcType=VARCHAR},
      </if>
      <if test="memberInputReminder != null">
        member_input_reminder=#{memberInputReminder,jdbcType=VARCHAR},
      </if>
      <if test="businessStartTime != null">
        business_start_time=#{businessStartTime,jdbcType=VARCHAR},
      </if>
      <if test="productStartTime != null">
        product_start_time=#{productStartTime,jdbcType=VARCHAR},
      </if>
      <if test="healthNoteSwitch != null">
        health_note_switch = #{healthNoteSwitch,jdbcType=TINYINT},
      </if>
      <if test="conventionalCuringCycle != null" >
        conventional_curing_cycle=#{conventionalCuringCycle,jdbcType=INTEGER},
      </if>
      <if test="criticalCuringPeriod != null" >
        critical_curing_period=#{criticalCuringPeriod,jdbcType=INTEGER},
      </if>
      <if test="advanceWarningCuring != null" >
        advance_warning_curing=#{advanceWarningCuring,jdbcType=INTEGER},
      </if>
      <if test="routineInspectionCycle != null" >
        routine_inspection_cycle=#{routineInspectionCycle,jdbcType=INTEGER},
      </if>
      <if test="criticalInspectionCycle != null" >
        critical_inspection_cycle=#{criticalInspectionCycle,jdbcType=INTEGER},
      </if>
      <if test="advanceWarningInspection != null" >
        advance_warning_inspection=#{advanceWarningInspection,jdbcType=INTEGER},
      </if>
      <if test="earlyWarningDays != null" >
        early_warning_days = #{earlyWarningDays,jdbcType=INTEGER},
      </if>
      <if test="remoteInquiry != null" >
        remote_inquiry  = #{remoteInquiry,jdbcType=INTEGER},
      </if>
      <if test="storedValueAuditWEB != null" >
        stored_value_audit_WEB  = #{storedValueAuditWEB,jdbcType=INTEGER},
      </if>
      <if test="storedValueAuditPOS != null" >
        stored_value_audit_POS = #{storedValueAuditPOS,jdbcType=INTEGER},
      </if>
      <if test="medicineModel != null" >
        medicine_model  = #{medicineModel,jdbcType=INTEGER},
      </if>
      <if test="inventoryAuditRoleId != null" >
        inventory_audit_role_id  = #{inventoryAuditRoleId,jdbcType=INTEGER},
      </if>
      <if test="storedValueSMSRemind != null" >
        stored_value_SMS_remind = #{storedValueSMSRemind,jdbcType=INTEGER},
      </if>
      <if test="promotionPriority != null" >
        promotion_priority=#{promotionPriority,jdbcType=INTEGER},
      </if>
      <if test="defaultPersonDateModifiable != null" >
        default_person_dateModifiable = #{defaultPersonDateModifiable,jdbcType=INTEGER},
      </if>
      <if test="businessScopeYn != null" >
        business_scope_yn = #{businessScopeYn},
      </if>
      <if test="couponNoteYn != null" >
        coupon_note_yn = #{couponNoteYn},
      </if>
      <if test="maintenancePlanData != null" >
        maintenance_plan_data = #{maintenancePlanData},
      </if>
      <if test="maintenanceDataUpdateYn != null" >
        maintenance_data_update_yn = #{maintenanceDataUpdateYn},
      </if>
      <if test="returnSalesYn != null" >
        return_sales_yn = #{returnSalesYn},
      </if>
      <if test="prescriptionRegisteredYn != null" >
        prescription_registered_yn = #{prescriptionRegisteredYn},
      </if>
      <if test="saleStorageYn != null" >
        sale_storage_yn = #{saleStorageYn},
      </if>
      <if test="drugSupervisionYn != null" >
        drug_supervision_yn = #{drugSupervisionYn},
      </if>
      <if test="providerQualificationExpiredRemindDays != null" >
        provider_qualification_expired_remind_days = #{providerQualificationExpiredRemindDays},
      </if>
      <if test="productQualificationExpiredRemindDays != null" >
        product_qualification_expired_remind_days = #{productQualificationExpiredRemindDays},
      </if>
      <if test="deadStockWarningDays != null" >
        dead_stock_warning_days = #{deadStockWarningDays},
      </if>
      <if test="deadStockWarningCount != null" >
        dead_stock_warning_count = #{deadStockWarningCount},
      </if>
      <if test="blindShiftHandover != null" >
        blind_shift_handover = #{blindShiftHandover,jdbcType=TINYINT},
      </if>
      <if test="auditingLevel != null" >
        auditing_level = #{auditingLevel},
      </if>
      <if test="checkUserId != null" >
        check_user_id = #{checkUserId},
      </if>
      <if test="sendDrugUserId != null" >
        send_drug_user_id = #{sendDrugUserId},
      </if>
      <if test="singleSelectRoleYn != null" >
        single_select_role_yn = #{singleSelectRoleYn,jdbcType=TINYINT},
      </if>
      <if test="recordMobileForNarcoticYn != null" >
        record_mobile_for_narcotic_yn = #{recordMobileForNarcoticYn,jdbcType=TINYINT},
      </if>
      <if test="accountSetYn != null" >
        account_set_yn = #{accountSetYn,jdbcType=TINYINT},
      </if>
      <if test="defaultAdjustPriceRatio != null" >
        default_adjust_price_ratio = #{defaultAdjustPriceRatio,jdbcType=DECIMAL},
      </if>
      <if test="medicalPaySwitch != null" >
        medical_pay_switch = #{medicalPaySwitch,jdbcType=TINYINT},
      </if>
      <if test="medicalLevelInfo != null" >
        medical_level_info = #{medicalLevelInfo,jdbcType=VARCHAR},
      </if>
      <if test="prescriptionAuditMode != null" >
        prescription_audit_mode = #{prescriptionAuditMode,jdbcType=TINYINT},
      </if>
      <if test="fingerprintSwitchYn != null" >
        fingerprint_switch_yn = #{fingerprintSwitchYn,jdbcType=TINYINT},
      </if>
      <if test="purchaserWarningDays != null" >
        purchaser_warning_days = #{purchaserWarningDays},
      </if>
      <if test="beforeStopSaleDays != null" >
        before_stop_sale_days = #{beforeStopSaleDays},
      </if>
      <if test="prescriptionFormExpiredDays != null" >
        prescription_form_expired_days = #{prescriptionFormExpiredDays},
      </if>
      <if test="prescriptionProductExpiredDays != null" >
        prescription_product_expired_days = #{prescriptionProductExpiredDays,jdbcType=TINYINT},
      </if>
      <if test="productTraceUploadYn != null" >
        product_trace_upload_yn = #{productTraceUploadYn},
      </if>
      <if test="productTraceUploadNonMsfxYn != null" >
        product_trace_upload_non_msfx_yn = #{productTraceUploadNonMsfxYn},
      </if>
      <if test="productSplitRows != null" >
        product_split_rows = #{productSplitRows},
      </if>
      <if test="openRealNameRegistration != null" >
        open_real_name_registration = #{openRealNameRegistration},
      </if>
      <if test="receivableNumber != null" >
        receivable_number = #{receivableNumber},
      </if>
      <if test="adMessageSwitch != null">
        ad_message_switch = #{adMessageSwitch},
      </if>
      <if test="phoneSignSwitch != null">
        phone_sign_switch = #{phoneSignSwitch},
      </if>
      <if test="inventoryLotNumAdjustSwitch != null">
        inventory_lot_num_adjust_switch = #{inventoryLotNumAdjustSwitch},
      </if>
      <if test="chineseMedicineNumber != null">
        chinese_medicine_number = #{chineseMedicineNumber},
      </if>
      <if test="chineseMedicineUnit != null">
        chinese_medicine_unit = #{chineseMedicineUnit},
      </if>
      <if test="auditingFirstRole != null">
        auditing_first_role = #{auditingFirstRole},
      </if>
      <if test="auditingSecondRole != null">
        auditing_second_role = #{auditingSecondRole},
      </if>
      <if test="auditingThirdRole != null">
        auditing_third_role = #{auditingThirdRole},
      </if>
      <if test="hiddenTrackPrescriptionInfo != null">
        hidden_track_prescription_info = #{hiddenTrackPrescriptionInfo},
      </if>
      <if test="showMedicareMatchBtnYn != null">
        show_medicare_match_btn_yn = #{showMedicareMatchBtnYn},
      </if>
      <if test="traceCodeSaleEntryYn != null">
        trace_code_sale_entry_yn = #{traceCodeSaleEntryYn},
      </if>
      <if test="deviceUdiSupervisionYn != null">
        device_udi_supervision_yn = #{deviceUdiSupervisionYn},
      </if>
      <if test="recordAddressForNarcoticYn != null">
        record_address_for_narcotic_yn = #{recordAddressForNarcoticYn},
      </if>
      <if test="identInputTipYn != null">
        ident_input_tip_yn = #{identInputTipYn},
      </if>
      <if test="productRecoverAuditingYn != null">
        product_recover_auditing_yn = #{productRecoverAuditingYn},
      </if>
      <if test="productRecoverAuditingRole != null">
        product_recover_auditing_role = #{productRecoverAuditingRole},
      </if>
      <if test="productPriceTwoDecimalPlacesYn != null">
        product_price_two_decimal_places_yn = #{productPriceTwoDecimalPlacesYn},
      </if>
      <if test="productMedicineSaleDecimalYn != null">
        product_medicine_sale_decimal_yn = #{productMedicineSaleDecimalYn},
      </if>
      <if test="productDrugSaleDecimalYn != null">
        product_drug_sale_decimal_yn = #{productDrugSaleDecimalYn},
      </if>
    </set>
    where organ_sign = #{organSign, jdbcType=VARCHAR}
  </update>

  <!-- 更新系统设置 若职业药师为空 则更新 -->
  <update id="updateEmptyLicensedPharmacist">
    update saas_system_config set licensed_pharmacist = #{licensedPharmacist}
    where organ_sign=#{organSign} and (licensed_pharmacist is null or licensed_pharmacist = '')
  </update>

  <select id="selectSystemConfigByOrganSign" resultType="com.xyy.common.provider.module.entity.SystemConfigPo">
    select * from saas_system_config where organ_sign = #{organSign, jdbcType=VARCHAR}
  </select>

  <!-- 查询职业药师为空的机构列表 -->
  <select id="queryEmptyLicensedPharmacistOrgList" resultType="java.lang.String">
    select organ_sign from saas_system_config where licensed_pharmacist is null or licensed_pharmacist = ''
  </select>

  <select id="getOpenTraceCodeSalesOrganSignList" resultType="java.lang.String">
    select organ_sign from xyy_saas_common.saas_system_config
    where (product_trace_upload_yn = 1
      or product_trace_upload_non_msfx_yn = 1
      or trace_code_sale_entry_yn = 1)
  </select>
</mapper>