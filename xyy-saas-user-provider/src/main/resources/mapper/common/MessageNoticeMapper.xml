<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.common.provider.module.dao.MessageNoticeMapper">
	<resultMap id="BaseResultMap" type="com.xyy.common.provider.module.entity.MessageNoticePo">
		<id     column="id"                  property="id"                  jdbcType="BIGINT" />
		<result column="title"              property="title"               jdbcType="VARCHAR" />
		<result column="content"            property="content"             jdbcType="VARCHAR" />
		<result column="label"              property="label"               jdbcType="VARCHAR" />
		<result column="scheduled_release_time"      property="scheduledReleaseTime"   jdbcType="TIMESTAMP" />
		<result column="classification"    property="classification"     jdbcType="TINYINT" />
		<result column="state"              property="state"               jdbcType="TINYINT" />
		<result column="release_people_id" property="releasePeopleId"    jdbcType="VARCHAR" />
        <result column="all_drugstore"     property="allDrugstore"        jdbcType="INTEGER" />
		<result column="create_time"         property="createTime"        jdbcType="TIMESTAMP" />
		<result column="update_time"         property="updateTime"        jdbcType="TIMESTAMP" />
        <result column="yn"                   property="yn"                 jdbcType="TINYINT" />
        <result column="release_time"        property="releaseTime"       jdbcType="TIMESTAMP" />
        <result column="create_user"         property="createUser"         jdbcType="VARCHAR" />
        <result column="update_user"         property="updateUser"         jdbcType="VARCHAR" />
        <result column="expire_time"         property="expireTime"        jdbcType="TIMESTAMP" />
        <result column="province"         property="province"        jdbcType="VARCHAR" />
        <result column="store_num"         property="storeNum"        jdbcType="INTEGER" />
        <result column="area_code"         property="areaCode"        jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List">
		id, title, content, label, scheduled_release_time, classification, state,
		release_people_id,all_drugstore,create_time,update_time,yn,release_time,create_user,update_user,expire_time,province,store_num,area_code
	</sql>

    <sql id="Base_Column_List_Simple">
        id, title, label, scheduled_release_time, classification, state,
        release_people_id,all_drugstore,create_time,update_time,yn,release_time,create_user,update_user,expire_time,province,store_num,area_code
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.xyy.common.provider.module.entity.MessageNoticePo">
		insert into saas_message_notice
		(id, title, content,label, scheduled_release_time, classification,state, release_people_id,all_drugstore,create_time,update_time,yn,
		release_time,create_user,update_user, expire_time, area_code
		)
		values
		(#{id,jdbcType=BIGINT}, #{title,jdbcType=VARCHAR},#{content},#{label,jdbcType=VARCHAR}, #{scheduledReleaseTime,jdbcType=TIMESTAMP},
		 #{classification,jdbcType=TINYINT},#{state,jdbcType=TINYINT},#{releasePeopleId,jdbcType=VARCHAR},#{allDrugstore}, #{createTime,jdbcType=TIMESTAMP},
		  #{updateTime,jdbcType=TIMESTAMP}, #{yn,jdbcType=TINYINT},
		 #{releaseTime,jdbcType=TIMESTAMP},#{createUser,jdbcType=VARCHAR},#{updateUser,jdbcType=VARCHAR},#{expireTime},#{areaCode}

		 )
	</insert>

    <update id="update" parameterType="com.xyy.common.provider.module.entity.MessageNoticePo">
        update saas_message_notice
        <set>
            <if test="title != null">
                title = #{title,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                content = #{content},
            </if>
            <if test="label != null">
                label = #{label,jdbcType=VARCHAR},
            </if>
            <if test="label == null">
                label = #{label,jdbcType=VARCHAR},
            </if>
            <if test="scheduledReleaseTime != null">
                scheduled_release_time = #{scheduledReleaseTime,jdbcType=TIMESTAMP},
            </if>
            <if test="classification != null">
                classification = #{classification,jdbcType=TINYINT},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=TINYINT},
            </if>
            <if test="releasePeopleId != null">
                release_people_id = #{releasePeopleId,jdbcType=VARCHAR},
            </if>
            <if test="allDrugstore != null">
                all_drugstore = #{allDrugstore},
            </if>
            <if test="yn != null">
                yn = #{yn,jdbcType=TINYINT},
            </if>
            <if test="releaseTime != null">
                release_time = #{releaseTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="expireTime != null">
                expire_time = #{expireTime},
            </if>
            <if test="province != null and province != ''">
                province = #{province},
            </if>
            <if test="storeNum != null">
                store_num = #{storeNum},
            </if>
            area_code = #{areaCode}
        </set>
        where  id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from saas_message_notice
        where yn=1 and id = #{id,jdbcType=BIGINT}
    </select>


    <select id="getMessageNoticeListPager" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Simple"/>
        from saas_message_notice
        where 1=1 and yn = 1
        <if test="releasePeopleId != null">
            and  release_people_id = #{releasePeopleId}
        </if>
        <if test="title != null and title !=''">
            and  title like CONCAT("%", #{title},"%")
        </if>
        <if test="classification != null">
            and  classification = #{classification}
        </if>
        <if test="state != null">
            and  state = #{state}
        </if>
        <if test="startReleaseTime != null and endReleaseTime != null ">
            <![CDATA[ and release_time >= #{startReleaseTime} ]]>
            <![CDATA[ and  #{endReleaseTime} >=release_time ]]>
        </if>
        order by  create_time  desc
    </select>

    <select id="getMessageNoticeByEmployeeIdPager" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from saas_message_notice
        WHERE yn = 1 AND state = 1
        <if test="messageNotice.classification != null">
            and classification = #{messageNotice.classification}
        </if>
        <if test="messageNotice.classificationList != null and messageNotice.classificationList.size > 0">
            and classification in(
            <foreach collection="messageNotice.classificationList" item="classification" index="index" separator=",">
                #{classification}
            </foreach>
            )
        </if>
        <if test="messageNotice.expireTime != null">
            <![CDATA[ and expire_time >= #{messageNotice.expireTime}]]>
        </if>
        <if test="list != null">
            and id in(
            <foreach collection="list" item="item" index="index" separator=",">
                #{item.messageNoticeId}
            </foreach>
            )
        </if>
        order by  release_time  desc
    </select>

    <update id="updateState" parameterType="com.xyy.common.provider.module.entity.MessageNoticePo">
        update saas_message_notice
        <set>
            <if test="state != null">
                state = #{state,jdbcType=TINYINT},
            </if>
            <if test="releasePeopleId != null">
                release_people_id = #{releasePeopleId,jdbcType=VARCHAR},
            </if>
            <if test="releaseTime != null">
                release_time = #{releaseTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectReleasePeopleId" resultType="string">
        select
         DISTINCT release_people_id as releasePeopleId
        from saas_message_notice
        where  yn = 1  and release_people_id is not null
    </select>

<!--    <select id="getMessageNoticeIdLatest"  resultMap="BaseResultMap">-->
<!--        select-->
<!--        a.id, a.title, a.content, a.label, a.scheduled_release_time, a.classification, b.state,-->
<!--        a.release_people_id,a.all_drugstore,a.create_time,a.update_time,a.yn,a.release_time,a.create_user,a.update_user,a.expire_time,a.province,a.store_num,a.area_code-->
<!--        from saas_message_notice a left join saas_message_notice_employee b on a.id = b.message_notice_id-->
<!--        where b.yn = 1 AND a.state = 1-->
<!--        <if test="organSign != null and organSign !=''">-->
<!--            and b.organ_sign = #{organSign}-->
<!--        </if>-->
<!--        <if test="employeeId != null">-->
<!--            and b.employee_id = #{employeeId}-->
<!--        </if>-->
<!--        <if test="state != null">-->
<!--            and b.state = #{state}-->
<!--        </if>-->
<!--        <if test="classification != null">-->
<!--            and a.classification = #{classification}-->
<!--        </if>-->
<!--        <if test="expireTime != null">-->
<!--            <![CDATA[ and a.expire_time >= #{expireTime}]]>-->
<!--        </if>-->
<!--        order by a.id desc limit 1-->
<!--    </select>-->


    <select id="getMessageNoticeIds" resultType="com.xyy.common.provider.module.entity.MessageNoticePo">
        select  * from saas_message_notice
        <where>
            <if test="state != null">
                and state = #{state}
            </if>
            <if test="classification != null">
                and classification = #{classification}
            </if>
            <if test="expireTime != null">
                <![CDATA[ and expire_time >= #{expireTime}]]>
            </if>
        </where>
    </select>
    <select id="getMessageNoticeIdLatest" resultType="com.xyy.common.provider.module.entity.MessageLatestPo">
        select * from saas_message_notice_employee force index(idx_employee_id)
        where yn = 1
        <if test="organSign != null and organSign !=''">
            and organ_sign = #{organSign}
        </if>
        <if test="employeeId != null">
            and employee_id = #{employeeId}
        </if>
        <if test="state != null">
            and state = #{state}
        </if>
        <if test="messageNoticeIds != null and messageNoticeIds.size()>0 ">
            and message_notice_id in(
            <foreach collection="messageNoticeIds" item="item" index="index" separator=",">
                #{item}
            </foreach>
            )
        </if>
        order by id desc limit 1
    </select>


</mapper>