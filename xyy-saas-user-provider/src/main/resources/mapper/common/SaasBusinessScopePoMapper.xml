<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.common.provider.module.dao.SaasBusinessScopePoMapper">
    <resultMap id="BaseResultMap" type="com.xyy.common.provider.module.entity.SaasBusinessScopePo">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="dict_id" property="dictId"/>
        <result column="name" property="name"/>
        <result column="sort" property="sort"/>
        <result column="yn" property="yn"/>
        <result column="status" property="status" />
        <result column="base_version" property="baseVersion"/>
        <result column="organ_sign" property="organSign"/>
        <result column="certificate_type" property="certificateType"/>
        <result column="enable_yn" property="enableYn"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, dict_id, name, sort, yn, status, base_version, organ_sign, certificate_type, enable_yn,
    create_time, update_time, create_user,update_user
  </sql>

    <select id="selectSaasBusinessScopeList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM saas_business_scope
        WHERE yn = 1 and
        organ_sign = #{organSign}
        <if test="chainStoreFlag != null and chainStoreFlag">
            AND certificate_type != 0
        </if>
        <if test="enableYn != null">
            and enable_yn = #{enableYn}
        </if>
        <if test="certificateType != null">
            AND certificate_type = #{certificateType}
        </if>
        <if test="certificateTypes != null and certificateTypes.size > 0">
            AND certificate_type in
            <foreach collection="certificateTypes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>

        </if>
        ORDER BY create_time
    </select>

    <select id="selectSaasBusinessScopeIdList" resultType="java.lang.Integer">
        SELECT DISTINCT(dict_id)
        FROM saas_business_scope
        WHERE yn = 1
        AND organ_sign = #{organSign}
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="enableYn != null">
            AND enable_yn = #{enableYn}
        </if>
        <if test="chainStoreFlag != null and chainStoreFlag">
            AND certificate_type != 0
        </if>
        ORDER BY id
    </select>

    <select id="syncData" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM saas_business_scope
        WHERE
        organ_sign = #{organSign}
        and CONVERT(base_version,SIGNED) &gt; #{baseVersion} order by base_version+0 limit #{count}
    </select>

    <select id="selectSaasBusinessScope" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM saas_business_scope
        WHERE yn = 1 and organ_sign = #{organSign} and dict_id = #{dictId} and enable_yn = 1 limit 1
    </select>

    <select id="selectMaxBaseVersion" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM saas_business_scope
        WHERE yn = 1
        and organ_sign = #{organSign}
        order by base_version DESC limit 1
    </select>

    <update id="updateByPrimaryKeySelective">
        update saas_business_scope
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="sort != null">
                sort = #{sort},
            </if>
            <if test="yn != null">
                yn = #{yn},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="certificateType != null">
                certificate_type = #{certificateType},
            </if>
            <if test="baseVersion != null">
                base_version = #{baseVersion},
            </if>
            <if test="enableYn != null">
                enable_yn = #{enableYn},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser},
            </if>
        </set>
        where dict_id = #{dictId}
        <if test="organSign != null and organSign != ''">
            and organ_sign = #{organSign}
        </if>
    </update>

    <update id="updateEnableYnById">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update saas_business_scope
            <set>
                enable_yn=${item.enableYn},
                base_version = ${item.baseVersion},
                update_user = #{item.updateUser}
            </set>
            where id = ${item.id}
        </foreach>
    </update>

    <update id="updateEnableYnByDict">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update saas_business_scope
            <set>
                enable_yn=${item.enableYn},
                base_version = ${item.baseVersion},
                update_user = #{item.updateUser}
            </set>
            where dict_id = ${item.dictId} and organ_sign = #{organSign}
        </foreach>
    </update>

    <insert id="insertSelective">
        insert into saas_business_scope
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dictId != null">
                dict_id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="yn != null">
                yn,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="baseVersion != null">
                base_version,
            </if>
            <if test="organSign != null">
                organ_sign,
            </if>
            <if test="certificateType != null">
                certificate_type,
            </if>
            <if test="enableYn != null">
                enable_yn,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dictId != null">
                #{dictId},
            </if>
            <if test="name != null">
                #{name},
            </if>
            <if test="sort != null">
                #{sort},
            </if>
            <if test="yn != null">
                #{yn},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="baseVersion != null">
                #{baseVersion},
            </if>
            <if test="organSign != null">
                #{organSign},
            </if>
            <if test="certificateType != null">
                #{certificateType},
            </if>
            <if test="enableYn != null">
                #{enableYn},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="createUser != null">
                #{createUser},
            </if>
            <if test="updateUser != null">
                #{updateUser},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert">
        INSERT INTO saas_business_scope (dict_id, name, sort, yn, status, base_version, organ_sign,  enable_yn,
        create_time, update_time ,create_user,update_user,certificate_type)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.dictId}, #{item.name},
            #{item.sort},#{item.yn},#{item.status},
            #{item.baseVersion}, #{item.organSign},
            #{item.enableYn}, #{item.createTime},
            #{item.updateTime}, #{item.createUser},
            #{item.updateUser},#{item.certificateType})
        </foreach>
    </insert>

    <delete id="deleteByParam">
        delete from saas_business_scope
        <where>
            <if test="organSign != null and organSign != ''">
                and organ_sign = #{organSign}
            </if>
            <if test="certificateType != null and certificateType != ''">
                and certificate_type = #{certificateType}
            </if>
            <if test="id != null">
                and id = #{id}
            </if>
        </where>
    </delete>

    <delete id="deleteByOrgansAndDicts">
        delete from saas_business_scope
        <where>
            organ_sign in
            <foreach collection="organSigns" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
            <if test="dicts != null">
                and dict_id in
                <foreach collection="dicts" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </delete>

    <select id="getNoRepeateId" resultMap="BaseResultMap">
        select  id,max(base_version) from saas_business_scope where organ_sign = #{organSign} group by dict_id
    </select>

    <delete id="deleteNotMax">
        delete from  saas_business_scope where organ_sign = #{organSign} and id not in
        <foreach collection="list" separator="," open="(" item="item" index="index" close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteById">
        delete from  saas_business_scope where organ_sign = #{organSign} and id in
        <foreach collection="list" separator="," open="(" item="item" index="index" close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteByDictId">
        delete from  saas_business_scope where organ_sign = #{organSign} and dict_id not in
        <foreach collection="list" separator="," open="(" item="item" index="index" close=")">
            #{item}
        </foreach>
    </delete>

    <update id="refreshCustomBS">
        update saas_business_scope set certificate_type = 15 where organ_sign = #{organSign} and dict_id in
        <foreach collection="ids" open="(" close=")" separator="," index="index" item="item">
            #{item}
        </foreach>
    </update>

    <update id="refreshParent">
        update saas_business_scope set enable_yn = 0 where dict_id in (707,713,731,732,775,798,822)
        and organ_sign in
        <foreach collection="organSigns" open="(" close=")" separator="," index="index" item="item">
            #{item}
        </foreach>
    </update>

    <select id="getAllIdScopeByOrgan" resultMap="BaseResultMap" parameterType="string">
        select id from saas_business_scope where organ_sign = #{organSign}
    </select>

    <update id="updateVersionById">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update saas_business_scope
            <set>
                base_version = ${item.baseVersion}
            </set>
            where id = #{item.id}
        </foreach>
    </update>

</mapper>