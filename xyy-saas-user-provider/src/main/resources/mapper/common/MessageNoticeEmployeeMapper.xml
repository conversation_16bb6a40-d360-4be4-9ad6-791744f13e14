<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xyy.common.provider.module.dao.MessageNoticeEmployeeMapper">
    <resultMap id="BaseResultMap" type="com.xyy.common.provider.module.entity.MessageNoticeEmployeePo">
        <id     column="id"                     property="id"                           jdbcType="BIGINT" />
        <result column="organ_sign"            property="organSign"                  jdbcType="VARCHAR" />
        <result column="message_notice_id"    property="messageNoticeId"         jdbcType="BIGINT" />
        <result column="employee_id"           property="employeeId"                  jdbcType="VARCHAR" />
        <result column="state"                  property="state"                        jdbcType="TINYINT" />
        <result column="yn"                     property="yn"                            jdbcType="TINYINT" />
    </resultMap>

    <sql id="Base_Column_List">
        id, organ_sign,message_notice_id, employee_id, state, yn
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.xyy.common.provider.module.entity.MessageNoticeEmployeePo">
        insert into saas_message_notice_employee
        (id, organ_sign,message_notice_id, employee_id, state, yn)
        values
        (#{id ,jdbcType=BIGINT}, #{organSign,jdbcType=VARCHAR}, #{messageNoticeId,jdbcType=BIGINT},#{employeeId,jdbcType=VARCHAR},
        #{state,jdbcType=TINYINT}, #{yn,jdbcType=TINYINT})
    </insert>

    <insert id="batchInsert">
        insert into saas_message_notice_employee
        (id, organ_sign,message_notice_id, employee_id, state, yn, custom_flag)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id,jdbcType=BIGINT},  #{item.organSign,jdbcType=VARCHAR},#{item.messageNoticeId,jdbcType=BIGINT},#{item.employeeId,jdbcType=VARCHAR},
            #{item.state,jdbcType=TINYINT}, #{item.yn,jdbcType=TINYINT}, #{item.customFlag})
        </foreach>
    </insert>


    <select id="getMessageNoticeDrugstoreList" resultType="string">
        select
        DISTINCT organ_sign AS  organSign
        from saas_message_notice_employee
        where  yn = 1
        <if test="customFlag != null">
            and  custom_flag = #{customFlag}
        </if>
        <if test="messageNoticeId != null">
            and  message_notice_id = #{messageNoticeId}
        </if>
        order by id desc
    </select>

    <update id="updateMessageNoticeByEmployeeId">
        update saas_message_notice_employee set state = 0
        where yn = 1 and  state = 1
        <if test="employeeId != null">
            and employee_id = #{employeeId}
        </if>
        <if test="organSign != null">
            and organ_sign = #{organSign}
        </if>
        <if test="list != null">
            and message_notice_id in(
            <foreach collection="list" item="item" index="index" separator=",">
                #{item}
            </foreach>
            )
        </if>
    </update>

    <update id="delByOrganSign">
        update saas_message_notice_employee set yn = 0
        where yn = 1
        <if test="messageNoticeId != null">
            and message_notice_id = #{messageNoticeId}
        </if>
        <if test="organSign != null">
            and organ_sign = #{organSign}
        </if>
    </update>

    <delete id="realDelByMessageNoticeId">
        delete from saas_message_notice_employee
        where message_notice_id = #{messageNoticeId}
    </delete>

    <select id="getMessageNoticeIdList"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from saas_message_notice_employee force index(idx_employee_id)
        where yn = 1
        <if test="drugstoreOrganSign != null and drugstoreOrganSign !=''">
            and organ_sign = #{drugstoreOrganSign}
        </if>
        <if test="employeeId != null">
            and employee_id = #{employeeId}
        </if>
        <if test="readState != null">
            and state = #{readState}
        </if>
    </select>

</mapper>