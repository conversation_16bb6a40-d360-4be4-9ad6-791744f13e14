<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.common.provider.module.dao.SaasDisbleBusinessMapper">

    <resultMap type="com.xyy.common.provider.module.entity.SaasDisableBusinessPo" id="SaasDisbleBusinessMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="businessScope" column="business_scope" jdbcType="INTEGER"/>
        <result property="organSign" column="organ_sign" jdbcType="VARCHAR"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryByOrgan" resultMap="SaasDisbleBusinessMap">
        select
          id, business_scope, organ_sign, update_user, create_time, update_time
        from saas_disble_business
        where organ_sign = #{organSign}
    </select>

    <!--查询单个-->
    <select id="queryById" resultMap="SaasDisbleBusinessMap">
        select
          id, business_scope, organ_sign, update_user, create_time, update_time
        from saas_disble_business
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="SaasDisbleBusinessMap">
        select
          id, business_scope, organ_sign, update_user, create_time, update_time
        from saas_disble_business
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="businessScope != null">
                and business_scope = #{businessScope}
            </if>
            <if test="organSign != null and organSign != ''">
                and organ_sign = #{organSign}
            </if>
            <if test="updateUser != null and updateUser != ''">
                and update_user = #{updateUser}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from saas_disble_business
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="businessScope != null">
                and business_scope = #{businessScope}
            </if>
            <if test="organSign != null and organSign != ''">
                and organ_sign = #{organSign}
            </if>
            <if test="updateUser != null and updateUser != ''">
                and update_user = #{updateUser}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into saas_disble_business(business_scope, organ_sign, update_user, create_time, update_time)
        values (#{businessScope}, #{organSign}, #{updateUser}, #{createTime}, #{updateTime})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into saas_disble_business(business_scope, organ_sign, update_user, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.businessScope}, #{entity.organSign}, #{entity.updateUser}, #{entity.createTime}, #{entity.updateTime})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into saas_disble_business(business_scope, organ_sign, update_user, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.businessScope}, #{entity.organSign}, #{entity.updateUser}, #{entity.createTime}, #{entity.updateTime})
        </foreach>
        on duplicate key update
        business_scope = values(business_scope),
        organ_sign = values(organ_sign),
        update_user = values(update_user),
        create_time = values(create_time),
        update_time = values(update_time)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update saas_disble_business
        <set>
            <if test="businessScope != null">
                business_scope = #{businessScope},
            </if>
            <if test="organSign != null and organSign != ''">
                organ_sign = #{organSign},
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user = #{updateUser},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from saas_disble_business where id = #{id}
    </delete>

    <delete id="deleteByOrgan">
        delete from saas_disble_business where organ_sign = #{organSign}
    </delete>

</mapper>

