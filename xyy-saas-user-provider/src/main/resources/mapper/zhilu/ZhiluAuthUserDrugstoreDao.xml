<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.zhilu.provider.module.dao.ZhiluAuthUserDrugstoreDao">

    <resultMap id="BaseResultMap" type="com.xyy.zhilu.provider.module.entity.ZhiluAuthUserDrugstore">
        <!--@Table zhilu_auth_user_drugstore-->
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="organSign" column="organ_sign" jdbcType="VARCHAR"/>
        <result property="createUserId" column="create_user_id" jdbcType="VARCHAR"/>
        <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
        <result property="modifyUserId" column="modify_user_id" jdbcType="VARCHAR"/>
        <result property="modifyUserName" column="modify_user_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result column="yn" property="yn" jdbcType="TINYINT" />
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
        select
          id, user_id, organ_sign, create_user_id, create_user_name, modify_user_id, modify_user_name, create_time, update_time, yn
        from xyy_saas_user.zhilu_auth_user_drugstore
        where id = #{id}
        and yn = 1
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BaseResultMap">
        select
          id, user_id, organ_sign, create_user_id, create_user_name, modify_user_id, modify_user_name, create_time, update_time, yn
        from xyy_saas_user.zhilu_auth_user_drugstore
        and yn = 1
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="BaseResultMap">
        select
          id, user_id, organ_sign, create_user_id, create_user_name, modify_user_id, modify_user_name, create_time, update_time, yn
        from xyy_saas_user.zhilu_auth_user_drugstore
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="organSign != null and organSign != ''">
                and organ_sign = #{organSign}
            </if>
            <if test="createUserId != null and createUserId != ''">
                and create_user_id = #{createUserId}
            </if>
            <if test="createUserName != null and createUserName != ''">
                and create_user_name = #{createUserName}
            </if>
            <if test="modifyUserId != null and modifyUserId != ''">
                and modify_user_id = #{modifyUserId}
            </if>
            <if test="modifyUserName != null and modifyUserName != ''">
                and modify_user_name = #{modifyUserName}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="yn != null">
                and yn = #{yn}
            </if>
        </where>
    </select>

    <select id="getByUserIdAndOrganSignList" resultMap="BaseResultMap">
        select
        id, user_id, organ_sign, create_user_id, create_user_name, modify_user_id, modify_user_name, create_time, update_time, yn
        from xyy_saas_user.zhilu_auth_user_drugstore
        where user_id = #{userId}
        and organ_sign in
        <foreach collection="organSignList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and yn = 1
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into xyy_saas_user.zhilu_auth_user_drugstore(user_id, organ_sign, create_user_id, create_user_name, modify_user_id, modify_user_name, create_time, update_time, yn)
        values (#{userId}, #{organSign}, #{createUserId}, #{createUserName}, #{modifyUserId}, #{modifyUserName}, #{createTime}, #{updateTime}, #{yn})
    </insert>

    <insert id="batchInsert" keyProperty="id" useGeneratedKeys="true">
        insert into xyy_saas_user.zhilu_auth_user_drugstore(user_id, organ_sign, create_user_id, create_user_name, modify_user_id, modify_user_name, create_time, update_time, yn)
        values
        <foreach collection="list" item="record" separator=",">
            (#{record.userId}, #{record.organSign}, #{record.createUserId}, #{record.createUserName}, #{record.modifyUserId}, #{record.modifyUserName}, #{record.createTime}, #{record.updateTime}, #{record.yn})
        </foreach>
    </insert>

    <insert id="batchInsertOrUpdate" keyProperty="id" useGeneratedKeys="true">
        insert into xyy_saas_user.zhilu_auth_user_drugstore(user_id, organ_sign, create_user_id, create_user_name, modify_user_id, modify_user_name, create_time, update_time, yn)
        values
        <foreach collection="list" item="record" separator=",">
            (#{record.userId}, #{record.organSign}, #{record.createUserId}, #{record.createUserName}, #{record.modifyUserId}, #{record.modifyUserName}, #{record.createTime}, #{record.updateTime}, #{record.yn})
        </foreach>
        ON DUPLICATE KEY UPDATE
        user_id = VALUES(user_id),
        organ_sign = VALUES(organ_sign),
        create_user_id = VALUES(create_user_id),
        create_user_name = VALUES(create_user_name),
        modify_user_id = VALUES(modify_user_id),
        modify_user_name = VALUES(modify_user_name),
        create_time = VALUES(create_time),
        update_time = VALUES(update_time),
        yn = VALUES(yn)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update xyy_saas_user.zhilu_auth_user_drugstore
        <set>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="organSign != null and organSign != ''">
                organ_sign = #{organSign},
            </if>
            <if test="createUserId != null and createUserId != ''">
                create_user_id = #{createUserId},
            </if>
            <if test="createUserName != null and createUserName != ''">
                create_user_name = #{createUserName},
            </if>
            <if test="modifyUserId != null and modifyUserId != ''">
                modify_user_id = #{modifyUserId},
            </if>
            <if test="modifyUserName != null and modifyUserName != ''">
                modify_user_name = #{modifyUserName},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="yn != null">
                yn = #{yn},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from xyy_saas_user.zhilu_auth_user_drugstore where id = #{id}
    </delete>

    <select id="getNameByUserId" resultType="string">
        select d.drugstore_name
        from zhilu_auth_user_drugstore ud
        left join saas_drugstore d on ud.organ_sign = d.organ_sign
        where ud.user_id = #{userId}
        and ud.yn = 1
    </select>

    <select id="getOrganSignByUserId" resultType="string">
        select organ_sign
        from zhilu_auth_user_drugstore
        where user_id = #{userId}
        and yn = 1
    </select>

    <delete id="deleteByUserId">
        delete from xyy_saas_user.zhilu_auth_user_drugstore where user_id = #{userId}
    </delete>

    <update id="deleteByUserIdSoft">
        update xyy_saas_user.zhilu_auth_user_drugstore
        set yn = 0,
        modify_user_id = #{modifyUserId},
        modify_user_name = #{modifyUserName},
        update_time = #{updateTime}
        where user_id = #{userId}
    </update>

    <delete id="deleteByOrganSignList">
        delete from xyy_saas_user.zhilu_auth_user_drugstore
        where organ_sign in
        <foreach collection="organSignList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>

    <update id="deleteByOrganSignListSoft">
        update xyy_saas_user.zhilu_auth_user_drugstore
        set yn = 0,
        modify_user_id = #{modifyUserId},
        modify_user_name = #{modifyUserName},
        update_time = #{time}
        where organ_sign in
        <foreach collection="organSignList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="userId != null and userId != ''">
            and user_id = #{userId}
        </if>
    </update>

</mapper>