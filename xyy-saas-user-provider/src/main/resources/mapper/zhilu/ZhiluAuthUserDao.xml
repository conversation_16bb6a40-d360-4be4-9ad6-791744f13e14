<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.zhilu.provider.module.dao.ZhiluAuthUserDao">

    <resultMap id="BaseResultMap" type="com.xyy.zhilu.provider.module.entity.ZhiluAuthUser">
        <!--@Table zhilu_auth_user-->
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="userRealName" column="user_real_name" jdbcType="VARCHAR"/>
        <result property="userNickname" column="user_nickname" jdbcType="VARCHAR"/>
        <result property="userAccount" column="user_account" jdbcType="VARCHAR"/>
        <result property="telephone" column="telephone" jdbcType="VARCHAR"/>
        <result property="createUserId" column="create_user_id" jdbcType="VARCHAR"/>
        <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
        <result property="modifyUserId" column="modify_user_id" jdbcType="VARCHAR"/>
        <result property="modifyUserName" column="modify_user_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result column="yn" property="yn" jdbcType="TINYINT" />
    </resultMap>

    <sql id="colAll">
        id, user_id, user_real_name, user_nickname, user_account, telephone, create_user_id, create_user_name, modify_user_id, modify_user_name, create_time, update_time, yn
    </sql>

    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
        select
          id, user_id, user_real_name, user_nickname, user_account, telephone, create_user_id, create_user_name, modify_user_id, modify_user_name, create_time, update_time, yn
        from xyy_saas_user.zhilu_auth_user
        where id = #{id}
        and yn = 1
    </select>

    <select id="queryByUserId" resultMap="BaseResultMap">
        select
          id, user_id, user_real_name, user_nickname, user_account, telephone, create_user_id, create_user_name, modify_user_id, modify_user_name, create_time, update_time, yn
        from xyy_saas_user.zhilu_auth_user
        where user_id = #{userId}
        and yn = 1
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BaseResultMap">
        select
          id, user_id, user_real_name, user_nickname, user_account, telephone, create_user_id, create_user_name, modify_user_id, modify_user_name, create_time, update_time, yn
        from xyy_saas_user.zhilu_auth_user
        where yn = 1
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="BaseResultMap">
        select
          id, user_id, user_real_name, user_nickname, user_account, telephone, create_user_id, create_user_name, modify_user_id, modify_user_name, create_time, update_time, yn
        from xyy_saas_user.zhilu_auth_user
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="userRealName != null and userRealName != ''">
                and user_real_name = #{userRealName}
            </if>
            <if test="userNickname != null and userNickname != ''">
                and user_nickname = #{userNickname}
            </if>
            <if test="userAccount != null and userAccount != ''">
                and user_account = #{userAccount}
            </if>
            <if test="telephone != null and telephone != ''">
                and telephone = #{telephone}
            </if>
            <if test="createUserId != null and createUserId != ''">
                and create_user_id = #{createUserId}
            </if>
            <if test="createUserName != null and createUserName != ''">
                and create_user_name = #{createUserName}
            </if>
            <if test="modifyUserId != null and modifyUserId != ''">
                and modify_user_id = #{modifyUserId}
            </if>
            <if test="modifyUserName != null and modifyUserName != ''">
                and modify_user_name = #{modifyUserName}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="yn != null">
                and yn = #{yn}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into xyy_saas_user.zhilu_auth_user(user_id, user_real_name, user_nickname, user_account, telephone, create_user_id, create_user_name, modify_user_id, modify_user_name, create_time, update_time, yn)
        values (#{userId}, #{userRealName}, #{userNickname}, #{userAccount}, #{telephone}, #{createUserId}, #{createUserName}, #{modifyUserId}, #{modifyUserName}, #{createTime}, #{updateTime}, #{yn})
    </insert>

    <insert id="insertOrUpdate">
        insert into xyy_saas_user.zhilu_auth_user(user_id, user_real_name, user_nickname, user_account, telephone, create_user_id, create_user_name, modify_user_id, modify_user_name, create_time, update_time, yn)
        values (#{userId}, #{userRealName}, #{userNickname}, #{userAccount}, #{telephone}, #{createUserId}, #{createUserName}, #{modifyUserId}, #{modifyUserName}, #{createTime}, #{updateTime}, #{yn})
        ON DUPLICATE KEY UPDATE
        user_id = VALUES(user_id),
        user_real_name = VALUES(user_real_name),
        user_nickname = VALUES(user_nickname),
        user_account = VALUES(user_account),
        telephone = VALUES(telephone),
        create_user_id = VALUES(create_user_id),
        create_user_name = VALUES(create_user_name),
        modify_user_id = VALUES(modify_user_id),
        modify_user_name = VALUES(modify_user_name),
        create_time = VALUES(create_time),
        update_time = VALUES(update_time),
        yn = VALUES(yn)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update xyy_saas_user.zhilu_auth_user
        <set>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="userRealName != null and userRealName != ''">
                user_real_name = #{userRealName},
            </if>
            <if test="userNickname != null and userNickname != ''">
                user_nickname = #{userNickname},
            </if>
            <if test="userAccount != null and userAccount != ''">
                user_account = #{userAccount},
            </if>
            <if test="telephone != null and telephone != ''">
                telephone = #{telephone},
            </if>
            <if test="createUserId != null and createUserId != ''">
                create_user_id = #{createUserId},
            </if>
            <if test="createUserName != null and createUserName != ''">
                create_user_name = #{createUserName},
            </if>
            <if test="modifyUserId != null and modifyUserId != ''">
                modify_user_id = #{modifyUserId},
            </if>
            <if test="modifyUserName != null and modifyUserName != ''">
                modify_user_name = #{modifyUserName},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="yn != null">
                yn = #{yn},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from xyy_saas_user.zhilu_auth_user where id = #{id}
    </delete>

    <select id="getUserBind" parameterType="string" resultMap="BaseResultMap">
        select <include refid="colAll"/>
        from zhilu_auth_user
        where yn = 1
        <if test="mixQuery != null and mixQuery != ''">
            AND (instr(user_account,#{mixQuery})>0 or instr(user_real_name,#{mixQuery})>0)
        </if>
        order by update_time desc
    </select>

    <delete id="deleteByUserId">
        delete from xyy_saas_user.zhilu_auth_user where user_id = #{userId}
    </delete>

    <update id="deleteByUserIdSoft">
        update xyy_saas_user.zhilu_auth_user
        set yn = 0,
        modify_user_id = #{modifyUserId},
        modify_user_name = #{modifyUserName},
        update_time = #{updateTime}
        where user_id = #{userId}
    </update>
</mapper>