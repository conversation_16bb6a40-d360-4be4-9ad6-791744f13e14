<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.zhilu.provider.module.dao.ZhiluDrugstoreDao">

    <resultMap id="BaseResultMap" type="com.xyy.zhilu.provider.module.entity.ZhiluDrugstore">
        <!--@Table zhilu_drugstore-->
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="organSign" column="organ_sign" jdbcType="VARCHAR"/>
        <result property="classification" column="classification" jdbcType="INTEGER"/>
        <result property="joinOrganSign" column="join_organ_sign" jdbcType="VARCHAR"/>
        <result property="createUserId" column="create_user_id" jdbcType="VARCHAR"/>
        <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
        <result property="modifyUserId" column="modify_user_id" jdbcType="VARCHAR"/>
        <result property="modifyUserName" column="modify_user_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result column="yn" property="yn" jdbcType="TINYINT" />
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="BaseResultMap">
        select
          id, organ_sign, classification, join_organ_sign, create_user_id, create_user_name, modify_user_id, modify_user_name, create_time, update_time, yn
        from xyy_saas_user.zhilu_drugstore
        where id = #{id}
    </select>

    <select id="queryByIds" resultMap="BaseResultMap">
        select
        id, organ_sign, classification, join_organ_sign, create_user_id, create_user_name, modify_user_id, modify_user_name, create_time, update_time, yn
        from xyy_saas_user.zhilu_drugstore
        where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BaseResultMap">
        select
          id, organ_sign, classification, join_organ_sign, create_user_id, create_user_name, modify_user_id, modify_user_name, create_time, update_time, yn
        from xyy_saas_user.zhilu_drugstore
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="BaseResultMap">
        select
          id, organ_sign, classification, join_organ_sign, create_user_id, create_user_name, modify_user_id, modify_user_name, create_time, update_time, yn
        from xyy_saas_user.zhilu_drugstore
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="organSign != null and organSign != ''">
                and organ_sign = #{organSign}
            </if>
            <if test="classification != null">
                and classification = #{classification}
            </if>
            <if test="joinOrganSign != null and joinOrganSign != ''">
                and join_organ_sign = #{joinOrganSign}
            </if>
            <if test="createUserId != null and createUserId != ''">
                and create_user_id = #{createUserId}
            </if>
            <if test="createUserName != null and createUserName != ''">
                and create_user_name = #{createUserName}
            </if>
            <if test="modifyUserId != null and modifyUserId != ''">
                and modify_user_id = #{modifyUserId}
            </if>
            <if test="modifyUserName != null and modifyUserName != ''">
                and modify_user_name = #{modifyUserName}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="yn != null">
                and yn = #{yn}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into xyy_saas_user.zhilu_drugstore(organ_sign, classification, join_organ_sign, create_user_id, create_user_name, modify_user_id, modify_user_name, create_time, update_time, yn)
        values (#{organSign}, #{classification}, #{joinOrganSign}, #{createUserId}, #{createUserName}, #{modifyUserId}, #{modifyUserName}, #{createTime}, #{updateTime}, #{yn})
    </insert>

    <insert id="batchInsert">
        insert into xyy_saas_user.zhilu_drugstore(organ_sign, classification, join_organ_sign, create_user_id, create_user_name, modify_user_id, modify_user_name, create_time, update_time, yn)
        values
        <foreach collection="list" item="record" separator=",">
            (#{record.organSign}, #{record.classification}, #{record.joinOrganSign}, #{record.createUserId}, #{record.createUserName}, #{record.modifyUserId}, #{record.modifyUserName}, #{record.createTime}, #{record.updateTime}, #{record.yn})
        </foreach>
    </insert>

    <insert id="batchInsertOrUpdate">
        insert into xyy_saas_user.zhilu_drugstore(organ_sign, classification, join_organ_sign, create_user_id, create_user_name, modify_user_id, modify_user_name, create_time, update_time, yn)
        values
        <foreach collection="list" item="record" separator=",">
            (#{record.organSign}, #{record.classification}, #{record.joinOrganSign}, #{record.createUserId}, #{record.createUserName}, #{record.modifyUserId}, #{record.modifyUserName}, #{record.createTime}, #{record.updateTime}, #{record.yn})
        </foreach>
        ON DUPLICATE KEY UPDATE
        organ_sign = VALUES(organ_sign),
        classification = VALUES(classification),
        join_organ_sign = VALUES(join_organ_sign),
        create_user_id = VALUES(create_user_id),
        create_user_name = VALUES(create_user_name),
        modify_user_id = VALUES(modify_user_id),
        modify_user_name = VALUES(modify_user_name),
        create_time = VALUES(create_time),
        update_time = VALUES(update_time),
        yn = VALUES(yn)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update xyy_saas_user.zhilu_drugstore
        <set>
            <if test="organSign != null and organSign != ''">
                organ_sign = #{organSign},
            </if>
            <if test="classification != null">
                classification = #{classification},
                join_organ_sign = #{joinOrganSign},
            </if>
            <if test="createUserId != null and createUserId != ''">
                create_user_id = #{createUserId},
            </if>
            <if test="createUserName != null and createUserName != ''">
                create_user_name = #{createUserName},
            </if>
            <if test="modifyUserId != null and modifyUserId != ''">
                modify_user_id = #{modifyUserId},
            </if>
            <if test="modifyUserName != null and modifyUserName != ''">
                modify_user_name = #{modifyUserName},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="yn != null">
                yn = #{yn},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from xyy_saas_user.zhilu_drugstore where id = #{id}
    </delete>

    <delete id="deleteByIds">
        delete from xyy_saas_user.zhilu_drugstore
        where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>

    <update id="deleteByIdsSoft">
        update xyy_saas_user.zhilu_drugstore
        set yn = 0,
        modify_user_id = #{modifyUserId},
        modify_user_name = #{modifyUserName},
        update_time = #{time}
        where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <!-- 关联查询 -->

    <resultMap id="ZhiluDrugstoreDtoResultMap" type="com.xyy.zhilu.dto.ZhiluDrugstoreDto">
        <!--@Table zhilu_drugstore-->
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="organSign" column="organ_sign" jdbcType="VARCHAR"/>
        <result property="drugstoreName" column="drugstore_name" jdbcType="VARCHAR"/>
        <result property="classification" column="classification" jdbcType="INTEGER"/>
        <result property="province" column="province" jdbcType="VARCHAR"/>
        <result property="city" column="city" jdbcType="VARCHAR"/>
        <result property="area" column="area" jdbcType="VARCHAR"/>
        <result property="address" column="address" jdbcType="VARCHAR"/>
        <result property="rootOrganSign" column="root_organ_sign" jdbcType="VARCHAR"/>
        <result property="joinOrganSign" column="join_organ_sign" jdbcType="VARCHAR"/>
        <result property="createUserId" column="create_user_id" jdbcType="VARCHAR"/>
        <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="getZhiluDrugstore" resultMap="ZhiluDrugstoreDtoResultMap">
        select
        Z.id, Z.organ_sign, S.drugstore_name, Z.classification, S.province, S.city, S.area, S.address, S.headquarters_organ_sign as root_organ_sign, Z.join_organ_sign, Z.create_user_id, Z.create_user_name, Z.create_time, Z.update_time
        from zhilu_drugstore Z
        left join saas_drugstore S on Z.organ_sign = S.organ_sign
        where Z.yn = 1
        <if test="mixQuery != null and mixQuery != ''">
            and (instr(S.drugstore_name, #{mixQuery})>0 or instr(S.organ_sign,#{mixQuery})>0)
        </if>
        <if test="classification != null">
            and Z.classification = #{classification}
        </if>
        <if test="areaCode != null and areaCode != ''">
            and S.area_code like CONCAT(#{areaCode},"%")
        </if>
        order by Z.update_time desc
    </select>

    <select id="getZhiluDrugstoreOrganSign" resultType="java.lang.String">
        select Z.organ_sign
        from zhilu_drugstore Z
        left join saas_drugstore S on Z.organ_sign = S.organ_sign
        where Z.yn = 1
        <if test="mixQuery != null and mixQuery != ''">
            and (instr(S.drugstore_name, #{mixQuery})>0 or instr(S.organ_sign,#{mixQuery})>0)
        </if>
        <if test="classification != null">
            and Z.classification = #{classification}
        </if>
        <if test="areaCode != null and areaCode != ''">
            and S.area_code like CONCAT(#{areaCode},"%")
        </if>
        order by Z.update_time desc
    </select>

    <select id="selectDrugstoreByOrganSignList" resultType="com.xyy.zhilu.dto.ZhiluJoinOrRootOrganSignDto">
        select organ_sign, drugstore_name
        from saas_drugstore
        where organ_sign in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="listSaasChainOrganSign" resultType="com.xyy.zhilu.dto.ZhiluJoinOrRootOrganSignDto">
        select organ_sign, drugstore_name
        from saas_drugstore
        where organ_sign_type = 3
        <if test="chainOrganSignName != null and chainOrganSignName != ''">
            and drugstore_name like CONCAT("%", #{chainOrganSignName},"%")
        </if>
    </select>

    <select id="listSaasOrganSign" resultType="com.xyy.zhilu.dto.SaasDrugstore4ZhiluDto">
        select
        S.organ_sign, S.drugstore_name, S.province, S.city, S.area, S.address, S.headquarters_organ_sign as root_organ_sign
        from saas_drugstore S
        left join zhilu_drugstore Z on Z.organ_sign = S.organ_sign
        where S.organ_sign_type = 1
        and (Z.yn = 0 or Z.yn is null)
        <if test="mixQuery != null and mixQuery != ''">
            and (instr(S.drugstore_name, #{mixQuery})>0 or instr(S.organ_sign,#{mixQuery})>0)
        </if>
        <if test="rootOrganSign != null and rootOrganSign != ''">
            and S.headquarters_organ_sign = #{rootOrganSign}
        </if>
        <if test="areaCode != null and areaCode != ''">
            and S.area_code like CONCAT(#{areaCode},"%")
        </if>
        order by S.id
    </select>

    <select id="getSaasOrganSign" resultType="java.lang.String">
        select S.organ_sign
        from saas_drugstore S
        left join zhilu_drugstore Z on Z.organ_sign = S.organ_sign
        where S.organ_sign_type = 1
        and (Z.yn = 0 or Z.yn is null)
        <if test="mixQuery != null and mixQuery != ''">
            and (instr(S.drugstore_name, #{mixQuery})>0 or instr(S.organ_sign,#{mixQuery})>0)
        </if>
        <if test="rootOrganSign != null and rootOrganSign != ''">
            and S.headquarters_organ_sign = #{rootOrganSign}
        </if>
        <if test="areaCode != null and areaCode != ''">
            and S.area_code like CONCAT(#{areaCode},"%")
        </if>
        order by S.id
    </select>

    <select id="existOrgan" parameterType="string" resultType="long">
        select id
        from zhilu_drugstore
        where organ_sign = #{organSign}
        and yn = 1
    </select>

    <select id="queryAuthorizationOrganSign" resultType="java.lang.String">
        select organ_sign
        from zhilu_auth_user_drugstore
        where yn = 1
        <if test="userId != null and userId != ''">
            and user_id = #{userId}
        </if>
    </select>
</mapper>