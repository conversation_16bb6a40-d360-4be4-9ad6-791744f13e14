<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <!-- 数据库驱动-->
    <classPathEntry location="xyy-saas-user-provider/src/lib/mysql-connector-java-5.1.28-bin.jar"/>

    <context id="generator" defaultModelType="flat" targetRuntime="MyBatis3Simple">

        <!-- 格式化java代码 -->
        <property name="javaFormatter" value="org.mybatis.generator.api.dom.DefaultJavaFormatter"/>

        <!-- 格式化XML代码 -->
        <property name="xmlFormatter" value="org.mybatis.generator.api.dom.DefaultXmlFormatter"/>

        <property name="javaFileEncoding" value="UTF-8"/>

        <plugin type="org.mybatis.generator.plugins.SerializablePlugin" />
        <!-- 重写注释接口，实现中文注释-->
        <commentGenerator type="com.xyy.user.provider.common.util.CommentGenerator">
            <property name="suppressDate" value="true" />
            <!-- 是否去除自动生成的注释
             true：是 ： false:否 -->
            <property name="suppressAllComments" value="true" />
        </commentGenerator>

        <!--数据库链接URL，用户名、密码 -->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="********************************************************************************************************************************************************************************************"
                        userId="superxyy" password="superxyy123" />

        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <!-- 生成模型的包名和位置-->
        <javaModelGenerator targetPackage="com.xyy.user.provider.module.entity" targetProject="xyy-saas-user-provider/src/main/java">
            <property name="enableSubPackages" value="false"/>
            <property name="trimStrings" value="false"/>
        </javaModelGenerator>

        <!-- targetPackage 和 targetProject：生成的 mapper 文件的包和位置 -->
        <sqlMapGenerator targetPackage="mapper" targetProject="xyy-saas-user-provider/src/main/resources">
            <!-- 针对数据库的一个配置，是否把 schema 作为字包名 -->
            <property name="enableSubPackages" value="false" />
        </sqlMapGenerator>

        <!-- 生成DAO的包名和位置-->
        <javaClientGenerator type="XMLMAPPER" targetPackage="com.xyy.user.provider.module.dao" targetProject="xyy-saas-user-provider/src/main/java">
            <property name="enableSubPackages" value="false"/>
        </javaClientGenerator>

        <!-- 要生成的表 tableName是数据库中的表名或视图名 domainObjectName是实体类名-->
        <table tableName="saas_certificate_type" domainObjectName="SaasCertificateType"
               enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false"
               enableSelectByExample="false" selectByExampleQueryId="false">
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
        </table>
        <table tableName="saas_certificate_business_scope" domainObjectName="SaasCertificateBusinessScope"
               enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false"
               enableSelectByExample="false" selectByExampleQueryId="false">
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
        </table>
        <table tableName="saas_purchaser_business_scope" domainObjectName="SaasPurchaserBusinessScope"
               enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false"
               enableSelectByExample="false" selectByExampleQueryId="false">
            <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
        </table>
    </context>
</generatorConfiguration>