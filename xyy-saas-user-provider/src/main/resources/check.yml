# dev环境
dev:
  # 待检测的本服务器的端口：
  # 1: 网关服务：请填本服务的http端口
  # 2: ms服务：请填写本dubbo服务的端口，如果有多个，可以填多个dubbo端口
  check_port:
    - 20880
  # 最大检测时间，单位s， 如果大于check_max， 没反应，就代表启动失败，停止继续部署
  check_max: 30
  # 检测的间隔，单位s
  check_interval: 3

test:
  check_port:
    - 20880
  check_max: 30
  check_interval: 2
  
test2:
  check_port:
    - 20880
  check_max: 30
  check_interval: 2
  
test3:
  check_port:
    - 20880
  check_max: 30
  check_interval: 2

bench:
  check_port:
    - 20880
  check_max: 30
  check_interval: 2


prod:
  check_port:
    - 20880
  check_max: 30
  check_interval: 2

prod2:
  check_port:
    - 20880
  check_max: 30
  check_interval: 2