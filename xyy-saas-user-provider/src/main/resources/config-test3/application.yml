server:
  port: 9091

# dubbo配置
dubbo:
  application:
    name: user
    owner: uzdz
    version: 1.0.0
  config:
    multiple: true
  registries:
    common:
      protocol: zookeeper
      address: zk1-test.zookeeper.ybm100.top:2181,zk2-test.zookeeper.ybm100.top:2181,zk3-test.zookeeper.ybm100.top:2181
      check: false
      default: false
    middlestage:
      id: middlestage
      protocol: zookeeper
      address: zk1-test.zookeeper.ybm100.top:2181,zk2-test.zookeeper.ybm100.top:2181,zk3-test.zookeeper.ybm100.top:2181
      check: false
      default: false
    user:
      protocol: zookeeper
      address: zk301-base-test-bj2.ybm100.top:2181,zk302-base-test-bj2.ybm100.top:2181,zk303-base-test-bj2.ybm100.top:2181
      check: false
      default: true
  protocol: # 用dubbo协议在22880端口暴露服务
    id: dubbo
    name: dubbo
    port: 20880
    threadpool: fixed
    threads: 300
  consumer:
    timeout: 600000
    check: false
  provider:
    timeout: 600000
    retries: -1
  service:
    delay: 5000

management:
  security:
    enabled: false
  health:
    dubbo:
      status:
        extras: load,threadpool

# spring配置
spring:
  http:
    multipart:
      enabled: true
      max-file-size: 3MB
      max-request-size: 3MB
  datasource:
    druid:
      filters: config,stat
      connection-properties: config.decrypt=true;config.decrypt.key=${spring.datasource.key}
      max-active: 50
      initial-size: 5
      max-wait: 18000
      min-idle: 5
      time-between-eviction-runs-millis: 2000
      min-evictable-idle-time-millis: 300000
      validation-query: select 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      keep-alive: true
#    url: jdbc:mysql://************:3307/xyy_saas_user?createDatabaseIfNotExist=true&useUnicode=true&characterEncoding=utf8&autoReconnect=true&allowMultiQueries=true&zeroDateTimeBehavior=convertToNull
#    # username: superxyy
#    # password: superxyy123
#    type: com.alibaba.druid.pool.DruidDataSource
#    driverClassName: com.mysql.jdbc.Driver
#    druid:
#      filters: config,stat
#      connection-properties: config.decrypt=true;config.decrypt.key=${spring.datasource.key}
#      max-active: 50
#      initial-size: 5
#      max-wait: 18000
#      min-idle: 5
#      time-between-eviction-runs-millis: 2000
#      min-evictable-idle-time-millis: 300000
#      validation-query: select 1
#      test-while-idle: true
#      test-on-borrow: false
#      test-on-return: false
#      keep-alive: true
  mvc:
    static-path-pattern: /static/**
  redis:
    # redis数据库索引（默认为0）
    database: 0
    # redis服务器地址（默认为localhost）
    host: db1-saas-test.redis.ybm100.top
    # redis端口（默认为6379）
    port: 51001
    # redis访问密码（默认为空）
    password: I2yUjDtmpC49DovW
    # redis连接超时时间（单位为毫秒）
    timeout: 10000
    # redis连接池配置
    pool:
      # 最大可用连接数（默认为8，负数表示无限）
      max-active: -1
      # 最大空闲连接数（默认为8，负数表示无限）
      max-idle: -1
      # 最小空闲连接数（默认为0，该值只有为正数才有作用）
      min-idle: 0
      # 从连接池中获取连接最大等待时间（默认为-1，单位为毫秒，负数表示无限）
      max-wait: -1

# 日志
logging:
  config: classpath:logback-spring.xml
  file: saas-user-provider

# mybatis DAO
mybatis:
  mapper-locations: classpath:/mapper/*.xml
  configuration:
    mapUnderscoreToCamelCase: true
    log-prefix: dao.

# pagehelper 分页
pagehelper:
  autoDialect: true
  closeConn: true
  reasonable: true

rabbitmq:
  config:
    address: rabbitmq1-saas-test-bj2.ybm100.top,rabbitmq2-saas-test-bj2.ybm100.top,rabbitmq3-saas-test-bj2.ybm100.top
    port: 5672
    username: admin
    password: admin123
    virtualHost: /
    connectionTimeout: 6000
  host: rabbitmq1-saas-test-bj2.ybm100.top,rabbitmq2-saas-test-bj2.ybm100.top,rabbitmq3-saas-test-bj2.ybm100.top
  port: 5672
  username: admin
  password: admin123
  virtualHost: /
  MESSAGE_PUSH_USER: message.push.user
  MESSAGE_PUSH_ORGANIZATION: message.push.organization
  MESSAGE_PUSH_MULTICAST: message.push.multicast

rabbitmqssl:
  host: rabbitmq1-saas-test-bj2.ybm100.top,rabbitmq2-saas-test-bj2.ybm100.top,rabbitmq3-saas-test-bj2.ybm100.top
  sslPort: 5671
  username: admin
  password: admin123
  virtualHost: /
  sslCertPassword: saasrabbit
  MESSAGE_PUSH_USER: message.push.user
  MESSAGE_PUSH_ORGANIZATION: message.push.organization
  MESSAGE_PUSH_MULTICAST: message.push.multicast

ybm100:
  auth_url: https://new-www.test.ybm100.com/
  sms_url: http://new-app.test.ybm100.com/
  user_url: http://new-admin.test.ybm100.com/
  ticket_url: http://admin.ybm100.com/
  voucher_url: http://www.ybm100.com/
  voucher_url_intranet: http://new-www.stage.ybm100.com/

# 发送短信验证码配置
xyy:
  saas:
    jointmanagement:
        host: http://www.test.ybm100.com/
        uri: crm/sms/send/dtly/verificationCode
  spring:
    zebra:
      jdbcRef: user,common
      defaultJdbcRef: user
      configManagerType: local
      mapperLocations: mapper/**/*.xml
      user:
        poolType: druid
      common:
        poolType: druid
      group:
        user:
          name[0]: user-n1
          writeWeight[0]: 0
          readWeight[0]: -1
          name[1]: user-n2
          writeWeight[1]: -1
          readWeight[1]: 1
        common:
          name[0]: common-n1
          writeWeight[0]: 0
          readWeight[0]: -1
          name[1]: common-n2
          writeWeight[1]: -1
          readWeight[1]: 1
      ds:
        user:
          url[0]: jdbc:mysql://************:3307/xyy_saas_user?createDatabaseIfNotExist=true&useUnicode=true&characterEncoding=utf8&autoReconnect=true&allowMultiQueries=true&zeroDateTimeBehavior=convertToNull&useSSL=true
          driverClass[0]: com.mysql.jdbc.Driver
          active[0]: true
          username[0]: superxyy
          password[0]: superxyy123
          url[1]: jdbc:mysql://************:3307/xyy_saas_user?createDatabaseIfNotExist=true&useUnicode=true&characterEncoding=utf8&autoReconnect=true&allowMultiQueries=true&zeroDateTimeBehavior=convertToNull&useSSL=true
          driverClass[1]: com.mysql.jdbc.Driver
          active[1]: true
          username[1]: superxyy
          password[1]: superxyy123
        common:
          url[0]: jdbc:mysql://************:3307/xyy_saas_common?useUnicode=true&characterEncoding=utf8&autoReconnect=true&allowMultiQueries=true
          driverClass[0]: com.mysql.jdbc.Driver
          active[0]: true
          username[0]: superxyy
          password[0]: superxyy123
          url[1]: jdbc:mysql://************:3307/xyy_saas_common?useUnicode=true&characterEncoding=utf8&autoReconnect=true&allowMultiQueries=true
          driverClass[1]: com.mysql.jdbc.Driver
          active[1]: true
          username[1]: superxyy
          password[1]: superxyy123
saas:
  cloud:
    url: http://saas-cloud.test.ybm100.com/

login:
  image:
    yituIp: "http://staging.yitutech.com"
    pemPath: "stagingpublic.pem"
    accessId: "15028"
    accessKey : "3248a652a28eca04b0250cf12dd24df2"
    userDefinedContent: "saas"

cat:
  appName: saas-user-provider

ftp:
  serverName: ftp.test.ybm100.top
  port: 2121
  username: ftp-saas-saasexcel
  password: ZaPKwaStm9NjnhHx
  remotePath: /saas
  downLoadUrl: http://downloads.test.ybm100.com/saas/saas3.0-excel
#
#common:
#  datasource:
#    url: jdbc:mysql://************:3307/xyy_saas_common?useUnicode=true&characterEncoding=utf8&autoReconnect=true&allowMultiQueries=true
#    username: superxyy
##    password: superxyy123
#    type: com.alibaba.druid.pool.DruidDataSource
#    driverClassName: com.mysql.jdbc.Driver
#    druid:
#      max-active: 50
#      initial-size: 5
#      max-wait: 60000
#      min-idle: 5
#      keep-alive: true
#      time-between-eviction-runs-millis: 2000
#      min-evictable-idle-time-millis: 300000
#      validation-query: select 1
#      test-while-idle: true
#      test-on-borrow: false
#      test-on-return: false
#      filters: config
#      connection-properties: config.decrypt=true;config.decrypt.key=${spring.datasource.key}

user:
  profile: test
  face:
    database:
      env: saas_group_test
  apollo:
    grayscale:
      onoff: 0
  checkpassword:
    limit: 10
  checkphone:
    limit: 11
  checkidcard:
    limit: 18
  checkname:
    limit: 15
  checknational:
    limit: 10
#  datasource:
#    url: jdbc:mysql://************:3307/xyy_saas_user?createDatabaseIfNotExist=true&useUnicode=true&characterEncoding=utf8&autoReconnect=true&allowMultiQueries=true&zeroDateTimeBehavior=convertToNull
#    # username: superxyy
#    # password: superxyy123
#    type: com.alibaba.druid.pool.DruidDataSource
#    driverClassName: com.mysql.jdbc.Driver
#    druid:
#      filters: config,stat
#      connection-properties: config.decrypt=true;config.decrypt.key=${spring.datasource.key}
#      max-active: 50
#      initial-size: 5
#      max-wait: 18000
#      min-idle: 5
#      time-between-eviction-runs-millis: 2000
#      min-evictable-idle-time-millis: 300000
#      validation-query: select 1
#      test-while-idle: true
#      test-on-borrow: false
#      test-on-return: false
#      keep-alive: true
      
#删除备份涉及的表
table:
  product:
    productAndInventory: saas_product_baseinfo,saas_inventory,saas_inventory_lot_number,saas_product_stock_detail
    inventory: saas_inventory,saas_inventory_lot_number,saas_product_stock_detail
    providerAndDealAccount: saas_provider_baseinfo,saas_business_deal_account
    notDelete: saas_xyy_approve_record
    dealAccount: saas_business_deal_account
  order:
    unhump: saas_sales_commission,saas_upload_error,saas_ykq_settlement,saas_sales_commission_records,saas_sales_commission_goods,saas_sales_commission_detail
    history: saas_order_info_history
  purchase:
    history: init_purchase_bill
  member:
    unhump: saas_member_exchange_product
    base: saas_member_base
  common:
    dict: saas_system_dict

rocketmq:
  #name-server: mq01-ykq-prod.rocketmq.ybm100.top:9876,mq02-ykq-prod.rocketmq.ybm100.top:9876,mq03-ykq-prod.rocketmq.ybm100.top:9876,mq04-ykq-prod.rocketmq.ybm100.top:9876
  #name-server: 127.0.0.1:9876
  name-server: mq1-test.rocketmq.ybm100.top:9876;mq2-test.rocketmq.ybm100.top:9876;mq3-test.rocketmq.ybm100.top:9876;mq4-test.rocketmq.ybm100.top:9876
  sendMessageTimeout: 3000
  producer:
    group: PID_saas_ykq_user_ms
    topic: saas-ykq_user-ms
    memberSmsSensitiveGroup: PID_member_sms_sensitive_group
    memberSmsSensitiveTopic: member_sms_sensitive_topic
  consumer:
    group: CID_saas_ykq_user_ms
    topic: saas-ykq_user-ms
rocketmq-employee:
  name-server: mq1-test.rocketmq.ybm100.top:9876;mq2-test.rocketmq.ybm100.top:9876;mq3-test.rocketmq.ybm100.top:9876;mq4-test.rocketmq.ybm100.top:9876
  sendMessageTimeout: 3000
  producer:
    group: PID_test_employee_group
    topic: test_employee_topic
  consumer:
    group: CID_test_employee_group
    topic: test_employee_topic
rocketmq-medicalInsurance:
  name-server: mq1-test.rocketmq.ybm100.top:9876;mq2-test.rocketmq.ybm100.top:9876;mq3-test.rocketmq.ybm100.top:9876;mq4-test.rocketmq.ybm100.top:9876
  sendMessageTimeout: 3000
  producer:
    group: PID_test_medical_insurance_group
    topic: test_medical_insurance_topic
  consumer:
    group: CID_test_medical_insurance_group
    topic: test_medical_insurance_topic
rocketmq-changeauthority:
  name-server: mq1-test.rocketmq.ybm100.top:9876;mq2-test.rocketmq.ybm100.top:9876;mq3-test.rocketmq.ybm100.top:9876;mq4-test.rocketmq.ybm100.top:9876
  sendMessageTimeout: 3000
  producer:
    group: PID_test_changeauthority_group
    topic: test_changeauthority_topic
  consumer:
    group: CID_test_changeauthority_group
    topic: test_changeauthority_topic
rocketmq-packageoperationlog:
  name-server: mq1-test.rocketmq.ybm100.top:9876;mq2-test.rocketmq.ybm100.top:9876;mq3-test.rocketmq.ybm100.top:9876;mq4-test.rocketmq.ybm100.top:9876
  sendMessageTimeout: 3000
  producer:
    group: PID_test_packageoperationlog_group
    topic: test_packageoperationlog_topic
  consumer:
    group: CID_test_packageoperationlog_group
    topic: test_packageoperationlog_topic
rocketmq-crm:
  name-server: mq1-test.rocketmq.ybm100.top:9876;mq2-test.rocketmq.ybm100.top:9876;mq3-test.rocketmq.ybm100.top:9876;mq4-test.rocketmq.ybm100.top:9876
  sendMessageTimeout: 3000
  producer:
    group: PID_test_crm_contract_group
    topic: test_crm_contract_topic
  consumer:
    group: CID_test_crm_contract_group
    topic: test_crm_contract_topic
rocketmq-business:
  name-server: mq1-test.rocketmq.ybm100.top:9876;mq2-test.rocketmq.ybm100.top:9876;mq3-test.rocketmq.ybm100.top:9876;mq4-test.rocketmq.ybm100.top:9876
  sendMessageTimeout: 3000
  producer:
    group: PID_test3_businessScope_group
    topic: test3_businessScope_topic
  consumer:
    group: CID_test3_businessScope_group
    topic: test3_businessScope_topic
rocketmq-user:
  name-server: mq1-test.rocketmq.ybm100.top:9876;mq2-test.rocketmq.ybm100.top:9876;mq3-test.rocketmq.ybm100.top:9876;mq4-test.rocketmq.ybm100.top:9876
  sendMessageTimeout: 3000
  producer:
    sync-assa-ybm-account:
      topic: test3_sync-saas-ybm-account_topic
    group: PID_test3_user_init_group
    topic: test3_user_init_topic
    update-user:
      topic: test3_crm_update_customer_topic
    bind-ybm:
      topic: test3_bind_ybm_topic
    drugstore-init:
      topic: test3_drugstore_init_topic
      group: CID_test3_drugstore_init_user_group
    binding-ybm-finish:
      topic: test3_binding_ybm_finish_topic
      group: CID_test3_binding_ybm_finish_user_group
    add-center-gray-menu:
      topic: test3_auto_add_gray_menu_topic
      group: CID_test3_auto_add_gray_menu_user_group
    drugstore-edit:
      topic: test3_drugstore_eidt_sync_purchaser_topic
      group: test3_CID_drugstore_eidt_sync_purchaser_group
rocketmq-ykq:
  name-server: mq1-test.rocketmq.ybm100.top:9876;mq2-test.rocketmq.ybm100.top:9876;mq3-test.rocketmq.ybm100.top:9876;mq4-test.rocketmq.ybm100.top:9876
  sendMessageTimeout: 3000
  #name-server: 127.0.0.1:9876
  producer:
    group: PID_saas_ykq_user_ms
    topic: saas-ykq_user-ms
rocketmq-auto-add-config:
  name-server: mq1-test.rocketmq.ybm100.top:9876;mq2-test.rocketmq.ybm100.top:9876;mq3-test.rocketmq.ybm100.top:9876;mq4-test.rocketmq.ybm100.top:9876
  sendMessageTimeout: 3000
  producer:
    group: PID_test3_auto_add_config_group
    topic: test3_auto_add_config_topic
audit:
  name-server: mq1-test.rocketmq.ybm100.top:9876;mq2-test.rocketmq.ybm100.top:9876;mq3-test.rocketmq.ybm100.top:9876;mq4-test.rocketmq.ybm100.top:9876
  sendMessageTimeout: 3000
  topic: t-xyy-workflow-eventlog_test3
  group: c-user-saas-workflow-eventlog_test3
app:
  id: xyy-saas-user-provider
apollo:
  bootstrap:
    enabled: true
  meta: http://node01-test.appolo.ybm100.top:8080,http://node02-test.appolo.ybm100.top:8080,http://node03-test.appolo.ybm100.top:8080

mqtt:
  instanceId: mqtt-cn-0pp1dq9nc07
  addr: mqtt-cn-0pp1dq9nc07.mqtt.aliyuncs.com
  port: 1883
  ssladdr: mqtt-cn-0pp1dq9nc07.mqtt.aliyuncs.com
  sslport: 8883
  userName:
  pwd:
  clientId:
  groupId: GID_Drugstore_Test
  accessKey: LTAI4FogiMb6vPryht13eRGy
  multicast: SAAS-Drugstore-Test/multicast
  org: SAAS-Drugstore-Test/push2org/
  user: SAAS-Drugstore-Test/push2user/

ecSms:
  privateKey: MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBANKsUDtxw7AcCjS+ru3DJw+22Lw8qhx7MkUBHgqABelNGkJJ2F+******************************/fZj3+d+blbB8stPKlH4LAFLpfv+xDNxbo+Tmgv0c/m3X+ciK99lOANcPj1G0R7i8LbRyxdU7J82zMzQYA7/OOW+wflAgMBAAECgYBBTc61DofFYHu9a9pXs2W5VkmK2Bc3Ku0jPLxuLs8mSZM5NyDOSMfSj89DeYp0ragw9v3/0R+SS7q9tMzVLGhxazOHQXA+SFTSYcZIqs/v+7eCVIIxVGJAkrn2UgbYQfrvNPOaDXglW/M5nsgGAL5bIevANc9XfC5LuS/geOfrAQJBAPxFdKBsxftq9HIl+W5mvArFtWFGJtOEK25yxdEIEz8JUeRhJAOpJZGHzKuG6Dj60oEc5h/pfyGXOUlk16OMBKUCQQDVyXVfCVzeaImkVeh28Q5RgyiJ5CAeV4A/2JSdL0GI64JyXdlJE1YUA+d8ujSPuBVBBxmQx6X8jDlE1ia7XVJBAkEA4P3JIHMiuDMRxOSda5twxmLCe/knE6pjWeQ/DUkYcTYMtZT1m9Y1zW/CWJC/eGIrcD+n9no3imvnCdBIinKMiQJBAJ39M2/4S1lRh+aaViEQI7S5aMv+en3xzCViuPEwaw30fYJnOJx4XbSM7hWOxikvsQVNaedEo7XvKqNkX1s3rAECQAfBVKs1oQWt2GKAr6Z9dq8DGr5XjctYwc6LPRYUaoKuZNX+DfyLMmpBsbI/oq3SgDHmoILayvBYpf24lPgoa3g=
  accessKey: zhl
  valitionCode:
    templateCode: TP95
    sceneCode: TP1227
  registerMerchant:
    templateCode: TP96
    sceneCode: TP1228
  merchantAuditingNotPass:
    templateCode: TP97
    sceneCode: TP1229
  jointVerifyCode:
    templateCode: TP98
    sceneCode: TP1230
#荷叶会员域名
lotus:
  host: https://saasheye3-web.test.ybm100.com