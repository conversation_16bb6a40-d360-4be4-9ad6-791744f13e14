<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.xyy.user</groupId>
    <artifactId>xyy-saas-user-provider</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>jar</packaging>

    <name>xyy-saas-user-provider</name>
    <description>children provider for user model</description>

    <parent>
        <groupId>com.xyy.user</groupId>
        <artifactId>user</artifactId>
        <version>0.0.1-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>2.15.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-to-slf4j</artifactId>
            <version>2.15.0</version>
        </dependency>

        <dependency>
            <groupId>com.xyy.user</groupId>
            <artifactId>xyy-saas-user-api</artifactId>
            <version>0.0.2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>3.10.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
            	<exclusion>
            		<artifactId>jackson-databind</artifactId>
            		<groupId>com.fasterxml.jackson.core</groupId>
            	</exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>dubbo</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>zookeeper</artifactId>
                    <groupId>org.apache.zookeeper</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dubbo</artifactId>
            <version>2.6.2</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
		</dependency>
        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
            <version>3.4.13</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.spring</groupId>
            <artifactId>spring-context-support</artifactId>
            <version>1.0.2</version>
        </dependency>
		<dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <exclusions>
            	<exclusion>
            		<artifactId>jackson-annotations</artifactId>
            		<groupId>com.fasterxml.jackson.core</groupId>
            	</exclusion>
                <exclusion>
                    <artifactId>jackson-core</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
		</dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
        </dependency>

<!--		接入zebra 去掉这个依赖 -->
<!--        <dependency>-->
<!--            <groupId>org.mybatis.spring.boot</groupId>-->
<!--            <artifactId>mybatis-spring-boot-starter</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.xyy.zebra</groupId>
            <artifactId>xyy-zebra-client</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!-- swagger配置界面ui-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot</artifactId>
            <version>2.0.3</version>
        </dependency>

        <!-- swagger2核心库-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <exclusions>
            	<exclusion>
            		<artifactId>jackson-annotations</artifactId>
            		<groupId>com.fasterxml.jackson.core</groupId>
            	</exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>

        <!-- google guava工具包-->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.5</version>
		</dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>2.11.2</version>
        </dependency>


        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>

        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <exclusions>
            	<exclusion>
            		<artifactId>jackson-databind</artifactId>
            		<groupId>com.fasterxml.jackson.core</groupId>
            	</exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>org.mybatis.generator</groupId>
            <artifactId>mybatis-generator-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xyy.saas.common</groupId>
            <artifactId>xyy-saas-common-util</artifactId>
            <exclusions>
            	<exclusion>
            		<artifactId>fastjson</artifactId>
            		<groupId>com.alibaba</groupId>
            	</exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.xyy.saas.common</groupId>
            <artifactId>xyy-saas-common-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
        </dependency>

            <!--去除catr依赖 -->
<!--        <dependency>-->
<!--            <groupId>com.xyy.cat</groupId>-->
<!--            <artifactId>xyy-common-filter</artifactId>-->
<!--            <version>3.2.0-SNAPSHOT</version>-->
<!--        </dependency>-->



        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>1.2.17</version>
        </dependency>


        <dependency>
            <groupId>com.baidu.aip</groupId>
            <artifactId>java-sdk</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.xyy.saas</groupId>-->
<!--            <artifactId>mq</artifactId>-->
<!--            <exclusions>-->
<!--            	<exclusion>-->
<!--            		<artifactId>jackson-databind</artifactId>-->
<!--            		<groupId>com.fasterxml.jackson.core</groupId>-->
<!--            	</exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.xyy.saas.rabbitmq.core</groupId>
            <artifactId>xyy-saas-rabbitmq-api</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.jpush.api</groupId>
            <artifactId>jpush-client</artifactId>
            <version>3.3.6</version>
        </dependency>
        <!-- 采购api -->
		<dependency>
			<groupId>com.xyy.saas.purchase.core</groupId>
			<artifactId>xyy-saas-purchase-api</artifactId>
			<version>0.0.1-SNAPSHOT</version>
            <!--<version>0.0.1-20190930.081018-327</version>-->
			<exclusions>
				<exclusion>
					<artifactId>jackson-annotations</artifactId>
					<groupId>com.fasterxml.jackson.core</groupId>
				</exclusion>
			</exclusions>
		</dependency>
        <!--		订单api-->
		<dependency>
			<groupId>com.xyy.saas.order.core</groupId>
			<artifactId>xyy-saas-order-api</artifactId>
			<version>0.0.3-SNAPSHOT</version>
            <!--<version>0.0.3-20190927.024349-311</version>-->
			<exclusions>
				<exclusion>
					<artifactId>jackson-databind</artifactId>
					<groupId>com.fasterxml.jackson.core</groupId>
				</exclusion>
                <exclusion>
                    <artifactId>jackson-core</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
            </exclusions>
		</dependency>

        <!--会员-->
        <dependency>
            <groupId>com.xyy.saas.member.core</groupId>
            <artifactId>xyy-saas-member-api</artifactId>
            <version>0.0.3-SNAPSHOT</version>
        </dependency>

        <!-- EC商城 api-->
        <dependency>
            <groupId>com.xyy.ec.promotion.business</groupId>
            <artifactId>xyy-ec-promotion-business-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
			<groupId>com.google.code.maven-play-plugin.org.playframework</groupId>
			<artifactId>jj-simplecaptcha</artifactId>
			<version>1.1</version>
		</dependency>
		<dependency>
			<groupId>com.google.code.maven-play-plugin.org.playframework</groupId>
			<artifactId>jj-imaging</artifactId>
			<version>1.1</version>
		</dependency>

        <dependency>
            <groupId>org.xyy.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

		<dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.70</version>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.9.6</version>
        </dependency>

        <dependency>
            <groupId>com.xyy.apollo</groupId>
            <artifactId>xyy-apollo-client</artifactId>
            <version>1.3.1</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-context</artifactId>
            <version>2.0.1.RELEASE</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.xyy.saas.report.forms.core</groupId>-->
<!--            <artifactId>xyy-saas-report-forms-api</artifactId>-->
<!--            <version>0.0.2-SNAPSHOT</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.xyy.xpop</groupId>
            <artifactId>xpop-server-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>3.11.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>javassist</artifactId>
                    <groupId>org.javassist</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-databind</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-core</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.xyy.saas</groupId>
            <artifactId>xyy-saas-workflow-api</artifactId>
            <version>1.6.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.xyy.saas</groupId>
            <artifactId>xyy-saas-workflow-starter</artifactId>
            <version>1.6.0-SNAPSHOT</version>
        </dependency>
        <!-- EC PUSH -->
        <dependency>
            <groupId>com.xyy.ec.push.server</groupId>
            <artifactId>xyy-ec-push-server-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <!-- 平滑发布 -->
        <dependency>
            <groupId>com.xyy.common</groupId>
            <artifactId>xyy-common-dubbo-client</artifactId>
            <version>3.0</version>
        </dependency>

        <dependency>
            <groupId>com.xyy.saas.datasync</groupId>
            <artifactId>saas-base-datasync-api</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>


        <dependency>
            <groupId>com.xyy.saas</groupId>
            <artifactId>xyy-saas-crm-api</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.xyy.saas.dynamic.config</groupId>
            <artifactId>xyy-saas-dynamic-config-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.taobao.trace</groupId>
            <artifactId>taobao-sdk-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xyy.user.center</groupId>
            <artifactId>xyy-saas-user-center-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>jackson-databind</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.xyy.user.center</groupId>
            <artifactId>user-center-common</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.xyy.saas</groupId>
            <artifactId>xyy-saas-crm-api</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>

        <!--   用户中台依赖    -->
        <dependency>
            <groupId>com.xyy.me.user.common.business</groupId>
            <artifactId>xyy-me-user-common-business-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!-- caffeine 本地缓存 -->
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>2.9.3</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <!-- 本地开发环境 -->
            <id>dev</id>
            <properties>
                <profiles.env>dev</profiles.env>
                <activeByDefault>true</activeByDefault>
            </properties>
        </profile>

        <profile>
            <!-- 测试环境 -->
            <id>test</id>
            <properties>
                <profiles.env>test</profiles.env>
            </properties>
        </profile>

        <profile>
            <!-- 生产环境 -->
            <id>prod</id>
            <properties>
                <profiles.env>prod</profiles.env>
            </properties>
        </profile>

        <profile>
            <!-- 预生产环境 -->
            <id>stage</id>
            <properties>
                <profiles.env>stage</profiles.env>
            </properties>
        </profile>

        <profile>
            <!-- 灰度环境 -->
            <id>prod2</id>
            <properties>
                <profiles.env>prod2</profiles.env>
            </properties>
        </profile>

        <profile>
            <!-- 测试灰度环境 -->
            <id>test2</id>
            <properties>
                <profiles.env>test2</profiles.env>
            </properties>
        </profile>

        <profile>
            <!-- 测试灰度环境 -->
            <id>test3</id>
            <properties>
                <profiles.env>test3</profiles.env>
            </properties>
        </profile>

        <profile>
            <!-- 压测环境 -->
            <id>bench</id>
            <properties>
                <profiles.env>bench</profiles.env>
            </properties>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <!-- 资源根目录排除各环境的配置，使用单独的资源目录来指定 -->
                <excludes>
                    <exclude>config-dev/*</exclude>
                    <exclude>config-prod/*</exclude>
                    <exclude>config-test/*</exclude>
                    <exclude>config-stage/*</exclude>
                    <exclude>config-prod2/*</exclude>
                    <exclude>config-test2/*</exclude>
                    <exclude>config-test3/*</exclude>
                </excludes>
            </resource>
            <resource>
                <!-- 根据参数指定资源目录 -->
                <directory>src/main/resources/config-${profiles.env}</directory>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
            </resource>

            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>*.properties</include>
                    <include>**/*.xml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>*.properties</include>
                    <include>**/*.xml</include>
                </includes>
                <filtering>false</filtering>
            </resource>

        </resources>
    </build>
</project>
