package com.xyy.saas.web.api.module.product.model;

import com.xyy.saas.product.core.dto.ProductDto;
import io.swagger.annotations.ApiModel;

import java.io.Serializable;
import java.math.BigDecimal;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

@ApiModel(description = "商品查询实体对象")
public class ProductCommonQueryVo extends ProductDto implements Serializable {

    private BigDecimal stockNum;

    //是否查询库存服务  0 不查询  1查询
    private Integer queryStore;

    /**
     * 是否查询售价调整 0:不查询,1:查询
     */
    private Integer queryAdjust;

    public BigDecimal getStockNum() {
        return stockNum;
    }

    public void setStockNum(BigDecimal stockNum) {
        this.stockNum = stockNum;
    }

    public Integer getQueryStore() {
        return queryStore;
    }

    public void setQueryStore(Integer queryStore) {
        this.queryStore = queryStore;
    }

    public Integer getQueryAdjust() {
        return queryAdjust;
    }

    public void setQueryAdjust(Integer queryAdjust) {
        this.queryAdjust = queryAdjust;
    }
}
