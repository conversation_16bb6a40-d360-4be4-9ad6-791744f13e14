package com.xyy.saas.web.api.module.supplier.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Classname Page
 * @Description 分页
 * @Date 2020/6/4 14:20
 */
@Data
@ToString
public class Page {
    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    @JsonProperty("page")
    private Integer page;

    /**
     * 每页行数
     */
    @ApiModelProperty(value = "每页行数")
    @JsonProperty("rows")
    private Integer rows;
}
