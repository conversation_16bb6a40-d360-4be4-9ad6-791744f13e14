package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T17:19:18.933+08:00")

@ApiModel(description = "商品配伍选择商品回显信息类")
public class ProductCompatibilityVo {

    @JsonProperty("id")
    private Long id;//数据库主键id
    @JsonProperty("pref")
    private String pref;//商品内部编码
    @JsonProperty("pharmacyPref")
    private String pharmacyPref;//商品外码编码（页面展示）
    @JsonProperty("productName")
    private String productName;//商品名称（页面展示）
    @JsonProperty("commonName")
    private String commonName;//通用名（页面展示）
    @JsonProperty("systemType")
    private Integer systemType;//商品类型（类型值，不做展示）
    @JsonProperty("organSign")
    private String organSign;//机构标识
    @JsonProperty("unitId")
    private Integer unitId;//单位id
    @JsonProperty("unitName")
    private String unitName;//单位名称
    @JsonProperty("manufacturer")
    private String manufacturer;//生产厂家
    @JsonProperty("approvalNumber")
    private String approvalNumber; //批准文号
    @JsonProperty("attributeSpecification")
    private String attributeSpecification; //规格/型号
    @JsonProperty("dosageFormId")
    private Long dosageFormId; //剂型ID
    @JsonProperty("productType")
    private Integer productType;//商品功能类型（类型值，不做展示）
    @JsonProperty("drugPermissionPerson")
    private String  drugPermissionPerson; //药品上市许可证持有人
    @ApiModelProperty(value = "药品上市许可证持有人")
    public String getDrugPermissionPerson() {
        return drugPermissionPerson;
    }

    public void setDrugPermissionPerson(String drugPermissionPerson) {
        this.drugPermissionPerson = drugPermissionPerson;
    }

    @ApiModelProperty(value = "处方分类id，需要从处方字典接口获取名称")
    public Integer getPrescriptionClassification() {
        return prescriptionClassification;
    }

    public void setPrescriptionClassification(Integer prescriptionClassification) {
        this.prescriptionClassification = prescriptionClassification;
    }

    @ApiModelProperty(value = "是否医保，0非医保，1医保")
    public Byte getMedicalInsurance() {
        return medicalInsurance;
    }

    public void setMedicalInsurance(Byte medicalInsurance) {
        this.medicalInsurance = medicalInsurance;
    }

    @JsonProperty("prescriptionClassification")
    private Integer prescriptionClassification;
    @JsonProperty("medicalInsurance")
    private Byte medicalInsurance;
    @ApiModelProperty(value = "商品内部编码")
    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    @ApiModelProperty(value = "商品外码编码（页面展示）")
    public String getPharmacyPref() {
        return pharmacyPref;
    }

    public void setPharmacyPref(String pharmacyPref) {
        this.pharmacyPref = pharmacyPref;
    }

    @ApiModelProperty(value = "商品名称（页面展示）")
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    @ApiModelProperty(value = "通用名（页面展示）")
    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    @ApiModelProperty(value = "机构标识")
    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    @ApiModelProperty(value = "数据库主键id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @ApiModelProperty(value = "单位id")
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @ApiModelProperty(value = "单位名称")
    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    @ApiModelProperty(value = "商品类型（类型值，不做展示）")
    public Integer getSystemType() {
        return systemType;
    }

    public void setSystemType(Integer systemType) {
        this.systemType = systemType;
    }

    @ApiModelProperty(value = "商品功能分类（类型值，不做展示）")
    public Integer getProductType() {
        return productType;
    }

    public void setProductType(Integer productType) {
        this.productType = productType;
    }

    @ApiModelProperty(value = "生产厂家")
    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    @ApiModelProperty(value = "批准文号")
    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    @ApiModelProperty(value = "规格型号")
    public String getAttributeSpecification() {
        return attributeSpecification;
    }

    public void setAttributeSpecification(String attributeSpecification) {
        this.attributeSpecification = attributeSpecification;
    }

    @ApiModelProperty(value = "剂型id")
    public Long getDosageFormId() {
        return dosageFormId;
    }

    public void setDosageFormId(Long dosageFormId) {
        this.dosageFormId = dosageFormId;
    }
}
