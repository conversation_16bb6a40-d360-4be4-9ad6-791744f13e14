package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * InventoryLotNumberAdjustVo
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-29T17:03:47.479+08:00")

public class InventoryLotNumberAdjustVo   {
  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("pref")
  private String pref = null;

  @JsonProperty("createUser")
  private String createUser = null;

  @JsonProperty("createTime")
  private Date createTime = null;

  @JsonProperty("updateUser")
  private String updateUser = null;

  @JsonProperty("updateTime")
  private Date updateTime = null;

  @JsonProperty("remark")
  private String remark = null;

  @JsonProperty("status")
  private Integer status = null;

  @JsonProperty("yn")
  private byte yn = 1;

  @JsonProperty("baseVersion")
  private String baseVersion = null;

  @JsonProperty("organsign")
  private String organsign = null;

  @JsonProperty("ticketUser")
  private String ticketUser = null;

  @JsonProperty("ticketTime")
  private String ticketTime = null;

  @JsonProperty("detailVos")
  private List<InventoryLotnumAdjustDetailVo> detailVos = null;

  public InventoryLotNumberAdjustVo id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * 主键id
   * @return id
  **/
  @ApiModelProperty(value = "主键id")


  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public InventoryLotNumberAdjustVo pref(String pref) {
    this.pref = pref;
    return this;
  }

   /**
   * 编号
   * @return pref
  **/
  @ApiModelProperty(value = "编号")


  public String getPref() {
    return pref;
  }

  public void setPref(String pref) {
    this.pref = pref;
  }

  public InventoryLotNumberAdjustVo createUser(String createUser) {
    this.createUser = createUser;
    return this;
  }

   /**
   * 创建人
   * @return createUser
  **/
  @ApiModelProperty(value = "创建人")


  public String getCreateUser() {
    return createUser;
  }

  public void setCreateUser(String createUser) {
    this.createUser = createUser;
  }

  public InventoryLotNumberAdjustVo createTime(Date createTime) {
    this.createTime = createTime;
    return this;
  }

   /**
   * 创建时间
   * @return createTime
  **/
  @ApiModelProperty(value = "创建时间")

  @Valid

  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public InventoryLotNumberAdjustVo updateUser(String updateUser) {
    this.updateUser = updateUser;
    return this;
  }

   /**
   * 更新人
   * @return updateUser
  **/
  @ApiModelProperty(value = "更新人")


  public String getUpdateUser() {
    return updateUser;
  }

  public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
  }

  public InventoryLotNumberAdjustVo updateTime(Date updateTime) {
    this.updateTime = updateTime;
    return this;
  }

   /**
   * 更新时间
   * @return updateTime
  **/
  @ApiModelProperty(value = "更新时间")

  @Valid

  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public InventoryLotNumberAdjustVo remark(String remark) {
    this.remark = remark;
    return this;
  }

   /**
   * 备注
   * @return remark
  **/
  @ApiModelProperty(value = "备注")


  public String getRemark() {
    return remark;
  }

  public void setRemark(String remark) {
    this.remark = remark;
  }

  public InventoryLotNumberAdjustVo status(Integer status) {
    this.status = status;
    return this;
  }

   /**
   * 状态
   * @return status
  **/
  @ApiModelProperty(value = "状态")


  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public InventoryLotNumberAdjustVo yn(byte yn) {
    this.yn = yn;
    return this;
  }

   /**
   * 是否删除
   * @return yn
  **/
  @ApiModelProperty(value = "是否删除")

  public byte getYn() {
    return yn;
  }

  public void setYn(byte yn) {
    this.yn = yn;
  }

  public InventoryLotNumberAdjustVo baseVersion(String baseVersion) {
    this.baseVersion = baseVersion;
    return this;
  }

   /**
   * 版本号
   * @return baseVersion
  **/
  @ApiModelProperty(value = "版本号")


  public String getBaseVersion() {
    return baseVersion;
  }

  public void setBaseVersion(String baseVersion) {
    this.baseVersion = baseVersion;
  }

  public InventoryLotNumberAdjustVo organsign(String organsign) {
    this.organsign = organsign;
    return this;
  }

   /**
   * 机构代码
   * @return organsign
  **/
  @ApiModelProperty(value = "机构代码")


  public String getOrgansign() {
    return organsign;
  }

  public void setOrgansign(String organsign) {
    this.organsign = organsign;
  }

  public InventoryLotNumberAdjustVo ticketUser(String ticketUser) {
    this.ticketUser = ticketUser;
    return this;
  }

   /**
   * 开票员
   * @return ticketUser
  **/
  @ApiModelProperty(value = "开票员")


  public String getTicketUser() {
    return ticketUser;
  }

  public void setTicketUser(String ticketUser) {
    this.ticketUser = ticketUser;
  }

  public InventoryLotNumberAdjustVo ticketTime(String ticketTime) {
    this.ticketTime = ticketTime;
    return this;
  }

   /**
   * 开票时间
   * @return ticketTime
  **/
  @ApiModelProperty(value = "开票时间")


  public String getTicketTime() {
    return ticketTime;
  }

  public void setTicketTime(String ticketTime) {
    this.ticketTime = ticketTime;
  }

  public InventoryLotNumberAdjustVo detailVos(List<InventoryLotnumAdjustDetailVo> detailVos) {
    this.detailVos = detailVos;
    return this;
  }

  public InventoryLotNumberAdjustVo addDetailVosItem(InventoryLotnumAdjustDetailVo detailVosItem) {
    if (this.detailVos == null) {
      this.detailVos = new ArrayList<InventoryLotnumAdjustDetailVo>();
    }
    this.detailVos.add(detailVosItem);
    return this;
  }

   /**
   * 调整明细信息
   * @return detailVos
  **/
  @ApiModelProperty(value = "调整明细信息")

  @Valid

  public List<InventoryLotnumAdjustDetailVo> getDetailVos() {
    return detailVos;
  }

  public void setDetailVos(List<InventoryLotnumAdjustDetailVo> detailVos) {
    this.detailVos = detailVos;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InventoryLotNumberAdjustVo inventoryLotNumberAdjustVo = (InventoryLotNumberAdjustVo) o;
    return Objects.equals(this.id, inventoryLotNumberAdjustVo.id) &&
        Objects.equals(this.pref, inventoryLotNumberAdjustVo.pref) &&
        Objects.equals(this.createUser, inventoryLotNumberAdjustVo.createUser) &&
        Objects.equals(this.createTime, inventoryLotNumberAdjustVo.createTime) &&
        Objects.equals(this.updateUser, inventoryLotNumberAdjustVo.updateUser) &&
        Objects.equals(this.updateTime, inventoryLotNumberAdjustVo.updateTime) &&
        Objects.equals(this.remark, inventoryLotNumberAdjustVo.remark) &&
        Objects.equals(this.status, inventoryLotNumberAdjustVo.status) &&
        Objects.equals(this.yn, inventoryLotNumberAdjustVo.yn) &&
        Objects.equals(this.baseVersion, inventoryLotNumberAdjustVo.baseVersion) &&
        Objects.equals(this.organsign, inventoryLotNumberAdjustVo.organsign) &&
        Objects.equals(this.ticketUser, inventoryLotNumberAdjustVo.ticketUser) &&
        Objects.equals(this.ticketTime, inventoryLotNumberAdjustVo.ticketTime) &&
        Objects.equals(this.detailVos, inventoryLotNumberAdjustVo.detailVos);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, pref, createUser, createTime, updateUser, updateTime, remark, status, yn, baseVersion, organsign, ticketUser, ticketTime, detailVos);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InventoryLotNumberAdjustVo {\n");

    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    pref: ").append(toIndentedString(pref)).append("\n");
    sb.append("    createUser: ").append(toIndentedString(createUser)).append("\n");
    sb.append("    createTime: ").append(toIndentedString(createTime)).append("\n");
    sb.append("    updateUser: ").append(toIndentedString(updateUser)).append("\n");
    sb.append("    updateTime: ").append(toIndentedString(updateTime)).append("\n");
    sb.append("    remark: ").append(toIndentedString(remark)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    yn: ").append(toIndentedString(yn)).append("\n");
    sb.append("    baseVersion: ").append(toIndentedString(baseVersion)).append("\n");
    sb.append("    organsign: ").append(toIndentedString(organsign)).append("\n");
    sb.append("    ticketUser: ").append(toIndentedString(ticketUser)).append("\n");
    sb.append("    ticketTime: ").append(toIndentedString(ticketTime)).append("\n");
    sb.append("    detailVos: ").append(toIndentedString(detailVos)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

