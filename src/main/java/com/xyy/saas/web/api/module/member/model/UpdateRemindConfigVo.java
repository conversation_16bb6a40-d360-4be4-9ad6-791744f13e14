package com.xyy.saas.web.api.module.member.model;

import java.io.Serializable;

public class UpdateRemindConfigVo implements Serializable {

    private static final long serialVersionUID = -41459012650574381L;

    /**
     * 创建会员提醒 1:开启 0关闭
     */
    private Byte memberCreateRemind;

    /**
     * 会员生日提醒 1:开启 0关闭
     */
    private Byte memberBirthdayRemind;

    /**
     * 储值变更提醒 1:开启 0关闭
     */
    private Byte prepaycardHistoryRemind;

    /**
     * 积分变更提醒 1:开启 0关闭
     */
    private Byte pointHistoryRemind;

    /**
     * 会员冻结提醒 1:开启 0关闭
     */
    private Byte memberDisableRemind;

    /**
     * 返券发送短信 1:开启 0关闭
     */
    private Byte sendCouponRemind;

    /**
     * 优惠券短信通知 1:开启 0关闭
     */
    private Byte couponMsgRemind;

    public Byte getMemberCreateRemind() {
        return memberCreateRemind;
    }

    public void setMemberCreateRemind(Byte memberCreateRemind) {
        this.memberCreateRemind = memberCreateRemind;
    }

    public Byte getMemberBirthdayRemind() {
        return memberBirthdayRemind;
    }

    public void setMemberBirthdayRemind(Byte memberBirthdayRemind) {
        this.memberBirthdayRemind = memberBirthdayRemind;
    }

    public Byte getPrepaycardHistoryRemind() {
        return prepaycardHistoryRemind;
    }

    public void setPrepaycardHistoryRemind(Byte prepaycardHistoryRemind) {
        this.prepaycardHistoryRemind = prepaycardHistoryRemind;
    }

    public Byte getPointHistoryRemind() {
        return pointHistoryRemind;
    }

    public void setPointHistoryRemind(Byte pointHistoryRemind) {
        this.pointHistoryRemind = pointHistoryRemind;
    }

    public Byte getMemberDisableRemind() {
        return memberDisableRemind;
    }

    public void setMemberDisableRemind(Byte memberDisableRemind) {
        this.memberDisableRemind = memberDisableRemind;
    }

    public Byte getSendCouponRemind() {
        return sendCouponRemind;
    }

    public void setSendCouponRemind(Byte sendCouponRemind) {
        this.sendCouponRemind = sendCouponRemind;
    }

    public Byte getCouponMsgRemind() {
        return couponMsgRemind;
    }

    public void setCouponMsgRemind(Byte couponMsgRemind) {
        this.couponMsgRemind = couponMsgRemind;
    }
}
