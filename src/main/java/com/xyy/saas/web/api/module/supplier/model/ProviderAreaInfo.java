package com.xyy.saas.web.api.module.supplier.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * saas_provider_area_info
 * <AUTHOR>
@ApiModel(description = "供应商区域信息对象")
public class ProviderAreaInfo implements Serializable {

    private static final long serialVersionUID = 1980484963384843843L;
    @ApiModelProperty(value = "id")
    @JsonProperty("id")
    private Long id;

    /**
     * 供应商基本信息的编码
     */
    @ApiModelProperty(value = "供应商基本信息的编码")
    @JsonProperty("providerPref")
    private String providerPref;

    /**
     * 区域编码
     */
    @ApiModelProperty(value = "区域编码")
    @JsonProperty("areaCode")
    private String areaCode;

    /**
     * 匹配状态 0未匹配 1匹配成功
     */
    @ApiModelProperty(value = "匹配状态 0未匹配 1匹配成功")
    @JsonProperty("matchStatus")
    private Byte matchStatus;

    /**
     * 机构号
     */
    @ApiModelProperty(value = "机构号")
    @JsonProperty("organsign")
    private String organsign;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @JsonProperty("createUser")
    private String createUser;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonProperty("createTime")
    private String createTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @JsonProperty("updateUser")
    private String updateUser;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonProperty("updateTime")
    private String updateTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @JsonProperty("remark")
    private String remark;


    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @JsonProperty("providerName")
    private String providerName;

    /**
     * 注册地址
     */
    @ApiModelProperty(value = "注册地址")
    @JsonProperty("registeredAddress")
    private String registeredAddress;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @JsonProperty("pharmacyPref")
    private String pharmacyPref;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    @JsonProperty("page")
    private Integer page;

    /**
     * 每页行数
     */
    @ApiModelProperty(value = "每页行数")
    @JsonProperty("rows")
    private Integer rows;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getProviderPref() {
        return providerPref;
    }

    public void setProviderPref(String providerPref) {
        this.providerPref = providerPref;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public Byte getMatchStatus() {
        return matchStatus;
    }

    public void setMatchStatus(Byte matchStatus) {
        this.matchStatus = matchStatus;
    }

    public String getOrgansign() {
        return organsign;
    }

    public void setOrgansign(String organsign) {
        this.organsign = organsign;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }


    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getProviderName() {
        return providerName;
    }

    public void setProviderName(String providerName) {
        this.providerName = providerName;
    }

    public String getRegisteredAddress() {
        return registeredAddress;
    }

    public void setRegisteredAddress(String registeredAddress) {
        this.registeredAddress = registeredAddress;
    }

    public String getPharmacyPref() {
        return pharmacyPref;
    }

    public void setPharmacyPref(String pharmacyPref) {
        this.pharmacyPref = pharmacyPref;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    @Override
    public String toString() {
        return "ProviderAreaInfoDto{" +
                "id=" + id +
                ", providerPref='" + providerPref + '\'' +
                ", areaCode='" + areaCode + '\'' +
                ", matchStatus=" + matchStatus +
                ", organsign='" + organsign + '\'' +
                ", createUser='" + createUser + '\'' +
                ", createTime=" + createTime +
                ", updateUser='" + updateUser + '\'' +
                ", updateTime=" + updateTime +
                ", remark='" + remark + '\'' +
                ", providerName='" + providerName + '\'' +
                ", registeredAddress='" + registeredAddress + '\'' +
                ", pharmacyPref='" + pharmacyPref + '\'' +
                ", page=" + page +
                ", rows=" + rows +
                '}';
    }
}