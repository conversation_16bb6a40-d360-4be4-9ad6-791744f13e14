package com.xyy.saas.web.api.common.filter;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.base.Strings;
import com.xyy.cat.util.CatUtil;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.web.api.common.base.GatewayException;
import com.xyy.saas.web.api.common.constants.CacheKeyPreConstant;
import com.xyy.saas.web.api.common.constants.ResultCodeMessage;
import com.xyy.saas.web.api.common.context.ActionContext;
import com.xyy.saas.web.api.common.context.ActionContextSupport;
import com.xyy.saas.web.api.common.enums.ClientEnum;
import com.xyy.saas.web.api.common.log.LogStrategy;
import com.xyy.saas.web.api.common.sign.SignOauthUtil;
import com.xyy.saas.web.api.common.swagger2.SwaggerUrlFilterHelper;
import com.xyy.saas.web.api.module.utils.JedisUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 参数校验过滤器
 */

@Component
public class GlobalParamCheckFilter implements Filter {

    public static Logger logger = LoggerFactory.getLogger(GlobalParamCheckFilter.class);


    /**
     * 无需过滤的url请求
     * */
    @Value("#{'${gateway.no-need-filter.url}'.split(',')}")
    private List NO_NEED_FILTER_URL = null;

    /**
     * 获取启动环境
     * */
    @Value("${gateway.env}")
    public String env = null;

    /**
     * 启动线上环境
     * */
    private static final String PROD_ENV = "prod";

    /**
     * 网关统一请求方式
     */
    private static final String REQUEST_METHOD = "POST";

    @Autowired
    private JedisUtils jedisUtil;

    @Autowired
    private LogStrategy logStrategy;

    @Override
    public void init(FilterConfig filterConfig) {
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        ResultVO result = new ResultVO();
        // 获取请求地址
        String requestURI = request.getRequestURI();
//        List<String> split = Splitter.on("/").trimResults().omitEmptyStrings().limit(2).splitToList(requestURI);
//        logger.info("GlobalParamCheckFilter doFilter start requestURI："+requestURI);
        //允许swagger页面或者通过swagger请求的接口
        if(SwaggerUrlFilterHelper.containsSwaggerUrl(request)){
            filterChain.doFilter(servletRequest, servletResponse);
            return;
        }

        // 无需过滤的URL请求
        if (NO_NEED_FILTER_URL.contains(requestURI)) {
            filterChain.doFilter(request, response);
            return;
        }
        ActionContext current = ActionContextSupport.getCurrent();
        Transaction transaction = CatUtil.initTransaction("params.check.url", requestURI);
        transaction.addData("client param data",current.getClientParams());
        if (!REQUEST_METHOD.equals(request.getMethod())) {
            CatUtil.errorCat(transaction, new GatewayException(ResultCodeMessage.REQUEST_METHOD_ERROR_MESSAGE));
            result.setCode(ResultCodeMessage.REQUEST_METHOD_ERROR_CODE);
            result.setMsg("当前请求方式为: " + request.getMethod() + ", 请求地址为:" + requestURI + ", " + ResultCodeMessage.REQUEST_METHOD_ERROR_MESSAGE);
            current.setResponseResult(result.getMsg());
            response.getWriter().write(JSONUtils.obj2JSON(result));
            return;
        }

        // 判断是否有公共参数 param
        if (current == null) {
            CatUtil.errorCat(transaction, new GatewayException(ResultCodeMessage.COMMON_PARAM_FAIL_MESSAGE));
            result.setCode(ResultCodeMessage.COMMON_PARAM_FAIL_CODE);
            result.setMsg(ResultCodeMessage.COMMON_PARAM_FAIL_MESSAGE);
            current.setResponseResult(ResultCodeMessage.COMMON_PARAM_FAIL_MESSAGE);
            response.getWriter().write(JSONUtils.obj2JSON(result));
            return;
        }


        // 判断公共参数是否上传 sign
        if (Strings.isNullOrEmpty(current.getClientSign())) {
            CatUtil.errorCat(transaction, new GatewayException(ResultCodeMessage.SIGN_NO_PARAM_MESSAGE));
            current.setResponseResult(ResultCodeMessage.SIGN_NO_PARAM_MESSAGE);
            result.setCode(ResultCodeMessage.SIGN_NO_PARAM_CODE);
            result.setMsg(ResultCodeMessage.SIGN_NO_PARAM_MESSAGE);
            response.getWriter().write(JSONUtils.obj2JSON(result));
            return;
        }


        // 判断公共参数是否上传 client
        if (Strings.isNullOrEmpty(current.getClient())) {
            CatUtil.errorCat(transaction, new GatewayException(ResultCodeMessage.CLIENT_NO_PARAM_MESSAGE));
            current.setResponseResult(ResultCodeMessage.CLIENT_NO_PARAM_MESSAGE);
            result.setCode(ResultCodeMessage.CLIENT_NO_PARAM_CODE);
            result.setMsg(ResultCodeMessage.CLIENT_NO_PARAM_MESSAGE);
            response.getWriter().write(JSONUtils.obj2JSON(result));
            return;
        }


        List<String> allClient = ClientEnum.getAllClient();
        if (!allClient.contains(current.getClient())) {
            Cat.logMetricForCount(CacheKeyPreConstant.CLIENT_NO_EXISTS_COUNT);
            CatUtil.errorCat(transaction, new GatewayException(ResultCodeMessage.CLIENT_NO_EXISTS_MESSAGE));
            current.setResponseResult(ResultCodeMessage.CLIENT_NO_EXISTS_MESSAGE);
            result.setCode(ResultCodeMessage.CLIENT_NO_EXISTS_CODE);
            result.setMsg(ResultCodeMessage.CLIENT_NO_EXISTS_MESSAGE);
            response.getWriter().write(JSONUtils.obj2JSON(result));
            return;
        }

        boolean s = SignOauthUtil.checkSign(current.getClientSign(),current,
                ClientEnum.getClientSecretKey(current.getClient()));
//         比较方法签名, 成功则通过拦截器
        if (!s) {
            logger.warn("GlobalParamCheckFilter sign compare fail,{},{},{}", requestURI,current.getClientSign(),current.getServerSign());
            result.setCode(ResultCodeMessage.SIGN_FAIL_ERROR_CODE);
            result.setMsg(ResultCodeMessage.SIGN_FAIL_ERROR_MESSAGE);
            response.getWriter().write(JSONUtils.obj2JSON(result));
            return;
        }

        filterChain.doFilter(servletRequest, servletResponse);
    }



    @Override
    public void destroy() {
    }
}
