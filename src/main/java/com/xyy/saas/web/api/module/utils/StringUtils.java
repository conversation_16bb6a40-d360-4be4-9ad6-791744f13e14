package com.xyy.saas.web.api.module.utils;

import java.io.UnsupportedEncodingException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * @author: uzdz
 * @date: 2018/8/27 11:44
 * @description: Stirng工具类
 */
public class StringUtils extends org.apache.commons.lang3.StringUtils {

    private static final String CHARSET_UTF8 = "utf-8";

    public static boolean areNotEmpty(String key, String value) {
        return isNoneEmpty(key) && isNoneEmpty(value);
    }

    /**
     * 转换为字节数组
     *
     * @param bytes
     * @return
     */
    public static String toString(byte[] bytes) {
        try {
            return new String(bytes, CHARSET_UTF8);
        } catch (UnsupportedEncodingException e) {
            return EMPTY;
        }
    }

    /**
     * 转换为字节数组
     *
     * @param str
     * @return
     */
    public static byte[] getBytes(String str) {
        if (str != null) {
            try {
                return str.getBytes(CHARSET_UTF8);
            } catch (UnsupportedEncodingException e) {
                return null;
            }
        } else {
            return null;
        }
    }

    public static boolean isNotBlank(CharSequence cs) {
        return !isBlank(cs);
    }

    /**
     * 是 null 是 "" 是 "  " 是 "null"
     *
     * @param cs
     * @return
     */
    public static boolean isNullOrEmpty(CharSequence cs) {
        return cs == null || cs.toString().trim().length() == 0 || "null".equals(cs.toString().trim());
    }

    /**
     * 不是 null 不是 "" 不是 "  " 不是 "null"
     *
     * @param cs
     * @return
     */
    public static boolean isNotNullAndEmpty(CharSequence cs) {
        return !isNullOrEmpty(cs);
    }


    /**
     *  将一个字符串通过一个分隔符分成多个字符串，获取第num个子字符串
     * @param str :字符串
     * @param num ： 第num个子字符串
     * @param separator ： 分隔符
     * @return
     */
    public static String getSubStrBySeparator(String str,Integer num,String separator){
        if(StringUtils.isBlank(str)){
            return "";
        }
        String[] strings = str.split(separator);
        if(num>strings.length){
            return "";
        }
        return strings[num-1];
    }

    /**
     * 判断是否包含utf8mb4字符
     * @param s
     * @return
     */
    public static boolean requiresMb4(String s) {
        if (s == null) {
            return false;
        }
        int len = s.length();
        return len != s.codePointCount(0, len);
    }

    /**
     * 过滤掉utf8mb4字符
     * @param input
     * @return
     */
    public static String filterUtf8Mb4(String input) {
        if (input == null) {
            return null;
        }
        // Define the regular expression pattern to match UTF-8 MB4 characters
        // The range U+10000 to U+10FFFF
        String pattern = "[\\x{10000}-\\x{10FFFF}]";

        // Create a Pattern object
        Pattern unicodePattern = Pattern.compile(pattern, Pattern.UNICODE_CASE | Pattern.UNICODE_CHARACTER_CLASS);

        // Use a Matcher to find and replace all occurrences of the pattern
        Matcher matcher = unicodePattern.matcher(input);
        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            // Replace each matched character with an empty string
            matcher.appendReplacement(result, "");
        }
        matcher.appendTail(result);

        return result.toString();
    }

    public static void main(String [] aggs){
        System.out.println(getSubStrBySeparator("12345,3232",3,","));
    }
}
