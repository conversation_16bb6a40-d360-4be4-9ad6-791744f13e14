package com.xyy.saas.web.api.module.product.util;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.dto.ResultCodeEnum;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

public class CommonConsumerFallback {
    /**
     * 降级处理
     *
     * @param name
     * @return
     */
    public static ResponseEntity<ResultVO> fallback(String name) {
        ResultVO<Boolean> resultVO = new ResultVO();
        resultVO.setCode(ResultCodeEnum.ERROR.getCode());
        resultVO.setMsg("请求过于频繁，客户端限流处理,请稍后再试");
        resultVO.setResult(false);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    /**
     * 限流处理
     *
     * @param name
     * @param ex
     * @return
     */
    public static  ResponseEntity<ResultVO> blockHandler( String name) {
        ResultVO<Boolean> resultVO = new ResultVO();
        resultVO.setCode(ResultCodeEnum.ERROR.getCode());
        resultVO.setMsg("请求过于频繁，客户端限流处理,请稍后再试");
        resultVO.setResult(false);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }


}
