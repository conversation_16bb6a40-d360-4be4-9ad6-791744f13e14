package com.xyy.saas.web.api.module.member.controller;


import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.member.core.api.MemberPrepayCardConfigForZBApi;
import com.xyy.saas.member.core.dto.MemberPrepayCardConfigBonusDto;
import com.xyy.saas.member.core.dto.MemberPrepayCardConfigDto;
import com.xyy.saas.member.core.dto.MemberPrepayCardConfigSaveDto;
import com.xyy.saas.member.core.response.ResultVO;
import com.xyy.saas.web.api.module.member.model.MemberPrepayCardConfigBonusVo;
import com.xyy.saas.web.api.module.member.model.MemberPrepayCardConfigQueryVo;
import com.xyy.saas.web.api.module.member.model.MemberPrepayCardConfigSaveVo;
import com.xyy.saas.web.api.module.member.model.MemberPrepayCardConfigVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/member/memberPrepayCard/zb/config")
@Api(value = "memberPrepayCardConfig", description = "会员储值优惠设置总部端API")
public class MemberPreparCardConfigForZBApiController {

    private static final Logger logger = Logger.getLogger(MemberPreparCardConfigForZBApiController.class);

    @Reference(version = "0.0.1")
    private MemberPrepayCardConfigForZBApi memberPrepayCardConfigForZBApi;

    @ApiOperation(value = "获取会员储值设置", notes = "获取会员储值设置", response = MemberPrepayCardConfigVo.class, tags = {"会员储值优惠设置总部端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberPrepayCardConfigVo.class)})
    @RequestMapping(value = "/get", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> get(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                 @RequestBody MemberPrepayCardConfigQueryVo vo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        return new ResponseEntity(memberPrepayCardConfigForZBApi.selectByMemberLevelIdForZB(vo.getMemberLevelId(), commonRequestModel.getOrganSign()), HttpStatus.OK);
    }

    @ApiOperation(value = "保存会员储值设置", notes = "保存会员储值设置", response = Boolean.class, tags = {"会员储值优惠设置总部端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> save(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                  @RequestBody MemberPrepayCardConfigSaveVo vo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String employeeId = commonRequestModel.getEmployeeId();
        List<MemberPrepayCardConfigVo> list = vo.getList();
        List<MemberPrepayCardConfigDto> copyList = new ArrayList<>();
        MemberPrepayCardConfigDto dto;
        if (list != null) {
            for (MemberPrepayCardConfigVo item : list) {
                dto = new MemberPrepayCardConfigDto();
                BeanUtils.copyProperties(item, dto);
                if (dto.getId() == null) {
                    dto.setCreateUser(employeeId + "");
                } else {
                    dto.setUpdateUser(employeeId + "");
                }
                copyList.add(dto);
            }
        }
        MemberPrepayCardConfigSaveDto saveDto = new MemberPrepayCardConfigSaveDto();
        saveDto.setList(copyList);
        saveDto.setDeleteIds(vo.getDeleteIds());
        saveDto.setLevelIds(vo.getLevelIds());
        saveDto.setEmployeeId(employeeId + "");
        saveDto.setOrganSign(commonRequestModel.getOrganSign());
        return new ResponseEntity(ResultVO.createSuccess(memberPrepayCardConfigForZBApi.saveForZB(saveDto)), HttpStatus.OK);
    }

    @ApiOperation(value = "根据会员等级对应的会员储值设置计算赠送金额", notes = "根据会员等级对应的会员储值设置计算赠送金额  \n" +
            "-1: 没有计算出赠送金额  \n" +
            "其他数值: 计算出的赠送金额", response = Integer.class, tags = {"会员储值优惠设置总部端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Integer.class)})
    @RequestMapping(value = "/calcBonus", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> calcBonus(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                       @RequestBody MemberPrepayCardConfigBonusVo vo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        MemberPrepayCardConfigBonusDto dto = new MemberPrepayCardConfigBonusDto();
        BeanUtils.copyProperties(vo, dto);
        dto.setOrganSign(commonRequestModel.getOrganSign());
        return new ResponseEntity(ResultVO.createSuccess(memberPrepayCardConfigForZBApi.calcBonusForZB(dto)), HttpStatus.OK);
    }

}
