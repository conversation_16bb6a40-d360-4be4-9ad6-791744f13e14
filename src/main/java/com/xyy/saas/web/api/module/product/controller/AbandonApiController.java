package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.common.module.api.SysConfigApi;
import com.xyy.common.module.dto.SystemConfigDto;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.inventory.cores.dto.ProductExtendInventoryVo;
import com.xyy.saas.product.control.dto.costprice.CostAdjustsByPrefResultDetailDto;
import com.xyy.saas.product.control.dto.costprice.CostPriceQueryProductDto;
import com.xyy.saas.product.core.api.ProductAbandonApi;
import com.xyy.saas.product.core.dto.*;
import com.xyy.saas.product.core.enums.CommonEnum;
import com.xyy.saas.product.core.enums.ProductStatusEnum;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.api.PermissionApi;
import com.xyy.user.module.dto.result.EmployeeDto;
import com.xyy.user.module.enums.DrugstoreBizModelEnum;
import com.xyy.user.module.enums.DrugstoreTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T19:34:49.364+08:00")

@Slf4j
@Controller
public class AbandonApiController implements AbandonApi {

    @Reference(version = "0.0.1")
    private com.xyy.saas.product.core.api.ProductApi productApi;

    @Reference(version = "0.0.1")
    private EmployeeApi employeeApi;

    @Reference(version = "0.0.1")
    private PermissionApi permissionApi;

    @Reference(version = "0.0.2")
    private com.xyy.saas.inventory.cores.api.InventoryApi inventoryApi;

    @Reference(version = "0.0.1")
    private com.xyy.saas.product.control.api.costprice.ProductCostPriceApi productCostPriceApi;

    @Reference(version = "0.0.1")
    private ProductAbandonApi productAbandonApi;

    @Reference(version = "0.0.1")
    private SysConfigApi sysConfigApi;

    @Override
    public ResponseEntity<ResultVO> abandon(HttpServletRequest request, @RequestBody AbandonProductUpdateDto dto) {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        dto.setOrganSign(organSign);
        dto.setUsername(employee);
        log.info("AbandonApiController#abandon, params:{}", JSON.toJSONString(dto));
        ResultVO<String> resultVO = productAbandonApi.abandon(dto);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> batchAbandon(HttpServletRequest request, @RequestBody ProductDto product) {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        EmployeeDto employeeDto = employeeApi.queryEmployeeById(Integer.valueOf(employee)).getResult();
        log.info("AbandonApiController#batchAbandon, param:{}, organSign:{}, employee:{}, identity:{}", JSON.toJSONString(product), organSign, employee, employeeDto.getIdentity());
        // 要废弃的商品Id列表
        List<Long> idList = new ArrayList<>();

        try {
            // 批量废弃商品时，全选满足查询条件的全部商品
            if (product.getSelectAll() != null && product.getSelectAll()) {
                if (null == product.getUsed()) {
                    product.setUsed(null);
                }
                product.setYn((byte) 1);
                if (null != employeeDto.getIdentity()) {
                    product.setIsHidden(employeeDto.getIdentity());
                }
                if (!StringUtils.isEmpty(product.getCreateTimeStart())) {
                    product.setCreateTimeStart(product.getCreateTimeStart() + " 00:00:00");
                }
                if (!StringUtils.isEmpty(product.getCreateTimeEnd())) {
                    product.setCreateTimeEnd(product.getCreateTimeEnd() + " 23:59:59");
                }
                Integer page = 1;
                Integer rows = 1000;
                product.setPage(page);
                product.setRows(rows);

                //如果是连锁门店，需要获取总部的机构号
                String modelJson = request.getHeader("commonRequestModel");
                Byte bizModel = null;
                if (!StringUtils.isEmpty(modelJson)) {
                    CommonRequestModel model = JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
                    if (DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())) {
                        product.setSelectOrganSign(organSign);//查询价格的机构号
                        organSign = model.getHeadquartersOrganSign();
                    }
                    bizModel = model.getBizModel();
                }
                product.setOrganSign(organSign);

                ResultVO result = this.productApi.productListChain(product, bizModel);
                PageInfo<ProductDto> pageInfo = (PageInfo<ProductDto>) result.getResult();
                if (!CollectionUtils.isEmpty(pageInfo.getList())) {
                    idList.addAll(pageInfo.getList().stream().map(v -> v.getId()).collect(Collectors.toList()));
                }
                while (page < pageInfo.getPages()) {
                    page += 1;
                    product.setPage(page);
                    result = this.productApi.productListChain(product, bizModel);
                    pageInfo = (PageInfo<ProductDto>) result.getResult();
                    if (!CollectionUtils.isEmpty(pageInfo.getList())) {
                        idList.addAll(pageInfo.getList().stream().map(v -> v.getId()).collect(Collectors.toList()));
                    }
                }

            // 批量废弃商品时，手动选择的商品
            } else {
                idList.addAll(product.getIdList());
            }

            // 执行废弃逻辑
            AbandonProductUpdateDto dto = new AbandonProductUpdateDto();
            dto.setOrganSign(organSign);
            dto.setUsername(employee);
            dto.setIds(idList);
            ResultVO<String> resultVO = productAbandonApi.batchAbandon(dto);
            return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);

        } catch (Exception e) {
            log.error("AbandonApiController#batchAbandon error", e);
            return new ResponseEntity<ResultVO>(ResultVO.createError(e.getMessage()), HttpStatus.OK);
        }
    }

    @Override
    public ResponseEntity<ResultVO> pageSearch(HttpServletRequest request, @RequestBody AbandonProductQueryDto queryDto) {
        log.info("AbandonApiController#pageSearch, params:{}", JSON.toJSONString(queryDto));
        long start_time0 = System.currentTimeMillis();
        String organSign = request.getHeader("organSign");
        String currentOrgan = organSign;
        String employee = request.getHeader("employeeId");
        String identity = employeeApi.queryEmployeeById(Integer.valueOf(employee)).getResult().getIdentity()+"";
        log.info("organSign:" + organSign + ",employee:" + employee + ",identity:" + identity);
        Byte isProductHidden = Byte.valueOf(identity);
        Integer page = null;
        Integer rows = null;

        ProductDto product = new ProductDto();
        BeanUtils.copyProperties(queryDto, product);

        // 状态 0-全部、1-已废弃、2-待审核、3-审核驳回
        if (null != queryDto.getStatus()) {
            switch (queryDto.getStatus()) {
                case 1: product.setStatus(ProductStatusEnum.CHECKED.code); break;
                case 2: product.setStatus(ProductStatusEnum.ABANDON_AUDITING.code); break;
                case 3: product.setStatus(ProductStatusEnum.ABANDON_AUDIT_REJECTED.code); break;
                default: product.setStatus(null); break;
            }
        }

        product.setProductName(queryDto.getMixQuery());
        //product.setUsed((byte) 0);
        product.setYn((byte) 0); // 已废弃的
        if ( null == product.getPage() ){
            page = 1;
        }else{
            page = product.getPage();
        }

        if ( null == product.getRows() ){
            rows = 10;
        }else{
            rows = product.getRows();
        }

        //默认查询启用
        if(null != isProductHidden) {
            product.setIsHidden(isProductHidden);
        }

        if(!StringUtils.isEmpty(product.getCreateTimeStart())){
            product.setCreateTimeStart(product.getCreateTimeStart()+" 00:00:00");
        }
        if(!StringUtils.isEmpty(product.getCreateTimeEnd())){
            product.setCreateTimeEnd(product.getCreateTimeEnd()+" 23:59:59");
        }
        product.setPage(page);
        product.setRows(rows);
        //如果是连锁门店，需要获取总部的机构号
        String modelJson = request.getHeader("commonRequestModel");
        Byte bizModel = null;
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                product.setSelectOrganSign(organSign);//查询价格的机构号
                organSign = model.getHeadquartersOrganSign();
            }
            bizModel = model.getBizModel();
        }
        product.setOrganSign(organSign);
        log.info("query product list user api need time:"+(System.currentTimeMillis() - start_time0)+"ms");
        long start_time = System.currentTimeMillis();
        ResultVO result = this.productApi.productListChain(product,bizModel);
        log.info("query product list api need time:"+(System.currentTimeMillis() - start_time)+"ms");

        //连锁和联营直接返回
        if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(bizModel) || DrugstoreBizModelEnum.CHAIN_STORE.toEquals(bizModel)) {
            if(product.getAddPriceFlag() != null && product.getAddPriceFlag() == 1){
                PageInfo info = (PageInfo) result.getResult();
                List<ProductDto> dtos = info.getList();
                if (null != dtos && dtos.size() > 0) {
                    List<String> prefs = new ArrayList<>();
                    dtos.forEach(item -> {
                        prefs.add(item.getPref());
                    });
                    CostPriceQueryProductDto queryProductDto = new CostPriceQueryProductDto();
                    queryProductDto.setChainOrganSign(currentOrgan);//门店机构号
                    queryProductDto.setProductPrefs(prefs);//商品内码集合
                    ResultVO priceResult = productCostPriceApi.getProductPriceInfosByPrefs(queryProductDto);
                    List<CostAdjustsByPrefResultDetailDto> prceList = (List<CostAdjustsByPrefResultDetailDto>) priceResult.getResult();
                    Map<String,CostAdjustsByPrefResultDetailDto> priceMaps = new HashMap<>();
                    if(!CollectionUtils.isEmpty(prceList)){
                        prceList.stream().forEach(priceVo -> {
                            priceMaps.put(priceVo.getProductPref(),priceVo);
                        });
                    }
                    dtos.stream().forEach(priceDto ->{
                        if(priceMaps.get(priceDto.getPref()) != null){
                            priceDto.setPriceUpper(priceMaps.get(priceDto.getPref()).getPriceUpper());//价格上限
                            priceDto.setPriceLower(priceMaps.get(priceDto.getPref()).getPriceLower());//价格下限
                        }
                    });
                }
            }
            return new ResponseEntity<ResultVO>(result,HttpStatus.OK);
        }

        // 单体逻辑
        long start_time1 = System.currentTimeMillis();
        Boolean roleExt = permissionApi.queryProductExtendPermissionByRoleId(Integer.parseInt(employee));
        if (roleExt){
            PageInfo info = (PageInfo) result.getResult();
            List<ProductDto> dtos = info.getList();
            if (null != dtos && dtos.size() > 0){
                List<String> prefs = new ArrayList<>();
                dtos.forEach(item -> {
                    prefs.add(item.getPref());
                });
                //调用库存的接口，扩展属性相关
                List<ProductExtendInventoryVo> extS = inventoryApi.findProductExtendInventoryAll(prefs, organSign);
                Map<String, ProductExtendInventoryVo> map = new HashMap<>();
                extS.forEach(item -> {
                    map.put(item.getProductPref(), item);
                });
                //将商品对应的扩展属性set到商品实体类中
                dtos.forEach(item -> {
                    com.xyy.saas.inventory.core.dto.ProductExtendInventoryVo productExtendInventoryVo = new com.xyy.saas.inventory.core.dto.ProductExtendInventoryVo();
                    ProductExtendInventoryVo vos = map.get(item.getPref());
                    BeanUtils.copyProperties(vos,productExtendInventoryVo);
                    item.setExtProperty(productExtendInventoryVo);
                });
                info.setList(dtos);
                result.setResult(info);
            }
        }

        log.info("query abandon product list inventory api need time:"+(System.currentTimeMillis() - start_time1)+"ms");
        return new ResponseEntity<ResultVO>(result,HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> recover(HttpServletRequest request, @RequestBody AbandonProductUpdateDto dto) {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        dto.setOrganSign(organSign);
        dto.setUsername(employee);
        log.info("AbandonApiController#recover, params:{}", JSON.toJSONString(dto));
        ResultVO<String> resultVO = checkRecoverAuditYn(organSign) ? ResultVO.createError("请重新刷新当前页面") : productAbandonApi.recover(dto);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> recoverApply(HttpServletRequest request, @RequestBody RecoverProductApplyDto dto) {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        // 判断是否【恢复回收站商品时审核】开关开启 - 由前端判断

        dto.setOrganSign(organSign);
        dto.setUsername(employee);
        ResultVO<SaasProductRecoverApplyDto> resultVO = !checkRecoverAuditYn(organSign) ? ResultVO.createError("商品恢复审核开关已关闭，如需审核，请重新设置") : productAbandonApi.recoverApply(dto);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> recoverAudit(HttpServletRequest request, @RequestBody RecoverProductAuditDto dto) {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        dto.setOrganSign(organSign);
        dto.setAuditUserId(Integer.valueOf(employee));
        String employeeName = employeeApi.queryEmployeeById(Integer.valueOf(employee)).getResult().getName();
        dto.setAuditUser(employeeName);
        ResultVO<ProductDto> resultVO = productAbandonApi.recoverAudit(dto);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }


    private boolean checkRecoverAuditYn(String organSign) {
        SystemConfigDto systemConfigDto = sysConfigApi.querySystemConfig(organSign);
        if (systemConfigDto == null) {
            return false;
        }
        return systemConfigDto.getProductRecoverAuditingYn() == CommonEnum.YNEnum.YES.getCode();
    }
}
