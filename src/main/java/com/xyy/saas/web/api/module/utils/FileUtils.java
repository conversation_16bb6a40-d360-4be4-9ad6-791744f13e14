package com.xyy.saas.web.api.module.utils;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.UUID;

public class FileUtils {

    private static final Logger logger = LogManager.getLogger(FileUtils.class);

    /**
     * multipartFile转化为file
     * @param multipartFile
     * @return
     */
    public static File multipartFileToFile(MultipartFile multipartFile) {
        if (multipartFile == null || multipartFile.getSize() <= 0) {
            return null;
        }
        logger.info("multipartFile转化为file, 文件名: " + multipartFile.getOriginalFilename());
        File file = null;
        try(InputStream ins = multipartFile.getInputStream()) {
            file = new File(multipartFile.getOriginalFilename());
            inputStreamToFile(ins, file);
        } catch (IOException e) {
            logger.error("multipartFile转化为file异常, 文件名: " + multipartFile.getOriginalFilename(), e);
        }
        return file;
    }

    /**
     * 通过url下载文件
     * @param fileUrl
     * @return
     */
    public static File downloadFromUrl(String fileUrl) {
        if (StringUtils.isEmpty(fileUrl)) {
            return null;
        }
        logger.info("通过url下载文件, 文件名: " + fileUrl);
        File file = null;
        InputStream ins = null;
        try {
            URL url = new URL(fileUrl);
            HttpURLConnection conn = (HttpURLConnection)url.openConnection();
            //设置超时间为3秒
            conn.setConnectTimeout(3 * 1000);
            conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            file = File.createTempFile(UUID.randomUUID().toString(), fileUrl.substring(fileUrl.lastIndexOf(".")));
            ins = conn.getInputStream();
            inputStreamToFile(ins, file);
            return file;
        } catch (Exception e) {
            logger.error("通过url下载文件异常, 文件名: " + fileUrl, e);
            return null;
        }
    }

    private static void inputStreamToFile(InputStream ins, File file) {
        try(OutputStream os = new FileOutputStream(file)) {
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
        } catch (Exception e) {
            logger.error("生成文件异常", e);
        }
    }
}