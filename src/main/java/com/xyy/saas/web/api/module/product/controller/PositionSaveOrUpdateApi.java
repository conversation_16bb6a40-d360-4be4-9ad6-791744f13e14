/**
 * NOTE: This class is auto generated by the swagger code generator program (2.2.3).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.web.api.module.product.model.SaasPositionVo;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-30T11:33:52.138+08:00")
@RequestMapping("/product")
@Api(value = "positionSaveOrUpdate", description = "the positionSaveOrUpdate API")
public interface PositionSaveOrUpdateApi {

    @ApiOperation(value = "架位管理保存", notes = "架位管理保存", response = ResultVO.class, tags={ "saveOrUpdatePosition", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    
    @RequestMapping(value = "/positionSaveOrUpdate",
        produces = { "application/xml", "application/json" }, 
        consumes = { "application/json", "application/xml" },
        method = RequestMethod.POST)
    ResponseEntity<ResultVO> positionSaveOrUpdate(@ApiParam(value = "架位Vo", required = true) @RequestHeader("userName") String userName, @RequestHeader("organSign") String organSign, @Valid @RequestBody SaasPositionVo saasPositionVo);

}
