package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

@ApiModel(description = "商品和标准库的对应关系")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")
public class ProductLibraryRelationPo {

    @JsonProperty("productPref")
    private String productPref = null;

    @JsonProperty("libraryId")
    private Long libraryId = null;

    public ProductLibraryRelationPo productPref(String productPref) {
        this.productPref = productPref;
        return this;
    }

    /**
     * 商品编号
     * @return productPref
     **/
    @ApiModelProperty(value = "商品编号")

    public String getProductPref() {
        return productPref;
    }

    public void setProductPref(String productPref) {
        this.productPref = productPref;
    }

    public ProductLibraryRelationPo libraryId(Long libraryId) {
        this.libraryId = libraryId;
        return this;
    }

    /**
     * 标准库id
     * @return productPref
     **/
    @ApiModelProperty(value = "标准库id")

    public Long getLibraryId() {
        return libraryId;
    }

    public void setLibraryId(Long libraryId) {
        this.libraryId = libraryId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ProductLibraryRelationPo that = (ProductLibraryRelationPo) o;
        return Objects.equals(productPref, that.productPref) &&
                Objects.equals(libraryId, that.libraryId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(productPref, libraryId);
    }

    @Override
    public String toString() {
        return "ProductLibraryRelationPo{" +
                "productPref='" + productPref + '\'' +
                ", libraryId=" + libraryId +
                '}';
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}
