package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.dto.ProductDto;
import com.xyy.saas.web.api.module.product.model.DrugStandardAreaVo;
import com.xyy.saas.web.api.module.product.model.DrugStandardQueryVo;
import com.xyy.saas.web.api.module.product.model.DrugStandardVo;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RequestMapping("/product")
@Api(value = "河北药监匹配模块", description = "河北药监匹配模块")
public interface DrugStandardApi {
    /**
     * 获取药监开通地区列表
     * @return
     */
    @ApiOperation(value = "获取药监开通地区列表", notes = "获取药监开通地区列表", response = DrugStandardAreaVo.class, tags={ "河北药监匹配模块", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = DrugStandardAreaVo.class) })
    @RequestMapping(value = "/drugstandard/arealist/query",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> areaList(HttpServletRequest request);

    /**
     * 获取指定地区药监标准库数据列表
     * @return
     */
    @ApiOperation(value = "获取指定地区药监标准库数据列表", notes = "获取指定地区药监标准库数据列表", response = DrugStandardVo.class, tags={ "河北药监匹配模块", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = DrugStandardVo.class) })
    @RequestMapping(value = "/drugstandard/infolist/query",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> proList(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody DrugStandardQueryVo drugStandardQueryVo);

}
