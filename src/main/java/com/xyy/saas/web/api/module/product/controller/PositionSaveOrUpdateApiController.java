package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.inventory.core.api.SaasPoSitionApi;
import com.xyy.saas.web.api.module.product.model.ResultCodeEnum;
import com.xyy.saas.web.api.module.product.model.SaasPositionVo;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-30T11:33:52.138+08:00")

@Controller
public class PositionSaveOrUpdateApiController implements PositionSaveOrUpdateApi {


    @Reference(version = "1.0.0")
    private SaasPoSitionApi saasPoSitionApi;
    public ResponseEntity<ResultVO> positionSaveOrUpdate(@ApiParam(value = "架位Vo" ,required=true ) @RequestHeader("userName") String userName, @RequestHeader("organSign") String organSign, @Valid @RequestBody SaasPositionVo saasPositionVo) {
        if(saasPositionVo==null){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.INVENTORY_PARAM_NULL), HttpStatus.INTERNAL_SERVER_ERROR);
        }
        com.xyy.saas.inventory.core.dto.SaasPositionVo vo = new com.xyy.saas.inventory.core.dto.SaasPositionVo();
        BeanUtils.copyProperties(saasPositionVo,vo);
        vo.setOrgansign(organSign);
        vo.setCreateUser(userName);
        Integer integer = saasPoSitionApi.saveOrUpdate(vo);
        if(integer>=1){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.SUCCESS), HttpStatus.OK);
        }else{
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.INVENTORY_RESULT_FAIL), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

}
