package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T17:19:18.933+08:00")

@ApiModel(description = "商品配伍选择商品更新保存信息类")
public class ProductCompatiSaveUpdateVo {

    @JsonProperty("id")
    private Long id ;//主键id

    @JsonProperty("pref")
    private String pref;//商品主商品内部编码

    @JsonProperty("createUser")
    private String createUser;//创建人

    @JsonProperty("updateUser")
    private String updateUser;//更新操作人

    @JsonProperty("guid")
    private String guid;//全局唯一id

    @JsonProperty("remark")
    private String remark;//备注信息

    @JsonProperty("comDetails")
    private List<ProductComDetailUpdateSaveVo> comDetails;//商品配伍列表

    @JsonProperty("drugPermissionPerson")
    private String  drugPermissionPerson; //药品上市许可证持有人

    @ApiModelProperty(value = "药品上市许可证持有人")
    public String getDrugPermissionPerson() {
        return drugPermissionPerson;
    }

    public void setDrugPermissionPerson(String drugPermissionPerson) {
        this.drugPermissionPerson = drugPermissionPerson;
    }

    @ApiModelProperty(value = "主键id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @ApiModelProperty(value = "配伍主商品内部编码")
    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    @ApiModelProperty(value = "创建人")
    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    @ApiModelProperty(value = "更新操作人")
    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    @ApiModelProperty(value = "全局唯一id")
    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    @ApiModelProperty(value = "备注信息")
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @ApiModelProperty(value = "商品配伍列表")
    public List<ProductComDetailUpdateSaveVo> getComDetails() {
        return comDetails;
    }

    public void setComDetails(List<ProductComDetailUpdateSaveVo> comDetails) {
        this.comDetails = comDetails;
    }
}
