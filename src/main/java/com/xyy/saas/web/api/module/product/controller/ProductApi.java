/**
 * NOTE: This class is auto generated by the swagger code generator program (2.2.3).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.xyy.saas.web.api.module.product.controller;

import com.github.pagehelper.PageInfo;
import com.xyy.me.product.common.exception.api.ApiException;
import com.xyy.me.product.general.api.vo.dictionary.DictCategoryGeneralVo;
import com.xyy.me.product.saas.api.dto.SaasCategoryReqDto;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.dto.MatchProductParam;
import com.xyy.saas.product.core.dto.ProductDto;
import com.xyy.saas.product.core.dto.ProductUploadInfoDto;
import com.xyy.saas.purchase.core.dto.ProductBaseinfoPoDto;
import com.xyy.saas.web.api.module.product.model.*;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T15:22:37.401+08:00")
@RequestMapping("/product")
@Api(value = "product", description = "the product API")
public interface ProductApi {

    @ApiOperation(value = "添加商品信息", notes = "添加商品信息", response = SaasProductBaseInfo.class, tags={ "product", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "操作成功", response = SaasProductBaseInfo.class) })
    
    @RequestMapping(value = "/product/addProduct",
        method = RequestMethod.POST)
    ResponseEntity<ResultVO> addProduct(HttpServletRequest request, @ApiParam(value = "商品信息", required = true) @Valid @RequestBody SaasProductBaseInfo saasProductBaseInfo);


    @ApiOperation(value = "修改商品信息", notes = "修改商品信息", response = SaasProductBaseInfo.class, tags={ "product", })
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "操作成功", response = SaasProductBaseInfo.class) })

    @RequestMapping(value = "/product/updateProduct",
        method = RequestMethod.POST)
    ResponseEntity<ResultVO> updateProduct(HttpServletRequest request, @ApiParam(value = "商品信息", required = true) @Valid @RequestBody SaasProductBaseInfo saasProductBaseInfo);


    @ApiOperation(value = "批量添加商品信息", notes = "批量添加商品信息", response = SaasProductBaseInfo.class, tags={ "product", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = SaasProductBaseInfo.class) })

    @RequestMapping(value = "/product/batchAddPro",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> batchAddProduct(HttpServletRequest request, @ApiParam(value = "商品信息", required = true) @Valid @RequestBody Products products);

    @ApiOperation(value = "批量修改商品的标准库信息", notes = "批量修改商品的标准库信息", response = ProductLibraryRelationPo.class, tags={ "product", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = SaasProductBaseInfo.class) })

    @RequestMapping(value = "/product/batchUpdateLibraryId",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> batchUpdateProductLibraryId(HttpServletRequest request, @ApiParam(value = "商品和标准库的对应关系", required = true) @Valid @RequestBody ProductLibraryRelations relations);

    @ApiOperation(value = "商品信息列表", notes = "商品信息列表", response = SaasProductBaseInfo.class, tags={ "product", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = SaasProductBaseInfo.class) })

    @RequestMapping(value = "/product/list",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> productList(HttpServletRequest request, @ApiParam(value = "商品信息", required = true) @RequestBody SaasProductBaseInfo saasProductBaseInfo);

    @ApiOperation(value = "根据商品id获取商品信息", notes = "根据商品id获取商品信息", response = SaasProductBaseInfo.class, tags={ "product", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = SaasProductBaseInfo.class) })

    @RequestMapping(value = "/product/list/{id}",
            method = RequestMethod.GET)
    ResponseEntity<ResultVO> getProductById(HttpServletRequest request, @ApiParam(value = "商品信息", required = true) @Valid @RequestBody Long id);

    @ApiOperation(value = "根据药店标识批量修改2.0升级3.0的商品拆零信息", notes = "根据药店标识批量修改2.0升级3.0的商品拆零信息", tags={ "product"})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = ResponseEntity.class) })
    @RequestMapping(value = "/product/updateScatteredPharmacy",
            method = RequestMethod.GET)
    ResponseEntity<ResultVO> updateScatteredPharmacy(HttpServletRequest request, @ApiParam(value = "药店标识", required = true) @RequestParam String organSigns);

    @ApiOperation(value = "根据药店标识获取当前快失效的商品总数", notes = "根据药店标识获取当前快失效的商品总数", tags={ "product"})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResponseEntity.class)})
    @RequestMapping(value = "/product/getNearlyExpireProCount", method = RequestMethod.GET)
    ResponseEntity<ResultVO> getNearlyEffectiveProductCount(HttpServletRequest request);


    @ApiOperation(value = "app 首页统计", notes = "app 首页统计", tags={ "product"})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = ProductCount.class) })
    @RequestMapping(value = "/product/getAppIndexCount",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> getAppIndexCount(@RequestHeader("commonRequestModel") String commonRequestModel,@RequestHeader(name = "organSign", required = true) String organSign);

    /*@ApiOperation(value = "测试接口问题", notes = "测试接口问题", tags={ "product"})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ProductCount.class) })
    @RequestMapping(value = "/product/getProductByOrganSign", method = RequestMethod.POST)
    ResponseEntity<ResultVO> getProductByOrganSign(@RequestParam(name = "organSign", required = true)String organSign);

    @ApiOperation(value = "批量修改商品的最后一次供应商信息", notes = "批量修改商品的最后一次供应商信息", tags={ "product"})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ProductCount.class) })
    @RequestMapping(value = "/product/batchUpdatePurchase", method = RequestMethod.POST)
    ResponseEntity<ResultVO> batchUpdatePurchase(@RequestParam(name = "organSign", required = true)String organSign);*/

    @ApiOperation(value = "更新版本号", notes = "更新版本号", tags={ "batchUpdateBaseVersion"})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = Boolean.class) })
    @RequestMapping(value = "/product/batchUpdateBaseVersion",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> batchUpdateBaseVersion(@ApiParam(value = "pageNum" ,required=true )  @Valid int pageNum, @ApiParam(value = "pageSize" ,required=true )  int pageSize, @ApiParam(value = "endPage" ,required=true )  int endPage);

    @ApiOperation(value = "六级分类", notes = "六级分类", tags={ "六级分类"})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = Boolean.class) })
    @RequestMapping(value = "/product/sixLevelsDict",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> sixLevelsDict(@RequestBody DictCategoryGeneralVo dictCategoryGeneralVo);

    @ApiOperation(value = "上传信息", notes = "上传信息", tags={ "上传信息"})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = Boolean.class) })
    @RequestMapping(value = "/query/upload",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> queryUpload(@RequestBody ProductUploadInfoDto productUploadInfoDto, @RequestHeader(name = "organSign", required = true) String organSign);


    @RequestMapping(value = "/product/updatePurchaseStatus",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> updatePurchaseStatus(HttpServletRequest request, @ApiParam(value = "修改商品禁采状态", required = true)  @RequestBody ProductDto productDto,@RequestHeader(name = "organSign", required = true)String organSign);


    @ApiOperation(value = "六级分类", notes = "六级分类", tags={ "六级分类"})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = Boolean.class) })
    @RequestMapping(value = "/product/sixLevelsDict/idToName",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> sixLevelsDictIdToName(@RequestBody SaasCategoryReqDto saasCategoryReqDto) throws ApiException;

    @RequestMapping(value = "/product/matchGate2", method = RequestMethod.POST)
    ResponseEntity<ResultVO> matchGate2(@RequestBody MatchProductParam matchProductParam, @RequestHeader(name = "organSign", required = true) String organSign);

    @RequestMapping(value = "/pop/manualProList", method = RequestMethod.POST)
    @ResponseBody
    public ResultVO<PageInfo<ProductBaseinfoPoDto>> queryManualMatchingPros(@RequestBody ManualProListQueryVo manualProListQueryVo,@RequestHeader(name = "organSign", required = true)String organSign);
}
