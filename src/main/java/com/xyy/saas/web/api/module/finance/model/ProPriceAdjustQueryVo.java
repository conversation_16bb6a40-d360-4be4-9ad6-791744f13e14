package com.xyy.saas.web.api.module.finance.model;

import com.xyy.saas.product.core.dto.PriceAdjustDetailDto;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Description 查询售价变更记录
 * @<PERSON>
 * @Create 2021-06-23 15:13
 */
@Data
public class ProPriceAdjustQueryVo {
    private Long id;
    private String pref;
    private Date createTime;
    private String createUser;
    private String updateUser;
    private Date updateTime;
    private String remark;
    private Byte status;
    private Byte yn;
    private String organSign;
    private String baseVersion;
    private String starttime;
    private String endtime;
    private String username;
    private String proname;
    private Long adjustId;
    private Integer userId;
    private Byte isProductHidden;
    private String adjustReason;
    private String applicableStores;
    private Integer todoRoleId;
    private String todoRoleName;
    private Byte auditState;
    private String createUserName;
    private List<PriceAdjustDetailDto> details;
    private List<Long> ids;
    private List<String> organSigns;
    private Byte bizModel;
    private Byte organSignType;
    private List<String> createUsers;
    private String data;
    private Byte approveOrganSign;
    private Byte updateBaseFlag;
    private String headerOganSign;
    private String headerOrganSign;

    /**页码*/
    private Integer pageNum;
    /**页大小*/
    private Integer pageSize;

    private Integer rows;
    private Integer page;
    private String types;
}



