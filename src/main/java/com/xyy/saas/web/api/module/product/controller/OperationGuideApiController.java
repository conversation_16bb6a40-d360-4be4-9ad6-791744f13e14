package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.assist.StringUtils;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.dto.OperationGuideReadReCordDto;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.member.core.api.MemberChronicProductApi;
import com.xyy.saas.member.core.dto.MemberChronicProductDto;
import com.xyy.saas.pharmacy.core.api.SaasPharmacyApi;
import com.xyy.saas.pharmacy.core.dto.*;
import com.xyy.saas.product.core.dto.ProductDto;
import com.xyy.saas.product.core.dto.StandardLibaryProductVoDto;
import com.xyy.saas.web.api.module.product.model.*;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.dto.result.EmployeeDto;
import com.xyy.user.module.enums.DrugstoreBizModelEnum;
import com.xyy.user.module.enums.DrugstoreTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-10-21T10:11:31.623+08:00")

@Slf4j
@Controller
public class OperationGuideApiController implements OperationGuideApi {

    @Reference(version = "0.0.1")
    private com.xyy.saas.common.api.OperationGuideApi operationGuideApi;

    @Override
    public ResponseEntity<ResultVO> needShowOperationGuide(HttpServletRequest request, OperationGuideReadRecordVo dto) {
        String organSign = request.getHeader("organSign");
        String username = request.getHeader("userName");
        String employeeId = request.getHeader("employeeId");
        Integer  guideType = dto.getGuideType();

        ResponseEntity<ResultVO> checkDataResult = checkData(employeeId, organSign, guideType);
        if (checkDataResult != null) return checkDataResult;

        OperationGuideReadReCordDto reCordDto = new OperationGuideReadReCordDto();
        BeanUtils.copyProperties(dto, reCordDto);
        reCordDto.setEmployeeId(employeeId);
        reCordDto.setOrganSign(organSign);

        log.info("查看是否显示操作指引入参:{}", JSONUtils.obj2JSON(reCordDto));
        ResultVO<OperationGuideReadReCordDto> queryOneReadRecord = operationGuideApi.queryOneReadRecord(reCordDto);
        log.info("查看是否显示操作指引入参出参:{}", JSONUtils.obj2JSON(queryOneReadRecord));

        // 如果没有已读记录需要显示操作指引
        return new ResponseEntity<ResultVO>(new ResultVO(queryOneReadRecord.getResult() == null), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> addOperationGuide(HttpServletRequest request, OperationGuideReadRecordVo dto) {
        log.info("新增操作指引入参入参:{}", JSONUtils.obj2JSON(dto));
        String organSign = request.getHeader("organSign");
        String username = request.getHeader("userName");
        String employeeId = request.getHeader("employeeId");
        Integer  guideType = dto.getGuideType();

        ResponseEntity<ResultVO> checkDataResult = checkData(employeeId, organSign, guideType);
        if (checkDataResult != null) return checkDataResult;


        OperationGuideReadReCordDto reCordDto = new OperationGuideReadReCordDto();
        BeanUtils.copyProperties(dto, reCordDto);
        reCordDto.setEmployeeId(employeeId);
        reCordDto.setOrganSign(organSign);

        operationGuideApi.insertReadRecords(reCordDto);
        return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.SUCCESS), HttpStatus.OK);
    }

    private ResponseEntity<ResultVO> checkData(String employeeId, String organSign, Integer guideType) {
        if(com.xyy.saas.common.assist.StringUtils.isBlank(organSign)){
            return  new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR,"机构号不能为空"), HttpStatus.OK);
        }

        if(StringUtils.isBlank(employeeId)){
            return  new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR,"员工ID不能为空"), HttpStatus.OK);
        }

        if(guideType == null){
            return  new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR,"指引操作类型不能为空"), HttpStatus.OK);
        }
        return null;
    }

}
