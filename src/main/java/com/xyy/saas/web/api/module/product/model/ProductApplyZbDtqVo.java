package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * @ClassName ProductApplyZbDtqVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/29 22:13
 * @Version 1.0
 **/
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

@ApiModel(description = "商品提报列表结果信息类")
public class ProductApplyZbDtqVo {
    @JsonProperty("pref")
    private String pref;//提报单编号，调用审批流任务businessKey
    @JsonProperty("commonName")
    private String commonName;//通用名
    @JsonProperty("productName")
    private String productName;//商品名称
    @JsonProperty("prescriptionClassificationName")
    private String prescriptionClassificationName;//处方分类
    @JsonProperty("prescriptionYnName")
    private String prescriptionYnName;//处方类别
    @JsonProperty("attributeSpecification")
    private String attributeSpecification;//规格型号
    @JsonProperty("unitName")
    private String unitName;//基本单位
    @JsonProperty("dosageFormName")
    private String dosageFormName;//剂型
    @JsonProperty("pharmacyPref")
    private String pharmacyPref;//商品编号
    @JsonProperty("systemTypeName")
    private String systemTypeName;//商品分类
    @JsonProperty("manufacturer")
    private String manufacturer;//生产厂家
    @JsonProperty("approvalNumber")
    private String approvalNumber;//批准文号
    @JsonProperty("auditStateName")
    private String auditStateName;//提报审批状态
    @JsonProperty("productAuditStateName")
    private String productAuditStateName;//首营审批状态
    @JsonProperty("createUserName")
    private String createUserName;//创建人名称
    @JsonProperty("createTime")
    private String createTime;//创建时间
    @JsonProperty("chainStoreOrgansignName")
    private String chainStoreOrgansignName;//来源门店名称
    @JsonProperty("barCode")
    private String barCode;//条形码

    @ApiModelProperty(value = "标准库id")
    public Long getStandardLibraryId() {
        return standardLibraryId;
    }

    public void setStandardLibraryId(Long standardLibraryId) {
        this.standardLibraryId = standardLibraryId;
    }

    @JsonProperty("标准库id")
    private Long standardLibraryId;//标准库id

    @ApiModelProperty(value = "调用审批流任务id")
    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    @ApiModelProperty(value = "调用审批流业务场景")
    public String getBusinessScene() {
        return businessScene;
    }

    public void setBusinessScene(String businessScene) {
        this.businessScene = businessScene;
    }

    @JsonProperty("taskId")
    private String taskId;//调用审批流任务id
    @JsonProperty("businessScene")
    private String businessScene;//调用审批流业务场景

    @ApiModelProperty(value = "资质过期")
    public String getExpireYnStr() {
        return expireYnStr;
    }

    public void setExpireYnStr(String expireYnStr) {
        this.expireYnStr = expireYnStr;
    }

    @JsonProperty("expireYnStr")
    private String expireYnStr;//资质过期

    @ApiModelProperty(value = "条形码")
    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    @ApiModelProperty(value = "门店零售价")
    public String getRetailPrice() {
        return retailPrice;
    }

    public void setRetailPrice(String retailPrice) {
        this.retailPrice = retailPrice;
    }

    @ApiModelProperty(value = "门店会员价")
    public String getVipPrice() {
        return vipPrice;
    }

    public void setVipPrice(String vipPrice) {
        this.vipPrice = vipPrice;
    }

    @JsonProperty("retailPrice")
    private String retailPrice;//门店零售价
    @JsonProperty("vipPrice")
    private String vipPrice;//门店会员价
    @JsonProperty("auditState")
    private Byte auditState;//提报审批状态
    @JsonProperty("productAuditState")
    private Byte productAuditState;//首营审批状态

    public Byte getAuditState() {
        return auditState;
    }

    public void setAuditState(Byte auditState) {
        this.auditState = auditState;
    }

    public Byte getProductAuditState() {
        return productAuditState;
    }

    public void setProductAuditState(Byte productAuditState) {
        this.productAuditState = productAuditState;
    }



    @ApiModelProperty(value = "来源门店名称")
    public String getChainStoreOrgansignName() {
        return chainStoreOrgansignName;
    }

    public void setChainStoreOrgansignName(String chainStoreOrgansignName) {
        this.chainStoreOrgansignName = chainStoreOrgansignName;
    }

    @ApiModelProperty(value = "提报单编号，调用审批流任务businessKey")
    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    @ApiModelProperty(value = "通用名称")
    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    @ApiModelProperty(value = "商品名称")
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    @ApiModelProperty(value = "处方分类")
    public String getPrescriptionClassificationName() {
        return prescriptionClassificationName;
    }

    public void setPrescriptionClassificationName(String prescriptionClassificationName) {
        this.prescriptionClassificationName = prescriptionClassificationName;
    }

    @ApiModelProperty(value = "处方类别")
    public String getPrescriptionYnName() {
        return prescriptionYnName;
    }

    public void setPrescriptionYnName(String prescriptionYnName) {
        this.prescriptionYnName = prescriptionYnName;
    }

    @ApiModelProperty(value = "规格型号")
    public String getAttributeSpecification() {
        return attributeSpecification;
    }

    public void setAttributeSpecification(String attributeSpecification) {
        this.attributeSpecification = attributeSpecification;
    }

    @ApiModelProperty(value = "基本单位")
    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    @ApiModelProperty(value = "剂型")
    public String getDosageFormName() {
        return dosageFormName;
    }

    public void setDosageFormName(String dosageFormName) {
        this.dosageFormName = dosageFormName;
    }

    @ApiModelProperty(value = "商品编号")
    public String getPharmacyPref() {
        return pharmacyPref;
    }

    public void setPharmacyPref(String pharmacyPref) {
        this.pharmacyPref = pharmacyPref;
    }

    @ApiModelProperty(value = "商品分类")
    public String getSystemTypeName() {
        return systemTypeName;
    }

    public void setSystemTypeName(String systemTypeName) {
        this.systemTypeName = systemTypeName;
    }

    @ApiModelProperty(value = "生产厂家")
    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    @ApiModelProperty(value = "批准文号")
    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    @ApiModelProperty(value = "提报审批状态")
    public String getAuditStateName() {
        return auditStateName;
    }

    public void setAuditStateName(String auditStateName) {
        this.auditStateName = auditStateName;
    }

    @ApiModelProperty(value = "首营审批状态")
    public String getProductAuditStateName() {
        return productAuditStateName;
    }

    public void setProductAuditStateName(String productAuditStateName) {
        this.productAuditStateName = productAuditStateName;
    }

    @ApiModelProperty(value = "创建人")
    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    @ApiModelProperty(value = "创建时间")
    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
}
