package com.xyy.saas.web.api.module.supplier.controller.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.api.ExportExcelApi;
import com.xyy.saas.common.api.SysConfigApi;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.dto.SystemConfigWEBDto;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.api.ProductApi;
import com.xyy.saas.product.core.api.ProductUpdateMsgQualityModifyApi;
import com.xyy.saas.product.core.dto.*;
import com.xyy.saas.supplier.api.SupplierUpdateMsgQualityModifyApi;
import com.xyy.saas.supplier.dto.*;
import com.xyy.saas.web.api.module.product.model.*;
import com.xyy.saas.web.api.module.product.util.CheckNull;
import com.xyy.saas.web.api.module.supplier.controller.SupplierQualityModifyApi;
import com.xyy.saas.web.api.module.supplier.model.*;
import com.xyy.saas.web.api.module.utils.ExportExcelUtil;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.api.EmployeefileApi;
import com.xyy.user.module.api.RoleApi;
import com.xyy.user.module.dto.EmployeeListRequestModel;
import com.xyy.user.module.dto.EmployeefileRequestModel;
import com.xyy.user.module.dto.result.EmployeeDto;
import com.xyy.user.module.dto.result.QueryEmployeePrimaryVO;
import com.xyy.user.module.enums.DrugstoreBizModelEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-07T16:41:52.348+08:00")

@Controller
public class SupplierQualityModifyController implements SupplierQualityModifyApi {

    private static final Logger logger = LoggerFactory.getLogger(SupplierQualityModifyController.class);

    @Reference(version = "0.0.1")
    private SupplierUpdateMsgQualityModifyApi supplierUpdateMsgQualityModifyApi;
    @Reference(version = "0.0.1")
    private EmployeeApi employeeApi;
    @Reference(version = "0.0.1")
    private EmployeefileApi employeefileApi;
    @Reference(version = "0.0.1")
    private RoleApi roleApi;
    @Reference(version = "0.0.3")
    public ExportExcelApi exportExcelApi;

    @Reference(version = "0.0.1")
    private SysConfigApi sysConfigApi;

    @Reference(version = "0.0.1")
    private ProductApi productApi;

    @Value("${ftp.remotePath}")
    public String remotePath;

    @Value("${ftp.downLoadUrl}")
    public String downLoadUrl;

    @Value("${ftp.serverName}")
    public String serverName;

    @Value("${ftp.port}")
    public String port;

    @Value("${ftp.username}")
    public String username;

    @Value("${ftp.password}")
    public String password;

    @Override
    public ResponseEntity<ResultVO> list(@RequestHeader(value = "organSign", required = true) String organSign, @RequestHeader(name = "employeeId", required = true) Integer employeeId, @RequestBody SupplierUpdatemsgList listDto) {
        logger.info("供应商质量信息变更列表入参+" + JSONObject.toJSONString(listDto));
        SupplierUpdatemsgListDto supplierUpdatemsgListDto = new SupplierUpdatemsgListDto();
        BeanUtils.copyProperties(listDto, supplierUpdatemsgListDto);
        supplierUpdatemsgListDto.setBeginTime(listDto.getStartTime());
        supplierUpdatemsgListDto.setOrganSign(organSign);
        ResultVO<EmployeeDto> employeeDtoResultVO = employeeApi.queryEmployeeById(employeeId);
        ResultVO<List<Integer>> listResultVO = roleApi.queryRoleIdsByEmployeeId(employeeId);
        supplierUpdatemsgListDto.setRolesIds(listResultVO.getResult());
        logger.info("employeeId:"+employeeId+",roleListsids:"+listResultVO.getResult());
        if (employeeDtoResultVO.getCode() == 0) {
            logger.info("用户信息:" + JSONObject.toJSONString(employeeDtoResultVO));
            supplierUpdatemsgListDto.setIdentity(Integer.valueOf(employeeDtoResultVO.getResult().getIdentity()));
        } else {
            supplierUpdatemsgListDto.setIdentity(0);
        }
        ResultVO<PageInfo> resultVO = supplierUpdateMsgQualityModifyApi.list(supplierUpdatemsgListDto);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> echo(HttpServletRequest request, @RequestHeader(value = "organSign", required = true) String organSign, @RequestBody SupplierUpdatemsgEcho echoDto) {
        ResultVO<SupplierUpdatemsgEchoDto> echo = supplierUpdateMsgQualityModifyApi.echo(echoDto.getGuid());
        String employeeId = request.getHeader("employeeId");
        ResultVO<List<Integer>> listResultVO = roleApi.queryRoleIdsByEmployeeId(Integer.parseInt(employeeId));
        if(listResultVO != null && listResultVO.getResult().contains(echo.getResult().getTodoRoleId())){
            echo.getResult().setCanCheck(1);
        }else{
            echo.getResult().setCanCheck(0);
        }
        return new ResponseEntity<ResultVO>(echo, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> save(HttpServletRequest request, @RequestHeader(value = "organSign", required = true) String organSign, @RequestHeader(name = "employeeId", required = true) Integer employeeId, @RequestBody SupplierUpdatemsgSave saveDto) {
        logger.info("供应商质量信息变更审核入参+" + JSONObject.toJSONString(saveDto));
        //验参
        if (saveDto.getList() == null || saveDto.getList().isEmpty()
                || saveDto.getUpdateContent() == null) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "参数错误"), HttpStatus.OK);
        }


        CommonRequestModel model =  JSONObject.parseObject(request.getHeader("commonRequestModel"), CommonRequestModel.class);
        /** 连锁_供应商质量信息变更 */
        if (null != saveDto.getUpdateType() && saveDto.getUpdateType() == 1 && DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel())) {
            List<SupplierUpdatemsg> updatemsgDtoList = saveDto.getList();
//            for (SupplierUpdatemsg updatemsgDto : updatemsgDtoList) {
//                if (toCheckParam.contains(updatemsgDto.getFieldName()) && org.springframework.util.StringUtils.isEmpty(updatemsgDto.getUpdateSrc())) {
//                    return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "["+updatemsgDto.getFieldValue()+"]必填"), HttpStatus.OK);
//                }
//            }

            Object updateContent = saveDto.getUpdateContent();
            logger.info("供应商质量信息内容 updateContent ：" + JSONObject.toJSONString(updateContent));
            if (null != updateContent) {

                JSONObject json = (JSONObject) JSON.toJSON(updateContent);
                Object object = json.get("providerSalesDtos");
                if (null != object) {
                    String salesList = JSONArray.toJSONString(object);
                    List<ProviderSalesDto> list = JSONArray.parseArray(salesList, ProviderSalesDto.class);
                    logger.info("供应商质量信息内容 list ：" + JSONObject.toJSONString(list));

                    for (ProviderSalesDto salesDto : list) {
                        if (org.springframework.util.StringUtils.isEmpty(salesDto.getAuthorizationNumExpirationDate())) {
                            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "[授权书号有效期]必填"), HttpStatus.OK);
                        }

                        if (org.springframework.util.StringUtils.isEmpty(salesDto.getSalesName())) {
                            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "[销售人员姓名]必填"), HttpStatus.OK);
                        }
                    }
                }
            }
        }

        String s = CheckNull.nullToEmpty((Map<String, Object>) saveDto.getUpdateContent());
        SupplierUpdatemsgSaveDto dto = new SupplierUpdatemsgSaveDto();
        BeanUtils.copyProperties(saveDto, dto);
        dto.setApplyUserId(employeeId);
        dto.setOrganSign(organSign);
        dto.setUpdateContent(s);
        ResultVO<EmployeeDto> employeeDtoResultVO = employeeApi.queryEmployeeById(employeeId);
        if (employeeDtoResultVO != null && employeeDtoResultVO.getCode() == 0 && employeeDtoResultVO.getResult() != null) {
            dto.setApplyUser(employeeDtoResultVO.getResult().getName());
        }
        ResultVO save = supplierUpdateMsgQualityModifyApi.save(dto);
        return new ResponseEntity<ResultVO>(save, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> audit(HttpServletRequest request,@RequestHeader(value = "organSign", required = true) String organSign, @RequestHeader(name = "employeeId", required = true) Integer employeeId, @RequestBody SupplierUpdatemsgAudit auditDto) {
        logger.info("供应商质量信息变更审核入参+" + JSONObject.toJSONString(auditDto));
        //验参
        if (auditDto == null || StringUtils.isBlank(auditDto.getGuid())
                || auditDto.getAuditState() == null || auditDto.getAuditState() == 0
                || auditDto.getAuditUserId() == null || auditDto.getAuditUserId() == 0
                || auditDto.getAuditTime() == null) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "参数错误"), HttpStatus.OK);
        }
        SupplierUpdatemsgAuditDto dto = new SupplierUpdatemsgAuditDto();
        ResultVO<EmployeeDto> employeeDtoResultVO = employeeApi.queryEmployeeById(auditDto.getAuditUserId());
        if (employeeDtoResultVO != null && employeeDtoResultVO.getCode() == 0 && employeeDtoResultVO.getResult() != null) {
            dto.setAuditUser(employeeDtoResultVO.getResult().getName());
        }
        BeanUtils.copyProperties(auditDto, dto);
        dto.setOrgansign(organSign);
        dto.setUpdateUser(String.valueOf(employeeId));
        String modelJson = request.getHeader("commonRequestModel");
        Byte bizModel = null;
        if(!org.springframework.util.StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            bizModel = model.getBizModel();
        }
        ResultVO audit = supplierUpdateMsgQualityModifyApi.audit(dto,bizModel);
        return new ResponseEntity<ResultVO>(audit, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> verifyPassword(@RequestHeader(value = "organSign", required = true) String organSign, @RequestBody SupplierUpdatemsgVerifyPassword passwordDto) {
        logger.info("供应商密码验证+" + JSONObject.toJSONString(passwordDto));
        //验参
        if (passwordDto == null || passwordDto.getApplyUserId() == null
                || passwordDto.getApplyUserId() == 0 || StringUtils.isBlank(passwordDto.getPassword())) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "参数错误"), HttpStatus.OK);
        }
        SupplierUpdatemsgVerifyPasswordDto dto = new SupplierUpdatemsgVerifyPasswordDto();
        BeanUtils.copyProperties(passwordDto, dto);
        dto.setOrganSign(organSign);
        ResultVO resultVO = supplierUpdateMsgQualityModifyApi.verifyPassword(dto);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> qualityUser(@RequestHeader(value = "organSign", required = true) String organSign) {
        EmployeeListRequestModel model = new EmployeeListRequestModel();
        model.setRoleId(3);
        model.setPageNo(1);
        model.setPageSize(1000);
        ResultVO resultVO = employeeApi.queryEmployeeByCondition(model, organSign);
        ResultVO<SystemConfigWEBDto> webSysConfig = sysConfigApi.getWEBSysConfig(organSign);
        if (resultVO.getCode() != 0 || webSysConfig.getCode() != 0) {
            return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
        }
        PageInfo<QueryEmployeePrimaryVO> queryEmployeePrimaryVOPageInfo = (PageInfo<QueryEmployeePrimaryVO>) resultVO.getResult();
        if (queryEmployeePrimaryVOPageInfo.getList() == null || queryEmployeePrimaryVOPageInfo.getList().size() == 0) {
            return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
        }
        PageInfo<QueryDefaultEmployeeVO> defaultEmployeeVOPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(queryEmployeePrimaryVOPageInfo, defaultEmployeeVOPageInfo);
        List<QueryEmployeePrimaryVO> list = queryEmployeePrimaryVOPageInfo.getList();
        List<QueryDefaultEmployeeVO> employeeS = new ArrayList<>();
        for (QueryEmployeePrimaryVO queryEmployeePrimaryVO : list) {
            QueryDefaultEmployeeVO vo = new QueryDefaultEmployeeVO();
            BeanUtils.copyProperties(queryEmployeePrimaryVO, vo);
            if (webSysConfig.getResult().getQualityOwnersEmployeeId().equals(queryEmployeePrimaryVO.getEmployeeId())) {
                vo.setIsdefault(1);
            } else {
                vo.setIsdefault(0);
            }
            employeeS.add(vo);
        }
        defaultEmployeeVOPageInfo.setList(employeeS);
        return new ResponseEntity<ResultVO>(new ResultVO(defaultEmployeeVOPageInfo), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> applyUser(@RequestHeader(value = "organSign", required = true) String organSign) {

        ResultVO resultVO = supplierUpdateMsgQualityModifyApi.applyUser(organSign);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    @Override
    public void export(HttpServletRequest request, HttpServletResponse response,@RequestHeader(value = "organSign", required = true) String organSign, @RequestHeader(name = "employeeId", required = true) Integer employeeId, @RequestBody SupplierUpdatemsgExport equipmentExportDto) {
        String headers[] = new String[]{};
        String fieldNames[] = new String[]{};

        String extfilename = equipmentExportDto.getExcelName() + ".xls";
        headers = new String[]{"单据编号", "变更类型", "申请人", "申请时间", "审核状态"};
        fieldNames = new String[]{"recallNo", "updateTypeName", "applyUser", "applyTime", "auditStateName"};
        EmployeefileRequestModel employeefileRequestModel = new EmployeefileRequestModel();
        employeefileRequestModel.setOrganSign(organSign);
        employeefileRequestModel.setPageNum(1);
        employeefileRequestModel.setPageSize(100);
        List<EmployeeDto> eList = employeefileApi.getEmployeefileByCondition(employeefileRequestModel).getResult().getList();
        Map<Integer, String> eMap = eList.stream().collect(Collectors.toMap(EmployeeDto::getId, p -> p.getName()));

        SupplierUpdatemsgListDto supplierUpdatemsgListDto = new SupplierUpdatemsgListDto();
        BeanUtils.copyProperties(equipmentExportDto, supplierUpdatemsgListDto);
        String ids = equipmentExportDto.getIds();
        String[] split = ids.split(",");
        ArrayList<String> listIds = new ArrayList<String>(Arrays.asList(split)) ;
        ArrayList<Integer> integers = new ArrayList<>();
        for(String s:listIds){
            integers.add(Integer.parseInt(s));
        }
        supplierUpdatemsgListDto.setDetailIds(integers);
        supplierUpdatemsgListDto.setOrganSign(organSign);
        ResultVO<EmployeeDto> employeeDtoResultVO = employeeApi.queryEmployeeById(employeeId);
        if (employeeDtoResultVO.getCode() == 0) {
            supplierUpdatemsgListDto.setIdentity(Integer.valueOf(employeeDtoResultVO.getResult().getIdentity()));
        } else {
            supplierUpdatemsgListDto.setIdentity(0);
        }
        List<SupplierUpdatemsgExportDto> list = supplierUpdateMsgQualityModifyApi.exportExcel(supplierUpdatemsgListDto);
        String sheetName = equipmentExportDto.getExcelName();

        String[] finalHeaders = headers;
        String[] finalFieldNames = fieldNames;
        try {
            ExportExcelUtil.createExcel(response, request, extfilename, sheetName, headers, fieldNames, list, true);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}


