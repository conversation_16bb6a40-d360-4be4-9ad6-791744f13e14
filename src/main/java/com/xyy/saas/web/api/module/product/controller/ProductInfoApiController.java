package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.github.pagehelper.PageInfo;
import com.xyy.common.module.api.SysConfigApi;
import com.xyy.common.module.dto.SystemConfigDto;
import com.xyy.saas.common.api.SaasAreaApi;
import com.xyy.saas.common.api.SaasCustomTypeApi;
import com.xyy.saas.common.api.SystemDictApi;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.dto.QueryDictByOrgTypeDto;
import com.xyy.saas.common.dto.SystemDictDto;
import com.xyy.saas.common.enums.CustomTypeBusinessTypeEnum;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.drugremind.core.api.SystemDrugRemindApi;
import com.xyy.saas.drugremind.core.dto.SystemDrugRemindDTO;
import com.xyy.saas.inventory.cores.api.InventoryForPromotionApi;
import com.xyy.saas.inventory.cores.api.SaasPoSitionApi;
import com.xyy.saas.inventory.cores.common.IRemoteBean;
import com.xyy.saas.inventory.cores.dto.ProductExtendInventoryVo;
import com.xyy.saas.inventory.cores.dto.SaasPositionVo;
import com.xyy.saas.inventory.cores.dto.repo.InventoryLotNumPromotionResult;
import com.xyy.saas.product.control.dto.costprice.CostAdjustsByPrefResultDetailDto;
import com.xyy.saas.product.control.dto.costprice.CostPriceQueryProductDto;
import com.xyy.saas.product.core.api.ProductDictionaryApi;
import com.xyy.saas.match.api.MatchStandardLibraryApi;
import com.xyy.saas.match.dto.MatchProductVoDto;
import com.xyy.saas.match.dto.MatchedProductDto;
import com.xyy.saas.member.core.dto.MemberPrepayCardDto;
import com.xyy.saas.order.core.dto.OrderInfoDto;
import com.xyy.saas.order.core.util.JacksonUtil;
import com.xyy.saas.product.control.dto.costprice.CostAdjustsByPrefResultDetailDto;
import com.xyy.saas.product.control.dto.costprice.CostPriceQueryProductDto;
import com.xyy.saas.product.core.api.*;
import com.xyy.saas.product.core.api.ProductDictionaryApi;
import com.xyy.saas.product.core.api.ProductToolApi;
import com.xyy.saas.product.core.api.StandardLibaryApi;
import com.xyy.saas.product.core.api.TjOrganSignApi;
import com.xyy.saas.product.core.dto.*;
import com.xyy.saas.product.core.vo.PosDrugIdentCodeUpdateVo;
import com.xyy.saas.rabbitmq.core.api.MessagePushApi;
import com.xyy.saas.web.api.common.constants.UserOperationConstans;
import com.xyy.saas.web.api.module.product.constant.DictConstant;
import com.xyy.saas.web.api.module.product.model.*;
import com.xyy.saas.web.api.module.product.util.DateUtil;
import com.xyy.saas.web.api.module.product.util.ExportExcelUtil;
import com.xyy.saas.web.api.module.product.util.TopClientUtil;
import com.xyy.saas.web.api.module.utils.JedisUtils;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.api.PermissionApi;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import com.xyy.user.module.dto.result.QueryDrugstoreDto;
import com.xyy.user.module.enums.DrugstoreBizModelEnum;
import com.xyy.user.module.enums.DrugstoreTypeEnum;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.net.UnknownHostException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Controller
public class ProductInfoApiController implements ProductInfoApi {

    private static final Logger logger = LoggerFactory.getLogger(ProductInfoApiController.class);

    @Reference(version = "0.0.1")
    private com.xyy.saas.product.core.api.ProductApi productApi;

    @Reference(version = "0.0.1")
    private EmployeeApi employeeApi;

    @Reference(version = "0.0.1")
    private MessagePushApi messagePushApi;

    @Reference(version = "0.0.2")
    private SaasPoSitionApi poSitionApi;

    @Reference(version = "0.0.1")
    private ProductDictionaryApi productDictionaryApi;

    @Reference(version = "0.0.1")
    private PermissionApi permissionApi;

    @Reference(version = "0.0.2")
    private com.xyy.saas.inventory.cores.api.InventoryApi inventoryApi;

    @Reference(version = "0.0.1")
    private StandardLibaryApi standardLibaryApi;

    @Reference(version = "0.0.1")
    private SystemDrugRemindApi systemDrugRemindApi;

    @Reference(version = "0.0.1")
    private com.xyy.saas.product.core.api.AdjustPriceApi adjustPriceApi;

    @Reference(version = "0.0.2")
    private InventoryForPromotionApi inventoryForPromotionApi;

    @Reference(version = "0.0.1")
    private SysConfigApi sysConfigApi;

    @Reference(version = "0.0.1")
    private ProductToolApi productToolApi;

    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;

    @Reference(version = "0.0.1")
    private SaasAreaApi saasAreaApi;


    @Reference(version = "0.0.1")
    private MatchStandardLibraryApi matchStandardLibraryApi;

    @Reference(version = "0.0.1")
    private com.xyy.saas.product.control.api.costprice.ProductCostPriceApi productCostPriceApi;

    @Value("${fengcheng.organsign.areacodes}")
    private String fengchengAreacodes;

    @Value("${msfx.appKey:31026904}")
    private String appKey;

    @Value("${msfx.secret:59a758df4ff1204bd4394a0d38513054}")
    private String secret;

    @Value("${msfx.url:http://gw.api.taobao.com/router/rest}")
    private String msfxUrl;

    @Value("${msfx.apiPath:alibaba.alihealth.drug.getbarcode.bytraccode}")
    private String apiPath;

    @Autowired
    private JedisUtils jedisUtils;

    @Reference(version = "0.0.1")
    private SystemDictApi systemDictApi;

    @Value("${linyi.organsign.areacodes}")
    private String linyiAreacodes;

    @Reference(version = "0.0.1")
    private SaasCustomTypeApi saasCustomTypeApi;

    @Reference(version = "0.0.1")
    private SnProductApi snProductApi;

    @Override
    public ResponseEntity<ResultVO> checkselfMotion(HttpServletRequest request) {
        logger.info("steping into checkselfMotion method: ok");
        String organSign = request.getHeader("organSign");
        JSONObject result = productApi.checkselfMotion(organSign);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(result), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> checkIsRoleExt(HttpServletRequest request) {
        String employee = request.getHeader("employeeId");
        Boolean roleExt = permissionApi.queryProductExtendPermissionByRoleId(Integer.parseInt(employee));
        logger.info("checkIsRoleExt ,employee:" + employee+",roleExt:"+roleExt);
        JSONObject json = new JSONObject();
        if(roleExt){
            json.put("result",1);//具有查看商品扩展信息权限
        }else{
            json.put("result",0);//不具有查看商品扩展信息权限
        }

        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(json), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> firstCheckToUser(HttpServletRequest request, @ApiParam(value = "请求信息对象" ,required=true )  @Valid @RequestBody UserQueryVo userQueryVo) {
        logger.info("firstCheckToUser ++++++++++++++++++++已经进入到此方法++++++++++++++++++++++++++id: " + userQueryVo.getId() + ",pwd:" + userQueryVo.getPwd());
        boolean flag = employeeApi.queryPasswordCorrect(userQueryVo.getId(), userQueryVo.getPwd());
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(flag), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> deleteProductAffirmOperation(HttpServletRequest request, Long id) {
        logger.info("+++++++++++进入到删除商品待确认接口++++++++++");
        if (StringUtils.isEmpty(id)){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "noParam"),HttpStatus.OK);
        }
        String organSign = request.getHeader("organSign");
        boolean flag = productApi.deleteProductAffirmOperation(id, organSign);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(flag),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> deleteProductForId(HttpServletRequest request, Long id) {
        if (StringUtils.isEmpty(id)){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "noParam"),HttpStatus.OK);
        }
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employee");

        boolean flag = productApi.deleteProductForId(id, organSign, Integer.parseInt(employee));
        if (flag){
            pushProductMessToMQ(organSign);
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(null),HttpStatus.OK);
        }else {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, null),HttpStatus.OK);
        }
    }

    private void pushProductMessToMQ(String organSign) {
        JSONObject json = new JSONObject();
        String[] tables = {"saas_product_baseinfo"};
        json.put("code", "sync");
        json.put("tables", tables);
        try {
            logger.info("pushProductMessToMQ before organSign:"+organSign);
            messagePushApi.sendMsgByOrganSign(organSign, json.toJSONString());
            logger.info("pushProductMessToMQ after organSign:"+organSign);
        }catch(Exception e){
            logger.error("pushProductMessToMQ error organSign:"+organSign+",exception:"+e,e);
            throw e;
        }

    }



    @Override
    public void exportExcelOneProduct(HttpServletRequest request, HttpServletResponse response, Long id) {
        String organSign = request.getHeader("organSign");
        String identity = request.getHeader("identity");
        List<ProductExportDto> list = new ArrayList<>();
        ProductExportDto pro = this.productApi.excelProductInfoByIdAndOrganSign(id, organSign, Byte.valueOf(identity));
        list.add(pro);
        try {
            response.setHeader("Content-type", "text/html;charset=utf-8");
            PrintWriter out = response.getWriter();
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            JSONObject json = new JSONObject();
            json.put("prodata", JSONObject.toJSON(list));
            json.put("datatime", df.format(new Date()));
            out.print(json);
            out.flush();
            out.close();
        } catch (IOException e) {
            logger.error("",e);
        }
    }

    @Override
    public ResponseEntity<ResultVO> resertLibraryIdToNull(HttpServletRequest request, @ApiParam(value = "请求信息对象" ,required=true )  @Valid @RequestBody ProductQueryVo productQueryVo) {
        String username = request.getHeader("employeeId");
        String organSign = request.getHeader("organSign");
        logger.info("resertLibraryIdToNull:employeeId:" + username + ",organSign:" + organSign);
        int state = this.productApi.resertLibraryIdToNull(productQueryVo.getId(), username, organSign);
        if ( state > 0 ){
            SystemConfigDto systemConfigDto = sysConfigApi.querySystemConfig(organSign);
            if(null != systemConfigDto && systemConfigDto.getHealthNoteSwitch()==1) {
                try {
                    ProductDto productDto = new ProductDto();
                    //productDto.setId(productQueryVo.getId());
                    ProductDto productByIdAndOrganSign = productApi.getProductByIdAndOrganSign(productQueryVo.getId(), organSign, null);
                    productDto.setPref(productByIdAndOrganSign.getPref());
                    productDto.setOrganSign(organSign);
                    productApi.pushProductInfoToYkqMQ(productDto, "PRODUCT_UNBIND_STANDARD_LIBRARY_ID");
                } catch (Exception e) {
                    logger.error("pushProductInfoToYkqMQ ProductInfo api occur errr:",e);
                }
            }
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(null),HttpStatus.OK);
        }
        return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, null),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> resertIsUsed(HttpServletRequest request, @ApiParam(value = "请求信息对象" ,required=true )  @Valid @RequestBody  ProductQueryVo productQueryVo) {
        String username = request.getHeader("employeeId");
        String organSign = request.getHeader("organSign");
        logger.info("resertIsUsed:employeeId:" + username + ",organSign:" + organSign+",flag:"+productQueryVo.getFlag());
        int state = this.productApi.resertProductIsUsed(productQueryVo.getId(), username, organSign,productQueryVo.getFlag());
        if ( state > 0 ){
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(null),HttpStatus.OK);
        }
        return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, null),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> toPrintList(HttpServletRequest request, Model model) {
        logger.info("++++++++++++++++++++++++++++进入到价签打印页面+++++++++++++++++++++++++++++++++++");
        // 架位
        SaasPositionVo sp = new SaasPositionVo();
//        String organSign = request.getHeader("organSign");
        String organSign = "ZHL0f2ce7b9";
        sp.setOrgansign(organSign);
        List<SaasPositionVo> positionList = null;
//        positionList = poSitionApi.getPositionList(sp);
//        IRemoteBean<List<SaasPositionVo>> iRemoteBean = poSitionApi.getPositionList(sp);
//        if (iRemoteBean.isSuccess()) {
//            positionList = iRemoteBean.getContent();
//        } else {
//            logger.info("ProductInfoController.toPrintList调用rpc接口poSitionApi.getPositionList(...)失败：{}", iRemoteBean.getResultMsg());
//        }
        List<Integer> ids = Arrays.asList(DictConstant.unitBussinessId);
        List<SystemDictDto> dictDtos = productDictionaryApi.findSystemDictDtoBybussinessIds(ids,organSign);
        // 单位
        List<SystemDictDto> unitDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.unitBussinessId)).collect(Collectors.toList());
        model.addAttribute("unitList", JSONObject.toJSON(unitDict).toString());
//        modelAndView.setViewName("basicinfo/printList");
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(model),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> toShowAdd(HttpServletRequest request, Model model, String productName) {
        List<Integer> ids = Arrays.asList(DictConstant.ABC_BUSSINESS_ID, DictConstant.commodtyTypeBussinessId, DictConstant.STORE_CONDITION_BUSINESS_ID,
                DictConstant.unitBussinessId, DictConstant.agentBussinessId, DictConstant.prescriptionBussinessId,DictConstant.maintenanceType);
        String organSign = request.getHeader("organSign");
        List<SystemDictDto> dictDtos = productDictionaryApi.findSystemDictDtoBybussinessIds(ids,organSign);
        // 单位
        List<SystemDictDto> unitDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.unitBussinessId)).collect(Collectors.toList());
        // 处方分类
        List<SystemDictDto> cfDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.prescriptionBussinessId)).collect(Collectors.toList());

        model.addAttribute("cfDict", cfDict);
        model.addAttribute("unitList", JSONObject.toJSON(unitDict).toString());
        model.addAttribute("cfList", JSONObject.toJSON(cfDict).toString());
        model.addAttribute("productName",productName);
//        modelAndView.setViewName("basicinfo/printAdd");
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(model),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO<PageInfo>> proQueryList(HttpServletRequest request, @ApiParam(value = "查询条件信息对象" ,required=true )  @Valid @RequestBody ProductCommonQueryVo productCommonQueryVo) {
        String employeeId = request.getHeader("employeeId");
        String organSign = request.getHeader("organSign");
        String identity = employeeApi.queryEmployeeById(Integer.valueOf(employeeId)).getResult().getIdentity()+"";
        logger.info("proQueryList employeeId:"+employeeId+",organSign:"+organSign +",identity:"+identity+",stockNum:"+productCommonQueryVo.getStockNum()+",queryAdjust:"+productCommonQueryVo.getQueryAdjust());
        Byte isProductHidden = Byte.valueOf(identity);
        Integer page = productCommonQueryVo.getPage();
        Integer rows = productCommonQueryVo.getRows();
        if ( null == page ) {
            page = 1;
        }
        if ( null == rows ) {
            rows = 10;
        }
        ProductDto product = new ProductDto();
        BeanUtils.copyProperties(productCommonQueryVo,product);
        //默认查询所有的商品
       /* if(null == product.getUsed()) {
            product.setUsed(new Byte("1"));
        }*/
        //默认查询启用
        if(null != isProductHidden) {
            product.setIsHidden(isProductHidden);
        }
        product.setPage(page);
        product.setRows(rows);
        //如果是连锁门店，需要获取总部的机构号
        String modelJson = request.getHeader("commonRequestModel");
        Byte bizModel = null;
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            bizModel = model.getBizModel();
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                product.setSelectOrganSign(organSign);//查询价格的机构号
                organSign = model.getHeadquartersOrganSign();
            }
        }
        product.setOrganSign(organSign);
        return  new ResponseEntity<ResultVO<PageInfo>>(productApi.queryProductListChain(product, productCommonQueryVo.getStockNum(),bizModel),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> sendMsg(HttpServletRequest request,@ApiParam(value = "查询条件信息对象" ,required=true )  @Valid @RequestBody ProductQueryVo productQueryVo) {
        String organSign = request.getHeader("organSign");
        String employeeId = request.getHeader("employeeId");
        //如果是连锁门店，需要获取总部的机构号
        String modelJson = request.getHeader("commonRequestModel");
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                organSign = model.getHeadquartersOrganSign();
            }
        }
        String identity = employeeApi.queryEmployeeById(Integer.valueOf(employeeId)).getResult().getIdentity()+"";
        logger.info("proQueryList employeeId:"+employeeId+",organSign:"+organSign +",identity:"+identity+",ids"+productQueryVo.getIds());
        Map<String, Object> result = productApi.changePrintStatus(productQueryVo.getIds(), organSign);
        if ((boolean) result.get("flag")) {
            JSONObject msg = new JSONObject();
            //设置唯一打印key
            String pref = identity + System.currentTimeMillis();
            msg.put("code", "pricePrint");
            msg.put("pricePrintList", String.valueOf(result.get("msg")));
            msg.put("pref", pref);
            pushProductMessToMQ(organSign);
            String printResult = productApi.printProductPrice(organSign, Integer.valueOf(employeeId), pref, JSON.toJSONString(msg));
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(printResult),HttpStatus.OK);
        } else {
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess("changePrintStatus fail"),HttpStatus.OK);
        }
    }

    @Override
    public ResponseEntity<ResultVO<List<ProductDto>>> proList(HttpServletRequest request, @ApiParam(value = "查询条件信息对象" ,required=true )  @Valid @RequestBody  ProductDto product) {
        long start_time0 = System.currentTimeMillis();
//        String identity = request.getHeader("identity");
        String organSign = request.getHeader("organSign");
        String currentOrgan = organSign;
        String employee = request.getHeader("employeeId");
        String identity = employeeApi.queryEmployeeById(Integer.valueOf(employee)).getResult().getIdentity()+"";
        logger.info("organSign:" + organSign + ",employee:" + employee + ",identity:" + identity);
        Byte isProductHidden = Byte.valueOf(identity);
        Integer page = null;
        Integer rows = null;
        if ( null == product.getPage() ){
            page = 1;
        }else{
            page = product.getPage();
        }

        if ( null == product.getRows() ){
            rows = 10;
        }else{
            rows = product.getRows();
        }
        //默认查询启用
        if(null == product.getUsed()) {
            product.setUsed(null);
        }
        product.setYn((byte) 1);
        //默认查询启用
        if(null != isProductHidden) {
            product.setIsHidden(isProductHidden);
        }

        if(!StringUtils.isEmpty(product.getCreateTimeStart())){
            product.setCreateTimeStart(product.getCreateTimeStart()+" 00:00:00");
        }
        if(!StringUtils.isEmpty(product.getCreateTimeEnd())){
            product.setCreateTimeEnd(product.getCreateTimeEnd()+" 23:59:59");
        }
        product.setPage(page);
        product.setRows(rows);
        //如果是连锁门店，需要获取总部的机构号
        String modelJson = request.getHeader("commonRequestModel");
        Byte bizModel = null;
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                product.setSelectOrganSign(organSign);//查询价格的机构号
                organSign = model.getHeadquartersOrganSign();
            }
            bizModel = model.getBizModel();
        }
        product.setOrganSign(organSign);
        logger.info("query product list user api need time:"+(System.currentTimeMillis() - start_time0)+"ms");
        long start_time = System.currentTimeMillis();
        ResultVO result = this.productApi.productListChain(product,bizModel);
        logger.info("query product list api need time:"+(System.currentTimeMillis() - start_time)+"ms");
        if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(bizModel) || DrugstoreBizModelEnum.CHAIN_STORE.toEquals(bizModel)){//连锁和联营直接返回
            if(product.getAddPriceFlag() != null && product.getAddPriceFlag() == 1){
                PageInfo info = (PageInfo) result.getResult();
                List<ProductDto> dtos = info.getList();
                if (null != dtos && dtos.size() > 0) {
                    List<String> prefs = new ArrayList<>();
                    dtos.forEach(item -> {
                        prefs.add(item.getPref());
                    });
                    CostPriceQueryProductDto queryProductDto = new CostPriceQueryProductDto();
                    queryProductDto.setChainOrganSign(currentOrgan);//门店机构号
                    queryProductDto.setProductPrefs(prefs);//商品内码集合
                    ResultVO priceResult = productCostPriceApi.getProductPriceInfosByPrefs(queryProductDto);
                    List<CostAdjustsByPrefResultDetailDto> prceList = (List<CostAdjustsByPrefResultDetailDto>) priceResult.getResult();
                    Map<String,CostAdjustsByPrefResultDetailDto> priceMaps = new HashMap<>();
                    if(!CollectionUtils.isEmpty(prceList)){
                        prceList.stream().forEach(priceVo -> {
                            priceMaps.put(priceVo.getProductPref(),priceVo);
                        });
                    }
                    dtos.stream().forEach(priceDto ->{
                        if(priceMaps.get(priceDto.getPref()) != null){
                            priceDto.setPriceUpper(priceMaps.get(priceDto.getPref()).getPriceUpper());//价格上限
                            priceDto.setPriceLower(priceMaps.get(priceDto.getPref()).getPriceLower());//价格下限
                        }
                    });
                }
            }
            return new ResponseEntity<ResultVO<List<ProductDto>>>(result,HttpStatus.OK);
        }

        //单体逻辑
        long start_time1 = System.currentTimeMillis();
        Boolean roleExt = permissionApi.queryProductExtendPermissionByRoleId(Integer.parseInt(employee));
        if (roleExt){
            PageInfo info = (PageInfo) result.getResult();
            List<ProductDto> dtos = info.getList();
            if (null != dtos && dtos.size() > 0){
                List<String> prefs = new ArrayList<>();
                dtos.forEach(item -> {
                    prefs.add(item.getPref());
                });
                //调用库存的接口，扩展属性相关
                List<ProductExtendInventoryVo> extS = inventoryApi.findProductExtendInventoryAll(prefs, currentOrgan);
                Map<String, ProductExtendInventoryVo> map = new HashMap<>();
                extS.forEach(item -> {
                    map.put(item.getProductPref(), item);
                });
                //将商品对应的扩展属性set到商品实体类中
                dtos.forEach(item -> {
                    com.xyy.saas.inventory.core.dto.ProductExtendInventoryVo productExtendInventoryVo = new com.xyy.saas.inventory.core.dto.ProductExtendInventoryVo();
                    ProductExtendInventoryVo vos = map.get(item.getPref());
                    BeanUtils.copyProperties(vos,productExtendInventoryVo);
                    item.setExtProperty(productExtendInventoryVo);
                });
                info.setList(dtos);
                result.setResult(info);
            }
        }

        logger.info("query product list inventory api need time:"+(System.currentTimeMillis() - start_time1)+"ms");
        return new ResponseEntity<ResultVO<List<ProductDto>>>(result,HttpStatus.OK);
    }

    @Override
    public void exportExcelProduct(HttpServletRequest request, HttpServletResponse response,
                                   @ApiParam(value = "当前登录机构码" ,required=true) @RequestHeader(value="organSign", required=true) String organSign,
                                   @ApiParam(value = "当前登录员工的ID" ,required=true) @RequestHeader(value="employeeId", required=true) String employeeId,
                                   @ApiParam(value = "商品详情信息对象" ,required=true )  @Valid @RequestBody ProductExportDto product) {
//        String organSign = request.getHeader("organSign");
//        String employeeId = request.getHeader("employeeId");
        logger.info("exportExcelProduct organSign: going into method");
        String identity = employeeApi.queryEmployeeById(Integer.valueOf(employeeId)).getResult().getIdentity()+"";
        logger.info("exportExcelProduct organSign:" + organSign + ",employee:" + employeeId + ",identity:" + identity);
//        String identity = request.getHeader("identity");
        Byte isProductHidden = Byte.valueOf(identity);
        if(isProductHidden!=null){
            product.setIsHidden(isProductHidden);
        }
        product.setOrganSign(organSign);
        List<ProductExportDto> list = this.productApi.exportProductList(product);
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
//        String extfilename="商品基本信息"+df.format(new Date())+".xls";
        String extfilename="product baseinfo information"+df.format(new Date())+".xls";
        String sheetName = "商品基本信息";
        String headers[] = new String[]{"状态", "商品编号", "通用名", "商品名称", "单位", "剂型", "规格/型号", "生产厂家","产地","商品类别", "ABC分类", "处方分类", "零售价", "会员价", "架位", "存储条件","条形码","批准文号","积分商品","特价商品","处方登记","养护类型","是否启用","标准库id"};
        String fieldNames[] = new String[]{"statusName", "pharmacyPref", "commonName", "productName", "unitName", "agentName", "attributeSpecification", "manufacturer","producingArea","categoryName", "abcDividingName",
                "prescriptionName", "retailPrice", "vipPrice","shelfPostionName","storageConditionName","barCode","approvalNumber","scoreYnStr","specialStr","prescriptionYnStr","maintenanceName","usedStr","standardLibraryId"};
        try {
            ExportExcelUtil.createExcel(response,request,extfilename,sheetName, headers, fieldNames, list,true);
        } catch (Exception e) {
            logger.error("",e);
        }
    }

    @Override
    public ResponseEntity<ResultVO> toAdd(HttpServletRequest request, Model model) throws UnknownHostException {
        String organSign = request.getHeader("organSign");
        Map<String, List<SystemDictDto>> dict = this.getAddOrUpdateDict(organSign);
        Set<String> keys = dict.keySet();
        for (String key : dict.keySet()) {
            if ("unitDict".equals(key)) {
                JSONArray array = listToJsonArray(key, dict.get(key));
                model.addAttribute(key, array.toString());
            } else if ("agentDict".equals(key)) {
                JSONArray array = listToJsonArray(key, dict.get(key));
                model.addAttribute(key, array.toString());
            } else if ("scopeDict".equals(key)) {
                JSONArray array = listToJsonArray(key, dict.get(key));
                model.addAttribute(key, array.toString());
            } else {
                model.addAttribute(key, dict.get(key));
            }
        }

        SaasPositionVo sp = new SaasPositionVo();
        sp.setOrgansign(organSign);
        List<SaasPositionVo> positionList = poSitionApi.getPositionList(sp).getContent();
        model.addAttribute("positionList", positionList);
//        if (iRemoteBean.isSuccess()) {
//            model.addAttribute("positionList", positionList);
//        } else {
//            model.addAttribute("positionList", new ArrayList<SaasPositionVo>());
//            logger.info("ProductInfoController.toAdd调用rpc接口poSitionApi.getPositionList(...)失败：{}", iRemoteBean.getResultMsg());
//        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(model),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> addOrUpdate(HttpServletRequest request, @ApiParam(value = "商品详情信息对象" ,required=true )  @Valid @RequestBody ProductDto product) {
        logger.info("addOrUpdate productbaseinfo:" + product.getOrganSign() + "--->" + JSONObject.toJSON(product));
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        if (StringUtils.isEmpty(product)) {
            logger.error("==============参数不合法");
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, null),HttpStatus.OK);
        }
        ResultVO<Boolean> paramResult = productParamLengthCheck(product);
        if (!paramResult.getResult()){
            logger.error("==============参数不合法 "+paramResult.getMsg());
            return new ResponseEntity<ResultVO>(paramResult, HttpStatus.OK);
        }
        if (StringUtils.isEmpty(product.getPref())) {
            product.setScatteredYn((byte) 0);
        }
        if (null != product.getYbmSync() && 1 == product.getYbmSync()){
            product.setQuickYn((byte) 1);
        }
        String modelJson = request.getHeader("commonRequestModel");
        Byte bizModel = null;
        Byte organSignType = null;
        String headquartersOrganSign = "";
        if (!StringUtils.isEmpty(modelJson)) {
            CommonRequestModel model = JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            bizModel = model.getBizModel();
            organSignType = model.getOrganSignType();
            headquartersOrganSign = model.getHeadquartersOrganSign();
        }
        if (DrugstoreBizModelEnum.JOINT_OPERATION.getKey()==(int)bizModel&&DrugstoreTypeEnum.HEADQUARTERS.getKey()==(int)organSignType&&!StringUtils.isEmpty(product.getOrganSign())){
            organSign = product.getOrganSign();
        }
        //如果是联营总部
        if(DrugstoreBizModelEnum.JOINT_OPERATION.getKey()==(int)bizModel&&DrugstoreTypeEnum.HEADQUARTERS.getKey()==(int)organSignType){
            product.setLyHeader(true);
        }
        product.setOrganSign(organSign);
        product.setScatteredYn(null);//此字段属性和当前业务无关
//        if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(bizModel)){//处理产地相关的逻辑
//            if(StringUtils.isEmpty(product.getAreaCode())){
//                //产地
//                Map<String,String> areCodeMap = new HashMap<>();
//                Set<Integer> areCodes = new HashSet<>();
//                String[] areCodestrs = product.getAreaCode().split(",");
//                for(String str : areCodestrs){
//                    if(!StringUtils.isEmpty(str)){
//                        areCodes.add(Integer.valueOf(str));
//                    }
//                }
//                if (!CollectionUtils.isEmpty(areCodes)) {
//                    XyySaasRegionParamsDto paramsDto = new XyySaasRegionParamsDto();
//                    paramsDto.setAreaCodes(new ArrayList(areCodes));
//                    List<SaasRegionBusinessDto> xyyRegionBusinessDtos = saasAreaApi.queryRegionByAreaCodeList(paramsDto);
//                    areCodeMap = xyyRegionBusinessDtos.stream().collect((Collectors.toMap(p -> p.getAreaCode() + "", p -> p.getAreaName())));
//                }
//                StringBuffer sb = new StringBuffer("");
//                for(String str : areCodestrs){
//                    if(!StringUtils.isEmpty(str)){
//                        sb.append(areCodeMap.get(str));
//                    }
//                }
//                product.setProducingArea(sb.toString());
//            }
//        }
        product.setProductType(product.getProductType() == null ? 0 : product.getProductType()); //自定义分类
        product.setProductFunctionCatagory (product.getProductFunctionCatagory() == null ? 0 : product.getProductFunctionCatagory());// 功能分类
        if (product.getResetStandardIdFlag() != null && product.getResetStandardIdFlag()) {
            product.setStandardLibraryId(null);
            product.setVirtualStandardLibraryId(0L);
        }
        ProductPrefDto productPrefDto = productApi.productAddOrUpdateChain(product, Integer.parseInt(employee), bizModel,headquartersOrganSign);
        logger.info("addOrUpdate productbaseinfo result status:{}", productPrefDto.getStatus());
        if (productPrefDto.getStatus() > 0) {
            // 将数据推送到mq和es  连锁门店 需要推送总部下所有的机构
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(bizModel)){
                List<SaaSDrugstoreDto> drugs = drugstoreApi.getDrugstoreByHeadquartersOrganSign(organSign);//获取门店列表
                if(!CollectionUtils.isEmpty(drugs)){
                    for(SaaSDrugstoreDto dto : drugs){
                        pushProductMessToMQ(dto.getOrganSign());
                    }
                }
            }else{
                pushProductMessToMQ(organSign);
            }
            ResultVO resultVO = new ResultVO();
            resultVO.setCode(0);
            resultVO.setMsg("save success");
            resultVO.setResult(productPrefDto);
            return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
        } else if (-1 == productPrefDto.getStatus()) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "新增  标准库ID已存在",-1),HttpStatus.OK);
        }else if (-2 == productPrefDto.getStatus()) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "商品编号已存在，请重新输入",-2),HttpStatus.OK);
        }else if (-3 == productPrefDto.getStatus()) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "提交异常，请重新提交",-3),HttpStatus.OK);
        }else if (-4 == productPrefDto.getStatus()) {
            String msg = "商品库里已存在该商品（商品编号："+productPrefDto.getPharmacyPref()+"），请核对商品信息后重新提交！";
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, msg,-4),HttpStatus.OK);
        }  else if (-5 == productPrefDto.getStatus()) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "提交失败,审批流生成异常,请重新提交！", -5), HttpStatus.OK);
        } else if (-6 == productPrefDto.getStatus()) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, productPrefDto.getResultMsg(), -6), HttpStatus.OK);
        } else if (-7 == productPrefDto.getStatus()) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "商品名称、通用名、条形码过长！", -7), HttpStatus.OK);
        }else {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "发生未知错误，需要进行排查"),HttpStatus.OK);
        }
    }

    @Override
    public ResponseEntity<ResultVO> forSaveProductPrice(HttpServletRequest request, @ApiParam(value = "商品详情信息对象" ,required=true )  @Valid @RequestBody ProductDto product) {
        String organSign = request.getHeader("organSign");
        //String organSign="ZHL00000275";
        if(StringUtils.isEmpty(product)){
            logger.error("==============参数不合法");
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, null),HttpStatus.OK);
        }
        String s = productApi.forSaveProductPrice(organSign, product);
        if(s!=null){
            ResultVO resultVO = JSON.parseObject(s, ResultVO.class);
            return new ResponseEntity<ResultVO>(resultVO,HttpStatus.OK);
        }
        String[] strings1 = new String[0];
        return new ResponseEntity<ResultVO>(new ResultVO(strings1),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> forResertBinnaryId(HttpServletRequest request, @ApiParam(value = "商品详情信息对象" ,required=true )  @Valid @RequestBody ProductDto product) {
        String organSign = request.getHeader("organSign");
        //String organSign="ZHL00000275";
        if(StringUtils.isEmpty(product)){
            logger.error("==============参数不合法");
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "null"),HttpStatus.OK);
        }
        SystemConfigDto systemConfigDto = sysConfigApi.querySystemConfig(organSign);
        Byte healthNoteSwitch = systemConfigDto.getHealthNoteSwitch();

        if(healthNoteSwitch==1){

            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.SUCCESS, "success","success"),HttpStatus.OK);
        }

        return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.SUCCESS, "success",""),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getProductById(HttpServletRequest request, @ApiParam(value = "商品详情信息对象" ,required=true )  @Valid @RequestBody ProductQueryVo productQueryVo) {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        String identity = employeeApi.queryEmployeeById(Integer.valueOf(employee)).getResult().getIdentity()+"";
        logger.info("organSign:" + organSign + ",employee:" + employee + ",identity:" + identity+",id:"+productQueryVo.getId());
        logger.info("getProductById organSign:{}", productQueryVo.getOrganSign());
//        String identity = request.getHeader("identity");

//        ModelClassVo model = new ModelClassVo();
//        if (null == productQueryVo.getId())
//            model.addAttribute("result", new ResultVO<>(ResultCodeEnum.ERROR, null));
        String priceQueryOrganSign = "";
        //如果是连锁门店，需要获取总部的机构号
        String modelJson = request.getHeader("commonRequestModel");
        CommonRequestModel model = JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
        Byte bizModel = model.getBizModel();
        logger.info("getProductById modelJson:{}", modelJson);
        if(!StringUtils.isEmpty(modelJson)) {
            if (DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())) {
                priceQueryOrganSign = organSign;
                organSign = model.getHeadquartersOrganSign();
            }
            if (DrugstoreBizModelEnum.JOINT_OPERATION.getKey()==(int)model.getBizModel()&&DrugstoreTypeEnum.HEADQUARTERS.getKey()==(int)model.getOrganSignType()&&!StringUtils.isEmpty(productQueryVo.getOrganSign())){
                organSign = productQueryVo.getOrganSign();
            }
        }
        ProductDto product = this.productApi.getProductByIdAndOrganSignChain(productQueryVo.getId(), organSign, Byte.valueOf(identity),priceQueryOrganSign,bizModel);
        //有效期转换显示
        product.setProductValidity(productToolApi.changeProductValidityDateToShow(product.getProductValidity()));
        // 连锁总部
        if (DrugstoreBizModelEnum.CHAIN_STORE.toEquals(bizModel) && DrugstoreTypeEnum.HEADQUARTERS.toEquals(model.getOrganSignType())) {
            QueryDictByOrgTypeDto queryDictByOrgTypeDto = new QueryDictByOrgTypeDto();
            queryDictByOrgTypeDto.setOrganSign(request.getHeader("organSign"));
            queryDictByOrgTypeDto.setOrgType(bizModel);
            queryDictByOrgTypeDto.setBusinessId(DictConstant.scopeOfOperation.toString());

            List<SystemDictDto> systemDictDtos = this.queryDictByOrgType(queryDictByOrgTypeDto);
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(systemDictDtos)) {
                Map<Integer, SystemDictDto> systemDictMap = systemDictDtos.stream().collect(Collectors.toMap(SystemDictDto::getId, Function.identity(), (k1, k2) -> k1));
                SystemDictDto systemDictDto = systemDictMap.get(product.getBusinessScope());
                if (systemDictDto != null) {
                    SystemDictDto parentSystemDictDto = systemDictMap.get(systemDictDto.getParentId());
                    StringBuilder sb = new StringBuilder();
                    if (parentSystemDictDto != null) {
                        sb.append(parentSystemDictDto.getName()).append(" / ");
                    }
                    sb.append(systemDictDto.getName());
                    product.setScopeName(sb.toString());
                }
            }
        }

        product.setPrescriptionClassification((product.getPrescriptionClassification() == null || product.getPrescriptionClassification() == -1 )?null:product.getPrescriptionClassification());
        product.setDosageFormId((product.getDosageFormId() == null || product.getDosageFormId() == 0 )? null:product.getDosageFormId());
        product.setAbcDividing((product.getAbcDividing() == null || product.getAbcDividing() == -1 )? null:product.getAbcDividing());
        product.setStorageCondition((product.getStorageCondition() == null || product.getStorageCondition() == -1 )? null:product.getStorageCondition());
//        model.addAttribute("result", product);
//        logger.info("getProductById productId:" + product.getId() +", drugRemindId:"+ product.getDrugRemindId() +", remind:"+ product.getReminder());
        if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(bizModel) || DrugstoreBizModelEnum.JOINT_OPERATION.toEquals(bizModel)){
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(product),HttpStatus.OK);
        }
        //单体逻辑
        Boolean roleExt = permissionApi.queryProductExtendPermissionByRoleId(Integer.parseInt(employee));
        if (roleExt){
            //TODO  ===============>>>>>>  缺库存和提成管理的接口
            ProductExtendInventoryVo inventoryVo = inventoryApi.findProductExtendInventory(product.getPref(), organSign);
            com.xyy.saas.inventory.core.dto.ProductExtendInventoryVo vos = new com.xyy.saas.inventory.core.dto.ProductExtendInventoryVo();
            BeanUtils.copyProperties(inventoryVo,vos);
            product.setExtProperty(vos);//从以前的回显接口中取出库存扩展的相关信息
        }


        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(product),HttpStatus.OK);
    }

    public List<SystemDictDto> queryDictByOrgType(QueryDictByOrgTypeDto queryDto) {
        logger.info("查询字典 query: {}, organSign: {}", JSON.toJSONString(queryDto));
        int pageNo = 1;
        List<SystemDictDto> result = new ArrayList<>();
        try {
            while(true && pageNo <= 100) {
                queryDto.setPageNum(pageNo);
                queryDto.setPageSize(100);
                ResultVO<PageInfo<SystemDictDto>> page = systemDictApi.queryDictByOrgType(queryDto);
                if (page == null) {
                    break;
                }
                PageInfo<SystemDictDto> pageInfo = page.getResult();
                if (pageInfo == null){
                    break;
                }
                List<SystemDictDto> list = pageInfo.getList();
                if (!org.apache.commons.collections.CollectionUtils.isEmpty(list)) {
                    result.addAll(list);
                }
                // 查询到最后一页退出
                if (pageInfo.isIsLastPage()) {
                    break;
                }
                pageNo++;
            }
            return result;
        } catch (Exception e) {
            logger.error("查询字典异常 query: {}", queryDto, e);
            Cat.logError(e);
            return Collections.EMPTY_LIST;
        }
    }

    @Override
    public ResponseEntity<ResultVO> toList(HttpServletRequest request, Model model) {
        // 架位
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employee");
        SaasPositionVo sp = new SaasPositionVo();
        sp.setOrgansign(organSign);
        List<SaasPositionVo> positionList = new ArrayList<>();
        positionList = poSitionApi.getPositionList(sp).getContent();
        List<Integer> ids = Arrays.asList(DictConstant.ABC_BUSSINESS_ID, DictConstant.commodtyTypeBussinessId, DictConstant.STORE_CONDITION_BUSINESS_ID,
                DictConstant.unitBussinessId, DictConstant.agentBussinessId, DictConstant.prescriptionBussinessId,DictConstant.maintenanceType);
        List<SystemDictDto> dictDtos = productDictionaryApi.findSystemDictDtoBybussinessIds(ids,organSign);

        // ABC分类
        List<SystemDictDto> abcDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.ABC_BUSSINESS_ID)).collect(Collectors.toList());
        // 商品类别
        List<SystemDictDto> proTypeDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.commodtyTypeBussinessId)).collect(Collectors.toList());
        // 储存条件
        List<SystemDictDto> storeDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.STORE_CONDITION_BUSINESS_ID)).collect(Collectors.toList());
        // 单位
        List<SystemDictDto> unitDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.unitBussinessId)).collect(Collectors.toList());
        // 剂型
        List<SystemDictDto> agentDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.agentBussinessId)).collect(Collectors.toList());
        // 处方分类
        List<SystemDictDto> cfDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.prescriptionBussinessId)).collect(Collectors.toList());
        //增加养护类型
        List<SystemDictDto> maintenanceDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.maintenanceType)).collect(Collectors.toList());
        //是否展示扩展属性字段
        Boolean roleExt = permissionApi.queryProductExtendPermissionByRoleId(Integer.parseInt(employee));
        //是否启用
        SystemDictDto isEnable = new SystemDictDto();
        isEnable.setName("是");
        isEnable.setId(1);
        SystemDictDto isEnable1 = new SystemDictDto();
        isEnable1.setName("否");
        isEnable1.setId(0);
        List<SystemDictDto> isUsed = new ArrayList<>();
        isUsed.add(isEnable);
        isUsed.add(isEnable1);
        if (roleExt){
            model.addAttribute("roleExt", 1);
        }else {
            model.addAttribute("roleExt", 0);
        }

        model.addAttribute("positionList", positionList);
        model.addAttribute("abcDict", abcDict);
        model.addAttribute("proTypeDict", proTypeDict);
        model.addAttribute("storeDict", storeDict);
        model.addAttribute("cfDict", cfDict);
        model.addAttribute("isUsed", isUsed);

        model.addAttribute("posList", JSONObject.toJSON(positionList).toString());
        model.addAttribute("abcList", JSONObject.toJSON(abcDict).toString());
        model.addAttribute("proTypeList", JSONObject.toJSON(proTypeDict).toString());
        model.addAttribute("storeList", JSONObject.toJSON(storeDict).toString());
        model.addAttribute("unitList", JSONObject.toJSON(unitDict).toString());
        model.addAttribute("cfList", JSONObject.toJSON(cfDict).toString());
        model.addAttribute("usedList", JSONObject.toJSON(isUsed).toString());
        model.addAttribute("maintenanceList", JSONObject.toJSON(maintenanceDict).toString());
        List<Map<String, Object>> agentList = new ArrayList<>();
        agentDict.forEach(item -> {
            Map<String, Object> map = new HashMap<>();
            map.put("id", item.getId());
            map.put("name", item.getName());
            agentList.add(map);
        });
        model.addAttribute("agentList", JSONObject.toJSON(agentList).toString());
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(model),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> toStandardLibraryList(HttpServletRequest request, Model model) {
        String organSign = request.getHeader("organSign");
        List<Integer> ids = Arrays.asList(DictConstant.unitBussinessId, DictConstant.agentBussinessId, DictConstant.prescriptionBussinessId);
        List<SystemDictDto> dictDtos = productDictionaryApi.findSystemDictDtoBybussinessIds(ids,organSign);
        // 单位
        List<SystemDictDto> unitDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.unitBussinessId)).collect(Collectors.toList());
        // 剂型
        List<SystemDictDto> agentDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.agentBussinessId)).collect(Collectors.toList());
        // 处方分类
        List<SystemDictDto> cfDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.prescriptionBussinessId)).collect(Collectors.toList());

        model.addAttribute("unitList", JSONObject.toJSON(unitDict).toString());
        List<Map<String, Object>> agentList = new ArrayList<>();
        agentDict.forEach(item -> {
            Map<String, Object> map = new HashMap<>();
            map.put("id", item.getId());
            map.put("name", item.getName());
            agentList.add(map);
        });
        model.addAttribute("agentList", JSONObject.toJSON(agentList).toString());
        model.addAttribute("cfList", JSONObject.toJSON(cfDict).toString());
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(model),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> query(HttpServletRequest request, @ApiParam(value = "标准库信息查询" ,required=true )  @Valid @RequestBody BinnaryQueryDto vo) throws Exception {
        if (vo.getProductName()!=null&&vo.getProductName().length()>50){
            return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.ERROR,"查询条件过长 不可大于50个长度"),HttpStatus.OK);
        }
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        if (vo.getPage() == null) {
            vo.setPage(1);
        }
        if (vo.getRows() == null || vo.getRows() == 0) {
            vo.setRows(10);
        }
        //获取登录信息
        String modelJson = request.getHeader("commonRequestModel");
        Byte bizModel = null;
        String headquartersOrganSign = "";
        //经营模式 1、单体 2、连锁 3、联营
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            bizModel = model.getBizModel();
            headquartersOrganSign = bizModel != 2 ? organSign : model.getHeadquartersOrganSign();
        }
        //使用总部机构号
        if(!StringUtils.isEmpty(headquartersOrganSign)){
            organSign = headquartersOrganSign;
        }
        //标准库调用--
        ResultVO result = standardLibaryApi.findStdProsWithLocalProsPageForHttpChain(vo,organSign,employee,bizModel);
        return new ResponseEntity<ResultVO>(result,HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> refreshZJMByOrganSigns(HttpServletRequest request, @ApiParam(value = "请求信息对象" ,required=true )  @Valid @RequestBody RefreshDataVo refreshDataVo) {
        logger.info("refreshZJMByOrganSigns params:organSigns:"+refreshDataVo.getOrganSigns()+",type:"+refreshDataVo.getType());
        String organSigns = refreshDataVo.getOrganSigns();
        if(StringUtils.isEmpty(organSigns)){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR,"请填写正确的机构号，注意格式，多个机构号以逗号分隔"),HttpStatus.OK);
        }
        String[] organSignArray = refreshDataVo.getOrganSigns().split(",");
        List<String> organSignList = new ArrayList<>();
        for(String organSign:organSignArray){
            organSignList.add(organSign);
        }
        if(organSignList.size() < 1){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR,"请至少填写正确的机构号"),HttpStatus.OK);
        }
        String result = "";
        try{
            result = productApi.refreshZJMByOrganSigns(organSignList);
        }catch (Exception e){
            logger.error("refreshZJMByOrganSigns occur error:"+e);
            result = "exception";
        }
        if(result.equals("success")){
            //数据刷新之后推送mq
            for(String organSign:organSignList){
                refreshDataSuccToMQ(organSign);
            }
        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(result),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> listAllDrugRemind(HttpServletRequest request) {
        String organSign = request.getHeader("organSign");
        //如果是连锁门店，需要获取总部的机构号
        String modelJson = request.getHeader("commonRequestModel");
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                organSign = model.getHeadquartersOrganSign();
            }
        }
        List<SystemDrugRemindDTO> list = systemDrugRemindApi.listAllByOrganSign(organSign);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(list),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> findDrugRemindByRemindId(String drugRemindId, String organSign) {
        boolean flag = productApi.findDrugRemindByRemindId(Integer.valueOf(drugRemindId), organSign);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(flag), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getProductsByParamsForYkq(HttpServletRequest request, @ApiParam(value = "请求信息对象" ,required=true )  @Valid @RequestBody YkqQueryVo ykqQueryVo) {
        if(StringUtils.isEmpty(ykqQueryVo.getOrganSign())){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR,"机构号不能为空"),HttpStatus.OK);
        }
        if(StringUtils.isEmpty(ykqQueryVo.getPrefs())){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR,"商品编号不能为空"),HttpStatus.OK);
        }
        String[] prefArry = ykqQueryVo.getPrefs().split(",");
        if(prefArry == null || prefArry.length == 0){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR,"商品编号不能为空"),HttpStatus.OK);
        }
        String organSign = ykqQueryVo.getOrganSign();//获取机构号
        List<String> prefList = Arrays.asList(prefArry);//获取机构号list
        Map<String,Object> param = new HashMap<>();
        param.put("organSign",organSign);
        param.put("list",prefList);
        List<ProductDto> productDtos = productApi.getProductsByParamsForYkq(param);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(productDtos),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> queryProductForYkq(HttpServletRequest request, @ApiParam(value = "查询条件信息对象" ,required=true )  @Valid @RequestBody ProductDto product) {
        if(StringUtils.isEmpty(product.getOrganSign())){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR,"机构号不能为空"), HttpStatus.OK);
        }
        Integer page = null;
        Integer rows = null;
        if ( null == product.getPage() ){
            page = 1;
        }else{
            page = product.getPage();
        }

        if ( null == product.getRows() ){
            rows = 20;
        }else{
            rows = product.getRows();
        }

        product.setPage(page);
        product.setRows(rows);
        ResultVO result = this.productApi.queryProductForYkq(product);

        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getAppointTypeList(@ApiParam(value = "当前登录机构码" ,required=true) @RequestHeader(value="organSign", required=true) String organSign,
                                                       @ApiParam(value = "当前登录员工的ID" ,required=true) @RequestHeader(value="employeeId", required=true) String employeeId,
                                                       @ApiParam(value = "请求字典类型" ,required=true )  @Valid @RequestBody SystemDictQueryVo systemDictQueryVo) {
        // 查询字典表
        Map<String, List<SystemDictVo>> map = new HashMap<>();
        Integer type = systemDictQueryVo.getType();
        if(type == 0 || type == 10000){
            //获取商品架位：
            SaasPositionVo sp = new SaasPositionVo();
            sp.setPositionStatus(1);
            sp.setOrgansign(organSign);
            IRemoteBean<List<SaasPositionVo>> iRemoteBean = poSitionApi.getPositionList(sp);
            List<SaasPositionVo> positionList = new ArrayList<>();
            if (iRemoteBean.isSuccess()) {
                positionList = iRemoteBean.getContent();
            }
            List<SystemDictVo> positionJonList = new ArrayList<>();
            if(positionList != null && positionList.size() > 0){
                for(SaasPositionVo vo:positionList){
                    SystemDictVo systemDictVo = new SystemDictVo();
                    systemDictVo.setId(vo.getId());
                    systemDictVo.setName(vo.getName());
                    positionJonList.add(systemDictVo);
                }
            }
            //货位列表
            map.put("positionList", positionJonList);//架位id，架位名称
        }
        if(systemDictQueryVo.getType() == 10000){
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(map), HttpStatus.OK);
        }
        List<Integer> ids = new ArrayList<>();
        switch(type){
            case 0:
                ids.add(DictConstant.commodtyTypeBussinessId);
                ids.add(DictConstant.productSystemType);
                ids.add(DictConstant.ABC_BUSSINESS_ID);
                break;
            case 10002:
                ids.add(DictConstant.commodtyTypeBussinessId);
                break;
            case 10101:
                ids.add(DictConstant.productSystemType);
                break;
            case 20001:
                ids.add(DictConstant.ABC_BUSSINESS_ID);
                break;
            default:return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.ERROR,"类型不支持"), HttpStatus.OK);

        }
        List<SystemDictDto> dictDtos = productDictionaryApi.findSystemDictDtoBybussinessIds(ids,organSign,1,1);
        switch(type){
            case 0:
                //商品自定义分类
                List<SystemDictDto> proTypeDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.commodtyTypeBussinessId)).collect(Collectors.toList());
                List<SystemDictVo> proTypeVo = new ArrayList<>();
                for(SystemDictDto dto:proTypeDict){
                    SystemDictVo vo = new SystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    proTypeVo.add(vo);
                }
                map.put("customTypeDict", proTypeVo);
                //商品分类
                List<SystemDictDto> comproDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.productSystemType)).collect(Collectors.toList());
                List<SystemDictVo> comproVo = new ArrayList<>();
                for(SystemDictDto dto:comproDict){
                    SystemDictVo vo = new SystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    comproVo.add(vo);
                }
                map.put("systemTypeDict",comproVo);
                // ABC分类
                List<SystemDictDto> abcDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.ABC_BUSSINESS_ID)).collect(Collectors.toList());
                List<SystemDictVo> abcVo = new ArrayList<>();
                for(SystemDictDto dto:abcDict){
                    SystemDictVo vo = new SystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    abcVo.add(vo);
                }
                map.put("abcTypeDict", abcVo);
                break;
            case 10002:
                List<SystemDictDto> proTypeDict1 = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.commodtyTypeBussinessId)).collect(Collectors.toList());
                List<SystemDictVo> proTypeVo1 = new ArrayList<>();
                for(SystemDictDto dto:proTypeDict1){
                    SystemDictVo vo = new SystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    proTypeVo1.add(vo);
                }
                map.put("customTypeDict", proTypeVo1);
                break;
            case 10101:
                //商品系统类型
                List<SystemDictDto> comproDict1 = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.productSystemType)).collect(Collectors.toList());
                List<SystemDictVo> comproVo1 = new ArrayList<>();
                for(SystemDictDto dto:comproDict1){
                    SystemDictVo vo = new SystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    comproVo1.add(vo);
                }
                map.put("systemTypeDict",comproVo1);
                break;
            case 20001:
                // ABC分类
                List<SystemDictDto> abcDict1 = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.ABC_BUSSINESS_ID)).collect(Collectors.toList());
                List<SystemDictVo> abcVo1 = new ArrayList<>();
                for(SystemDictDto dto:abcDict1){
                    SystemDictVo vo = new SystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    abcVo1.add(vo);
                }
                map.put("abcTypeDict", abcVo1);
                break;
        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(map), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getCanTakePartInPromotionProduct(HttpServletRequest request, @ApiParam(value = "促销请求查询对象" ,required=true )  @Valid @RequestBody PromotionQueryVo promotionQueryVo) {
        if(promotionQueryVo == null){
            return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.ERROR,"参数不能为空"),HttpStatus.OK);
        }
        String organSign = request.getHeader("organSign");
        //String organSign="ZHL0f2ce7b9";
        if (promotionQueryVo.getPage() == null) {
            promotionQueryVo.setPage(1);
        }
        if (promotionQueryVo.getRows() == null) {
            promotionQueryVo.setRows(50);
        }
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(promotionQueryVo.getRows());
        pageInfo.setPageNum(promotionQueryVo.getPage());
        PromotionQueryDto promotionQueryDto = new PromotionQueryDto();
        BeanUtils.copyProperties(promotionQueryVo,promotionQueryDto);
        promotionQueryDto.setOrganSign(organSign);
        promotionQueryDto.setMinGrossMargin((promotionQueryDto.getMinGrossMargin() != null && promotionQueryDto.getMinGrossMargin().compareTo(new BigDecimal(0)) == 1) ? promotionQueryDto.getMinGrossMargin():null );
        promotionQueryDto.setMaxGrossMargin((promotionQueryDto.getMaxGrossMargin()!= null && promotionQueryDto.getMaxGrossMargin().compareTo(new BigDecimal(0)) == 1)?promotionQueryDto.getMaxGrossMargin():null);
        PageInfo resultPageInfo = productApi.getCanTakePartInPromotionProduct(pageInfo,promotionQueryDto);
        List<PromotionMixResultDto> resultDtos = resultPageInfo.getList();
        List<Integer> ids = new ArrayList<>();
        ids.add(DictConstant.commodtyTypeBussinessId);
        ids.add(DictConstant.productSystemType);
        ids.add(DictConstant.ABC_BUSSINESS_ID);
        ids.add(DictConstant.unitBussinessId);
        ids.add(DictConstant.agentBussinessId);
        List<SystemDictDto> dictDtos = productDictionaryApi.findSystemDictDtoBybussinessIds(ids,organSign,1,1);
        Map<Integer,String> productTypeMap = new HashMap<>();
        Map<Integer,String> customTypeMap = new HashMap<>();
        Map<Integer,String> abcTypeMap = new HashMap<>();
        Map<Integer,String> unitTypeMap = new HashMap<>();
        Map<Integer,String> doseFormTypeMap = new HashMap<>();
        for(SystemDictDto sdd:dictDtos){
            if(sdd.getBussinessId().equals(DictConstant.commodtyTypeBussinessId) ){
                customTypeMap.put(sdd.getId(),sdd.getName());
            }
            if(sdd.getBussinessId().equals(DictConstant.productSystemType) ){
                productTypeMap.put(sdd.getId(),sdd.getName());
            }
            if(sdd.getBussinessId().equals(DictConstant.ABC_BUSSINESS_ID)){
                abcTypeMap.put(sdd.getId(),sdd.getName());
            }
            if(sdd.getBussinessId().equals(DictConstant.unitBussinessId)){
                unitTypeMap.put(sdd.getId(),sdd.getName());
            }
            if(sdd.getBussinessId().equals(DictConstant.agentBussinessId) ){
                doseFormTypeMap.put(sdd.getId(),sdd.getName());
            }
        }
        List<PromotionMixResultVo> vos = new ArrayList<>();
        for(PromotionMixResultDto dto:resultDtos){
            dto.setSystemTypeName(productTypeMap.get(dto.getSystemTypeId()));
            dto.setCustomTypeName(customTypeMap.get(dto.getCustomTypeId()));
            dto.setAbcTypeName(abcTypeMap.get(dto.getAbcTypeId()));
            dto.setUnitName(unitTypeMap.get(dto.getUnitId()));
            dto.setDosageFormName(doseFormTypeMap.get(dto.getDosageFormId()));
            PromotionMixResultVo mmv = new PromotionMixResultVo();
            BeanUtils.copyProperties(dto,mmv);
            vos.add(mmv);
        }
        resultPageInfo.setList(vos);
        return new ResponseEntity<ResultVO>(new ResultVO(resultPageInfo),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getCanTakePartInMemberDayProduct(HttpServletRequest request, @ApiParam(value = "请求字典类型" ,required=true )  @Valid @RequestBody MemberDayQueryVo memberDayQueryVo) {
        if(memberDayQueryVo == null){
            return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.ERROR,"参数不能为空"),HttpStatus.OK);
        }
        String organSign = request.getHeader("organSign");
        if (memberDayQueryVo.getPage() == null) {
            memberDayQueryVo.setPage(1);
        }
        if (memberDayQueryVo.getRows() == null) {
            memberDayQueryVo.setRows(50);
        }
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(memberDayQueryVo.getRows());
        pageInfo.setPageNum(memberDayQueryVo.getPage());
        MemberDayQueryDto memberDayQueryDto = new MemberDayQueryDto();
        BeanUtils.copyProperties(memberDayQueryVo,memberDayQueryDto);
        memberDayQueryDto.setOrganSign(organSign);
        logger.info("vo getMinScoreRate:"+memberDayQueryVo.getMinScoreRate());
        logger.info("vo getMaxScoreRate:"+memberDayQueryVo.getMaxScoreRate());
        logger.info("vo getMinGrossMargin:"+memberDayQueryDto.getMinGrossMargin());
        logger.info("vo getMaxGrossMargin:"+memberDayQueryDto.getMaxGrossMargin());
//        memberDayQueryDto.setMinGrossMargin((memberDayQueryDto.getMinGrossMargin() != null && memberDayQueryDto.getMinGrossMargin().compareTo(new BigDecimal(0)) == 1) ? memberDayQueryDto.getMinGrossMargin():null );
//        memberDayQueryDto.setMaxGrossMargin((memberDayQueryDto.getMaxGrossMargin()!= null && memberDayQueryDto.getMaxGrossMargin().compareTo(new BigDecimal(0)) == 1)?memberDayQueryDto.getMaxGrossMargin():null);
        PageInfo resultPageInfo = productApi.getCanTakePartInMemberDayProduct(pageInfo,memberDayQueryDto);
        return new ResponseEntity<ResultVO>(new ResultVO(resultPageInfo),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getCanTakePartInMemberDayProductNoPage(HttpServletRequest request, @ApiParam(value = "请求字典类型" ,required=true )  @Valid @RequestBody MemberDayQueryVo memberDayQueryVo) {
        if(memberDayQueryVo == null){
            return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.ERROR,"参数不能为空"),HttpStatus.OK);
        }
        String organSign = request.getHeader("organSign");
        MemberDayQueryDto memberDayQueryDto = new MemberDayQueryDto();
        BeanUtils.copyProperties(memberDayQueryVo,memberDayQueryDto);
        memberDayQueryDto.setOrganSign(organSign);
        List<MemberDayMixResultDto> list = productApi.getCanTakePartInMemberDayProduct(memberDayQueryDto);
        return new ResponseEntity<ResultVO>(new ResultVO(list),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> updateSpecialProduct(HttpServletRequest request,@ApiParam(value = "请求信息对象" ,required=true )  @Valid @RequestBody SpecialProductUpdateVo specialProductUpdateVo) {
        String organSign = request.getHeader("organSign");
        if(StringUtils.isEmpty(specialProductUpdateVo.getProductPrefs())){
            return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.ERROR,"商品编号列表为空"),HttpStatus.OK);
        }
        if(specialProductUpdateVo.getScoreProductYn() == null){
            return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.ERROR,"商品是否积分字段不能为空"),HttpStatus.OK);
        }
        try{
            String[] prefArray = specialProductUpdateVo.getProductPrefs().split(",");
            List<String> prefs = Arrays.asList(prefArray);
            SpecialProductUpdateDto dto = new SpecialProductUpdateDto();
            dto.setOrganSign(organSign);
            dto.setProductPrefs(prefs);
            dto.setScoreProductYn(specialProductUpdateVo.getScoreProductYn());
            dto.setScoreRate(specialProductUpdateVo.getScoreRate());
            int result = productApi.batchUpdateSpecialProductInfo(dto);
            if(result<=0){
                return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.ERROR,"没发生异常，但是更新失败，需要排查原因"),HttpStatus.OK);
            }
        }catch (Exception e){
            logger.error("updateSpecialProduct occur error:organSign:"+organSign+",productPrefs:"
                    +specialProductUpdateVo.getProductPrefs()+",scoreProductYn:"+specialProductUpdateVo.getScoreProductYn()+",scoreRate:"+specialProductUpdateVo.getScoreRate()+",errorinfo:"+e);
            return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.ERROR,"没发生异常，但是更新失败，需要排查原因"),HttpStatus.OK);

        }
        //最后推送mq信息到pos
        refreshDataSuccToMQ(organSign);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess("success"),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getSingleProductInventoryInfoVo(HttpServletRequest request, @ApiParam(value = "请求信息对象" ,required=true )  @Valid @RequestBody SingleProductParamVo singleProductParamVo) {
        String organSign = request.getHeader("organSign");
        if(StringUtils.isEmpty(singleProductParamVo.getPref())){
            return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.ERROR,"商品内码编号必须填写"),HttpStatus.OK);
        }
        List<SingleProductInventoryInfoVo> results = new ArrayList<>();
        List<String> prefs = new ArrayList<>();
        prefs.add(singleProductParamVo.getPref());
        IRemoteBean<List<InventoryLotNumPromotionResult>> tempList = inventoryForPromotionApi.queryLotNumByProductsPref(organSign,prefs);
        if(tempList.isSuccess()){
            List<InventoryLotNumPromotionResult> inventorys = tempList.getContent();
            if(inventorys.size() > 0){
                for(InventoryLotNumPromotionResult invo:inventorys){
                    SingleProductInventoryInfoVo vo = new SingleProductInventoryInfoVo();
                    vo.setLotNumber(invo.getLotNumber());
                    vo.setStockNumber(invo.getStockNumber());
                    vo.setExpirationDate(DateUtil.parseDateToStr(invo.getExpirationDate(),DateUtil.DATE_FORMAT_YYYY_MM_DD));
                    vo.setProducedDate(DateUtil.parseDateToStr(invo.getProducedDate(),DateUtil.DATE_FORMAT_YYYY_MM_DD));
                    results.add(vo);
                }
            }
        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(results),HttpStatus.OK);
    }



    @Override
    public ResponseEntity<ResultVO> queryPriceLabelList(HttpServletRequest request) {
        String organSign = request.getHeader("organSign");
        //String organSign="23123";
        //查询未删除的
        Integer yn =1;
        List<PriceLabelVoDto> priceLabelDtos = productApi.queryPriceLabelList(organSign,yn);
        List<PriceLabelDto>  PriceLabelDtos= new ArrayList<>();
        if(priceLabelDtos!=null && priceLabelDtos.size()>0){
            for ( PriceLabelVoDto  priceLabelVoDto:priceLabelDtos){
                PriceLabelDto priceLabelDto = new PriceLabelDto();
                BeanUtils.copyProperties(priceLabelVoDto,priceLabelDto);
                String content = priceLabelDto.getContent();
                List<HashMap> maps = JSON.parseArray(content, HashMap.class);
                priceLabelDto.setContentlist(maps);
                PriceLabelDtos.add(priceLabelDto);
            }

        }
        //查询最后一次保存或修改的数据
        PriceLabelVoDto  priceLabelVoDto= productApi.getPriceLabelVoDto(organSign,yn);
        HashMap<String, Object> Map = new HashMap<>();
        Map.put("list",PriceLabelDtos);
        if(priceLabelVoDto!=null){
            Map.put("id",priceLabelVoDto.getId());

        }else{
            Map.put("id",null);
        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(Map),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> queryPriceLabelImportId(HttpServletRequest request,@RequestBody PriceLabelDto priceLabel) {
        List<String> listDtos = productApi.queryPriceLabelProductImportList(priceLabel.getId());
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(listDtos),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> queryPriceLabelListHtml(HttpServletRequest request,@ApiParam(value = "价签模板详情信息对象", required = true) @Valid @RequestBody PriceLabelDto priceLabel) {
        String organSign = request.getHeader("organSign");
        //String organSign="23123";
        //查询未删除的
        Integer yn =1;
        List<PriceLabelVoDto> priceLabelDtos = productApi.queryPriceLabelListForHtml(organSign,yn,priceLabel.getType());
        List<PriceLabelDto>  PriceLabelDtos= new ArrayList<>();
        if(priceLabelDtos!=null && priceLabelDtos.size()>0){
            for ( PriceLabelVoDto  priceLabelVoDto:priceLabelDtos){
                PriceLabelDto priceLabelDto = new PriceLabelDto();
                BeanUtils.copyProperties(priceLabelVoDto,priceLabelDto);
                String content = priceLabelDto.getContent();
                List<HashMap> maps = new ArrayList<>();
                priceLabelDto.setContentlist(maps);
                PriceLabelDtos.add(priceLabelDto);
            }

        }
        //查询最后一次保存或修改的数据
        PriceLabelVoDto  priceLabelVoDto= productApi.getPriceLabelVoDtoForHtml(organSign,yn,priceLabel.getType());
        HashMap<String, Object> Map = new HashMap<>();
        Map.put("list",PriceLabelDtos);
        if(priceLabelVoDto!=null){
            Map.put("id",priceLabelVoDto.getId());

        }else{
            Map.put("id",null);
        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(Map),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> addOrUpdatePriceLabel(HttpServletRequest request, @ApiParam(value = "价签模板详情信息对象" ,required=true )  @Valid @RequestBody PriceLabelDto priceLabel) {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        if(StringUtils.isEmpty(priceLabel)){
            logger.error("==============参数不合法");
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, null),HttpStatus.OK);
        }
        Integer systemTempletYn = priceLabel.getSystemTempletYn();
        Integer printNumber = priceLabel.getPrintNumber();
        if(systemTempletYn==1){
            if(printNumber!=21){
                logger.error("==============系统模板打印数量不合法");
                return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, null),HttpStatus.OK);
            }
        }
        if(systemTempletYn!=1){
            if(printNumber<1 ||printNumber>24){
                logger.error("==============自定义模板打印数量不合法");
                return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, null),HttpStatus.OK);
            }
        }
        priceLabel.setOrganSign(organSign);
        List<HashMap> contentlist = priceLabel.getContentlist();
        if(contentlist!=null&&contentlist.size()>0){
            String content = JSONArray.toJSONString(contentlist);
            priceLabel.setContent(content);
        }
        PriceLabelVoDto priceLabelVoDto = new PriceLabelVoDto();
        BeanUtils.copyProperties(priceLabel,priceLabelVoDto);

        int status= productApi.addOrUpdatePriceLabel(priceLabelVoDto,employee);
        if (status > 0) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.SUCCESS, "save success",status),HttpStatus.OK);
        } else {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "保存或更新失败",status),HttpStatus.OK);
        }

    }

    @Override
    public ResponseEntity<ResultVO> deletePriceLabelForId(HttpServletRequest request, @ApiParam(value = "价签模板详情信息对象" ,required=true )  @Valid @RequestBody PriceLabelDto priceLabel) {
        if (StringUtils.isEmpty(priceLabel.getId())){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "noParam"),HttpStatus.OK);
        }
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        boolean flag=  productApi.deletePriceLabelForId(priceLabel.getId(),organSign,employee);
        if (flag){
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(null),HttpStatus.OK);
        }else {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, null),HttpStatus.OK);
        }

    }

    @Override
    public ResponseEntity<ResultVO> getProductListByPref(HttpServletRequest request,  @ApiParam(value = "价签模板详情信息对象" ,required=true )  @Valid @RequestBody PriceLabelDto priceLabel ) {
        List<String> prefList = priceLabel.getPrefList();
        if (prefList==null||prefList.size()==0){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "noParam"),HttpStatus.OK);
        }
        String organSign = request.getHeader("organSign");
        Byte bizModel = null;
        //如果是连锁门店，需要获取总部的机构号
        String modelJson = request.getHeader("commonRequestModel");
        String organSignForPrice = "";
        if(!StringUtils.isEmpty(modelJson)) {
            CommonRequestModel model = JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            bizModel = model.getBizModel();
            if (DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())) {
                organSignForPrice = organSign;
                organSign = model.getHeadquartersOrganSign();
            }
        }
        List<ProductPriceLabelDto> pplds = productApi.getProductListByPrefChain(prefList,organSign,bizModel,organSignForPrice);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(pplds),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> updateProductPrintStatusByPref(HttpServletRequest request,@ApiParam(value = "价签模板详情信息对象" ,required=true )  @Valid @RequestBody PriceLabelDto priceLabel) {
        List<String> prefList = priceLabel.getPrefList();
        if (prefList==null||prefList.size()==0){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "noParam"),HttpStatus.OK);
        }
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        //如果是连锁门店，需要获取总部的机构号
        String modelJson = request.getHeader("commonRequestModel");
        if(!StringUtils.isEmpty(modelJson)) {
            CommonRequestModel model = JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            if (DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())) {
                organSign = model.getHeadquartersOrganSign();
            }
        }

        // 加锁,避免频繁更新造成死锁
        int status = productApi.updateProductPrintStatusByPref(prefList,organSign,employee);
      /*  String key = "updatePrintStatus:"+organSign;
        boolean allowUpdatePrintStatus = jedisUtils.setNx(key, 1 + "", 5 );
        while (!allowUpdatePrintStatus){
            allowUpdatePrintStatus = jedisUtils.setNx(key, 1 + "", 5 );
        }

        try {
            status = productApi.updateProductPrintStatusByPref(prefList,organSign,employee);
        } catch (Exception exception) {
            logger.info("updateProductPrintStatusByPref fail,exception:{}",exception);
        }finally {
            jedisUtils.del(key);
        }*/
        if (status > 0) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.SUCCESS, "更新成功"),HttpStatus.OK);
        } else {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "更新失败"),HttpStatus.OK);
        }
    }

    @Override
    public ResponseEntity<ResultVO> getPriceLabelByPref(HttpServletRequest request,@ApiParam(value = "价签模板详情信息对象" ,required=true )  @Valid @RequestBody PriceLabelDto priceLabel) {
        String organSign = request.getHeader("organSign");
        PriceLabelVoDto priceLabelParam = new PriceLabelVoDto();
        priceLabelParam.setTempletPref(priceLabel.getTempletPref());
        PriceLabelVoDto vo = productApi.getPriceLabelByPref(priceLabelParam);
        if(vo == null){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "模板不存在，请校验模板编号是否正确！"),HttpStatus.OK);
        }
        return new ResponseEntity<ResultVO>(new ResultVO(vo),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> configPriceLabel(HttpServletRequest request,@ApiParam(value = "价签模板详情信息对象" ,required=true )  @Valid @RequestBody PriceLabelDto priceLabel) {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        PriceLabelVoDto priceLabelParam = new PriceLabelVoDto();
        priceLabelParam.setTempletPref(priceLabel.getTempletPref());
        priceLabelParam.setOrganSign(organSign);//绑定当前门店机构号
        ResultVO<Integer> vo = productApi.configPriceLabel(priceLabelParam,employee);
        if(vo != null && vo.getCode() != 0){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "模板不存在，请校验模板编号是否正确！"),HttpStatus.OK);
        }
        return new ResponseEntity<ResultVO>(vo,HttpStatus.OK);
    }


    @Override
    public ResponseEntity<ResultVO> getAdjustPriceListByPref(HttpServletRequest request,@ApiParam(value = "商品售价调整信息实体" ,required=true )  @Valid @RequestBody ProPriceAdjustDto proPriceAdjustDto) {
        String organSign = request.getHeader("organSign");
        //String organSign="ZHL0f2ce7b9";
        if(StringUtils.isEmpty(proPriceAdjustDto.getProductPref())){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR,"商品编号不能为空"),HttpStatus.OK);
        }
        if (proPriceAdjustDto.getPage() == null) {
            proPriceAdjustDto.setPage(1);
        }
        if (proPriceAdjustDto.getRows() == null) {
            proPriceAdjustDto.setRows(50);
        }
        proPriceAdjustDto.setOrganSign(organSign);
        ResultVO result =adjustPriceApi.getAdjustPriceListByPref(proPriceAdjustDto );

        return new ResponseEntity<ResultVO>(result,HttpStatus.OK);
    }

    private void refreshDataSuccToMQ(String organSign) {
        JSONObject json = new JSONObject();
        String[] tables = {"saas_product_baseinfo"};
        json.put("code", "sync");
        json.put("tables", tables);

        messagePushApi.sendMsgByOrganSign(organSign, json.toJSONString());
    }



    private JSONArray listToJsonArray(String key, List<SystemDictDto> dtos) {
        JSONArray array = new JSONArray();
        dtos.forEach(item -> {
            JSONObject json = new JSONObject();
            json.put("data", item.getId());
            json.put("value", item.getName());
            array.add(json);
        });
        return array;
    }

    private Map<String, List<SystemDictDto>> getAddOrUpdateDict(String organSign) {
        List<Integer> ids = Arrays.asList(DictConstant.unitBussinessId, DictConstant.agentBussinessId, DictConstant.commodtyTypeBussinessId, DictConstant.STORE_CONDITION_BUSINESS_ID,
                DictConstant.ABC_BUSSINESS_ID, DictConstant.prescriptionBussinessId, DictConstant.scopeOfOperation, DictConstant.maintenanceType, DictConstant.functionBussinessId);
        List<SystemDictDto> dictDtos = productDictionaryApi.findSystemDictDtoBybussinessIds(ids,organSign);
        // 查询字典表
        Map<String, List<SystemDictDto>> map = new HashMap<>();
        // 单位
        List<SystemDictDto> unitDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.unitBussinessId)).collect(Collectors.toList());
        // 剂型
        List<SystemDictDto> agentDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.agentBussinessId)).collect(Collectors.toList());
        // 商品类别
        List<SystemDictDto> proTypeDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.commodtyTypeBussinessId)).collect(Collectors.toList());
        // 存储条件
        List<SystemDictDto> storeDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.STORE_CONDITION_BUSINESS_ID)).collect(Collectors.toList());
        // ABC分类
        List<SystemDictDto> abcDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.ABC_BUSSINESS_ID)).collect(Collectors.toList());
        // 处方分类
        List<SystemDictDto> cfDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.prescriptionBussinessId)).collect(Collectors.toList());
        // 经营范围
        List<SystemDictDto> scopeDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.scopeOfOperation)).collect(Collectors.toList());
        // 养护类型
        List<SystemDictDto> yhDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.maintenanceType)).collect(Collectors.toList());
        // 商品功能分类
        List<SystemDictDto> ptypeDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.functionBussinessId)).collect(Collectors.toList());

        map.put("unitDict", unitDict);
        map.put("agentDict", agentDict);
        map.put("proTypeDict", proTypeDict);
        map.put("storeDict", storeDict);
        map.put("abcDict", abcDict);
        map.put("cfDict", cfDict);
        map.put("scopeDict", scopeDict);
        map.put("yhDict", yhDict);
        map.put("ptypeDict", ptypeDict);

        return map;
    }

    /**
     * 功能描述: 根据商品名称模糊查询商品列表
     * @Param: [request, productName]
     * @Return: org.springframework.http.ResponseEntity<com.xyy.saas.common.util.ResultVO>
     */
    @Override
    public ResponseEntity<ResultVO> getProductsByProductName(HttpServletRequest request,@ApiParam(value = "请求信息对象" ,required=true ) @Valid @RequestBody ProductCommonQueryVo product){
        if(null == product){
            return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.ERROR,"查询参数实体不能为空"),HttpStatus.OK);
        }
        String organSign = product.getOrganSign();
        if(StringUtils.isEmpty(organSign)){
            return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.ERROR,"机构号不能为空"),HttpStatus.OK);
        }
        String productName = product.getProductName();
        if(StringUtils.isEmpty(productName)){
            return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.ERROR,"商品名称不能为空"),HttpStatus.OK);
        }
        if ( null == product.getPage() ) {
            product.setPage(1);
        }
        if ( null == product.getRows() ) {
            product.setRows(50);
        }
        Integer queryStore = product.getQueryStore();
        if(null == queryStore){
            //默认不查询库存
            queryStore = 0;
        }
        return  new ResponseEntity<ResultVO>(ResultVO.createSuccess(productApi.getProductsByProductName(product,queryStore)),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> updateProductPharmacyPrefRedisKey(HttpServletRequest request,@ApiParam(value = "外码更新后的value" ,required=true )  @Valid @RequestBody UpdateRedisVo updateRedisVo) {
        String organSign = updateRedisVo.getOrganSign();
        String keyValue = updateRedisVo.getKeyValue();
        int result = 0;
        result = productToolApi.updateProductPharmacyPrefRedisValue(organSign,keyValue);
        if(result == 0){
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(null),HttpStatus.OK);
        }else{
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(null),HttpStatus.OK);
        }
    }

//    @Override
//    public ResponseEntity<ResultVO> rereshProductChongfu(HttpServletRequest request, @Valid @RequestBody ProductRefreshVo refreshVo) {
//        if(refreshVo == null || StringUtils.isEmpty(refreshVo.getOrganSigns())){
//            return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.ERROR,"参数列表不能为空"),HttpStatus.OK);
//        }
//        String[] organSigns = refreshVo.getOrganSigns().split(",");
//        if(organSigns.length > 0){
//            for(String organSign:organSigns){
//                //重复处理单个机构下的重复商品数据,调用dubbo方法
//                List<ProductDto> list = productApi.getRepeatProductsByOrganSign(organSign,refreshVo.getStartTime(),refreshVo.getEndTime());
//                if(refreshVo.getIsExcute() == 1){
//                    for(ProductDto dto : list){
//                        this.productApi.resertProductIsUsed(dto.getId(), "0", organSign,0);
//                    }
//                }
//            }
//        }
//        return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.SUCCESS,"执行成功"),HttpStatus.OK);
//    }


    @Reference(version = "0.0.1")
    private TjOrganSignApi tjOrganSignApi;

    @Override
    public ResponseEntity<ResultVO> firstCheckToUser(HttpServletRequest request, @RequestBody TjCheckQueryVo queryVo) {
        logger.info("天津过检日志调试==================");
        Integer count = tjOrganSignApi.getCountByOrganSign(queryVo.getOrganSign());
        TjCheckResultVo tjCheckResultVo = new TjCheckResultVo();
        if (count != null && count > 0) {
            tjCheckResultVo.setIsOpen((byte) 1);
        }else{
            tjCheckResultVo.setIsOpen((byte) 0);
        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(tjCheckResultVo), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> standardLibraryMatch(@RequestBody String organSigns) {
        logger.info("SaaS商品匹配标准库ID开始==================");
        long time = System.currentTimeMillis();
        productApi.standardLibraryMatch(organSigns);
        logger.info("SaaS商品匹配标准库ID结束，总耗时{}==================",(System.currentTimeMillis())-time);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess("执行成功"), HttpStatus.OK);
    }

    /***
     * 功能描述: <br> 把es中标准库的信息放入es中
     * 〈〉0删除redis中数据  1 初始化redis中数据
     * @Param: []
     * @Return: org.springframework.http.ResponseEntity<com.xyy.saas.common.util.ResultVO>
     */
    @Override
    public ResponseEntity<ResultVO> standardLibraryInit(@RequestBody String isDelete) {
        logger.info("初始化匹配标准库ID到redis中开始==================");
        long time = System.currentTimeMillis();
        if(StringUtils.isEmpty(isDelete)){
            isDelete = "1";
        }
        productApi.standardLibraryInit(isDelete);
        logger.info("初始化匹配标准库ID到redis中结束，耗时{}==================",(System.currentTimeMillis())-time);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess("执行成功"), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getproductType(HttpServletRequest request) {
        String organSign = request.getHeader("organSign");
        ResultVO<QueryDrugstoreDto> resultVO = drugstoreApi.queryDrugstoreByOrganSign(organSign);
        if (resultVO.getResult() != null && DrugstoreBizModelEnum.CHAIN_STORE.toEquals(resultVO.getResult().getBizModel())) {
            List<SystemDictDto> dictDtos = saasCustomTypeApi.getAllOfNewTransToOld(organSign, CustomTypeBusinessTypeEnum.PRODUCT_TYPE.getValue());
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(dictDtos), HttpStatus.OK);
        }

        List<Integer> ids = new ArrayList<>();
        ids.add(DictConstant.PRODUCT_FIRST_BUSSINESS_ID);
        ids.add(DictConstant.PRODUCT_SECOND_BUSSINESS_ID);
        ids.add(DictConstant.PRODUCT_THIRD_BUSSINESS_ID);
        List<SystemDictDto> dictDtos = productDictionaryApi.findSystemDictDtoBybussinessIds(ids,organSign,1,1);
        List<SystemDictDto> firstProductTypeList = new ArrayList<>();
        List<SystemDictDto> secondProductTypeList = new ArrayList<>();
        List<SystemDictDto> thirdProductTypeList = new ArrayList<>();

        for(SystemDictDto sdd:dictDtos){
            if(sdd.getBussinessId().equals(DictConstant.PRODUCT_FIRST_BUSSINESS_ID) ){
                firstProductTypeList.add(sdd);
            }
            if(sdd.getBussinessId().equals(DictConstant.PRODUCT_SECOND_BUSSINESS_ID) ){
                secondProductTypeList.add(sdd);
            }
            if(sdd.getBussinessId().equals(DictConstant.PRODUCT_THIRD_BUSSINESS_ID)){
                thirdProductTypeList.add(sdd);
            }

        }
        for(SystemDictDto  second:secondProductTypeList){
            for(SystemDictDto  third:thirdProductTypeList){
                if(second.getId().equals(third.getParentId())){
                    second.getChildren().add(third);
                }
            }
        }
        for(SystemDictDto  first:firstProductTypeList){
            for(SystemDictDto  second:secondProductTypeList){
                if(first.getId().equals(second.getParentId())){
                    first.getChildren().add(second);
                }

            }
        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(firstProductTypeList), HttpStatus.OK);
    }

    /**
     * 根据条件查询商品信息
     * @param request
     * @param productQueryVo
     * @return
     */
    @Override
    public ResponseEntity<ResultVO> getProductByPref(HttpServletRequest request, @ApiParam(value = "商品详情信息对象", required = true) @Valid @RequestBody ProductQueryVo productQueryVo){
        String organSign = request.getHeader("organSign");
        String modelJson = request.getHeader("commonRequestModel");
        if(StringUtils.isEmpty(productQueryVo.getPref())){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR,"pref不能为空"),HttpStatus.OK);
        }
        if (!StringUtils.isEmpty(modelJson)) {
            CommonRequestModel model = JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            if (DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())) {
                organSign = model.getHeadquartersOrganSign();
            }
        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(productApi.getProductByPref(productQueryVo.getPref(),organSign)), HttpStatus.OK);
    }
    /**
     * @desc: 查询商品资质预警信息
     * @param request
     * @param product
     * @return: org.springframework.http.ResponseEntity<com.xyy.saas.common.util.ResultVO>
     * @author: hbt
     */
    @Override
    public ResponseEntity<ResultVO> warningInfo(HttpServletRequest request, @RequestBody ProductDto product) {
        return new ResponseEntity<ResultVO>(productApi.getWarningProductInfo(product), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getMustFieldByOrganSign(HttpServletRequest request,HttpServletResponse response) {
        String organSign = request.getHeader("organSign");
        response.setHeader("Access-Control-Allow-Origin", "*");
        String result = "";
        logger.info("获取必填字段:机构号:{}", organSign);
        if (StringUtils.isEmpty(organSign)) {
            return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.ERROR,"机构号必填"),HttpStatus.OK);
        }
        SaaSDrugstoreDto drugstoreDto = drugstoreApi.getDrugstoreByOrganSign(organSign);
        logger.info("获取必填字段:机构信息:{}", JSON.toJSONString(drugstoreDto));
        if (drugstoreDto == null) {
            return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.ERROR,"根据机构号获取机构信息为空"),HttpStatus.OK);
        }
        SystemConfigDto systemConfigDto = sysConfigApi.querySystemConfig(organSign);
        if(systemConfigDto.getSaleStorageYn() == 0){
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(result), HttpStatus.OK);
        }
        String[] areas = drugstoreDto.getAreaCode().split(",");
        String province = "";
        String city = "";
        String area = "";
        switch (areas.length) {
            case 1:
                province = areas[0];
                break;
            case 2:
                province = areas[0];
                city = areas[1];
                break;
            case 3:
                province = areas[0];
                city = areas[1];
                area = areas[2];
                break;
        }
        JSONObject map = JSONObject.parseObject(fengchengAreacodes);
        logger.info("getMustFieldByOrganSign map:{},areas:{}", map, areas);
        if (map.containsKey(area)) {
            result = map.getString(area);
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(result), HttpStatus.OK);
        } else if (map.containsKey(city)) {
            result = map.getString(city);
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(result), HttpStatus.OK);
        } else if (map.containsKey(province)) {
            result = map.getString(province);
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(result), HttpStatus.OK);
        } else {
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(result), HttpStatus.OK);
        }
    }

    public ResultVO<Boolean> productParamLengthCheck(ProductDto product){
        if (product.getCommonName()!=null&&product.getCommonName().length()>100){
            return new ResultVO(ResultCodeEnum.ERROR, "通用名称长度不可超过100个字符", false);
        }
        if (product.getProductName()!=null&&product.getProductName().length()>100){
            return new ResultVO(ResultCodeEnum.ERROR, "商品名称长度不可超过100个字符", false);
        }
        if (product.getMnemonicCode()!=null&&product.getMnemonicCode().length()>201){
            return new ResultVO(ResultCodeEnum.ERROR, "助记码长度不可超过201个字符", false);
        }
        if (product.getAttributeSpecification()!=null&&product.getAttributeSpecification().length()>100){
            return new ResultVO(ResultCodeEnum.ERROR, "规格/型号不可超过100个字符", false);
        }
        if (product.getUsageAndDosage()!=null&&product.getUsageAndDosage().length()>15000){
            return new ResultVO(ResultCodeEnum.ERROR, "用法用量不可超过15000个字符", false);
        }
        if (product.getPref()!=null&&product.getPref().length()>32){
            return new ResultVO(ResultCodeEnum.ERROR, "商品编号不可超过32个字符", false);
        }
        // 新增校验  修改不校验
        if (product.getId() == null && product.getPharmacyPref()!=null&&product.getPharmacyPref().length()>20){
            return new ResultVO(ResultCodeEnum.ERROR, "商品编号不可超过20个字符", false);
        }
        if (product.getManufacturer()!=null&&product.getManufacturer().length()>100){
            return new ResultVO(ResultCodeEnum.ERROR, "生产厂家不可超过100个字符", false);
        }
        if (product.getProducingArea()!=null&&product.getProducingArea().length()>100){
            return new ResultVO(ResultCodeEnum.ERROR, "产地不可超过100个字符", false);
        }
        if (product.getBarCode()!=null&&product.getBarCode().length()>100){
            return new ResultVO(ResultCodeEnum.ERROR, "条形码不可超过100个字符", false);
        }
        if (product.getApprovalNumber()!=null&&product.getApprovalNumber().length()>100){
            return new ResultVO(ResultCodeEnum.ERROR, "批准文号/备案/注册证号不可超过100个字符", false);
        }
        if (product.getDrugPermissionPerson()!=null&&product.getDrugPermissionPerson().length()>64){
            return new ResultVO(ResultCodeEnum.ERROR, "上市许可持有人不可超过64字符", false);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(product.getDrugIdentCode())){
            if (product.getDrugIdentCode().length() != 7){
                return new ResultVO(ResultCodeEnum.ERROR, "录入追溯码前7位或扫描追溯码", false);
            }
            try {
                Integer.valueOf(product.getDrugIdentCode());
            } catch (Exception e) {
                return new ResultVO(ResultCodeEnum.ERROR, "格式错误，仅支持数字", false);
            }
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(product.getManufacturerUscc())){
            String producerSocialCreditCode = product.getManufacturerUscc();
            if (producerSocialCreditCode.length() > 256){
                return new ResultVO(ResultCodeEnum.ERROR, "生产厂商社会信用代码不可超过256字符", false);
            }
            // 正则表达式，匹配数字或英文字母
            if (!producerSocialCreditCode.matches("^[a-zA-Z0-9]+$")){
                return new ResultVO(ResultCodeEnum.ERROR, "生产厂商社会信用代码只能为数字或英文字母", false);
            }
        }
        if (product.getMinPriceNumber()!=null&&product.getMinPriceNumber().length()>20){
            return new ResultVO(ResultCodeEnum.ERROR, "最小包装数量不可超过20字符", false);
        }
        if (product.getMinPriceUnit()!=null&&product.getMinPriceUnit().length()>20){
            return new ResultVO(ResultCodeEnum.ERROR, "最小计价单位不可超过20字符", false);
        }
        return new ResultVO(ResultCodeEnum.SUCCESS, "", true);
    }


    @Override
    public ResponseEntity<ResultVO> banchMatchStandLibrary(HttpServletRequest request, @Valid @RequestBody MatchProductVoDto matchProductVoDto) {
        logger.info("banchMatchStandLibrary  productbaseinfo 方法开始执行  matchProductVoDto:{}", JSONUtils.obj2JSON(matchProductVoDto));
        String organSign = request.getHeader("organSign");
        matchProductVoDto.setOrganSign(organSign);
        String employee = request.getHeader("employeeId");
        String identity = employeeApi.queryEmployeeById(Integer.valueOf(employee)).getResult().getIdentity()+"";
        logger.info("organSign:" + organSign + ",employee:" + employee + ",identity:" + identity);
        Byte isProductHidden = Byte.valueOf(identity);
        if(isProductHidden!=null){
            matchProductVoDto.setIsHidden(isProductHidden);
        }
        boolean flag = matchStandardLibraryApi.banchMatchStandLibrary(matchProductVoDto);
//        boolean flag = standardLibaryApi.banchMatchStandLibrary(matchProductVoDto);
        if(!flag){
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(false), HttpStatus.OK);
        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(flag), HttpStatus.OK);

    }

    @Override
    public ResponseEntity<ResultVO> getAbnormalProList(HttpServletRequest request, @Valid @RequestBody ProductVoDto productVoDto) {
        try{
            String organSign = request.getHeader("organSign");
            String employee = request.getHeader("employeeId");
            String identity = employeeApi.queryEmployeeById(Integer.valueOf(employee)).getResult().getIdentity()+"";
            logger.info(",employee:" + employee + ",identity:" + identity);
            Byte isProductHidden = Byte.valueOf(identity);
            if(isProductHidden!=null){
                productVoDto.setIsHidden(Byte.valueOf(isProductHidden));
            }
            PageInfo pageInfo = new PageInfo();
            pageInfo.setPageNum(productVoDto.getPage()==null?1:productVoDto.getPage());
            pageInfo.setPageSize(productVoDto.getRows()==null?10:productVoDto.getRows());
            productVoDto.setOrganSign(organSign);
            PageInfo<ProductVoDto> proInfo = productApi.getAbnormalProList(pageInfo,productVoDto);
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(proInfo), HttpStatus.OK);
        }catch (Exception e){
            e.printStackTrace();
            logger.error(e.getMessage()+"异常商品信息列表查询失败");
            return new ResponseEntity<ResultVO>(new ResultVO<PageInfo>(ResultCodeEnum.ERROR,null,null), HttpStatus.OK);
        }
    }

    @Override
    public ResponseEntity<ResultVO> libraryQuery(HttpServletRequest request, @Valid @RequestBody BinnaryQueryDto vo) {
       logger.info("ProductInfoApiController#libraryQuery rows:{}, page:{}", vo.getRows(), vo.getPage());
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        Integer employeeId = Integer.parseInt(employee);
        String key = UserOperationConstans.USER_OPERATION_KEY_PREF + UserOperationConstans.SPLIT + String.valueOf(employeeId) + UserOperationConstans.SPLIT + UserOperationConstans.LIBRARY_QUERY;
        boolean fixResult = jedisUtils.setNx(key, 1 + "", 3 * 60);
        logger.info("libraryQuery redis加分布式锁，key:{},结果:{}", key, fixResult);
        if (!fixResult) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "查询中，请稍后再试"), HttpStatus.OK);
        }
        if ( vo.getPage() == null) {
            vo.setPage(0);
        }

        if (vo.getRows() == null) {
            vo.setRows(10);
        }
        ResultVO result = null;
        try{
            result = standardLibaryApi.findStdProsWithLocalProsPage(vo, organSign);
        }catch (Exception e){
            e.printStackTrace();
            result = new ResultVO(ResultCodeEnum.ERROR, "查询出错，请稍后再试");
        }finally {
            boolean resultEnd = jedisUtils.delVal(key);
            if (!resultEnd) {
                logger.info("libraryQuery 释放Redis锁失败，key:{}", key);
            } else {
                logger.info("libraryQuery 释放Redis锁成功，key:{}", key);
            }
        }
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> deleteMatchedSuccess(HttpServletRequest request, @Valid @RequestBody ProductDto productDto) {
        logger.info("deleteMatchedSuccessList  productbaseinfo 方法开始执行");
        String organSign = request.getHeader("organSign");
        boolean result = true;
        result = matchStandardLibraryApi.deleteMatchedSuccess(productDto.getId(), organSign);
//        result = standardLibaryApi.deleteMatchedSuccess(id,organSign);
        if(!result){
            return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.ERROR,"删除失败"), HttpStatus.OK);
        }

        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(result), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> saveMatchedSuccess(HttpServletRequest request) {
        logger.info("saveMatchedSuccess  productbaseinfo 方法开始执行");
        boolean result = true;
        String organSign = request.getHeader("organSign");
        result = matchStandardLibraryApi.saveMatchedSuccess(organSign);
//        result = standardLibaryApi.saveMatchedSuccess(organSign);
        if(!result){
            return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.ERROR,"保存失败"), HttpStatus.OK);
        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(result), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> queryMatchedSuccessPage(HttpServletRequest request, @RequestBody PageDto pageDto) {
        String organSign = request.getHeader("organSign");
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(pageDto.getPage()==null?1:pageDto.getPage());
        pageInfo.setPageSize(pageDto.getRows()==null?10:pageDto.getRows());
        logger.info("queryMatchedSuccessPage  productbaseinfo 方法开始执行 pageDto:{}, organSign:{}", JSONUtils.obj2JSON(pageDto), organSign);
        PageInfo<MatchedProductDto> productDtoPageInfo = matchStandardLibraryApi.queryMatchedSuccessPage(organSign,pageInfo);
//        PageInfo<MatchedSLProductDto> productDtoPageInfo = standardLibaryApi.queryMatchedSuccessPage(organSign,pageInfo);
        if(productDtoPageInfo==null){
            return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.ERROR,""), HttpStatus.OK);
        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(productDtoPageInfo), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> queryCustomField(HttpServletRequest request){
        String organSign = request.getHeader("organSign");
        logger.info("ProductInfoApiController#queryCustomField begin organSign:{}", organSign);

        logger.info("ProductInfoApiController#queryCustomField end organSign:{}", organSign );
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> uniqueProduct(HttpServletRequest request, @RequestBody ProductDto productDto){
        logger.info("uniqueProduct productbaseinfo 方法开始执行,organSign:{},barCode:{},commonName:{},manufacturer:{},attributeSpecification:{}",productDto.getOrganSign(),productDto.getBarCode(),productDto.getCommonName(),productDto.getManufacturer(),productDto.getAttributeSpecification());
        String organSign = request.getHeader("organSign");
        productDto.setOrganSign(organSign);
        ResultVO resultVO = productApi.uniqueProduct(productDto);
        logger.info("uniqueProduct productbaseinfo 方法执行结束,organSign:{},barCode:{},commonName:{},manufacturer:{},attributeSpecification:{}",productDto.getOrganSign(), productDto.getBarCode(), productDto.getCommonName(), productDto.getManufacturer(), productDto.getAttributeSpecification());
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(resultVO), HttpStatus.OK);
    }

    /**
     * 查验商品是否强制追溯
     *
     * @param request
     * @param prefList
     * @return
     */
    @Override
    public ResponseEntity<ResultVO> checkTraceProduct(HttpServletRequest request, List<String> prefList){
        String organSign = request.getHeader("organSign");
        ResultVO<Map<String, Integer>> resultVO = productApi.checkTraceProduct(organSign, prefList);

        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(resultVO), HttpStatus.OK);
    }

    public ResponseEntity<ResultVO> getMustFieldByOrganSignLinyi(HttpServletRequest request,HttpServletResponse response) {
        String organSign = request.getHeader("organSign");
        response.setHeader("Access-Control-Allow-Origin", "*");
        String result = "";
        logger.info("获取必填字段:机构号:{}", organSign);
        if (StringUtils.isEmpty(organSign)) {
            return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.ERROR,"机构号必填"),HttpStatus.OK);
        }
        SaaSDrugstoreDto drugstoreDto = drugstoreApi.getDrugstoreByOrganSign(organSign);
        logger.info("获取必填字段:机构信息:{}", JSON.toJSONString(drugstoreDto));
        if (drugstoreDto == null) {
            return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.ERROR,"根据机构号获取机构信息为空"),HttpStatus.OK);
        }
        SystemConfigDto systemConfigDto = sysConfigApi.querySystemConfig(organSign);
        if(systemConfigDto.getSaleStorageYn() == 0){
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(result), HttpStatus.OK);
        }
        String[] areas = drugstoreDto.getAreaCode().split(",");
        String province = "";
        String city = "";
        String area = "";
        switch (areas.length) {
            case 1:
                province = areas[0];
                break;
            case 2:
                province = areas[0];
                city = areas[1];
                break;
            case 3:
                province = areas[0];
                city = areas[1];
                area = areas[2];
                break;
        }
        JSONObject map = JSONObject.parseObject(linyiAreacodes);
        if (map.containsKey(city)) {
            result = map.getString(city);
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(result), HttpStatus.OK);
        } else  {
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(result), HttpStatus.OK);
        }
    }

    @Override
    public ResponseEntity<ResultVO> convertYBMInfo(HttpServletRequest request,@RequestBody String data){
        String organSign = request.getHeader("organSign");
        if (StringUtils.isEmpty(data)){
            return new ResponseEntity<ResultVO>(ResultVO.createError("参数错误"), HttpStatus.OK);
        }

        JSONObject jo = JSON.parseObject(data);
        //判断skuid是否为数字类型，如果不是数字类型或者为空则判断标准库id是否有值且为long类型，如果都没有则返回空
        if(jo.get("skuId") != null && NumberUtils.isDigits(jo.get("skuId").toString())){
            Long skuId = Long.valueOf(jo.get("skuId").toString());
            ProductDto productDto = productApi.convertYBMInfo(skuId,organSign);
            if(productDto == null ){
                return new ResponseEntity<ResultVO>(ResultVO.createError("获取订单商品信息失败,请稍后重试!"), HttpStatus.OK);
            }
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(productDto), HttpStatus.OK);
        }else if (jo.get("standardLibraryId") != null && NumberUtils.isDigits(jo.get("standardLibraryId").toString())){
            Long standardLibraryId = Long.valueOf(jo.get("standardLibraryId").toString());
            StandardLibaryProductVoDto productVo = matchStandardLibraryApi.findStandLibrayById(standardLibraryId);
            if(productVo != null){
                ProductDto productDto = new ProductDto();
                BeanUtils.copyProperties(productVo,productDto);
                return new ResponseEntity<ResultVO>(ResultVO.createSuccess(productDto), HttpStatus.OK);
            }else{
                return new ResponseEntity<ResultVO>(ResultVO.createSuccess(), HttpStatus.OK);
            }

        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> checkSNOrgan(HttpServletRequest request) {
        String organSign = request.getHeader("organSign");
        String modelJson = request.getHeader("commonRequestModel");
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                organSign = model.getHeadquartersOrganSign();
            }
        }
        ResultVO<Boolean> medic = snProductApi.isZhiluMedic(organSign);
        return new ResponseEntity<ResultVO>(medic, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> noticeMiddle(HttpServletRequest request, @RequestBody String data) {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        if(StringUtils.isEmpty(organSign)) {
            return new ResponseEntity<ResultVO>(ResultVO.createError("参数错误"), HttpStatus.OK);
        }
        JSONObject jo = JSON.parseObject(data);
        String productPref = (String)jo.get("productPref");
        if(StringUtils.isEmpty(productPref)) {
            return new ResponseEntity<ResultVO>(ResultVO.createError("商品参数错误"), HttpStatus.OK);
        }
        String key = organSign + ":" + employee + ":" + productPref;
        logger.info("ProductInfoApiController.noticeMiddle organSign:{} employee:{} productPref:{} key:{}", organSign, employee, productPref, key);
        if(jedisUtils.exists(key)) {
            return new ResponseEntity<ResultVO>(ResultVO.createError("请耐心等待3-5分钟查看物开平台是否同步成功"), HttpStatus.OK);
        }
        // 三分钟 防重复点击
        jedisUtils.setKeyAndExpireTime(key, "1", 3*60*1000);

        NoticeMiddleParam param = new NoticeMiddleParam();
        param.setOrganSign(organSign);
        param.setProductPref(productPref);
        productApi.noticeMiddle(param);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> syncSingleToMiddle(HttpServletRequest request, @RequestBody String data) {

        JSONObject jo = JSON.parseObject(data);
        String productPref = (String)jo.get("productPref");
        String organSign = (String)jo.get("organSign");
        String orgCode = (String)jo.get("orgCode");
        if(StringUtils.isEmpty(organSign)) {
            return new ResponseEntity<ResultVO>(ResultVO.createError("总部机构号为空"), HttpStatus.OK);
        }
        if(StringUtils.isEmpty(productPref)) {
            return new ResponseEntity<ResultVO>(ResultVO.createError("同步单个商品时,商品编码必传!"), HttpStatus.OK);
        }
        NoticeMiddleParam param = new NoticeMiddleParam();
        param.setOrganSign(organSign);
        param.setProductPref(productPref);
        param.setOrgCode(orgCode);
        productApi.syncSingleToMiddle(param);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> syncAllToMiddle(HttpServletRequest request, @RequestBody String data) {
        JSONObject jo = JSON.parseObject(data);
        String orgCode = (String)jo.get("orgCode");
        String organSign = (String)jo.get("organSign");
        if(StringUtils.isEmpty(organSign)) {
            return new ResponseEntity<ResultVO>(ResultVO.createError("总部机构号为空"), HttpStatus.OK);
        }
        NoticeMiddleParam param = new NoticeMiddleParam();
        param.setOrganSign(organSign);
        param.setOrgCode(orgCode);
        productApi.syncAllToMiddle(param);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> queryYbmLowestPrice(HttpServletRequest request, @RequestBody  YbmProductDto ybmProductDto) {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        logger.info("ProductInfoApiController.queryYbmLowestPrice organSign:{} employee:{}", organSign, employee);
        ResultVO result = this.productApi.queryYbmLowestPriceFromDataCenter(organSign,ybmProductDto);
        return new ResponseEntity<ResultVO>(result,HttpStatus.OK);

    }

    @Override
    public ResponseEntity<ResultVO> queryYbmLowestPriceV2(HttpServletRequest request, @RequestBody  YbmProductDto ybmProductDto) {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        logger.info("ProductInfoApiController.queryYbmLowestPriceV2 organSign:{} employee:{}", organSign, employee);
        ResultVO result = this.productApi.queryYbmLowestPriceV2(organSign,ybmProductDto);
        return new ResponseEntity<ResultVO>(result,HttpStatus.OK);

    }

    @Override
    public ResponseEntity<ResultVO> queryYbmLowestPriceByProvinceV2(HttpServletRequest request,@RequestBody YbmProductDto ybmProductDto) {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        logger.info("ProductInfoApiController.queryYbmLowestPriceV2 organSign:{} employee:{}", organSign, employee);
        ResultVO result = this.productApi.queryYbmLowestPriceByProvinceV2(organSign,ybmProductDto);
        return new ResponseEntity<ResultVO>(result,HttpStatus.OK);

    }

    @Override
    public void exportYbmLowestPriceV2(HttpServletRequest request,HttpServletResponse response, @RequestBody  ProductDto productDto) {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        logger.info("ProductInfoApiController.queryYbmLowestPriceV2 organSign:{} employee:{}", organSign, employee);
        List<YbmProductDto> ybmProductDtoList = this.productApi.exportYbmLowestPriceV2(organSign, productDto);
        String sheetName = "未知";
        if(productDto.getRecommendType() != null){
            switch(productDto.getRecommendType()){
                case 1:
                    sheetName = "即将售罄";
                    break;
                case 2:
                    sheetName = "本店热销";
                    break;
                default:
                    break;
            }
        }

        String[] headers = {"商品名称","规格","厂家","活动价","上次采购价","下单链接"};
        String[] fieldNames = {"commonName","attributeSpecification","manufacturer","discountPrice","lastCostPrice","linkAddress"};
        try{
            String fileName = URLDecoder.decode(sheetName, "UTF-8");
            ExportExcelUtil.createExcel(response,request,fileName+DateUtil.parseDateToStr(new Date(),"yyyyMMdd")+".xls",sheetName,headers,fieldNames,ybmProductDtoList,false);
        }catch (Exception e){
            logger.error("exportYbmLowestPriceV2-导出失败,错误信息：{}",JSONUtils.obj2JSON(e));
        }
    }

    @Override
    public void exportYbmLowestPriceByProvinceV2(HttpServletRequest request,HttpServletResponse response) {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        logger.info("ProductInfoApiController.queryYbmLowestPriceV2 organSign:{} employee:{}", organSign, employee);
        List<YbmProductDto> ybmProductDtoList = this.productApi.exportYbmLowestPriceByProvinceV2(organSign);

        String sheetName = "本省热销";

        String[] headers = {"商品名称","规格","厂家","活动价","下单链接"};
        String[] fieldNames = {"commonName","attributeSpecification","manufacturer","discountPrice","linkAddress"};
        try{
            String fileName = URLDecoder.decode(sheetName, "UTF-8");
            ExportExcelUtil.createExcel(response,request,fileName+DateUtil.parseDateToStr(new Date(),"yyyyMMdd")+".xls",sheetName,headers,fieldNames,ybmProductDtoList,false);
        }catch (Exception e){
            logger.error("exportYbmLowestPriceV2-导出失败,错误信息：{}",JSONUtils.obj2JSON(e));
        }
    }


    @Override
    public ResponseEntity<ResultVO> queryMarketingAnalyseData(HttpServletRequest request, @RequestBody  ProductDto productDto) {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        logger.info("ProductInfoApiController.queryMarketingAnalyseData organSign:{} employee:{}", organSign, employee);
        ResultVO result = this.productApi.queryProductMarketingAnalyseForPageHome(organSign);
        return new ResponseEntity<ResultVO>(result,HttpStatus.OK);

    }

    @Override
    public ResponseEntity<ResultVO> queryMarketingAnalyseDataV2(HttpServletRequest request, @RequestBody  ProductDto productDto) {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        ResultVO result = this.productApi.queryProductMarketingAnalyseForPageHomeV2(organSign,productDto.getRecommendType());
        logger.info("ProductInfoApiController.queryMarketingAnalyseDataV2 organSign:{} employee:{}", organSign, employee);
        return new ResponseEntity<ResultVO>(result,HttpStatus.OK);

    }


    //************************首页采购推荐 走人工数据********************

    @Override
    public ResponseEntity<ResultVO> queryMarketingAnalyseFlag(HttpServletRequest request, @Valid ProductDto product) {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        logger.info("ProductInfoApiController.queryMarketingAnalyseFlag organSign:{} employee:{},indexPurchaseAreas:{}", organSign, employee);
        boolean analyseFlag = this.productApi.queryMarketingAnalyseFlag(organSign);
        return new ResponseEntity<>(ResultVO.createSuccess(analyseFlag), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getYbmGoodsSpecialManual(HttpServletRequest request, @Valid YbmProductDto ybmProductDto) {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        logger.info("ProductInfoApiController.getYbmGoodsSpecialManual organSign:{} employee:{}", organSign, employee);
        ResultVO result = this.productApi.queryYbmGoodsFromSpecialManual(organSign,ybmProductDto);
        return new ResponseEntity<ResultVO>(result,HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getDrugIdentCode(HttpServletRequest request) {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        logger.info("ProductInfoApiController.getDrugIdentCode organSign:{} employee:{}", organSign, employee);
        ResultVO result = this.productApi.getDrugIdentCode(organSign);
        return new ResponseEntity<ResultVO>(result,HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> syncDrugIdentCode(HttpServletRequest request) {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        logger.info("ProductInfoApiController.syncDrugIdentCode organSign:{} employee:{}", organSign, employee);
        ResultVO result = this.productApi.syncDrugIdentCode();
        return new ResponseEntity<ResultVO>(result,HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> posUpdateDrugIdentCode(@RequestHeader("commonRequestModel") String commonRequestModelStr,@RequestBody PosDrugIdentCodeUpdateVo posUpdateVo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        logger.info("ProductInfoApiController.posUpdateDrugIdentCode commonRequestModelStr:{}", commonRequestModelStr);
        logger.info("ProductInfoApiController.posUpdateDrugIdentCode posUpdateVo:{}", JSONObject.toJSONString(posUpdateVo));
        PosDrugIdentCodeUpdateDto posUpdateDto = new PosDrugIdentCodeUpdateDto();
        BeanUtils.copyProperties(posUpdateVo,posUpdateDto);
        if (commonRequestModel != null){
            posUpdateDto.setOrganSign(commonRequestModel.getOrganSign());
        }
        if(org.apache.commons.lang3.StringUtils.isBlank(posUpdateDto.getOrganSign())){
            return new ResponseEntity<>(ResultVO.createError("机构编码不能为空"),HttpStatus.OK);
        }
        if(org.apache.commons.lang3.StringUtils.isBlank(posUpdateDto.getDrugIdentCode())){
            return new ResponseEntity<>(ResultVO.createError("药品标识码不能为空"),HttpStatus.OK);
        }
        if(org.apache.commons.lang3.StringUtils.isBlank(posUpdateDto.getPref())){
            return new ResponseEntity<>(ResultVO.createError("商品编码不能为空"),HttpStatus.OK);
        }
        logger.info("ProductInfoApiController.posUpdateDrugIdentCode organSign:{}", posUpdateDto.getOrganSign());
        ResultVO result = this.productApi.posUpdateDrugIdentCode(posUpdateDto);
        return new ResponseEntity<>(result,HttpStatus.OK);
    }

    /**
     * 根据追溯码码查询69码
     *
     * @param request
     * @param reqData
     * @return
     */
    @Override
    public ResponseEntity<ResultVO> queryMsfxByTraceCode(HttpServletRequest request, @RequestBody String reqData) {
        ResultVO resultVO = new ResultVO();
        try {
            logger.info("根据追溯码查询69码入参,reqData:{}",reqData);
            Map<String,Object> dataMap = JacksonUtil.json2map(reqData);
            logger.info("根据追溯码查询69码入参,dataMap:{}",JSON.toJSONString(dataMap));
            if(dataMap == null || dataMap.get("traceCode") == null){
                resultVO.setCode(ResultCodeEnum.ERROR.getCode());
                resultVO.setMsg("追溯码为空，请检查入参");
                return new ResponseEntity(resultVO,HttpStatus.OK);
            }
            String traceCode = dataMap.get("traceCode").toString();
            if(org.apache.commons.lang3.StringUtils.isBlank(traceCode)){
                resultVO.setCode(ResultCodeEnum.ERROR.getCode());
                resultVO.setMsg("追溯码为空，请检查入参");
                return new ResponseEntity(resultVO,HttpStatus.OK);
            }
            AliTraceInfoVo vo = getTraceInfo(traceCode);
            resultVO.setCode(ResultCodeEnum.SUCCESS.getCode());
            resultVO.setMsg("操作成功");
            resultVO.setResult(vo);
            return new ResponseEntity(resultVO,HttpStatus.OK);
        } catch (Exception e) {
            logger.error("根据追溯码查询69码异常：" + reqData,e);
            resultVO.setCode(ResultCodeEnum.ERROR.getCode());
            resultVO.setMsg("根据追溯码查询69码异常，异常原因："+e.getMessage());
            return new ResponseEntity(resultVO,HttpStatus.OK);
        }
    }

    private AliTraceInfoVo getTraceInfo(String traceCode){
        Map<String,String> param = new HashMap<>();
        param.put("trace_code",traceCode);
        AliTraceInfoVo aliTraceInfo = new AliTraceInfoVo();
        try {
            String response = TopClientUtil.post(appKey, secret, msfxUrl, apiPath,param);
            logger.info("请求码上放心返回内容：" + response);
            if(org.apache.commons.lang3.StringUtils.isBlank(response)){
                return aliTraceInfo;
            }
            JSONObject jsonObject = JSONObject.parseObject(response);
            JSONObject responseObj = jsonObject.getJSONObject("alibaba_alihealth_drug_getbarcode_bytraccode_response");
            if (responseObj != null) {
                JSONObject result = responseObj.getJSONObject("result");
                if (result != null) {
                    JSONObject modelJson = result.getJSONObject("model");
                    if (modelJson != null) {
                        aliTraceInfo.setBarCode(modelJson.getString("barCode"));
                        aliTraceInfo.setBatchNo(modelJson.getString("batchNo"));
                        aliTraceInfo.setProduceDate(modelJson.getString("produceDate"));
                        aliTraceInfo.setExpireDate(modelJson.getString("expireDate"));
                    }
                }
            }
        }catch (Exception e){
            logger.error("请求码上放心接口异常：" + e.getMessage());
        }
        return aliTraceInfo;
    }
}