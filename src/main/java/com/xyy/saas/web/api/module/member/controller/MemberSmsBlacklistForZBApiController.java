package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.member.core.api.MemberBaseApi;
import com.xyy.saas.member.core.dto.MemberBaseDto;
import com.xyy.saas.member.core.dto.MemberShareDto;
import com.xyy.saas.web.api.module.utils.DateUtil;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.api.sms.SaasSmsBlacklistApi;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import com.xyy.user.module.dto.result.EmployeeDto;
import com.xyy.user.module.dto.sms.SaasSmsBlacklistDto;
import com.xyy.user.module.dto.sms.SaasSmsBlacklistParamDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-08-29T21:37:06.377+08:00")

@Slf4j
@Controller
@RequestMapping("/member/memberSmsBlacklist/zb")
@Api(value = "memberSmsBlacklist", description = "the memberSmsBlacklist API")
public class MemberSmsBlacklistForZBApiController {

    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;

    @Reference(version = "0.0.1")
    public EmployeeApi employeeApi;

    @Reference(version = "0.0.1")
    private MemberBaseApi memberBaseApi;

    @Reference(version = "0.0.1")
    private SaasSmsBlacklistApi saasSmsBlacklistApi;

    @ApiOperation(value = "分页获取会员短信黑名单列表", notes = "分页获取会员短信黑名单列表", response = MemberShareDto.class, tags = {"memberSmsBlacklist",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = PageInfo.class)})
    @RequestMapping(value = "/queryList", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> queryList(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                     @RequestBody SaasSmsBlacklistParamDto paramDto) {
        try {
            CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
            String organSign = commonRequestModel.getOrganSign();
            paramDto.setRootOrganSign(organSign);
            if (paramDto.getStartTime() != null) {
                paramDto.setStartTime(DateUtil.getDayBeginTime(paramDto.getStartTime()));
            }
            if (paramDto.getEndTime() != null) {
                paramDto.setEndTime(DateUtil.getDayEndTime(paramDto.getEndTime()));
            }
            return new ResponseEntity(saasSmsBlacklistApi.pageSearch(paramDto), HttpStatus.OK);
        } catch (Exception e) {
            log.error("MemberSmsBlacklistForZBApiController#queryList error, param:{}", JSON.toJSONString(paramDto), e);
            return new ResponseEntity(ResultVO.createError(e.getMessage()), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "添加会员短信黑名单", notes = "添加会员短信黑名单", response = MemberShareDto.class, tags = {"memberSmsBlacklist",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = PageInfo.class)})
    @RequestMapping(value = "/add", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> add(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                    @RequestBody SaasSmsBlacklistParamDto paramDto) {
        log.info("MemberSmsBlacklistForZBApiController#add, commonRequestModel:{}, paramDto:{}", commonRequestModelStr, JSON.toJSONString(paramDto));
        try {
            // 检查传参
            if (StringUtils.isEmpty(paramDto.getTelephone())) {
                return new ResponseEntity(ResultVO.createError("参数不合法"), HttpStatus.OK);
            }

            // 取值
            CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
            String rootOrganSign = commonRequestModel.getOrganSign();
            String employeeId = commonRequestModel.getEmployeeId();
            EmployeeDto employeeDto = employeeApi.queryEmployeeById(Integer.valueOf(employeeId)).getResult();

            // 查会员
            MemberBaseDto memberBaseParam = new MemberBaseDto();
            memberBaseParam.setHeadquartersOrganSign(rootOrganSign);
            memberBaseParam.setTelephone(paramDto.getTelephone());
            memberBaseParam.setYn(1);
            List<MemberBaseDto> memberBaseDtoList = memberBaseApi.selectMemberBaseListByCondition(memberBaseParam);
//            if (CollectionUtils.isEmpty(memberBaseDtoList)) {
//                log.info("MemberSmsBlacklistForZBApiController#add 未找到相关会员, memberBaseDtoList为空，memberBaseParam:{}", JSON.toJSONString(memberBaseParam));
//                return new ResponseEntity(ResultVO.createError("未找到相关会员"), HttpStatus.OK);
//            }

            // 组装
            List<SaaSDrugstoreDto> drugstoreList = drugstoreApi.getDrugstoreByHeadquartersOrganSign(rootOrganSign);
            if (CollectionUtils.isEmpty(drugstoreList)) {
                log.info("MemberSmsBlacklistForZBApiController#add 未找到相关会员, drugstoreList为空，rootOrganSign:{}", rootOrganSign);
                return new ResponseEntity(ResultVO.createError("未找到相关机构"), HttpStatus.OK);
            }
            Map<String, SaaSDrugstoreDto> drugstoreMap = drugstoreList.stream()
                    .collect(Collectors.toMap(SaaSDrugstoreDto::getOrganSign, Function.identity()));
            List<SaasSmsBlacklistDto> blacklistDtos = new ArrayList<>();
            Date time = new Date();
            //不是会员的手机号也可以加入黑名单
            if (!CollectionUtils.isEmpty(memberBaseDtoList)) {
                for (MemberBaseDto memberBaseDto : memberBaseDtoList) {
                    SaaSDrugstoreDto drugstore = drugstoreMap.get(memberBaseDto.getOrgansign());
                    if (drugstore == null) {
                        continue;
                    }
                    SaasSmsBlacklistDto blacklistDto = new SaasSmsBlacklistDto();
                    blacklistDto.setOrganSign(drugstore.getOrganSign());
                    blacklistDto.setOrganSignName(drugstore.getDrugstoreName());
                    blacklistDto.setRootOrganSign(rootOrganSign);
                    blacklistDto.setTelephone(paramDto.getTelephone());
                    blacklistDto.setMemberId(memberBaseDto.getId());
                    blacklistDto.setCreateUserId(Long.valueOf(employeeId));
                    blacklistDto.setCreateUserName(employeeDto.getName());
                    blacklistDto.setCreateTime(time);

                    blacklistDtos.add(blacklistDto);
                }
            }else {
                SaasSmsBlacklistDto blacklistDto = new SaasSmsBlacklistDto();
                blacklistDto.setOrganSign(rootOrganSign);
                blacklistDto.setOrganSignName("");
                blacklistDto.setRootOrganSign(rootOrganSign);
                blacklistDto.setTelephone(paramDto.getTelephone());
                blacklistDto.setMemberId(0L);
                blacklistDto.setCreateUserId(Long.valueOf(employeeId));
                blacklistDto.setCreateUserName(employeeDto.getName());
                blacklistDto.setCreateTime(time);

                blacklistDtos.add(blacklistDto);
            }
            if (CollectionUtils.isEmpty(blacklistDtos)) {
                log.info("MemberSmsBlacklistForZBApiController#add 未找到相关会员, blacklistDtos为空");
                return new ResponseEntity(ResultVO.createError("未找到相关会员"), HttpStatus.OK);
            }

            // 插入
            SaasSmsBlacklistParamDto dto = new SaasSmsBlacklistParamDto();
            dto.setBlacklistDtos(blacklistDtos);

            return new ResponseEntity(saasSmsBlacklistApi.batchInsert(dto), HttpStatus.OK);
        } catch (Exception e) {
            log.error("MemberSmsBlacklistForZBApiController#add error, param:{}", JSON.toJSONString(paramDto), e);
            return new ResponseEntity(ResultVO.createError(e.getMessage()), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "移除会员短信黑名单", notes = "移除会员短信黑名单", response = MemberShareDto.class, tags = {"memberSmsBlacklist",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = PageInfo.class)})
    @RequestMapping(value = "/delete", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> delete(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                       @RequestBody SaasSmsBlacklistParamDto paramDto) {
        if (StringUtils.isEmpty(paramDto.getIdList())) {
            return new ResponseEntity(ResultVO.createError("参数不合法"),HttpStatus.OK);
        }
        log.info("MemberSmsBlacklistForZBApiController#delete, commonRequestModel:{}, paramDto:{}", commonRequestModelStr, JSON.toJSONString(paramDto));

        try {
            if (paramDto.getStartTime() != null) {
                paramDto.setStartTime(DateUtil.getDayBeginTime(paramDto.getStartTime()));
            }
            if (paramDto.getEndTime() != null) {
                paramDto.setEndTime(DateUtil.getDayEndTime(paramDto.getEndTime()));
            }
            return new ResponseEntity(saasSmsBlacklistApi.delete(paramDto), HttpStatus.OK);
        } catch (Exception e) {
            log.error("MemberSmsBlacklistForZBApiController#delete error, param:{}", JSON.toJSONString(paramDto), e);
            return new ResponseEntity(ResultVO.createError(e.getMessage()), HttpStatus.OK);
        }
    }
}
