package com.xyy.saas.web.api.module.product.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@ApiModel(value = "请求参数封装model")
public class ProductStockModel implements Serializable {

    private static final long serialVersionUID = -2449922063211772653L;
    /**
     * 请求字典类型(查询商品信息使用)
     */
    @ApiModelProperty(value = "请求字典类型",name = "type")
    private Integer type;

    /**
     * 请求字典类型(查询商品信息使用)
     */
    @ApiModelProperty(value = "商品id",name = "productId")
    private Long productId;

    /**
     * 机构唯一标识(查询商品信息使用)
     */
    @ApiModelProperty(value = "机构唯一标识",name = "organSign")
    private String organSign;

    /**
     * 调拨单信息
     */
    @ApiModelProperty(value = "调拨单信息",name = "data")
    private String data;

    @ApiModelProperty(value = "当前页",name = "page")
    private Integer page;

    @ApiModelProperty(value = "每页数量",name = "rows")
    private Integer rows;

    @ApiModelProperty(value = "商品信息",name = "productName")
    private String  productName;

    @ApiModelProperty(value = "库存",name = "stockNumber")
    private Integer  stockNumber;

    @ApiModelProperty(value = "状态",name = "status")
    private Integer status;

    @ApiModelProperty(value = "架位",name = "positionid")
    private Integer positionid;

    @ApiModelProperty(value = "联营主体guid",name = "mainStoreGuid")
    private String mainStoreGuid;

    @ApiModelProperty(value = "单据编号",name = "billNo")
    private String billNo;
}
