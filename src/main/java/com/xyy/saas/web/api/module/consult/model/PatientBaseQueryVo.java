package com.xyy.saas.web.api.module.consult.model;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 患者查询条件Vo
 * <AUTHOR>
 */
@ApiModel(description = "患者查询条件")
public class PatientBaseQueryVo extends PageInfo<PatientBaseQueryVo> implements Serializable {

    private static final long serialVersionUID = 4475776432879057215L;
    /**
     * 混合查询
     */
    @ApiModelProperty(value = "混合查询")
    private String mixedQuery;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 性别 1-男 2-女
     */
    @ApiModelProperty(value = "性别 1-男 2-女", example = "1")
    private Byte sex;

    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码")
    private String telephone;

    /**
     * 婚否 0-未婚 1-已婚
     */
    @ApiModelProperty(value = "婚否 0-未婚 1-已婚", example = "0")
    private Byte married;

    /**
     * 过敏史 0-无 1-有
     */
    @ApiModelProperty(value = "过敏史 0-无 1-有", example = "0")
    private Byte allergy;

    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识", required = true)
    private String organSign;

    /**
     * 患者guid
     */
    @ApiModelProperty(value = "患者guid")
    private String guid;

    public String getMixedQuery() {
        return mixedQuery;
    }

    public void setMixedQuery(String mixedQuery) {
        this.mixedQuery = mixedQuery;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Byte getSex() {
        return sex;
    }

    public void setSex(Byte sex) {
        this.sex = sex;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public Byte getMarried() {
        return married;
    }

    public void setMarried(Byte married) {
        this.married = married;
    }

    public Byte getAllergy() {
        return allergy;
    }

    public void setAllergy(Byte allergy) {
        this.allergy = allergy;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    @Override
    public String toString() {
        return "PatientBaseQueryVo{" +
                "mixedQuery='" + mixedQuery + '\'' +
                ", name='" + name + '\'' +
                ", sex=" + sex +
                ", telephone='" + telephone + '\'' +
                ", married=" + married +
                ", allergy=" + allergy +
                ", organSign='" + organSign + '\'' +
                ", guid='" + guid + '\'' +
                '}';
    }
}