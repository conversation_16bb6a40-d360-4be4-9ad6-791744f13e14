package com.xyy.saas.web.api.module.pay.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@ApiModel(description = "结算记录查询Vo")
@Data
public class SettlementQueryVo {

    @ApiModelProperty(value = "开始日期")
    private String startTime;


    @ApiModelProperty(value = "结束日期")
    private String endTime;

    @ApiModelProperty(value = "渠道类型 1 银联 2 乐刷")
    private Integer channelType;

}