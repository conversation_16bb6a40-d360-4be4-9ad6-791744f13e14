package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.promotion.core.dto.PromotionPlanDto;
import com.xyy.saas.web.api.module.product.model.PromotionPlanVo;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR> on 2018年9月19日
 */
@RequestMapping("/product")
@Api(value = "促销计划", description = "促销计划")
public interface PromotionPlanApi {

	@ApiOperation(value = "保存促销计划", notes = "保存促销计划")
	
	@RequestMapping(value = "/promotion/save", produces = "application/json", method = RequestMethod.POST)
    ResponseEntity<ResultVO> save(HttpServletRequest request, @ApiParam PromotionPlanVo promotionPlan);

	@ApiOperation(value = "定时器扫描促销活动状态", notes = "定时器扫描促销活动状态)", response = String.class, tags={ "member", })
	@ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = String.class) })
	@RequestMapping(value = "/promotion/taskUpdatePromotionState",
			method = RequestMethod.POST)
    ResponseEntity<ResultVO> taskUpdatePromotionState();

	@ApiOperation(value = "老版促销查询促销方式接口", notes = "老版促销查询促销方式接口", response = PromotionPlanDto.class, tags={ "促销计划", })
	@ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = PromotionPlanVo.class) })
	@RequestMapping(value = "/product/getPromotionPlanType", method = RequestMethod.POST)
    ResponseEntity<ResultVO> getPromotionPlanType(@RequestParam(value = "prefList") List<String> prefList);

	@ApiOperation(value = "老版促销刷数据接口", notes = "老版促销刷数据接口", response = PromotionPlanDto.class, tags={ "促销计划", })
	@ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = PromotionPlanVo.class) })
	@RequestMapping(value = "/product/promotionSsj", method = RequestMethod.GET)
	int promotionSsj(@RequestParam String org);

}
