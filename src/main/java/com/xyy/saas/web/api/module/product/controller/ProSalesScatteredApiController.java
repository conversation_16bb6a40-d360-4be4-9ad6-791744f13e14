package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.dto.ProSalesScatteredDto;
import com.xyy.saas.product.core.dto.SalesSplitProductDto;
import com.xyy.saas.web.api.module.product.model.ProSalesScatteredVo;
import com.xyy.saas.web.api.module.product.model.SalesSplitProductVo;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T15:59:22.655+08:00")

@Controller
public class ProSalesScatteredApiController implements ProSalesScatteredApi {

    @com.alibaba.dubbo.config.annotation.Reference(version = "0.0.1")
    private com.xyy.saas.product.core.api.ProSalesScatteredApi salesScatteredApi;

    public ResponseEntity<ResultVO> addPro(HttpServletRequest request, @ApiParam(value = "销售拆零商品信息" ,required=true )  @Valid @RequestBody SalesSplitProductVo salesSplitProductVo) {
        SalesSplitProductDto proSalesScatteredDto = new SalesSplitProductDto();
        BeanUtils.copyProperties(salesSplitProductVo, proSalesScatteredDto);
        String organSign = request.getHeader("organSign");
        String username = request.getHeader("userName");
        proSalesScatteredDto.setOrganSign(organSign);

        List<ProSalesScatteredVo> salesData = salesSplitProductVo.getSalesData();
        List<ProSalesScatteredDto> list = new ArrayList<>();
        if (null != salesData && salesData.size() > 0){
            for (ProSalesScatteredVo vo : salesData) {
                ProSalesScatteredDto dto = new ProSalesScatteredDto();
                BeanUtils.copyProperties(vo, dto);
                list.add(dto);
            }
        }

        proSalesScatteredDto.setSalesData(list);
        ResultVO result = salesScatteredApi.save(proSalesScatteredDto, username);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    public ResponseEntity<ResultVO> updatePro(HttpServletRequest request, @ApiParam(value = "销售拆零商品信息" ,required=true )  @Valid @RequestBody SalesSplitProductVo salesSplitProductVo) {
        SalesSplitProductDto proSalesScatteredDto = new SalesSplitProductDto();
        BeanUtils.copyProperties(salesSplitProductVo, proSalesScatteredDto);
        String organSign = request.getHeader("organSign");
        String username = request.getHeader("userName");
        proSalesScatteredDto.setOrganSign(organSign);

        ResultVO result = salesScatteredApi.save(proSalesScatteredDto, username);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

}
