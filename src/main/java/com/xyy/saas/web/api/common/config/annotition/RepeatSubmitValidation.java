package com.xyy.saas.web.api.common.config.annotition;


import java.lang.annotation.*;

@Inherited
@Documented
@Target({ElementType.TYPE,ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface RepeatSubmitValidation {
    /**
     * 防重标识过期时间 单位毫秒，默认2秒
     */
    int pexpire() default 2000;

    /**
     * 重复提交 返回类型 1.Integer 2.string 3.common.util.ResultVO
     * 4.org.springframework.http.ResponseEntity<ResultVO>
     * @return
     */
    int resultType();
}