/**
 * NOTE: This class is auto generated by the swagger code generator program (2.2.3).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.dto.FirstCheckProductQueryDto;
import com.xyy.saas.web.api.module.product.model.FirstCheckProductQuery;
import com.xyy.saas.web.api.module.product.model.FirstCheckProductVo;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T18:00:37.938+08:00")

@Api(value = "firstCheckProduct", description = "the firstCheckProduct API")
@RequestMapping("/product")
public interface FirstCheckProductApi {

    @ApiOperation(value = "根据首营商品ID获取首营品种信息", notes = "根据首营商品ID获取首营品种信息", response = FirstCheckProductVo.class, tags={ "firstCheckProduct", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = FirstCheckProductVo.class) })

    @RequestMapping(value = "/firstCheckProduct/getProductById/{guid}/{organ}",
            method = RequestMethod.GET)
    ResponseEntity<ResultVO> getProductById(String guid,String organ);


    @ApiOperation(value = "根据条件首营品种列表", notes = "根据条件首营品种列表", response = FirstCheckProductVo.class, tags={ "firstCheckProduct", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = FirstCheckProductVo.class) })

    @RequestMapping(value = "/firstCheckProduct/list",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> list(@ApiParam(value = "首营商品列表查询条件实体" ,required=true )  @Valid @RequestBody FirstCheckProductQuery firstCheckProductQuery);

    /**
     * 连锁首营商品列表分页查询
     * @param
     * @return
     */
    @ApiOperation(value = "连锁首营商品信息查询列表接口", notes = "连锁首营商品信息查询列表接口", response = ResultVO.class, tags = {"4.0首营商品信息API接口",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class)})
    @RequestMapping(value = "/baseinfo/product/FirstProductList", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> queryFirstProductList(HttpServletRequest request, @ApiParam(value = "首营商品查询对象", required = true) @Valid @RequestBody FirstCheckProductQueryDto firstProduct);
    /**
     * 连锁首营商品详情页数据
     * @param
     * @return
     */
    @ApiOperation(value = "连锁首营商品详情接口", notes = "连锁首营商品详情接口", response = ResultVO.class, tags = {"4.0首营商品信息API接口",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class)})
    @RequestMapping(value = "/baseinfo/product/toProductInfo", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> getFirstProduct(HttpServletRequest request, @ApiParam(value = "首营商品查询对象", required = true) @Valid @RequestBody FirstCheckProductQueryDto firstProduct);

    /**
     * 连锁首营商品打印
     * @param
     * @return
     */
    @ApiOperation(value = "连锁首营商品打印接口", notes = "连锁首营商品打印接口", response = ResultVO.class, tags = {"4.0首营商品信息API接口",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class)})
    @RequestMapping(value = "/baseinfo/product/printProduct", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> printFirstProduct(HttpServletRequest request, @ApiParam(value = "首营商品查询对象", required = true) @Valid @RequestBody FirstCheckProductQueryDto firstProduct);


    /**
     * 获取连锁首营商品审核记录列表
     * @param
     * @return
     */
    @ApiOperation(value = "连锁首营商品审核记录", notes = "连锁首营商品审核记录", response = ResultVO.class, tags = {"4.0首营商品信息API接口",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class)})
    @RequestMapping(value = "/baseinfo/product/getApproveFlowList", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> getApproveFlowList(HttpServletRequest request, @ApiParam(value = "首营商品查询对象", required = true) @Valid @RequestBody FirstCheckProductQueryDto firstProduct);



}
