package com.xyy.saas.web.api.common.swagger2;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.ArrayList;
import java.util.List;

@Configuration
@EnableSwagger2 // 启用swagger2
public class Swagger2Config {
    /**
     * 默认swagger文档
     * @return
     */
    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("member")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.xyy.saas.web.api.module.member")) //扫描路径
                .paths(PathSelectors.any()) //定义哪些路径的接口需要生成文档
                .build()
                .globalOperationParameters(getGlobalParamList());
    }

    /**
     * product服务分组
     * @return
     */
    @Bean
    public Docket productDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("product")
                .apiInfo(apiInfo("product模块 Swagger2接口文档","网关API分组saas-gateway-web product模块 Swagger2接口文档"))
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.xyy.saas.web.api.module.product"))
                .paths(PathSelectors.any())
                .build()
                .globalOperationParameters(getGlobalParamList());
    }


    /**
     * pay服务分组
     * @return
     */
    @Bean
    public Docket payDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("pay")
                .apiInfo(apiInfo("pay模块 Swagger2接口文档","网关API分组saas-gateway-web pay模块 Swagger2接口文档"))
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.xyy.saas.web.api.module.pay"))
                .paths(PathSelectors.any())
                .build()
                .globalOperationParameters(getGlobalParamList());
    }

    @Bean
    public Docket promotionDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("promotion")
                .apiInfo(apiInfo("promotion模块 Swagger2接口文档","网关API分组saas-gateway-web promotion模块 Swagger2接口文档"))
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.xyy.saas.web.api.module.promotion"))
                .paths(PathSelectors.any())
                .build()
                .globalOperationParameters(getGlobalParamList());
    }

    @Bean
    public Docket supplierDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("supplier")
                .apiInfo(apiInfo("supplier模块 Swagger2接口文档","网关API分组saas-gateway-web supplier模块 Swagger2接口文档"))
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.xyy.saas.web.api.module.supplier"))
                .paths(PathSelectors.any())
                .build()
                .globalOperationParameters(getGlobalParamList());
    }

    @Bean
    public Docket externalDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("external")
                .apiInfo(apiInfo("external模块 Swagger2接口文档","网关API分组saas-web-api external模块 Swagger2接口文档"))
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.xyy.saas.web.api.module.external"))
                .paths(PathSelectors.any())
                .build()
                .globalOperationParameters(getGlobalParamList());
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("member模块 Swagger2接口文档") //文档首页标题
                .description("网关API分组saas-gateway-web member模块 Swagger2接口文档") //文档描述信息
                .contact(new Contact("hanweiwang", "url", "<EMAIL>")) //创建者信息
                .termsOfServiceUrl("**********************:saas/saas-gateway-web.git")
                .version("1.0") //文档版本
                .build();
    }

    private ApiInfo apiInfo(String title, String description) {
        return new ApiInfoBuilder()
                .title(title) //文档首页标题
                .description(description) //文档描述信息
                .contact(new Contact("hanweiwang", "url", "<EMAIL>")) //创建者信息
                .termsOfServiceUrl("**********************:saas/saas-gateway-web.git")
                .version("1.0") //文档版本
                .build();
    }

    /**
     * 获取全局的swagger来源的参数
     * @return
     */
    private List<Parameter> getGlobalParamList(){
        ParameterBuilder parameterBuilder = new ParameterBuilder();
        List<Parameter> pars = new ArrayList<>();
        parameterBuilder.name("swaggerSource").description("用于全局标识swagger来源的接口访问，业务接口不用理会该参数，请忽略").modelRef(new ModelRef("string")).parameterType("header").required(true).defaultValue("1").hidden(true).build();
        Parameter parameter = parameterBuilder.build();
        pars.add(parameter);
        return pars;
    }

}
