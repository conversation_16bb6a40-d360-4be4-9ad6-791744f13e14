package com.xyy.saas.web.api.module.product.model;

import io.swagger.annotations.ApiModel;

import java.util.List;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

@ApiModel(description = "更新参类")
public class YbmlUpdateVo {

    /**
     * 商品pref列表
     */
    private List<String> prefs;

    /**
     * 是否医保，0：非医保药品，1：医保药品
     */
    private Integer medicalInsurance;

    /**
     * s是否全选,0:部分,1:全选
     */
    private Integer allFlag;

    /**
     * 全选时用于查询的接口
     */
    private String param;

    public List<String> getPrefs() {
        return prefs;
    }

    public void setPrefs(List<String> prefs) {
        this.prefs = prefs;
    }

    public Integer getMedicalInsurance() {
        return medicalInsurance;
    }

    public void setMedicalInsurance(Integer medicalInsurance) {
        this.medicalInsurance = medicalInsurance;
    }

    public Integer getAllFlag() {
        return allFlag;
    }

    public void setAllFlag(Integer allFlag) {
        this.allFlag = allFlag;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }
}
