package com.xyy.saas.web.api.module.consult.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.consult.cores.api.ConsultDrugApi;
import com.xyy.saas.consult.cores.dto.ConsultDrugDto;
import com.xyy.saas.web.api.module.consult.model.ConsultDrugVo;
import io.swagger.annotations.*;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/member/consultDrug")
@Api(value = "consultDrug", description = "远程问诊曾用药品/联合用药API")
public class ConsultDrugApiController {

	private static final Logger logger = LogManager.getLogger(ConsultDrugApiController.class);
    @Reference( version = "0.0.2")
    private ConsultDrugApi consultDrugApi;


    @ApiOperation(value = "添加曾用药品/联合用药", notes = "添加曾用药品/联合用药", response = Boolean.class, tags = {"consultDrug",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/insert", method = RequestMethod.POST,produces =  "application/json",
            consumes = "application/json")
    public ResponseEntity<ResultVO> insert(@ApiParam(value = "曾用药品/联合用药", required = true) @RequestBody ConsultDrugVo consultDrugVo) {
        if (consultDrugVo.getConsultRecordId() == null || consultDrugVo.getConsultRecordId() <= 0) {
            return new ResponseEntity(new ResultVO(-1, "参数consultRecordId不能为空", false), HttpStatus.OK);
        }
        if (StringUtils.isEmpty(consultDrugVo.getProductPref())) {
            return new ResponseEntity(new ResultVO(-1, "参数productPref不能为空", false), HttpStatus.OK);
        }
        if (StringUtils.isEmpty(consultDrugVo.getOrganSign())) {
            return new ResponseEntity(new ResultVO(-1, "参数organSign不能为空", false), HttpStatus.OK);
        }
        ConsultDrugDto dto = new ConsultDrugDto();
        BeanUtils.copyProperties(consultDrugVo, dto);
        return new ResponseEntity(ResultVO.createSuccess(consultDrugApi.insert(dto)), HttpStatus.OK);
    }

}
