package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description 患者资料Vo
 * <AUTHOR>
 * @Create 2020-10-12 20:47
 */
@Data
public class MemberChronicPatientVo implements Serializable {
    @ApiModelProperty(value = "会员手机号")
    private String telephone;
    @ApiModelProperty(value = "姓名")
    private String name;
    @ApiModelProperty(value = "会员卡号")
    private String cartNo;
    @ApiModelProperty(value = "来源门店")
    private String drugstore;
    @ApiModelProperty(value = "来源门店名称")
    private String drugstoreName;
    @ApiModelProperty(value = "性别 1男 2女")
    private Integer sex;
    @ApiModelProperty(value = "年龄")
    private Integer age;
    @ApiModelProperty(value = "身份证号")
    private String idCard;
    @ApiModelProperty(value = "'身份证号密文'")
    private String idCardEncrypted;
    @ApiModelProperty(value = "关心病种(高血压、糖尿病)")
    private String chronicNames;
    @ApiModelProperty(value = "会员状态1启用  0停用")
    private Integer state;
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    @ApiModelProperty(value = "患者信息")
    private String patientInfo;

    @ApiModelProperty(value = " 是否为患者[患者信息详情]")
    private Integer patient;
    @ApiModelProperty(value = " 身高")
    private String height;
    @ApiModelProperty(value = " 体重")
    private String weight;
    @ApiModelProperty(value = " 血型:A型、B型、AB型、O型；默认为：空；")
    private String blood;
    @ApiModelProperty(value = " 婚姻状况:已婚、未婚、离异、丧偶；默认为：空；")
    private String marriage;
    @ApiModelProperty(value = " 过敏史")
    private String historyOfAllergy;
    @ApiModelProperty(value = " 既往病史")
    private String jiWangBingShi;
    @ApiModelProperty(value = " 健康需求")
    private String jianKangXuQiu;
    @ApiModelProperty(value = " 用药禁忌")
    private String yongYaoJinJi;
    @ApiModelProperty(value = " 常用药物")
    private String changYongYao;
    @ApiModelProperty(value = " 职业")
    private String profession;
    @ApiModelProperty(value = " 电子邮件")
    private String mailBox;
    @ApiModelProperty(value = " 关心病种")
    private String guanXinBing;
    @ApiModelProperty(value = " 医保卡")
    private String carteVital;
    @ApiModelProperty(value = " 患者身份证")
    private  String idCards;
    @ApiModelProperty(value = " 患者病例")
    private String patientCase;
    @ApiModelProperty(value = " 医疗保险特殊药品使用申请表")
    private String applyForCard;
    @ApiModelProperty(value = " 享受门诊重症待遇通知单")
    private String letterOfNotice;
    @ApiModelProperty(value = " 其他")
    private String others;
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "慢病编号")
    private String chronicPrefs;
    @ApiModelProperty(value = "会员guid")
    private String guid;
}