package com.xyy.saas.web.api.module.consult.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 药店远程问诊服务记录Vo
 * <AUTHOR>
 */
@ApiModel(description = "药店远程问诊服务记录")
public class ConsultServiceRecordVo implements Serializable {

    private static final long serialVersionUID = 5108307704713574095L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 过期时间
     */
    @ApiModelProperty(value = "过期时间")
    private Date expiredTime;

    /**
     * 剩余条数
     */
    @ApiModelProperty(value = "剩余条数")
    private Integer remainingCount;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识", required = true)
    private String organSign;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer baseVersion;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getExpiredTime() {
        return expiredTime;
    }

    public void setExpiredTime(Date expiredTime) {
        this.expiredTime = expiredTime;
    }

    public Integer getRemainingCount() {
        return remainingCount;
    }

    public void setRemainingCount(Integer remainingCount) {
        this.remainingCount = remainingCount;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign == null ? null : organSign.trim();
    }

    public Integer getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(Integer baseVersion) {
        this.baseVersion = baseVersion;
    }

    @Override
    public String toString() {
        return "ConsultServiceRecordVo{" +
                "id=" + id +
                ", expiredTime=" + expiredTime +
                ", remainingCount=" + remainingCount +
                ", createUser='" + createUser + '\'' +
                ", createTime=" + createTime +
                ", updateUser='" + updateUser + '\'' +
                ", updateTime=" + updateTime +
                ", organSign='" + organSign + '\'' +
                ", baseVersion=" + baseVersion +
                '}';
    }
}