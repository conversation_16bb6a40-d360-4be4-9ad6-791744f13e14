package com.xyy.saas.web.api.module.consult.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 远程问诊第三方处方Vo
 * <AUTHOR>
 */
@ApiModel(description = "远程问诊第三方处方")
public class ConsultPrescriptionVo implements Serializable {

    private static final long serialVersionUID = 2770735764214827402L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 处方单号
     */
    @ApiModelProperty(value = "处方单号")
    private String pref;

    /**
     * 远程问诊id
     */
    @ApiModelProperty(value = "远程问诊id")
    private Long consultRecordId;

    /**
     * 患者id
     */
    @ApiModelProperty(value = "患者id")
    private Long patientId;

    /**
     * 医生名称
     */
    @ApiModelProperty(value = "医生名称")
    private String doctorName;

    /**
     * 科室
     */
    @ApiModelProperty(value = "科室")
    private String departments;

    /**
     * 医院
     */
    @ApiModelProperty(value = "医院")
    private String hospital;

    /**
     * 诊断内容
     */
    @ApiModelProperty(value = "诊断内容")
    private String diagnosticContent;

    /**
     * 处方pdf地址
     */
    @ApiModelProperty(value = "处方pdf地址")
    private String prescriptionPdf;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 是否被调取过 0-否 1-是
     */
    @ApiModelProperty(value = "是否被调取过 0-否 1-是", example = "0")
    private Byte used;

    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识")
    private String organSign;

    /**
     * 逻辑删除 1-有效 0-删除
     */
    @ApiModelProperty(value = "逻辑删除 1-有效 0-删除", example = "1")
    private Byte yn;

    /**
     * 关联我方处方guid
     */
    @ApiModelProperty(value = "关联我方处方guid")
    private String prescriptionGuid;

    /**
     * 处方药匹配的商品编码列表
     */
    @ApiModelProperty(value = "处方药匹配的商品编码列表")
    private List<String> productPrefs;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref == null ? null : pref.trim();
    }

    public Long getConsultRecordId() {
        return consultRecordId;
    }

    public void setConsultRecordId(Long consultRecordId) {
        this.consultRecordId = consultRecordId;
    }

    public Long getPatientId() {
        return patientId;
    }

    public void setPatientId(Long patientId) {
        this.patientId = patientId;
    }

    public String getDoctorName() {
        return doctorName;
    }

    public void setDoctorName(String doctorName) {
        this.doctorName = doctorName == null ? null : doctorName.trim();
    }

    public String getDepartments() {
        return departments;
    }

    public void setDepartments(String departments) {
        this.departments = departments == null ? null : departments.trim();
    }

    public String getHospital() {
        return hospital;
    }

    public void setHospital(String hospital) {
        this.hospital = hospital == null ? null : hospital.trim();
    }

    public String getDiagnosticContent() {
        return diagnosticContent;
    }

    public void setDiagnosticContent(String diagnosticContent) {
        this.diagnosticContent = diagnosticContent == null ? null : diagnosticContent.trim();
    }

    public String getPrescriptionPdf() {
        return prescriptionPdf;
    }

    public void setPrescriptionPdf(String prescriptionPdf) {
        this.prescriptionPdf = prescriptionPdf == null ? null : prescriptionPdf.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    public Byte getUsed() {
        return used;
    }

    public void setUsed(Byte used) {
        this.used = used;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public Byte getYn() {
        return yn;
    }

    public void setYn(Byte yn) {
        this.yn = yn;
    }

    public String getPrescriptionGuid() {
        return prescriptionGuid;
    }

    public void setPrescriptionGuid(String prescriptionGuid) {
        this.prescriptionGuid = prescriptionGuid == null ? null : prescriptionGuid.trim();
    }
    public List<String> getProductPrefs() {
        return productPrefs;
    }

    public void setProductPrefs(List<String> productPrefs) {
        this.productPrefs = productPrefs;
    }

    @Override
    public String toString() {
        return "ConsultPrescriptionVo{" +
                "id=" + id +
                ", pref='" + pref + '\'' +
                ", consultRecordId=" + consultRecordId +
                ", patientId=" + patientId +
                ", doctorName='" + doctorName + '\'' +
                ", departments='" + departments + '\'' +
                ", hospital='" + hospital + '\'' +
                ", diagnosticContent='" + diagnosticContent + '\'' +
                ", prescriptionPdf='" + prescriptionPdf + '\'' +
                ", createTime=" + createTime +
                ", createUser='" + createUser + '\'' +
                ", updateTime=" + updateTime +
                ", updateUser='" + updateUser + '\'' +
                ", used=" + used +
                ", organSign='" + organSign + '\'' +
                ", yn=" + yn +
                ", prescriptionGuid='" + prescriptionGuid + '\'' +
                ", productPrefs=" + productPrefs +
                '}';
    }
}