package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 *会员慢病药品配置表(慢病病种关联的药品详情列表)
 *<AUTHOR>
 */
@Data
public class MemberChronicProductVo {
    //序号、商品编号、通用名称、规格/型号、基本单位、剂型、生产厂家、基本单位服用天数。
    @ApiModelProperty(value = "主键")
    private Long id; //id 自增
    @ApiModelProperty(value = "商品编号")
    private String productPref; //商品编号
    @ApiModelProperty(value = "通用名称")
    private String commonName; //通用名称
    @ApiModelProperty(value = "规格/型号")
    private String attributeSpecification; //规格/型号
    @ApiModelProperty(value = "基本单位(盒、支、箱)")
    private String unitName; //基本单位(盒、支、箱)
    @ApiModelProperty(value = "剂型-如:胶囊")
    private String dosageFormName; //剂型-如:胶囊
    @ApiModelProperty(value = "生产厂家")
    private String manufacturer; //生产厂家

    @ApiModelProperty(value = "基本单位服用天数1-999")
    private Integer unitDays; //基本单位服用天数1-999

    @ApiModelProperty(value = "慢病编号")
    private String pref; //慢病编号
    @ApiModelProperty(value = "所属门店(总部展示该字段)")
    private String organSign; //所属机构
    @ApiModelProperty(value = "所属门店名称(总部展示该字段)")
    private String organSignName; //所属机构

    //@ApiModelProperty(value = "创建人")
    //private Integer createUser; //创建人
    //@ApiModelProperty(value = "创建时间")
    //private Date createTime; //创建时间
    //@ApiModelProperty(value = "修改人")
    //private Integer updateUser; //修改人
    //@ApiModelProperty(value = "修改时间")
    //private Date updateTime; //修改时间
}