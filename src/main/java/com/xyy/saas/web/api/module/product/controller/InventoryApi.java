package com.xyy.saas.web.api.module.product.controller;


import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.web.api.module.product.model.InventoryVo;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-01-28T15:28:18.933+08:00")
@RequestMapping("/product/inventory")
@Api(value = "Inventory", description = "the Inventory API")
public interface InventoryApi {

    /**
     * 查询商户缺货数量-2019年1月28日15:42:10
     * @param organSign
     * @return
     */
    @ApiOperation(value = "查询缺货商品数量", notes = "查询缺货商品数量", response = Integer.class, tags = {"inventory", })
    @ApiResponses(value = {
            @ApiResponse(code = 0, message = "操作成功", response = Integer.class)})
    @RequestMapping(value = "/findAbsentInventory",method = RequestMethod.GET)
    @ResponseBody
    ResponseEntity<ResultVO> findAbsentInventory(@ApiParam(value = "商户编号", required = true) @RequestParam String organSign);
    /**
     * 修改库存数据-2019年1月28日15:42:10
     * @param organSign
     * @return
     */
    @ApiOperation(value = "修改库存数据", notes = "修改库存数据", response = String.class, tags = {"adjustInventory", })
    @ApiResponses(value = {
            @ApiResponse(code = 0, message = "操作成功", response = String.class)})
    @RequestMapping(value = "/adjustInventory",method = RequestMethod.GET)
    @ResponseBody
    String adjustInventory(@ApiParam(value = "商品库存Vo", required = true) @RequestParam InventoryVo inventoryVo);

    /**
     * 修改批号库存数据-2019年1月28日15:42:10
     * @param organSign
     * @return
     */
    @ApiOperation(value = "修改批号库存数据", notes = "修改批号库存数据", response = String.class, tags = {"adjustInventoryLotNumber", })
    @ApiResponses(value = {
            @ApiResponse(code = 0, message = "操作成功", response = String.class)})
    @RequestMapping(value = "/adjustInventoryLotNumber",method = RequestMethod.GET)
    @ResponseBody
    String adjustInventoryLotNumber(@ApiParam(value = "批号库存编号", required = true) @RequestParam String pref, @ApiParam(value = "机构编码", required = true) @RequestParam String organSign,
                                    @ApiParam(value = "库存数量", required = true) @RequestParam BigDecimal stockNumbers);

    /**
     * 根据商品账页批量修改库存数据-2019年3月25日13:28:10
     * @param organSign
     * @return
     */
    @ApiOperation(value = "根据商品账页批量修改库存数据", notes = "根据商品账页批量修改库存数据", response = String.class, tags = {"adjustInventoryLotNumber", })
    @ApiResponses(value = {
            @ApiResponse(code = 0, message = "操作成功", response = String.class)})
    @RequestMapping(value = "/updateBatchInventoryAndLotNumbers",method = RequestMethod.GET)
    @ResponseBody
    String updateBatchInventoryAndLotNumbers(@ApiParam(value = "商品账页编号", required = true) @RequestParam String stockPref, @ApiParam(value = "机构编码", required = true) @RequestParam String organSign);

    /**
     * 根据商品编号和库存数量判断，并返回库存数量-2019年3月25日13:28:10
     * @param organSign
     * @return
     */
    @ApiOperation(value = "根据商品编号和库存数量判断，并返回库存数量", notes = "根据商品编号和库存数量判断，并返回库存数量", response = ResultVO.class,  tags = {"findAndCompareStockNumber", })
    @ApiResponses(value = {
            @ApiResponse(code = 0, message = "操作成功", response = ResultVO.class)})
    @RequestMapping(value = "/findAndCompareStockNumber",
            method = RequestMethod.POST)
    @ResponseBody
    ResponseEntity<ResultVO> findAndCompareStockNumber(@ApiParam(value = "商品信息实体", required = true) @RequestBody String jsonVos);
}
