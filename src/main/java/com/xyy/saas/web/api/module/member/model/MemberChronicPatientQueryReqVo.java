package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Description 会员信息查询VO
 * <AUTHOR>
 * @Create 2020-10-12 20:24
 */
@Data
public class MemberChronicPatientQueryReqVo {
    //查询条件-1. 2.关心病种 3.来源门店 4.状态  5.创建日期 开始时间-结束时间

    @ApiModelProperty(value = "患者信息(会员信息)-混合查询")
    private String memberMixedQuery;
    @ApiModelProperty(value = "关心病种")
    private String chronicPref;
    @ApiModelProperty(value = "来源门店--总部查询使用")
    private String drugstore;
    @ApiModelProperty(value = "会员状态1启用  0停用")
    private Integer state;
    @ApiModelProperty(value = "创建日期-开始日期")
    private String startDate;
    @ApiModelProperty(value = "创建日期-结束日期")
    private String endDate;

    @ApiModelProperty(value = "开始的页码")
    private Integer pageNum;
    @ApiModelProperty(value = "每页的数量")
    private Integer pageSize;

    private Integer startAge;

    private Integer endAge;

    private List<Integer> startBirthdayRange;

    private List<Integer> endBirthdayRange;

    private Byte isDrugstoreHidden;
}
