package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Maps;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.encryption.cores.enums.DataTypeEnum;
import com.xyy.saas.member.core.api.DataTokenFlushApi;
import com.xyy.saas.member.core.api.MemberShareApi;
import com.xyy.saas.member.core.dto.*;
import com.xyy.saas.member.core.enums.MemberShareStatusEnum;
import com.xyy.saas.web.api.module.member.model.MemberPointHistory;
import com.xyy.saas.web.api.module.utils.ExportExcelUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/member/memberPointHistory")
public class MemberPointHistoryApiController {

    private static final Logger logger = LoggerFactory.getLogger(MemberPointHistoryApiController.class);

    @Reference( version = "0.0.1")
    private com.xyy.saas.member.core.api.MemberPointHistoryApi memberPointHistoryApi;

    @Reference( version = "0.0.1")
    private com.xyy.saas.member.core.api.MemberExchangeInfoApi memberExchangeInfoApi;

    @Reference(version = "0.0.1")
    private DataTokenFlushApi dataTokenService;

    @Reference(version = "0.0.1")
    private MemberShareApi memberShareApi;

    private static final String ORGAN_SIGN_TYPE = "1";

    @ApiOperation(value = "同步会员积分变动历史", notes = "同步会员积分变动历史", response = MemberPointHistory.class, tags={ "memberPointHistory", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberPointHistory.class) })

    @RequestMapping(value = "/getSyncMemberPointHistoryList",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getSyncMemberPointHistoryList(@RequestHeader(name = "organSign", required = true) String organSign, @ApiParam(value = "会员积分变动历史", required = true) @RequestBody MemberPointHistory memberPointHistory){
        MemberPointHistoryDto mph = new MemberPointHistoryDto();
        BeanUtils.copyProperties(memberPointHistory,mph);
        mph.setOrgansign(organSign);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(memberPointHistoryApi.getSyncMemberPointHistoryList(mph)), HttpStatus.OK);
    }

    @ApiOperation(value = "门店端:积分变更记录列表", notes = "门店端:积分变更记录列表", response = MemberPointHistoryConditionDto.class, tags={ "memberPointHistory", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberPointHistoryConditionDto.class) })
    @RequestMapping(value = "/getMemberPointHistoryPager", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> getMemberPointHistoryPager(@RequestHeader("commonRequestModel") String commonRequestModel,
                                                               @ApiParam(value = "会员积分变动历史" ,required=true )  @RequestBody MemberPointHistory memberPointHistory) {
        MemberPointHistoryConditionDto mph = new MemberPointHistoryConditionDto();
        BeanUtils.copyProperties(memberPointHistory,mph);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(memberPointHistory.getPageNum() == null ? 1:memberPointHistory.getPageNum());
        pageInfo.setPageSize(memberPointHistory.getPageSize() == null ? 50:memberPointHistory.getPageSize());
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);

        // 单体门店 或 联营连锁本店
        if (model.getBizModel() == 1 || (null != memberPointHistory.getOrgansign() && memberPointHistory.getOrgansign().equals(ORGAN_SIGN_TYPE))) {
            mph.setOrganSign(model.getOrganSign());
        } else {
            // 联营连锁 查询全部门店
            mph.setHeadquartersOrganSign(model.getHeadquartersOrganSign());
            mph.setSourceOrganSign(model.getOrganSign());
            mph.setOrganSign(model.getOrganSign());
//            List<MemberShareDto> shareRel = memberShareApi.selectSharedList(model.getHeadquartersOrganSign()).getResult();
//            List<String> shares = shareRel.stream().map(s -> s.getOrganSign()).collect(Collectors.toList());
//            if (CollectionUtils.isNotEmpty(shares) && shares.contains(model.getOrganSign())){
//                shares.add(model.getHeadquartersOrganSign());
//                //共享门店
//                mph.setOrganList(shares);
//            }else {
//                //不共享门店
//                mph.setOrgansign(model.getOrganSign());
//            }
        }
        mph.setMemberName(memberPointHistory.getMixedQuery());
        mph.setPage(pageInfo);
        PageInfo result = memberPointHistoryApi.getMemberPointHistoryPagerRlOrgan(mph, pageInfo);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(result),HttpStatus.OK);
    }

    @ApiOperation(value = "门店端:积分变更商品详情", notes = "门店端:积分变更商品详情", response = ResultVO.class, tags={ "memberPointHistory", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/getMemberPointHistoryDetail", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> getMemberPointHistoryDetail(@RequestHeader("commonRequestModel") String commonRequestModel,
                                                                @ApiParam(value = "会员积分单据编号",required=true )  @RequestBody MemberExchangeInfoDto memberExchangeInfoDto) {
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        memberExchangeInfoDto.setIsHidden(model.getIdentity().intValue());
        PageInfo pageInfo = memberExchangeInfoApi.getMemberExchangeDetailPage(memberExchangeInfoDto);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(pageInfo),HttpStatus.OK);
    }

    @ApiOperation(value = "门店端:积分变更记录导出", notes = "门店端:积分变更记录导出")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = com.xyy.saas.common.util.ResultVO.class) })
    @RequestMapping(value = "/exportMemberPointHistory",  method = RequestMethod.POST)
    public void exportRecordExcel(@RequestHeader("commonRequestModel") String commonRequestModel, HttpServletResponse response, HttpServletRequest request,
                                  @RequestBody MemberPointHistory memberPointHistory){
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);

        MemberPointHistoryConditionDto mph = new MemberPointHistoryConditionDto();
        BeanUtils.copyProperties(memberPointHistory,mph);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(200);

        // 单体门店 或 联营连锁本店
        if (model.getBizModel() == 1 || (null != memberPointHistory.getOrgansign() && memberPointHistory.getOrgansign().equals(ORGAN_SIGN_TYPE))) {
            mph.setOrganSign(model.getOrganSign());
        } else {
            // 联营连锁 查询全部门店
            mph.setHeadquartersOrganSign(model.getHeadquartersOrganSign());
        }
        mph.setMemberName(memberPointHistory.getMixedQuery());
        List<MemberPointHistoryConditionDto> pointHistoryList = new ArrayList<>();
        PageInfo result = memberPointHistoryApi.getMemberPointHistoryPager(mph, pageInfo);
        if (result != null && CollectionUtils.isNotEmpty(result.getList())) {
            pointHistoryList.addAll(result.getList());
            int firstPage = 1;
            int pages = result.getPages();
            /** 遍历查询数据 */
            while (firstPage < pages) {
                firstPage += 1;
                pageInfo.setPageNum(firstPage);
                PageInfo pointListPager = memberPointHistoryApi.getMemberPointHistoryPager(mph, pageInfo);
                pointHistoryList.addAll(pointListPager.getList());
            }
            // 会员手机号脱敏
            List<MemberPointHistoryConditionDto> hasTelephone = pointHistoryList.stream().filter(dto -> StringUtils.isNotEmpty(dto.getTelephoneEncrypted())).collect(Collectors.toList());
            Map<String, String> telephoneMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(hasTelephone)) {
                Set<String> telephoneTokens = hasTelephone.stream().map(member -> member.getTelephoneEncrypted()).collect(Collectors.toSet());
                telephoneMap = dataTokenService.getMemberSensitiveData(new ArrayList<>(telephoneTokens), Integer.valueOf(model.getEmployeeId()), DataTypeEnum.MOBILE_PHONE);
            }
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            for (MemberPointHistoryConditionDto pointHistory : pointHistoryList) {
                pointHistory.setSourceOrganSign(dateFormat.format(pointHistory.getCreateTime()));
                if (StringUtil.isNotEmpty(pointHistory.getTelephoneEncrypted())
                        && telephoneMap.containsKey(pointHistory.getTelephoneEncrypted())) {
                    pointHistory.setTelephone(telephoneMap.get(pointHistory.getTelephoneEncrypted()));
                }
            }
        }
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String extfilename="积分变更记录"+df.format(new Date())+".xls";
        String sheetName = "积分变更记录";
        String headers[] = new String[]{"单据编号","会员姓名","会员卡号","会员手机号","来源门店","积分变动","本期结转积分",
                "交易门店","门店编码","变更类型","变更时间","变更原因","操作人"};
        String fieldNames[] = new String[]{"businessNo","memberName","cartNo","telephone","sourceOrganSignName","operatorPoint","currentPoint",
                "organSignName","organSign","operatorTypeDesc","createTime", "editReason", "createUser"};
        try {
            ExportExcelUtil.createExcel(response,request,extfilename,sheetName, headers, fieldNames, pointHistoryList,true);
        } catch (Exception e) {
            logger.error("MemberPointHistoryController exportRecordExcel is error.", e);
        }
    }

    @ApiOperation(value = "门店端:查询会员积分变更详情", notes = "门店端:查询会员积分变更详情", response = ResultVO.class, tags={ "memberPointHistory", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberPointHistoryDto.class) })
    @RequestMapping(value = "/getMemberPointHistory", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getMemberPointHistory(@ApiParam(value = "会员积分变动编号",required=true )  @RequestBody MemberPointHistory memberPointHistory) {
        MemberPointHistoryDto memberPointHistoryDto = new MemberPointHistoryDto();
        BeanUtils.copyProperties(memberPointHistory, memberPointHistoryDto);
        List<MemberPointHistoryDto> list = memberPointHistoryApi.getMemberPointHistoryByGuid(memberPointHistoryDto);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(list),HttpStatus.OK);
    }

    /*
     * POS端查询会员积分变更历史（分页）,仅供POS调用
     * POS端添加分页查询的功能，增加此接口，不支持分页功能调用 getMemberPointHistory
     */
    @RequestMapping(value = "/getMemberPointHistory/page", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getMemberPointHistoryPage(@ApiParam(value = "会员积分变动编号",required=true )  @RequestBody MemberPointHistory memberPointHistory) {
        MemberPointHistoryDto memberPointHistoryDto = new MemberPointHistoryDto();
        BeanUtils.copyProperties(memberPointHistory, memberPointHistoryDto);
        memberPointHistoryDto.setPageNum(memberPointHistory.getPageNum() == null ? 1:memberPointHistory.getPageNum());
        memberPointHistoryDto.setPageSize(memberPointHistory.getPageSize() == null ? 50:memberPointHistory.getPageSize());
        PageInfo result = memberPointHistoryApi.getMemberPointHistoryByGuidPager(memberPointHistoryDto);
        return new ResponseEntity<>(ResultVO.createSuccess(result), HttpStatus.OK);
    }

}
