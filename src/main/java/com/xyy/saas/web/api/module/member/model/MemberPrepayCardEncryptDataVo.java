package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 会员储值卡加密参数Vo
 * <AUTHOR>
 */
@ApiModel(description = "会员储值卡加密参数Vo")
public class MemberPrepayCardEncryptDataVo implements Serializable {

    private static final long serialVersionUID = -5188575498523389298L;

    /**
     * 加密参数，使用aes cbc算法
     */
    @ApiModelProperty(value = "加密参数", example = "O3FF5IsHxd77OnPHzPqBCwf3wpVxGvriGWtvEN1SrSucHNrnlWSSqf5SQIR046wbzPHVnACnAOvOC0fmyB3A5DVLpss/Tjx6F9qBLZcSUbjeZodpqe6Wk5EKOOMGL++Uthg29xytzsEM7bBi25GMqg7qzRnNFWl9B1PVSb4+NutbnQNehH9UyzdiJ9TRkC9o/SGYIXeGb9/2fA+u8jLIIfd6gV3RXcRb9bT8Cjr/Vdw=")
    private String data;

    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识", hidden = true, example = "ZHL0001411")
    private String organSign;

    /**
     * 总部机构标识
     */
    @ApiModelProperty(value = "总部机构标识", hidden = true, example = "ZHLZB000011")
    private String headquartersOrganSign;

    /**
     * 员工id
     */
    @ApiModelProperty(value = "员工id", hidden = true, example = "1771")
    private String employeeId;

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public String getHeadquartersOrganSign() {
        return headquartersOrganSign;
    }

    public void setHeadquartersOrganSign(String headquartersOrganSign) {
        this.headquartersOrganSign = headquartersOrganSign;
    }

    public String getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }

    @Override
    public String toString() {
        return "MemberPrepayCardEncryptDataVo{" +
                "data='" + data + '\'' +
                ", organSign='" + organSign + '\'' +
                ", headquartersOrganSign='" + headquartersOrganSign + '\'' +
                ", employeeId='" + employeeId + '\'' +
                '}';
    }
}
