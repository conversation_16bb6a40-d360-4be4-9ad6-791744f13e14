package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 会员储值卡流水查询Vo
 * <AUTHOR>
 */
@ApiModel(description = "会员储值卡流水查询")
public class MemberPrepayCardHistoryQueryVo implements Serializable {


    private static final long serialVersionUID = 7058812163924212469L;

    /**
     * 操作类型 1-充值 2-消费 3-退款 4-退货
     * @see com.xyy.saas.member.core.enums.PrepayCardOperateTypeEnum
     */
    @ApiModelProperty(value = "操作类型 1-充值 2-消费 3-退款 4-退货", example = "1")
    private Byte operateType;

    /**
     * 支付类型 1-现金 2-微信 3-支付宝 4-银联 9-其他
     * @see com.xyy.saas.member.core.enums.PayTypeEnum
     */
    @ApiModelProperty(value = "支付类型 1-现金 2-微信 3-支付宝 4-银联 9-其他", example = "1")
    private Byte payType;

    /**
     * 查询开始时间
     */
    @ApiModelProperty(value = "查询开始时间", example = "1563552000000")
    private Date startTime;

    /**
     * 查询结束时间
     */
    @ApiModelProperty(value = "查询结束时间", example = "1563638399000")
    private Date endTime;

    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识", hidden = true)
    private String organsign;

    /**
     * 会员等级id
     */
    @ApiModelProperty(value = "会员等级id", example = "12")
    private Long vipLevelId;

    /**
     * 混合查询 会员姓名/首字母缩写/手机号/会员卡号
     */
    @ApiModelProperty(value = "混合查询 会员姓名/首字母缩写/手机号/会员卡号", example = "张三")
    private String mixedQuery;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人", example = "90")
    private String createUser;

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码", required = true, example = "1")
    private Integer pageNum;

    /**
     * 每页数量
     */
    @ApiModelProperty(value = "每页数量", required = true, example = "10")
    private Integer pageSize;

    /**
     * 导出excel文件名称
     */
    @ApiModelProperty(value = "导出excel文件名称", example = "汇总数据2019-08-01")
    private String excelName;

    /**
     * 是否隐藏门店
     */
    @ApiModelProperty(value = "是否隐藏门店")
    private Byte isDrugstoreHidden;

    public Byte getOperateType() {
        return operateType;
    }

    public void setOperateType(Byte operateType) {
        this.operateType = operateType;
    }

    public Byte getPayType() {
        return payType;
    }

    public void setPayType(Byte payType) {
        this.payType = payType;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getOrgansign() {
        return organsign;
    }

    public void setOrgansign(String organsign) {
        this.organsign = organsign;
    }

    public Long getVipLevelId() {
        return vipLevelId;
    }

    public void setVipLevelId(Long vipLevelId) {
        this.vipLevelId = vipLevelId;
    }

    public String getMixedQuery() {
        return mixedQuery;
    }

    public void setMixedQuery(String mixedQuery) {
        this.mixedQuery = mixedQuery;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getExcelName() {
        return excelName;
    }

    public void setExcelName(String excelName) {
        this.excelName = excelName;
    }

    public Byte getIsDrugstoreHidden() {
        return isDrugstoreHidden;
    }

    public void setIsDrugstoreHidden(Byte isDrugstoreHidden) {
        this.isDrugstoreHidden = isDrugstoreHidden;
    }

    @Override
    public String toString() {
        return "MemberPrepayCardHistoryQueryVo{" +
                "operateType=" + operateType +
                ", payType=" + payType +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", organsign='" + organsign + '\'' +
                ", vipLevelId=" + vipLevelId +
                ", mixedQuery='" + mixedQuery + '\'' +
                ", createUser='" + createUser + '\'' +
                ", pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                ", excelName='" + excelName + '\'' +
                '}';
    }
}