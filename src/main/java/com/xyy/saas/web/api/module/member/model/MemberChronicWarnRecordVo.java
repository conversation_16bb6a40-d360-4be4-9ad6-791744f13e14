package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 *会员慢病提醒记录表[商品、购买门店、会员]
 *<AUTHOR>
 */
@Data
public class MemberChronicWarnRecordVo {
    //序号、门店（总部展示该字段）√、会员手机号、姓名、会员等级、会员卡号、性别、年龄、通用名称、商品编号、规格/型号、基本单位、剂型、生产厂家、购买日期、数量、慢病种类、基本单位用完天数、本次购买用完天数、预计药品服完日期；
    //@ApiModelProperty(value = "主键")
    //private Long id; //id 自增
    //@ApiModelProperty(value = "慢病编号")
    //private String pref; //慢病编号
    @ApiModelProperty(value = "所属机构号(购买门店)")
    private String organSign; //所属机构(购买门店)
    @ApiModelProperty(value = "所属机构名称(购买门店)")
    private String organSignName; //所属机构(购买门店)
    @ApiModelProperty(value = "会员手机号")
    private String telephone;
    @ApiModelProperty(value = "姓名")
    private String name;
    @ApiModelProperty(value = "会员等级")
    private Long vipLevelId;
    @ApiModelProperty(value = "会员卡号")
    private String cartNo;
    @ApiModelProperty(value = "性别 1男 2女")
    private Integer sex;
    @ApiModelProperty(value = "年龄")
    private Integer age;
    @ApiModelProperty(value = "通用名称(商品)")
    private String commonName; //通用名称
    @ApiModelProperty(value = "商品编号")
    private String productPref; //商品编号
    @ApiModelProperty(value = "规格型号-0.3GX12粒X2板/50MG*12S")
    private String attributeSpecification;  //规格型号-0.3GX12粒X2板/50MG*12S
    @ApiModelProperty(value = "基本单位(盒、支、箱)")
    private String unitName; //单位名称-基本单位(盒、支、箱)
    @ApiModelProperty(value = "剂型-如:胶囊")
    private String dosageFormName; //剂型-如:胶囊剂/片剂
    @ApiModelProperty(value = "生产厂家")
    private String manufacturer; //生产厂家
    @ApiModelProperty(value = "购买日期")
    private Date orderDate; //商品购买日期[待确认()可以根据 预计药品用完日期-本次购买用完天数得到]
    @ApiModelProperty(value = "商品购买数量")
    private Integer amount; //商品购买数量
    @ApiModelProperty(value = "慢病种类")
    private String chronicName; //慢病种类名称
    @ApiModelProperty(value = "助记码")
    private String mnemonicCode;
    //购买日期、数量、慢病种类、基本单位用完天数、本次购买用完天数、预计药品服完日期；
    @ApiModelProperty(value = "基本单位服用天数1-999")
    private Integer unitDays; //基本单位服用天数1-999
    @ApiModelProperty(value = "本次购买用完天数")
    private Integer totalDays; //本次购买用完天数
    @ApiModelProperty(value = "预计药品用完日期")
    private Date overDate;//预计药品用完日期

    @ApiModelProperty(value = "会员guid")
    private String guid; //会员guid

}