package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 销售拆零商品信息实体
 */
@ApiModel(description = "销售拆零商品信息实体")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T15:59:22.655+08:00")

public class ProSalesScatteredVo   {
  @JsonProperty("productId")
  private Long productId = null;

  @JsonProperty("salesProductId")
  private Long salesProductId = null;

  @JsonProperty("ruleId")
  private Long ruleId = null;

  @JsonProperty("number")
  private BigDecimal number = null;

  @JsonProperty("count")
  private BigDecimal count = null;

  @JsonProperty("unitId")
  private Integer unitId = null;

  @JsonProperty("scatteredSpecification")
  private String scatteredSpecification = null;

  @JsonProperty("lotNumber")
  private String lotNumber = null;

  @JsonProperty("salesRecordId")
  private Long salesRecordId = null;

  @JsonProperty("productName")
  private String productName = null;

  @JsonProperty("splitNum")
  private BigDecimal splitNum = null;

  @JsonProperty("salesDetailId")
  private Long salesDetailId = null;

  @JsonProperty("lotId")
  private Integer lotId = null;

  @JsonProperty("productPref")
  private String productPref = null;

  @JsonProperty("salesProductPref")
  private String salesProductPref = null;

  @JsonProperty("rulePref")
  private String rulePref = null;

  public ProSalesScatteredVo productId(Long productId) {
    this.productId = productId;
    return this;
  }

   /**
   * 商品ID
   * @return productId
  **/
  @ApiModelProperty(value = "商品ID")


  public Long getProductId() {
    return productId;
  }

  public void setProductId(Long productId) {
    this.productId = productId;
  }

  public ProSalesScatteredVo salesProductId(Long salesProductId) {
    this.salesProductId = salesProductId;
    return this;
  }

   /**
   * 拆零商品ID
   * @return salesProductId
  **/
  @ApiModelProperty(value = "拆零商品ID")


  public Long getSalesProductId() {
    return salesProductId;
  }

  public void setSalesProductId(Long salesProductId) {
    this.salesProductId = salesProductId;
  }

  public ProSalesScatteredVo ruleId(Long ruleId) {
    this.ruleId = ruleId;
    return this;
  }

   /**
   * 拆零规则ID
   * @return ruleId
  **/
  @ApiModelProperty(value = "拆零规则ID")


  public Long getRuleId() {
    return ruleId;
  }

  public void setRuleId(Long ruleId) {
    this.ruleId = ruleId;
  }

  public ProSalesScatteredVo number(BigDecimal number) {
    this.number = number;
    return this;
  }

   /**
   * 拆零商品个数
   * @return number
  **/
  @ApiModelProperty(value = "拆零商品个数")


  public BigDecimal getNumber() {
    return number;
  }

  public void setNumber(BigDecimal number) {
    this.number = number;
  }

  public ProSalesScatteredVo count(BigDecimal count) {
    this.count = count;
    return this;
  }

   /**
   * 拆零后该商品的个数
   * @return count
  **/
  @ApiModelProperty(value = "拆零后该商品的个数")


  public BigDecimal getCount() {
    return count;
  }

  public void setCount(BigDecimal count) {
    this.count = count;
  }

  public ProSalesScatteredVo unitId(Integer unitId) {
    this.unitId = unitId;
    return this;
  }

   /**
   * 包装单位
   * @return unitId
  **/
  @ApiModelProperty(value = "包装单位")


  public Integer getUnitId() {
    return unitId;
  }

  public void setUnitId(Integer unitId) {
    this.unitId = unitId;
  }

  public ProSalesScatteredVo scatteredSpecification(String scatteredSpecification) {
    this.scatteredSpecification = scatteredSpecification;
    return this;
  }

   /**
   * 拆零规格
   * @return scatteredSpecification
  **/
  @ApiModelProperty(value = "拆零规格")


  public String getScatteredSpecification() {
    return scatteredSpecification;
  }

  public void setScatteredSpecification(String scatteredSpecification) {
    this.scatteredSpecification = scatteredSpecification;
  }

  public ProSalesScatteredVo lotNumber(String lotNumber) {
    this.lotNumber = lotNumber;
    return this;
  }

   /**
   * 批号
   * @return lotNumber
  **/
  @ApiModelProperty(value = "批号")


  public String getLotNumber() {
    return lotNumber;
  }

  public void setLotNumber(String lotNumber) {
    this.lotNumber = lotNumber;
  }

  public ProSalesScatteredVo salesRecordId(Long salesRecordId) {
    this.salesRecordId = salesRecordId;
    return this;
  }

   /**
   * 销售拆零主表ID值
   * @return salesRecordId
  **/
  @ApiModelProperty(value = "销售拆零主表ID值")


  public Long getSalesRecordId() {
    return salesRecordId;
  }

  public void setSalesRecordId(Long salesRecordId) {
    this.salesRecordId = salesRecordId;
  }

  public ProSalesScatteredVo productName(String productName) {
    this.productName = productName;
    return this;
  }

   /**
   * 商品名称
   * @return productName
  **/
  @ApiModelProperty(value = "商品名称")


  public String getProductName() {
    return productName;
  }

  public void setProductName(String productName) {
    this.productName = productName;
  }

  public ProSalesScatteredVo splitNum(BigDecimal splitNum) {
    this.splitNum = splitNum;
    return this;
  }

   /**
   * 每个商品拆成几个
   * @return splitNum
  **/
  @ApiModelProperty(value = "每个商品拆成几个")


  public BigDecimal getSplitNum() {
    return splitNum;
  }

  public void setSplitNum(BigDecimal splitNum) {
    this.splitNum = splitNum;
  }

  public ProSalesScatteredVo salesDetailId(Long salesDetailId) {
    this.salesDetailId = salesDetailId;
    return this;
  }

   /**
   * 销售拆零明细表ID
   * @return salesDetailId
  **/
  @ApiModelProperty(value = "销售拆零明细表ID")


  public Long getSalesDetailId() {
    return salesDetailId;
  }

  public void setSalesDetailId(Long salesDetailId) {
    this.salesDetailId = salesDetailId;
  }

  public ProSalesScatteredVo lotId(Integer lotId) {
    this.lotId = lotId;
    return this;
  }

   /**
   * 批准文号对应ID
   * @return lotId
  **/
  @ApiModelProperty(value = "批准文号对应ID")


  public Integer getLotId() {
    return lotId;
  }

  public void setLotId(Integer lotId) {
    this.lotId = lotId;
  }

  public ProSalesScatteredVo productPref(String productPref) {
    this.productPref = productPref;
    return this;
  }

   /**
   * 源商品编号
   * @return productPref
  **/
  @ApiModelProperty(value = "源商品编号")


  public String getProductPref() {
    return productPref;
  }

  public void setProductPref(String productPref) {
    this.productPref = productPref;
  }

  public ProSalesScatteredVo salesProductPref(String salesProductPref) {
    this.salesProductPref = salesProductPref;
    return this;
  }

   /**
   * 拆零后的商品编号
   * @return salesProductPref
  **/
  @ApiModelProperty(value = "拆零后的商品编号")


  public String getSalesProductPref() {
    return salesProductPref;
  }

  public void setSalesProductPref(String salesProductPref) {
    this.salesProductPref = salesProductPref;
  }

  public ProSalesScatteredVo rulePref(String rulePref) {
    this.rulePref = rulePref;
    return this;
  }

   /**
   * 拆零规则编号
   * @return rulePref
  **/
  @ApiModelProperty(value = "拆零规则编号")


  public String getRulePref() {
    return rulePref;
  }

  public void setRulePref(String rulePref) {
    this.rulePref = rulePref;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ProSalesScatteredVo productSalesScatteredVo = (ProSalesScatteredVo) o;
    return Objects.equals(this.productId, productSalesScatteredVo.productId) &&
        Objects.equals(this.salesProductId, productSalesScatteredVo.salesProductId) &&
        Objects.equals(this.ruleId, productSalesScatteredVo.ruleId) &&
        Objects.equals(this.number, productSalesScatteredVo.number) &&
        Objects.equals(this.count, productSalesScatteredVo.count) &&
        Objects.equals(this.unitId, productSalesScatteredVo.unitId) &&
        Objects.equals(this.scatteredSpecification, productSalesScatteredVo.scatteredSpecification) &&
        Objects.equals(this.lotNumber, productSalesScatteredVo.lotNumber) &&
        Objects.equals(this.salesRecordId, productSalesScatteredVo.salesRecordId) &&
        Objects.equals(this.productName, productSalesScatteredVo.productName) &&
        Objects.equals(this.splitNum, productSalesScatteredVo.splitNum) &&
        Objects.equals(this.salesDetailId, productSalesScatteredVo.salesDetailId) &&
        Objects.equals(this.lotId, productSalesScatteredVo.lotId) &&
        Objects.equals(this.productPref, productSalesScatteredVo.productPref) &&
        Objects.equals(this.salesProductPref, productSalesScatteredVo.salesProductPref) &&
        Objects.equals(this.rulePref, productSalesScatteredVo.rulePref);
  }

  @Override
  public int hashCode() {
    return Objects.hash(productId, salesProductId, ruleId, number, count, unitId, scatteredSpecification, lotNumber, salesRecordId, productName, splitNum, salesDetailId, lotId, productPref, salesProductPref, rulePref);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ProductSalesScatteredVo {\n");

    sb.append("    productId: ").append(toIndentedString(productId)).append("\n");
    sb.append("    salesProductId: ").append(toIndentedString(salesProductId)).append("\n");
    sb.append("    ruleId: ").append(toIndentedString(ruleId)).append("\n");
    sb.append("    number: ").append(toIndentedString(number)).append("\n");
    sb.append("    count: ").append(toIndentedString(count)).append("\n");
    sb.append("    unitId: ").append(toIndentedString(unitId)).append("\n");
    sb.append("    scatteredSpecification: ").append(toIndentedString(scatteredSpecification)).append("\n");
    sb.append("    lotNumber: ").append(toIndentedString(lotNumber)).append("\n");
    sb.append("    salesRecordId: ").append(toIndentedString(salesRecordId)).append("\n");
    sb.append("    productName: ").append(toIndentedString(productName)).append("\n");
    sb.append("    splitNum: ").append(toIndentedString(splitNum)).append("\n");
    sb.append("    salesDetailId: ").append(toIndentedString(salesDetailId)).append("\n");
    sb.append("    lotId: ").append(toIndentedString(lotId)).append("\n");
    sb.append("    productPref: ").append(toIndentedString(productPref)).append("\n");
    sb.append("    salesProductPref: ").append(toIndentedString(salesProductPref)).append("\n");
    sb.append("    rulePref: ").append(toIndentedString(rulePref)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

