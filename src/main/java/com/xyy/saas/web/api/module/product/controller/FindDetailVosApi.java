/**
 * NOTE: This class is auto generated by the swagger code generator program (2.2.3).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.web.api.module.product.model.InventoryLotnumAdjustDetailVo;
import com.xyy.saas.web.api.module.product.model.InventoryLotnumAdjustDetailVoList;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-29T17:04:23.698+08:00")
@RequestMapping("/product")
@Api(value = "findDetailVos", description = "the findDetailVos API")
public interface FindDetailVosApi {

    @ApiOperation(value = "查询所有商品批号库存明细调整", notes = "查询所有商品批号库存明细调整", response = InventoryLotnumAdjustDetailVoList.class, tags={ "InventoryLotnumAdjustDetailVo", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Invalid input", response = InventoryLotnumAdjustDetailVoList.class) })
    
    @RequestMapping(value = "/findDetailVos",
        produces = { "application/xml", "application/json" }, 
        consumes = { "application/json", "application/xml" },
        method = RequestMethod.POST)
    ResponseEntity<InventoryLotnumAdjustDetailVoList> findDetailVos(@ApiParam(value = "批号库存明细调整Vo", required = true) @Valid @RequestBody InventoryLotnumAdjustDetailVo inventoryLotnumAdjustDetailVo);


    /**
     * 修改供应商往来账页
     * @param organSign
     * @return
     */
    @ApiOperation(value = "修改供应商往来账页", notes = "修改供应商往来账页", response = String.class, tags = {"organSign", })
    @ApiResponses(value = {
            @ApiResponse(code = 0, message = "操作成功", response = String.class)})
    @RequestMapping(value = "/updateBusinessDealAccount",method = RequestMethod.POST)
    @ResponseBody
    String updateBusinessDealAccount(@ApiParam(value = "机构标识", required = true) @RequestParam String organSign);

}
