package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * @ClassName DrugStandardAreaVo
 * @Description 药监匹配地区对象
 * <AUTHOR>
 * @Date 2020/11/16 12:28
 * @Version 1.0
 **/
@ApiModel(value = "药监匹配地区对象", description = "药监匹配地区对象")
public class DrugStandardAreaVo implements Serializable {
    @ApiModelProperty(value = "地区编号")
    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    @ApiModelProperty(value = "地区名称")
    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    @JsonProperty("areaCode")
    private String areaCode;
    @JsonProperty("areaName")
    private String areaName;
}
