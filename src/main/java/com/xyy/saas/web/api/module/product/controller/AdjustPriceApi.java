/**
 * NOTE: This class is auto generated by the swagger code generator program (2.2.3).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.dto.PriceAdjustDto;
import com.xyy.saas.product.core.dto.ProductPriceListDto;
import com.xyy.saas.web.api.common.config.annotition.RepeatSubmitValidation;
import com.xyy.saas.web.api.module.product.model.ApproveHistoryVo;
import com.xyy.saas.web.api.module.product.model.PriceAdjustQueryVo;
import com.xyy.saas.web.api.module.product.model.PriceAdjustVo;
import com.xyy.saas.web.api.module.product.model.PriceImportVo;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

@Api(value = "连锁商品调价单API", description = "连锁商品调价单API")
@RequestMapping("/product")
public interface AdjustPriceApi {

    @ApiOperation(value = "商品售价调整新增", notes = "商品售价调整新增", response = PriceAdjustVo.class, tags={ "adjustPrice", })
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "操作成功", response = PriceAdjustVo.class) })

    @RequestMapping(value = "/adjustPrice/add",
        method = RequestMethod.POST)
    @RepeatSubmitValidation(resultType = 4)
    ResponseEntity<ResultVO> add(@ApiParam(value = "商品售价调整信息实体", required = true) @Valid @RequestBody PriceAdjustVo priceAdjustVo);


    @ApiOperation(value = "删除售价调整单接口", notes = "删除售价调整单接口", response = PriceAdjustVo.class, tags={ "adjustPrice", })
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "操作成功", response = PriceAdjustVo.class) })

    @RequestMapping(value = "/adjustPrice/delAdjust",
        method = RequestMethod.POST)
    ResponseEntity<ResultVO> delAdjust(@ApiParam(value = "售价调整单主表ID", required = true) @Valid @RequestBody Long id);


    @ApiOperation(value = "删除售价调整单接口", notes = "删除售价调整单接口", response = PriceAdjustVo.class, tags={ "adjustPrice", })
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "操作成功", response = PriceAdjustVo.class) })

    @RequestMapping(value = "/adjustPrice/exportExcelAdjustDetail",
        method = RequestMethod.POST)
    ResponseEntity<ResultVO> exportExcelAdjustDetail(@ApiParam(value = "商品售价调整信息实体", required = true) @Valid @RequestBody PriceAdjustVo priceAdjustVo);


    @ApiOperation(value = "首页获取当天的售价调整单总数", notes = "首页获取当天的售价调整单总数", response = PriceAdjustVo.class, tags={ "adjustPrice", })
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "操作成功", response = PriceAdjustVo.class) })

    @RequestMapping(value = "/adjustPrice/homepageAdjust",
        method = RequestMethod.POST)
    ResponseEntity<ResultVO> homepageAdjust();


    @ApiOperation(value = "商品售价调整单列表查询", notes = "商品售价调整单列表查询", response = PriceAdjustVo.class, tags={ "adjustPrice", })
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "操作成功", response = PriceAdjustVo.class) })

    @RequestMapping(value = "/adjustPrice/list",
        method = RequestMethod.POST)
    ResponseEntity<ResultVO> list(HttpServletRequest request, @ApiParam(value = "商品售价调整信息实体", required = true) @Valid @RequestBody PriceAdjustQueryVo priceAdjustQueryVo);


    @ApiOperation(value = "获取售价调整单的详情列表", notes = "获取售价调整单的详情列表", response = PriceAdjustVo.class, tags={ "adjustPrice", })
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "操作成功", response = PriceAdjustVo.class) })

    @RequestMapping(value = "/adjustPrice/queryAdjustDetailsList",
        method = RequestMethod.POST)
    ResponseEntity<ResultVO> queryAdjustDetailsList(@ApiParam(value = "商品审核列表实体以及查询条件实体", required = true) @Valid @RequestBody PriceAdjustVo priceAdjustVo);


    @ApiOperation(value = "商品售价调整单提交", notes = "商品售价调整单提交", response = PriceAdjustVo.class, tags={ "adjustPrice", })
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "操作成功", response = PriceAdjustVo.class) })

    @RequestMapping(value = "/adjustPrice/submit",
        method = RequestMethod.POST)
    @RepeatSubmitValidation(resultType = 4)
    ResponseEntity<ResultVO> submit(HttpServletRequest request,@ApiParam(value = "商品售价调整信息实体", required = true) @Valid @RequestBody PriceAdjustVo priceAdjustVo);


    @ApiOperation(value = "商品售价调整修改接口", notes = "商品售价调整修改接口", response = ApproveHistoryVo.class, tags={ "adjustPrice", })
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "操作成功", response = ApproveHistoryVo.class) })

    @RequestMapping(value = "/adjustPrice/update",
        method = RequestMethod.POST)
    ResponseEntity<ResultVO> update(HttpServletRequest request, @ApiParam(value = "商品售价调整信息实体", required = true) @Valid @RequestBody PriceAdjustVo priceAdjustVo);

    @ApiOperation(value = "获取调价部门接口", notes = "获取调价部门接口", response = ApproveHistoryVo.class, tags={ "连锁商品调价单API", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = ApproveHistoryVo.class) })

    @RequestMapping(value = "/adjustPrice/findAdjustOrganSigns",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> findAdjustOrganSigns(HttpServletRequest request);

    @ApiOperation(value = "调价记录接口", notes = "调价记录接口", response = ApproveHistoryVo.class, tags={ "连锁商品调价单API", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = ApproveHistoryVo.class) })

    @RequestMapping(value = "/adjustPrice/findRecords",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> findRecords(HttpServletRequest request, @ApiParam(value = "商品售价调整信息实体", required = true) @Valid @RequestBody PriceAdjustQueryVo priceAdjustQueryVo);

    @ApiOperation(value = "获取总部下面所有门店接口，包含总部", notes = "获取总部下面所有门店接口，包含总部", response = ApproveHistoryVo.class, tags={ "连锁商品调价单API", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = ApproveHistoryVo.class) })

    @RequestMapping(value = "/adjustPrice/findAllPartments",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> findAllPartments(HttpServletRequest request);

    @RequestMapping(value = "/adjustPrice/findAllPartments/v2",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> findAllPartmentsV2(HttpServletRequest request, @RequestBody PriceAdjustQueryVo priceAdjustQueryVo);

    @ApiOperation(value = "获取总部下面所有门店接口，排除总部", notes = "获取总部下面所有门店接口，排除总部", response = ApproveHistoryVo.class, tags={ "连锁商品调价单API", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = ApproveHistoryVo.class) })

    @RequestMapping(value = "/adjustPrice/findAllDrugstores",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> findAllDrugstores(HttpServletRequest request);

    @ApiOperation(value = "价格查询接口", notes = "价格查询接口", response = ApproveHistoryVo.class, tags={ "连锁商品调价单API", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = ApproveHistoryVo.class) })

    @RequestMapping(value = "/adjustPrice/queryPrice",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> queryPrice(HttpServletRequest request, @ApiParam(value = "商品售价调整信息实体", required = true) @Valid @RequestBody PriceAdjustQueryVo priceAdjustQueryVo);

    @ApiOperation(value = "价格查询接口", notes = "价格查询接口", response = ApproveHistoryVo.class, tags={ "连锁商品调价单API", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = ApproveHistoryVo.class) })

    @RequestMapping(value = "/adjustPrice/queryPriceOneProduct",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> queryPriceOneProduct(HttpServletRequest request, @ApiParam(value = "商品售价调整信息实体", required = true) @Valid @RequestBody PriceAdjustQueryVo priceAdjustQueryVo);

    @ApiOperation(value = "售价调整单 查询", notes = "售价调整单 查询", response = ApproveHistoryVo.class, tags={ "售价调整单API", })
    @ApiResponses(value = {  @ApiResponse(code = 200, message = "操作成功", response = ApproveHistoryVo.class) })
    @RequestMapping(value = "/adjustPrice/query", method = RequestMethod.POST)
    ResponseEntity<ResultVO> query(HttpServletRequest request, @ApiParam(value = "商品售价调整信息实体", required = true) @Valid @RequestBody PriceAdjustDto priceAdjustDto);

    /**
     * @param request  rows page id
     * @param priceAdjustDto
     * @return
     */
    @ApiOperation(value = "售价调整单 新增", notes = "售价调整单 新增", response = ApproveHistoryVo.class, tags={ "售价调整单API", })
    @ApiResponses(value = {  @ApiResponse(code = 200, message = "操作成功", response = ApproveHistoryVo.class) })
    @RequestMapping(value = "/adjustPrice/queryAdjustDetails", method = RequestMethod.POST)
    ResponseEntity<ResultVO> queryAdjustDetails(HttpServletRequest request, @ApiParam(value = "商品售价调整信息实体", required = true) @Valid @RequestBody PriceAdjustDto priceAdjustDto);

    /**
     *  rows page proName
     * @param request
     * @param priceAdjustDto
     * @return
     */
    @ApiOperation(value = "售价调整单 查询商品", notes = "售价调整单 查询商品", response = ApproveHistoryVo.class, tags={ "售价调整单API", })
    @ApiResponses(value = {  @ApiResponse(code = 200, message = "操作成功", response = ApproveHistoryVo.class) })
    @RequestMapping(value = "/adjustPrice/queryProductList", method = RequestMethod.POST)
    ResponseEntity<ResultVO> queryProductList(HttpServletRequest request, @ApiParam(value = "商品售价调整信息实体", required = true) @Valid @RequestBody PriceAdjustDto priceAdjustDto);

    /**
     * data
     * @param request
     * @param priceAdjustDto
     * @return
     */
    @ApiOperation(value = "售价调整单 保存调价单", notes = "售价调整单 保存调价单", response = ApproveHistoryVo.class, tags={ "售价调整单API", })
    @ApiResponses(value = {  @ApiResponse(code = 200, message = "操作成功", response = ApproveHistoryVo.class) })
    @RequestMapping(value = "/adjustPrice/save", method = RequestMethod.POST)
    ResponseEntity<ResultVO> adjustPriceSave(HttpServletRequest request, @ApiParam(value = "商品售价调整信息实体", required = true) @Valid @RequestBody PriceAdjustDto priceAdjustDto);

    /**
     * data
     * @param request
     * @param priceAdjustDto
     * @return
     */
    @ApiOperation(value = "售价调整单 提交前保存", notes = "售价调整单 提交前保存", response = ApproveHistoryVo.class, tags={ "售价调整单API", })
    @ApiResponses(value = {  @ApiResponse(code = 200, message = "操作成功", response = ApproveHistoryVo.class) })
    @RequestMapping(value = "/adjustPrice/forSaveProductPrice", method = RequestMethod. POST)
    ResponseEntity<ResultVO> forSaveProductPrice(HttpServletRequest request, @ApiParam(value = "商品售价调整信息实体", required = true) @Valid @RequestBody PriceAdjustDto priceAdjustDto);

    /**
     * data
     * @param request
     * @param priceAdjustDto
     * @return
     */
    @ApiOperation(value = "售价调整单 提交表单", notes = "售价调整单 提交表单 ", response = ApproveHistoryVo.class, tags={ "售价调整单API", })
    @ApiResponses(value = {  @ApiResponse(code = 200, message = "操作成功", response = ApproveHistoryVo.class) })
    @RequestMapping(value = "/adjustPrice/submit1", method = RequestMethod. POST)
    ResponseEntity<ResultVO> adjustPriceSubmit(HttpServletRequest request, @ApiParam(value = "商品售价调整信息实体", required = true) @Valid @RequestBody PriceAdjustDto priceAdjustDto);

    @RequestMapping(value = "/adjustPrice/queryImportList",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> queryImportList(HttpServletRequest request, @ApiParam(value = "导入任务id", required = true)  @RequestBody PriceImportVo param);
}
