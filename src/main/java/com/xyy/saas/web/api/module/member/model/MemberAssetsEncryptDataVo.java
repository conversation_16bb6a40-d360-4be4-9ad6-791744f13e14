package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 会员资产支付
 */
@ApiModel(description = "会员资产加密参数Vo")
public class MemberAssetsEncryptDataVo implements Serializable {

    /**
     * 加密参数，使用aes cbc算法
     */
    @ApiModelProperty(value = "加密参数", example = "O3FF5IsHxd77OnPHzPqBCwf3wpVxGvriGWtvEN1SrSucHNrnlWSSqf5SQIR046wbzPHVnACnAOvOC0fmyB3A5DVLpss/Tjx6F9qBLZcSUbjeZodpqe6Wk5EKOOMGL++Uthg29xytzsEM7bBi25GMqg7qzRnNFWl9B1PVSb4+NutbnQNehH9UyzdiJ9TRkC9o/SGYIXeGb9/2fA+u8jLIIfd6gV3RXcRb9bT8Cjr/Vdw=")
    private String data;


    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }
}
