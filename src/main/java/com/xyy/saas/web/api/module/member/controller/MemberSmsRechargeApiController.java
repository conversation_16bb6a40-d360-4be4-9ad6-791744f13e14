package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.util.StringUtil;
import com.xyy.saas.common.exception.BusinessException;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.member.core.api.MemberSmsApi;
import com.xyy.saas.member.core.api.MemberSmsRechargeApi;
import com.xyy.saas.member.core.dto.MemberSmsDto;
import com.xyy.saas.member.core.dto.MemberSmsRechargeDto;
import com.xyy.saas.pay.core.api.PayInfoApi;
import com.xyy.saas.pay.core.dto.GeneratorQRCodeResultDto;
import com.xyy.saas.pay.core.dto.PayInfoDto;
import com.xyy.saas.pay.core.dto.res.PayQrCodeInfoDto;
import com.xyy.saas.pay.core.emum.ChinaUmsBillStatus;
import com.xyy.saas.web.api.common.config.PaymentChangeGrayConfig;
import com.xyy.saas.web.api.module.member.model.MemberSmsRechargeReqVo;
import com.xyy.saas.web.api.module.member.model.MemberSmsRechargeVo;
import com.xyy.saas.web.api.module.member.model.MemberSmsRecordVo;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.dto.result.EmployeeDto;
import com.xyy.user.module.dto.result.QueryDrugstoreDto;
import io.swagger.annotations.*;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.math.BigDecimal;
import java.util.*;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-08-28T21:12:06.377+08:00")

@Controller
@RequestMapping("/member/memberSmsRecharge")
@Api(value = "memberSmsRecharge", description = "the memberSmsRecharge API")
public class MemberSmsRechargeApiController {

    private static final Logger logger = LogManager.getLogger(MemberSmsRechargeApiController.class);
    @Reference(version = "0.0.1")
    private MemberSmsRechargeApi memberSmsRechargeApi;
    @Reference(version = "0.0.1")
    private MemberSmsApi memberSmsApi;
    @Reference( version = "0.0.1")
    private PayInfoApi payInfoApi;
    @Reference(version = "0.0.1")
    public DrugstoreApi drugstoreApi;
    @Reference(version = "0.0.1")
    public EmployeeApi employeeApi;

    @Autowired
    private PaymentChangeGrayConfig paymentChangeGrayConfig;

    @ApiOperation(value = "短信充值记录", notes = "短信充值记录", response = MemberSmsRecordVo.class, tags={ "memberSmsRecharge", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberSmsRecordVo.class) })
    @RequestMapping(value = "/getMemberSmsRechargeList",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getMemberSmsRechargeList(@RequestHeader(name = "organSign", required = true) String organSign,
                                                             @RequestHeader(name = "employeeId", required = true) Integer employeeId,
                                                             @RequestBody MemberSmsRechargeDto rechargeDto){
        com.xyy.saas.common.util.ResultVO resultVO = drugstoreApi.queryDrugstoreByOrganSign(organSign);
        String organName="";
        if (Objects.nonNull(resultVO) && 0 == resultVO.getCode()){
            organName = ((QueryDrugstoreDto)resultVO.getResult()).getDrugstoreName();
        }else{
            return new ResponseEntity(new ResultVO(1, resultVO.getMsg(), false), HttpStatus.OK);
        }
        com.xyy.saas.common.util.ResultVO resultVO1 = employeeApi.queryEmployeeById(employeeId);
        String name = "";
        if (Objects.nonNull(resultVO1) && 0 == resultVO1.getCode()){
            name = ((EmployeeDto)resultVO1.getResult()).getName();
        }else{
            return new ResponseEntity(new ResultVO(1, resultVO.getMsg(), false), HttpStatus.OK);
        }
        rechargeDto.setPageNum(rechargeDto.getPageNum() == null ? 1 : rechargeDto.getPageNum());
        rechargeDto.setPageSize(rechargeDto.getPageSize() == null ? 10 : rechargeDto.getPageSize());
        rechargeDto.setOrganSign(organSign);
        if(!StringUtil.isEmpty(rechargeDto.getStartDate())) {
            rechargeDto.setStartDate(rechargeDto.getStartDate() + " 00:00:00");
        }
        if(!StringUtil.isEmpty(rechargeDto.getEndDate())) {
            rechargeDto.setEndDate(rechargeDto.getEndDate() + " 23:59:59");
        }
        PageInfo<MemberSmsRechargeDto> info = memberSmsRechargeApi.getMemberSmsRechargeList(rechargeDto);
        BigDecimal price = new BigDecimal(0);
        int count = 0;
        MemberSmsDto smsDto = memberSmsApi.getMemberSmsDto(organSign,name,organName);
        
//        MemberSmsRechargeDto dto = new MemberSmsRechargeDto();
//        dto.setOrganSign(organSign);
        rechargeDto.setPageNum(null);
        rechargeDto.setPageSize(null);
        PageInfo<MemberSmsRechargeDto> info1 = memberSmsRechargeApi.getMemberSmsRechargeList(rechargeDto);
        for (MemberSmsRechargeDto recharge: info1.getList()) {
                price = price.add(recharge.getPrice());
                count = count+recharge.getCount();
        }
        smsDto.setSumAmount(price);
        smsDto.setSumCount(count);
        Map<String,Object> map = new HashMap(2);
        map.put("pageInfo",info);
        map.put("memberSms",smsDto);
        return new ResponseEntity(ResultVO.createSuccess(map), HttpStatus.OK);
    }

    @ApiOperation(value = "生成充值二维码", notes = "生成充值二维码", response = String.class, tags = {"memberSmsRecharge",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = String.class)})
    @RequestMapping(value = "/getQrCode", method = RequestMethod.POST,produces =  "application/json",consumes = "application/json")
    ResponseEntity<ResultVO> getQrCode(@RequestHeader(name = "organSign", required = true) String organSign,
                                       @RequestHeader(name = "employeeId", required = true) Integer employeeId,
                                       @ApiParam(value = "充值信息", required = true) @RequestBody MemberSmsRechargeReqVo vo) throws BusinessException {
        Long smsPackageId = vo.getSmsPackageId();
        if (smsPackageId == null || smsPackageId <= 0) {
            return new ResponseEntity(new ResultVO(1, "参数smsPackageId不能为空", false), HttpStatus.OK);
        }
        String createUser = vo.getCreateUser();
        if (StringUtils.isEmpty(createUser)) {
            return new ResponseEntity(new ResultVO(1, "参数createUser不能为空", false), HttpStatus.OK);
        }
        logger.info("[SMS] 生成充值二维码:smsPackageId [" + smsPackageId + "], createUser: [" + createUser + "]");
        com.xyy.saas.common.util.ResultVO resultVO = drugstoreApi.queryDrugstoreByOrganSign(organSign);
        logger.info("[SMS] 生成充值二维码1 [" + JSONUtils.obj2JSON(resultVO) + "]");
        String organName="";
        if (Objects.nonNull(resultVO) && 0 == resultVO.getCode()){
            organName = ((QueryDrugstoreDto)resultVO.getResult()).getDrugstoreName();
        }else{
            return new ResponseEntity(new ResultVO(-1, resultVO.getMsg(), false), HttpStatus.OK);
        }
        com.xyy.saas.common.util.ResultVO resultVO1 = employeeApi.queryEmployeeById(employeeId);
        logger.info("[SMS] 生成充值二维码2 [" + JSONUtils.obj2JSON(resultVO1) + "]");
        String name = "";
        if (Objects.nonNull(resultVO1) && 0 == resultVO1.getCode()){
            name = ((EmployeeDto)resultVO1.getResult()).getName();
        }else{
            return new ResponseEntity(new ResultVO(1, resultVO.getMsg(), false), HttpStatus.OK);
        }
        //创建订单
        MemberSmsRechargeDto rechargeDto = new MemberSmsRechargeDto();
        rechargeDto.setOrganSign(organSign);
        rechargeDto.setCreateUser(name);
        rechargeDto.setOrganName(organName);
        rechargeDto.setSmsPackageId(vo.getSmsPackageId());
        logger.info("[SMS] 生成充值二维码3 [" + JSONUtils.obj2JSON(rechargeDto) + "]");
        rechargeDto = memberSmsRechargeApi.smsRecharge(rechargeDto);
        logger.info("[SMS] 生成充值二维码4 [" + JSONUtils.obj2JSON(rechargeDto) + "]");
        if (rechargeDto == null)  {
            return new ResponseEntity(new ResultVO(1, "创建订单失败", false), HttpStatus.OK);
        }
        String orderId = rechargeDto.getOrderId();
        if (StringUtils.isEmpty(orderId)) {
            return new ResponseEntity(new ResultVO(1, "创建订单失败", false), HttpStatus.OK);
        }
        //生成付款二维码
        PayInfoDto payInfoDto = new PayInfoDto();
        payInfoDto.setOrderNo(orderId);
        payInfoDto.setOrganSign(organSign);
        payInfoDto.setOrderSource(1);
        //1被扫 2主扫 3 h5 4app 5小程序  6公众号 7 短信
        payInfoDto.setBusinessType(7);
        payInfoDto.setTotalAmount(rechargeDto.getPrice());

        if(!paymentChangeGrayConfig.payChannleChange(organSign)){
            GeneratorQRCodeResultDto qrCodeResultDto = null;
            // 重试3次尝试获取二维码url
            for (int i = 0; i < 3; ++i) {
                logger.info("[SMS] 生成充值二维码5 [" + JSONUtils.obj2JSON(payInfoDto) + "]");
                qrCodeResultDto = payInfoApi.insertChinaUmsC2BPayInfo(payInfoDto);
                logger.info("[SMS] 生成充值二维码6 [" + JSONUtils.obj2JSON(payInfoDto) + "]");
                if (qrCodeResultDto != null && qrCodeResultDto.getErrCode().equals("SUCCESS")) {
                    break;
                }
            }
            if (qrCodeResultDto == null || !qrCodeResultDto.getErrCode().equals("SUCCESS")) {
                return new ResponseEntity(new ResultVO(2, "生成支付二维码地址失败", false), HttpStatus.OK);
            }
            String qrCode = "";
            if (qrCodeResultDto != null) {
                qrCode = qrCodeResultDto.getBillQRCode();
            }
            Map<String, String> map = new HashMap<>(3);
            map.put("qrCode", qrCode);
            map.put("price",rechargeDto.getPrice().toString());
            map.put("orderId", orderId);
            logger.info("[SMS] 生成充值二维码SUCCESS:map [" + JSONUtils.obj2JSON(map) + "]");
            return new ResponseEntity(ResultVO.createSuccess(map), HttpStatus.OK);
        }

        payInfoDto.setPayType(vo.getPayType());
        PayQrCodeInfoDto payQrCodeInfoDto = payInfoApi.paymentChannlePayInfo(payInfoDto);
        Map<String, String> map = new HashMap<>(3);
        map.put("qrCode", payQrCodeInfoDto.getQrCode());
        map.put("price",rechargeDto.getPrice().toString());
        map.put("orderId", orderId);
        logger.info("[SMS] 生成充值二维码SUCCESS:map [" + JSONUtils.obj2JSON(map) + "]");
        return new ResponseEntity(ResultVO.createSuccess(map), HttpStatus.OK);

    }



    @ApiOperation(value = "短信充值查询支付状态", notes = "短信充值查询支付状态", response = String.class, tags = {"memberSmsRecharge",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = String.class)})
    @RequestMapping(value = "/getSmsRecharge", method = RequestMethod.POST,produces =  "application/json",consumes = "application/json")
    ResponseEntity<ResultVO> getSmsRecharge(@RequestHeader(name = "organSign", required = true) String organSign,
                                       @ApiParam(value = "充值信息", required = true) @RequestBody MemberSmsRechargeVo vo) {
        String orderId = vo.getOrderId();
        if(StringUtils.isEmpty(orderId)){
            logger.info("[SMS] 短信充值orderId为空");
            return new ResponseEntity(new ResultVO(1, "参数orderId不能为空", false), HttpStatus.OK);
        }
        //查询订单
        MemberSmsRechargeDto smsRechargeDto = memberSmsRechargeApi.getMemberSmsRecharge(orderId,organSign);
        if(Objects.isNull(smsRechargeDto)){
            logger.info("[SMS] 短信充值订单不存在 orderId: [" + orderId + "], organSign: [" + organSign + "]");
            return new ResponseEntity(new ResultVO(1, "短信充值订单不存在", false), HttpStatus.OK);
        }
        //订单状态为 未支付 状态再次请求支付结果
        if (ChinaUmsBillStatus.UNPAID == smsRechargeDto.getPayStatus().intValue()) {
            List<PayInfoDto> list = new ArrayList<>();
            PayInfoDto payDto = new PayInfoDto();
            payDto.setOrderNo(orderId);
            payDto.setOrganSign(organSign);
            list.add(payDto);
            List<PayInfoDto> resultList;
            if(!paymentChangeGrayConfig.payChannleChange(organSign)){
                resultList = payInfoApi.getChinaUmsC2BPayStatus(list);
            }else{
                resultList = payInfoApi.getPaymentPayStatus(payDto);
            }
            if (Objects.isNull(resultList) || resultList.size() < 1) {
                logger.info("[SMS] 支付接口查询订单为空, orderId: [" + orderId + "], organSign: [" + organSign + "]");
                return new ResponseEntity(new ResultVO(1,"支付接口查询订单为空",false), HttpStatus.OK);
            }
            logger.info("支付服务查询结果: " + JSONObject.toJSONString(list.get(0)));
            int payStatus = resultList.get(0).getStatus();
            // 如果查询的订单状态非未支付，更新本地订单表
            if (payStatus != ChinaUmsBillStatus.UNPAID) {
                smsRechargeDto = new MemberSmsRechargeDto();
                smsRechargeDto.setOrderId(orderId);
                smsRechargeDto.setOrganSign(organSign);
                smsRechargeDto.setPayStatus((byte) payStatus);
                //订单支付成功，更新实付金额
                if (payStatus == ChinaUmsBillStatus.PAID) {
                    BigDecimal actualAmount = resultList.get(0).getTotalAmount().divide(new BigDecimal(100));
                    smsRechargeDto.setActualAmount(actualAmount);
                }
                memberSmsRechargeApi.updateSmsRecharge(smsRechargeDto);
            }
            if(resultList.get(0).getStatus() == ChinaUmsBillStatus.PAID){
                return new ResponseEntity(ResultVO.createSuccess(true), HttpStatus.OK);
            }else{
                return new ResponseEntity(ResultVO.createSuccess(false), HttpStatus.OK);
            }
        }else{
            return new ResponseEntity(ResultVO.createSuccess(false), HttpStatus.OK);
        }
    }


    /**
     * 处理订单没给返回消息
     * 查询未支付订单
     * 主动请求支付服务查询结果更新状态
     *
     * @return
     */
    @ApiOperation(value = "定时任务接口", notes = "定时任务接口", response = String.class, tags = {"memberSmsRecharge",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = String.class)})
    @RequestMapping(value = "/timingTasks", method = RequestMethod.POST)
    ResponseEntity<ResultVO> timingTasks() {
        List<MemberSmsRechargeDto> retList = memberSmsRechargeApi.getSmsRechargeList(null);
        for (MemberSmsRechargeDto rechargeDto : retList) {
            //订单状态为 未支付 状态再次请求支付结果
            if (ChinaUmsBillStatus.UNPAID == rechargeDto.getPayStatus().intValue()) {
                String orderId = rechargeDto.getOrderId();
                String organSign = rechargeDto.getOrganSign();
                List<PayInfoDto> list = new ArrayList<>();
                PayInfoDto payDto = new PayInfoDto();
                payDto.setOrderNo(rechargeDto.getOrderId());
                payDto.setOrganSign(rechargeDto.getOrganSign());
                list.add(payDto);
                List<PayInfoDto> resultList;
                if(!paymentChangeGrayConfig.payChannleChange(organSign)){
                    resultList = payInfoApi.getChinaUmsC2BPayStatus(list);
                }else{
                    resultList = payInfoApi.getPaymentPayStatus(payDto);
                }
                if (Objects.isNull(resultList) || resultList.size() < 1) {
                    logger.info("[SMS] 支付接口查询订单为空, orderId: [" + orderId + "], organSign: [" + organSign + "]");
                    continue;
                }
                logger.info("支付服务查询结果: " + JSONObject.toJSONString(list.get(0)));
                int payStatus = resultList.get(0).getStatus();
                // 如果查询的订单状态非未支付，更新本地订单表
                if (payStatus != ChinaUmsBillStatus.UNPAID) {
                    rechargeDto = new MemberSmsRechargeDto();
                    rechargeDto.setOrderId(orderId);
                    rechargeDto.setOrganSign(organSign);
                    rechargeDto.setPayStatus((byte) payStatus);
                    //订单支付成功，更新实付金额
                    if (payStatus == ChinaUmsBillStatus.PAID) {
                        BigDecimal actualAmount = resultList.get(0).getTotalAmount().divide(new BigDecimal(100));
                        rechargeDto.setActualAmount(actualAmount);
                    }
                    memberSmsRechargeApi.updateSmsRecharge(rechargeDto);
                }
            }
        }
        return new ResponseEntity(ResultVO.createSuccess(true), HttpStatus.OK);
    }

}
