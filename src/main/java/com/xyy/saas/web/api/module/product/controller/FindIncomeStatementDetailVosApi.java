/**
 * NOTE: This class is auto generated by the swagger code generator program (2.2.3).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.web.api.module.product.model.InventoryIncomeStatementDetailVo;
import com.xyy.saas.web.api.module.product.model.InventoryIncomeStatementDetailVoList;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-30T11:21:29.525+08:00")
@RequestMapping("/product")
@Api(value = "findIncomeStatementDetailVos", description = "the findIncomeStatementDetailVos API")
public interface FindIncomeStatementDetailVosApi {

    @ApiOperation(value = "查询所有报损报溢明细", notes = "查询所有报损报溢明细", response = InventoryIncomeStatementDetailVoList.class, tags={ "InventoryIncomeStatementDetailVo", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "操作成功", response = InventoryIncomeStatementDetailVoList.class) })
    
    @RequestMapping(value = "/findIncomeStatementDetailVos",
        produces = { "application/xml", "application/json" }, 
        consumes = { "application/json", "application/xml" },
        method = RequestMethod.POST)
    ResponseEntity<InventoryIncomeStatementDetailVoList> findIncomeStatementDetailVos(@ApiParam(value = "报损报溢明细Vo", required = true) @Valid @RequestBody InventoryIncomeStatementDetailVo inventoryIncomeStatementDetailVo);

}
