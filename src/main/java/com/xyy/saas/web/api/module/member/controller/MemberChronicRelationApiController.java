package com.xyy.saas.web.api.module.member.controller;

import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @Description 会员慢病关联API
 * <AUTHOR>
 * @Create 2020-09-24 16:15
 * @menu 会员慢病关联API
 */
@Controller
@RequestMapping(value = "/member/memberChronicRelation")
@Api(value = "memberChronicRelation", description = "会员慢病关联API")
public class MemberChronicRelationApiController {
    private static final Logger logger = LoggerFactory.getLogger(MemberChronicRelationApiController.class);

//    @Reference(version = "0.0.1")
//    private MemberChronicRelationApi memberChronicRelationApi;

//    /**
//     * @param organSign 机构信息
//     * @param memberChronicRelationVo 实体VO信息
//     */
//    @ApiOperation(value = "新增或编辑会员慢病关联", notes = "新增或编辑会员慢病关联", response = Boolean.class, tags={ "会员慢病关联", })
//    @ApiResponses(value = {
//            @ApiResponse(code = 200, message = "操作成功", response = Boolean.class) })
//    @RequestMapping(value = "/saveOrUpdateChronicRelation",
//            method = RequestMethod.POST)
//    ResponseEntity<ResultVO> saveOrUpdateChronicRelation(@RequestHeader(name = "organSign", required = true) String organSign,
//                                               @RequestBody MemberChronicRelationVo memberChronicRelationVo) {
//        //组装Dto参数
//        MemberChronicRelationDto memberChronicRelationDto = new MemberChronicRelationDto();
//        BeanUtils.copyProperties(memberChronicRelationVo, memberChronicRelationDto);
//        memberChronicRelationDto.setOrganSign(organSign);
//        return new ResponseEntity(ResultVO.createSuccess(
//                memberChronicRelationApi.saveOrUpdateChronicRelation(memberChronicRelationDto)),
//                HttpStatus.OK);
//    }


    //<editor-folder desc="Yapi接口文档">
//    /**
//     * @param organSign 机构信息
//     * @param memberChronicRelationVo 实体VO信息
//     */
//    @ApiOperation(value = "新增或编辑会员慢病关联", notes = "新增或编辑会员慢病关联", response = Boolean.class, tags={ "会员慢病关联", })
//    @ApiResponses(value = {
//            @ApiResponse(code = 200, message = "操作成功", response = Boolean.class) })
//    @RequestMapping(value = "/saveOrUpdateChronicRelation",
//            method = RequestMethod.POST)
//    ResultVO<Boolean> saveOrUpdateChronicRelation(@RequestHeader(name = "organSign", required = true) String organSign,
//                                                         @RequestBody MemberChronicRelationVo memberChronicRelationVo) {
//        return null;
//    }
    //</editor-folder>
}
