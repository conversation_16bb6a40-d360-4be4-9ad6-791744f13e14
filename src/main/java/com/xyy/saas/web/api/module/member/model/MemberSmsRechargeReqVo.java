package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(description = "会员短信充值")
public class MemberSmsRechargeReqVo  implements Serializable {

    private static final long serialVersionUID = -6909463946068181030L;

    /**
     * 购买服务id
     */
    @ApiModelProperty(value = "充值套餐id", required = true, example = "1")
    private Long smsPackageId;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", required = true)
    private String createUser;


    /**
     * 1.聚合支付 2 支付宝 3 微信
     */
    private int payType = 3 ;


    public Long getSmsPackageId() {
        return smsPackageId;
    }

    public void setSmsPackageId(Long smsPackageId) {
        this.smsPackageId = smsPackageId;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    @Override
    public String toString() {
        return "MemberSmsRechargeVo{" +
                "smsPackageId=" + smsPackageId +
                ", createUser='" + createUser + '\'' +
                '}';
    }

    public int getPayType() {
        return payType;
    }

    public void setPayType(int payType) {
        this.payType = payType;
    }
}
