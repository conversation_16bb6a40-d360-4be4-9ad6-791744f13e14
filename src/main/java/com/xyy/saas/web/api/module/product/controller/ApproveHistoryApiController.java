package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.dto.ApproveHistoryDto;
import com.xyy.saas.web.api.module.product.model.ApproveHistoryVo;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T19:34:49.364+08:00")

@Controller
public class ApproveHistoryApiController implements ApproveHistoryApi {

    @Reference(version = "0.0.1")
    private com.xyy.saas.product.core.api.ApproveHistoryApi approveHistoryApi;

    public ResponseEntity<ResultVO> batchUpdatePro(@ApiParam(value = "商品审核列表实体以及查询条件实体" ,required=true )  @Valid @RequestBody ApproveHistoryVo approveHistoryVo) {
        ApproveHistoryDto approveHistoryDto = new ApproveHistoryDto();
        BeanUtils.copyProperties(approveHistoryVo, approveHistoryDto);
        ResultVO result = approveHistoryApi.batchUpdatePro(approveHistoryDto);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    public ResponseEntity<ResultVO> proList(@ApiParam(value = "商品审核列表实体以及查询条件实体" ,required=true )  @Valid @RequestBody ApproveHistoryVo approveHistoryVo) {
        ApproveHistoryDto approveHistoryDto = new ApproveHistoryDto();
        BeanUtils.copyProperties(approveHistoryVo, approveHistoryDto);
        ResultVO result = approveHistoryApi.proList(approveHistoryDto);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    public ResponseEntity<ResultVO> updatePro(@ApiParam(value = "商品审核列表实体以及查询条件实体" ,required=true )  @Valid @RequestBody ApproveHistoryVo approveHistoryVo) {
        ApproveHistoryDto approveHistoryDto = new ApproveHistoryDto();
        BeanUtils.copyProperties(approveHistoryVo, approveHistoryDto);
        ResultVO result = approveHistoryApi.updatePro(approveHistoryDto);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

}
