package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 会员储值设置保存Vo
 * <AUTHOR>
 */
@ApiModel(description = "会员储值设置保存")
public class MemberPrepayCardConfigSaveVo implements Serializable {

    private static final long serialVersionUID = -6102285842882436236L;
    /**
     * 新增或者修改的会员储值设置列表
     */
    @ApiModelProperty(value = "新增或者修改的会员储值设置列表")
    private List<MemberPrepayCardConfigVo> list;

    /**
     * 需要删除的会员储值设置id列表
     */
    @ApiModelProperty(value = "需要删除的会员储值设置id列表")
    private List<Long> deleteIds;

    /**
     * 需要同步的会员级别id列表
     */
    @ApiModelProperty(value = "需要同步的会员级别id列表")
    private List<Long> levelIds;

    public List<MemberPrepayCardConfigVo> getList() {
        return list;
    }

    public void setList(List<MemberPrepayCardConfigVo> list) {
        this.list = list;
    }

    public List<Long> getDeleteIds() {
        return deleteIds;
    }

    public void setDeleteIds(List<Long> deleteIds) {
        this.deleteIds = deleteIds;
    }

    public List<Long> getLevelIds() {
        return levelIds;
    }

    public void setLevelIds(List<Long> levelIds) {
        this.levelIds = levelIds;
    }

    @Override
    public String toString() {
        return "MemberPrepayCardConfigSaveDto{" +
                "list=" + list +
                ", deleteIds=" + deleteIds +
                ", levelIds=" + levelIds +
                '}';
    }
}