package com.xyy.saas.web.api.module.member.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 壹块钱会员Vo
 *
 * <AUTHOR>
 */
@ApiModel(description = "壹块钱会员Vo")
public class MemberOneMoneyVo implements Serializable {

    private static final long serialVersionUID = -4423856428279111690L;

    /**
     * 会员名称
     */
    @JsonProperty("name")
    private String name;

    /**
     * 性别 1 男 2 女
     */
    @JsonProperty("sex")
    private Integer sex;

    /**
     * 会员等级id
     */
    @JsonProperty("vipLevelId")
    private Long vipLevelId;

    /**
     * 手机号
     */
    @JsonProperty("telephone")
    private String telephone;

    /**
     * 机构号
     */
    @JsonProperty("organSign")
    private String organSign;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getSex() {
        return sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public Long getVipLevelId() {
        return vipLevelId;
    }

    public void setVipLevelId(Long vipLevelId) {
        this.vipLevelId = vipLevelId;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    @ApiModelProperty(value = "药店机构唯一标识（机构号）")
    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }
}
