package com.xyy.saas.web.api.common.authorization;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.web.api.common.context.ActionContext;

/**
 * 授权-定义权限校验骨架接口
 * <AUTHOR>
 * @date 2019-08-08
 * @mondify
 * @copyright
 */
public interface IClientAuthorization {
    /**
     *
     * @param context
     * @return
     */
    ResultVO clientAuth(ActionContext context);

    /**
     * 具体执行权限校验
     * @param context
     * @return
     */
    ResultVO doClientAuth(ActionContext context);
}
