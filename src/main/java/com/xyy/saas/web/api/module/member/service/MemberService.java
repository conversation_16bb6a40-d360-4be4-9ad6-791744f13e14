package com.xyy.saas.web.api.module.member.service;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.member.core.dto.MemberChangePointDto;
import com.xyy.saas.member.core.dto.MemberPointHistoryConditionDto;

/**
 * <AUTHOR>
 */
public interface MemberService {

    /**
     * c端 修改积分接口
     * @param changePointDto
     */
    void editMemberPoint(MemberChangePointDto changePointDto);


    /**
     * c端查询 积分变动记录接口
     * @param dto
     * @return
     */
    PageInfo<MemberPointHistoryConditionDto> getMemberPointHistoryPager(MemberPointHistoryConditionDto dto);

}
