package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.member.core.api.MemberSmsRecordApi;
import com.xyy.saas.member.core.dto.MemberShareDto;
import com.xyy.saas.member.core.dto.MemberSmsRecordNewDto;
import com.xyy.saas.member.core.dto.MemberSmsRecordNewParamDto;
import com.xyy.saas.web.api.module.utils.DateUtil;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-08-29T21:37:06.377+08:00")

@Slf4j
@Controller
@RequestMapping("/member/memberSmsSendHistory/zb")
@Api(value = "memberSmsSendHistory", description = "the memberSmsSendHistory API")
public class MemberSmsSendHistoryForZBApiController {

    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;

    @Reference(version = "0.0.1")
    private MemberSmsRecordApi memberSmsRecordApi;

    @ApiOperation(value = "分页获取短信发送记录列表", notes = "分页获取短信发送记录列表", response = MemberShareDto.class, tags = {"memberSmsSendHistory",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = PageInfo.class)})
    @RequestMapping(value = "/queryList", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> queryList(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                       @RequestBody MemberSmsRecordNewParamDto paramDto) {
        try {
            CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
            String organSign = commonRequestModel.getOrganSign();

            List<String> organSignList = new ArrayList<>();
            organSignList.add(organSign);
            if(commonRequestModel.getOrganSignType() == 3) {
                List<SaaSDrugstoreDto> drugstoreList = drugstoreApi.getDrugstoreByHeadquartersOrganSign(organSign);
                List<String> storeOrganSigns = drugstoreList.stream().map(v -> v.getOrganSign()).collect(Collectors.toList());
                organSignList.addAll(storeOrganSigns);
            }
            paramDto.setOrganSignList(organSignList);
            if (paramDto.getStartTime() != null) {
                paramDto.setStartTime(DateUtil.getDayBeginTime(paramDto.getStartTime()));
            }
            if (paramDto.getEndTime() != null) {
                paramDto.setEndTime(DateUtil.getDayEndTime(paramDto.getEndTime()));
            }
            PageInfo<MemberSmsRecordNewDto> smsRecordListNew = memberSmsRecordApi.getMemberSmsRecordListNew(paramDto);

            return new ResponseEntity(ResultVO.createSuccess(smsRecordListNew), HttpStatus.OK);
        } catch (Exception e) {
            log.error("MemberSmsSendHistoryForZBApiController#queryList error, param:{}", JSON.toJSONString(paramDto), e);
            return new ResponseEntity(ResultVO.createError(e.getMessage()), HttpStatus.OK);
        }
    }
}
