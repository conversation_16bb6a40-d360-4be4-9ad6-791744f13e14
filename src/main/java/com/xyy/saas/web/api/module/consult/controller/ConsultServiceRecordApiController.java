package com.xyy.saas.web.api.module.consult.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.util.DateUtil;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.consult.cores.api.ConsultServiceRecordApi;
import com.xyy.saas.consult.cores.dto.ConsultServiceRecordDto;
import com.xyy.saas.consult.cores.dto.ConsultServiceRecordInactiveAccountDto;
import com.xyy.saas.web.api.module.consult.model.ConsultCommitmentLetterVo;
import com.xyy.saas.web.api.module.utils.ExportExcelUtil;
import com.xyy.saas.web.api.module.utils.FileUtils;
import com.xyy.saas.web.api.module.utils.MailUtil;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.dto.result.QueryDrugstoreDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/member/consultServiceRecord")
@Api(value = "consultServiceRecord", description = "药店远程问诊服务记录API")
public class ConsultServiceRecordApiController {

    @Value("${consult.commitmentLetter.addressTo}")
    private String commitmentLetteraddressTo;

	private static final Logger logger = LogManager.getLogger(ConsultServiceRecordApiController.class);
    @Reference(version = "0.0.2")
    private ConsultServiceRecordApi consultServiceRecordApi;

    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;


    @ApiOperation(value = "根据机构标识查询药店远程问诊服务记录", notes = "根据机构标识查询药店远程问诊服务记录", response = ConsultServiceRecordDto.class, tags = {"consultServiceRecord",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ConsultServiceRecordDto.class)})
    @RequestMapping(value = "/selectByOrganSign", method = RequestMethod.POST,produces =  "application/json")
    ResponseEntity<ResultVO> selectByOrganSign(@RequestHeader(name = "organSign", required = true) String organSign) {
        return new ResponseEntity(ResultVO.createSuccess(consultServiceRecordApi.selectByOrganSign(organSign)), HttpStatus.OK);
    }

    @ApiOperation(value = "查询购买包年/包季服务的记录列表", notes = "查询购买包年/包季服务的记录列表", response = ConsultServiceRecordDto.class, tags = {"consultServiceRecord",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ConsultServiceRecordDto.class)})
    @RequestMapping(value = "/selectPeriodAccountList", method = {RequestMethod.POST, RequestMethod.GET},produces =  "application/json")
    ResponseEntity<ResultVO> selectPeriodAccountList(@RequestParam(required = false,  defaultValue = "false") Boolean debug) {
        logger.info("发送开通包年包季服务机构明细");
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.DATE_FORMAT_YYYYMMDD);
            SimpleDateFormat sdf2 = new SimpleDateFormat(DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS);
            List<ConsultServiceRecordInactiveAccountDto> list = consultServiceRecordApi.selectPeriodAccountList();
            String[] header = new String[]{"鲮鲤机构ID", "药店名称", "所在省", "所在市", "所在区/县", "详细地址", "付费类型", "有效期", "付款金额", "付款时间"};
            String title = "开通包年包季服务机构明细";
            List<String[]> dataList = new ArrayList<>();
            list.forEach(item -> {
                String[] row = new String[]{
                        item.getOrganSign(),
                        item.getDrugstoreName(),
                        item.getProvince(),
                        item.getCity(),
                        item.getArea(),
                        item.getAddress(),
                        item.getNeedAudit() == 1 ? "远程问诊+远程审方" : "远程问诊",
                        item.getServiceItemName(),
                        item.getActualAmount().toString(),
                        sdf2.format(item.getPayTime()),
                };
                dataList.add(row);
            });
            // 创建excel并写入文件
            XSSFWorkbook excel = ExportExcelUtil.createExcelOfUsers(header, title + sdf.format(new Date()), title, dataList, null);
            File file = File.createTempFile(title, ".xls");
            FileOutputStream out = new FileOutputStream(file);
            excel.write(out);
            // 发送邮件
            String addressTo = null;
            if (debug) {
                addressTo = "<EMAIL>";
            } else {
                addressTo = "<EMAIL>,<EMAIL>";
            }
            MailUtil.sendMail(title, "Hi:\n截至今日为止的" + title + "，请查收。", addressTo, "", "", file, title + sdf.format(new Date()) + ".xls");

            return new ResponseEntity(ResultVO.createSuccess(true), HttpStatus.OK);
        } catch (Exception e) {
            logger.error("发送开通包年包季服务机构明细异常", e);
            return new ResponseEntity(ResultVO.createSuccess(false), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "上传承诺函V2，接收文件地址", notes = "上传承诺函V2，接收文件地址", response = Boolean.class, tags = {"consultServiceRecord",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/uploadCommitmentLetterV2", method = {RequestMethod.POST},produces =  "application/json")
    ResponseEntity<ResultVO> uploadCommitmentLetterV2(@RequestHeader(name = "organSign", required = true) String organSign,
                                                      @RequestBody ConsultCommitmentLetterVo vo) {
        try {
            String fileUrl = vo.getFile();
            if (StringUtils.isEmpty(fileUrl)) {
                return new ResponseEntity(new ResultVO(-1, "上传文件不能为空", false), HttpStatus.OK);
            }
            String extendName = Optional.ofNullable(fileUrl.substring(fileUrl.lastIndexOf("."))).orElse("");
            ConsultServiceRecordDto dto = new ConsultServiceRecordDto();
            dto.setOrganSign(organSign);
            dto.setCommitmentLetter(fileUrl);
            boolean isOk = consultServiceRecordApi.uploadCommitmentLetter(dto);
            // 发送邮件
            if (isOk) {
                QueryDrugstoreDto drugstoreDto = drugstoreApi.queryDrugstoreByOrganSign(organSign).getResult();
                String title = drugstoreDto.getDrugstoreName() + "-承诺函";
                StringBuffer content = new StringBuffer();
                content.append("Dear all:<br/>")
                        .append("  药店名称: ").append(drugstoreDto.getDrugstoreName()).append("<br/>")
                        .append("  所属省份: ").append(drugstoreDto.getProvince()).append("<br/>")
                        .append("  申请状态: 申请中<br/>")
                        .append("  请查收。");

                MailUtil.sendMail(title, content.toString(), commitmentLetteraddressTo, "", "", FileUtils.downloadFromUrl(fileUrl), title + extendName);
                return new ResponseEntity(ResultVO.createSuccess(isOk), HttpStatus.OK);
            }
            return new ResponseEntity(new ResultVO(-1, "上传承诺函失败", false), HttpStatus.OK);
        } catch (Exception e) {
            logger.error("上传承诺函异常", e);
            return new ResponseEntity(new ResultVO(-2, "上传承诺函异常", false), HttpStatus.OK);
        }
    }

}
