package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * InventoryVo
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T18:13:39.358+08:00")

public class InventoryVo   {
  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("pref")
  private String pref = null;

  @JsonProperty("productPref")
  private String productPref = null;

  @JsonProperty("stockNumber")
  private BigDecimal stockNumber = null;

  @JsonProperty("stockAmount")
  private BigDecimal stockAmount = null;

  @JsonProperty("costPrice")
  private BigDecimal costPrice = null;

  @JsonProperty("lastCostPrice")
  private BigDecimal lastCostPrice = null;

  @JsonProperty("lastProvidePref")
  private String lastProvidePref = null;

  @JsonProperty("lastInTime")
  private Date lastInTime = null;

  @JsonProperty("createUser")
  private String createUser = null;

  @JsonProperty("createTime")
  private Date createTime = null;

  @JsonProperty("updateUser")
  private String updateUser = null;

  @JsonProperty("updateTime")
  private Date updateTime = null;

  @JsonProperty("remark")
  private String remark = null;

  @JsonProperty("version")
  private Integer version = null;

  @JsonProperty("yn")
  private byte yn = 1;

  @JsonProperty("baseVersion")
  private String baseVersion = null;

  @JsonProperty("organsign")
  private String organsign = null;

  @JsonProperty("productName")
  private String productName = null;

  @JsonProperty("attributeSpecification")
  private String attributeSpecification = null;

  @JsonProperty("packgeUnit")
  private String packgeUnit = null;

  @JsonProperty("manufacturer")
  private String manufacturer = null;

  @JsonProperty("positionname")
  private String positionname = null;

  @JsonProperty("positionid")
  private Integer positionid = null;

  @JsonProperty("excludeIds")
  private String excludeIds = null;

  @JsonProperty("startTime")
  private String startTime = null;

  @JsonProperty("commonName")
  private String commonName = null;

  @JsonProperty("referenceNmb")
  private String referenceNmb = null;

  @JsonProperty("parea")
  private String parea = null;

  @JsonProperty("productValue")
  private String productValue = null;

  @JsonProperty("providerName")
  private String providerName = null;

  @JsonProperty("unitName")
  private String unitName = null;

  public InventoryVo id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * 主键id
   * @return id
  **/
  @ApiModelProperty(value = "主键id")


  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public InventoryVo pref(String pref) {
    this.pref = pref;
    return this;
  }

   /**
   * 编号
   * @return pref
  **/
  @ApiModelProperty(value = "编号")


  public String getPref() {
    return pref;
  }

  public void setPref(String pref) {
    this.pref = pref;
  }

  public InventoryVo productPref(String productPref) {
    this.productPref = productPref;
    return this;
  }

   /**
   * 商品编号
   * @return productPref
  **/
  @ApiModelProperty(value = "商品编号")


  public String getProductPref() {
    return productPref;
  }

  public void setProductPref(String productPref) {
    this.productPref = productPref;
  }

  public InventoryVo stockNumber(BigDecimal stockNumber) {
    this.stockNumber = stockNumber;
    return this;
  }

   /**
   * 库存数量
   * @return stockNumber
  **/
  @ApiModelProperty(value = "库存数量")

  @Valid

  public BigDecimal getStockNumber() {
    return stockNumber;
  }

  public void setStockNumber(BigDecimal stockNumber) {
    this.stockNumber = stockNumber;
  }

  public InventoryVo stockAmount(BigDecimal stockAmount) {
    this.stockAmount = stockAmount;
    return this;
  }

   /**
   * 库存金额
   * @return stockAmount
  **/
  @ApiModelProperty(value = "库存金额")

  @Valid

  public BigDecimal getStockAmount() {
    return stockAmount;
  }

  public void setStockAmount(BigDecimal stockAmount) {
    this.stockAmount = stockAmount;
  }

  public InventoryVo costPrice(BigDecimal costPrice) {
    this.costPrice = costPrice;
    return this;
  }

   /**
   * 成本价
   * @return costPrice
  **/
  @ApiModelProperty(value = "成本价")

  @Valid

  public BigDecimal getCostPrice() {
    return costPrice;
  }

  public void setCostPrice(BigDecimal costPrice) {
    this.costPrice = costPrice;
  }

  public InventoryVo lastCostPrice(BigDecimal lastCostPrice) {
    this.lastCostPrice = lastCostPrice;
    return this;
  }

   /**
   * 最后一次成本价
   * @return lastCostPrice
  **/
  @ApiModelProperty(value = "最后一次成本价")

  @Valid

  public BigDecimal getLastCostPrice() {
    return lastCostPrice;
  }

  public void setLastCostPrice(BigDecimal lastCostPrice) {
    this.lastCostPrice = lastCostPrice;
  }

  public InventoryVo lastProvidePref(String lastProvidePref) {
    this.lastProvidePref = lastProvidePref;
    return this;
  }

   /**
   * 最后供应商
   * @return lastProvidePref
  **/
  @ApiModelProperty(value = "最后供应商")


  public String getLastProvidePref() {
    return lastProvidePref;
  }

  public void setLastProvidePref(String lastProvidePref) {
    this.lastProvidePref = lastProvidePref;
  }

  public InventoryVo lastInTime(Date lastInTime) {
    this.lastInTime = lastInTime;
    return this;
  }

   /**
   * 最后一次入库时间
   * @return lastInTime
  **/
  @ApiModelProperty(value = "最后一次入库时间")

  @Valid

  public Date getLastInTime() {
    return lastInTime;
  }

  public void setLastInTime(Date lastInTime) {
    this.lastInTime = lastInTime;
  }

  public InventoryVo createUser(String createUser) {
    this.createUser = createUser;
    return this;
  }

   /**
   * 创建人
   * @return createUser
  **/
  @ApiModelProperty(value = "创建人")


  public String getCreateUser() {
    return createUser;
  }

  public void setCreateUser(String createUser) {
    this.createUser = createUser;
  }

  public InventoryVo createTime(Date createTime) {
    this.createTime = createTime;
    return this;
  }

   /**
   * 创建时间
   * @return createTime
  **/
  @ApiModelProperty(value = "创建时间")

  @Valid

  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public InventoryVo updateUser(String updateUser) {
    this.updateUser = updateUser;
    return this;
  }

   /**
   * 更新人
   * @return updateUser
  **/
  @ApiModelProperty(value = "更新人")


  public String getUpdateUser() {
    return updateUser;
  }

  public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
  }

  public InventoryVo updateTime(Date updateTime) {
    this.updateTime = updateTime;
    return this;
  }

   /**
   * 更新时间
   * @return updateTime
  **/
  @ApiModelProperty(value = "更新时间")

  @Valid

  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public InventoryVo remark(String remark) {
    this.remark = remark;
    return this;
  }

   /**
   * 更新人
   * @return remark
  **/
  @ApiModelProperty(value = "更新人")


  public String getRemark() {
    return remark;
  }

  public void setRemark(String remark) {
    this.remark = remark;
  }

  public InventoryVo version(Integer version) {
    this.version = version;
    return this;
  }

   /**
   * 更新人
   * @return version
  **/
  @ApiModelProperty(value = "更新人")


  public Integer getVersion() {
    return version;
  }

  public void setVersion(Integer version) {
    this.version = version;
  }

  public InventoryVo yn(byte yn) {
    this.yn = yn;
    return this;
  }

   /**
   * 更新人
   * @return yn
  **/
  @ApiModelProperty(value = "更新人")

  public byte getYn() {
    return yn;
  }

  public void setYn(byte yn) {
    this.yn = yn;
  }

  public InventoryVo baseVersion(String baseVersion) {
    this.baseVersion = baseVersion;
    return this;
  }

   /**
   * 版本号
   * @return baseVersion
  **/
  @ApiModelProperty(value = "版本号")


  public String getBaseVersion() {
    return baseVersion;
  }

  public void setBaseVersion(String baseVersion) {
    this.baseVersion = baseVersion;
  }

  public InventoryVo organsign(String organsign) {
    this.organsign = organsign;
    return this;
  }

   /**
   * 机构代码
   * @return organsign
  **/
  @ApiModelProperty(value = "机构代码")


  public String getOrgansign() {
    return organsign;
  }

  public void setOrgansign(String organsign) {
    this.organsign = organsign;
  }

  public InventoryVo productName(String productName) {
    this.productName = productName;
    return this;
  }

   /**
   * 商品名称
   * @return productName
  **/
  @ApiModelProperty(value = "商品名称")


  public String getProductName() {
    return productName;
  }

  public void setProductName(String productName) {
    this.productName = productName;
  }

  public InventoryVo attributeSpecification(String attributeSpecification) {
    this.attributeSpecification = attributeSpecification;
    return this;
  }

   /**
   * 商品规格
   * @return attributeSpecification
  **/
  @ApiModelProperty(value = "商品规格")


  public String getAttributeSpecification() {
    return attributeSpecification;
  }

  public void setAttributeSpecification(String attributeSpecification) {
    this.attributeSpecification = attributeSpecification;
  }

  public InventoryVo packgeUnit(String packgeUnit) {
    this.packgeUnit = packgeUnit;
    return this;
  }

   /**
   * 包装单位
   * @return packgeUnit
  **/
  @ApiModelProperty(value = "包装单位")


  public String getPackgeUnit() {
    return packgeUnit;
  }

  public void setPackgeUnit(String packgeUnit) {
    this.packgeUnit = packgeUnit;
  }

  public InventoryVo manufacturer(String manufacturer) {
    this.manufacturer = manufacturer;
    return this;
  }

   /**
   * 生产厂家
   * @return manufacturer
  **/
  @ApiModelProperty(value = "生产厂家")


  public String getManufacturer() {
    return manufacturer;
  }

  public void setManufacturer(String manufacturer) {
    this.manufacturer = manufacturer;
  }

  public InventoryVo positionname(String positionname) {
    this.positionname = positionname;
    return this;
  }

   /**
   * 架位名称
   * @return positionname
  **/
  @ApiModelProperty(value = "架位名称")


  public String getPositionname() {
    return positionname;
  }

  public void setPositionname(String positionname) {
    this.positionname = positionname;
  }

  public InventoryVo positionid(Integer positionid) {
    this.positionid = positionid;
    return this;
  }

   /**
   * 架位ID
   * @return positionid
  **/
  @ApiModelProperty(value = "架位ID")


  public Integer getPositionid() {
    return positionid;
  }

  public void setPositionid(Integer positionid) {
    this.positionid = positionid;
  }

  public InventoryVo excludeIds(String excludeIds) {
    this.excludeIds = excludeIds;
    return this;
  }

   /**
   * 排除的ids
   * @return excludeIds
  **/
  @ApiModelProperty(value = "排除的ids")


  public String getExcludeIds() {
    return excludeIds;
  }

  public void setExcludeIds(String excludeIds) {
    this.excludeIds = excludeIds;
  }

  public InventoryVo startTime(String startTime) {
    this.startTime = startTime;
    return this;
  }

   /**
   * 页面开始时间
   * @return startTime
  **/
  @ApiModelProperty(value = "页面开始时间")


  public String getStartTime() {
    return startTime;
  }

  public void setStartTime(String startTime) {
    this.startTime = startTime;
  }

  public InventoryVo commonName(String commonName) {
    this.commonName = commonName;
    return this;
  }

   /**
   * 商品通用名
   * @return commonName
  **/
  @ApiModelProperty(value = "商品通用名")


  public String getCommonName() {
    return commonName;
  }

  public void setCommonName(String commonName) {
    this.commonName = commonName;
  }

  public InventoryVo referenceNmb(String referenceNmb) {
    this.referenceNmb = referenceNmb;
    return this;
  }

   /**
   * 批准文号
   * @return referenceNmb
  **/
  @ApiModelProperty(value = "批准文号")


  public String getReferenceNmb() {
    return referenceNmb;
  }

  public void setReferenceNmb(String referenceNmb) {
    this.referenceNmb = referenceNmb;
  }

  public InventoryVo parea(String parea) {
    this.parea = parea;
    return this;
  }

   /**
   * 产地
   * @return parea
  **/
  @ApiModelProperty(value = "产地")


  public String getParea() {
    return parea;
  }

  public void setParea(String parea) {
    this.parea = parea;
  }

  public InventoryVo productValue(String productValue) {
    this.productValue = productValue;
    return this;
  }

   /**
   * 不合格登记商品查询
   * @return productValue
  **/
  @ApiModelProperty(value = "不合格登记商品查询")


  public String getProductValue() {
    return productValue;
  }

  public void setProductValue(String productValue) {
    this.productValue = productValue;
  }

  public InventoryVo providerName(String providerName) {
    this.providerName = providerName;
    return this;
  }

   /**
   * 供应商名称
   * @return providerName
  **/
  @ApiModelProperty(value = "供应商名称")


  public String getProviderName() {
    return providerName;
  }

  public void setProviderName(String providerName) {
    this.providerName = providerName;
  }

  public InventoryVo unitName(String unitName) {
    this.unitName = unitName;
    return this;
  }

   /**
   * 商品单位名称
   * @return unitName
  **/
  @ApiModelProperty(value = "商品单位名称")


  public String getUnitName() {
    return unitName;
  }

  public void setUnitName(String unitName) {
    this.unitName = unitName;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InventoryVo inventoryVo = (InventoryVo) o;
    return Objects.equals(this.id, inventoryVo.id) &&
        Objects.equals(this.pref, inventoryVo.pref) &&
        Objects.equals(this.productPref, inventoryVo.productPref) &&
        Objects.equals(this.stockNumber, inventoryVo.stockNumber) &&
        Objects.equals(this.stockAmount, inventoryVo.stockAmount) &&
        Objects.equals(this.costPrice, inventoryVo.costPrice) &&
        Objects.equals(this.lastCostPrice, inventoryVo.lastCostPrice) &&
        Objects.equals(this.lastProvidePref, inventoryVo.lastProvidePref) &&
        Objects.equals(this.lastInTime, inventoryVo.lastInTime) &&
        Objects.equals(this.createUser, inventoryVo.createUser) &&
        Objects.equals(this.createTime, inventoryVo.createTime) &&
        Objects.equals(this.updateUser, inventoryVo.updateUser) &&
        Objects.equals(this.updateTime, inventoryVo.updateTime) &&
        Objects.equals(this.remark, inventoryVo.remark) &&
        Objects.equals(this.version, inventoryVo.version) &&
        Objects.equals(this.yn, inventoryVo.yn) &&
        Objects.equals(this.baseVersion, inventoryVo.baseVersion) &&
        Objects.equals(this.organsign, inventoryVo.organsign) &&
        Objects.equals(this.productName, inventoryVo.productName) &&
        Objects.equals(this.attributeSpecification, inventoryVo.attributeSpecification) &&
        Objects.equals(this.packgeUnit, inventoryVo.packgeUnit) &&
        Objects.equals(this.manufacturer, inventoryVo.manufacturer) &&
        Objects.equals(this.positionname, inventoryVo.positionname) &&
        Objects.equals(this.positionid, inventoryVo.positionid) &&
        Objects.equals(this.excludeIds, inventoryVo.excludeIds) &&
        Objects.equals(this.startTime, inventoryVo.startTime) &&
        Objects.equals(this.commonName, inventoryVo.commonName) &&
        Objects.equals(this.referenceNmb, inventoryVo.referenceNmb) &&
        Objects.equals(this.parea, inventoryVo.parea) &&
        Objects.equals(this.productValue, inventoryVo.productValue) &&
        Objects.equals(this.providerName, inventoryVo.providerName) &&
        Objects.equals(this.unitName, inventoryVo.unitName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, pref, productPref, stockNumber, stockAmount, costPrice, lastCostPrice, lastProvidePref, lastInTime, createUser, createTime, updateUser, updateTime, remark, version, yn, baseVersion, organsign, productName, attributeSpecification, packgeUnit, manufacturer, positionname, positionid, excludeIds, startTime, commonName, referenceNmb, parea, productValue, providerName, unitName);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InventoryVo {\n");

    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    pref: ").append(toIndentedString(pref)).append("\n");
    sb.append("    productPref: ").append(toIndentedString(productPref)).append("\n");
    sb.append("    stockNumber: ").append(toIndentedString(stockNumber)).append("\n");
    sb.append("    stockAmount: ").append(toIndentedString(stockAmount)).append("\n");
    sb.append("    costPrice: ").append(toIndentedString(costPrice)).append("\n");
    sb.append("    lastCostPrice: ").append(toIndentedString(lastCostPrice)).append("\n");
    sb.append("    lastProvidePref: ").append(toIndentedString(lastProvidePref)).append("\n");
    sb.append("    lastInTime: ").append(toIndentedString(lastInTime)).append("\n");
    sb.append("    createUser: ").append(toIndentedString(createUser)).append("\n");
    sb.append("    createTime: ").append(toIndentedString(createTime)).append("\n");
    sb.append("    updateUser: ").append(toIndentedString(updateUser)).append("\n");
    sb.append("    updateTime: ").append(toIndentedString(updateTime)).append("\n");
    sb.append("    remark: ").append(toIndentedString(remark)).append("\n");
    sb.append("    version: ").append(toIndentedString(version)).append("\n");
    sb.append("    yn: ").append(toIndentedString(yn)).append("\n");
    sb.append("    baseVersion: ").append(toIndentedString(baseVersion)).append("\n");
    sb.append("    organsign: ").append(toIndentedString(organsign)).append("\n");
    sb.append("    productName: ").append(toIndentedString(productName)).append("\n");
    sb.append("    attributeSpecification: ").append(toIndentedString(attributeSpecification)).append("\n");
    sb.append("    packgeUnit: ").append(toIndentedString(packgeUnit)).append("\n");
    sb.append("    manufacturer: ").append(toIndentedString(manufacturer)).append("\n");
    sb.append("    positionname: ").append(toIndentedString(positionname)).append("\n");
    sb.append("    positionid: ").append(toIndentedString(positionid)).append("\n");
    sb.append("    excludeIds: ").append(toIndentedString(excludeIds)).append("\n");
    sb.append("    startTime: ").append(toIndentedString(startTime)).append("\n");
    sb.append("    commonName: ").append(toIndentedString(commonName)).append("\n");
    sb.append("    referenceNmb: ").append(toIndentedString(referenceNmb)).append("\n");
    sb.append("    parea: ").append(toIndentedString(parea)).append("\n");
    sb.append("    productValue: ").append(toIndentedString(productValue)).append("\n");
    sb.append("    providerName: ").append(toIndentedString(providerName)).append("\n");
    sb.append("    unitName: ").append(toIndentedString(unitName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

