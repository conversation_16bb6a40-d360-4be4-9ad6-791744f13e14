package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * @ClassName ProductSystemDictVo
 * @Description 前端字典封装信息类
 * <AUTHOR>
 * @Date 2019/9/23 14:44
 * @Version 1.0
 **/
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-07T14:34:49.141+08:00")

@ApiModel(description = "字典返回到前端封装信息类")
public class ProductSystemDictVo {

    @JsonProperty("id")
    private Integer id;
    @JsonProperty("bussinessId")
    private Integer bussinessId;
    @JsonProperty("name")
    private String name;
    @JsonProperty("createUser")
    private Integer createUser;
    @JsonProperty("createTime")
    private Date createTime;
    @JsonProperty("sort")
    private Integer sort;
    @JsonProperty("value")
    private Integer value;
    @JsonProperty("systemYn")
    private Byte systemYn;
    @JsonProperty("description")
    private String description;
    @JsonProperty("organSign")
    private String organSign;
    @JsonProperty("yn")
    private Byte yn;
    @JsonProperty("status")
    private Byte status;
    @JsonProperty("baseVersion")
    private Long baseVersion;
    @JsonProperty("sidx")
    private String sidx;
    @JsonProperty("sord")
    private String sord;
    @JsonProperty("disabled")
    private boolean disabled;

    @ApiModelProperty(value = "字典信息主键id")
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @ApiModelProperty(value = "字典业务id")
    public Integer getBussinessId() {
        return bussinessId;
    }

    public void setBussinessId(Integer bussinessId) {
        this.bussinessId = bussinessId;
    }

    @ApiModelProperty(value = "字典展示名称")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @ApiModelProperty(value = "字典创建人")
    public Integer getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Integer createUser) {
        this.createUser = createUser;
    }

    @ApiModelProperty(value = "字典创建时间")
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @ApiModelProperty(value = "字典排序字段")
    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    @ApiModelProperty(value = "字典值")
    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    @ApiModelProperty(value = "是否系统类型")
    public Byte getSystemYn() {
        return systemYn;
    }

    public void setSystemYn(Byte systemYn) {
        this.systemYn = systemYn;
    }

    @ApiModelProperty(value = "字典描述字段")
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @ApiModelProperty(value = "机构号")
    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    @ApiModelProperty(value = "字典是否被删除，0删除，1未删除")
    public Byte getYn() {
        return yn;
    }

    public void setYn(Byte yn) {
        this.yn = yn;
    }

    @ApiModelProperty(value = "字典是否启用，0禁用，1启用")
    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    @ApiModelProperty(value = "字典修改版本号")
    public Long getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(Long baseVersion) {
        this.baseVersion = baseVersion;
    }

    @ApiModelProperty(value = "字典排序字段")
    public String getSidx() {
        return sidx;
    }

    public void setSidx(String sidx) {
        this.sidx = sidx;
    }

    @ApiModelProperty(value = "字典排序字段")
    public String getSord() {
        return sord;
    }

    public void setSord(String sord) {
        this.sord = sord;
    }

    @ApiModelProperty(value = "是否被禁用，true：被禁用，false：未禁用")
    public boolean isDisabled() {
        return disabled;
    }

    public void setDisabled(boolean disabled) {
        this.disabled = disabled;
    }
}
