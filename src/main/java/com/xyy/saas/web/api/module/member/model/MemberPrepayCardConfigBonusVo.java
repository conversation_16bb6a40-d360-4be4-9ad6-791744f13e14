package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 计算会员储值赠送余额Vo
 * <AUTHOR>
 */
@ApiModel(description = "计算会员储值赠送余额")
public class MemberPrepayCardConfigBonusVo implements Serializable {

    private static final long serialVersionUID = -5200666984802674146L;
    /**
     * 会员等级id
     */
    @ApiModelProperty(value = "会员等级id", required = true, example = "102")
    private Long memberLevelId;

    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识", hidden = true)
    private String organSign;

    /**
     * 实充金额
     */
    @ApiModelProperty(value = "会员等级id", required = true, example = "500.00")
    private BigDecimal amount;

    public Long getMemberLevelId() {
        return memberLevelId;
    }

    public void setMemberLevelId(Long memberLevelId) {
        this.memberLevelId = memberLevelId;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    @Override
    public String toString() {
        return "MemberPrepayCardConfigBonusVo{" +
                "memberLevelId=" + memberLevelId +
                ", organSign='" + organSign + '\'' +
                ", amount=" + amount +
                '}';
    }
}