package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModelProperty;

public class MemberPrepayCardTokenVo {

    /**
     * 会员储值令牌(32位)
     * 从接口/memberPrepayCard/token/get获取
     * @see com.xyy.saas.member.core.swagger.api.MemberPreparCardTokenApiController
     */
    @ApiModelProperty(value = "会员储值令牌(32位)，从接口/memberPrepayCard/token/get获取", example = "5GAlEbZx/fc79dg+ue7pnrQwypD6WjLKGKwh/c3M2THrEHdNKAnuoSbQiUKA8VspKSEh+Q3gsUnT+95z++Hq30Q9TQ1t+fkZ2akj5z+OXvQ2IZTmlczwJsPLdeCBCcU0yOYQtxdlX2sD7crk8Ho/z65jb1GuNxyPFN6xzidGmqyW73JWdv/AAf4VhDPaUdXqFNtRkt7U0taiaRzEFL5H3g==")
    private String token;

    /**
     * 请求参数签名，暂时不用
     * 签名格式为 key1=value&key2=value2...&token=TOKEN
     * key按照字典顺序排列，使用RSA方式加密
     */
    @ApiModelProperty(value = "请求参数签名, 签名格式为 key1=value&key2=value2...&token=TOKEN, key按照字典顺序排列，使用RSA方式加密", hidden = true, example = "")
    private String sign;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    @Override
    public String toString() {
        return "MemberPrepayCardTokenVo{" +
                "token='" + token + '\'' +
                ", sign='" + sign + '\'' +
                '}';
    }
}
