package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName ScatteredRuleQueryExportVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/9/24 12:23
 * @Version 1.0
 **/
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

@ApiModel(description = "商品拆零信息维护导出文件查询对象")
public class ScatteredRuleQueryExportVo extends ScatteredRuleQueryVo {

    @JsonProperty("colName")
    private String[] colName;//导出列的字段名称
    @JsonProperty("colNameDesc")
    private String[] colNameDesc;//导出列的描述
    @JsonProperty("excelName")
    private String excelName;//文件名称
    @JsonProperty("isAll")
    private Byte isAll;//1代表导出全部，2代表导出部分
    @JsonProperty("prefs")
    private String[] prefs;//当isAll参数为2,时，此数组存放需要导出的部分商品内码集合，是拆零前商品的内码
    @JsonProperty("createUserName")
    private String createUserName;

    @ApiModelProperty(value = "用户名称，当前登录用户的名称，导出excel时必传")
    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    @ApiModelProperty(value = "导出列的字段名称")
    public String[] getColName() {
        return colName;
    }

    public void setColName(String[] colName) {
        this.colName = colName;
    }

    @ApiModelProperty(value = "导出列的描述")
    public String[] getColNameDesc() {
        return colNameDesc;
    }

    public void setColNameDesc(String[] colNameDesc) {
        this.colNameDesc = colNameDesc;
    }

    @ApiModelProperty(value = "文件名称")
    public String getExcelName() {
        return excelName;
    }

    public void setExcelName(String excelName) {
        this.excelName = excelName;
    }

    @ApiModelProperty(value = "1代表导出全部，2代表导出部分")
    public Byte getIsAll() {
        return isAll;
    }

    public void setIsAll(Byte isAll) {
        this.isAll = isAll;
    }

    @ApiModelProperty(value = "当isAll参数为2,时，此数组存放需要导出的部分商品内码集合，是拆零前商品的内码")
    public String[] getPrefs() {
        return prefs;
    }

    public void setPrefs(String[] prefs) {
        this.prefs = prefs;
    }


}
