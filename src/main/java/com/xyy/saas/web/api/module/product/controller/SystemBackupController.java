package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.api.ProductApi;
import com.xyy.saas.product.core.dto.ProductDto;
import com.xyy.saas.web.api.module.product.model.SystemBackupDto;
import com.xyy.saas.web.api.module.product.util.ExportExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.OutputStream;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

@Controller
@RequestMapping(value = "/product/systemBackup")
@Slf4j
public class SystemBackupController {
    private Random rand = new Random();

    @Reference(version = "0.0.1", retries = -1)
    private ProductApi productApi;


    @RequestMapping(value = "/download")
    public void download(HttpServletRequest request, HttpServletResponse response) {
        String organSign = request.getHeader("organSign");
        String modelJson = request.getHeader("commonRequestModel");
        CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);

        ProductDto dto = new ProductDto();
        //默认查询启用
        if (null == dto.getUsed()) {
            dto.setUsed(new Byte("1"));
        }
        dto.setOrganSign(organSign);
        dto.setPage(1);
        int randNumber = rand.nextInt(1000 - 800 + 1) + 800;
        dto.setRows(randNumber);
        ResultVO resultVO = this.productApi.productList(dto);
        PageInfo result = (PageInfo) resultVO.getResult();

        List<ProductDto> list1 = null;
        if (null != result) {
            list1 = result.getList();
        }else {
            list1 = new ArrayList<>();
        }
        List<SystemBackupDto> systemBackupDtos = new ArrayList<>();
        list1.forEach(item -> {
            SystemBackupDto dto1 = new SystemBackupDto();
            BeanUtils.copyProperties(item,dto1);
            systemBackupDtos.add(dto1);
        });
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
        String extfilename = "备份数据" + df.format(new Date()) + ".bak";
        String sheetName = "备份数据";
        String headers[] = new String[]{"通用名".getBytes().toString(), "混合查询".getBytes().toString(), "规格/型号".getBytes().toString(),  "商品编码".getBytes().toString(), "商品名称".getBytes().toString(), "商品编号".getBytes().toString(), "生产厂家".getBytes().toString(), "批准文号".getBytes().toString(), "条形码".getBytes().toString()
        ,"产地".getBytes().toString()};
        String fieldNames[] = new String[]{"commonName", "mixedQuery", "attributeSpecification",  "pref", "productName", "pharmacyPref",
                "manufacturer", "approvalNumber", "barCode","producingArea"};
        try {



            OutputStream out = null;
            try {
                out = response.getOutputStream();
                XSSFWorkbook work = new XSSFWorkbook();
                //创建工作表
                Sheet sheet = work.createSheet(sheetName);
                //显示标题
                Row title_row = sheet.createRow(0);
                title_row.setHeight((short) (40 * 20));
                Row header_row = sheet.createRow(0);
                //创建单元格的 显示样式
                CellStyle style = work.createCellStyle();
                style.setAlignment(CellStyle.ALIGN_CENTER); //水平方向上的对其方式
                style.setVerticalAlignment(CellStyle.VERTICAL_CENTER);  //垂直方向上的对其方式
                int headcell_index=0;

                sheet.setColumnWidth(headcell_index, 1600);
                Cell headcell = header_row.createCell(headcell_index);
                //应用样式到  单元格上
                headcell.setCellStyle(style);
                headcell.setCellValue("序号");
                headcell_index=1;

                for (int i = 0; i < headers.length; i++) {
                    sheet.setColumnWidth(headcell_index, 5000);
                    Cell cell = header_row.createCell(headcell_index);
                    cell.setCellStyle(style);
                    cell.setCellValue(headers[i]);
                    headcell_index++;
                }
                //插入需导出的数据
                for (int i = 0; i < systemBackupDtos.size(); i++) {
                    Row row = sheet.createRow(i + 1);
                    row.setHeight((short) (20 * 20)); //设置行高  基数为20
                    Object obj = systemBackupDtos.get(i);
                    Class classType = obj.getClass();
                    int cell_index=0;
                    Cell cell = row.createCell(cell_index);
                    cell.setCellValue(i+1);
                    cell_index=1;
                    for (int j = 0; j < fieldNames.length; j++) {
                        String fieldName = fieldNames[j];
                        String firstLetter = fieldName.substring(0, 1).toUpperCase();
                        String getMethodName = "get" + firstLetter + fieldName.substring(1);
                        Method getMethod = classType.getMethod(getMethodName, new Class[]{});
                        Object value = getMethod.invoke(obj, new Object[]{});
                        if(value!=null){
                            if (value instanceof Date) {//日期类型
                                SimpleDateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
                                cell.setCellValue(df.format(value));
                            } else {
                                cell.setCellValue(value.toString());
                            }
                        }
                        cell_index++;
                    }
                }
                //   String agent = request.getHeader("USER-AGENT").toLowerCase();
                //根据浏览器类型处理文件名称
                //   if (agent.indexOf("msie") > -1) {
                extfilename = URLEncoder.encode(extfilename, "UTF-8");
                response.setContentType("application/msexcel");
                response.setHeader("Content-disposition", "attachment;filename=\"" + extfilename + "\"");
                work.write(out);
                out.flush();
                out.close();
            } catch (Exception e) {
                log.error("导出excel出错: "+e.getMessage());
                e.printStackTrace();
            } finally {
                out.close();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
