package com.xyy.saas.web.api.module.product.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel(description = "供应商信息实体")
public class SaasProviderBaseInfo {

	private Long id;

	/** 供应商编号 */
	@ApiModelProperty(value = "供应商编号")
	private String pref;

	/** 混合查询 */
	@ApiModelProperty(value = "混合查询")
	private String mnemonicCode;

	/** 供应商名称 */
	@ApiModelProperty(value = "供应商名称")
	private String providerName;

	/** 法定代表人 */
	@ApiModelProperty(value = "法定代表人")
	private String legalRepresentative;

	/** 供应商类别 */
	@ApiModelProperty(value = "供应商类别")
	private int providerType;

	/** 注册地址 */
	@ApiModelProperty(value = "注册地址")
	private String registeredAddress;

	/** 经营范围 */
	@ApiModelProperty(value = "经营范围")
	private String businessScope;

	/** 营业执照编码 */
	@ApiModelProperty(value = "营业执照编码 ")
	private String businessLicense;

	/** 发证机关 */
	@ApiModelProperty(value = "发证机关")
	private String licenceAuthority;

	/** 注册日期 */
	@ApiModelProperty(value = "注册日期")
	private Date registeredDate;

	/** 有效期至 */
	@ApiModelProperty(value = "有效期至 ")
	private Date expirationDate;

	/** 有效期至方式:1--长期，2--手填，当为2的时候，expiration_date必须有值 */
	@ApiModelProperty(value = "有效期至方式:1--长期，2--手填，当为2的时候，expiration_date必须有值", allowableValues = "1,2", required = true)
	private int expirationDateType;

	/** 是否三证合一：0--否，1--是 */
	@ApiModelProperty(value = "是否三证合一：0--否，1--是 ", allowableValues = "0,1", required = true)
	private int treeInOneType;

	/** 开户银行 */
	@ApiModelProperty(value = "开户银行")
	private String depositBank;

	/** 银行账号 */
	@ApiModelProperty(value = "银行账号")
	private String bankAccount;

	/** 开户户名 */
	@ApiModelProperty(value = "开户户名")
	private String accountName;

	/** 组织机构代码证 */
	@ApiModelProperty(value = " 组织机构代码证")
	private String organizationCertificationCode;

	/** 发证日期 */
	@ApiModelProperty(value = "发证日期")
	private Date organizationCertificationDate;

	/** 有效期至 */
	@ApiModelProperty(value = "有效期至")
	private Date organizationCertificationExpirationDate;

	/** 税务登记号 */
	@ApiModelProperty(value = "税务登记号")
	private String organizationCertificationTaxNo;

	/** 组织机构代码证发证机关 */
	@ApiModelProperty(value = "组织机构代码证发证机关")
	private String organizationCertificationAuthority;

	/** 创建人 */
	@ApiModelProperty(value = "")
	private String createUser;

	/** 创建时间 */
	@ApiModelProperty(value = "")
	private Date createTime;

	/** 更新人 */
	@ApiModelProperty(value = "")
	private String updateUser;

	/** 更新时间 */

	@ApiModelProperty(value = "")
	private Date updateTime;

	/** 状态：1--审核通过，2--审核中，3,--审核未通过 */

	@ApiModelProperty(value = "状态：1--审核通过，2--审核中，3,--审核未通过")
	private int status;

	@ApiModelProperty(value = "")
	private String baseVersion; // 操作版本号
	@ApiModelProperty(value = "药店唯一标识")
	private String organSign; // 药店唯一标识

	@ApiModelProperty(value = "")
	private int yn;// 逻辑删除,1--有效,0--删除
	@ApiModelProperty(value = "")
	private String remark;// 存json数据

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getPref() {
		return pref;
	}

	public void setPref(String pref) {
		this.pref = pref;
	}

	public String getMnemonicCode() {
		return mnemonicCode;
	}

	public void setMnemonicCode(String mnemonicCode) {
		this.mnemonicCode = mnemonicCode;
	}

	public String getProviderName() {
		return providerName;
	}

	public void setProviderName(String providerName) {
		this.providerName = providerName;
	}

	public String getLegalRepresentative() {
		return legalRepresentative;
	}

	public void setLegalRepresentative(String legalRepresentative) {
		this.legalRepresentative = legalRepresentative;
	}

	public int getProviderType() {
		return providerType;
	}

	public void setProviderType(int providerType) {
		this.providerType = providerType;
	}

	public String getRegisteredAddress() {
		return registeredAddress;
	}

	public void setRegisteredAddress(String registeredAddress) {
		this.registeredAddress = registeredAddress;
	}

	public String getBusinessScope() {
		return businessScope;
	}

	public void setBusinessScope(String businessScope) {
		this.businessScope = businessScope;
	}

	public String getBusinessLicense() {
		return businessLicense;
	}

	public void setBusinessLicense(String businessLicense) {
		this.businessLicense = businessLicense;
	}

	public String getLicenceAuthority() {
		return licenceAuthority;
	}

	public void setLicenceAuthority(String licenceAuthority) {
		this.licenceAuthority = licenceAuthority;
	}

	public Date getRegisteredDate() {
		return registeredDate;
	}

	public void setRegisteredDate(Date registeredDate) {
		this.registeredDate = registeredDate;
	}

	public Date getExpirationDate() {
		return expirationDate;
	}

	public void setExpirationDate(Date expirationDate) {
		this.expirationDate = expirationDate;
	}

	@ApiModelProperty(dataType = "int")
	public int getExpirationDateType() {
		return expirationDateType;
	}

	public void setExpirationDateType(int expirationDateType) {
		this.expirationDateType = expirationDateType;
	}

	public int getTreeInOneType() {
		return treeInOneType;
	}

	public void setTreeInOneType(int treeInOneType) {
		this.treeInOneType = treeInOneType;
	}

	public String getDepositBank() {
		return depositBank;
	}

	public void setDepositBank(String depositBank) {
		this.depositBank = depositBank;
	}

	public String getBankAccount() {
		return bankAccount;
	}

	public void setBankAccount(String bankAccount) {
		this.bankAccount = bankAccount;
	}

	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	public String getOrganizationCertificationCode() {
		return organizationCertificationCode;
	}

	public void setOrganizationCertificationCode(String organizationCertificationCode) {
		this.organizationCertificationCode = organizationCertificationCode;
	}

	public Date getOrganizationCertificationDate() {
		return organizationCertificationDate;
	}

	public void setOrganizationCertificationDate(Date organizationCertificationDate) {
		this.organizationCertificationDate = organizationCertificationDate;
	}

	public Date getOrganizationCertificationExpirationDate() {
		return organizationCertificationExpirationDate;
	}

	public void setOrganizationCertificationExpirationDate(Date organizationCertificationExpirationDate) {
		this.organizationCertificationExpirationDate = organizationCertificationExpirationDate;
	}

	public String getOrganizationCertificationTaxNo() {
		return organizationCertificationTaxNo;
	}

	public void setOrganizationCertificationTaxNo(String organizationCertificationTaxNo) {
		this.organizationCertificationTaxNo = organizationCertificationTaxNo;
	}

	public String getOrganizationCertificationAuthority() {
		return organizationCertificationAuthority;
	}

	public void setOrganizationCertificationAuthority(String organizationCertificationAuthority) {
		this.organizationCertificationAuthority = organizationCertificationAuthority;
	}

	public String getCreateUser() {
		return createUser;
	}

	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getUpdateUser() {
		return updateUser;
	}

	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public String getBaseVersion() {
		return baseVersion;
	}

	public void setBaseVersion(String baseVersion) {
		this.baseVersion = baseVersion;
	}

	public String getOrganSign() {
		return organSign;
	}

	public void setOrganSign(String organSign) {
		this.organSign = organSign;
	}

	public int getYn() {
		return yn;
	}

	public void setYn(int yn) {
		this.yn = yn;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

}
