package com.xyy.saas.web.api.module.consult.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 远程问诊服务购买项目Vo
 * <AUTHOR>
 */
@ApiModel(description = "远程问诊服务购买项目")
public class ConsultServiceItemVo implements Serializable {

    private static final long serialVersionUID = -3108684649062599274L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称", required = true)
    private String name;

    /**
     * 类型 1-按条数计费 2-按时间计费
     */
    @ApiModelProperty(value = "类型 1-按条数计费 2-按时间计费", required = true, example = "1")
    private Byte type;

    /**
     * 充值天数
     */
    @ApiModelProperty(value = "充值天数", example = "30")
    private Integer days;

    /**
     * 充值条数
     */
    @ApiModelProperty(value = "充值条数", example = "1")
    private Integer count;

    /**
     * 价格
     */
    @ApiModelProperty(value = "价格", required = true)
    private BigDecimal price;

    /**
     * 状态 0-禁用 1-启用
     */
    @ApiModelProperty(value = "状态 0-禁用 1-启用",example = "1")
    private Byte status;

    /**
     * 是否需要审方服务 0-否 1-是
     */
    @ApiModelProperty(value = "是否需要审方服务 0-否 1-是",example = "0")
    private Byte needAudit;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public Integer getDays() {
        return days;
    }

    public void setDays(Integer days) {
        this.days = days;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Byte getNeedAudit() {
        return needAudit;
    }

    public void setNeedAudit(Byte needAudit) {
        this.needAudit = needAudit;
    }

    @Override
    public String toString() {
        return "ConsultServiceItemVo{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", type=" + type +
                ", days=" + days +
                ", count=" + count +
                ", price=" + price +
                ", status=" + status +
                ", needAudit=" + needAudit +
                '}';
    }
}