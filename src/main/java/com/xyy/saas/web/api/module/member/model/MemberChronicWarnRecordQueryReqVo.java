package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * @Description 会员慢病提醒记录表[商品、购买门店、会员]-查询条件
 * <AUTHOR>
 * @Create 2020-09-24 20:15
 */
public class MemberChronicWarnRecordQueryReqVo extends PageQueryVo{
    /*
    -- 会员信息、关心病种、药品信息、药品用完提前天数、超过预计药品用完日期、购买日期。(分页且涉及关联表查询)
    -- 1）会员信息：支持手动输入会员手机号、姓名、卡号进行查询，支持模糊匹配；
    -- 2）关心病种：下拉选择，下拉列表中显示全部和慢病管理中添加的所有慢病种类，默认显示全部；
    -- 3）药品信息：支持手动输入通用名称、助记码和商品编号进行查询，支持模糊匹配；
    -- 4）药品用完提前天数XX天：输入的是天数，只可输入0-31之间的整数，默认为空并显示0-31提示；
    -- 5）超过预计药品用完日期XX天不显示：输入的是天数，只可输入1-31之间的整数，默认为空并显示1-31提示；
    -- 6）购买日期：可设置开始日期-结束日期；
    -- 7）查询按钮：点击后根据当前条件进行查询，条件互为并且关系；
    */
    @ApiModelProperty(value = "会员混合查询")
    private String memberMixedQuery;
    @ApiModelProperty(value = "关心病种")
    private String chronicPref; //-1表示全部(待定)
    @ApiModelProperty(value = "商品混合查询")
    private String goodMixedQuery;
    @ApiModelProperty(value = "药品用完提前天数XX天0-31")
    private Integer daysBefore;
    @ApiModelProperty(value = "超过预计药品用完日期XX天不显示1-31")
    private Integer daysAfter;
    @ApiModelProperty(value = "购买范围日期开始日期")
    private Date startDate;
    @ApiModelProperty(value = "购买范围日期结束日期")
    private Date endDate;
    @ApiModelProperty(value = "购买门店-仅总部使用")
    private String organSign;
    @ApiModelProperty(value = "是否隐藏门店")
    private Byte isDrugstoreHidden;

    public String getMemberMixedQuery() {
        return memberMixedQuery;
    }

    public void setMemberMixedQuery(String memberMixedQuery) {
        this.memberMixedQuery = memberMixedQuery;
    }

    public String getChronicPref() {
        return chronicPref;
    }

    public void setChronicPref(String chronicPref) {
        this.chronicPref = chronicPref;
    }

    public String getGoodMixedQuery() {
        return goodMixedQuery;
    }

    public void setGoodMixedQuery(String goodMixedQuery) {
        this.goodMixedQuery = goodMixedQuery;
    }

    public Integer getDaysBefore() {
        return daysBefore;
    }

    public void setDaysBefore(Integer daysBefore) {
        this.daysBefore = daysBefore;
    }

    public Integer getDaysAfter() {
        return daysAfter;
    }

    public void setDaysAfter(Integer daysAfter) {
        this.daysAfter = daysAfter;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public Byte getIsDrugstoreHidden() {
        return isDrugstoreHidden;
    }

    public void setIsDrugstoreHidden(Byte isDrugstoreHidden) {
        this.isDrugstoreHidden = isDrugstoreHidden;
    }
}
