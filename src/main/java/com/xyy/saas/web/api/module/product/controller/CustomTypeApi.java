package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.dto.SaasCustomTypeAddDto;
import com.xyy.saas.common.dto.SaasCustomTypeQueryDto;
import com.xyy.saas.common.dto.SaasCustomTypeUpdateDto;
import com.xyy.saas.common.util.ResultVO;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T18:13:39.358+08:00")
@RequestMapping("/product/customType")
@Api(value = "YbmlProduct", description = "商品自定义API接口")
public interface CustomTypeApi {

    @ApiOperation(value = "分页搜索接口", notes = "分页搜索接口", response = ResultVO.class, tags={ "商品自定义API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/pageSearch")
    ResponseEntity<ResultVO> pageSearch(HttpServletRequest request, @ApiParam(value = "分页查询参数", required = true) @RequestBody SaasCustomTypeQueryDto queryDto);


    @ApiOperation(value = "查询直接子分类接口", notes = "查询直接子分类接口", response = ResultVO.class, tags={ "商品自定义API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/getDirectChildrens")
    ResponseEntity<ResultVO> getDirectChildrens(HttpServletRequest request, @ApiParam(value = "查询参数", required = true) @RequestBody SaasCustomTypeQueryDto queryDto);


    @ApiOperation(value = "查全部分类接口", notes = "查全部分类接口", response = ResultVO.class, tags={ "商品自定义API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/getAll")
    ResponseEntity<ResultVO> getAll(HttpServletRequest request);


    @ApiOperation(value = "更新分类接口", notes = "更新分类接口", response = ResultVO.class, tags={ "商品自定义API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/update")
    ResponseEntity<ResultVO> update(HttpServletRequest request, @ApiParam(value = "更新分类参数", required = true) @RequestBody SaasCustomTypeUpdateDto updateDto);


    @ApiOperation(value = "删除分类接口", notes = "删除分类接口", response = ResultVO.class, tags={ "商品自定义API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/delete")
    ResponseEntity<ResultVO> delete(HttpServletRequest request, @ApiParam(value = "删除分类参数", required = true) @RequestBody SaasCustomTypeUpdateDto updateDto);

    @ApiOperation(value = "新增分类接口", notes = "新增分类接口", response = ResultVO.class, tags={ "商品自定义API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/addType")
    ResponseEntity<ResultVO> addType(HttpServletRequest request, @ApiParam(value = "新增分类接口", required = true) @RequestBody SaasCustomTypeAddDto addDto);

}
