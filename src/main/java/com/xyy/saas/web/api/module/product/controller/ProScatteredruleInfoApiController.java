package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.api.ExportExcelApi;
import com.xyy.saas.common.api.SystemDictApi;
import com.xyy.saas.common.dto.ExportExcelDto;
import com.xyy.saas.common.dto.SystemDictDto;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.api.ProScatteredruleApi;
import com.xyy.saas.product.core.api.ProductApi;
import com.xyy.saas.product.core.dto.*;
import com.xyy.saas.rabbitmq.core.api.MessagePushApi;
import com.xyy.saas.web.api.module.product.constant.DictConstant;
import com.xyy.saas.web.api.module.product.model.ExportProductScatteredRuleVo;
import com.xyy.saas.web.api.module.product.model.ProductScatteredRuleVo;
import com.xyy.saas.web.api.module.product.model.ScatteredRuleQueryExportVo;
import com.xyy.saas.web.api.module.product.model.ScatteredRuleQueryVo;
import com.xyy.saas.web.api.module.product.util.DateUtil;
import com.xyy.saas.web.api.module.product.util.ExecServiceTask;
import com.xyy.saas.web.api.module.product.util.ExportExcelUtil;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.api.EmployeefileApi;
import com.xyy.user.module.api.RoleApi;
import com.xyy.user.module.dto.EmployeefileRequestModel;
import com.xyy.user.module.dto.result.EmployeeDto;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Controller
public class ProScatteredruleInfoApiController implements ProScatteredruleInfoApi {

    @Reference(version = "0.0.1")
    private ProScatteredruleApi ruleApi;

    @Reference(version = "0.0.1")
    private ProductApi productApi;

    @Reference(version = "0.0.1")
    private SystemDictApi systemDictApi;

    @Reference(version = "0.0.1")
    private MessagePushApi messagePushApi;

    @Reference(version = "0.0.1")
    private EmployeeApi employeeApi;

    @Reference(version = "0.0.3")
    public ExportExcelApi exportExcelApi;

    @Reference(version = "0.0.1")
    private EmployeefileApi employeefileApi;

    @Reference(version = "0.0.1")
    public RoleApi roleUserApi;

    @Value("${ftp.serverName}")
    public String serverName;

    @Value("${ftp.port}")
    public String port;

    @Value("${ftp.username}")
    public String username;

    @Value("${ftp.password}")
    public String password;

    @Value("${ftp.remotePath}")
    public String remotePath;

    @Value("${ftp.downLoadUrl}")
    public String downLoadUrl;

    private static final Logger logger = LoggerFactory.getLogger(ProScatteredruleInfoApiController.class);

    @Override
    public ResponseEntity<ResultVO> query(HttpServletRequest request, @ApiParam(value = "商品拆零信息维护 新增查询对象" ,required=true )  @Valid @RequestBody ScatteredRuleQueryVo scatteredRuleQueryVo) {
        logger.info("ProScatteredruleInfoApiController query name:"+scatteredRuleQueryVo.getName()+",page:"+scatteredRuleQueryVo.getPage()+",rows:"+scatteredRuleQueryVo.getRows());
        String organSign = request.getHeader("organSign");
        String employeeId = request.getHeader("employeeId");
        Byte identity = employeeApi.queryEmployeeById(Integer.valueOf(employeeId)).getResult().getIdentity();
        Byte isProductHidden = identity;
        Integer page = scatteredRuleQueryVo.getPage();
        Integer rows = scatteredRuleQueryVo.getRows();
        if ( null == page )
            page = 1;

        if ( null == rows )
            rows = 50;

        PageInfo info = new PageInfo();
        info.setPageNum(page);
        info.setPageSize(rows);
        ScatteredQueryDto paramDto = new ScatteredQueryDto();
        paramDto.setName(scatteredRuleQueryVo.getName());//名称模糊搜索
        paramDto.setIsProductHidden(isProductHidden);//
        paramDto.setOrganSign(organSign);
        paramDto.setProductPref(scatteredRuleQueryVo.getProductPref());
        paramDto.setManufacturer(scatteredRuleQueryVo.getManufacturer());
        paramDto.setCreateUser(scatteredRuleQueryVo.getCreateUser());
        paramDto.setCreateTimeStart(scatteredRuleQueryVo.getCreateTimeStart());
        paramDto.setCreateTimeEnd(scatteredRuleQueryVo.getCreateTimeEnd());
        PageInfo<ProductScatteredRuleVoDto> result = this.ruleApi.selectProScatteredListByParams(info, paramDto);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(result), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> productQuery(HttpServletRequest request, @ApiParam(value = "商品拆零信息维护 新增查询对象" ,required=true )  @Valid @RequestBody ScatteredRuleQueryVo scatteredRuleQueryVo) {
        logger.info("ProScatteredruleInfoApiController productQuery name:"+scatteredRuleQueryVo.getName()+",page:"+scatteredRuleQueryVo.getPage()+",rows:"+scatteredRuleQueryVo.getRows());
        String organSign = request.getHeader("organSign");
        String employeeId = request.getHeader("employeeId");
        Byte identity = employeeApi.queryEmployeeById(Integer.valueOf(employeeId)).getResult().getIdentity();
        Byte isProductHidden = identity;
        Integer page = scatteredRuleQueryVo.getPage();
        Integer rows = scatteredRuleQueryVo.getRows();
        if(page==null){
            page=1;
        }
        if(rows==null){
            rows=50;
        }
        PageInfo info = new PageInfo();
        info.setPageNum(page);
        info.setPageSize(rows);
        PageInfo<ProductDto> dto = this.productApi.ruleProductQuery(info, scatteredRuleQueryVo.getName(), isProductHidden, 0, 0, organSign,scatteredRuleQueryVo.getProductName(),scatteredRuleQueryVo.getProductPref(),scatteredRuleQueryVo.getApprovalNumber(),scatteredRuleQueryVo.getSystemType());
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(dto), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> chainProductQuery(HttpServletRequest request, @ApiParam(value = "连锁商品拆零信息维护 新增查询对象" ,required=true )  @Valid @RequestBody ScatteredRuleQueryVo scatteredRuleQueryVo) {
        logger.info("ProScatteredruleInfoApiController productQuery name:"+scatteredRuleQueryVo.getName()+",page:"+scatteredRuleQueryVo.getPage()+",rows:"+scatteredRuleQueryVo.getRows());
        String organSign = request.getHeader("organSign");
        String employeeId = request.getHeader("employeeId");
        Byte identity = employeeApi.queryEmployeeById(Integer.valueOf(employeeId)).getResult().getIdentity();
        Byte isProductHidden = identity;
        Integer page = scatteredRuleQueryVo.getPage();
        Integer rows = scatteredRuleQueryVo.getRows();
        if(page==null){
            page=1;
        }
        if(rows==null){
            rows=50;
        }
        PageInfo info = new PageInfo();
        info.setPageNum(page);
        info.setPageSize(rows);
        RuleProductQueryDto ruleProductQueryDto = new RuleProductQueryDto();
        BeanUtils.copyProperties(scatteredRuleQueryVo,ruleProductQueryDto);
        PageInfo<ProductDto> dto = this.productApi.ruleProductQuery(info, isProductHidden, 0, 0, organSign, ruleProductQueryDto);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(dto), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getProScatteredById(HttpServletRequest request, @ApiParam(value = "商品拆零信息维护 新增查询对象" ,required=true )  @Valid @RequestBody ScatteredRuleQueryVo scatteredRuleQueryVo) {
        logger.info("ProScatteredruleInfoApiController getProScatteredById id:"+scatteredRuleQueryVo.getId());
        Long id = scatteredRuleQueryVo.getId();
        String organSign = request.getHeader("organSign");
        ProductScatteredRuleVoDto result = null;
        if (null == id){
            result = null;
        }
        result = this.ruleApi.getProScatteredById(id, organSign);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(result), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> addOrUpdate(HttpServletRequest request, @ApiParam(value = "拆零商品信息封装对象" ,required=true )  @Valid @RequestBody ProductScatteredRuleVo scatteredRulePo) throws InterruptedException {
        if(StringUtils.isEmpty(scatteredRulePo) ){
            logger.error("==============参数不合法");
            return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, null), HttpStatus.OK);
        }
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        logger.info("xiaoshou chailing addOrUpdate organSign:"+organSign+",employee:"+employee+",productPref:"+scatteredRulePo.getProductPref()+",getPharmacyPref:"+scatteredRulePo.getPharmacyPref());
        int status = 0;
        scatteredRulePo.setYn((byte) 1);
        // 新增一条商品规则的时候需要将原商品信息的状态设置为拆零状态、在Service中已做处理
        if ( StringUtils.isEmpty(scatteredRulePo.getId()) ){
            scatteredRulePo.setOrganSign(organSign);
            scatteredRulePo.setCreateUser(StringUtils.isEmpty(scatteredRulePo.getCreateUser())?employee:scatteredRulePo.getCreateUser());
            scatteredRulePo.setCreateTime(new Date());
        } else{
            scatteredRulePo.setOrganSign(organSign);
            scatteredRulePo.setCreateUser(StringUtils.isEmpty(scatteredRulePo.getCreateUser())?employee:scatteredRulePo.getCreateUser());
            scatteredRulePo.setUpdateUser(StringUtils.isEmpty(scatteredRulePo.getUpdateUser())?employee:scatteredRulePo.getUpdateUser());
            scatteredRulePo.setUpdateTime(new Date());
        }
        ProductScatteredRuleDto ruleDto = new ProductScatteredRuleDto();
        BeanUtils.copyProperties(scatteredRulePo,ruleDto);
        ResultVO result = ruleApi.save(ruleDto);
        if (result.getCode() == ResultCodeEnum.SUCCESS.getCode()){
            Thread.sleep(1000);
            pushProScatteredRuleMessToMQ(scatteredRulePo.getOrganSign());
        }
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> proScatteredExportListExcel(HttpServletRequest request, @ApiParam(value = "拆零商品信息封装对象" ,required=true )  @Valid @RequestBody ScatteredRuleQueryExportVo exportVo) throws InterruptedException {
        logger.info("+++++++++++商品拆零导出+++++++++++++++");
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        logger.info("+++++++++++商品拆零导出+++++++++++++++organ Sign："+organSign);
        EmployeeDto employeeDto = employeeApi.queryEmployeeById(Integer.valueOf(employee)).getResult();
        String createUserName = employeeDto.getName();
        Byte identity = employeeDto.getIdentity();
        Byte isProductHidden = identity;
        ExportScatteredQueryDto paramDto = new ExportScatteredQueryDto();
        paramDto.setOrganSign(organSign);
        paramDto.setIsProductHidden(isProductHidden);//
        if(exportVo.getIsAll() == 2){
            paramDto.setProductPrefs(Arrays.asList(exportVo.getPrefs()));
        }else{
            paramDto.setName(exportVo.getName());//名称模糊搜索
            paramDto.setProductPref(exportVo.getProductPref());
            paramDto.setManufacturer(exportVo.getManufacturer());
            paramDto.setCreateUser(exportVo.getCreateUser());
            paramDto.setCreateTimeStart(exportVo.getCreateTimeStart());
            paramDto.setCreateTimeEnd(exportVo.getCreateTimeEnd());
        }
        List<ExportProductScatteredRuleVo> resultPsrs = new ArrayList<>();
        List<ProductScatteredRuleVoDto> listPsrvds =  this.ruleApi.exportProScatteredListByParams(paramDto);
        List<Integer> ids = Arrays.asList(DictConstant.unitBussinessId, DictConstant.productSystemType);
        List<SystemDictDto> dictDtos = systemDictApi.findSystemDictDtoBybussinessIds(ids,organSign);
        Map<Integer,String> unitMap = new HashMap<>();
        Map<Integer,String> systemMap = new HashMap<>();
        for(SystemDictDto dto:dictDtos){
//            logger.info("unitid:"+dto.getId()+",unitname:"+dto.getName()+",businessid:"+dto.getBussinessId());
            if(dto.getBussinessId().equals(DictConstant.unitBussinessId)){
//                logger.info("unitid:"+dto.getId()+",unitname:"+dto.getName());
                unitMap.put(dto.getId(),dto.getName());
            }
            if(dto.getBussinessId().equals(DictConstant.productSystemType)){
                systemMap.put(dto.getId(),dto.getName());
            }
        }
        //查全体员工（包括已离职及已禁用）
        EmployeefileRequestModel employeefileRequestModel = new EmployeefileRequestModel();
        employeefileRequestModel.setOrganSign(organSign);
        employeefileRequestModel.setPageNum(1);
        employeefileRequestModel.setPageSize(100);
        List<EmployeeDto> eList = employeefileApi.getEmployeefileByCondition(employeefileRequestModel).getResult().getList();
        Map<Integer, String> eMap = eList.stream().collect(Collectors.toMap(EmployeeDto::getId, p -> p.getName()));
        if(listPsrvds != null && listPsrvds.size() > 0){
            for(ProductScatteredRuleVoDto dto:listPsrvds){
                ExportProductScatteredRuleVo vo = new ExportProductScatteredRuleVo();
                vo.setScatteredNumber(dto.getScatteredNumber()+"");
                vo.setProductName(dto.getProductName());
                vo.setManufacturer(dto.getManufacturer());
                vo.setPharmacyPref(dto.getPharmacyPref());
                vo.setUnitId(unitMap.get(dto.getUnitId()));
//                logger.info("导出拆零表格，unitid:" + dto.getUnitId()+",unitName:" + unitMap.get(dto.getUnitId()));
                vo.setCreateTimeStr(DateUtil.parseDateToStr(dto.getCreateTime(),DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS));
                vo.setCreateUserName(eMap.get(Integer.valueOf(dto.getCreateUser())));
                vo.setDrugPermissionPerson(dto.getDrugPermissionPerson());
                resultPsrs.add(vo);
            }
        }
        /*============================传ftp begin wht==============================*/
        String excelName=exportVo.getExcelName(); //文件名称
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
        String extfilename="";
        String uuid= ExportExcelUtil.getUUID();
        if(org.apache.commons.lang3.StringUtils.isNotBlank(excelName)){
            extfilename=excelName+".xls";
        }else{
            excelName="商品拆零信息维护报表"+df.format(new Date());
            extfilename="商品拆零信息维护报表"+df.format(new Date())+".xls";
        }
        String localPath = remotePath + "/" + uuid;
        String jsonStr = toJsonFromObj(exportVo);
        String extfilenames=extfilename;
        ExportExcelDto exportExcelDto=new ExportExcelDto();
        exportExcelDto.setSearchParamer(jsonStr);
        exportExcelDto.setExcelName(excelName);
        exportExcelDto.setFileName(uuid+"/"+extfilename);
        exportExcelDto.setCreateTime(new Date());
//        exportExcelDto.setName(eMap.get(Integer.valueOf(employee)));
        exportExcelDto.setName(createUserName);//前端必传
        exportExcelDto.setStatus((byte)0); //处理中
        exportExcelDto.setUserId(Integer.valueOf(employee));
        exportExcelDto.setMatchUrl("/baseinfo/proScattered/exportListExcel");
        exportExcelDto.setModuleId(10025); //父模块id
        exportExcelDto.setOperateModule("基础信息"); //父模块名称
        exportExcelDto.setSubModule("商品拆零信息维护"); //子模块名称
        exportExcelDto.setSubmoduleId(10047); //子模块id
        exportExcelDto.setUrl(downLoadUrl+localPath+"/"+extfilename);
        ResultVO<List<Integer>> listResultVO = roleUserApi.queryRoleIdsByEmployeeId(Integer.valueOf(employee));
        List<Integer> idList = listResultVO.getResult();
        String str = org.apache.commons.lang3.StringUtils.join(idList, ",");
        exportExcelDto.setRoleIdstr(str); //角色id字符串
        String headers[] = exportVo.getColNameDesc();
        String fieldNames[] = exportVo.getColName();
        String colNameDescs = org.apache.commons.lang3.StringUtils.join(exportVo.getColNameDesc(), ","); // 数组转字符串(逗号分隔)(推荐)
        String colNames = org.apache.commons.lang3.StringUtils.join(exportVo.getColName(), ","); // 数组转字符串(逗号分隔)(推荐)
        exportExcelDto.setColName(colNames);
        exportExcelDto.setColNameDesc(colNameDescs);
        exportExcelDto.setOrganSign(organSign);
        ExportExcelDto resultDto=exportExcelApi.saveExportExcel(exportExcelDto);
        /*============================传ftp end wht==============================*/
        String sheetName = "商品拆零信息维护报表";
        ExecServiceTask.execute(new Runnable(){
            @Override
            public void run() {
                try {
                    ExportExcelUtil.createExcelUpFtp(extfilenames,sheetName, headers, fieldNames, resultPsrs,true,serverName,port,username,password,localPath);
                    resultDto.setEndTime(new Date());
                    resultDto.setStatus((byte)1);
                    exportExcelApi.updateExportExcel(resultDto); //更新导出完成时间
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        });
        return new ResponseEntity(new ResultVO<>(resultDto) , HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> proScatteredUpdateStatus(HttpServletRequest request, @ApiParam(value = "拆零商品信息封装对象" ,required=true )  @Valid @RequestBody ScatteredRuleQueryVo updateVo) throws InterruptedException {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        ruleApi.updateStatus(updateVo.getId(),updateVo.getStatus(),organSign);
        pushProScatteredRuleMessToMQ(organSign);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(0), HttpStatus.OK);
    }

    private void pushProScatteredRuleMessToMQ(String organSign) {
        JSONObject json = new JSONObject();
        String[] tables = {"saas_product_scattered_rule","saas_inventory","saas_inventory_lot_number","saas_product_baseinfo","saas_product_scattered_record", "saas_product_scattered_record_detail"};
        json.put("code", "sync");
        json.put("tables", tables);
        messagePushApi.sendMsgByOrganSign(organSign, json.toJSONString());
    }

    public static String toJsonFromObj(Object obj) {
        String outJson ="";
        try {
            ObjectMapper mapper = new ObjectMapper();
            mapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY );
            outJson = mapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return outJson;
    }
}
