package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

@Api(value = "*******首页采购商品推荐", description = "*******首页采购商品推荐")
@RequestMapping("/product")
public interface HomeProductRecordApi {
    /**
     * 首页采购商品推荐查询接口
     */
    @ApiOperation(value = "首页采购商品推荐查询接口", notes = "首页采购商品推荐查询接口", response = ResultVO.class, tags={ "*******首页采购商品推荐", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/recommend/purchaseProduct",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> toShowEcPoroductsRecord(HttpServletRequest request);
}
