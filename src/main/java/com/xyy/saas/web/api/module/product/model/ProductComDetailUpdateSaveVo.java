package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-07T14:34:49.141+08:00")

@ApiModel(description = "商品配伍禁忌查询对象")
public class ProductComDetailUpdateSaveVo {

    @JsonProperty("id")
    private Long id;//数据库主键id

    @JsonProperty("subPref")
    private String subPref;//对应主表商品的配伍禁忌商品内码

    @JsonProperty("createUser")
    private String createUser;//创建人

    @JsonProperty("updateUser")
    private String updateUser;//更新人

    @JsonProperty("guid")
    private String guid;//全局唯一id

    @ApiModelProperty(value = "主键id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @ApiModelProperty(value = "对应主表商品的配伍禁忌商品内码")
    public String getSubPref() {
        return subPref;
    }

    public void setSubPref(String subPref) {
        this.subPref = subPref;
    }

    @ApiModelProperty(value = "创建人")
    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    @ApiModelProperty(value = "更新人")
    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    @ApiModelProperty(value = "全局唯一id")
    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }
}
