package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * @ClassName SpecialProductUpdateVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/8/27 16:09
 * @Version 1.0
 **/
@ApiModel(description = "积分商品更新信息类")
public class SpecialProductUpdateVo {

    //商品内码
    @JsonProperty("productPrefs")
    private String productPrefs;
    //商品积分倍率
    @JsonProperty("scoreRate")
    private BigDecimal scoreRate;
    //是否积分商品
    @JsonProperty("scoreProductYn")
    private Byte scoreProductYn;

    @ApiModelProperty(value = "积分倍率")
    public BigDecimal getScoreRate() {
        return scoreRate;
    }

    public void setScoreRate(BigDecimal scoreRate) {
        this.scoreRate = scoreRate;
    }

    @ApiModelProperty(value = "是否积分商品：是传1，否传0")
    public Byte getScoreProductYn() {
        return scoreProductYn;
    }

    public void setScoreProductYn(Byte scoreProductYn) {
        this.scoreProductYn = scoreProductYn;
    }

    @ApiModelProperty(value = "商品编号列表字符串，多个以逗号,分隔")
    public String getProductPrefs() {
        return productPrefs;
    }

    public void setProductPrefs(String productPrefs) {
        this.productPrefs = productPrefs;
    }
}
