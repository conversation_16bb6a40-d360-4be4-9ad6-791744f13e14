package com.xyy.saas.web.api.module.consult.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 药店远程问诊服务订单记录Vo
 * <AUTHOR>
 */
@ApiModel(description = "药店远程问诊服务订单记录")
public class ConsultServiceOrderVo implements Serializable {

    private static final long serialVersionUID = 5172483215285561398L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderId;

    /**
     * 服务项目id
     */
    @ApiModelProperty(value = "服务项目id", required = true)
    private Long serviceItemId;

    /**
     * 服务项目名称
     */
    @ApiModelProperty(value = "服务项目名称")
    private String serviceItemName;

    /**
     * 服务类型
     */
    @ApiModelProperty(value = "服务类型", example = "1")
    private Byte type;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价", example = "1.5")
    private BigDecimal price;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量", example = "1")
    private Integer num;

    /**
     * 应收金额
     */
    @ApiModelProperty(value = "应收金额")
    private BigDecimal receivableAmount;

    /**
     * 实收金额
     */
    @ApiModelProperty(value = "实收金额")
    private BigDecimal actualAmount;

    /**
     * 充值天数
     */
    @ApiModelProperty(value = "充值天数")
    private Integer days;

    /**
     * 充值条数
     */
    @ApiModelProperty(value = "充值条数")
    private Integer count;

    /**
     * 是否需要审方服务 0-否 1-是
     */
    @ApiModelProperty(value = "是否需要审方服务 0-否 1-是")
    private Byte needAudit;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer baseVersion;

    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识", required = true)
    private String organSign;

    /**
     * 支付类型
     */
    @ApiModelProperty(value = "支付类型", example = "1")
    private Byte payType;

    /**
     * 支付状态 1-支付成功 2-退款成功 11-未支付 12-支付失败 13-退款失败
     * @see com.xyy.saas.pay.core.emum.ChinaUmsBillStatus
     */
    @ApiModelProperty(value = "1-支付成功 2-退款成功 11-未支付 12-支付失败 13-退款失败", example = "1")
    private Byte payStatus;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
    private Date payTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    public Long getServiceItemId() {
        return serviceItemId;
    }

    public void setServiceItemId(Long serviceItemId) {
        this.serviceItemId = serviceItemId;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName == null ? null : serviceItemName.trim();
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public BigDecimal getReceivableAmount() {
        return receivableAmount;
    }

    public void setReceivableAmount(BigDecimal receivableAmount) {
        this.receivableAmount = receivableAmount;
    }

    public BigDecimal getActualAmount() {
        return actualAmount;
    }

    public void setActualAmount(BigDecimal actualAmount) {
        this.actualAmount = actualAmount;
    }

    public Integer getDays() {
        return days;
    }

    public void setDays(Integer days) {
        this.days = days;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Byte getNeedAudit() {
        return needAudit;
    }

    public void setNeedAudit(Byte needAudit) {
        this.needAudit = needAudit;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(Integer baseVersion) {
        this.baseVersion = baseVersion;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign == null ? null : organSign.trim();
    }

    public Byte getPayType() {
        return payType;
    }

    public void setPayType(Byte payType) {
        this.payType = payType;
    }

    public Byte getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(Byte payStatus) {
        this.payStatus = payStatus;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    @Override
    public String toString() {
        return "ConsultServiceOrderVo{" +
                "id=" + id +
                ", orderId='" + orderId + '\'' +
                ", serviceItemId=" + serviceItemId +
                ", serviceItemName='" + serviceItemName + '\'' +
                ", type=" + type +
                ", price=" + price +
                ", num=" + num +
                ", receivableAmount=" + receivableAmount +
                ", actualAmount=" + actualAmount +
                ", days=" + days +
                ", count=" + count +
                ", needAudit=" + needAudit +
                ", createUser='" + createUser + '\'' +
                ", createTime=" + createTime +
                ", updateUser='" + updateUser + '\'' +
                ", updateTime=" + updateTime +
                ", baseVersion=" + baseVersion +
                ", organSign='" + organSign + '\'' +
                ", payType=" + payType +
                ", payStatus=" + payStatus +
                ", payTime=" + payTime +
                '}';
    }
}