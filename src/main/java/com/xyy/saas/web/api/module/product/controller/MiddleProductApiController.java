package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.dto.ProductDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T19:34:49.364+08:00")

@Slf4j
@Controller
public class MiddleProductApiController implements MiddleProductApi {

    @Reference(version = "0.0.1")
    private com.xyy.saas.product.core.api.ProductApi productApi;

    @Override
    public ResponseEntity<ResultVO> addProductCorrection(HttpServletRequest request, @RequestBody ProductDto productDto) {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        log.info("addProductCorrection organSign:{}, employeeId:{}, param:{}", organSign, employee, JSON.toJSONString(productDto));
        productDto.setOrganSign(organSign);
        ResultVO<Boolean> resultVO = productApi.addProductCorrection2(productDto);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }
}
