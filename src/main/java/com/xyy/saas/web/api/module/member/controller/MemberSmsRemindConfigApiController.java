package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.member.core.api.MemberSmsRemindConfigApi;
import com.xyy.saas.member.core.dto.MemberSmsRemindConfigDto;
import com.xyy.saas.web.api.module.member.model.UpdateRemindConfigVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2020-01-10T11:16:06.377+08:00")
@Controller
@RequestMapping("/member/memberSmsRemindConfig")
@Api(value = "memberSmsRemindConfig", description = "the memberSmsRemindConfig API")
public class MemberSmsRemindConfigApiController {

    @Reference(version = "0.0.1")
    MemberSmsRemindConfigApi memberSmsRemindConfigApi;

    @ApiOperation(value = "短信设置查询", notes = "短信设置查询", response = JSONObject.class, tags={ "memberSmsSignnature", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = JSONObject.class) })
    @RequestMapping(value = "/selectConfigByOrganSign", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    public ResponseEntity<ResultVO> selectConfigByOrganSign(@RequestHeader("commonRequestModel") String commonRequestModelStr){
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String organSign = "";
        if(commonRequestModel.getBizModel() == 1){
            organSign = commonRequestModel.getOrganSign();
        }else{
            if(commonRequestModel.getOrganSignType() == 1){
                organSign = commonRequestModel.getHeadquartersOrganSign();
            }else{
                organSign = commonRequestModel.getOrganSign();
            }
        }
        MemberSmsRemindConfigDto dto = memberSmsRemindConfigApi.selectByOrganSign(organSign);
        return new ResponseEntity(ResultVO.createSuccess(dto), HttpStatus.OK);
    }

    @ApiOperation(value = "短信设置修改", notes = "短信设置修改", response = JSONObject.class, tags={ "memberSmsSignnature", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = JSONObject.class) })
    @RequestMapping(value = "/updateConfig", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    public ResponseEntity<ResultVO> updateConfig(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                 @RequestBody UpdateRemindConfigVo configVo){
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String organSign = commonRequestModel.getOrganSign();
        String employeeId = commonRequestModel.getEmployeeId();
        MemberSmsRemindConfigDto dto = new MemberSmsRemindConfigDto();
        BeanUtils.copyProperties(configVo, dto);
        dto.setCreateUser(employeeId);
        dto.setOrganSign(organSign);
        int result = memberSmsRemindConfigApi.save(dto);
        return new ResponseEntity(ResultVO.createSuccess(result), HttpStatus.OK);
    }

}
