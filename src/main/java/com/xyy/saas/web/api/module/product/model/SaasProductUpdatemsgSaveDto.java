package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

@ApiModel(description = "质量变更列表传参")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-07T16:41:52.348+08:00")
public class SaasProductUpdatemsgSaveDto implements Serializable {
    @ApiModelProperty(value = "商品编号")
    private String pref;
    @ApiModelProperty(value = "变更类型 1:供应商 0：商品")
    private Integer updateType;
    @ApiModelProperty(value = "申请时间")
    private String applyTime;
    private String commonName;

    private Object updateContent;

    @ApiModelProperty(value = "变更信息")
    @JsonProperty("list")
    List<SaasProductUpdatemsgDto> list;

    @ApiModelProperty(value = "变更类型 1:单个 2：批量")
    private Byte optType;

    @ApiModelProperty(value = "是否变更标准库id")
    private Boolean resetStandardIdFlag;

    public Object getUpdateContent() {
        return updateContent;
    }

    public void setUpdateContent(Object updateContent) {
        this.updateContent = updateContent;
    }

    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    public Integer getUpdateType() {
        return updateType;
    }

    public void setUpdateType(Integer updateType) {
        this.updateType = updateType;
    }

    public String getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(String applyTime) {
        this.applyTime = applyTime;
    }

    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    public List<SaasProductUpdatemsgDto> getList() {

        return list;
    }

    public void setList(List<SaasProductUpdatemsgDto> list) {
        this.list = list;
    }

    public Byte getOptType() {
        return optType;
    }

    public void setOptType(Byte optType) {
        this.optType = optType;
    }

    public Boolean getResetStandardIdFlag() {
        return resetStandardIdFlag;
    }

    public void setResetStandardIdFlag(Boolean resetStandardIdFlag) {
        this.resetStandardIdFlag = resetStandardIdFlag;
    }
}
