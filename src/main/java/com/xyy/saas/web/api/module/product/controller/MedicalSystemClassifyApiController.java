package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.medical.core.api.MedicalSystemClassifyApi;
import com.xyy.saas.medical.core.dto.MedicalSystemClassifyDto;
import com.xyy.saas.web.api.module.product.model.MedicalSystemClassify;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @title: MedicalSystemClassifyApiController
 * @date 2019-09-11  17:53
 * @description: TODO
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-09-11T15:28:18.933+08:00")
@Controller
public class MedicalSystemClassifyApiController implements MedicalSystemClassifyApiSwag {


    @Reference(version = "0.0.1")
    private MedicalSystemClassifyApi medicalSystemClassifyApi;

    @Override
    public ResultVO findMedicalSystemClassify(HttpServletRequest request, MedicalSystemClassify medicalSystemClassify) {
        MedicalSystemClassifyDto medicalSystemClassifyDto = new MedicalSystemClassifyDto();
        BeanUtils.copyProperties(medicalSystemClassify,medicalSystemClassifyDto);
        return ResultVO.createSuccess(medicalSystemClassifyApi.findMedicalSystemClassify(medicalSystemClassifyDto));
    }
}
