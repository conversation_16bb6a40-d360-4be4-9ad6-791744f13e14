package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * 会员级别
 * <AUTHOR>
 */
@ApiModel(description = "会员级别")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-06T17:16:06.377+08:00")
public class MemberLevel   {
  private Long id = null;

  private String name = null;

  private Long upLevelId = null;

  private BigDecimal upPoint = null;

  private Double discount = null;

  private Integer priceStrategy = null;

  private Integer status = null;

  private String createUser = null;

  private String createTime = null;

  private String updateUser = null;

  private String updateTime = null;

  private Integer yn = null;

  private Integer baseVersion = null;

  private String organsign = null;

  private Integer isSpecial;


  public MemberLevel id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * id
   * @return id
  **/
  @ApiModelProperty(value = "id")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public MemberLevel name(String name) {
    this.name = name;
    return this;
  }

   /**
   * 级别名称
   * @return name
  **/
  @ApiModelProperty(value = "级别名称  必传")
  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public MemberLevel upLevelId(Long upLevelId) {
    this.upLevelId = upLevelId;
    return this;
  }

   /**
   * 上级等级id 0 为最高级
   * @return upLevelId
  **/
  @ApiModelProperty(value = "上级等级id 0 为最高级  必传")
  public Long getUpLevelId() {
    return upLevelId;
  }

  public void setUpLevelId(Long upLevelId) {
    this.upLevelId = upLevelId;
  }

  public MemberLevel upPoint(BigDecimal upPoint) {
    this.upPoint = upPoint;
    return this;
  }

   /**
   * 升级积分
   * @return upPoint
  **/
  @ApiModelProperty(value = "升级积分  必传")
  public BigDecimal getUpPoint() {
    return upPoint;
  }

  public void setUpPoint(BigDecimal upPoint) {
    this.upPoint = upPoint;
  }

  public MemberLevel discount(Double discount) {
    this.discount = discount;
    return this;
  }

   /**
   * 折扣率 
   * @return discount
  **/
  @ApiModelProperty(value = "折扣率 必传")
  public Double getDiscount() {
    return discount;
  }

  public void setDiscount(Double discount) {
    this.discount = discount;
  }


  public MemberLevel priceStrategy(Integer priceStrategy) {
    this.priceStrategy = priceStrategy;
    return this;
  }

   /**
   * 价格策略 1 零售价 2 会员价
   * @return priceStrategy
  **/
  @ApiModelProperty(value = "价格策略 1 零售价 2 会员价  必传")


  public Integer getPriceStrategy() {
    return priceStrategy;
  }

  public void setPriceStrategy(Integer priceStrategy) {
    this.priceStrategy = priceStrategy;
  }

  public MemberLevel status(Integer status) {
    this.status = status;
    return this;
  }

   /**
   * 状态 启用 禁用 0 禁用 1 启用
   * @return status
  **/
  @ApiModelProperty(value = "状态   0 禁用 1 启用  必传")
  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public MemberLevel createUser(String createUser) {
    this.createUser = createUser;
    return this;
  }

   /**
   * 创建人
   * @return createUser
  **/
  @ApiModelProperty(value = "创建人  非必传")


  public String getCreateUser() {
    return createUser;
  }

  public void setCreateUser(String createUser) {
    this.createUser = createUser;
  }

  public MemberLevel createTime(String createTime) {
    this.createTime = createTime;
    return this;
  }

   /**
   * 创建时间
   * @return createTime
  **/
  @ApiModelProperty(value = "创建时间  非必传")
  public String getCreateTime() {
    return createTime;
  }

  public void setCreateTime(String createTime) {
    this.createTime = createTime;
  }

  public MemberLevel updateUser(String updateUser) {
    this.updateUser = updateUser;
    return this;
  }

   /**
   * 更新人
   * @return updateUser
  **/
  @ApiModelProperty(value = "更新人  非必传")
  public String getUpdateUser() {
    return updateUser;
  }

  public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
  }

  public MemberLevel updateTime(String updateTime) {
    this.updateTime = updateTime;
    return this;
  }

   /**
   * 更新时间
   * @return updateTime
  **/
  @ApiModelProperty(value = "更新时间  非必传")
  public String getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(String updateTime) {
    this.updateTime = updateTime;
  }

  public MemberLevel yn(Integer yn) {
    this.yn = yn;
    return this;
  }

   /**
   * 是否删除 1 有效 0 删除
   * @return yn
  **/
  @ApiModelProperty(value = "是否删除 1 有效 0 删除  必传")
  public Integer getYn() {
    return yn;
  }

  public void setYn(Integer yn) {
    this.yn = yn;
  }

  public MemberLevel baseVersion(Integer baseVersion) {
    this.baseVersion = baseVersion;
    return this;
  }

   /**
   * 版本号
   * @return baseVersion
  **/
  @ApiModelProperty(value = "版本号  非必传")
  public Integer getBaseVersion() {
    return baseVersion;
  }

  public void setBaseVersion(Integer baseVersion) {
    this.baseVersion = baseVersion;
  }

  public MemberLevel organsign(String organsign) {
    this.organsign = organsign;
    return this;
  }

   /**
   * 机构标识
   * @return organsign
  **/
  @ApiModelProperty(value = "机构标识")
  public String getOrgansign() {
    return organsign;
  }

  public void setOrgansign(String organsign) {
    this.organsign = organsign;
  }


  /**
   * 机构标识
   * @return organsign
   **/
  @ApiModelProperty(value = "特价会员价 0否 1是")
  public Integer getIsSpecial() {
    return isSpecial;
  }

  public void setIsSpecial(Integer isSpecial) {
    this.isSpecial = isSpecial;
  }
}

