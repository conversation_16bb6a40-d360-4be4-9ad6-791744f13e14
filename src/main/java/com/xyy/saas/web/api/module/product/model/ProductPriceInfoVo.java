package com.xyy.saas.web.api.module.product.model;

import java.math.BigDecimal;

/**
 * @ClassName ProductPriceInfoVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/1/14 12:50
 * @Version 1.0
 **/
public class ProductPriceInfoVo {

    private Long id;
    private String pharmacyPref;//"药店商品编号",
    private String commonName;//"商品通用名称",
    private String productName;//":"商品名称",
    private String attributeSpecification;//":"规格",
    private String jixingName;//":"剂型名称",
    private String unitName;//":"单位名称",
    private String manufacturer;//":"生产厂家",
    private String producingArea;//":"产地",
    private BigDecimal retailPrice;//":"原零售价",
    private BigDecimal vipPrice;//":"原会员价",
    private String organSignName;//":"部门名称",
    private String productTypeName;//":"自定义分类名称",
    private String systemTypeName;//":"商品分类名称",

    public String getPharmacyPref() {
        return pharmacyPref;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setPharmacyPref(String pharmacyPref) {
        this.pharmacyPref = pharmacyPref;
    }

    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getAttributeSpecification() {
        return attributeSpecification;
    }

    public void setAttributeSpecification(String attributeSpecification) {
        this.attributeSpecification = attributeSpecification;
    }

    public String getJixingName() {
        return jixingName;
    }

    public void setJixingName(String jixingName) {
        this.jixingName = jixingName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getProducingArea() {
        return producingArea;
    }

    public void setProducingArea(String producingArea) {
        this.producingArea = producingArea;
    }

    public BigDecimal getRetailPrice() {
        return retailPrice;
    }

    public void setRetailPrice(BigDecimal retailPrice) {
        this.retailPrice = retailPrice;
    }

    public BigDecimal getVipPrice() {
        return vipPrice;
    }

    public void setVipPrice(BigDecimal vipPrice) {
        this.vipPrice = vipPrice;
    }

    public String getOrganSignName() {
        return organSignName;
    }

    public void setOrganSignName(String organSignName) {
        this.organSignName = organSignName;
    }

    public String getProductTypeName() {
        return productTypeName;
    }

    public void setProductTypeName(String productTypeName) {
        this.productTypeName = productTypeName;
    }

    public String getSystemTypeName() {
        return systemTypeName;
    }

    public void setSystemTypeName(String systemTypeName) {
        this.systemTypeName = systemTypeName;
    }
}
