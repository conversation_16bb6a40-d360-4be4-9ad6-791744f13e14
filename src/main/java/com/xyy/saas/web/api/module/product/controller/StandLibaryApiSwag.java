package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.match.dto.MatchProductVoDto;
import com.xyy.saas.product.core.dto.StandardLibaryProductVoDto;
import io.swagger.annotations.*;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @title: MedicalProductApiSwag
 * @date 2019-12-19  16:43
 * @description: 商品标准库API
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T17:19:18.933+08:00")
@Api(value = "StandLibaryApiSwag", description = "商品与标准库相关API接口")
@RequestMapping("/product")
public interface StandLibaryApiSwag {

    /**
     * 依据条码查询标准库id 并判断匹配后的标准库id是否已存在，若不存在，则返回匹配到的标准库信息 V4.1.3
     * @param barCode 条码
     * @return
     */
    @ApiOperation(value = "扫码枪触发商品匹配", notes = "扫码枪触发商品匹配", response = ResultVO.class, tags={ "商品与标准库相关API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ApiImplicitParams({
            @ApiImplicitParam(name="organSign", value="机构标识", required=true, paramType="header", dataType = "string")
    })
    @ResponseBody
    @RequestMapping(value = "/standLibary/matchStandLibrary",method = RequestMethod.POST)
    public ResultVO matchStandLibrary(HttpServletRequest request, @Valid @RequestBody StandardLibaryProductVoDto voDto);

    /**
     * 手动批量数据匹配
     *
     * @param organSign
     * @return
     */
    @ApiOperation(value = "手动批量数据匹配", notes = "手动批量数据匹配", response = ResultVO.class, tags = {"商品与标准库相关API接口",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class)})
    @ApiImplicitParams({
            @ApiImplicitParam(name = "organSign", value = "机构标识", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "isProductHidden", value = "用户分组标识", required = true, paramType = "query", dataType = "byte")
    })
    @ResponseBody
    @RequestMapping(value = "/standLibary/banchMatchStandLibrary", method = RequestMethod.POST)
    public ResultVO banchMatchStandLibrary(HttpServletRequest request, @Valid @RequestBody MatchProductVoDto matchProductVoDto);

    /**
     * 分页查询匹配成功的标准库信息
     * @param organSign
     * @param rows
     * @param page
     * @return
     */
    @ApiOperation(value = "分页查询匹配成功的标准库信息", notes = "分页查询匹配成功的标准库信息", response = ResultVO.class, tags={ "商品与标准库相关API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ApiImplicitParams({
            @ApiImplicitParam(name="organSign", value="机构标识", required=true, paramType="header", dataType = "string"),
            @ApiImplicitParam(name="rows",value = "每页行数" ,required=true, paramType = "query" ,dataType = "integer"),
            @ApiImplicitParam(name="page",value = "页码" ,required=true, paramType = "query" ,dataType = "integer")
    })
    @ResponseBody
    @RequestMapping(value = "/standLibary/queryMatchedSuccessPage",method = RequestMethod.POST)
    public ResultVO  queryMatchedSuccessPage(HttpServletRequest request, @RequestParam Integer rows, @RequestParam Integer page);

    /**
     * 手动删除匹配成功的数据
     * @param id
     * @param organSign
     * @return
     */
    @ApiOperation(value = "手动删除匹配成功的数据", notes = "手动删除匹配成功的数据", response = ResultVO.class, tags={ "商品与标准库相关API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ApiImplicitParams({
            @ApiImplicitParam(name="organSign", value="机构标识", required=true, paramType="header", dataType = "string"),
            @ApiImplicitParam(name="id",value = "商品id" ,required=true, paramType = "query" ,dataType = "int")
    })
    @ResponseBody
    @RequestMapping(value = "/standLibary/deleteMatchedSuccess",method = RequestMethod.POST)
    public ResultVO deleteMatchedSuccess(HttpServletRequest request, Long id);

    /**
     * 保存匹配成功的结果
     * @param organSign
     * @return
     */
    @ApiOperation(value = "提交匹配成功的匹配结果", notes = "提交匹配成功的匹配结果", response = ResultVO.class, tags={ "商品与标准库相关API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ApiImplicitParams({
            @ApiImplicitParam(name="organSign", value="机构标识", required=true, paramType="header", dataType = "string")
    })
    @ResponseBody
    @RequestMapping(value = "/standLibary/saveMatchedSuccess",method = RequestMethod.POST)
    public ResultVO saveMatchedSuccess(HttpServletRequest request);
}
