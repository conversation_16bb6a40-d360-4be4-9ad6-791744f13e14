package com.xyy.saas.web.api.module.product.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * @ClassName SystemDictVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/8/27 10:55
 * @Version 1.0
 **/
@ApiModel(description = "商品字典结果返回信息类")
public class SystemDictVo {
    //数据字典id
    private Integer id;
    //业务类型
    private Integer bussinessId;
    //数据字典名称，用作展示
    private String name;
    //创建人
    private Integer createUser;
    //创建时间
    private Date createTime;
    //是否排序
    private Integer sort;
    //值
    private Integer value;
    //是否系统类型
    private Byte systemYn;
    //字典描述
    private String description;
    //机构号
    private String organSign;
    //是否被删除
    private Byte yn;
    //是否审核通过
    private Byte status;
    //版本号
    private Long baseVersion;
    //排序字段
    private String sidx;
    //排序字段
    private String sord;

    @ApiModelProperty(value = "数据字典id")
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @ApiModelProperty(value = "业务类型")
    public Integer getBussinessId() {
        return bussinessId;
    }

    public void setBussinessId(Integer bussinessId) {
        this.bussinessId = bussinessId;
    }

    @ApiModelProperty(value = "字典名称，用作展示")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @ApiModelProperty(value = "创建人")
    public Integer getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Integer createUser) {
        this.createUser = createUser;
    }

    @ApiModelProperty(value = "创建时间")
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @ApiModelProperty(value = "排序字段")
    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    @ApiModelProperty(value = "值")
    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    @ApiModelProperty(value = "是否系统类型")
    public Byte getSystemYn() {
        return systemYn;
    }

    public void setSystemYn(Byte systemYn) {
        this.systemYn = systemYn;
    }

    @ApiModelProperty(value = "描述")
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @ApiModelProperty(value = "机构号")
    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    @ApiModelProperty(value = "是否被删除")
    public Byte getYn() {
        return yn;
    }

    public void setYn(Byte yn) {
        this.yn = yn;
    }

    @ApiModelProperty(value = "状态")
    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    @ApiModelProperty(value = "版本号")
    public Long getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(Long baseVersion) {
        this.baseVersion = baseVersion;
    }

    @ApiModelProperty(value = "排序字段")
    public String getSidx() {
        return sidx;
    }

    public void setSidx(String sidx) {
        this.sidx = sidx;
    }

    @ApiModelProperty(value = "排序字段")
    public String getSord() {
        return sord;
    }

    public void setSord(String sord) {
        this.sord = sord;
    }

}
