/**
 * NOTE: This class is auto generated by the swagger code generator program (2.2.3).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.web.api.module.product.model.InventoryIncomeStatementVo;
import com.xyy.saas.web.api.module.product.model.InventoryIncomeStatementVoList;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-30T11:13:55.467+08:00")
@RequestMapping("/product")
@Api(value = "findIncomeStatementVos", description = "the findIncomeStatementVos API")
public interface FindIncomeStatementVosApi {

    @ApiOperation(value = "查询所有报损报溢单", notes = "查询所有报损报溢单", response = InventoryIncomeStatementVoList.class, tags={ "InventoryIncomeStatementVo", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "操作成功", response = InventoryIncomeStatementVoList.class) })
    
    @RequestMapping(value = "/findIncomeStatementVos",
        produces = { "application/xml", "application/json" }, 
        consumes = { "application/json", "application/xml" },
        method = RequestMethod.POST)
    ResponseEntity<InventoryIncomeStatementVoList> findIncomeStatementVos(@ApiParam(value = "报损报溢单Vo", required = true) @Valid @RequestBody InventoryIncomeStatementVo inventoryIncomeStatementVo);

}
