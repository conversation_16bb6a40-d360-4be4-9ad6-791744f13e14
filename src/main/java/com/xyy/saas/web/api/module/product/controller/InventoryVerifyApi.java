package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.inventory.core.dto.InventoryVerifyVo;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-01-28T15:28:18.933+08:00")
@RequestMapping("/product/inventoryVerify")
@Api(value = "InventoryVerifyApi", description = "the InventoryVerify API")
public interface InventoryVerifyApi {

    /**
     * 保存盘点结果-2019年1月29日18:20:01
     * @param vo
     * @return
     */
    @ApiOperation(value = "保存盘点结果", notes = "保存盘点结果", response = Boolean.class, tags = {"InventoryVerify", })
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/saveInventoryVerify",method = RequestMethod.POST)
    @ResponseBody
    ResponseEntity<ResultVO> saveInventoryVerify(@ApiParam(value = "盘点确认单信息", required = true) @Valid @RequestBody InventoryVerifyVo vo);
}
