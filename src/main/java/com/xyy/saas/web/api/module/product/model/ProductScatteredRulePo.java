package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.Objects;

/**
 * 商品拆零规则信息实体
 */
@ApiModel(description = "商品拆零规则信息实体")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T15:07:38.324+08:00")

public class ProductScatteredRulePo   {
  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("productPref")
  private String productPref = null;

  @JsonProperty("organSign")
  private String organSign = null;

  @JsonProperty("mixedQuery")
  private String mixedQuery = null;

  @JsonProperty("scatteredNumber")
  private Integer scatteredNumber = null;

  @JsonProperty("mnemonicCode")
  private String mnemonicCode = null;

  @JsonProperty("scatteredSpecification")
  private String scatteredSpecification = null;

  @JsonProperty("unitId")
  private Integer unitId = null;

  @JsonProperty("status")
  private Byte status = null;

  @JsonProperty("remark")
  private String remark = null;

  @JsonProperty("createUser")
  private String createUser = null;

  @JsonProperty("createTime")
  private Date createTime = null;

  @JsonProperty("updateUser")
  private String updateUser = null;

  @JsonProperty("updateTime")
  private Date updateTime = null;

  @JsonProperty("yn")
  private Byte yn = null;

  @JsonProperty("baseVersion")
  private String baseVersion = null;

  public ProductScatteredRulePo id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * 商品id
   * @return id
  **/
  @ApiModelProperty(value = "商品id")


  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public ProductScatteredRulePo productPref(String productPref) {
    this.productPref = productPref;
    return this;
  }

   /**
   * 商品编号
   * @return productPref
  **/
  @ApiModelProperty(value = "商品编号")


  public String getProductPref() {
    return productPref;
  }

  public void setProductPref(String productPref) {
    this.productPref = productPref;
  }

  public ProductScatteredRulePo organSign(String organSign) {
    this.organSign = organSign;
    return this;
  }

   /**
   * 药店唯一标识    商品编号+药店唯一标识为该药店商品的唯一标识
   * @return organSign
  **/
  @ApiModelProperty(value = "药店唯一标识    商品编号+药店唯一标识为该药店商品的唯一标识")


  public String getOrganSign() {
    return organSign;
  }

  public void setOrganSign(String organSign) {
    this.organSign = organSign;
  }

  public ProductScatteredRulePo mixedQuery(String mixedQuery) {
    this.mixedQuery = mixedQuery;
    return this;
  }

   /**
   * 混合查询
   * @return mixedQuery
  **/
  @ApiModelProperty(value = "混合查询")


  public String getMixedQuery() {
    return mixedQuery;
  }

  public void setMixedQuery(String mixedQuery) {
    this.mixedQuery = mixedQuery;
  }

  public ProductScatteredRulePo scatteredNumber(Integer scatteredNumber) {
    this.scatteredNumber = scatteredNumber;
    return this;
  }

   /**
   * 拆零数量 
   * @return scatteredNumber
  **/
  @ApiModelProperty(value = "拆零数量 ")


  public Integer getScatteredNumber() {
    return scatteredNumber;
  }

  public void setScatteredNumber(Integer scatteredNumber) {
    this.scatteredNumber = scatteredNumber;
  }

  public ProductScatteredRulePo mnemonicCode(String mnemonicCode) {
    this.mnemonicCode = mnemonicCode;
    return this;
  }

   /**
   * 助记码
   * @return mnemonicCode
  **/
  @ApiModelProperty(value = "助记码")


  public String getMnemonicCode() {
    return mnemonicCode;
  }

  public void setMnemonicCode(String mnemonicCode) {
    this.mnemonicCode = mnemonicCode;
  }

  public ProductScatteredRulePo scatteredSpecification(String scatteredSpecification) {
    this.scatteredSpecification = scatteredSpecification;
    return this;
  }

   /**
   * 拆零规格
   * @return scatteredSpecification
  **/
  @ApiModelProperty(value = "拆零规格")


  public String getScatteredSpecification() {
    return scatteredSpecification;
  }

  public void setScatteredSpecification(String scatteredSpecification) {
    this.scatteredSpecification = scatteredSpecification;
  }

  public ProductScatteredRulePo unitId(Integer unitId) {
    this.unitId = unitId;
    return this;
  }

   /**
   * 拆零单位
   * @return unitId
  **/
  @ApiModelProperty(value = "拆零单位")


  public Integer getUnitId() {
    return unitId;
  }

  public void setUnitId(Integer unitId) {
    this.unitId = unitId;
  }

  public ProductScatteredRulePo status(Byte status) {
    this.status = status;
    return this;
  }

   /**
   * 是否启用，1--启用，0--不启用
   * @return status
  **/
  @ApiModelProperty(value = "是否启用，1--启用，0--不启用")


  public Byte getStatus() {
    return status;
  }

  public void setStatus(Byte status) {
    this.status = status;
  }

  public ProductScatteredRulePo remark(String remark) {
    this.remark = remark;
    return this;
  }

   /**
   * 备注
   * @return remark
  **/
  @ApiModelProperty(value = "备注")


  public String getRemark() {
    return remark;
  }

  public void setRemark(String remark) {
    this.remark = remark;
  }

  public ProductScatteredRulePo createUser(String createUser) {
    this.createUser = createUser;
    return this;
  }

   /**
   * 创建人
   * @return createUser
  **/
  @ApiModelProperty(value = "创建人")


  public String getCreateUser() {
    return createUser;
  }

  public void setCreateUser(String createUser) {
    this.createUser = createUser;
  }

  public ProductScatteredRulePo createTime(Date createTime) {
    this.createTime = createTime;
    return this;
  }

   /**
   * 创建时间
   * @return createTime
  **/
  @ApiModelProperty(value = "创建时间")


  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public ProductScatteredRulePo updateUser(String updateUser) {
    this.updateUser = updateUser;
    return this;
  }

   /**
   * 更新人
   * @return updateUser
  **/
  @ApiModelProperty(value = "更新人")


  public String getUpdateUser() {
    return updateUser;
  }

  public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
  }

  public ProductScatteredRulePo updateTime(Date updateTime) {
    this.updateTime = updateTime;
    return this;
  }

   /**
   * 更新时间
   * @return updateTime
  **/
  @ApiModelProperty(value = "更新时间")


  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public ProductScatteredRulePo yn(Byte yn) {
    this.yn = yn;
    return this;
  }

   /**
   * 逻辑删除 1 有效 0 删除
   * @return yn
  **/
  @ApiModelProperty(value = "逻辑删除 1 有效 0 删除")


  public Byte getYn() {
    return yn;
  }

  public void setYn(Byte yn) {
    this.yn = yn;
  }

  public ProductScatteredRulePo baseVersion(String baseVersion) {
    this.baseVersion = baseVersion;
    return this;
  }

   /**
   * 操作版本号
   * @return baseVersion
  **/
  @ApiModelProperty(value = "操作版本号")


  public String getBaseVersion() {
    return baseVersion;
  }

  public void setBaseVersion(String baseVersion) {
    this.baseVersion = baseVersion;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ProductScatteredRulePo productScatteredRulePo = (ProductScatteredRulePo) o;
    return Objects.equals(this.id, productScatteredRulePo.id) &&
        Objects.equals(this.productPref, productScatteredRulePo.productPref) &&
        Objects.equals(this.organSign, productScatteredRulePo.organSign) &&
        Objects.equals(this.mixedQuery, productScatteredRulePo.mixedQuery) &&
        Objects.equals(this.scatteredNumber, productScatteredRulePo.scatteredNumber) &&
        Objects.equals(this.mnemonicCode, productScatteredRulePo.mnemonicCode) &&
        Objects.equals(this.scatteredSpecification, productScatteredRulePo.scatteredSpecification) &&
        Objects.equals(this.unitId, productScatteredRulePo.unitId) &&
        Objects.equals(this.status, productScatteredRulePo.status) &&
        Objects.equals(this.remark, productScatteredRulePo.remark) &&
        Objects.equals(this.createUser, productScatteredRulePo.createUser) &&
        Objects.equals(this.createTime, productScatteredRulePo.createTime) &&
        Objects.equals(this.updateUser, productScatteredRulePo.updateUser) &&
        Objects.equals(this.updateTime, productScatteredRulePo.updateTime) &&
        Objects.equals(this.yn, productScatteredRulePo.yn) &&
        Objects.equals(this.baseVersion, productScatteredRulePo.baseVersion);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, productPref, organSign, mixedQuery, scatteredNumber, mnemonicCode, scatteredSpecification, unitId, status, remark, createUser, createTime, updateUser, updateTime, yn, baseVersion);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ProductScatteredRulePo {\n");

    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    productPref: ").append(toIndentedString(productPref)).append("\n");
    sb.append("    organSign: ").append(toIndentedString(organSign)).append("\n");
    sb.append("    mixedQuery: ").append(toIndentedString(mixedQuery)).append("\n");
    sb.append("    scatteredNumber: ").append(toIndentedString(scatteredNumber)).append("\n");
    sb.append("    mnemonicCode: ").append(toIndentedString(mnemonicCode)).append("\n");
    sb.append("    scatteredSpecification: ").append(toIndentedString(scatteredSpecification)).append("\n");
    sb.append("    unitId: ").append(toIndentedString(unitId)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    remark: ").append(toIndentedString(remark)).append("\n");
    sb.append("    createUser: ").append(toIndentedString(createUser)).append("\n");
    sb.append("    createTime: ").append(toIndentedString(createTime)).append("\n");
    sb.append("    updateUser: ").append(toIndentedString(updateUser)).append("\n");
    sb.append("    updateTime: ").append(toIndentedString(updateTime)).append("\n");
    sb.append("    yn: ").append(toIndentedString(yn)).append("\n");
    sb.append("    baseVersion: ").append(toIndentedString(baseVersion)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

