package com.xyy.saas.web.api.module.product.model;

import java.util.List;

/**
 * @ClassName AdjustDetailInfoVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/1/14 14:57
 * @Version 1.0
 **/
public class AdjustDetailInfoVo {

    private Long id;//调价单id
    private String pref;//":"单据编号",
    private String applicableStores;//":"适用门店编号，以逗号隔开,全部门店是all",
    private String applicableStoresName;//":"适用门店名称，以逗号隔开，作为前端显示",
    private String createUserName;//"://"创建人名称",
    private String createTimeStr;//":"创建时间，格式yyyy-MM-dd HH:mm:ss",
    private String adjustReason;//":"调价原因",
    private Integer aproveStatus;//":"审批状态，0全部，1审批中，1已通过，2已驳回",
    private List<AdjustDetailVo> list;//调价详情信息

    public Byte getCanApprove() {
        return canApprove;
    }

    public void setCanApprove(Byte canApprove) {
        this.canApprove = canApprove;
    }

    private Byte canApprove;//1代表可以审核，2代表不可审核

    public int getBusinessScene() {
        return businessScene;
    }

    public void setBusinessScene(int businessScene) {
        this.businessScene = businessScene;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    private int businessScene;//业务类型
    private String taskId;//任务id


    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    public String getApplicableStores() {
        return applicableStores;
    }

    public void setApplicableStores(String applicableStores) {
        this.applicableStores = applicableStores;
    }

    public String getApplicableStoresName() {
        return applicableStoresName;
    }

    public void setApplicableStoresName(String applicableStoresName) {
        this.applicableStoresName = applicableStoresName;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getCreateTimeStr() {
        return createTimeStr;
    }

    public void setCreateTimeStr(String createTimeStr) {
        this.createTimeStr = createTimeStr;
    }

    public String getAdjustReason() {
        return adjustReason;
    }

    public void setAdjustReason(String adjustReason) {
        this.adjustReason = adjustReason;
    }

    public Integer getAproveStatus() {
        return aproveStatus;
    }

    public void setAproveStatus(Integer aproveStatus) {
        this.aproveStatus = aproveStatus;
    }
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public List<AdjustDetailVo> getList() {
        return list;
    }

    public void setList(List<AdjustDetailVo> list) {
        this.list = list;
    }
}
