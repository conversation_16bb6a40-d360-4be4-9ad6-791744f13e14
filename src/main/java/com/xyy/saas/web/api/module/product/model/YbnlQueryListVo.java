package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/7/1 14:30
 * @Version 1.0
 */
@Data
@ApiModel(description = "医保目录列表查询参数类")
public class YbnlQueryListVo implements Serializable {

    private static final long serialVersionUID = 9206432446835736472L;
    @JsonProperty("mixQuery")
    private String mixQuery;

    @JsonProperty("nationalDrugCode")
    private String nationalDrugCode;

    @JsonProperty("areaDrugCode")
    private String areaDrugCode;

    @JsonProperty("organSign")
    private String organSign;

    /**
     * 医保药品，不传：全部，0：否，1：是
     */
    @JsonProperty("medicalInsurance")
    private Integer medicalInsurance;

    /**
     * 商品分类
     */
    @JsonProperty("systemType")
    private Integer systemType;

    /**
     * 匹配状态, 不传：全部，0：未匹配，1：精确匹配，2：模糊匹配
     */
    @JsonProperty("matchStatus")
    private Integer matchStatus;

    @JsonProperty("pageNum")
    private Integer pageNum;

    @JsonProperty("pageSize")
    private Integer pageSize;

    @JsonProperty("stockNumberType")
    private String stockNumberType;

    @JsonProperty("invalidCode")
    private Byte invalidCode;

    /**
     * 商品审核状态：1--审核通过，2--审核中，3,--审核未通过
     */
    @JsonProperty("productStatus")
    private Byte productStatus;
}
