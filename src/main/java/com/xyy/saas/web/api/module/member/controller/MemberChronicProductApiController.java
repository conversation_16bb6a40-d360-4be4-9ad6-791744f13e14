package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.member.core.api.MemberChronicProductApi;
import com.xyy.saas.member.core.dto.*;
import com.xyy.saas.member.core.response.ResultVO;
import com.xyy.saas.product.core.enums.DrugstoreTypeProductEnum;
import com.xyy.saas.web.api.module.member.model.*;
import com.xyy.saas.web.api.module.member.service.DrugstoreService;
import com.xyy.saas.web.api.module.product.util.DrugstoreTypeUtil;
import com.xyy.saas.web.api.module.utils.StringUtils;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description 会员慢病种类关联药品API
 * <AUTHOR>
 * @Create 2020-09-24 16:15
 * @menu 会员慢病种类关联药品API
 */
@Slf4j
@Controller
@RequestMapping(value = "/member/memberChronicProduct")
@Api(value = "memberChronicProduct", description = "会员慢病种类关联药品API")
public class MemberChronicProductApiController {

    @Reference(version = "0.0.1")
    private MemberChronicProductApi memberChronicProductApi;

    @Autowired
    private DrugstoreService drugstoreService;

    /**
     * @param commonRequestModelStr 机构信息
     * @param configProductReqVo    实体VO信息
     */
    @ApiOperation(value = "配置慢病种类关联药品", notes = "配置慢病种类关联药品", response = Boolean.class, tags = {"会员慢病种类关联药品",})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/editChronicConfigProduct",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> editChronicConfigProduct(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                             @RequestBody MemberChronicConfigProductReqVo configProductReqVo) {
        CommonRequestModel model = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String employeeId = model.getEmployeeId();
        String organSign = model.getOrganSign();
        //当前操作机构(配置慢病种类关联药品-权限管控--单体、联营门店、连锁总部)
        ResponseEntity<ResultVO> operResponseEntity = checkChronicProductOper(model.getOrganSign());
        if (operResponseEntity != null) {
            return operResponseEntity;
        }
        if (configProductReqVo == null) {
            return new ResponseEntity(new ResultVO(-1, "参数不能为空", false), HttpStatus.OK);
        }
        MemberChronicConfigProductSaveReqDto saveReqDto = new MemberChronicConfigProductSaveReqDto();
        saveReqDto.setOrganSign(organSign);
        //添加删除
        List<Long> del = configProductReqVo.getDelMemberChronicConfigProductIdList();
        if (del != null && del.size() > 0) {
            List<MemberChronicProductSaveDto> delList = Lists.newArrayList();
            for (Long delId : del) {
                MemberChronicProductSaveDto delDto = new MemberChronicProductSaveDto();
                delDto.setId(delId);
                delDto.setUpdateUser(Integer.parseInt(employeeId));
                delDto.setCreateTime(System.currentTimeMillis());
                delDto.setOrganSign(organSign);
                delList.add(delDto);
            }
            saveReqDto.setDelMemberChronicConfigProductIdList(delList);
        }
        //编辑或添加
        List<MemberChronicProductSaveVo> add = configProductReqVo.getAddMemberChronicConfigProductList();
        if (add != null && add.size() > 0) {
            List<MemberChronicProductSaveDto> addList = Lists.newArrayList();
            List<MemberChronicProductSaveDto> updateList = Lists.newArrayList();
            for (MemberChronicProductSaveVo saveVo : add) {
                if (saveVo.getId() != null && saveVo.getId() > 0) {
                    MemberChronicProductSaveDto saveDto = new MemberChronicProductSaveDto();
                    BeanUtils.copyProperties(saveVo, saveDto);
                    saveDto.setUpdateUser(Integer.parseInt(employeeId));
                    saveDto.setCreateTime(System.currentTimeMillis());
                    saveDto.setOrganSign(organSign);
                    updateList.add(saveDto);
                }else{
                    MemberChronicProductSaveDto updateDto = new MemberChronicProductSaveDto();
                    BeanUtils.copyProperties(saveVo, updateDto);
                    updateDto.setCreateUser(Integer.parseInt(employeeId));
                    updateDto.setCreateTime(System.currentTimeMillis());
                    updateDto.setOrganSign(organSign);
                    addList.add(updateDto);
                }

            }
            saveReqDto.setAddMemberChronicConfigProductList(addList);
            saveReqDto.setUpdateMemberChronicConfigProductList(updateList);
        }
        boolean configProduct = memberChronicProductApi.editChronicConfigProduct(saveReqDto);
        if (configProduct) {
            return new ResponseEntity(new ResultVO(0, "操作成功", true), HttpStatus.OK);
        }
        return new ResponseEntity(new ResultVO(-1, "操作失败", false), HttpStatus.OK);
    }

    /**
     * @param commonRequestModelStr 机构信息
     * @param queryVo               查询条件
     */
    @ApiOperation(value = "列表查询会员慢病种类关联药品", notes = "列表查询会员慢病种类关联药品", response = MemberChronicProductVo.class, tags = {"会员慢病种类关联药品",})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberChronicProductVo.class)})
    @RequestMapping(value = "/queryChronicProductList",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> queryChronicProductList(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                            @RequestBody MemberChronicProductQueryReqVo queryVo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        if(queryVo == null ){
            return new ResponseEntity(new ResultVO(-1, "参数不能为空", false), HttpStatus.OK);
        }
        if(StringUtils.isEmpty(queryVo.getChronicPref())){
            return new ResponseEntity(new ResultVO(-1, "参数慢病编号不能为空", false), HttpStatus.OK);
        }
        MemberChronicProductQueryReqDto queryDto = new MemberChronicProductQueryReqDto();
        BeanUtils.copyProperties(queryVo, queryDto);
        queryDto.setOrganSign(commonRequestModel.getOrganSign());
        return new ResponseEntity(ResultVO.createSuccess(memberChronicProductApi.queryChronicProductList(null, queryDto)), HttpStatus.OK);
    }

    /**
     * @param commonRequestModelStr 机构信息
     * @param queryVo               查询条件
     */
    @ApiOperation(value = "分页查询列表会员慢病种类关联药品", notes = "分页查询列表会员慢病种类关联药品", response = MemberChronicProductVo.class, tags = {"会员慢病种类关联药品",})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberChronicProductVo.class)})
    @RequestMapping(value = "/queryPageChronicProductList",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> queryPageChronicProductList(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                                @RequestBody MemberChronicProductQueryReqVo queryVo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        if(queryVo == null ){
            return new ResponseEntity(new ResultVO(-1, "参数不能为空", false), HttpStatus.OK);
        }
        if(StringUtils.isEmpty(queryVo.getChronicPref())){
            return new ResponseEntity(new ResultVO(-1, "参数慢病编号不能为空", false), HttpStatus.OK);
        }
        MemberChronicProductQueryReqDto queryDto = new MemberChronicProductQueryReqDto();
        BeanUtils.copyProperties(queryVo, queryDto);
        queryDto.setOrganSign(commonRequestModel.getOrganSign());
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(queryVo.getPageNum());
        pageInfo.setPageSize(queryVo.getPageSize());
        PageInfo<MemberChronicProductDto> memberChronicProductDtoPageInfo = memberChronicProductApi.queryChronicProductList(pageInfo, queryDto);
        return new ResponseEntity(ResultVO.createSuccess(memberChronicProductDtoPageInfo), HttpStatus.OK);
    }

    /**
     * @param commonRequestModelStr 机构信息
     * @param queryVo               查询条件
     */
    @ApiOperation(value = "分页查询待选中的商品列表", notes = "分页查询待选中的商品列表", response = MemberChronicProductVo.class, tags = {"会员慢病种类关联药品",})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberChronicProductVo.class)})
    @RequestMapping(value = "/selectProductList",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> selectProductList(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                      @RequestBody MemberChronicProductSelectListParamVo queryVo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        if(queryVo == null ){
            return new ResponseEntity(new ResultVO(-1, "参数不能为空", false), HttpStatus.OK);
        }
        if(StringUtils.isEmpty(queryVo.getChronicPref())){
            return new ResponseEntity(new ResultVO(-1, "参数慢病编号不能为空", false), HttpStatus.OK);
        }
        MemberChronicProductSelectListParamDto queryDto = new MemberChronicProductSelectListParamDto();
        BeanUtils.copyProperties(queryVo, queryDto);
        queryDto.setOrganSign(commonRequestModel.getOrganSign());
        queryDto.setCheckPref(true);
        return new ResponseEntity(ResultVO.createSuccess(memberChronicProductApi.selectProductList(queryDto)), HttpStatus.OK);
    }

    /**
     * @param queryVo 查询条件
     * @return
     */
    @ApiOperation(value = "pos端查询会员慢病商品列表", notes = "查询会员慢病商品列表", response = MemberChronicProductVo.class, tags = {"查询会员慢病商品列表",})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberChronicProductVo.class)})
    @RequestMapping(value = "/pos/selectMemberProductList",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> posSelectMemberProductList(@RequestBody MemberPosChronicProductParamVo queryVo) {
        if(queryVo == null ){
            return new ResponseEntity(new ResultVO(-1, "参数不能为空", false), HttpStatus.OK);
        }
        if(StringUtils.isEmpty(queryVo.getOrganSign())){
            return new ResponseEntity(new ResultVO(-1, "机构号不能为空", false), HttpStatus.OK);
        }
        if(StringUtils.isEmpty(queryVo.getMemberGuid())){
            return new ResponseEntity(new ResultVO(-1, "会员Guid不能为空", false), HttpStatus.OK);
        }
        MemberPosChronicProductParamDto queryDto = new MemberPosChronicProductParamDto();
        queryDto.setMemberGuid(queryVo.getMemberGuid());
        queryDto.setOrganSign(queryVo.getOrganSign());
        ResponseEntity responseEntity = new ResponseEntity(ResultVO.createSuccess(memberChronicProductApi.posSelectMemberProductList(queryDto)), HttpStatus.OK);
        log.info("pos端查询会员慢病商品列表"+ JSONObject.toJSONString(responseEntity));
        return responseEntity;
    }

    /**
     * @param queryVo 查询条件
     * @return
     */
    @ApiOperation(value = "pos端查询会员慢病商品列表", notes = "查询会员慢病商品列表", response = MemberChronicProductVo.class, tags = {"查询会员慢病商品列表",})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberChronicProductVo.class)})
    @RequestMapping(value = "/pos/selectMemberProductByProductPrefList",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> posSelectMemberProductByProductPrefList(@RequestBody MemberPosChronicProductParamVo queryVo) {
        if(queryVo == null ){
            return new ResponseEntity(new ResultVO(-1, "参数不能为空", false), HttpStatus.OK);
        }
        if(StringUtils.isEmpty(queryVo.getOrganSign())){
            return new ResponseEntity(new ResultVO(-1, "机构号不能为空", false), HttpStatus.OK);
        }
        if(queryVo.getProductPrefList() == null || queryVo.getProductPrefList().size() == 0){
            return new ResponseEntity(new ResultVO(-1, "商品内码集合不能为空", false), HttpStatus.OK);
        }
        MemberPosChronicProductParamDto queryDto = new MemberPosChronicProductParamDto();
        queryDto.setProductPrefList(queryVo.getProductPrefList());
        queryDto.setOrganSign(queryVo.getOrganSign());
        ResponseEntity responseEntity = new ResponseEntity(ResultVO.createSuccess(memberChronicProductApi.posSelectMemberProductByProductPrefList(queryDto)), HttpStatus.OK);
        log.info("pos端查询会员慢病商品列表"+ JSONObject.toJSONString(responseEntity));
        return responseEntity;
    }

    /**
     * 当前操作机构(配置慢病种类关联药品-权限管控--单体、联营门店、连锁总部)
     *
     * @param organSign
     * @return
     */
    private ResponseEntity<ResultVO> checkChronicProductOper(String organSign) {
        if (organSign == null) {
            return new ResponseEntity(new ResultVO(-1, "机构参数不能为空", false), HttpStatus.OK);
        }
        SaaSDrugstoreDto saaSDrugstoreDto = drugstoreService.getDrugstoreByOrganSign(organSign);
        if (saaSDrugstoreDto != null) {
            DrugstoreTypeProductEnum drugstoreTypeProductEnum = DrugstoreTypeUtil.isDrugstoreTypeEnum(saaSDrugstoreDto.getBizModel(), saaSDrugstoreDto.getOrganSignType());
            if (drugstoreTypeProductEnum != null && (drugstoreTypeProductEnum.getType() == DrugstoreTypeProductEnum.SINGLE_DRUGSTORE.getType()
                    || drugstoreTypeProductEnum.getType() == DrugstoreTypeProductEnum.CHAIN_HEADQUARTERS.getType()
                    || drugstoreTypeProductEnum.getType() == DrugstoreTypeProductEnum.JOIN_DRUGSTORE.getType())
                    ) {
                //当前机构具备权限
            } else {
                //当前机构不具备操作权限
                return new ResponseEntity(new ResultVO(-1, "当前机构无法配置慢病种类关联药品", false), HttpStatus.OK);
            }
        } else {
            return new ResponseEntity(new ResultVO(-1, "当前机构不存在！", false), HttpStatus.OK);

        }
        return null;
    }
}
