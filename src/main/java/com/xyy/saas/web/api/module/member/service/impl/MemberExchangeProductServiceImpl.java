package com.xyy.saas.web.api.module.member.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.inventory.cores.api.InventoryApi;
import com.xyy.saas.inventory.cores.dto.InventoryDto;
import com.xyy.saas.inventory.cores.dto.InventoryProductDto;
import com.xyy.saas.inventory.cores.dto.InventoryProductInfoDto;
import com.xyy.saas.inventory.cores.dto.requ.InventoryPromotionRequ;
import com.xyy.saas.member.core.api.MemberExchangeProductApi;
import com.xyy.saas.member.core.dto.MemberExchangeProductDto;
import com.xyy.saas.product.core.api.ProductApi;
import com.xyy.saas.product.core.dto.ProductDto;
import com.xyy.saas.product.core.dto.ProductVoDto;
import com.xyy.saas.product.core.dto.ShelfPostionProductDto;
import com.xyy.saas.web.api.module.member.service.DrugstoreService;
import com.xyy.saas.web.api.module.member.service.MemberExchangeProductService;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 积分可兑换商品服务
 * 从会员模块上移到网关层，为了使会员不依赖库存
 * 次方案可能也只是临时方案
 * <AUTHOR>
 * @since 2020-05-30
 */
@Service
public class MemberExchangeProductServiceImpl implements MemberExchangeProductService {

    private static final Logger logger = LogManager.getLogger(MemberExchangeProductService.class);

    @Reference(version = "0.0.1")
    private MemberExchangeProductApi memberExchangeProductApi;

    @Reference(version = "0.0.1")
    private ProductApi productApi;

    @Reference(version = "0.0.2")
    private InventoryApi inventoryApi;

    @Autowired
    private DrugstoreService drugstoreService;

    /**
     * 获取积分兑换商品分页列表
     * @param exchangeProductDto
     * @return
     */
    @Override
    public PageInfo getExchangeProductListPager(MemberExchangeProductDto exchangeProductDto) {
        try {
            List<String> organList = new ArrayList<>();
            /** 总店 */
            if (StringUtils.isEmpty(exchangeProductDto.getOrganSign())) {
                List<SaaSDrugstoreDto> drugstoreList = drugstoreService.getDrugstoreByHeadquartersOrganSign(exchangeProductDto.getIsDrugstoreHidden(), exchangeProductDto.getHeadquartersOrganSign());
                if (drugstoreList != null && !drugstoreList.isEmpty()) {
                    drugstoreList.stream().forEach(item -> {
                        organList.add(item.getOrganSign());
                    });
                }
            }
            // 根据商品名称模糊查询商品编号
            List<String> prefList = Lists.newArrayList();
            if (!StringUtils.isEmpty(exchangeProductDto.getCommonName())) {
                /** 门店 或 总店选门店 */
                if (!StringUtils.isEmpty(exchangeProductDto.getOrganSign())) {
                    String prefs = productApi.getPrefsByProductMixcondAndHidden(exchangeProductDto.getCommonName(), exchangeProductDto.getIshidden(), exchangeProductDto.getOrganSign());
                    if (!StringUtils.isEmpty(prefs)) {
                        prefList.addAll(Arrays.asList(prefs.split(",")));
                    }
                } else {
                    exchangeProductDto.setOrganList(organList);
                    for (String organSign : exchangeProductDto.getOrganList()) {
                        String prefs = productApi.getPrefsByProductMixcondAndHidden(exchangeProductDto.getCommonName(), exchangeProductDto.getIshidden(), organSign);
                        if (!StringUtils.isEmpty(prefs)) {
                            List<String> tempPrefs = Arrays.asList(prefs.split(","));
                            prefList.addAll(tempPrefs);
                        }
                    }
                }
                if (prefList.isEmpty()) {
                    return new PageInfo(Collections.EMPTY_LIST);
                }
                exchangeProductDto.setPrefList(prefList);
            }
            exchangeProductDto.setYn(1);
            exchangeProductDto.setOrganList(organList.size() > 0 ? organList : null);
            PageInfo pageInfo = memberExchangeProductApi.getExchangeProductListPager(exchangeProductDto);
            if (pageInfo == null || CollectionUtils.isEmpty(pageInfo.getList())) {
                return new PageInfo(Collections.EMPTY_LIST);
            }
            List<MemberExchangeProductDto> exchangeProductlist = pageInfo.getList();
//--------------------------------------------------------------------------------------------------------------------------------------
            /** 门店 或 总店选门店 */
            /** 查询商品的库存信息 */
            Map<String, String> drugstoreMap;
            List<SaaSDrugstoreDto> drugstoreList;
            if (!StringUtils.isEmpty(exchangeProductDto.getOrganSign())) {
                // 符合商品名称模糊查询的商品编码
                List<String> phPrefs = exchangeProductlist.stream().map(MemberExchangeProductDto::getProductPref).collect(Collectors.toList());
                SaaSDrugstoreDto drugstore = drugstoreService.getDrugstoreByOrganSign(exchangeProductDto.getOrganSign());
                // 查询商品
                List<ProductDto> productList = productApi.getProductsByParams(phPrefs, null, exchangeProductDto.getOrganSign(), null);
                Map<String, ProductDto> productMap = productList.stream().collect(Collectors.toMap(ProductDto::getPref, Function.identity(), (s1, s2) -> s1));

                // 库存信息
                InventoryPromotionRequ inventoryRequest = new InventoryPromotionRequ();
                inventoryRequest.setOrganSign(exchangeProductDto.getOrganSign());
                inventoryRequest.setProductList(phPrefs);
                ResultVO<PageInfo<InventoryDto>> result = inventoryApi.getStockNumber(inventoryRequest);
                Map<String, InventoryDto> inventoryMap = new HashMap<>();
                if (result != null || result.getResult() != null) {
                    PageInfo<InventoryDto> inventoryPage = result.getResult();
                    if (inventoryPage != null && !CollectionUtils.isEmpty(inventoryPage.getList())) {
                        inventoryMap = inventoryPage.getList().stream().collect(Collectors.toMap(InventoryDto::getProductPref, Function.identity(), (s1, s2) -> s1));
                    }
                }

                Map<String, InventoryDto> inventoryFinalMap = inventoryMap;
                exchangeProductlist.stream().forEach(item -> {
                    ProductDto product = productMap.get(item.getProductPref());
                    InventoryDto inventory = inventoryFinalMap.get(item.getProductPref());
                    item.setInventoryNum(Optional.ofNullable(inventory).map(InventoryDto::getStockNumber).orElse(BigDecimal.ZERO));
                    if (product != null) {
                        // 显示内码
                        item.setPharmacyPref(product.getPharmacyPref());
                        item.setProductName(product.getProductName());
                        item.setCommonName(product.getCommonName());
                        item.setUnitName(product.getUnitName());
                        item.setAttributeSpecification(product.getAttributeSpecification());
                        item.setManufacturer(product.getManufacturer());
                        item.setRetailPrice(product.getRetailPrice());
                    }
                    if (null != drugstore) {
                        item.setOrganSignName(drugstore.getDrugstoreName() == null ? "" : drugstore.getDrugstoreName());
                    }
                });
                pageInfo.setList(exchangeProductlist);
                return pageInfo;
            } else {
                //总部选择全部
                //门店对应商品编码集合
                Map<String, List<String>> hashMapProductPref = Maps.newHashMap();
                if (organList.size() > 0) {
                    for (String organ : organList) {
                        List<String> list = Lists.newArrayList();
                        for (MemberExchangeProductDto model : exchangeProductlist) {
                            if (model.getOrganSign().equals(organ)) {
                                list.add(model.getProductPref());
                            }
                        }
                        if(list.size()>0){
                            hashMapProductPref.put(organ, list);
                        }
                    }
                }
                drugstoreList = drugstoreService.getDrugstoreByHeadquartersOrganSign(exchangeProductDto.getHeadquartersOrganSign());
                drugstoreMap = drugstoreList.stream().collect(Collectors.toMap(SaaSDrugstoreDto::getOrganSign, p -> p.getDrugstoreName()));
                for (String organ : hashMapProductPref.keySet()) {
                    List<String> phPrefList = hashMapProductPref.get(organ);
                    List<ProductDto> tempPros = productApi.getProductsByParams(phPrefList, null, organ, null);
                    Map<String, ProductDto> productMap = tempPros.stream().collect(Collectors.toMap(ProductDto::getPref, Function.identity(), (s1, s2) -> s1));
                    // 库存信息
                    InventoryPromotionRequ inventoryRequest = new InventoryPromotionRequ();
                    inventoryRequest.setOrganSign(organ);
                    inventoryRequest.setProductList(phPrefList);
                    ResultVO<PageInfo<InventoryDto>> result = inventoryApi.getStockNumber(inventoryRequest);
                    Map<String, InventoryDto> inventoryMap = new HashMap<>();
                    if (result != null || result.getResult() != null) {
                        PageInfo<InventoryDto> inventoryPage = result.getResult();
                        if (inventoryPage != null && !CollectionUtils.isEmpty(inventoryPage.getList())) {
                            inventoryMap = inventoryPage.getList().stream().collect(Collectors.toMap(InventoryDto::getProductPref, Function.identity(), (s1, s2) -> s1));
                        }
                    }
                    Map<String, InventoryDto> inventoryFinalMap = inventoryMap;
                    exchangeProductlist.stream().forEach(item -> {
                        ProductDto product = productMap.get(item.getProductPref());
                        InventoryDto inventory = inventoryFinalMap.get(item.getProductPref());
                        item.setInventoryNum(Optional.ofNullable(inventory).map(InventoryDto::getStockNumber).orElse(BigDecimal.ZERO));

                        if (product != null) {
                            // 显示内码
                            item.setPharmacyPref(product.getPharmacyPref());
                            item.setProductName(product.getProductName());
                            item.setCommonName(product.getCommonName());
                            item.setUnitName(product.getUnitName());
                            item.setAttributeSpecification(product.getAttributeSpecification());
                            item.setManufacturer(product.getManufacturer());
                            item.setRetailPrice(product.getRetailPrice());
                        }
                        if (!drugstoreMap.isEmpty() && drugstoreMap.containsKey(item.getOrganSign())) {
                            item.setOrganSignName(drugstoreMap.get(item.getOrganSign()));
                        }
                    });
                }
                pageInfo.setList(exchangeProductlist);
                return pageInfo;
            }
        } catch (Exception e) {
            logger.error("查询积分可兑换商品分页异常 {}", JSONObject.toJSONString(exchangeProductDto), e);
            return new PageInfo(Collections.EMPTY_LIST);
        }
    }

    /**
     * 新增积分兑换 商品查询列表
     * @param exchangeProductDto
     * @return
     */
    @Override
    public PageInfo getShelfProductPager(MemberExchangeProductDto exchangeProductDto) {
        try {
            PageInfo pageInfo = new PageInfo();
            pageInfo.setPageNum(exchangeProductDto.getPageNum());
            pageInfo.setPageSize(exchangeProductDto.getPageSize());
            InventoryProductDto productDto = new InventoryProductDto();
            BeanUtils.copyProperties(exchangeProductDto, productDto);
            productDto.setFlag(3);
            productDto.setInventoryFlag(2); //库存大于0的商品
            productDto.setCommodityVal(exchangeProductDto.getProductName());
            logger.info("getShelfProductPager param:" + JSONObject.toJSONString(productDto));
            List<String> list  = memberExchangeProductApi.getExistExchangeProductpref(exchangeProductDto.getOrganSign());
            if(!list.isEmpty() && list.size() > 0){
                productDto.setProductPrefNotList(list);
            }
            PageInfo<InventoryProductInfoDto> proInfo = inventoryApi.getInventoryAndProductList(pageInfo, productDto);
            return proInfo;
        } catch (BeansException e) {
            logger.error("新增积分兑换 商品查询列表异常 {}", JSONObject.toJSONString(exchangeProductDto));
            return new PageInfo(Collections.EMPTY_LIST);
        }
    }

    /**
     * 为宜块钱提供不分页获取积分商品列表
     * @param exchangeProductDto
     * @return
     */
    @Override
    public List<MemberExchangeProductDto> getYKQExchangeProductList(MemberExchangeProductDto exchangeProductDto) {
        List<MemberExchangeProductDto> exchangeProductlist = memberExchangeProductApi.getExchangeProductList(exchangeProductDto);
        if (CollectionUtils.isEmpty(exchangeProductlist)) {
            return Collections.EMPTY_LIST;
        }
        String organSign = exchangeProductDto.getOrganSign();
        List<String> phPrefs = exchangeProductlist.stream().map(MemberExchangeProductDto::getProductPref).collect(Collectors.toList());
        // 分批调取rpc查询关联数据
        int size = phPrefs.size();
        int batch = 100;
        int batchCount = (size - 1) / batch + 1;
        Map<String, ProductDto> productMap = new HashMap<>(size);
        Map<String, InventoryDto> inventoryMap = new HashMap<>(size);
        for (int i = 0; i < batchCount; ++i) {
            // 查询商品
            List<ProductDto> productList = productApi.getProductsByParams(phPrefs, null, exchangeProductDto.getOrganSign(), null);
            productMap.putAll(productList.stream().collect(Collectors.toMap(ProductDto::getPref, Function.identity(), (s1, s2) -> s1)));

            // 库存信息
            InventoryPromotionRequ inventoryRequest = new InventoryPromotionRequ();
            inventoryRequest.setProductList(phPrefs);
            inventoryRequest.setOrganSign(organSign);
            ResultVO<PageInfo<InventoryDto>> result = inventoryApi.getStockNumber(inventoryRequest);
            if (result != null || result.getResult() != null) {
                PageInfo<InventoryDto> inventoryPage = result.getResult();
                if (inventoryPage != null && !CollectionUtils.isEmpty(inventoryPage.getList())) {
                    inventoryMap.putAll(inventoryPage.getList().stream().collect(Collectors.toMap(InventoryDto::getProductPref, Function.identity(), (s1, s2) -> s1)));
                }
            }
        }

        // 关联数据
        exchangeProductlist.stream().forEach(item->{
            ProductDto product = productMap.get(item.getProductPref());
            InventoryDto inventory = inventoryMap.get(item.getProductPref());
            item.setInventoryNum(Optional.ofNullable(inventory).map(InventoryDto::getStockNumber).orElse(BigDecimal.ZERO));
            if(product != null){
                // 显示内码
                item.setPharmacyPref(product.getPharmacyPref());
                item.setProductName(product.getProductName());
                item.setCommonName(product.getCommonName());
                item.setUnitName(product.getUnitName());
                item.setAttributeSpecification(product.getAttributeSpecification());
                item.setManufacturer(product.getManufacturer());
                item.setRetailPrice(product.getRetailPrice());
            }
        });
        // 排序
        exchangeProductlist = exchangeProductlist.stream()
                .filter(item -> {
                    return item.getInventoryNum().compareTo(BigDecimal.ZERO) > 0;
                })
                .sorted((a, b) -> {
                    // 积分正序，时间倒序
                    if (a.getIntegral().compareTo(b.getIntegral()) == 0) {
                        if (a.getCreateTime().before(b.getCreateTime())) {
                            return 1;
                        }
                        return -1;
                    }
                    if (a.getIntegral().compareTo(b.getIntegral()) > 0) {
                        return 1;
                    }
                    return -1;
                })
                .limit(10) // 只获取前十
                .collect(Collectors.toList());
        return exchangeProductlist;
    }

}
