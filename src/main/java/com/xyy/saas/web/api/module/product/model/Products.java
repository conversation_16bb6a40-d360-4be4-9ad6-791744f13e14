package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 商品信息批量导入实体信息
 */
@ApiModel(description = "商品信息批量导入实体信息")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")
public class Products {

    @JsonProperty("saasProductBaseInfos")
    private List<SaasProductBaseInfo> saasProductBaseInfos = null;

    public Products details(List<SaasProductBaseInfo> saasProductBaseInfos) {
        this.saasProductBaseInfos = saasProductBaseInfos;
        return this;
    }

    public Products addDetailsItem(SaasProductBaseInfo detailsItem) {
        if (this.saasProductBaseInfos == null) {
            this.saasProductBaseInfos = new ArrayList<>();
        }
        this.saasProductBaseInfos.add(detailsItem);
        return this;
    }

    /**
     * 商品信息实体集合
     * @return details
     **/
    @ApiModelProperty(value = "商品信息实体集合")

    @Valid

    public List<SaasProductBaseInfo> getSaasProductBaseInfos() {
        return saasProductBaseInfos;
    }

    public void setSaasProductBaseInfos(List<SaasProductBaseInfo> saasProductBaseInfos) {
        this.saasProductBaseInfos = saasProductBaseInfos;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Products products = (Products) o;
        return Objects.equals(saasProductBaseInfos, products.saasProductBaseInfos);
    }

    @Override
    public int hashCode() {
        return Objects.hash(saasProductBaseInfos);
    }

    @Override
    public String toString() {
        return "Products{" +
                "saasProductBaseInfos=" + saasProductBaseInfos +
                '}';
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}
