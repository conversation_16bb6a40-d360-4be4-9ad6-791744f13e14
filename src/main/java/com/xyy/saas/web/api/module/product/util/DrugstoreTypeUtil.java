package com.xyy.saas.web.api.module.product.util;

import com.xyy.saas.product.core.enums.DrugstoreTypeProductEnum;
import com.xyy.user.module.enums.DrugstoreBizModelEnum;
import com.xyy.user.module.enums.DrugstoreTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @ClassName DrugstoreTypeUtil
 * @Description 当前登录机构的机构类型判别工具类
 * <AUTHOR>
 * @Date 2020/2/22 16:27
 * @Version 1.0
 **/
public class DrugstoreTypeUtil {

    private static final Logger logger = LoggerFactory.getLogger(DrugstoreTypeUtil.class);
    /**
     * 获取当前登录机构的机构类型：单体药店，连锁门店，连锁总部，联营门店，联营总部 5种基本类型
     * @param bizModel
     * @param organSignType
     * *@param organSign  当前登录机构编号
     * @return
     */
    public static DrugstoreTypeProductEnum isDrugstoreTypeEnum(Byte bizModel, Byte organSignType,String organSign){
        if(DrugstoreBizModelEnum.DRUGSTORE.toEquals(bizModel)){//单体门店判断
            return DrugstoreTypeProductEnum.SINGLE_DRUGSTORE;
        }else if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(bizModel)){//连锁判断
            if(DrugstoreTypeEnum.DRUGSTORE.toEquals(organSignType)){//连锁门店
                return DrugstoreTypeProductEnum.CHAIN_DRUGSTORE;
            }else if(DrugstoreTypeEnum.HEADQUARTERS.toEquals(organSignType)) {//连锁总部
                return DrugstoreTypeProductEnum.CHAIN_HEADQUARTERS;
            }else{
                logger.error("当前登录的连锁机构参数不合法，需要用户服务排查参数：organSign:{},bizModel:{},organSignType:{}",organSign,bizModel,organSignType);
                return null;
            }
        }else if(DrugstoreBizModelEnum.JOINT_OPERATION.toEquals(bizModel)){//联营判断
            if(DrugstoreTypeEnum.DRUGSTORE.toEquals(organSignType)){//联营门店
                return DrugstoreTypeProductEnum.JOIN_DRUGSTORE;
            }else if(DrugstoreTypeEnum.HEADQUARTERS.toEquals(organSignType)) {//联营总部
                return DrugstoreTypeProductEnum.JOIN_HEADQUARTERS;
            }else{
                logger.error("当前登录的联营机构参数不合法，需要用户服务排查参数：organSign:{},bizModel:{},organSignType:{}",organSign,bizModel,organSignType);
                return null;
            }
        }else{
            logger.error("当前登录非单体，连锁，联营结构，需要用户服务排查参数：organSign:{},bizModel:{},organSignType:{}",organSign,bizModel,organSignType);
            return null;
        }
    }

    /**
     *
     * @param bizModel
     * @param organSignType
     * @return
     */
    public static DrugstoreTypeProductEnum isDrugstoreTypeEnum(Byte bizModel, Byte organSignType){
        if(DrugstoreBizModelEnum.DRUGSTORE.toEquals(bizModel)){//单体门店判断
            return DrugstoreTypeProductEnum.SINGLE_DRUGSTORE;
        }else if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(bizModel)){//连锁判断
            if(DrugstoreTypeEnum.DRUGSTORE.toEquals(organSignType)){//连锁门店
                return DrugstoreTypeProductEnum.CHAIN_DRUGSTORE;
            }else if(DrugstoreTypeEnum.HEADQUARTERS.toEquals(organSignType)) {//连锁总部
                return DrugstoreTypeProductEnum.CHAIN_HEADQUARTERS;
            }else{
                return null;
            }
        }else if(DrugstoreBizModelEnum.JOINT_OPERATION.toEquals(bizModel)){//联营判断
            if(DrugstoreTypeEnum.DRUGSTORE.toEquals(organSignType)){//联营门店
                return DrugstoreTypeProductEnum.JOIN_DRUGSTORE;
            }else if(DrugstoreTypeEnum.HEADQUARTERS.toEquals(organSignType)) {//联营总部
                return DrugstoreTypeProductEnum.JOIN_HEADQUARTERS;
            }else{
                return null;
            }
        }else{
            return null;
        }
    }
}
