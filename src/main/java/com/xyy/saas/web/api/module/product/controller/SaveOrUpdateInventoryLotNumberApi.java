/**
 * NOTE: This class is auto generated by the swagger code generator program (2.2.3).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.web.api.module.product.model.InventoryLotNumberPageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-28T11:40:30.801+08:00")
@RequestMapping("/product")
@Api(value = "findInventoryLotNumber", description = "the findInventoryLotNumber API")
public interface SaveOrUpdateInventoryLotNumberApi {

    @ApiOperation(value = "批量导入商品批号库存", notes = "批量导入商品批号库存", response = ResultVO.class, tags={ "inventoryLotNumber", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Invalid input", response = ResultVO.class) })
    
    @RequestMapping(value = "/saveOrUpdateInventoryLotNumber",
        produces = {  "application/json" },
        consumes = { "application/json" },
        method = RequestMethod.POST)
    ResponseEntity<ResultVO> saveOrUpdateInventoryLotNumber(/*@ApiParam(value = "批号库存Vo" ,required=true )*/ @RequestHeader("userName") String userName, @RequestHeader("organSign") String organSign, @Valid @RequestBody InventoryLotNumberPageVo inventoryLotNumberPageVo);

}
