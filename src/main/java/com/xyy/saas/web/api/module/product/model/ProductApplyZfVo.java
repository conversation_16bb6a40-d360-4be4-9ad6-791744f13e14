package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName ProductApplyZfVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/30 16:15
 * @Version 1.0
 **/
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

@ApiModel(description = "商品提报列表结果信息类")
public class ProductApplyZfVo {
    @ApiModelProperty(value = "待作废的提报单编号")
    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    @JsonProperty("pref")
    private String pref;//待作废的提报单编号

}
