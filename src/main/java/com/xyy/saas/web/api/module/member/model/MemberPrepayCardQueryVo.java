package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 会员储值卡查询Vo
 * <AUTHOR>
 */
@ApiModel(description = "会员储值卡查询")
public class MemberPrepayCardQueryVo implements Serializable {

    private static final long serialVersionUID = -5386365017841408723L;

    /**
     * 混合查询 会员姓名/首字母缩写/手机号/会员卡号
     */
    @ApiModelProperty(value = "混合查询 会员姓名/首字母缩写/手机号/会员卡号", example = "张三")
    private String mixedQuery;

    /**
     * 会员等级id
     */
    @ApiModelProperty(value = "会员等级id", example = "197")
    private Long vipLevelId;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", example = "12")
    private String createUser;

    /**
     * 查询开卡开始时间
     */
    @ApiModelProperty(value = "查询开卡开始时间", example = "1563552000000")
    private Date startTime;

    /**
     * 查询开卡结束时间
     */
    @ApiModelProperty(value = "查询开卡结束时间", example = "1563638399000")
    private Date endTime;

    /**
     * 查询最后充值开始时间
     */
    @ApiModelProperty(value = "查询最后充值开始时间", example = "1563542000000")
    private Date startLastDepositTime;

    /**
     * 查询最后充值结束时间
     */
    @ApiModelProperty(value = "查询最后充值结束时间", example = "1563652399000")
    private Date endLastDepositTime;

    /**
     * 查询余额下限
     */
    @ApiModelProperty(value = "查询余额下限", example = "10.00")
    private BigDecimal minTotalAmount;

    /**
     * 查询余额上限
     */
    @ApiModelProperty(value = "查询余额上限", example = "1000.00")
    private BigDecimal maxTotalAmount;

    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识", hidden = true)
    private String organsign;

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码", required = true, example = "1")
    private Integer pageNum;

    /**
     * 每页数量
     */
    @ApiModelProperty(value = "每页数量", required = true, example = "10")
    private Integer pageSize;

    /**
     * 导出excel文件名称
     */
    @ApiModelProperty(value = "导出excel文件名称", example = "汇总数据2019-08-01")
    private String excelName;

    @ApiModelProperty(value = "是否隐藏门店")
    private Byte isDrugstoreHidden;

    public String getMixedQuery() {
        return mixedQuery;
    }

    public void setMixedQuery(String mixedQuery) {
        this.mixedQuery = mixedQuery;
    }

    public Long getVipLevelId() {
        return vipLevelId;
    }

    public void setVipLevelId(Long vipLevelId) {
        this.vipLevelId = vipLevelId;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getStartLastDepositTime() {
        return startLastDepositTime;
    }

    public void setStartLastDepositTime(Date startLastDepositTime) {
        this.startLastDepositTime = startLastDepositTime;
    }

    public Date getEndLastDepositTime() {
        return endLastDepositTime;
    }

    public void setEndLastDepositTime(Date endLastDepositTime) {
        this.endLastDepositTime = endLastDepositTime;
    }

    public BigDecimal getMinTotalAmount() {
        return minTotalAmount;
    }

    public void setMinTotalAmount(BigDecimal minTotalAmount) {
        this.minTotalAmount = minTotalAmount;
    }

    public BigDecimal getMaxTotalAmount() {
        return maxTotalAmount;
    }

    public void setMaxTotalAmount(BigDecimal maxTotalAmount) {
        this.maxTotalAmount = maxTotalAmount;
    }

    public String getOrgansign() {
        return organsign;
    }

    public void setOrgansign(String organsign) {
        this.organsign = organsign;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getExcelName() {
        return excelName;
    }

    public void setExcelName(String excelName) {
        this.excelName = excelName;
    }

    public Byte getIsDrugstoreHidden() {
        return isDrugstoreHidden;
    }

    public void setIsDrugstoreHidden(Byte isDrugstoreHidden) {
        this.isDrugstoreHidden = isDrugstoreHidden;
    }

    @Override
    public String toString() {
        return "MemberPrepayCardQueryVo{" +
                "mixedQuery='" + mixedQuery + '\'' +
                ", vipLevelId=" + vipLevelId +
                ", createUser='" + createUser + '\'' +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", startLastDepositTime=" + startLastDepositTime +
                ", endLastDepositTime=" + endLastDepositTime +
                ", minTotalAmount=" + minTotalAmount +
                ", maxTotalAmount=" + maxTotalAmount +
                ", organsign='" + organsign + '\'' +
                ", pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                ", excelName='" + excelName + '\'' +
                '}';
    }
}