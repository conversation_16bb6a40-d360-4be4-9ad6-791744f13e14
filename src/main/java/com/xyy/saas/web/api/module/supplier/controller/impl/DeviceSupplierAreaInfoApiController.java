package com.xyy.saas.web.api.module.supplier.controller.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.supplier.api.AreaBaseinfoApi;
import com.xyy.saas.supplier.api.SupplierAreaInfoApi;
import com.xyy.saas.supplier.dto.AreaBaseinfoDto;
import com.xyy.saas.supplier.dto.ProviderAreaInfoDto;
import com.xyy.saas.web.api.module.supplier.controller.DeviceSupplierAreaInfoApi;
import com.xyy.saas.web.api.module.supplier.model.AreaBaseinfo;
import com.xyy.saas.web.api.module.supplier.model.ProviderAreaInfo;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;

/**
 * desc 器械匹配
 *
 * <AUTHOR>
 * @date 2024/05/23 10:10
 */
@Controller
public class DeviceSupplierAreaInfoApiController implements DeviceSupplierAreaInfoApi {
    @Reference(version = "0.0.1")
    private AreaBaseinfoApi areaBaseinfoApi;

    @Reference(version = "0.0.1")
    private SupplierAreaInfoApi supplierAreaInfoApi;

    @Override
    public ResponseEntity<ResultVO> findAreaBaseinfoByCondition(HttpServletRequest request, @NotNull @RequestBody AreaBaseinfo areaBaseinfo) {
        AreaBaseinfoDto areaBaseinfoDto = new AreaBaseinfoDto();
        BeanUtils.copyProperties(areaBaseinfo,areaBaseinfoDto);
        //2-河北器械
        areaBaseinfoDto.setType(Byte.parseByte("2"));
        ResultVO<PageInfo<AreaBaseinfoDto>> areaBaseinfoByCondition = areaBaseinfoApi.findAreaBaseinfoByCondition(areaBaseinfoDto);
        return new ResponseEntity<>(areaBaseinfoByCondition, HttpStatus.OK);
    }

    /**
     * 分页查询供应商和区域码关系
     *
     * @param request
     * @param providerAreaInfo
     * @return
     */
    @Override
    public ResponseEntity<ResultVO> findSupplierAreaByCondition(HttpServletRequest request, @NotNull @RequestBody ProviderAreaInfo providerAreaInfo) {
        String organSign = request.getHeader("organSign");
        ProviderAreaInfoDto providerAreaInfoDto = new ProviderAreaInfoDto();
        BeanUtils.copyProperties(providerAreaInfo,providerAreaInfoDto);
        providerAreaInfoDto.setOrgansign(organSign);
        //2-河北器械
        providerAreaInfoDto.setType(Byte.parseByte("2"));
        ResultVO<PageInfo<ProviderAreaInfoDto>> supplierAreaByCondition = supplierAreaInfoApi.findSupplierAreaByCondition(providerAreaInfoDto);
        return new ResponseEntity<>(supplierAreaByCondition, HttpStatus.OK);
    }

    /**
     * 保存供应商与区域编码关系
     *
     * @param request
     * @param providerAreaInfo
     * @return
     */
    @Override
    public ResponseEntity<ResultVO> saveSupplierAreaByCondition(HttpServletRequest request, @NotNull @RequestBody ProviderAreaInfo providerAreaInfo) {
        String organSign = request.getHeader("organSign");
        ProviderAreaInfoDto providerAreaInfoDto = new ProviderAreaInfoDto();
        BeanUtils.copyProperties(providerAreaInfo,providerAreaInfoDto);
        providerAreaInfoDto.setOrgansign(organSign);
        //2-河北器械
        providerAreaInfoDto.setType(Byte.parseByte("2"));
        ResultVO<Integer> integerResultVO = supplierAreaInfoApi.saveSupplierAreaByCondition(providerAreaInfoDto);
        return new ResponseEntity<>(integerResultVO, HttpStatus.OK);
    }
}
