package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Description 会员慢病患者回访信息VO-新增、编辑
 * @<PERSON> zhuzhi<PERSON>an
 * @Create 2020-10-13 9:52
 */
@Data
public class MemberChronicVisitInfoReqVo {
	@ApiModelProperty(value = "主键")
    private Long id; 
	@ApiModelProperty(value = "回访门店(总部修改回访记录)")
    private String reVisitDrugstore;
	@ApiModelProperty(value = "会员guid")
    private String guid; 
	@ApiModelProperty(value = "回访人")
    private String revisiter; 
	@ApiModelProperty(value = "回访时间")
    private Date visitTime;
	@ApiModelProperty(value = "回访内容")
    private String visitContent; 
}