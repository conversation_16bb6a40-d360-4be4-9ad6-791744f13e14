package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * InventoryIncomeStatementDetailVo
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-30T11:21:29.525+08:00")

public class InventoryIncomeStatementDetailVo   implements Serializable{
    private static final long serialVersionUID = 3158111319762907620L;
  @JsonProperty("id")
  private Integer id = null;

  @JsonProperty("pref")
  private String pref = null;

  @JsonProperty("profitLossPref")
  private String profitLossPref = null;

  @JsonProperty("createUser")
  private String createUser = null;

  @JsonProperty("createTime")
  private Date createTime = null;

  @JsonProperty("updateUser")
  private String updateUser = null;

  @JsonProperty("updateTime")
  private Date updateTime = null;

  @JsonProperty("remark")
  private String remark = null;

  @JsonProperty("yn")
  private byte yn = 1;

  @JsonProperty("baseVersion")
  private String baseVersion = null;

  @JsonProperty("organsign")
  private String organsign = null;

  @JsonProperty("productPref")
  private String productPref = null;

  @JsonProperty("batchNumber")
  private String batchNumber = null;

  @JsonProperty("inventory")
  private BigDecimal inventory = null;

  @JsonProperty("actualStore")
  private BigDecimal actualStore = null;

  @JsonProperty("profitLossQuantity")
  private BigDecimal profitLossQuantity = null;

  public InventoryIncomeStatementDetailVo id(Integer id) {
    this.id = id;
    return this;
  }

   /**
   * 主键id
   * @return id
  **/
  @ApiModelProperty(value = "主键id")


  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public InventoryIncomeStatementDetailVo pref(String pref) {
    this.pref = pref;
    return this;
  }

   /**
   * 编号
   * @return pref
  **/
  @ApiModelProperty(value = "编号")


  public String getPref() {
    return pref;
  }

  public void setPref(String pref) {
    this.pref = pref;
  }

  public InventoryIncomeStatementDetailVo profitLossPref(String profitLossPref) {
    this.profitLossPref = profitLossPref;
    return this;
  }

   /**
   * 损益表单据编号
   * @return profitLossPref
  **/
  @ApiModelProperty(value = "损益表单据编号")


  public String getProfitLossPref() {
    return profitLossPref;
  }

  public void setProfitLossPref(String profitLossPref) {
    this.profitLossPref = profitLossPref;
  }

  public InventoryIncomeStatementDetailVo createUser(String createUser) {
    this.createUser = createUser;
    return this;
  }

   /**
   * 创建人
   * @return createUser
  **/
  @ApiModelProperty(value = "创建人")


  public String getCreateUser() {
    return createUser;
  }

  public void setCreateUser(String createUser) {
    this.createUser = createUser;
  }

  public InventoryIncomeStatementDetailVo createTime(Date createTime) {
    this.createTime = createTime;
    return this;
  }

   /**
   * 创建时间
   * @return createTime
  **/
  @ApiModelProperty(value = "创建时间")

  @Valid

  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public InventoryIncomeStatementDetailVo updateUser(String updateUser) {
    this.updateUser = updateUser;
    return this;
  }

   /**
   * 更新人
   * @return updateUser
  **/
  @ApiModelProperty(value = "更新人")


  public String getUpdateUser() {
    return updateUser;
  }

  public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
  }

  public InventoryIncomeStatementDetailVo updateTime(Date updateTime) {
    this.updateTime = updateTime;
    return this;
  }

   /**
   * 更新时间
   * @return updateTime
  **/
  @ApiModelProperty(value = "更新时间")

  @Valid

  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public InventoryIncomeStatementDetailVo remark(String remark) {
    this.remark = remark;
    return this;
  }

   /**
   * 损益原因
   * @return remark
  **/
  @ApiModelProperty(value = "损益原因")


  public String getRemark() {
    return remark;
  }

  public void setRemark(String remark) {
    this.remark = remark;
  }

  public InventoryIncomeStatementDetailVo yn(byte yn) {
    this.yn = yn;
    return this;
  }

   /**
   * 是否删除
   * @return yn
  **/
  @ApiModelProperty(value = "是否删除")

  public byte getYn() {
    return yn;
  }

  public void setYn(byte yn) {
    this.yn = yn;
  }

  public InventoryIncomeStatementDetailVo baseVersion(String baseVersion) {
    this.baseVersion = baseVersion;
    return this;
  }

   /**
   * 版本号
   * @return baseVersion
  **/
  @ApiModelProperty(value = "版本号")


  public String getBaseVersion() {
    return baseVersion;
  }

  public void setBaseVersion(String baseVersion) {
    this.baseVersion = baseVersion;
  }

  public InventoryIncomeStatementDetailVo organsign(String organsign) {
    this.organsign = organsign;
    return this;
  }

   /**
   * 机构代码
   * @return organsign
  **/
  @ApiModelProperty(value = "机构代码")


  public String getOrgansign() {
    return organsign;
  }

  public void setOrgansign(String organsign) {
    this.organsign = organsign;
  }

  public InventoryIncomeStatementDetailVo productPref(String productPref) {
    this.productPref = productPref;
    return this;
  }

   /**
   * 商品编号
   * @return productPref
  **/
  @ApiModelProperty(value = "商品编号")


  public String getProductPref() {
    return productPref;
  }

  public void setProductPref(String productPref) {
    this.productPref = productPref;
  }

  public InventoryIncomeStatementDetailVo batchNumber(String batchNumber) {
    this.batchNumber = batchNumber;
    return this;
  }

   /**
   * 批号
   * @return batchNumber
  **/
  @ApiModelProperty(value = "批号")


  public String getBatchNumber() {
    return batchNumber;
  }

  public void setBatchNumber(String batchNumber) {
    this.batchNumber = batchNumber;
  }

  public InventoryIncomeStatementDetailVo inventory(BigDecimal inventory) {
    this.inventory = inventory;
    return this;
  }

   /**
   * 库存数量
   * @return inventory
  **/
  @ApiModelProperty(value = "库存数量")

  @Valid

  public BigDecimal getInventory() {
    return inventory;
  }

  public void setInventory(BigDecimal inventory) {
    this.inventory = inventory;
  }

  public InventoryIncomeStatementDetailVo actualStore(BigDecimal actualStore) {
    this.actualStore = actualStore;
    return this;
  }

   /**
   * 实际数量
   * @return actualStore
  **/
  @ApiModelProperty(value = "实际数量")

  @Valid

  public BigDecimal getActualStore() {
    return actualStore;
  }

  public void setActualStore(BigDecimal actualStore) {
    this.actualStore = actualStore;
  }

  public InventoryIncomeStatementDetailVo profitLossQuantity(BigDecimal profitLossQuantity) {
    this.profitLossQuantity = profitLossQuantity;
    return this;
  }

   /**
   * 损溢数量
   * @return profitLossQuantity
  **/
  @ApiModelProperty(value = "损溢数量")

  @Valid

  public BigDecimal getProfitLossQuantity() {
    return profitLossQuantity;
  }

  public void setProfitLossQuantity(BigDecimal profitLossQuantity) {
    this.profitLossQuantity = profitLossQuantity;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InventoryIncomeStatementDetailVo inventoryIncomeStatementDetailVo = (InventoryIncomeStatementDetailVo) o;
    return Objects.equals(this.id, inventoryIncomeStatementDetailVo.id) &&
        Objects.equals(this.pref, inventoryIncomeStatementDetailVo.pref) &&
        Objects.equals(this.profitLossPref, inventoryIncomeStatementDetailVo.profitLossPref) &&
        Objects.equals(this.createUser, inventoryIncomeStatementDetailVo.createUser) &&
        Objects.equals(this.createTime, inventoryIncomeStatementDetailVo.createTime) &&
        Objects.equals(this.updateUser, inventoryIncomeStatementDetailVo.updateUser) &&
        Objects.equals(this.updateTime, inventoryIncomeStatementDetailVo.updateTime) &&
        Objects.equals(this.remark, inventoryIncomeStatementDetailVo.remark) &&
        Objects.equals(this.yn, inventoryIncomeStatementDetailVo.yn) &&
        Objects.equals(this.baseVersion, inventoryIncomeStatementDetailVo.baseVersion) &&
        Objects.equals(this.organsign, inventoryIncomeStatementDetailVo.organsign) &&
        Objects.equals(this.productPref, inventoryIncomeStatementDetailVo.productPref) &&
        Objects.equals(this.batchNumber, inventoryIncomeStatementDetailVo.batchNumber) &&
        Objects.equals(this.inventory, inventoryIncomeStatementDetailVo.inventory) &&
        Objects.equals(this.actualStore, inventoryIncomeStatementDetailVo.actualStore) &&
        Objects.equals(this.profitLossQuantity, inventoryIncomeStatementDetailVo.profitLossQuantity);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, pref, profitLossPref, createUser, createTime, updateUser, updateTime, remark, yn, baseVersion, organsign, productPref, batchNumber, inventory, actualStore, profitLossQuantity);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InventoryIncomeStatementDetailVo {\n");

    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    pref: ").append(toIndentedString(pref)).append("\n");
    sb.append("    profitLossPref: ").append(toIndentedString(profitLossPref)).append("\n");
    sb.append("    createUser: ").append(toIndentedString(createUser)).append("\n");
    sb.append("    createTime: ").append(toIndentedString(createTime)).append("\n");
    sb.append("    updateUser: ").append(toIndentedString(updateUser)).append("\n");
    sb.append("    updateTime: ").append(toIndentedString(updateTime)).append("\n");
    sb.append("    remark: ").append(toIndentedString(remark)).append("\n");
    sb.append("    yn: ").append(toIndentedString(yn)).append("\n");
    sb.append("    baseVersion: ").append(toIndentedString(baseVersion)).append("\n");
    sb.append("    organsign: ").append(toIndentedString(organsign)).append("\n");
    sb.append("    productPref: ").append(toIndentedString(productPref)).append("\n");
    sb.append("    batchNumber: ").append(toIndentedString(batchNumber)).append("\n");
    sb.append("    inventory: ").append(toIndentedString(inventory)).append("\n");
    sb.append("    actualStore: ").append(toIndentedString(actualStore)).append("\n");
    sb.append("    profitLossQuantity: ").append(toIndentedString(profitLossQuantity)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

