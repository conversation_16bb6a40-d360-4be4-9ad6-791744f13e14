package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 会员慢病种类关联药品配置
 */
@Data
public class MemberChronicProductSaveVo {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "慢病编号")
    private String pref;

    @ApiModelProperty(value = "机构号")
    private String organSign;

    @ApiModelProperty(value = "商品编号")
    private String productPref;

    @ApiModelProperty(value = "通用名称")
    private String commonName;

    @ApiModelProperty(value = "规格/型号")
    private String attributeSpecification;

    @ApiModelProperty(value = "基本单位(盒、支、箱)")
    private String unitName;

    @ApiModelProperty(value = "剂型-如:胶囊")
    private String dosageFormName;

    @ApiModelProperty(value = "生产厂家")
    private String manufacturer;

    @ApiModelProperty(value = "基本单位服用天数1-999")
    private Integer unitDays;

    @ApiModelProperty(value = "创建人")
    private Integer createUser;

    @ApiModelProperty(value = "创建时间")
    private Long createTime;

    @ApiModelProperty(value = "修改人")
    private Integer updateUser;

    @ApiModelProperty(value = "修改时间")
    private Long updateTime;

    @ApiModelProperty(value = "混合查询")
    private String mixedQuery;

    //商品名称
    @ApiModelProperty(value = "商品名称")
    private String productName;
    /**
     * 批准文号
     */
    @ApiModelProperty(value = "批准文号")
    private String approvalNumber;
    /**
     * 外码
     */
    @ApiModelProperty(value = "外码")
    private String pharmacyPref;
}