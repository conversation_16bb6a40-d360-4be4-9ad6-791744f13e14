package com.xyy.saas.web.api.module.product.controller;


import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.api.ProductPropertyApi;
import com.xyy.saas.product.core.dto.AddProductPropertyDto;
import com.xyy.saas.product.core.dto.ProductPropertyResult;
import com.xyy.saas.product.core.dto.QueryProductPropertyDto;
import com.xyy.saas.product.core.dto.UpdateProductPropertyDto;
import com.xyy.saas.web.api.module.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Api(tags = {"商品服务-商品属性接口"}, description = "商品属性 Controller", produces = MediaType.APPLICATION_JSON_VALUE)
@RestController
@RequestMapping("/product/property")
@Slf4j
public class ProductPropertyApiController {
    @Reference(version = "0.0.1")
    public ProductPropertyApi productPropertyApi;


    @ApiOperation(value="添加商品属性", notes="添加商品属性")
    @ApiResponses(value = {@ApiResponse(code = 0, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/add",method = RequestMethod.POST)
    public ResultVO<Boolean> add(@RequestHeader("commonRequestModel") String commonRequestModelStr, @RequestBody AddProductPropertyDto propertyDto) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        log.info("添加商品属性请求参数:{}", JSONObject.toJSONString(propertyDto));
        if (null != commonRequestModel && StringUtils.isNotBlank(commonRequestModel.getEmployeeId())){
            String employeeId = commonRequestModel.getEmployeeId();
            propertyDto.setCreateUser(Integer.valueOf(employeeId));
        }
        ResultVO<Boolean> resultVO = productPropertyApi.add(propertyDto);
        log.info("获取结果：{}", JSON.toJSONString(resultVO));
        return resultVO;
    }


    @ApiOperation(value="更新商品属性", notes="更新商品属性")
    @ApiResponses(value = {@ApiResponse(code = 0, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/update",method = RequestMethod.POST)
    public ResultVO<Boolean> update(HttpServletRequest request, @RequestBody UpdateProductPropertyDto propertyDto) {
        log.info("更新商品属性请求参数:{}", JSONObject.toJSONString(propertyDto));
        String employee = request.getHeader("employeeId");
        propertyDto.setCreateUser(employee);
        ResultVO<Boolean> resultVO = productPropertyApi.update(propertyDto);
        log.info("获取结果：{}", JSON.toJSONString(resultVO));
        return resultVO;
    }

    @ApiOperation(value="查询商品属性列表", notes="查询商品属性列表")
    @ApiResponses(value = {@ApiResponse(code = 0, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/list",method = RequestMethod.POST)
    public ResultVO<List<ProductPropertyResult>> list(HttpServletRequest request, @RequestBody QueryProductPropertyDto propertyDto) {
        log.info("查询商品属性列表请求参数:{}", JSONObject.toJSONString(propertyDto));
        String employee = request.getHeader("employeeId");
        propertyDto.setEmployeeId(Integer.parseInt(employee));
        ResultVO<List<ProductPropertyResult>> resultVO = productPropertyApi.list(propertyDto);
        log.info("获取结果：{}", JSON.toJSONString(resultVO));
        return resultVO;
    }

    @ApiOperation(value="更新商品属性", notes="更新商品属性")
    @ApiResponses(value = {@ApiResponse(code = 0, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/update/status",method = RequestMethod.POST)
    public ResultVO<Boolean> updateStatus(HttpServletRequest request, @RequestBody UpdateProductPropertyDto propertyDto) {
        log.info("更新商品属性请求参数:{}", JSONObject.toJSONString(propertyDto));
        String employee = request.getHeader("employeeId");
        propertyDto.setCreateUser(employee);
        ResultVO<Boolean> resultVO = productPropertyApi.updateStatus(propertyDto);
        log.info("获取结果：{}", JSON.toJSONString(resultVO));
        return resultVO;
    }
}
