package com.xyy.saas.web.api.module.product.util;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Map;


public class CheckNull {

    public static String nullToEmpty(Map<String, Object> map) {
        ObjectMapper s = new JsonDealUtils();
        try {
            return s.writeValueAsString(map);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return "";
    }

}
