package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 */
@ApiModel(description = "会员日商品")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-08-29T19:34:09.377+08:00")
public class MemberPreferentialProductSaveVo {
	/**
     * 主键id
     */
    @ApiModelProperty(value = "主键id", required = true, example = "1")
    private Long id;

    /**
     * guid
     */
    @ApiModelProperty(value = "guid", required = true, example = "b9e9c7e6-ca4b-11e9-a2a1-0235d2b38928")
    private String guid;

    /**
     * 会员日guid
     */
    @ApiModelProperty(value = "会员日guid", required = true, example = "b9e9c7e6-ca4b-11e9-a2a1-0235d2b38928")
    private String preferentiaoGuid;

    /**
     * 会员日对应商品选择表格
     */
    @ApiModelProperty(value = "会员日对应商品选择表格", required = true, example = "会员日对应商品选择表格")
    private String choiceContent;

    /**
     * 是否逻辑删除，0 有效 1 删除
     */
    @ApiModelProperty(value = "是否逻辑删除，0 有效 1 删除", required = true, example = "1")
    private Byte yn;

    /**
     * 机构码
     */
    @ApiModelProperty(value = "机构码", required = true, example = "机构码")
    private String organsign;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号", required = true, example = "版本号")
    private Integer baseVersion;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getPreferentiaoGuid() {
        return preferentiaoGuid;
    }

    public void setPreferentiaoGuid(String preferentiaoGuid) {
        this.preferentiaoGuid = preferentiaoGuid;
    }

    public String getChoiceContent() {
        return choiceContent;
    }

    public void setChoiceContent(String choiceContent) {
        this.choiceContent = choiceContent;
    }

    public Byte getYn() {
        return yn;
    }

    public void setYn(Byte yn) {
        this.yn = yn;
    }

    public String getOrgansign() {
        return organsign;
    }

    public void setOrgansign(String organsign) {
        this.organsign = organsign;
    }

    public Integer getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(Integer baseVersion) {
        this.baseVersion = baseVersion;
    }
}