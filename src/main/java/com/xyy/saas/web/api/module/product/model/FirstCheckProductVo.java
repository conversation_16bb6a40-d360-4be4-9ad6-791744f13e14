package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.Objects;

/**
 * 首营商品列表查询返回实体
 */
@ApiModel(description = "首营商品列表查询返回实体")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T18:00:37.938+08:00")

public class FirstCheckProductVo {
  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("pref")
  private String pref = null;

  @JsonProperty("productPref")
  private String productPref = null;

  @JsonProperty("status")
  private Byte status = null;

  @JsonProperty("approvalUser")
  private String approvalUser = null;

  @JsonProperty("approvalTime")
  private Date approvalTime = null;

  @JsonProperty("createUser")
  private String createUser = null;

  @JsonProperty("createTime")
  private Date createTime = null;

  @JsonProperty("updateUser")
  private String updateUser = null;

  @JsonProperty("updateTime")
  private Date updateTime = null;

  @JsonProperty("yn")
  private Byte yn = null;

  @JsonProperty("baseVersion")
  private String baseVersion = null;

  @JsonProperty("organSign")
  private String organSign = null;

  @JsonProperty("createTimeStr")
  private String createTimeStr = null;

  @JsonProperty("unitId")
  private Integer unitId = null;

  @JsonProperty("dosageFormId")
  private Integer dosageFormId = null;

  @JsonProperty("storageCondition")
  private Integer storageCondition = null;

  @JsonProperty("productType")
  private Integer productType = null;

  @JsonProperty("abcDividing")
  private Integer abcDividing = null;

  @JsonProperty("containingHempYn")
  private Byte containingHempYn = null;

  @JsonProperty("prescriptionClassification")
  private Integer prescriptionClassification = null;

  @JsonProperty("prescriptionYn")
  private Byte prescriptionYn = null;

  @JsonProperty("businessScope")
  private Integer businessScope = null;

  @JsonProperty("maintenanceType")
  private Integer maintenanceType = null;

  @JsonProperty("productFunctionCatagory")
  private Integer productFunctionCatagory = null;

  @JsonProperty("scoreProductYn")
  private Byte scoreProductYn = null;

  @JsonProperty("statusName")
  private Byte statusName = null;

  @JsonProperty("shelfPosition")
  private Integer shelfPosition = null;

  @JsonProperty("isProductHidden")
  private Byte isProductHidden = null;

  public FirstCheckProductVo id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * 主键
   * @return id
  **/
  @ApiModelProperty(value = "主键")


  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public FirstCheckProductVo pref(String pref) {
    this.pref = pref;
    return this;
  }

   /**
   * 编号
   * @return pref
  **/
  @ApiModelProperty(value = "编号")


  public String getPref() {
    return pref;
  }

  public void setPref(String pref) {
    this.pref = pref;
  }

  public FirstCheckProductVo productPref(String productPref) {
    this.productPref = productPref;
    return this;
  }

   /**
   * 商品编号
   * @return productPref
  **/
  @ApiModelProperty(value = "商品编号")


  public String getProductPref() {
    return productPref;
  }

  public void setProductPref(String productPref) {
    this.productPref = productPref;
  }

  public FirstCheckProductVo status(Byte status) {
    this.status = status;
    return this;
  }

   /**
   * 状态，1：录入成功；2：质量负责人审批成功；3：质量负责人审批驳回;4:企业负责人审批成功;5:企业负责人审批驳回
   * @return status
  **/
  @ApiModelProperty(value = "状态，1：录入成功；2：质量负责人审批成功；3：质量负责人审批驳回;4:企业负责人审批成功;5:企业负责人审批驳回")


  public Byte getStatus() {
    return status;
  }

  public void setStatus(Byte status) {
    this.status = status;
  }

  public FirstCheckProductVo approvalUser(String approvalUser) {
    this.approvalUser = approvalUser;
    return this;
  }

   /**
   * 审批人名称
   * @return approvalUser
  **/
  @ApiModelProperty(value = "审批人名称")


  public String getApprovalUser() {
    return approvalUser;
  }

  public void setApprovalUser(String approvalUser) {
    this.approvalUser = approvalUser;
  }

  public FirstCheckProductVo approvalTime(Date approvalTime) {
    this.approvalTime = approvalTime;
    return this;
  }

   /**
   * 审批时间
   * @return approvalTime
  **/
  @ApiModelProperty(value = "审批时间")


  public Date getApprovalTime() {
    return approvalTime;
  }

  public void setApprovalTime(Date approvalTime) {
    this.approvalTime = approvalTime;
  }

  public FirstCheckProductVo createUser(String createUser) {
    this.createUser = createUser;
    return this;
  }

   /**
   * 创建人
   * @return createUser
  **/
  @ApiModelProperty(value = "创建人")


  public String getCreateUser() {
    return createUser;
  }

  public void setCreateUser(String createUser) {
    this.createUser = createUser;
  }

  public FirstCheckProductVo createTime(Date createTime) {
    this.createTime = createTime;
    return this;
  }

   /**
   * 创建时间
   * @return createTime
  **/
  @ApiModelProperty(value = "创建时间")


  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public FirstCheckProductVo updateUser(String updateUser) {
    this.updateUser = updateUser;
    return this;
  }

   /**
   * 更新人
   * @return updateUser
  **/
  @ApiModelProperty(value = "更新人")


  public String getUpdateUser() {
    return updateUser;
  }

  public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
  }

  public FirstCheckProductVo updateTime(Date updateTime) {
    this.updateTime = updateTime;
    return this;
  }

   /**
   * 更新时间
   * @return updateTime
  **/
  @ApiModelProperty(value = "更新时间")


  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public FirstCheckProductVo yn(Byte yn) {
    this.yn = yn;
    return this;
  }

   /**
   * 删除标志，1:有效；0删除
   * @return yn
  **/
  @ApiModelProperty(value = "删除标志，1:有效；0删除")


  public Byte getYn() {
    return yn;
  }

  public void setYn(Byte yn) {
    this.yn = yn;
  }

  public FirstCheckProductVo baseVersion(String baseVersion) {
    this.baseVersion = baseVersion;
    return this;
  }

   /**
   * 操作版本号
   * @return baseVersion
  **/
  @ApiModelProperty(value = "操作版本号")


  public String getBaseVersion() {
    return baseVersion;
  }

  public void setBaseVersion(String baseVersion) {
    this.baseVersion = baseVersion;
  }

  public FirstCheckProductVo organSign(String organSign) {
    this.organSign = organSign;
    return this;
  }

   /**
   * 药店唯一标识
   * @return organSign
  **/
  @ApiModelProperty(value = "药店唯一标识")


  public String getOrganSign() {
    return organSign;
  }

  public void setOrganSign(String organSign) {
    this.organSign = organSign;
  }

  public FirstCheckProductVo createTimeStr(String createTimeStr) {
    this.createTimeStr = createTimeStr;
    return this;
  }

   /**
   * 创建时间字符串格式
   * @return createTimeStr
  **/
  @ApiModelProperty(value = "创建时间字符串格式")


  public String getCreateTimeStr() {
    return createTimeStr;
  }

  public void setCreateTimeStr(String createTimeStr) {
    this.createTimeStr = createTimeStr;
  }

  public FirstCheckProductVo unitId(Integer unitId) {
    this.unitId = unitId;
    return this;
  }

   /**
   * 单位名称
   * @return unitId
  **/
  @ApiModelProperty(value = "单位名称")


  public Integer getUnitId() {
    return unitId;
  }

  public void setUnitId(Integer unitId) {
    this.unitId = unitId;
  }

  public FirstCheckProductVo dosageFormId(Integer dosageFormId) {
    this.dosageFormId = dosageFormId;
    return this;
  }

   /**
   * 剂型名称
   * @return dosageFormId
  **/
  @ApiModelProperty(value = "剂型名称")


  public Integer getDosageFormId() {
    return dosageFormId;
  }

  public void setDosageFormId(Integer dosageFormId) {
    this.dosageFormId = dosageFormId;
  }

  public FirstCheckProductVo storageCondition(Integer storageCondition) {
    this.storageCondition = storageCondition;
    return this;
  }

   /**
   * 存储条件
   * @return storageCondition
  **/
  @ApiModelProperty(value = "存储条件")


  public Integer getStorageCondition() {
    return storageCondition;
  }

  public void setStorageCondition(Integer storageCondition) {
    this.storageCondition = storageCondition;
  }

  public FirstCheckProductVo productType(Integer productType) {
    this.productType = productType;
    return this;
  }

   /**
   * 商品类别
   * @return productType
  **/
  @ApiModelProperty(value = "商品类别")


  public Integer getProductType() {
    return productType;
  }

  public void setProductType(Integer productType) {
    this.productType = productType;
  }

  public FirstCheckProductVo abcDividing(Integer abcDividing) {
    this.abcDividing = abcDividing;
    return this;
  }

   /**
   * ABC分类
   * @return abcDividing
  **/
  @ApiModelProperty(value = "ABC分类")


  public Integer getAbcDividing() {
    return abcDividing;
  }

  public void setAbcDividing(Integer abcDividing) {
    this.abcDividing = abcDividing;
  }

  public FirstCheckProductVo containingHempYn(Byte containingHempYn) {
    this.containingHempYn = containingHempYn;
    return this;
  }

   /**
   * 是否含麻
   * @return containingHempYn
  **/
  @ApiModelProperty(value = "是否含麻")


  public Byte getContainingHempYn() {
    return containingHempYn;
  }

  public void setContainingHempYn(Byte containingHempYn) {
    this.containingHempYn = containingHempYn;
  }

  public FirstCheckProductVo prescriptionClassification(Integer prescriptionClassification) {
    this.prescriptionClassification = prescriptionClassification;
    return this;
  }

   /**
   * 处方分类
   * @return prescriptionClassification
  **/
  @ApiModelProperty(value = "处方分类")


  public Integer getPrescriptionClassification() {
    return prescriptionClassification;
  }

  public void setPrescriptionClassification(Integer prescriptionClassification) {
    this.prescriptionClassification = prescriptionClassification;
  }

  public FirstCheckProductVo prescriptionYn(Byte prescriptionYn) {
    this.prescriptionYn = prescriptionYn;
    return this;
  }

   /**
   * 登记处方
   * @return prescriptionYn
  **/
  @ApiModelProperty(value = "登记处方")


  public Byte getPrescriptionYn() {
    return prescriptionYn;
  }

  public void setPrescriptionYn(Byte prescriptionYn) {
    this.prescriptionYn = prescriptionYn;
  }

  public FirstCheckProductVo businessScope(Integer businessScope) {
    this.businessScope = businessScope;
    return this;
  }

   /**
   * 经营范围
   * @return businessScope
  **/
  @ApiModelProperty(value = "经营范围")


  public Integer getBusinessScope() {
    return businessScope;
  }

  public void setBusinessScope(Integer businessScope) {
    this.businessScope = businessScope;
  }

  public FirstCheckProductVo maintenanceType(Integer maintenanceType) {
    this.maintenanceType = maintenanceType;
    return this;
  }

   /**
   * 养护类型
   * @return maintenanceType
  **/
  @ApiModelProperty(value = "养护类型")


  public Integer getMaintenanceType() {
    return maintenanceType;
  }

  public void setMaintenanceType(Integer maintenanceType) {
    this.maintenanceType = maintenanceType;
  }

  public FirstCheckProductVo productFunctionCatagory(Integer productFunctionCatagory) {
    this.productFunctionCatagory = productFunctionCatagory;
    return this;
  }

   /**
   * 商品功能分类
   * @return productFunctionCatagory
  **/
  @ApiModelProperty(value = "商品功能分类")


  public Integer getProductFunctionCatagory() {
    return productFunctionCatagory;
  }

  public void setProductFunctionCatagory(Integer productFunctionCatagory) {
    this.productFunctionCatagory = productFunctionCatagory;
  }

  public FirstCheckProductVo scoreProductYn(Byte scoreProductYn) {
    this.scoreProductYn = scoreProductYn;
    return this;
  }

   /**
   * 是否积分商品
   * @return scoreProductYn
  **/
  @ApiModelProperty(value = "是否积分商品")


  public Byte getScoreProductYn() {
    return scoreProductYn;
  }

  public void setScoreProductYn(Byte scoreProductYn) {
    this.scoreProductYn = scoreProductYn;
  }

  public FirstCheckProductVo statusName(Byte statusName) {
    this.statusName = statusName;
    return this;
  }

   /**
   * 是否启用
   * @return statusName
  **/
  @ApiModelProperty(value = "是否启用")


  public Byte getStatusName() {
    return statusName;
  }

  public void setStatusName(Byte statusName) {
    this.statusName = statusName;
  }

  public FirstCheckProductVo shelfPosition(Integer shelfPosition) {
    this.shelfPosition = shelfPosition;
    return this;
  }

   /**
   * 架位
   * @return shelfPosition
  **/
  @ApiModelProperty(value = "架位")


  public Integer getShelfPosition() {
    return shelfPosition;
  }

  public void setShelfPosition(Integer shelfPosition) {
    this.shelfPosition = shelfPosition;
  }

  public FirstCheckProductVo isProductHidden(Byte isProductHidden) {
    this.isProductHidden = isProductHidden;
    return this;
  }

   /**
   * 是否隐藏商品
   * @return isProductHidden
  **/
  @ApiModelProperty(value = "是否隐藏商品")


  public Byte getIsProductHidden() {
    return isProductHidden;
  }

  public void setIsProductHidden(Byte isProductHidden) {
    this.isProductHidden = isProductHidden;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    FirstCheckProductVo firstCheckProductVo = (FirstCheckProductVo) o;
    return Objects.equals(this.id, firstCheckProductVo.id) &&
        Objects.equals(this.pref, firstCheckProductVo.pref) &&
        Objects.equals(this.productPref, firstCheckProductVo.productPref) &&
        Objects.equals(this.status, firstCheckProductVo.status) &&
        Objects.equals(this.approvalUser, firstCheckProductVo.approvalUser) &&
        Objects.equals(this.approvalTime, firstCheckProductVo.approvalTime) &&
        Objects.equals(this.createUser, firstCheckProductVo.createUser) &&
        Objects.equals(this.createTime, firstCheckProductVo.createTime) &&
        Objects.equals(this.updateUser, firstCheckProductVo.updateUser) &&
        Objects.equals(this.updateTime, firstCheckProductVo.updateTime) &&
        Objects.equals(this.yn, firstCheckProductVo.yn) &&
        Objects.equals(this.baseVersion, firstCheckProductVo.baseVersion) &&
        Objects.equals(this.organSign, firstCheckProductVo.organSign) &&
        Objects.equals(this.createTimeStr, firstCheckProductVo.createTimeStr) &&
        Objects.equals(this.unitId, firstCheckProductVo.unitId) &&
        Objects.equals(this.dosageFormId, firstCheckProductVo.dosageFormId) &&
        Objects.equals(this.storageCondition, firstCheckProductVo.storageCondition) &&
        Objects.equals(this.productType, firstCheckProductVo.productType) &&
        Objects.equals(this.abcDividing, firstCheckProductVo.abcDividing) &&
        Objects.equals(this.containingHempYn, firstCheckProductVo.containingHempYn) &&
        Objects.equals(this.prescriptionClassification, firstCheckProductVo.prescriptionClassification) &&
        Objects.equals(this.prescriptionYn, firstCheckProductVo.prescriptionYn) &&
        Objects.equals(this.businessScope, firstCheckProductVo.businessScope) &&
        Objects.equals(this.maintenanceType, firstCheckProductVo.maintenanceType) &&
        Objects.equals(this.productFunctionCatagory, firstCheckProductVo.productFunctionCatagory) &&
        Objects.equals(this.scoreProductYn, firstCheckProductVo.scoreProductYn) &&
        Objects.equals(this.statusName, firstCheckProductVo.statusName) &&
        Objects.equals(this.shelfPosition, firstCheckProductVo.shelfPosition) &&
        Objects.equals(this.isProductHidden, firstCheckProductVo.isProductHidden);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, pref, productPref, status, approvalUser, approvalTime, createUser, createTime, updateUser, updateTime, yn, baseVersion, organSign, createTimeStr, unitId, dosageFormId, storageCondition, productType, abcDividing, containingHempYn, prescriptionClassification, prescriptionYn, businessScope, maintenanceType, productFunctionCatagory, scoreProductYn, statusName, shelfPosition, isProductHidden);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class FirstCheckProductVo {\n");

    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    pref: ").append(toIndentedString(pref)).append("\n");
    sb.append("    productPref: ").append(toIndentedString(productPref)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    approvalUser: ").append(toIndentedString(approvalUser)).append("\n");
    sb.append("    approvalTime: ").append(toIndentedString(approvalTime)).append("\n");
    sb.append("    createUser: ").append(toIndentedString(createUser)).append("\n");
    sb.append("    createTime: ").append(toIndentedString(createTime)).append("\n");
    sb.append("    updateUser: ").append(toIndentedString(updateUser)).append("\n");
    sb.append("    updateTime: ").append(toIndentedString(updateTime)).append("\n");
    sb.append("    yn: ").append(toIndentedString(yn)).append("\n");
    sb.append("    baseVersion: ").append(toIndentedString(baseVersion)).append("\n");
    sb.append("    organSign: ").append(toIndentedString(organSign)).append("\n");
    sb.append("    createTimeStr: ").append(toIndentedString(createTimeStr)).append("\n");
    sb.append("    unitId: ").append(toIndentedString(unitId)).append("\n");
    sb.append("    dosageFormId: ").append(toIndentedString(dosageFormId)).append("\n");
    sb.append("    storageCondition: ").append(toIndentedString(storageCondition)).append("\n");
    sb.append("    productType: ").append(toIndentedString(productType)).append("\n");
    sb.append("    abcDividing: ").append(toIndentedString(abcDividing)).append("\n");
    sb.append("    containingHempYn: ").append(toIndentedString(containingHempYn)).append("\n");
    sb.append("    prescriptionClassification: ").append(toIndentedString(prescriptionClassification)).append("\n");
    sb.append("    prescriptionYn: ").append(toIndentedString(prescriptionYn)).append("\n");
    sb.append("    businessScope: ").append(toIndentedString(businessScope)).append("\n");
    sb.append("    maintenanceType: ").append(toIndentedString(maintenanceType)).append("\n");
    sb.append("    productFunctionCatagory: ").append(toIndentedString(productFunctionCatagory)).append("\n");
    sb.append("    scoreProductYn: ").append(toIndentedString(scoreProductYn)).append("\n");
    sb.append("    statusName: ").append(toIndentedString(statusName)).append("\n");
    sb.append("    shelfPosition: ").append(toIndentedString(shelfPosition)).append("\n");
    sb.append("    isProductHidden: ").append(toIndentedString(isProductHidden)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

