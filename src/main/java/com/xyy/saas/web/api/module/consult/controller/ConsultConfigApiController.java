package com.xyy.saas.web.api.module.consult.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.common.module.dto.SystemConfigWEBDto;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.consult.cores.api.ConsultConfigApi;
import com.xyy.saas.web.api.module.consult.model.ConsultConfigVo;
import io.swagger.annotations.*;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/member/consultConfig")
@Api(value = "consultConfig", description = "远程问诊配置API")
public class ConsultConfigApiController {

	private static final Logger logger = LogManager.getLogger(ConsultConfigApiController.class);
    @Reference(version = "0.0.2")
    private ConsultConfigApi consultConfigApi;



    @ApiOperation(value = "获取远程问诊配置", notes = "获取远程问诊配置", response = Boolean.class, tags = {"consultConfig",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/getConfig", method = RequestMethod.POST,produces =  "application/json",consumes = "application/json")
    ResponseEntity<ResultVO> getConfig(@RequestHeader(name = "organSign", required = true) String organSign) {
        return new ResponseEntity(ResultVO.createSuccess(consultConfigApi.getConfig(organSign)), HttpStatus.OK);
    }

    @ApiOperation(value = "设置远程问诊配置", notes = "参数remoteInquiry", response = Boolean.class, tags = {"consultConfig",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/setConfig", method = RequestMethod.POST,produces =  "application/json",consumes = "application/json")
    ResponseEntity<ResultVO> setConfig(@RequestHeader(name = "organSign", required = true) String organSign,
                                       @ApiParam(value = "系统配置", required = true) @RequestBody ConsultConfigVo consultConfigVo) {
        Byte remoteInquiry = consultConfigVo.getRemoteInquiry();
        if (remoteInquiry == null) {
            return new ResponseEntity(new ResultVO(-1, "参数remoteInquiry不能为空", false), HttpStatus.OK);
        }
        SystemConfigWEBDto dto = new SystemConfigWEBDto();
        dto.setRemoteInquiry(consultConfigVo.getRemoteInquiry());
        return new ResponseEntity(ResultVO.createSuccess(consultConfigApi.setConfig(dto, organSign)), HttpStatus.OK);
    }

}
