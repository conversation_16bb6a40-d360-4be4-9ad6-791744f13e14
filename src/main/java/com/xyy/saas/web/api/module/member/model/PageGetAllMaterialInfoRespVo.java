package com.xyy.saas.web.api.module.member.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class PageGetAllMaterialInfoRespVo {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 药店机构号
     */
    private String organSign;

    /**
     * 药店机构号名字
     */
    private String organSignName;

    /**
     * 申请时药店机构号名字
     */
    private String organSignApplyName;

    /**
     * 收件人姓名
     */
    private String name;

    /**
     * 收件人手机号
     */
    private String phone;

    /**
     * 快递公司名称
     */
    private String company;

    /**
     * 快递单号
     */
    private String number;

    /**
     * 申请时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyTime;

    /**
     * 寄出时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendTime;

    /**
     * 申请人ID
     */
    private String applyEmployeeId;

    /**
     * 申请人名字
     */
    private String applyEmployeeName;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 详情地址
     */
    private String address;

    /**
     * 状态,
     * 1 申请中 3 重复申请已作废 5 已提交至厂家 7 已寄出
     */
    private Integer state;

    /**
     * state 对应的值
     */
    private String stateVal;
}
