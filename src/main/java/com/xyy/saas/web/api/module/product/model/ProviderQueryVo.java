package com.xyy.saas.web.api.module.product.model;

import io.swagger.annotations.ApiModel;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

@ApiModel(description = "供应商实体查询对象")
public class ProviderQueryVo {

    private Long id;//供应商主键id，供应商删除和供应商禁用启用时必传
    private Integer rows;//条数
    private Integer page;//当前页码
    private String name;//供应商混合查询条件
    private Integer flag;//供应商禁用时传0表示禁用，传1表示启用
    private Byte used;//是否启用，
    //新增根据录入时间查询，与创建时间比较
    private String startTime;
    private String endTime;
    private String sidx;//排序字段
    private String sord;//倒序or正序排序

    /** 供应商类别*/
    private Integer providerType;

    /** 状态：1--审核通过，2--审核中，3,--审核未通过*/
    private Byte status;

    /** 创建人*/
    private String createUser;

    /***导出供应商id集合*/
    private String[] prefs;
    //供应商内码
    private String pref;

    //机构号
    private String organSign;

    private Byte noxyy; //查小药药 0:不查  1:查询

    private Byte model;//经营模式 1、单体 2、连锁 3、联营

    private Byte organSignType;//机构类型 1、门店  3、总部

    private Byte relateYn; //是否关联 0:未关联 1:已关联

    private String relateYnStr; //是否关联 0:未关联 1:已关联

    public Byte getRelateYn() {
        return relateYn;
    }

    public void setRelateYn(Byte relateYn) {
        this.relateYn = relateYn;
    }

    public String getRelateYnStr() {
        return relateYnStr;
    }

    public void setRelateYnStr(String relateYnStr) {
        this.relateYnStr = relateYnStr;
    }

    public Byte getOrganSignType() {
        return organSignType;
    }

    public void setOrganSignType(Byte organSignType) {
        this.organSignType = organSignType;
    }

    public Byte getModel() {
        return model;
    }

    public void setModel(Byte model) {
        this.model = model;
    }

    public String getSidx() {
        return sidx;
    }

    public void setSidx(String sidx) {
        this.sidx = sidx;
    }

    public String getSord() {
        return sord;
    }

    public void setSord(String sord) {
        this.sord = sord;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Byte getUsed() {
        return used;
    }

    public void setUsed(Byte used) {
        this.used = used;
    }

    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    public Integer getProviderType() {
        return providerType;
    }

    public void setProviderType(Integer providerType) {
        this.providerType = providerType;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String[] getPrefs() {
        return prefs;
    }

    public void setPrefs(String[] prefs) {
        this.prefs = prefs;
    }

    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public Byte getNoxyy() {
        return noxyy;
    }

    public void setNoxyy(Byte noxyy) {
        this.noxyy = noxyy;
    }
}
