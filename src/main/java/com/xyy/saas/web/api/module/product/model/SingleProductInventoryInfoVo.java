package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * @ClassName SingleProductInventoryInfoVo
 * @Description 单个商品批号库存列表信息类
 * <AUTHOR>
 * @Date 2019/8/30 19:06
 * @Version 1.0
 **/
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

@ApiModel(description = "单个商品批号库存列表信息类")
public class SingleProductInventoryInfoVo {
    @JsonProperty("lotNumber")
    private String lotNumber;
    @JsonProperty("producedDate")
    private String producedDate;
    @JsonProperty("expirationDate")
    private String expirationDate;
    @JsonProperty("stockNumber")
    private BigDecimal stockNumber;

    @ApiModelProperty(value = "批号")
    public String getLotNumber() {
        return lotNumber;
    }

    public void setLotNumber(String lotNumber) {
        this.lotNumber = lotNumber;
    }

    @ApiModelProperty(value = "生产日期")
    public String getProducedDate() {
        return producedDate;
    }

    public void setProducedDate(String producedDate) {
        this.producedDate = producedDate;
    }

    @ApiModelProperty(value = "有效期至")
    public String getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(String expirationDate) {
        this.expirationDate = expirationDate;
    }

    @ApiModelProperty(value = "当前库存")
    public BigDecimal getStockNumber() {
        return stockNumber;
    }

    public void setStockNumber(BigDecimal stockNumber) {
        this.stockNumber = stockNumber;
    }

}
