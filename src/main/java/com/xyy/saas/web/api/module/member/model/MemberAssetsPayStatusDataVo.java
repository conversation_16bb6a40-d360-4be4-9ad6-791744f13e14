package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 会员资产支付状态查询
 */
@ApiModel(description = "会员资产支付状态查询加密参数Vo")
public class MemberAssetsPayStatusDataVo implements Serializable {

    /**
     * 会员guid
     */
    @ApiModelProperty(value = "会员guid", example = "584a4460-2144-47c6-b7f7-d85b33d3322c")
    private String memberGuid;
    /**
     * bussinessNo
     */
    @ApiModelProperty(value = "bussinessNo", example = "1240098777")
    private String bussinessNo;
    /**
     * 需要查询的支付状态
     */
    @ApiModelProperty(value = "需要查询的支付状态", example = "1 券 2 会员资产")
    private List<Integer> needPayStatus;


    public String getMemberGuid() {
        return memberGuid;
    }

    public void setMemberGuid(String memberGuid) {
        this.memberGuid = memberGuid;
    }

    public String getBussinessNo() {
        return bussinessNo;
    }

    public void setBussinessNo(String bussinessNo) {
        this.bussinessNo = bussinessNo;
    }

    public List<Integer> getNeedPayStatus() {
        return needPayStatus;
    }

    public void setNeedPayStatus(List<Integer> needPayStatus) {
        this.needPayStatus = needPayStatus;
    }
}
