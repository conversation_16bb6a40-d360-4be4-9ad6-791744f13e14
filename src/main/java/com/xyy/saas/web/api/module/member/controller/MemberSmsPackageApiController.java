package com.xyy.saas.web.api.module.member.controller;


import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.member.core.api.MemberSmsPackageApi;
import com.xyy.saas.member.core.dto.MemberSmsPackageDto;
import com.xyy.saas.web.api.module.member.model.MemberSmsPackageVo;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.dto.result.EmployeeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-08-29T21:37:06.377+08:00")

@Slf4j
@Controller
@RequestMapping("/member/memberSmsPackage")
@Api(value = "memberSmsPackage", description = "the memberSmsPackage API")
public class MemberSmsPackageApiController {

    @Reference(version = "0.0.1")
    private MemberSmsPackageApi smsPackageApi;
    @Reference(version = "0.0.1")
    private EmployeeApi employeeApi;

    private Pattern pattern = Pattern.compile("^[0-9]+(\\.[0-9]{1,2})?$");

    @ApiOperation(value = "短信套餐", notes = "短信套餐", response = MemberSmsPackageDto.class, tags={ "memberSmsPackage", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberSmsPackageDto.class) })
    @RequestMapping(value = "/getMemberSmsPackageList",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getMemberSmsPackageList(@RequestHeader(name = "organSign", required = true) String organSign,
                                                            @RequestBody MemberSmsPackageVo smsPackageDto){
        MemberSmsPackageDto dto = new MemberSmsPackageDto();
        BeanUtils.copyProperties(smsPackageDto, dto);
//        dto.setPage(dto.getPage() == null ? 0 : dto.getPage());
//        dto.setRows(dto.getRows() == null ? 10 : dto.getRows());
        dto.setStatus((byte)1);
        //按照短信条数大小从小到大排序
        dto.setOrderParams(1);
        return new ResponseEntity(ResultVO.createSuccess(smsPackageApi.getMemberSmsPackage(dto)), HttpStatus.OK);
    }


    @ApiOperation(value = "新建短信套餐", notes = "新建短信套餐", response = MemberSmsPackageDto.class, tags={ "memberSmsPackage", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberSmsPackageDto.class) })
    @RequestMapping(value = "/saveMemberSmsPackage",  method = RequestMethod.POST)
    public ResponseEntity<ResultVO> saveMemberSmsPackage(@RequestHeader(name = "organSign", required = true) String organSign,
                                                         @RequestHeader(name = "employee", required = true) Integer employeeId,
                                                         @RequestBody MemberSmsPackageVo smsPackageDto){

        boolean flagCount = Pattern.matches("^[1-9][0-9]*$", smsPackageDto.getCount().toString());
        if(!flagCount){
            return new ResponseEntity(new ResultVO(-1, "数量 必须为正整数", false), HttpStatus.OK);
        }
        Matcher price = pattern.matcher(smsPackageDto.getPrice().toString());
        if(!price.matches()){
            return new ResponseEntity(new ResultVO(-1, "价格 支持最多两位小数", false), HttpStatus.OK);
        }
        Matcher cp = pattern.matcher(smsPackageDto.getCostPrice().toString());
        if(!cp.matches()){
            return new ResponseEntity(new ResultVO(-1, "原价 支持最多两位小数", false), HttpStatus.OK);
        }
        if(1 == smsPackageDto.getIsSale() && smsPackageDto.getPrice().compareTo(smsPackageDto.getCostPrice()) == 1){
            return new ResponseEntity(new ResultVO(-1, "价格必须小于原价", false), HttpStatus.OK);
        }
        MemberSmsPackageDto dto = new MemberSmsPackageDto();
        BeanUtils.copyProperties(smsPackageDto, dto);
        com.xyy.saas.common.util.ResultVO<EmployeeDto> resultVO= employeeApi.queryEmployeeById(employeeId);
        if(Objects.nonNull(resultVO) && Objects.nonNull(resultVO.getResult())){
            dto.setCreateUser(((EmployeeDto)resultVO.getResult()).getName());
        }else{
            return new ResponseEntity(new ResultVO(-1, "用户不存在", false), HttpStatus.OK);
        }
        return new ResponseEntity(ResultVO.createSuccess(smsPackageApi.saveMemberSmsPackage(dto)), HttpStatus.OK);
    }


    @ApiOperation(value = "更新短信套餐", notes = "更新短信套餐", response = MemberSmsPackageDto.class, tags={ "memberSmsPackage", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberSmsPackageDto.class) })
    @RequestMapping(value = "/updateMemberSmsPackage",  method = RequestMethod.POST)
    public ResponseEntity<ResultVO> updateMemberSmsPackage(@RequestHeader(name = "organSign", required = true) String organSign,
                                                           @RequestHeader(name = "employee", required = true) String employeeId,
                                                           @RequestBody MemberSmsPackageVo smsPackageDto){
        MemberSmsPackageDto dto = new MemberSmsPackageDto();
        BeanUtils.copyProperties(smsPackageDto, dto);
        com.xyy.saas.common.util.ResultVO<EmployeeDto> resultVO= employeeApi.queryEmployeeById(Integer.getInteger(employeeId));
        if(Objects.nonNull(resultVO) && Objects.nonNull(resultVO.getResult())){
            dto.setCreateUser(((EmployeeDto)resultVO.getResult()).getName());
        }else{
            return new ResponseEntity(new ResultVO(-1, "用户不存在", false), HttpStatus.OK);
        }
        return new ResponseEntity(ResultVO.createSuccess(smsPackageApi.updateMemberSmsPackage(dto)), HttpStatus.OK);
    }


    @ApiOperation(value = "删除短信套餐", notes = "删除短信套餐", response = MemberSmsPackageDto.class, tags={ "memberSmsPackage", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberSmsPackageDto.class) })
    @RequestMapping(value = "/deleteMemberSmsPackage",  method = RequestMethod.POST)
    public ResponseEntity<ResultVO> deleteMemberSmsPackage(@RequestHeader(name = "organSign", required = true) String organSign,
                                                           @RequestBody MemberSmsPackageVo smsPackageDto){
        return new ResponseEntity(ResultVO.createSuccess(smsPackageApi.deleteMemberSmsPackage(smsPackageDto.getId())), HttpStatus.OK);
    }

}
