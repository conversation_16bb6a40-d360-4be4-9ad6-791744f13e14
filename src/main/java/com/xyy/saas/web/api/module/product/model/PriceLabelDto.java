package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.product.core.dto.PageDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@ApiModel(description = "价签实体")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

public class PriceLabelDto extends PageDto implements Serializable{
    @JsonProperty("id")
    private Long id; //价签模板id
    @JsonProperty("templetName")
    private String templetName;//模板名称
    @JsonProperty("systemTempletYn")
    private Integer  systemTempletYn;//是否系统模板  1：是 0：否
    @JsonProperty("templetType")
    private Integer templetType;//模板类型对应的value值
    @JsonProperty("printNumber")
    private Integer printNumber;//打印数量
    @JsonProperty("cutLineYn")
    private Integer cutLineYn;//是否有分割线  1：有 0：没有
    @JsonProperty("content")
    private String content;//模板内容  jion数据
    @JsonProperty("contentlist")
    private List<HashMap>  contentlist;//   模板内容
    @JsonProperty("createUser")
    private String createUser; //创建人
    @JsonProperty("createTime")
    private Date createTime; //创建时间
    @JsonProperty("updateUser")
    private String updateUser; //更新人
    @JsonProperty("updateTime")
    private Date updateTime; //更新时间
    @JsonProperty("organSign")
    private String organSign; //药店唯一标识
    @JsonProperty("yn")
    private Integer yn;//逻辑删除 1 有效 0 删除
    @JsonProperty("prefList")
    private  List<String> prefList;   //商品编号集合
    @ApiModelProperty(value = "type：1表示新模板")
    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    @JsonProperty("type")
    private Byte type;//1代表新模板

    @ApiModelProperty(value = "模板编号")
    public String getTempletPref() {
        return templetPref;
    }

    public void setTempletPref(String templetPref) {
        this.templetPref = templetPref;
    }

    @JsonProperty("templetPref")
    private String templetPref;//模板编号

    public Integer getCellSize() {
        return cellSize;
    }

    public void setCellSize(Integer cellSize) {
        this.cellSize = cellSize;
    }

    @JsonProperty("cellSize")
    private Integer cellSize;//单元格大小，默认为2
    @ApiModelProperty(value = "商品编号集合")
    public List<String> getPrefList() {
        return prefList;
    }

    public void setPrefList(List<String> prefList) {
        this.prefList = prefList;
    }

    @ApiModelProperty(value = "价签模板id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    @ApiModelProperty(value = "模板名称")
    public String getTempletName() {
        return templetName;
    }

    public void setTempletName(String templetName) {
        this.templetName = templetName;
    }
    @ApiModelProperty(value = "是否系统模板  1：是 0：否")
    public Integer getSystemTempletYn() {
        return systemTempletYn;
    }
    public void setSystemTempletYn(Integer systemTempletYn) {
        this.systemTempletYn = systemTempletYn;
    }

    @ApiModelProperty(value = "模板类型对应的value值")
    public Integer getTempletType() {
        return templetType;
    }

    public void setTempletType(Integer templetType) {
        this.templetType = templetType;
    }
    @ApiModelProperty(value = "打印数量")
    public Integer getPrintNumber() {
        return printNumber;
    }

    public void setPrintNumber(Integer printNumber) {
        this.printNumber = printNumber;
    }
    @ApiModelProperty(value = "是否有分割线  1：有 0：没有")
    public Integer getCutLineYn() {
        return cutLineYn;
    }

    public void setCutLineYn(Integer cutLineYn) {
        this.cutLineYn = cutLineYn;
    }

    @ApiModelProperty(value = "模板内容  jion数据")
    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
    @ApiModelProperty(value = "模板内容")
    public List<HashMap> getContentlist() {
        return contentlist;
    }

    public void setContentlist(List<HashMap> contentlist) {
        this.contentlist = contentlist;
    }
    @ApiModelProperty(value = "创建人")
    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }
    @ApiModelProperty(value = "创建时间")
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    @ApiModelProperty(value = "更新人")
    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }
    @ApiModelProperty(value = "更新时间")
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    @ApiModelProperty(value = "药店唯一标识")
    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }
    @ApiModelProperty(value = "逻辑删除 1 有效 0 删除")
    public Integer getYn() {
        return yn;
    }

    public void setYn(Integer yn) {
        this.yn = yn;
    }
}
