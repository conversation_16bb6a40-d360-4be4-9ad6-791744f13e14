package com.xyy.saas.web.api.module.supplier.controller.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.DateUtil;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.supplier.api.SupplierCurrentAccountApi;
import com.xyy.saas.supplier.dto.BalanceDealAccountDto;
import com.xyy.saas.web.api.module.supplier.controller.SupplierCurrentAccountApiSwag;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


@Controller
public class SupplierCurrentAccountController implements SupplierCurrentAccountApiSwag {
    private static final Logger logger = LoggerFactory.getLogger(SupplierCurrentAccountController.class);

    @Reference(version = "0.0.1")
    private SupplierCurrentAccountApi supplierCurrentAccountApi;

    @Override
    public ResultVO<PageInfo<BalanceDealAccountDto>> query(HttpServletRequest request,@RequestBody BalanceDealAccountDto balanceDealAccountDto){
        String organSign = request.getHeader("organSign");
        PageInfo pageInfo=new PageInfo();
        pageInfo.setPageSize(balanceDealAccountDto.getRows()==null?50:balanceDealAccountDto.getRows());
        pageInfo.setPageNum(balanceDealAccountDto.getPage()==null?1:balanceDealAccountDto.getPage());
        balanceDealAccountDto.setOrganSign(organSign);
        String startDateStr = balanceDealAccountDto.getBeginTimeStr();
        String endDateStr = balanceDealAccountDto.getEndTimeStr();
        if(!StringUtils.isEmpty(startDateStr)){
            balanceDealAccountDto.setBeginTime(DateUtil.parseStrToDate(startDateStr, DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS));
        }
        if(!StringUtils.isEmpty(endDateStr)){
            balanceDealAccountDto.setEndTime(DateUtil.parseStrToDate(endDateStr, DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS));
        }
        ResultVO<PageInfo<BalanceDealAccountDto>> resultVO = supplierCurrentAccountApi.findDealAccount(pageInfo, balanceDealAccountDto);
        if (resultVO != null){
            if (resultVO.getCode() != 0) {
                logger.error("balanceDealAccountApi.findDealAccount:" + resultVO.getMsg());
            }
        }
        return resultVO;
    }

    @Override
    public ResultVO<Boolean> updateDealAccountBatch(@RequestBody List<BalanceDealAccountDto> dealAccountDtoList){
        return supplierCurrentAccountApi.updateDealAccountBatch(dealAccountDtoList);
    }

    @Override
    public ResultVO<String> handleInitDealAccountSettlement(@RequestBody List<BalanceDealAccountDto> dealAccountDtoList){
        return supplierCurrentAccountApi.handleInitDealAccountSettlement(dealAccountDtoList);
    }
}
