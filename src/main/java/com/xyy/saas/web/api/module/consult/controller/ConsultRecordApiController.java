package com.xyy.saas.web.api.module.consult.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.common.util.DateUtil;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.consult.cores.api.ConsultRecordApi;
import com.xyy.saas.consult.cores.api.PatientBaseApi;
import com.xyy.saas.consult.cores.dto.ConsultRecordDailyDto;
import com.xyy.saas.consult.cores.dto.ConsultRecordDetailDto;
import com.xyy.saas.consult.cores.dto.ConsultRecordQueryDto;
import com.xyy.saas.consult.cores.dto.PatientBaseQueryDto;
import com.xyy.saas.web.api.module.consult.model.ConsultRecordDetailVo;
import com.xyy.saas.web.api.module.consult.model.ConsultRecordQueryVo;
import com.xyy.saas.web.api.module.utils.ExportExcelUtil;
import com.xyy.saas.web.api.module.utils.MailUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/member/consultRecord")
@Api(value = "consultRecord", description = "远程问诊记录API")
public class ConsultRecordApiController {

	private static final Logger logger = LogManager.getLogger(ConsultRecordApiController.class);

    @Reference(version = "0.0.2")
    private ConsultRecordApi consultRecordApi;

    @Reference(version = "0.0.2")
    private PatientBaseApi patientBaseApi;


    @ApiOperation(value = "保存远程问诊服务记录", notes = "如果患者不存者，创建新患者，否则更新患者信息", response = Boolean.class, tags = {"consultRecord",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/create", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> create(@RequestHeader(name = "organSign", required = true) String organSign,
                                    @RequestBody ConsultRecordDetailVo consultRecordDetailVo) {
        if (StringUtils.isEmpty(consultRecordDetailVo.getName())) {
            return new ResponseEntity(new ResultVO(-1, "参数name不能为空", false), HttpStatus.OK);
        }
        if (consultRecordDetailVo.getBirthday() == null) {
            return new ResponseEntity(new ResultVO(-1, "参数birthday不能为空", false), HttpStatus.OK);
        }
        String idCard = consultRecordDetailVo.getIdCard();
        if (!StringUtils.isEmpty(idCard) && !idCard.matches("^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$")) {
            return new ResponseEntity(new ResultVO(-1, "参数idcard不合法", false), HttpStatus.OK);
        }
        if (StringUtils.isEmpty(consultRecordDetailVo.getName())) {
            return new ResponseEntity(new ResultVO(-1, "参数name不能为空", false), HttpStatus.OK);
        }
        if (StringUtils.isEmpty(consultRecordDetailVo.getTelephone())) {
            return new ResponseEntity(new ResultVO(-1, "参数telephone不能为空", false), HttpStatus.OK);
        }
        if (!consultRecordDetailVo.getTelephone().replaceAll("1[0-9]{10}", "").equals("")) {
            return new ResponseEntity(new ResultVO(-1, "参数telephone不合法", false), HttpStatus.OK);
        }
        if (StringUtils.isEmpty(consultRecordDetailVo.getPatientCondition())) {
            return new ResponseEntity(new ResultVO(-1, "参数patientCondition不能为空", false), HttpStatus.OK);
        }
        if (consultRecordDetailVo.getSex() == null) {
            consultRecordDetailVo.setSex((byte) 1);
        }
        if (consultRecordDetailVo.getMarried() == null) {
            consultRecordDetailVo.setMarried((byte) 0);
        }
        if (consultRecordDetailVo.getAllergy() == null) {
            consultRecordDetailVo.setMarried((byte) 0);
        }
        // 判断患者是否已存在，逻辑为姓名和手机号都一样
        Long patientId = consultRecordDetailVo.getPatientId();
        PatientBaseQueryDto query = new PatientBaseQueryDto();
        query.setId(consultRecordDetailVo.getPatientId());
        query.setOrganSign(organSign);
        query.setName(consultRecordDetailVo.getName());
        query.setTelephone(consultRecordDetailVo.getTelephone());
        int count = patientBaseApi.selectByNameAndTelephone(query);
        if (count > 0) {
            return new ResponseEntity(new ResultVO(-2, "患者已存在", false), HttpStatus.OK);
        }
        return new ResponseEntity(ResultVO.createSuccess(createConsultRecord(organSign, consultRecordDetailVo)), HttpStatus.OK);
    }

    private ConsultRecordDetailDto createConsultRecord(String organSign, ConsultRecordDetailVo consultRecordDetailVo) {
        ConsultRecordDetailDto dto = new ConsultRecordDetailDto();
        BeanUtils.copyProperties(consultRecordDetailVo, dto);
        dto.setOrganSign(organSign);
        return consultRecordApi.save(dto);
    }

    @ApiOperation(value = "跳转远程问诊页面", notes = "会自动判断并保存该条远程问诊记录，并返回第三方远程问诊url", response = ConsultRecordDetailDto.class, tags = {"consultRecord",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ConsultRecordDetailDto.class)})
    @RequestMapping(value = "/gotoConsult", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> gotoConsult(@RequestHeader(name = "organSign", required = true) String organSign,
                                         @RequestBody ConsultRecordDetailVo consultRecordDetailVo) {
        if (StringUtils.isEmpty(consultRecordDetailVo.getName())) {
            return new ResponseEntity(new ResultVO(-1, "参数name不能为空", false), HttpStatus.OK);
        }
        if (consultRecordDetailVo.getSex() == null) {
            return new ResponseEntity(new ResultVO(-1, "参数sex不能为空", false), HttpStatus.OK);
        }
        if (consultRecordDetailVo.getBirthday() == null) {
            return new ResponseEntity(new ResultVO(-1, "参数birthday不能为空", false), HttpStatus.OK);
        }
        if (StringUtils.isEmpty(consultRecordDetailVo.getTelephone())) {
            return new ResponseEntity(new ResultVO(-1, "参数telephone不能为空", false), HttpStatus.OK);
        }
        if (!consultRecordDetailVo.getTelephone().replaceAll("1[0-9]{10}", "").equals("")) {
            return new ResponseEntity(new ResultVO(-1, "参数telephone不合法", false), HttpStatus.OK);
        }
        if (consultRecordDetailVo.getMarried() == null) {
            consultRecordDetailVo.setMarried((byte) 0);
        }
        if (consultRecordDetailVo.getAllergy() == null) {
            consultRecordDetailVo.setMarried((byte) 0);
        }
        // 判断患者是否已存在，逻辑为姓名和手机号都一样
        PatientBaseQueryDto query = new PatientBaseQueryDto();
        query.setId(consultRecordDetailVo.getPatientId());
        query.setOrganSign(organSign);
        query.setName(consultRecordDetailVo.getName());
        query.setTelephone(consultRecordDetailVo.getTelephone());
        int count = patientBaseApi.selectByNameAndTelephone(query);
        if (count > 0) {
            return new ResponseEntity(new ResultVO(-2, "患者已存在", false), HttpStatus.OK);
        }
        ConsultRecordDetailDto dto;
        // 记录未保存，先保存记录
        if (consultRecordDetailVo.getId() == null) {
            dto = createConsultRecord(organSign, consultRecordDetailVo);
        }
        // 记录已经保存
        else {
            dto = new ConsultRecordDetailDto();
            BeanUtils.copyProperties(consultRecordDetailVo, dto);
            dto.setOrganSign(organSign);
        }
        if (dto == null || dto.getId() == null ||  dto.getId() <= 0) {
            logger.error("保存问诊记录失败: " + consultRecordDetailVo);
            return new ResponseEntity(new ResultVO(-2, "生成跳转连接失败", false), HttpStatus.OK);
        }
        // 返回加密编码第三方远程问诊url
        String aesData = consultRecordApi.encryptConsultParam(dto);
        // 返回更新后的id和patient，防止重复生成新数据
        Map<String, Object> map = new HashMap<>();
        map.put("id",  dto.getId());
        map.put("patientId", dto.getPatientId());
        map.put("url", aesData);
        return new ResponseEntity(ResultVO.createSuccess(map), HttpStatus.OK);
    }

    @ApiOperation(value = "根据主键查询远程问诊记录，关联患者信息", notes = "根据主键查询远程问诊记录，关联患者信息", response = ConsultRecordDetailDto.class, tags = {"consultRecord",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ConsultRecordDetailDto.class)})
    @RequestMapping(value = "/selectById", method = RequestMethod.POST,produces =  "application/json",consumes = "application/json")
    ResponseEntity<ResultVO> selectById(@RequestHeader(name = "organSign", required = true) String organSign,
                                        @RequestBody ConsultRecordDetailVo consultRecordDetailVo) {
        Long id = consultRecordDetailVo.getId();
        if (id == null || id <= 0) {
            return new ResponseEntity(new ResultVO(-1, "参数id不能为空", false), HttpStatus.OK);
        }
        ConsultRecordDetailDto dto = consultRecordApi.selectById(id, organSign);
        return new ResponseEntity(ResultVO.createSuccess(dto), HttpStatus.OK);
    }

    @ApiOperation(value = "根据查询条件查询远程记录列表", notes = "根据查询条件查询远程记录列表", response = ConsultRecordDetailDto.class, tags = {"consultRecord",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ConsultRecordDetailDto.class)})
    @RequestMapping(value = "/selectListByQuery", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> selectListByQuery(@RequestHeader(name = "organSign", required = true) String organSign,
                                               @RequestBody ConsultRecordQueryVo consultRecordQueryVo) {
        ConsultRecordQueryDto query = new ConsultRecordQueryDto();
        BeanUtils.copyProperties(consultRecordQueryVo, query);
        query.setOrganSign(organSign);
        logger.info("xxxxx查询问诊信息列表，请求参数: " + consultRecordQueryVo);
        logger.info("xxxxx查询问诊信息列表，查询参数: " + query);
        return new ResponseEntity(ResultVO.createSuccess(consultRecordApi.selectListByQuery(query)), HttpStatus.OK);
    }
    

    @ApiOperation(value = "根据患者id查询最新一条远程问诊记录", notes = "根据患者id查询最新一条远程问诊记录", response = JSONObject.class, tags = {"consultRecord",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = JSONObject.class)})
    @RequestMapping(value = "/selectLastestByPatientId", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> selectLastestByPatientId(@RequestHeader(name = "organSign", required = true) String organSign,
                                                      @RequestBody ConsultRecordQueryVo consultRecordQueryVo) {
        Long patientId = consultRecordQueryVo.getPatientId();
        if (patientId == null || patientId <= 0) {
            new ResponseEntity(new ResultVO(-1, "参数patientId不能为空", false), HttpStatus.OK);
        }
        return new ResponseEntity(ResultVO.createSuccess(consultRecordApi.selectLastestByPatientId(patientId, organSign)), HttpStatus.OK);
    }

    @ApiOperation(value = "检查问诊是否结束", notes = "检查问诊是否结束", response = JSONObject.class, tags = {"consultRecord",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = JSONObject.class)})
    @RequestMapping(value = "/chechEnd", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> chechEnd(@RequestHeader(name = "organSign", required = true) String organSign,
                                      @RequestBody ConsultRecordDetailVo consultRecordDetailVo) {
        Long id = consultRecordDetailVo.getId();
        if (id == null || id <= 0) {
            new ResponseEntity(new ResultVO(-1, "参数id不能为空", false), HttpStatus.OK);
        }
        return new ResponseEntity(ResultVO.createSuccess(consultRecordApi.checkEndById(id, organSign)), HttpStatus.OK);
    }

    @ApiOperation(value = "发送友德医对账日明细的邮件", notes = "发送友德医对账日明细的邮件  \n" +
            "debug: true-开启debug模式，只发送到发件箱, false-关闭debug模式  \n" +
            "获取上周五到此刻的数据", response = JSONObject.class, tags = {"consultRecord",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = JSONObject.class)})
    @RequestMapping(value = "/sendEmailOfConsultRecordDaily", method = {RequestMethod.POST, RequestMethod.GET},produces =  "application/json")
    ResponseEntity<ResultVO> sendEmailOfConsultRecordDaily(@RequestParam(required = false,  defaultValue = "false") Boolean debug) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.DATE_FORMAT_YYYYMMDD);
            SimpleDateFormat sdf2 = new SimpleDateFormat(DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS);
            // 获取当天的开始时间和结束时间
            Date now = new Date();
            Calendar cal = Calendar.getInstance();
            Date startTime = null;

            cal.set(Calendar.DAY_OF_WEEK, 6);
            cal.add(Calendar.WEEK_OF_YEAR, -1);
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            startTime = cal.getTime();

            logger.info("发送友徳医对账明细" + sdf.format(now));
//            startTime = sdf2.parse("2019-09-01 00:00:00");
//            endTime = sdf2.parse("2019-09-30 23:59:59");
            // 查询友德医对账日明细
            List<ConsultRecordDailyDto> list = consultRecordApi.getDailyConsultDetailV2(startTime, now);
            String[] header = new String[]{"远程问诊id", "处方创建时间", "机构号", "药店名称", "所属省份",
                    "服务类型", "是否开方", "处方pdf原始地址", "第三方处方单号", "患者id", "患者姓名",
                    "患者联系电话", "医生姓名", "诊断内容"};
            String title = "友徳医对账明细表";
            List<String[]> dataList = new ArrayList<>();
            list = list == null ? new ArrayList<>() : list;
            list.forEach(item -> {
                String[] row = new String[]{
                        item.getId().toString(),
                        sdf2.format(item.getCreateTime()),
                        item.getOrganSign(),
                        item.getDrugstoreName(),
                        item.getProvince(),
                        item.getConsultServiceType(),
                        item.getHasThirdPrescription() ? "是" : "否",
                        Optional.ofNullable(item.getPrescriptionPdf()).orElse(""),
                        Optional.ofNullable(item.getThirdNo()).orElse(""),
                        Optional.ofNullable(item.getPatientId()).orElse(0l).toString(),
                        item.getPatientName(),
                        item.getTelephone(),
                        Optional.ofNullable(item.getDoctorName()).orElse(""),
                        Optional.ofNullable(item.getDiagnosticContent()).orElse(""),
                };
                dataList.add(row);
            });
            // 创建excel并写入文件
            XSSFWorkbook excel = ExportExcelUtil.createExcelOfUsers(header, title + sdf.format(now), title, dataList, null);
            File file = File.createTempFile(title, ".xls");
            FileOutputStream out = new FileOutputStream(file);
            excel.write(out);
            // 发送邮件
            String addressTo = null;
            if (debug) {
                addressTo = "<EMAIL>";
            } else {
                addressTo = "<EMAIL>,<EMAIL>";
            }
            MailUtil.sendMail(title, "Hi:\n本周明细，请查收。", addressTo, "", "", file, title + sdf.format(now) + ".xls");
            return new ResponseEntity(ResultVO.createSuccess(true), HttpStatus.OK);
        } catch (Exception e) {
            logger.error("发送友徳医对账明细失败。", e);
            return new ResponseEntity(ResultVO.createSuccess(false), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "调取远程问诊分页", notes = "调取远程问诊分页", response = ConsultRecordDetailDto.class, tags = {"consultRecord",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ConsultRecordDetailDto.class)})
    @RequestMapping(value = "/getPrescriptionPage", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> getPrescriptionPage(@RequestHeader(name = "organSign", required = true) String organSign,
                                                 @RequestBody ConsultRecordQueryVo consultRecordQueryVo) {
        ConsultRecordQueryDto query = new ConsultRecordQueryDto();
        BeanUtils.copyProperties(consultRecordQueryVo, query);
        Integer pageNum = consultRecordQueryVo.getPage();
        pageNum = pageNum == null ? 1 : pageNum;
        Integer pageSize = consultRecordQueryVo.getRows();
        pageSize = pageSize == null ? 10 : pageSize;
        query.setPageNum(pageNum);
        query.setPageSize(pageSize);
        // 查询未调取过的未过期的，按姓名正序排列
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.HOUR, -24);
        query.setValidTime(cal.getTime());
        query.setHasPrescription(1);
        query.setUsed((byte) 0);
        query.setSort((byte) 2);
        query.setOrganSign(organSign);
        query.setValidStatus((byte) 1);
        // 查询时间
        String queryDate = consultRecordQueryVo.getQueryDate();
        if (!StringUtils.isEmpty(queryDate)) {
            SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS);
            try {
                query.setStartTime(sdf.parse(queryDate + " 00:00:00"));
                query.setEndTime(sdf.parse(queryDate + " 23:59:59"));
            } catch (Exception e) {
                return new ResponseEntity(new ResultVO(-1, "参数queryDate格式不正确", false), HttpStatus.OK);
            }
        }
        logger.info("调取远程问诊分页: {}", query);
        return new ResponseEntity(ResultVO.createSuccess(consultRecordApi.selectListByQuery(query)), HttpStatus.OK);
    }

}

