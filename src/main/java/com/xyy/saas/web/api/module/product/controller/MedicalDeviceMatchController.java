package com.xyy.saas.web.api.module.product.controller;


import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.api.ProductDrugStandardApi;
import com.xyy.saas.product.core.dto.ProductCountryDrugStandardDto;
import com.xyy.saas.product.core.dto.ProductDrugStandardDto;
import com.xyy.saas.udi.core.api.ProductMedicalDeviceApi;
import com.xyy.saas.udi.core.dto.MedicalDeviceQueryDto;
import com.xyy.saas.udi.core.dto.MedicalDeviceUdiQueryDto;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import com.xyy.user.module.enums.DrugstoreBizModelEnum;
import com.xyy.user.module.enums.DrugstoreTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Objects;

@Controller
public class MedicalDeviceMatchController implements MedicalDeviceMatchApi{

    private static final Logger logger = LoggerFactory.getLogger(MedicalDeviceMatchController.class);

    @Reference(version = "0.0.1")
    private ProductMedicalDeviceApi medicalDeviceApi;

    @Reference(version = "0.0.1")
    private ProductDrugStandardApi standardApi;

    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;


    @Override
    public ResponseEntity<ResultVO> list(HttpServletRequest request, @Valid @RequestBody MedicalDeviceQueryDto dto) {
        if(null == dto){
            logger.info("查询参数为空！");
            return new ResponseEntity(new ResultVO(ResultCodeEnum.ERROR_INVALID_PARAM),HttpStatus.OK);
        }
//        String organSign = request.getHeader("organSign");
//        dto.setOrganSign(organSign);
        SaaSDrugstoreDto drugstoreDto = drugstoreApi.getDrugstoreByOrganSign(dto.getOrganSign());
        if (drugstoreDto != null
                && Objects.equals(drugstoreDto.getOrganSignType(), DrugstoreTypeEnum.DRUGSTORE.getKey())
                && Objects.equals(drugstoreDto.getBizModel(), DrugstoreBizModelEnum.CHAIN_STORE.getKey())) {
            dto.setOrganSign(drugstoreDto.getHeadquartersOrganSign());
        }
        PageInfo pageInfo = new PageInfo();
        Integer rows = dto.getRows();
        Integer page = dto.getPage();
        if(null == page || page == 0){
            page = 1;
        }
        if(null == rows || rows == 0){
            rows = 50;
        }
        pageInfo.setPageNum(page);
        pageInfo.setPageSize(rows);
        return new ResponseEntity(standardApi.queryDeviceDrugStandard(pageInfo,dto),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> matchMedicalDevice(HttpServletRequest request, @Valid @RequestBody MedicalDeviceUdiQueryDto dto) {
        logger.info("医疗器械匹配请求参数：{}", JSON.toJSONString(dto));
        dto.setOrganSign(request.getHeader("organSign"));
        PageInfo pageInfo = new PageInfo();
        Integer rows = dto.getRows();
        Integer page = dto.getPage();
        if(null == page || page == 0){
            page = 1;
        }
        if(null == rows || rows == 0){
            rows = 50;
        }
        pageInfo.setPageNum(page);
        pageInfo.setPageSize(rows);
        Byte udiYn = dto.getIsCountryMedicalDeviceUdiYn();
        if (!ObjectUtils.isEmpty(udiYn) && udiYn == 1){
            return new ResponseEntity(medicalDeviceApi.queryCountryDeviceUdi(pageInfo,dto), HttpStatus.OK);
        }
        return new ResponseEntity(medicalDeviceApi.queryDeviceUdi(pageInfo,dto), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> matchProduct(HttpServletRequest request, @Valid @RequestBody ProductDrugStandardDto dto) {
        dto.setOrganSign(request.getHeader("organSign"));
        String employeeId = request.getHeader("employeeId");
        return new ResponseEntity(standardApi.deviceMatchProduct(dto,employeeId),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> countryMatchProduct(HttpServletRequest request, @Valid @RequestBody ProductCountryDrugStandardDto dto) {
        dto.setOrganSign(request.getHeader("organSign"));
        String employeeId = request.getHeader("employeeId");
        return new ResponseEntity(standardApi.countDeviceMatchProduct(dto,employeeId), HttpStatus.OK);
    }
}
