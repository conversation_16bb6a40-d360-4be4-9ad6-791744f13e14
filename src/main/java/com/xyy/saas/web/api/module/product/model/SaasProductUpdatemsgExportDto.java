package com.xyy.saas.web.api.module.product.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

@ApiModel(description = "质量变更导出传参")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-07T16:41:52.348+08:00")
public class SaasProductUpdatemsgExportDto implements Serializable {
    @ApiModelProperty(value = "页码")
    private Integer page;
    @ApiModelProperty(value = "每页显示条数")
    private Integer rows;
    @ApiModelProperty(value = "单据编号")
    private String recallNo;
    @ApiModelProperty(value = "变更类型 1:供应商 0：商品")
    private Integer updateType;
    @ApiModelProperty(value = "申请人")
    private Integer applyUserId;
    @ApiModelProperty(value = "查询开始时间")
    private String beginTime;
    @ApiModelProperty(value = "查询结束时间")
    private String endTime;
    @ApiModelProperty(value = "审核状态 3:拒绝 2:同意 1：审核中")
    private Integer auditState;
    @ApiModelProperty(value = "导出名称")
    private String excelName;

    private String  ids;

    public String getIds() {
        return ids;
    }

    public void setIds(String ids) {
        this.ids = ids;
    }

    private List<Integer> detailIds;

    public List<Integer> getDetailIds() {
        return detailIds;
    }

    public void setDetailIds(List<Integer> detailIds) {
        this.detailIds = detailIds;
    }

    public String getExcelName() {
        return excelName;
    }

    public void setExcelName(String excelName) {
        this.excelName = excelName;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public String getRecallNo() {
        return recallNo;
    }

    public void setRecallNo(String recallNo) {
        this.recallNo = recallNo;
    }

    public Integer getUpdateType() {
        return updateType;
    }

    public void setUpdateType(Integer updateType) {
        this.updateType = updateType;
    }

    public Integer getApplyUserId() {
        return applyUserId;
    }

    public void setApplyUserId(Integer applyUserId) {
        this.applyUserId = applyUserId;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Integer getAuditState() {
        return auditState;
    }

    public void setAuditState(Integer auditState) {
        this.auditState = auditState;
    }
}
