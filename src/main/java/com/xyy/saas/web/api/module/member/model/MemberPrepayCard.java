package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 会员储值卡详情
 * <AUTHOR>
 */
@ApiModel(description = "会员储值卡详情")
public class MemberPrepayCard implements Serializable {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 会员guid
     */
    @ApiModelProperty(value = "会员guid")
    private String memberGuid;

    /**
     * 储值卡实充余额
     */
    @ApiModelProperty(value = "储值卡实充余额")
    private BigDecimal amount;

    /**
     * 储值卡赠送余额
     */
    @ApiModelProperty(value = "储值卡赠送余额")
    private BigDecimal bonus;

    /**
     * 余额总额（实充余额 + 赠送余额）
     */
    @ApiModelProperty(value = "余额总额（实充余额 + 赠送余额）")
    private BigDecimal totalAmount;

    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识")
    private String organsign;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 最后充值时间
     */
    @ApiModelProperty(value = "最后充值时间")
    private Date lastDepositTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMemberGuid() {
        return memberGuid;
    }

    public void setMemberGuid(String memberGuid) {
        this.memberGuid = memberGuid;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getBonus() {
        return bonus;
    }

    public void setBonus(BigDecimal bonus) {
        this.bonus = bonus;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getOrgansign() {
        return organsign;
    }

    public void setOrgansign(String organsign) {
        this.organsign = organsign;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Date getLastDepositTime() {
        return lastDepositTime;
    }

    public void setLastDepositTime(Date lastDepositTime) {
        this.lastDepositTime = lastDepositTime;
    }

    @Override
    public String toString() {
        return "MemberPrepayCard{" +
                "id=" + id +
                ", memberGuid='" + memberGuid + '\'' +
                ", amount=" + amount +
                ", bonus=" + bonus +
                ", totalAmount=" + totalAmount +
                ", organsign='" + organsign + '\'' +
                ", createTime=" + createTime +
                ", createUser='" + createUser + '\'' +
                ", updateTime=" + updateTime +
                ", updateUser='" + updateUser + '\'' +
                ", lastDepositTime=" + lastDepositTime +
                '}';
    }
}