package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class MemberChangePoint {



    /**
     * 会员id
     */
    private Long memberId;

    /**
     * 总积分
     */
    private BigDecimal quantity;

    /**
     * 操作人
     */
    private String operator;

    /**
     * pos编号
     */
    private String ticketNo;

    /**
     * 变动原因
     */
    private String editReason;

    /**
     * 0--减,1--加
     */
    private Integer editType;

    /**
     * 业务单据编号
     */
    private String bussinessNo;

    /**
     * 机构标识
     */
    private String organSign;


    private String guid;

    @ApiModelProperty(value = "业务号")
    public String getBussinessNo() {
        return bussinessNo;
    }

    public void setBussinessNo(String bussinessNo) {
        this.bussinessNo = bussinessNo;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    @ApiModelProperty(value = "操作积分")
    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    @ApiModelProperty(value = "操作人")
    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    @ApiModelProperty(value = "小票号")
    public String getTicketNo() {
        return ticketNo;
    }

    public void setTicketNo(String ticketNo) {
        this.ticketNo = ticketNo;
    }

    @ApiModelProperty(value = "修改原因")
    public String getEditReason() {
        return editReason;
    }

    public void setEditReason(String editReason) {
        this.editReason = editReason;
    }

    @ApiModelProperty(value = "编辑类型 1增 2减")
    public Integer getEditType() {
        return editType;
    }

    public void setEditType(Integer editType) {
        this.editType = editType;
    }

    @ApiModelProperty(value = "机构标识")
    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }


    @ApiModelProperty(value = "会员 唯一标识")
    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }
}
