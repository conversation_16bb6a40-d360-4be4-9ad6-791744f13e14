package com.xyy.saas.web.api.module.product.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "咨询记录列表", description = "咨询记录列表")
public class ConsultListVo {

    @ApiModelProperty(
            value = "患者名称",
            name = "userName"
    )
    private String userName;

    @ApiModelProperty(
            value = "页码",
            name = "page"
    )
    private Integer page;
    @ApiModelProperty(
            value = "每页数据量",
            name = "rows"
    )
    private Integer rows;

    @ApiModelProperty(
            value = "开始时间",
            name = "beginTime"
    )
    private String beginTime;

    @ApiModelProperty(
            value = "结束时间",
            name = "endTime"
    )
    private String endTime;

    @ApiModelProperty(
            value = "药师id",
            name = "pharmacistId"
    )
    private Integer pharmacistId;


    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Integer getPharmacistId() {
        return pharmacistId;
    }

    public void setPharmacistId(Integer pharmacistId) {
        this.pharmacistId = pharmacistId;
    }
}
