package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @annotation:
 * <AUTHOR>
 * @create 2019-05-08 19:29
 **/
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-05-08T12:28:35.123+08:00")
public class InventoryBaseDto implements Serializable {
    private static final long serialVersionUID = 3151211339135407620L;
    @JsonProperty("id")
    private Long id = null;

    @JsonProperty("pref")
    private String pref = null;

    @JsonProperty("productPref")
    private String productPref = null;

    @JsonProperty("stockNumber")
    private BigDecimal stockNumber = null;

    @JsonProperty("stockAmount")
    private BigDecimal stockAmount = null;

    @JsonProperty("costPrice")
    private BigDecimal costPrice = null;

    @JsonProperty("lastCostPrice")
    private BigDecimal lastCostPrice = null;

    @JsonProperty("lastProvidePref")
    private String lastProvidePref = null;

    @JsonProperty("lastInTime")
    private Date lastInTime = null;

    @JsonProperty("createUser")
    private String createUser = null;

    @JsonProperty("createTime")
    private Date createTime = null;

    @JsonProperty("updateUser")
    private String updateUser = null;

    @JsonProperty("updateTime")
    private Date updateTime = null;

    @JsonProperty("remark")
    private String remark = null;

    @JsonProperty("version")
    private Integer version = null;

    @JsonProperty("yn")
    private byte yn = 1;

    @JsonProperty("baseVersion")
    private String baseVersion = null;

    @JsonProperty("organsign")
    private String organsign = null;

    public InventoryBaseDto id(Long id) {
        this.id = id;
        return this;
    }
    /**
     * 主键id
     * @return id
     **/
    @ApiModelProperty(value = "主键id")


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public InventoryBaseDto pref(String pref) {
        this.pref = pref;
        return this;
    }

    /**
     * 编号
     * @return pref
     **/
    @ApiModelProperty(value = "编号")


    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    public InventoryBaseDto productPref(String productPref) {
        this.productPref = productPref;
        return this;
    }

    /**
     * 商品编号
     * @return productPref
     **/
    @ApiModelProperty(value = "商品编号")


    public String getProductPref() {
        return productPref;
    }

    public void setProductPref(String productPref) {
        this.productPref = productPref;
    }

    public InventoryBaseDto stockNumber(BigDecimal stockNumber) {
        this.stockNumber = stockNumber;
        return this;
    }

    /**
     * 库存数量
     * @return stockNumber
     **/
    @ApiModelProperty(value = "库存数量")

    @Valid

    public BigDecimal getStockNumber() {
        return stockNumber;
    }

    public void setStockNumber(BigDecimal stockNumber) {
        this.stockNumber = stockNumber;
    }

    public InventoryBaseDto stockAmount(BigDecimal stockAmount) {
        this.stockAmount = stockAmount;
        return this;
    }

    /**
     * 库存金额
     * @return stockAmount
     **/
    @ApiModelProperty(value = "库存金额")

    @Valid

    public BigDecimal getStockAmount() {
        return stockAmount;
    }

    public void setStockAmount(BigDecimal stockAmount) {
        this.stockAmount = stockAmount;
    }

    public InventoryBaseDto costPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
        return this;
    }

    /**
     * 成本价
     * @return costPrice
     **/
    @ApiModelProperty(value = "成本价")

    @Valid

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public InventoryBaseDto lastCostPrice(BigDecimal lastCostPrice) {
        this.lastCostPrice = lastCostPrice;
        return this;
    }

    /**
     * 最后一次成本价
     * @return lastCostPrice
     **/
    @ApiModelProperty(value = "最后一次成本价")

    @Valid

    public BigDecimal getLastCostPrice() {
        return lastCostPrice;
    }

    public void setLastCostPrice(BigDecimal lastCostPrice) {
        this.lastCostPrice = lastCostPrice;
    }

    public InventoryBaseDto lastProvidePref(String lastProvidePref) {
        this.lastProvidePref = lastProvidePref;
        return this;
    }

    /**
     * 最后供应商
     * @return lastProvidePref
     **/
    @ApiModelProperty(value = "最后供应商")


    public String getLastProvidePref() {
        return lastProvidePref;
    }

    public void setLastProvidePref(String lastProvidePref) {
        this.lastProvidePref = lastProvidePref;
    }

    public InventoryBaseDto lastInTime(Date lastInTime) {
        this.lastInTime = lastInTime;
        return this;
    }

    /**
     * 最后一次入库时间
     * @return lastInTime
     **/
    @ApiModelProperty(value = "最后一次入库时间")

    @Valid

    public Date getLastInTime() {
        return lastInTime;
    }

    public void setLastInTime(Date lastInTime) {
        this.lastInTime = lastInTime;
    }

    public InventoryBaseDto createUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    /**
     * 创建人
     * @return createUser
     **/
    @ApiModelProperty(value = "创建人")


    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public InventoryBaseDto createTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    /**
     * 创建时间
     * @return createTime
     **/
    @ApiModelProperty(value = "创建时间")

    @Valid

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public InventoryBaseDto updateUser(String updateUser) {
        this.updateUser = updateUser;
        return this;
    }

    /**
     * 更新人
     * @return updateUser
     **/
    @ApiModelProperty(value = "更新人")


    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public InventoryBaseDto updateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    /**
     * 更新时间
     * @return updateTime
     **/
    @ApiModelProperty(value = "更新时间")

    @Valid

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public InventoryBaseDto remark(String remark) {
        this.remark = remark;
        return this;
    }

    /**
     * 更新人
     * @return remark
     **/
    @ApiModelProperty(value = "更新人")


    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public InventoryBaseDto version(Integer version) {
        this.version = version;
        return this;
    }

    /**
     * 更新人
     * @return version
     **/
    @ApiModelProperty(value = "更新人")


    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public InventoryBaseDto yn(byte yn) {
        this.yn = yn;
        return this;
    }

    /**
     * 更新人
     * @return yn
     **/
    @ApiModelProperty(value = "更新人")

    public byte getYn() {
        return yn;
    }

    public void setYn(byte yn) {
        this.yn = yn;
    }

    public InventoryBaseDto baseVersion(String baseVersion) {
        this.baseVersion = baseVersion;
        return this;
    }

    /**
     * 版本号
     * @return baseVersion
     **/
    @ApiModelProperty(value = "版本号")


    public String getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(String baseVersion) {
        this.baseVersion = baseVersion;
    }

    public InventoryBaseDto organsign(String organsign) {
        this.organsign = organsign;
        return this;
    }

    /**
     * 机构代码
     * @return organsign
     **/
    @ApiModelProperty(value = "机构代码")


    public String getOrgansign() {
        return organsign;
    }

    public void setOrgansign(String organsign) {
        this.organsign = organsign;
    }



}

