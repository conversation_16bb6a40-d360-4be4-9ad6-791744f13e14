package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.pharmacy.core.dto.PharmacyConsultSaveDto;
import com.xyy.saas.pharmacy.core.dto.SaasPharmacyConsultDrugVo;
import com.xyy.saas.web.api.module.product.model.*;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-10-21T10:11:31.623+08:00")

@Api(value = "药学服务", description = "the pharmacyApi API")
@RequestMapping("/product/pharmacy")
public interface PharmacyApi {

    @ApiOperation(value = "新增药学记录", notes = "新增药学记录", response = SaasPharmacyConsultDrugVo.class, tags={ "药学服务", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = SaasPharmacyConsultDrugVo.class) })

    @RequestMapping(value = "/save",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> savePharmacyConsult(HttpServletRequest request, @ApiParam(value = "咨询信息", required = true) @RequestBody PharmacyConsultSaveVo pharmacyConsultSaveVo);


    @ApiOperation(value = "(商品)标准库列表", notes = "(商品)标准库列表", response = AddDrugListResult.class, tags={ "药学服务", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = AddDrugListResult.class) })

    @RequestMapping(value = "/addList",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> addDrugList(@RequestBody AddDrugListVo vo, HttpServletRequest request);

    @ApiOperation(value = "用药检查", notes = "用药检查", response = PharmacyConsultSaveVo.class, tags={ "药学服务", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = PharmacyConsultSaveVo.class) })

    @RequestMapping(value = "/incompatibility",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> incompatibility(HttpServletRequest request, @RequestBody IncompatibilityVo vo);

    @ApiOperation(value = "咨询记录列表", notes = "咨询记录列表", response = PharmacyConsultSaveVo.class, tags={ "药学服务", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = PharmacyConsultSaveVo.class) })

    @RequestMapping(value = "/consultList",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> consultList(HttpServletRequest request, @RequestBody ConsultListVo vo);

    @ApiOperation(value = "咨询记录药品明细", notes = "咨询记录明细", response = AddDrugListResult.class, tags={ "药学服务", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = AddDrugListResult.class) })

    @RequestMapping(value = "/consultDrugDetails",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> consultDrugDetails(HttpServletRequest request, @RequestBody ConsultDrugDetailsVo vo);

    @ApiOperation(value = "指导单(说明书)", notes = "指导单(说明书)", response = DirectionAndGuidanceResultVo.class, tags={ "药学服务", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = DirectionAndGuidanceResultVo.class) })

    @RequestMapping(value = "/guidance",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> guidance(HttpServletRequest request, @RequestBody DirectionAndGuidanceVo vo);

    @ApiOperation(value = "咨询记录患者信息明细", notes = "咨询记录患者信息明细", response = PharmacyConsultSaveVo.class, tags={ "药学服务", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = PharmacyConsultSaveVo.class) })

    @RequestMapping(value = "/consultUserDetails",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> consultUserDetails(HttpServletRequest request, @RequestBody ConsultUserDetailsVo vo);

    @ApiOperation(value = "查询机构下的全体员工", notes = "查询机构下的全体员工", response = PharmacyConsultSaveDto.class, tags={ "药学服务", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = PharmacyConsultSaveDto.class) })

    @RequestMapping(value = "/queryAllEmployeeByOrganSign",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> queryAllEmployeeByOrganSign(HttpServletRequest request);

    @ApiOperation(value = "查询列表中出现的员工", notes = "查询列表中出现的员工", response = PharmacyConsultSaveDto.class, tags={ "药学服务", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = PharmacyConsultSaveDto.class) })

    @RequestMapping(value = "/listAllEmployeeByOrganSign",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> listAllEmployeeByOrganSign(HttpServletRequest request);

}