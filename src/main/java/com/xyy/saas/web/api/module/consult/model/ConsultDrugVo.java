package com.xyy.saas.web.api.module.consult.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 远程问诊曾用药品/联合用药Vo
 * <AUTHOR>
 */
@ApiModel(description = "远程问诊曾用药品/联合用药")
public class ConsultDrugVo {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", hidden = true)
    private Long id;

    /**
     * 远程问诊记录id
     */
    @ApiModelProperty(value = "远程问诊记录id", required = true)
    private Long consultRecordId;

    /**
     * 药品编号
     */
    @ApiModelProperty(value = "药品编号", required = true)
    private String productPref;

    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识", required = true)
    private String organSign;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号", hidden = true)
    private Integer baseVersion;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getConsultRecordId() {
        return consultRecordId;
    }

    public void setConsultRecordId(Long consultRecordId) {
        this.consultRecordId = consultRecordId;
    }

    public String getProductPref() {
        return productPref;
    }

    public void setProductPref(String productPref) {
        this.productPref = productPref == null ? null : productPref.trim();
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign == null ? null : organSign.trim();
    }

    public Integer getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(Integer baseVersion) {
        this.baseVersion = baseVersion;
    }

    @Override
    public String toString() {
        return "ConsultDrug{" +
                "id=" + id +
                ", consultRecordId=" + consultRecordId +
                ", productPref='" + productPref + '\'' +
                ", organSign='" + organSign + '\'' +
                ", baseVersion=" + baseVersion +
                '}';
    }
}