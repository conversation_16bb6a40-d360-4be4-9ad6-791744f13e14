package com.xyy.saas.web.api.module.consult.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.consult.cores.api.ConsultServiceOrderApi;
import com.xyy.saas.consult.cores.dto.ConsultServiceOrderDto;
import com.xyy.saas.pay.core.api.PayInfoApi;
import com.xyy.saas.pay.core.dto.GeneratorQRCodeResultDto;
import com.xyy.saas.pay.core.dto.PayInfoDto;
import com.xyy.saas.pay.core.emum.ChinaUmsBillStatus;
import com.xyy.saas.web.api.module.consult.model.ConsultServiceOrderVo;
import com.xyy.saas.web.api.module.consult.model.ConsultServicePayVo;
import io.swagger.annotations.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/member/consultServiceOrder")
@Api(value = "consultServiceOrder", description = "远程问诊服务订单API")
public class ConsultServiceOrderApiController {

    private static final Logger logger = LogManager.getLogger(ConsultServiceOrderApiController.class);


    @Reference( version = "0.0.2")
    private ConsultServiceOrderApi consultServiceOrderApi;
    @Reference( version = "0.0.1")
    private PayInfoApi payInfoApi;

    @ApiOperation(value = "根据主键查询远程问诊服务订单", notes = "根据主键查询远程问诊服务订单", response = ConsultServiceOrderDto.class, tags = {"consultServiceOrder",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ConsultServiceOrderDto.class)})
    @RequestMapping(value = "/selectById", method = RequestMethod.POST,produces =  "application/json",consumes = "application/json")
    ResponseEntity<ResultVO> selectById(@RequestHeader(name = "organSign", required = true) String organSign,
                                        @ApiParam(value = "主键", required = true) @RequestBody ConsultServiceOrderVo consultServiceOrderVo) {
        Long id = consultServiceOrderVo.getId();
        if (id == null || id <= 0) {
            return new ResponseEntity(new ResultVO(-1, "参数id不合法", false), HttpStatus.OK);
        }
        return new ResponseEntity(ResultVO.createSuccess(consultServiceOrderApi.selectById(id)), HttpStatus.OK);
    }

    @ApiOperation(value = "根据订单号查询远程问诊服务订单", notes = "根据订单号查询远程问诊服务订单", response = ConsultServiceOrderDto.class, tags = {"consultServiceOrder",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ConsultServiceOrderDto.class)})
    @RequestMapping(value = "/selectByOrderId", method = RequestMethod.POST,produces =  "application/json",consumes = "application/json")
    ResponseEntity<ResultVO> selectByOrderId(@RequestHeader(name = "organSign", required = true) String organSign,
                                             @ApiParam(value = "订单号", required = true) @RequestBody ConsultServiceOrderVo consultServiceOrderVo) {
        String orderId = consultServiceOrderVo.getOrderId();
        if (StringUtils.isEmpty(orderId)) {
            return new ResponseEntity(new ResultVO(-1, "参数orderId不能为空", false), HttpStatus.OK);
        }
        return new ResponseEntity(ResultVO.createSuccess(consultServiceOrderApi.selectByOrderId(orderId, organSign)), HttpStatus.OK);
    }

    @ApiOperation(value = "生成支付二维码", notes = "生成支付二维码", response = String.class, tags = {"consultServiceOrder",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = String.class)})
    @RequestMapping(value = "/getQrCode", method = RequestMethod.POST,produces =  "application/json",consumes = "application/json")
    ResponseEntity<ResultVO> getQrCode(@RequestHeader(name = "organSign", required = true) String organSign,
                                       @ApiParam(value = "支付信息", required = true) @RequestBody ConsultServicePayVo vo) {
        Long serviceItemId = vo.getServiceItemId();
        if (serviceItemId == null || serviceItemId <= 0) {
            return new ResponseEntity(new ResultVO(-1, "参数serviceItemId不能为空", false), HttpStatus.OK);
        }
        String createUser = vo.getCreateUser();
        if (StringUtils.isEmpty(createUser)) {
            return new ResponseEntity(new ResultVO(-1, "参数createUser不能为空", false), HttpStatus.OK);
        }
        Integer num = vo.getNum();
        if (num == null || num <= 0) {
            num = 1;
        }
        ConsultServiceOrderDto dto = new ConsultServiceOrderDto();
        dto.setOrganSign(organSign);
        dto.setServiceItemId(serviceItemId);
        dto.setNum(num);
        dto.setCreateUser(createUser);
        // 先创建订单
        dto = consultServiceOrderApi.create(dto);
        if (dto == null)  {
            return new ResponseEntity(new ResultVO(-1, "创建订单失败", false), HttpStatus.OK);
        }
        String orderId = dto.getOrderId();
        if (StringUtils.isEmpty(orderId)) {
            return new ResponseEntity(new ResultVO(-1, "创建订单失败", false), HttpStatus.OK);
        }
        // 生成付款二维码
        PayInfoDto payInfoDto = new PayInfoDto();
        payInfoDto.setOrderNo(orderId);
        payInfoDto.setOrganSign(organSign);
        payInfoDto.setOrderSource(1);
        payInfoDto.setTotalAmount(dto.getReceivableAmount());
        // 远程问诊业务标识
        payInfoDto.setBusinessType(2);
        GeneratorQRCodeResultDto generatorQRCodeResultDto = null;
        // 重试3次尝试获取二维码url
        for (int i = 0; i < 3; ++i) {
            generatorQRCodeResultDto = payInfoApi.insertChinaUmsC2BPayInfo(payInfoDto);
            if (generatorQRCodeResultDto != null && generatorQRCodeResultDto.getErrCode().equals("SUCCESS")) {
                break;
            }
        }
        if (generatorQRCodeResultDto == null || !generatorQRCodeResultDto.getErrCode().equals("SUCCESS")) {
            return new ResponseEntity(new ResultVO(-2, "生成支付二维码地址失败", false), HttpStatus.OK);
        }
        String qrCode = "";
        if (generatorQRCodeResultDto != null) {
            qrCode = generatorQRCodeResultDto.getBillQRCode();
        }
        Map<String, String> map = new HashMap<>();
        map.put("qrCode", qrCode);
        map.put("orderId", orderId);
        return new ResponseEntity(ResultVO.createSuccess(map), HttpStatus.OK);
    }

    @ApiOperation(value = "查询支付状态", notes = "查询支付状态", response = String.class, tags = {"consultServiceOrder",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = String.class)})
    @RequestMapping(value = "/checkPayStatus", method = RequestMethod.POST,produces =  "application/json",consumes = "application/json")
    ResponseEntity<ResultVO> checkPayStatus(@RequestHeader(name = "organSign", required = true) String organSign,
                                            @ApiParam(value = "订单号", required = true) @RequestBody ConsultServiceOrderVo vo) {
        logger.info("轮询请求支付状态" + organSign + vo);
        String orderId = vo.getOrderId();
        if (StringUtils.isEmpty(orderId)) {
            logger.info("查询支付状态失败，参数orderId能为空");
            return new ResponseEntity(new ResultVO(-1, "参数orderId不能为空", false), HttpStatus.OK);
        }
        // 优先查自己的订单表
        ConsultServiceOrderDto orderDto = consultServiceOrderApi.selectByOrderId(orderId, organSign);
        // 订单不存在
        if (orderDto == null) {
            logger.info("查询支付状态失败，订单不存在, orderId: [" + orderId + "], organSign: [" + organSign + "]");
            return new ResponseEntity(new ResultVO(-1, "订单不存在", false), HttpStatus.OK);
        }
        logger.info("订单支付状态===================" + orderDto.getPayStatus() + "支付状态 ");
        // 本地订单表状态为未支付，调取支付服务再次查询
        if (orderDto.getPayStatus().intValue() != ChinaUmsBillStatus.PAID) {
            List<PayInfoDto> list = new ArrayList<>();
            PayInfoDto payDto = new PayInfoDto();
            payDto.setOrderNo(orderId);
            payDto.setOrganSign(organSign);
            list.add(payDto);
            List<PayInfoDto> resultList = payInfoApi.getChinaUmsC2BPayStatus(list);
            if (resultList == null || resultList.size() < 1) {
                logger.info("支付接口查询订单为空, orderId: [" + orderId + "], organSign: [" + organSign + "]");
                return new ResponseEntity(ResultVO.createSuccess(false), HttpStatus.OK);
            }
            logger.info("支付服务查询结果: " + JSONObject.toJSONString(list.get(0)));
            int payStatus = resultList.get(0).getStatus();
            // 如果查询的订单状态不为未支付，更新本地订单表
            logger.info("支付状态 ======================" + payStatus + "未支付");
            if (payStatus != ChinaUmsBillStatus.UNPAID) {
                orderDto = new ConsultServiceOrderDto();
                orderDto.setOrderId(orderId);
                orderDto.setOrganSign(organSign);
                orderDto.setPayStatus((byte) payStatus);
                // 订单支付成功，更新实付金额
                if (payStatus == ChinaUmsBillStatus.PAID) {
                    BigDecimal actualAmount = resultList.get(0).getTotalAmount().divide(new BigDecimal(100));
                    orderDto.setActualAmount(actualAmount);
                    orderDto.setPayTime(resultList.get(0).getUpdateTime());
                }
                consultServiceOrderApi.updatePayStatusById(orderDto);
            }
            logger.info("支付状态 ======================" + payStatus + "已支付");
            return new ResponseEntity(ResultVO.createSuccess(resultList.get(0).getStatus() == ChinaUmsBillStatus.PAID), HttpStatus.OK);
        }
        logger.info("未支付直接返回成功-over--------------------");
        return new ResponseEntity(ResultVO.createSuccess(true), HttpStatus.OK);
    }

    @ApiOperation(value = "扫描未支付的订单", notes = "扫描未支付的订单", response = String.class, tags = {"consultServiceOrder",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = String.class)})
    @RequestMapping(value = "/scanUnpayOrder", method = {RequestMethod.POST, RequestMethod.GET}, produces = "application/json",consumes = "application/json")
    ResponseEntity<ResultVO> scanUnpayOrder() {
        ConsultServiceOrderDto query = new ConsultServiceOrderDto();
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MINUTE, -30);
        query.setCreateTime(cal.getTime());
        consultServiceOrderApi.scanUnpayOrder(query);
        return new ResponseEntity(ResultVO.createSuccess(true), HttpStatus.OK);
    }

}
