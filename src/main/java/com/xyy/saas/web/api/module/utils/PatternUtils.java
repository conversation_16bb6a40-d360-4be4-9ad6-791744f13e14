package com.xyy.saas.web.api.module.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 正则表达式工具类
 */
public class PatternUtils {


    /**
     * 全中文
     */
    public static final String CHINESE_CHARACTER = "^[\\u4e00-\\u9fa5]+$";


    /**
     * 数字和字母
     */
    public static final String NUMBER_AND_LETTER = "^[A-Za-z0-9]+$";


    /**
     * 电话号码
     */
    public static final String TELEPHONE = "^1[0-9]{10}$";
    /**
     * 全匹配
     */
    public static boolean isMatch(String regex,String patternStr){
        Pattern pattern = Pattern.compile(patternStr);
        Matcher matcher = pattern.matcher(regex);
        return matcher.matches();
    }

    public static void main(String [] args){
        System.out.println(isMatch("111aaa",NUMBER_AND_LETTER));
    }
}
