package com.xyy.saas.web.api.module.consult.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 患者Vo
 * <AUTHOR>
 */
@ApiModel(description = "患者")
public class PatientBaseVo implements Serializable {

    private static final long serialVersionUID = 7838022283294963405L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 混合查询
     */
    @ApiModelProperty(value = "混合查询", hidden = true)
    private String mixedQuery;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名", required = true)
    private String name;

    /**
     * 性别 1-男 2-女
     */
    @ApiModelProperty(value = "性别 1-男 2-女", required = true, example = "1")
    private Byte sex;

    /**
     * 生日
     */
    @ApiModelProperty(value = "生日", required = true)
    private Date birthday;

    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码", required = true)
    private String telephone;

    /**
     * 婚否 0-未婚 1-已婚
     */
    @ApiModelProperty(value = "婚否 0-未婚 1-已婚", required = true, example = "0")
    private Byte married;

    /**
     * 过敏史 0-无 1-有
     */
    @ApiModelProperty(value = "过敏史 0-无 1-有", required = true, example = "0")
    private Byte allergy;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", required = true)
    private String createUser;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号", hidden = true)
    private Integer baseVersion;

    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识", required = true)
    private String organSign;

    /**
     * 患者guid
     */
    @ApiModelProperty(value = "患者guid", hidden = true)
    private String guid;

    /**
     * 逻辑删除 1-有效 0-删除
     */
    @ApiModelProperty(value = "逻辑删除 1-有效 0-删除", hidden = true)
    private Byte yn;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMixedQuery() {
        return mixedQuery;
    }

    public void setMixedQuery(String mixedQuery) {
        this.mixedQuery = mixedQuery == null ? null : mixedQuery.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public Byte getSex() {
        return sex;
    }

    public void setSex(Byte sex) {
        this.sex = sex;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone == null ? null : telephone.trim();
    }

    public Byte getMarried() {
        return married;
    }

    public void setMarried(Byte married) {
        this.married = married;
    }

    public Byte getAllergy() {
        return allergy;
    }

    public void setAllergy(Byte allergy) {
        this.allergy = allergy;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(Integer baseVersion) {
        this.baseVersion = baseVersion;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign == null ? null : organSign.trim();
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid == null ? null : guid.trim();
    }

    public Byte getYn() {
        return yn;
    }

    public void setYn(Byte yn) {
        this.yn = yn;
    }

    @Override
    public String toString() {
        return "PatientBase{" +
                "id=" + id +
                ", mixedQuery='" + mixedQuery + '\'' +
                ", name='" + name + '\'' +
                ", sex=" + sex +
                ", birthday=" + birthday +
                ", telephone='" + telephone + '\'' +
                ", married=" + married +
                ", allergy=" + allergy +
                ", createUser='" + createUser + '\'' +
                ", createTime=" + createTime +
                ", updateUser='" + updateUser + '\'' +
                ", updateTime=" + updateTime +
                ", baseVersion=" + baseVersion +
                ", organSign='" + organSign + '\'' +
                ", guid='" + guid + '\'' +
                ", yn=" + yn +
                '}';
    }
}