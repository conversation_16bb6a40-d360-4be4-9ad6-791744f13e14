package com.xyy.saas.web.api.module.finance.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xyy.saas.product.core.dto.PageDto;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class ProPriceAdjustVo extends PageDto implements Serializable{

    private static final long serialVersionUID = 3158531339775407842L;
    private Long id;
    private Long productId;
    private String productPref; //商品编号
    private String productName;
    private String attributeSpecification; // 规格
    private Integer unitId; // 单位
    private BigDecimal retailPrice; // 零售价
    private BigDecimal vipPrice; // 会员价
    private String manufacturer; // 生产厂家
    private String approvalNumber; // 批准文号
    private String producingArea; // 产地
    private BigDecimal newPrice; // 调整后单价
    private BigDecimal newMemberPrice; // 调整后会员价格
    private Byte status; // 当前状态
    private String unitName; // 单位名称

    // 单据编号
    private String pref;
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date createTime; // 生效时间
    private String remark;
    private String createUser; // 申请人


    private String updateTimeStart;//查询开始时间，格式是1999-08-28

    private String updateTimeEnd;//查询截止-时间，格式是1999-08-28
    private String commonName;
    private String pharmacyPref;//药店商品编号

    private String updateUser; //操作人（更新人）
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date updateTime;//更新时间
    private String organSign;//机构号

    private String barCode; //新增返回条码字段


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getProductPref() {
        return productPref;
    }

    public void setProductPref(String productPref) {
        this.productPref = productPref;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getAttributeSpecification() {
        return attributeSpecification;
    }

    public void setAttributeSpecification(String attributeSpecification) {
        this.attributeSpecification = attributeSpecification;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public BigDecimal getRetailPrice() {
        return retailPrice;
    }

    public void setRetailPrice(BigDecimal retailPrice) {
        this.retailPrice = retailPrice;
    }

    public BigDecimal getVipPrice() {
        return vipPrice;
    }

    public void setVipPrice(BigDecimal vipPrice) {
        this.vipPrice = vipPrice;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    public String getProducingArea() {
        return producingArea;
    }

    public void setProducingArea(String producingArea) {
        this.producingArea = producingArea;
    }

    public BigDecimal getNewPrice() {
        return newPrice;
    }

    public void setNewPrice(BigDecimal newPrice) {
        this.newPrice = newPrice;
    }

    public BigDecimal getNewMemberPrice() {
        return newMemberPrice;
    }

    public void setNewMemberPrice(BigDecimal newMemberPrice) {
        this.newMemberPrice = newMemberPrice;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    public String getPharmacyPref() {
        return pharmacyPref;
    }

    public void setPharmacyPref(String pharmacyPref) {
        this.pharmacyPref = pharmacyPref;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public String getUpdateTimeStart() {
        return updateTimeStart;
    }

    public void setUpdateTimeStart(String updateTimeStart) {
        this.updateTimeStart = updateTimeStart;
    }

    public String getUpdateTimeEnd() {
        return updateTimeEnd;
    }

    public void setUpdateTimeEnd(String updateTimeEnd) {
        this.updateTimeEnd = updateTimeEnd;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }
}
