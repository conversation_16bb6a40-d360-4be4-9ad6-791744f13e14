/**
 * NOTE: This class is auto generated by the swagger code generator program (2.2.3).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.web.api.module.product.model.ProductScatteredRulePo;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T15:07:38.324+08:00")
@RequestMapping("/product")
@Api(value = "proScatteredrule", description = "the proScatteredrule API")
public interface ProScatteredruleApi {

    @ApiOperation(value = "添加商品拆零规则信息", notes = "添加商品拆零规则信息", response = ProductScatteredRulePo.class, tags={ "scatteredrule", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "操作成功", response = ProductScatteredRulePo.class) })
    
    @RequestMapping(value = "/proScatteredrule/addRule",
        method = RequestMethod.POST)
    ResponseEntity<ResultVO> addRule(HttpServletRequest request, @ApiParam(value = "商品拆零规则信息", required = true) @Valid @RequestBody ProductScatteredRulePo productScatteredRulePo);


    @ApiOperation(value = "修改商品拆零规则信息", notes = "修改商品拆零规则信息", response = ProductScatteredRulePo.class, tags={ "scatteredrule", })
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "操作成功", response = ProductScatteredRulePo.class) })

    @RequestMapping(value = "/proScatteredrule/updateRule/",
        method = RequestMethod.POST)
    ResponseEntity<ResultVO> updateRule(HttpServletRequest request, @ApiParam(value = "商品拆零规则信息", required = true) @Valid @RequestBody ProductScatteredRulePo productScatteredRulePo);

    @ApiOperation(value = "根据商品拆零规则id查询该规则信息", notes = "根据商品拆零规则id查询该规则信息", response = ProductScatteredRulePo.class, tags={ "scatteredrule", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = ProductScatteredRulePo.class) })

    @RequestMapping(value = "/proScatteredrule/toEdit/{id}",
            method = RequestMethod.GET)
    ResponseEntity<ResultVO> getProScatteredById(HttpServletRequest request, @ApiParam(value = "商品拆零规则信息", required = true) @Valid @RequestBody Long id);

    @ApiOperation(value = "商品拆零规则信息列表", notes = "商品拆零规则信息列表", response = ProductScatteredRulePo.class, tags={ "scatteredrule", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = ProductScatteredRulePo.class) })

    @RequestMapping(value = "/proScatteredrule/toList/",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> proRuleList(HttpServletRequest request, @ApiParam(value = "商品拆零规则信息", required = true) @Valid @RequestBody ProductScatteredRulePo productScatteredRulePo);

}
