package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.web.api.module.product.model.InventoryPlanDetailVo;
import com.xyy.saas.web.api.module.product.model.InventoryPlanDetailVoList;
import io.swagger.annotations.ApiParam;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-29T18:35:09.839+08:00")

@Controller
public class FindPlanDetailVosApiController implements FindPlanDetailVosApi {



    public ResponseEntity<InventoryPlanDetailVoList> findPlanDetailVos(@ApiParam(value = "盘点单明细Vo" ,required=true )  @Valid @RequestBody InventoryPlanDetailVo inventoryPlanDetailVo) {
        // do some magic!
        return new ResponseEntity<InventoryPlanDetailVoList>(HttpStatus.OK);
    }

}
