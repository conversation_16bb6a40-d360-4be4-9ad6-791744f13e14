package com.xyy.saas.web.api.module.supplier.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(description = "审核")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-07T16:41:52.348+08:00")
public class SupplierUpdatemsgAudit implements Serializable {
    @ApiModelProperty(value = "guid")
    private String guid;

    @ApiModelProperty(value = "审核状态 3:拒绝 2:同意")
    private Integer auditState;

    @ApiModelProperty(value = "备注")
    private String auditRemark;

    @ApiModelProperty(value = "审核人id")
    private Integer auditUserId;

    @ApiModelProperty(value = "审核人姓名")
    private String auditUser;

    @ApiModelProperty(value = "审核时间")
    private String auditTime;


    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public Integer getAuditState() {
        return auditState;
    }

    public void setAuditState(Integer auditState) {
        this.auditState = auditState;
    }

    public String getAuditUser() {
        return auditUser;
    }

    public void setAuditUser(String auditUser) {
        this.auditUser = auditUser;
    }

    public String getAuditRemark() {
        return auditRemark;
    }

    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark;
    }

    public Integer getAuditUserId() {
        return auditUserId;
    }

    public void setAuditUserId(Integer auditUserId) {
        this.auditUserId = auditUserId;
    }

    public String getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(String auditTime) {
        this.auditTime = auditTime;
    }
}
