package com.xyy.saas.web.api.module.pay.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/5/7 19:25
 */
@ApiModel(description = "营业执照信息")
@Data
public class LicenseVo {
    @ApiModelProperty(value = "执照图片")
    private String licenseImg;
    @ApiModelProperty(value = "执照类型 （营业执照 ： 01）")
    private String licenseType;
    @ApiModelProperty(value = "执照名称")
    private String licenseName;
    @ApiModelProperty(value = "执照编号")
    private String licenseNo;
    @ApiModelProperty(value = "执照期限类型（短期 0   长期 1）")
    private String licenseEndType;
    @ApiModelProperty(value = "执照签发日期 yyyy-MM-dd")
    private String licenseStartDate;
    @ApiModelProperty(value = "执照到期日期 yyyy-MM-dd")
    private String licenseEndDate;

}
