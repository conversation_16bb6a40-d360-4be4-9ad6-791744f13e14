package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 会员储值卡流水查询Vo
 * <AUTHOR>
 */
@ApiModel(description = "会员储值卡流水查询")
public class MemberPrepayCardHistoryDetailVo implements Serializable {

    private static final long serialVersionUID = 2205157980866402010L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", example = "1001")
    private Long id;

    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识", example = "ZHL00001411")
    private String organsign;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrgansign() {
        return organsign;
    }

    public void setOrgansign(String organsign) {
        this.organsign = organsign;
    }

    @Override
    public String toString() {
        return "MemberPrepayCardHistoryDetailVo{" +
                "id=" + id +
                ", organsign='" + organsign + '\'' +
                '}';
    }
}