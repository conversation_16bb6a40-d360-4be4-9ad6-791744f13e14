package com.xyy.saas.web.api.module.product.model;

import io.swagger.annotations.ApiModel;

import java.util.Date;
import java.util.List;
import java.util.Map;

@ApiModel("促销计划")
public class PromotionPlanVo {

	private Long id;
	private String pref;//促销编号
	private String promotionName;//促销方案名称
	private Date startDate;//生效日期
	private Date endDate;//失效日期
	private Byte targetUser;//适用对象，1--所有用户，2--会员用户，3--普通用户
	private String vipLevels;//会员日生效会员级别
	private Byte promotionType;//促销类型：1--满折，2--满送，3--会员日，4--限量特价
	private Byte promotionConditionType;//促销触发类型：1--满件折，2--满额折
	private Byte accumulationYn;//是否累加，0--否，1--是
	private Byte targetProductScope;//适用商品范围：1--全部商品，2--部分商品，3--仅设置不参与促销商品
	private String promotionWeekDay;//促销日约束，周一到周日的一个或者多个以逗号分隔组成的数字串
	private String promotionDay;//指定具体促销日，1-31之间的一个或者多个以逗号分隔组成的数字串
	private Byte status;//是否启用：0--否，1--是
	private String createUser;//创建人
	private Date createTime;//创建时间
	private String updateUser;//更新人
	private Date updateTime;//更新时间
	private Byte yn;//逻辑删除 1 有效 0 删除
	private String baseVersion;//操作版本号
	private String organSign;//药店唯一标识
	/**
	 * 商品id串，用逗号分隔
	 */
	private String productIds;

	/**
	 * vip等级ID串，用逗号分隔
	 */
	private String vipLevelIds;

	/**
	 * 满多少件打几折，满多少钱打几折,满多少件送多少件 ,前台传递参数类似于---2:50;3:80
	 */
	private String discountStr;

	/**
	 * 会员级别，促销折扣，积分倍率，传递参数类似于---1:80:1.8;2:90:1.5
	 */
	private String vipScoreStr;

	/**
	 * 赠送商品信息，类似于13131:0.01;123131:0.01
	 */
	private String giftIdStr;

	/**
	 * 限特价商品相关信息，类似于-----12121:28.00:100:5;221212:25.00:300:6
	 */
	private String limitedSaleStr;

	private List<PromotionPlanDiscountDetailVo> promotionPlanDiscountDetailPoList;

	private List<Map<String,Object>> promotionPlanGoodsPoList;
	private List<Map<String,Object>> promotionPlanDetailPoList;
	private List<Map<String,Object>> discountProductList;
	private List<Map<String,Object>> vipDayConfigInfoList;


	private String startDateStr;
	private String endDateStr;

	/**
	 * 1--促销日约束，2--指定具体日
	 */
	private Integer restraintType;
}
