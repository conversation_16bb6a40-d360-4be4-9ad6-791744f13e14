package com.xyy.saas.web.api.module.member.utils;

/**
 * 返回结果状态枚举类
 * <AUTHOR>
 */
public enum ResultCodeEnum {
    SUCCESS(0,"success"),
    ERROR(1,"error"),
    NO_LEVEL_NAME(18004, "参数name不能为空"),
    NO_UP_LEVEL_ID(18005, "参数upLevelId不能为空"),
    NO_STATUS(18006, "参数status不能为空"),
    NO_UP_POINT(18007, "参数upPoint不能为空"),
    NO_DISCOUNT(18008, "参数discount不能为空"),
    NOPRICE_STRATEGY(18009, "参数priceStrategy不能为空"),
    NO_YN(18010, "参数yn不能为空"),
    NO_CART_NO(18011, "参数cartNo不能为空"),
    NO_SEX(18012, "参数sex不能为空"),
    NO_VIP_LEVEL_ID(18013, "参数vipLevelId不能为空"),
    NO_TELLPHNOE(18014, "参数tellphone不能为空"),
    NO_NAME(18015, "参数name不能为空"),
    NO_EXCHANGE_TYPE(18016,"参数exchangeType不能为空"),
    NO_MEMBER_LEVEL_ID(18017,"参数memberLevelId不能为空"),
    NO_CONSUME_PRICE(18018,"参数consumePrice不能为空"),
    NO_POINT(18019,"参数point不能为空"),
    NO_EXCHANGE_PRICE(18020,"参数exchange_price不能为空"),
    EXISTS_CARTNO(18021,"会员卡号已注册，请重新输入会员卡号！"),
    EXISTS_TELEPHONE(18022,"电话号码已注册，请重新输入电话号码！"),
    NO_EDITTYPE(18023,"修改类型不能为空"),
    NO_MEMBERID(18024,"会员id不能为空"),
    NO_OPERATOR_POINT(18025,"操作积分不能为空"),
    ALREADY_DELETE(18025,"保存失败，未找到当前会员信息！"),
    NO_CLEARPOINT_TYPE(18026, "积分清零类型不能为空"),
    NO_EXCHANGE_PRODUCT(18027, "积分兑换商品编号不能为空"),
    NO_NEED_POINT(18028, "参数needPoint不能为空"),
    NO_MEDICAL_NO(18029,"档案编号不能为空！"),
    EXISTS_MEDICAL_NO(18030,"档案编号重复！");






    private int code;
    private String msg;
    ResultCodeEnum(int code, String msg){
        this.code=code;
        this.msg=msg;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}