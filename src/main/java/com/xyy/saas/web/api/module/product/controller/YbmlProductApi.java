/**
 * NOTE: This class is auto generated by the swagger code generator program (2.2.3).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.national.core.dto.SaasNationalMenuSummaryQueryDto;
import com.xyy.saas.web.api.module.product.model.*;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T18:13:39.358+08:00")
@RequestMapping("/product/ybml")
@Api(value = "YbmlProduct", description = "药店商品与国家地区医保目录关系API接口")
public interface YbmlProductApi {

    @ApiOperation(value = "一键清除接口", notes = "一键清除接口", response = ResultVO.class, tags={ "药店商品与国家地区医保目录关系API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/match/clear")
    ResponseEntity<ResultVO> matchClear(HttpServletRequest request, @RequestBody YbmlUpdateVo updateVo);

    @ApiOperation(value = "一键同步POS对码接口", notes = "一键同步POS对码接口", response = ResultVO.class, tags={ "药店商品与国家地区医保目录关系API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/match/syncFromMedicare")
    ResponseEntity<ResultVO> matchSyncMedicare(HttpServletRequest request, @RequestBody YbmlUpdateVo updateVo);

    @ApiOperation(value = "一键匹配接口", notes = "一键匹配接口", response = ResultVO.class, tags={ "药店商品与国家地区医保目录关系API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/productYbmlAutoMatch/submit")
    ResponseEntity<ResultVO> productYbmlMatchSubmit(HttpServletRequest request);


    @ApiOperation(value = "一键匹配取消接口", notes = "一键匹配取消接口", response = ResultVO.class, tags={ "药店商品与国家地区医保目录关系API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/productYbmlAutoMatch/cancel")
    ResponseEntity<ResultVO> productYbmlMatchCancel(HttpServletRequest request, @ApiParam(value = "一键匹配取消参数", required = true) @RequestBody YbmlQuickMatchParamVo ybmlQuickMatchParamVo);


    @ApiOperation(value = "一键匹配结果查询接口", notes = "一键匹配结果接口", response = ResultVO.class, tags={ "药店商品与国家地区医保目录关系API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/productYbmlAutoMatch/resultQuery")
    ResponseEntity<ResultVO> productYbmlMatchResultQuery(HttpServletRequest request, @ApiParam(value = "一键匹配结果查询参数", required = true) @RequestBody YbmlQuickMatchParamVo ybmlQuickMatchParamVo);


    @ApiOperation(value = "医保目录列表查询接口", notes = "医保目录列表查询接口", response = ResultVO.class, tags={ "药店商品与国家地区医保目录关系API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/ybmlList")
    ResponseEntity<ResultVO> ybmlList(HttpServletRequest request, @ApiParam(value = "医保目录列表查询参数", required = true) @RequestBody YbmlListQueryVo ybmlListQueryVo);


    @ApiOperation(value = "手动匹配药店商品与医保目录接口", notes = "手动匹配药店商品与医保目录接口", response = ResultVO.class, tags={ "药店商品与国家地区医保目录关系API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/productYbmlManualMatch")
    ResponseEntity<ResultVO> productYbmlManualMatch(HttpServletRequest request, @ApiParam(value = "医保目录列表查询参数", required = true) @RequestBody YbmlManualMatchParamVo ybmlManualMatchParamVo);


    @ApiOperation(value = "列表界面查询统计数据", notes = "列表界面查询统计数据", response = ResultVO.class, tags={ "药店商品与国家地区医保目录关系API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/querySummary")
    ResponseEntity<ResultVO> querySummary(HttpServletRequest request, @ApiParam(value = "列表界面查询统计数据参数", required = true) @RequestBody SaasNationalMenuSummaryQueryDto summaryQueryDto);

    @ApiOperation(value = "国家药品目录列表查询接口", notes = "国家药品目录列表查询接口", response = ResultVO.class, tags={ "国家药品目录列表查询接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/queryList")
    ResponseEntity<ResultVO> queryList(HttpServletRequest request, @ApiParam(value = "医保目录列表查询参数", required = true) @RequestBody YbnlQueryListVo ybnlQueryListVo);

    @ApiOperation(value = "国家药品目录导入任务id查询接口", notes = "国家药品目录导入任务id查询接口", response = ResultVO.class, tags={ "国家药品目录导入任务id查询接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/queryTaskId")
    ResponseEntity<ResultVO> queryTaskId(HttpServletRequest request, @ApiParam(value = "国家药品目录导入任务id查询参数", required = true) @RequestBody String param);


    @ApiOperation(value = "更新商品信息接口", notes = "更新商品信息接口", response = ResultVO.class, tags={ "更新商品信息接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/updateProduct")
    ResponseEntity<ResultVO> updateProduct(HttpServletRequest request);


    @ApiOperation(value = "批量修改商品是否为医保", notes = "批量修改商品是否为医保", response = ResultVO.class, tags={ "批量修改商品是否为医保", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/batchSetYbType")
    ResponseEntity<ResultVO> batchSetYbType(HttpServletRequest request, @ApiParam(value = "批量修改商品是否为医保参数", required = true) @RequestBody YbmlUpdateVo updateVo);

    @ApiOperation(value = "批量清除匹配关系", notes = "批量清除匹配关系", response = ResultVO.class, tags={ "批量清除匹配关系", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/deleteMatch")
    ResponseEntity<ResultVO> deleteMatch(HttpServletRequest request, @ApiParam(value = "批量清除匹配关系参数", required = true) @RequestBody YbmlUpdateVo updateVo);
}
