package com.xyy.saas.web.api.module.member.service;

import com.xyy.saas.member.core.dto.MemberConvertV2Dto;

import java.util.Map;

/**
 * 积分兑换流水记录服务
 * 从会员模块上移到网关层，为了使会员不依赖库存
 * 此方案可能也只是临时方案
 * <AUTHOR>
 * @since 2020-05-30
 */
public interface MemberExchangeInfoService {

    /**
     * pos端 会员积分兑换
     * @param convertDto
     * @return Map
     */
    Map<String, Object> saveMemberExchangeInfo(MemberConvertV2Dto convertDto);
}
