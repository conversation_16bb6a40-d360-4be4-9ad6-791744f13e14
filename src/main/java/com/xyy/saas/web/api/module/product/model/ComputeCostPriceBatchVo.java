package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName ComputeCostPriceBatchVo
 * @Description 后端计算器批量信息封装对象
 * <AUTHOR>
 * @Date 2020/8/19 17:22
 * @Version 1.0
 **/
@ApiModel(description = "后端计算器批量信息封装对象")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")
public class ComputeCostPriceBatchVo {

    @JsonProperty("adjustmentRatio")
    private String adjustmentRatio;//调整比例

    @JsonProperty("details")
    private List<ComputeCostPriceBatchDetailVo> details;//批量成本价信息对象

    @ApiModelProperty(value = "批量成本价信息对象")
    public List<ComputeCostPriceBatchDetailVo> getDetails() {
        return details;
    }

    public void setDetails(List<ComputeCostPriceBatchDetailVo> details) {
        this.details = details;
    }

    @ApiModelProperty(value = "调整比例")
    public String getAdjustmentRatio() {
        return adjustmentRatio;
    }

    public void setAdjustmentRatio(String adjustmentRatio) {
        this.adjustmentRatio = adjustmentRatio;
    }
}
