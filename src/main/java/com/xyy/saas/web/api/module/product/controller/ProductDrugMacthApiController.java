package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.api.ProductDrugStandardApi;
import com.xyy.saas.product.core.dto.AutoMatchDto;
import com.xyy.saas.product.core.dto.DrugStandardDto;
import com.xyy.saas.product.core.dto.PageDto;
import com.xyy.saas.product.core.dto.ProductDrugStandardDto;
import com.xyy.saas.product.core.dto.ProductJobDto;
import com.xyy.saas.product.core.dto.ProductMatchConfirmDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@Controller
public class ProductDrugMacthApiController implements ProductDrugMacthApi {

    private static final Logger logger = LoggerFactory.getLogger(ProductDrugMacthApiController.class);

    @Reference(version = "0.0.1")
    private ProductDrugStandardApi standardApi;

    @Override
    public ResponseEntity<ResultVO> list(HttpServletRequest request, @Valid @RequestBody ProductDrugStandardDto dto) {
        if(null == dto){
            logger.info("查询参数为空！");
            return new ResponseEntity(new ResultVO(ResultCodeEnum.ERROR_INVALID_PARAM),HttpStatus.OK);
        }
        String organSign = request.getHeader("organSign");
        dto.setOrganSign(organSign);
        PageInfo pageInfo = new PageInfo();
        Integer rows = dto.getRows();
        Integer page = dto.getPage();
        if(null == page || page == 0){
            page = 1;
        }
        if(null == rows || rows == 0){
            rows = 50;
        }
        pageInfo.setPageNum(page);
        pageInfo.setPageSize(rows);
        return new ResponseEntity(standardApi.queryProductDrugStandard(pageInfo,dto),HttpStatus.OK);
    }

    /**
     * 通过名称和批准文号匹配
     * @param request
     * @param dto
     * @return
     */
    @Override
    public ResponseEntity<ResultVO> matchDrugStandard(HttpServletRequest request, @Valid @RequestBody DrugStandardDto dto){
//        dto.setOrganSign(request.getHeader("organSign"));
        PageInfo pageInfo = new PageInfo();
        Integer rows = dto.getRows();
        Integer page = dto.getPage();
        if(null == page || page == 0){
            page = 1;
        }
        if(null == rows || rows == 0){
            rows = 50;
        }
        pageInfo.setPageNum(page);
        pageInfo.setPageSize(rows);
        return new ResponseEntity(ResultVO.createSuccess(standardApi.queryDrugStandard(pageInfo,dto)),HttpStatus.OK);
    }

    /**
     * 匹配商品
     * @param request
     * @param dto
     * @return
     */
    @Override
    public ResponseEntity<ResultVO> matchProduct(HttpServletRequest request, @Valid @RequestBody ProductDrugStandardDto dto) {
        dto.setOrganSign(request.getHeader("organSign"));
        String employeeId = request.getHeader("employeeId");
        return new ResponseEntity(standardApi.matchProduct(dto,employeeId),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> autoMatchProduct(HttpServletRequest request, @Valid @RequestBody AutoMatchDto dto) {
        dto.setOrganSign(request.getHeader("organSign"));
        dto.setCreateUser(request.getHeader("employeeId"));
        return new ResponseEntity(standardApi.autoMatchProducts(dto),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> jobQuery(HttpServletRequest request, @Valid @RequestBody ProductJobDto dto) {
        return new ResponseEntity(standardApi.queryJobStatus(dto), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> queryUnConfirmedProducts(@RequestHeader("commonRequestModel") String commonRequestModelStr, @Valid @RequestBody PageDto pageDto) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String organSign = commonRequestModel.getOrganSign();
        PageInfo pageInfo = new PageInfo();
        Integer rows = pageDto.getRows();
        Integer page = pageDto.getPage();
        if(null == page || page == 0){
            page = 1;
        }
        if(null == rows || rows == 0){
            rows = 50;
        }
        pageInfo.setPageNum(page);
        pageInfo.setPageSize(rows);
        return new ResponseEntity(standardApi.queryUnConfirmedProducts(pageInfo, organSign),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> confirmAllProducts(@RequestHeader("commonRequestModel") String commonRequestModelStr, @Valid @RequestBody ProductMatchConfirmDto dto) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        dto.setOrganSign(commonRequestModel.getOrganSign());
        dto.setUpdateUser(commonRequestModel.getEmployeeId());
        return new ResponseEntity(standardApi.confirmAllProducts(dto),HttpStatus.OK);
    }

}
