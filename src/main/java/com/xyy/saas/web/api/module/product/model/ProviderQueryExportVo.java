package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * @ClassName ProviderQueryExportVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/9/25 16:25
 **/
public class ProviderQueryExportVo  extends  ProviderQueryVo{
    @JsonProperty("colName")
    private String[] colName;//导出列的字段名称
    @JsonProperty("colNameDesc")
    private String[] colNameDesc;//导出列的描述
    @JsonProperty("excelName")
    private String excelName;//文件名称

    public String[] getColName() {
        return colName;
    }

    public void setColName(String[] colName) {
        this.colName = colName;
    }

    public String[] getColNameDesc() {
        return colNameDesc;
    }

    public void setColNameDesc(String[] colNameDesc) {
        this.colNameDesc = colNameDesc;
    }

    public String getExcelName() {
        return excelName;
    }

    public void setExcelName(String excelName) {
        this.excelName = excelName;
    }
}
