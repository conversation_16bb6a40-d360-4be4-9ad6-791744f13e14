package com.xyy.saas.web.api.module.external.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.dto.ProductDto;
import com.xyy.saas.web.api.module.external.model.ProductExternal;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * @Classname ProductExternalApiSwag
 * @Description 商品对外提供API
 * @Date 2020/5/14 9:57
 * <AUTHOR>
 */

@RequestMapping("/product/external")
@Api(value = "商品对外提供API", description = "商品对外提供API")
public interface ProductExternalApiSwag {

    @ApiOperation(value = "更新商品信息", notes = "商品对外提供API", response = Integer.class, tags={ "商品对外提供API", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = Integer.class) })
    @RequestMapping(value = "/updateProduct", method = RequestMethod.POST)
    ResponseEntity<Integer> updateProduct(@ApiParam(value = "商品信息", required = true) @Valid @NotNull @RequestBody ProductExternal productExternal);

    @ApiOperation(value = "查询标准库商品信息", notes = "商品对外提供API", response = ResultVO.class, tags={ "商品对外提供API", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/findStandLibrarysByOrganSign", method = RequestMethod.POST)
    ResponseEntity<ResultVO> findStandLibrarysByOrganSign(@ApiParam(value = "商品信息", required = true) @Valid @NotNull @RequestBody ProductDto productDto);
}
