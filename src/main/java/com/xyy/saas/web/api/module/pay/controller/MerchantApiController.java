package com.xyy.saas.web.api.module.pay.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.pay.core.api.MerchantApi;
import com.xyy.saas.pay.core.dto.*;
import com.xyy.saas.web.api.module.pay.model.*;
import com.xyy.saas.web.api.module.utils.HttpClientUtil;
import com.xyy.saas.web.api.module.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.http.client.config.RequestConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * <AUTHOR>
 * @date 2021/5/7 19:08
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2021-05-07T11:16:06.377+08:00")

@Controller
@RequestMapping("/pay/merchant")
@Api(value = "merchant", description = "pay merchant API")
public class MerchantApiController {
    private static final Logger logger = LoggerFactory.getLogger(MerchantApiController.class);

    @Reference(version = "0.0.1")
    private MerchantApi merchantApi;

    @Value("${pay.isShowJobNumberWindow}")
    private String IS_SHOW_JOBNUMBER_WINDOW;

    @Value("${sso.apiUrl}")
    private String SSO_API_URL;

    @Value("${sso.getUserByStaffNumOrAccount}")
    private String SSO_GET_USER_BY_STAFFNUM_OR_ACCOUNT;

    @Value("${merchantVerify.defaultJobNum}")
    private String MERCHANTVERIFY_DEFAULTJOBNUM;

    @ApiOperation(value = "聚合支付门店资料提交", notes = "聚合支付门店资料提交", tags={ "merchant", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = JSONObject.class) })
    @RequestMapping(value = "/merchantCreate", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    public ResponseEntity<ResultVO> merchantCreate(@RequestHeader(name = "organSign", required = true) String organSign,
                                                             @RequestBody MerchantCreateVo templateVo){
        logger.info("聚合支付门店资料提交+" + JSONObject.toJSONString(templateVo));
        ResultVO resultVO = checkMerchantCreateVoProp(templateVo);
        if(resultVO.getCode() != ResultCodeEnum.SUCCESS.getCode()) {
            return new ResponseEntity(resultVO, HttpStatus.OK);
        }
        MerchantCreateDto merchantCreateDto = new MerchantCreateDto();
        if(templateVo.getBeneficiaryInfoVo() != null) {
            BeneficiaryInfoDto beneficiaryInfoDto = new BeneficiaryInfoDto();
            BeanUtils.copyProperties(templateVo.getBeneficiaryInfoVo(), beneficiaryInfoDto);
            merchantCreateDto.setBeneficiaryInfoDto(beneficiaryInfoDto);
        }
        if(templateVo.getBusinessInfoVo() != null) {
            BusinessInfoDto businessInfoDto = new BusinessInfoDto();
            BeanUtils.copyProperties(templateVo.getBusinessInfoVo(), businessInfoDto);
            merchantCreateDto.setBusinessInfoDto(businessInfoDto);
        }
        if(templateVo.getLicenseVo() != null) {
            LicenseDto licenseDto = new LicenseDto();
            BeanUtils.copyProperties(templateVo.getLicenseVo(), licenseDto);
            merchantCreateDto.setLicenseDto(licenseDto);
        }
        if(templateVo.getSettleInfoVo() != null) {
            SettleInfoDto settleInfoDto = new SettleInfoDto();
            BeanUtils.copyProperties(templateVo.getSettleInfoVo(), settleInfoDto);
            merchantCreateDto.setSettleInfoDto(settleInfoDto);
        }
        if(templateVo.getStoreInfoVo() != null) {
            StoreInfoDto storeInfoDto = new StoreInfoDto();
            BeanUtils.copyProperties(templateVo.getStoreInfoVo(), storeInfoDto);
            merchantCreateDto.setStoreInfoDto(storeInfoDto);
        }
        BeanUtils.copyProperties(templateVo, merchantCreateDto);
        merchantApi.merchantCreate(organSign, merchantCreateDto);
        return new ResponseEntity(ResultVO.createSuccess(), HttpStatus.OK);
    }


    @ApiOperation(value = "聚合支付门店提交审核", notes = "聚合支付门店提交审核",  tags={ "merchant", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = JSONObject.class) })
    @RequestMapping(value = "/merchantVerify", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    public ResponseEntity<ResultVO> merchantVerify(@RequestHeader(name = "organSign", required = true) String organSign,
                                                   @RequestBody MerchantVerifyVo templateVo){
        String jobNumber = templateVo.getJobNumber();
        //如果提交审核时需要弹窗填写工号并且不为默认工号，则需要校验工号
        if (IS_SHOW_JOBNUMBER_WINDOW.equals("1") && !MERCHANTVERIFY_DEFAULTJOBNUM.equals(jobNumber)){
            if (StringUtils.isEmpty(jobNumber)){
                return new ResponseEntity(ResultVO.createError("工号未填写，请检查"), HttpStatus.OK);
            }
            try {
                if (!checkStaffByJobNum(jobNumber)) {
                    return new ResponseEntity(ResultVO.createError("工号填写不正确"), HttpStatus.OK);
                }
            } catch (Exception e) {
                logger.error("/pay/merchant/merchantVerify checkStaffByJobNum查询用户中台接口异常",e);
                return new ResponseEntity(ResultVO.createError("校验工号异常"), HttpStatus.OK);
            }

        }
        MerchantVerifyDto merchantVerifyDto = new MerchantVerifyDto();
        merchantVerifyDto.setOrganSign(organSign);
        merchantVerifyDto.setJobNumber(jobNumber==null?"":jobNumber);
        if(merchantApi.merchantVerify(merchantVerifyDto)) {
            return new ResponseEntity(ResultVO.createSuccess(), HttpStatus.OK);
        }
        return new ResponseEntity(ResultVO.createError("信息填写未完成，请检查"), HttpStatus.OK);
    }


    @ApiOperation(value = "聚合支付门店信息查询", notes = "聚合支付门店信息查询",tags={ "merchant", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MerchantCreateVo.class) })
    @RequestMapping(value = "/merchantCertQuery", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    public ResponseEntity<ResultVO> merchantCertQuery(@RequestHeader(name = "organSign", required = true) String organSign){
        MerchantCreateDto merchantCreateDto = merchantApi.merchantCertQuery(organSign);
        MerchantCreateVo merchantCreateVo = new MerchantCreateVo();
        if(merchantCreateDto.getBeneficiaryInfoDto() != null) {
            BeneficiaryInfoVo beneficiaryInfoVo = new BeneficiaryInfoVo();
            BeanUtils.copyProperties(merchantCreateDto.getBeneficiaryInfoDto(), beneficiaryInfoVo);
            merchantCreateVo.setBeneficiaryInfoVo(beneficiaryInfoVo);
        }
        if(merchantCreateDto.getBusinessInfoDto() != null) {
            BusinessInfoVo businessInfoVo = new BusinessInfoVo();
            BeanUtils.copyProperties(merchantCreateDto.getBusinessInfoDto(), businessInfoVo);
            merchantCreateVo.setBusinessInfoVo(businessInfoVo);
        }
        if(merchantCreateDto.getLicenseDto() != null) {
            LicenseVo licenseVo = new LicenseVo();
            BeanUtils.copyProperties(merchantCreateDto.getLicenseDto(), licenseVo);
            merchantCreateVo.setLicenseVo(licenseVo);
        }
        if(merchantCreateDto.getSettleInfoDto() != null) {
            SettleInfoVo settleInfoVo = new SettleInfoVo();
            BeanUtils.copyProperties(merchantCreateDto.getSettleInfoDto(), settleInfoVo);
            merchantCreateVo.setSettleInfoVo(settleInfoVo);
        }
        if(merchantCreateDto.getStoreInfoDto() != null) {
            StoreInfoVo storeInfoVo = new StoreInfoVo();
            BeanUtils.copyProperties(merchantCreateDto.getStoreInfoDto(), storeInfoVo);
            merchantCreateVo.setStoreInfoVo(storeInfoVo);
        }
        BeanUtils.copyProperties(merchantCreateDto, merchantCreateVo);
        return new ResponseEntity(ResultVO.createSuccess(merchantCreateVo), HttpStatus.OK);
    }


    @ApiOperation(value = "聚合支付门店状态查询", notes = "聚合支付门店状态查询", tags={ "merchant", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MerchantStatusVo.class) })
    @RequestMapping(value = "/merchantStatusQuery", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    public ResponseEntity<ResultVO> merchantStatusQuery(@RequestHeader(name = "organSign", required = true) String organSign){
        MerchantStatusDto merchantStatusDto = merchantApi.merchantStatusQuery(organSign);
        MerchantStatusVo merchantStatusVo = new MerchantStatusVo();
        BeanUtils.copyProperties(merchantStatusDto, merchantStatusVo);
        return new ResponseEntity(ResultVO.createSuccess(merchantStatusVo), HttpStatus.OK);
    }


    @ApiOperation(value = "行业查询", notes = "行业查询", tags={ "merchant", })
    @ApiResponses(value = {@ApiResponse(code = 0, message = "操作成功", response = JSONObject.class) })
    @RequestMapping(value = "/mcc", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    public ResponseEntity<ResultVO> mccQuery(@RequestBody MccCodeQueryVo queryVo){
        MccCodeQueryDto codeQueryDto = new MccCodeQueryDto();
        codeQueryDto.setParentCode(queryVo.getParentCode());
        codeQueryDto.setBasicCode(queryVo.getBasicCode());
        return new ResponseEntity(ResultVO.createSuccess(merchantApi.mccQuery(codeQueryDto)), HttpStatus.OK);
    }


    @ApiOperation(value = "地区查询", notes = "地区查询", tags={ "merchant", })
    @ApiResponses(value = {@ApiResponse(code = 0, message = "操作成功", response = JSONObject.class) })
    @RequestMapping(value = "/area", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    public ResponseEntity<ResultVO> areaQuery(@RequestBody CodeQueryVo queryVo){
        AreaCodeQueryDto codeQueryDto = new AreaCodeQueryDto();
        codeQueryDto.setParentCode(queryVo.getParentCode());
        return new ResponseEntity(ResultVO.createSuccess(merchantApi.areaQuery(codeQueryDto)), HttpStatus.OK);
    }

    @ApiOperation(value = "银行查询", notes = "银行查询", tags={ "merchant", })
    @ApiResponses(value = {@ApiResponse(code = 0, message = "操作成功", response = JSONObject.class) })
    @RequestMapping(value = "/bank", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    public ResponseEntity<ResultVO> bankQuery(@RequestBody QueryBankListVo queryVo) {
        QueryBankListDto queryBankListDto = new QueryBankListDto();
         BeanUtils.copyProperties(queryVo, queryBankListDto);
        return new ResponseEntity(ResultVO.createSuccess(merchantApi.queryBankList(queryBankListDto)), HttpStatus.OK);
    }

    @ApiOperation(value = "是否弹出填写工号窗口", notes = "是否弹出填写工号窗口", tags={ "merchant", })
    @ApiResponses(value = {@ApiResponse(code = 0, message = "操作成功", response = String.class) })
    @RequestMapping(value = "/isShowJobNumberWindow", method = {RequestMethod.POST, RequestMethod.GET},produces =  "application/json", consumes = "application/json")
    public ResponseEntity<ResultVO> isShowJobNumberWindow() {
        Map<String,String> resultMap = new HashMap<>();
        resultMap.put("isShowJobNumberWindow",IS_SHOW_JOBNUMBER_WINDOW);
        return new ResponseEntity(ResultVO.createSuccess(resultMap), HttpStatus.OK);
    }

    @ApiOperation(value = "查询微信认证确认二维码", notes = "查询微信认证确认二维码", tags={ "merchant", })
    @ApiResponses(value = {@ApiResponse(code = 0, message = "操作成功", response = WeChatStatusResultVo.class) })
    @RequestMapping(value = "/weChatStatusQuery", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    public ResponseEntity<ResultVO> weChatStatusQuery(@RequestHeader(name = "organSign", required = true) String organSign) {
        WeChatStatusResultDto weChatStatusResultDto = merchantApi.weChatStatusQuery(organSign);
        WeChatStatusResultVo weChatStatusResultVo = new WeChatStatusResultVo();
        BeanUtils.copyProperties(weChatStatusResultDto, weChatStatusResultVo);
        return new ResponseEntity(ResultVO.createSuccess(weChatStatusResultVo), HttpStatus.OK);
    }

    private boolean checkStaffByJobNum(String jobNumber) throws Exception{
        RequestConfig requestConfig = RequestConfig.custom().setConnectionRequestTimeout(10)
                .setConnectTimeout(7000)
                .setSocketTimeout(30000)
                .build();
        Map<String, Object> params = new HashMap<>(2);
        params.put("staffNum",jobNumber);
        String resultStr = HttpClientUtil.httpGet(SSO_API_URL + SSO_GET_USER_BY_STAFFNUM_OR_ACCOUNT ,params,null,requestConfig);
        ObjectMapper mapper = new ObjectMapper();
        if (StringUtils.isNotEmpty(resultStr)){
            Map<String,Object> map = mapper.readValue(resultStr, Map.class);
            logger.info("checkStaffByJobNum param:{},result:{},map:{}",jobNumber,resultStr);
            logger.info("code:{},result:{}",map.get("code"),map.get("result"));
            if ("200".equals(map.get("code").toString()) && map.get("result") != null){
                return true;
            }
        }
        return false;
    }


    private ResultVO checkMerchantCreateVoProp(MerchantCreateVo templateVo) {
        if(StringUtils.isEmpty(templateVo.getAccountType())) {
            return ResultVO.createError("商户类型不能为空");
        }
        if(templateVo.getBeneficiaryInfoVo() != null) {
            ResultVO resultVO = checkBeneficiaryInfoVo(templateVo.getBeneficiaryInfoVo());
            if(resultVO.getCode() != ResultCodeEnum.SUCCESS.getCode()) {
                return resultVO;
            }
        }
        if(templateVo.getBusinessInfoVo() != null) {
            ResultVO resultVO = checkBusinessInfoVo(templateVo.getBusinessInfoVo());
            if(resultVO.getCode() != ResultCodeEnum.SUCCESS.getCode()) {
                return resultVO;
            }
        }
        if(templateVo.getLicenseVo() != null) {
            ResultVO resultVO = checkLicenseVo(templateVo.getLicenseVo());
            if(resultVO.getCode() != ResultCodeEnum.SUCCESS.getCode()) {
                return resultVO;
            }
        }
        if(templateVo.getSettleInfoVo() != null) {
            ResultVO resultVO = checkSettleInfoVo(templateVo.getSettleInfoVo());
            if(resultVO.getCode() != ResultCodeEnum.SUCCESS.getCode()) {
                return resultVO;
            }
        }
        if(templateVo.getStoreInfoVo() != null) {
            ResultVO resultVO = checkStoreInfoVo(templateVo.getStoreInfoVo());
            if(resultVO.getCode() != ResultCodeEnum.SUCCESS.getCode()) {
                return resultVO;
            }
        }
        return ResultVO.createSuccess();
    }

    private ResultVO checkLicenseVo(LicenseVo licenseVo) {
        if(StringUtils.isEmpty(licenseVo.getLicenseType())) {
            return ResultVO.createError("执照类型不能为空");
        }
        if(StringUtils.isEmpty(licenseVo.getLicenseEndType())) {
            return ResultVO.createError("执照期限类型不能为空");
        }
        if("0".equals(licenseVo.getLicenseEndType())) {
            if(StringUtils.isEmpty(licenseVo.getLicenseStartDate()) || StringUtils.isEmpty(licenseVo.getLicenseEndDate())) {
                return ResultVO.createError("执照期限不能为空");
            }
        }
        if(StringUtils.isEmpty(licenseVo.getLicenseImg())) {
            return ResultVO.createError("执照图片不能为空");
        }
        if(StringUtils.isEmpty(licenseVo.getLicenseName())) {
            return ResultVO.createError("执照名称不能为空");
        }
        if(StringUtils.isEmpty(licenseVo.getLicenseNo())) {
            return ResultVO.createError("执照编号不能为空");
        }
        return ResultVO.createSuccess();
    }


    private ResultVO checkBusinessInfoVo(BusinessInfoVo businessInfoVo) {
        if(StringUtils.isEmpty(businessInfoVo.getAddress())) {
            return ResultVO.createError("商户详细地址不能为空");
        }
        if(StringUtils.isEmpty(businessInfoVo.getCashierDeskPic())) {
            return ResultVO.createError("收银台照片不能为空");
        }
        if(StringUtils.isEmpty(businessInfoVo.getInsidePic())) {
            return ResultVO.createError("经营场所内设照片不能为空");
        }
        if(StringUtils.isEmpty(businessInfoVo.getCityCode())||  StringUtils.isEmpty(businessInfoVo.getProvinceCode()) || StringUtils.isEmpty(businessInfoVo.getAreaCode())) {
            return ResultVO.createError("商户地区不能为空");
        }
        if(StringUtils.isEmpty(businessInfoVo.getDoorPic())) {
            return ResultVO.createError("商户门店图片不能为空");
        }
        if(StringUtils.isEmpty(businessInfoVo.getMccCode())) {
            return ResultVO.createError("商户行业不能为空");
        }
        if(StringUtils.isEmpty(businessInfoVo.getName())) {
            return ResultVO.createError("商户全称不能为空");
        }
        if(StringUtils.isEmpty(businessInfoVo.getShortName())) {
            return ResultVO.createError("商户简称不能为空");
        }
        return checkBusinessShortName(businessInfoVo.getShortName());
    }

    private ResultVO checkBusinessShortName(String name) {
        String[] filterWords = {"藏独","法轮功","色情","毒品","赌博","博彩","基金","理财","担保","贷款","集资","P2P",
                "麻醉","间谍服务","私人侦探","股权","众筹","乐刷","套现","千万户","000","t0","信用卡","官方",
                "担保","资金","淘宝","财务","收款","超时","商圈"};
        for(String word: filterWords) {
            if(name.contains(word)) {
                return ResultVO.createError("商户简称不能包含：" + word);
            }
        }
        if(isInteger(name)) {
            return ResultVO.createError("商户简称不能为纯数字");
        }
        return ResultVO.createSuccess();
    }

    public boolean isInteger(String str) {
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        return pattern.matcher(str).matches();
    }

    private ResultVO checkBeneficiaryInfoVo(BeneficiaryInfoVo beneficiaryInfoVo) {
        if(StringUtils.isEmpty(beneficiaryInfoVo.getBeneficiaryAddress())) {
            return ResultVO.createError("受益人地址不能为空");
        }
        if(StringUtils.isEmpty(beneficiaryInfoVo.getBeneficiaryCardNo())) {
            return ResultVO.createError("受益人证件号码不能为空");
        }
        if(StringUtils.isEmpty(beneficiaryInfoVo.getBeneficiaryName())) {
            return ResultVO.createError("受益人姓名不能为空");
        }
        if(StringUtils.isEmpty(beneficiaryInfoVo.getBeneficiaryCardType())) {
            return ResultVO.createError("受益人证件类型不能为空");
        }
        if(StringUtils.isEmpty(beneficiaryInfoVo.getBeneficiaryCardEndType())) {
            return ResultVO.createError("受益人证件期限类型不能为空");
        }
        if("0".equals(beneficiaryInfoVo.getBeneficiaryCardEndType())) {
            if(StringUtils.isEmpty(beneficiaryInfoVo.getBeneficiaryCardStartDate()) || StringUtils.isEmpty(beneficiaryInfoVo.getBeneficiaryCardEndDate())) {
                return ResultVO.createError("受益人证件日期不能为空");
            }
        }
        return ResultVO.createSuccess();
    }

    private ResultVO checkSettleInfoVo(SettleInfoVo settleInfoVo) {
        if(StringUtils.isEmpty(settleInfoVo.getBankCardFrontPic())) {
            return ResultVO.createError("银行卡或开户许可证图片不能为空");
        }
        if(StringUtils.isEmpty(settleInfoVo.getBankCardNo())) {
            return ResultVO.createError("银行卡号不能为空");
        }
        if(StringUtils.isEmpty(settleInfoVo.getLegalFlag())) {
            return ResultVO.createError("结算标志不能为空");
        }
        if(StringUtils.isEmpty(settleInfoVo.getUnionPay())) {
            return ResultVO.createError("开户支行不能为空");
        }
        if(StringUtils.isEmpty(settleInfoVo.getSettleType())) {
            return ResultVO.createError("账户类型不能为空");
        }
        return ResultVO.createSuccess();
    }

    private ResultVO checkStoreInfoVo(StoreInfoVo storeInfoVo) {
        if(StringUtils.isEmpty(storeInfoVo.getCorporationCardBackImg())) {
            return ResultVO.createError("法人身份证反面不能为空");
        }
        if(StringUtils.isEmpty(storeInfoVo.getCorporationCardFrontImg())) {
            return ResultVO.createError("法人身份证正面不能为空");
        }
        if(StringUtils.isEmpty(storeInfoVo.getCorporationCardEndType())) {
            return ResultVO.createError("法人证件期限类型不能为空");
        }
        if("0".equals(storeInfoVo.getCorporationCardEndType())) {
            if(StringUtils.isEmpty(storeInfoVo.getCorporationCardStartDate()) || StringUtils.isEmpty(storeInfoVo.getCorporationCardEndDate())) {
                return ResultVO.createError("法人证件日期不能为空");
            }
        }
        if(StringUtils.isEmpty(storeInfoVo.getCorporationCardNo())) {
            return ResultVO.createError("法人证件号码不能为空");
        }
        if(StringUtils.isEmpty(storeInfoVo.getCorporationName())) {
            return ResultVO.createError("法人姓名不能为空");
        }
        return ResultVO.createSuccess();
    }
}
