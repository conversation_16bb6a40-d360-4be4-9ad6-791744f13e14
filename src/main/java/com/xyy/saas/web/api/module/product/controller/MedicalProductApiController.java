package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.medical.core.api.MedicalProductApi;
import com.xyy.saas.medical.core.dto.SaasMedicalProductDto;
import com.xyy.saas.web.api.module.product.model.ImportMedicalVO;
import com.xyy.saas.web.api.module.product.model.MedicalProductModel;
import com.xyy.saas.web.api.module.product.model.UploadMedicalVO;
import com.xyy.saas.web.api.module.product.util.CollateDataUtil;
import com.xyy.saas.web.api.module.utils.DateUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * <AUTHOR>
 * @title: MedicalProductApiController
 * @date 2019-09-07  20:45
 * @description: 医保商品swag 实现
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-09-11T15:28:18.933+08:00")
@Controller
@Slf4j
public class MedicalProductApiController implements MedicalProductApiSwag {

    @Reference(version = "0.0.1")
    private MedicalProductApi medicalProductApi;

    @Override
    public ResultVO syncMedicalProducts(HttpServletRequest request, @RequestBody UploadMedicalVO uploadMedicalVO) {
        String organSign = uploadMedicalVO.getOrganSign();
        return ResultVO.createSuccess(medicalProductApi.syncMedicalProducts(organSign));
    }

    @Override
    public ResultVO syncData(HttpServletRequest request, String organSign, Integer baseVersion, Integer count) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("organSign",organSign);
        paramMap.put("baseVersion",baseVersion);
        paramMap.put("count",count);
        return ResultVO.createSuccess(medicalProductApi.syncData(paramMap));
    }

    @Override
    public ResultVO uploadMedicalProducts(HttpServletRequest request, @RequestBody UploadMedicalVO uploadMedicalVO) {
        if (uploadMedicalVO==null) {
            log.info("医保上传上传数据为空");
            return new ResultVO(ResultCodeEnum.ERROR,"数据为空");
        }
        if (CollectionUtils.isEmpty(uploadMedicalVO.getMedicalProductList())) {
            log.info("医保上传上传医保列表为空");
            return new ResultVO(ResultCodeEnum.ERROR,"医保列表为空");
        }
        List<SaasMedicalProductDto> saasMedicalProductDtos = new ArrayList<>();
        for (MedicalProductModel medicalProductModel : uploadMedicalVO.getMedicalProductList()) {
            SaasMedicalProductDto medicalProductDto = new SaasMedicalProductDto();
            BeanUtils.copyProperties(medicalProductModel,medicalProductDto);
            //时间处理
            medicalProductDto.setUpdateTime(DateUtil.praseStr2DatePos(medicalProductModel.getUpdateTime(), DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS));
            medicalProductDto.setCreateTime(DateUtil.praseStr2DatePos(medicalProductModel.getCreateTime(), DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS));
            saasMedicalProductDtos.add(medicalProductDto);
        }
        return ResultVO.createSuccess(medicalProductApi.uploadMedicalProducts(saasMedicalProductDtos,uploadMedicalVO.getOrganSign()));
    }

    @Override
    public ResultVO banchImportMedicalData(HttpServletRequest request, ImportMedicalVO importMedicalVO) {
        return ResultVO.createSuccess(medicalProductApi.banchImportMedicalData(importMedicalVO.getList(),importMedicalVO.getOrganSign(),importMedicalVO.getEmployeeId()));
    }

}
