package com.xyy.saas.web.api.module.medical.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.github.tobato.fastdfs.domain.fdfs.StorePath;
import com.github.tobato.fastdfs.service.FastFileStorageClient;
import com.xyy.saas.common.exception.BusinessException;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.pay.core.api.PayInfoApi;
import com.xyy.saas.pay.core.dto.GeneratorQRCodeResultDto;
import com.xyy.saas.pay.core.dto.PayInfoDto;
import com.xyy.saas.pay.core.dto.res.PayQrCodeInfoDto;
import com.xyy.saas.pay.core.emum.ChinaUmsBillStatus;
import com.xyy.saas.user.center.api.client.madical.MedicalOrderApi;
import com.xyy.saas.user.center.api.enums.MedicalOrderStatusEnum;
import com.xyy.saas.user.center.api.pojo.request.medical.MedicalOrderCondition;
import com.xyy.saas.user.center.api.pojo.request.medical.MedicalOrderParam;
import com.xyy.saas.user.center.api.pojo.request.medical.MedicalToMaoZhuMqParam;
import com.xyy.saas.user.center.api.pojo.response.medical.MedicalInsurancePackageDTO;
import com.xyy.saas.user.center.api.pojo.response.medical.MedicalOrderDTO;
import com.xyy.saas.user.center.api.pojo.response.medical.MedicalOrderStatisticDTO;
import com.xyy.saas.web.api.common.config.PaymentChangeGrayConfig;
import com.xyy.saas.web.api.common.constants.TemplateConstants;
import com.xyy.saas.web.api.module.medical.model.MedicalInsuranceOrderCondition;
import com.xyy.saas.web.api.module.medical.model.MedicalInsurancePackage;
import com.xyy.saas.web.api.module.medical.model.MedicalOrderQrCode;
import com.xyy.saas.web.api.module.utils.PDFUtil;
import com.xyy.user.center.common.enums.CodeEnum;
import com.xyy.user.center.common.model.Result;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.dto.result.QueryDrugstoreDto;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 医保服务购买
 * <AUTHOR>
 * @date 2021/08/27
 */
@Slf4j
@Api(value = "医保服务购买", tags = {"医保服务购买"})
@RestController
@RequestMapping("/pay/medical")
public class MedicalInsurancePurchaseController {
    @Reference(version = "1.0.0")
    private MedicalOrderApi medicalOrderApi;
    @Reference( version = "0.0.1")
//    @Reference( version = "0.0.1",url = "127.0.0.1:20848")
    private PayInfoApi payInfoApi;
    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;

    @Resource
    @Qualifier("unAuthFastFileStorageClient")
    private FastFileStorageClient fastFileStorageClient;
    @Value("${unauthfdfs.upload.url}")
    private String uploadFileUrl;


    @Autowired
    private PaymentChangeGrayConfig paymentChangeGrayConfig;

    @Autowired
    ResourceLoader resourceLoader;

    @Autowired
    private PDFUtil pdfUtil;

    /**
     * 购买统计
     * @param organSign
     * @return
     */
    @PostMapping("/orderStatistic")
    public ResponseEntity<ResultVO<MedicalOrderStatisticDTO>> orderStatistic(@RequestHeader(value = "organSign") String organSign){
        MedicalOrderCondition condition = new MedicalOrderCondition();
        condition.setOrganSign(organSign);
        condition.setOrderStatus(MedicalOrderStatusEnum.PAID.getOrderStatus());
        Result<MedicalOrderStatisticDTO> statisticResult = medicalOrderApi.queryStatistic(condition);
        ResultVO resultVO = new ResultVO(statisticResult.getCode(), statisticResult.getMsg(), statisticResult.getResult());
        return new ResponseEntity(resultVO, HttpStatus.OK);
    }

    /**
     * 售卖列表
     * @return
     */
    @PostMapping("/saleList")
    public ResponseEntity<ResultVO<MedicalInsurancePackageDTO>> saleList(){
        Result<List<MedicalInsurancePackageDTO>> packageListResult = medicalOrderApi.queryPackageList();
        ResultVO resultVO = new ResultVO(packageListResult.getCode(), packageListResult.getMsg(), packageListResult.getResult());
        return new ResponseEntity(resultVO, HttpStatus.OK);
    }

    /**
     * 支付二维码
     * @param organSign
     * @param employeeId
     * @param medicalInsurancePackage
     * @return
     */
    @PostMapping("/payQrCode")
    public ResponseEntity<ResultVO<MedicalOrderQrCode>> payQrCode(@RequestHeader(value = "organSign") String organSign,
                                                                  @RequestHeader(value = "employeeId") Integer employeeId,
                                                                  @RequestBody MedicalInsurancePackage medicalInsurancePackage) throws BusinessException {
        MedicalOrderParam medicalOrderParam = new MedicalOrderParam();
        medicalOrderParam.setOrganSign(organSign);
        medicalOrderParam.setEmployeeId(employeeId);
        medicalOrderParam.setPackageId(medicalInsurancePackage.getPackageId());
        // 生成订单
        Result<MedicalOrderDTO> orderResult = medicalOrderApi.createOrder(medicalOrderParam);
        if (!orderResult.isSuccess()) {
            return new ResponseEntity(new ResultVO<>(orderResult.getCode(), orderResult.getMsg(), orderResult.getResult()), HttpStatus.OK);
        }
        MedicalOrderDTO order = orderResult.getResult();
        // 生成付款二维码
        PayInfoDto payInfoDto = new PayInfoDto();
        payInfoDto.setOrderNo(order.getOrderNo());
        payInfoDto.setOrganSign(organSign);
        //1被扫 2主扫 3 h5 4app 5小程序  6公众号 7 短信
        payInfoDto.setOrderSource(1);
        payInfoDto.setTotalAmount(order.getOrderPrice());
        // 走之前的 银联商户二维码
        if(!paymentChangeGrayConfig.payChannleChange(organSign)){
            payInfoDto.setBusinessType(7);
            GeneratorQRCodeResultDto qrCodeResultDto = null;
            // 重试3次尝试获取二维码url
            for (int i = 0; i < 3; ++i) {
                log.info("医保服务生成充值二维码请求:{}", JSON.toJSONString(payInfoDto));
                try {
                    qrCodeResultDto = payInfoApi.insertChinaUmsC2BPayInfo(payInfoDto);
                } catch (Exception e) {
                    log.error("医保服务生成充值二维码异常", e);
                    return new ResponseEntity(new ResultVO(CodeEnum.ERROR.getCode(), "生成支付二维码异常", null), HttpStatus.OK);
                }
                if (qrCodeResultDto != null && qrCodeResultDto.getErrCode().equals("SUCCESS")) {
                    break;
                }
            }
            if (qrCodeResultDto == null || !qrCodeResultDto.getErrCode().equals("SUCCESS")) {
                return new ResponseEntity(new ResultVO(CodeEnum.ERROR.getCode(), "生成支付二维码失败", null), HttpStatus.OK);
            }
            MedicalOrderQrCode medicalOrderQrCode = new MedicalOrderQrCode();
            medicalOrderQrCode.setOrderNo(order.getOrderNo());
            medicalOrderQrCode.setQrCode(qrCodeResultDto.getBillQRCode());
            medicalOrderQrCode.setOrderPrice(order.getOrderPrice());
            return new ResponseEntity(ResultVO.createSuccess(medicalOrderQrCode), HttpStatus.OK);
        }
        // 对接支付中台，设置新的业务类型 8- 医保购买
        payInfoDto.setBusinessType(8);
        payInfoDto.setPayType(medicalInsurancePackage.getPayType());
        PayQrCodeInfoDto payQrCodeInfoDto = payInfoApi.paymentChannlePayInfo(payInfoDto);
        MedicalOrderQrCode medicalOrderQrCode = new MedicalOrderQrCode();
        medicalOrderQrCode.setOrderNo(order.getOrderNo());
        medicalOrderQrCode.setOrderPrice(order.getOrderPrice());
        medicalOrderQrCode.setQrCode(payQrCodeInfoDto.getQrCode());

        return new ResponseEntity(ResultVO.createSuccess(medicalOrderQrCode), HttpStatus.OK);
    }

    /**
     * 查询订单支付状态
     * @param organSign
     * @param condition
     * @return
     */
    @PostMapping("/checkPayStatus")
    public ResponseEntity<ResultVO<Boolean>> checkPayStatus(@RequestHeader(value = "organSign") String organSign,
                                                            @RequestBody MedicalInsuranceOrderCondition condition) throws BusinessException {
        ResultVO<Boolean> resultVO = this.updateOrderStatus(organSign, condition.getOrderNo());
        return new ResponseEntity(resultVO, HttpStatus.OK);
    }

    /**
     * 订单列表
     * @param organSign
     * @param condition
     * @return
     */
    @PostMapping("orderList")
    public ResponseEntity<ResultVO<PageInfo<MedicalOrderDTO>>> orderList(@RequestHeader(value = "organSign") String organSign,
                                                                         @RequestBody MedicalInsuranceOrderCondition condition){
        MedicalOrderCondition medicalOrderCondition = new MedicalOrderCondition();
        medicalOrderCondition.setOrganSign(organSign);
        medicalOrderCondition.setPayTimeFrom(condition.getPayTimeFrom());
        medicalOrderCondition.setPayTimeTo(condition.getPayTimeTo());
        medicalOrderCondition.setEmployeeName(condition.getEmployeeName());
        medicalOrderCondition.setOrderStatus(MedicalOrderStatusEnum.PAID.getOrderStatus());
        medicalOrderCondition.setPageNum(condition.getPageNum());
        medicalOrderCondition.setPageSize(condition.getPageSize());
        Result<PageInfo<MedicalOrderDTO>> pageInfoResult = medicalOrderApi.queryPageList(medicalOrderCondition);
        ResultVO<PageInfo<MedicalOrderDTO>> resultVO = ResultVO.createSuccess(pageInfoResult.getResult());
        return new ResponseEntity(resultVO, HttpStatus.OK);
    }

    /**
     * 定时检查未支付订单
     * @return
     */
    @RequestMapping("/scheduleCheckPayStatus")
    public ResponseEntity<ResultVO<Boolean>> scheduleCheckOrderStatus(){
        log.info("定时任务开始-医保服务订单刷新订单状态");
        MedicalOrderCondition condition = new MedicalOrderCondition();
        condition.setOrderStatus(MedicalOrderStatusEnum.UNPAID.getOrderStatus());
        Date currentTime = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(currentTime);
        c.add(Calendar.HOUR, -24);
        Date dataBefore = c.getTime();
        condition.setCreateTimeFrom(dataBefore);
        condition.setCreateTimeTo(currentTime);
        condition.setPageNum(1);
        condition.setPageSize(500);
        Result<PageInfo<MedicalOrderDTO>> pageInfoResult = medicalOrderApi.queryPageList(condition);
        PageInfo<MedicalOrderDTO> pageInfo = pageInfoResult.getResult();
        if (pageInfo == null || CollectionUtils.isEmpty(pageInfo.getList())) {
            log.info("定时任务结束-医保服务订单刷新订单状态(无需要刷新订单数据)");
            return new ResponseEntity(ResultVO.createSuccess(true), HttpStatus.OK);
        }
        List<MedicalOrderDTO> list = pageInfo.getList();
        for (MedicalOrderDTO dto : list) {
            try {
                this.updateOrderStatus(dto.getOrganSign(), dto.getOrderNo());
                log.info("定时任务结束-医保服务订单刷新订单状态");
            } catch (Exception e) {
                log.error("定时检查未支付订单异常, request:{}", JSON.toJSONString(dto), e);
            }
        }
        return new ResponseEntity(ResultVO.createSuccess(true), HttpStatus.OK);
    }

    /**
     * 查询订单支付状态更新订单状态
     * @param organSign
     * @param orderNo
     * @return
     */
    private ResultVO<Boolean> updateOrderStatus(String organSign, String orderNo) throws BusinessException {
        Result<MedicalOrderDTO> orderResult = medicalOrderApi.queryByOrderNo(orderNo);
        if (orderResult == null || orderResult.getResult() == null) {
            return ResultVO.createError("订单不存在");
        }
        MedicalOrderDTO order = orderResult.getResult();
        if (order.getOrderStatus() == MedicalOrderStatusEnum.PAID.getOrderStatus()) {
            return ResultVO.createSuccess(Boolean.TRUE);
        }

        List<PayInfoDto> list = new ArrayList<>();
        PayInfoDto payDto = new PayInfoDto();
        payDto.setOrderNo(orderNo);
        payDto.setOrganSign(organSign);
        list.add(payDto);
        List<PayInfoDto> resultList;
        if(!paymentChangeGrayConfig.payChannleChange(organSign)){
            resultList = payInfoApi.getChinaUmsC2BPayStatus(list);
        }else{
            resultList = payInfoApi.getPaymentPayStatus(payDto);
        }
        if (CollectionUtils.isEmpty(resultList)) {
            return ResultVO.createError("支付接口查询订单为空");
        }
        PayInfoDto payInfoDto = resultList.get(0);
        if (payInfoDto.getStatus() == ChinaUmsBillStatus.PAID) {
            Date payTime = payInfoDto.getCreateTime();
            if (payTime == null) {
                payTime = new Date();
            }
            // 医保服务结束时间为支付时间3年后
            Calendar c = Calendar.getInstance();
            c.setTime(payTime);
            c.add(Calendar.YEAR, 3);
            Date serviceEndTime = c.getTime();

            BigDecimal payAmount = payInfoDto.getTotalAmount().divide(new BigDecimal(100));
            MedicalOrderDTO orderDTO = new MedicalOrderDTO();
            orderDTO.setOrderNo(orderNo);
            orderDTO.setOrderStatus(MedicalOrderStatusEnum.PAID.getOrderStatus());
            orderDTO.setPayAmount(payAmount);
            orderDTO.setPayTime(payTime);
            orderDTO.setServiceEndTime(serviceEndTime);
            orderDTO.setAgreementUrl(getPDFUrl(organSign));
            Result<Boolean> updateResult = medicalOrderApi.updateOrder(orderDTO);
            if (updateResult.isSuccess() && updateResult.getResult()) {
                // 发送MQ通知毛竹签约
                MedicalToMaoZhuMqParam param = new MedicalToMaoZhuMqParam();
                param.setOrganSign(organSign);
                param.setPayAmount(payAmount);
                param.setOrderNo(orderNo);
                medicalOrderApi.notifyMaoZhuToSign(param);
            }
            return ResultVO.createSuccess(Boolean.TRUE);
        }
        return ResultVO.createSuccess(Boolean.FALSE);
    }

    private String getPDFUrl(String organSign){
        String url = "";
        try {
            QueryDrugstoreDto queryDrugstoreDto = drugstoreApi.queryDrugstoreByOrganSign(organSign).getResult();
            //设置模板参数
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("drugstoreName", queryDrugstoreDto.getDrugstoreName());
            //获取模板内容
            String templateContent = pdfUtil.getTemplateContent(TemplateConstants.PDF_TEMPLATE, paramMap);
            byte[] pdf = pdfUtil.html2Pdf(templateContent);

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String fileName = "医保购买协议" + organSign + "-" + sdf.format(new Date()) + ".pdf";
            StorePath storePath = null;
            InputStream inputStream = new ByteArrayInputStream(pdf);
            storePath = fastFileStorageClient.uploadFile(inputStream, pdf.length, FilenameUtils.getExtension(fileName), null);
            url = uploadFileUrl + storePath.getFullPath();
        }catch (Exception e){
            log.error("MedicalInsurancePurchaseController#getPDFUrl error:{}",e);
        }
        return url;
    }
}
