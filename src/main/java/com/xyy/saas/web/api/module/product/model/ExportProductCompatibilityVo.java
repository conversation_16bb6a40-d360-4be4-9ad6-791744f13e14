package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;

import java.io.Serializable;

/**
 * @ClassName ExportProductCompatibilityVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/9/26 11:25
 * @Version 1.0
 **/
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-07T14:34:49.141+08:00")

@ApiModel(description = "商品配伍禁忌导出查询对象")
public class ExportProductCompatibilityVo implements Serializable {

    @JsonProperty("commonName")
    private String commonName;//通用名称（页面展示用）
    @JsonProperty("attributeSpecification")
    private String attributeSpecification;//规格（页面展示）
    @JsonProperty("unitId")
    private String unitId;//单位（页面展示）
    @JsonProperty("pharmacyPref")
    private String pharmacyPref;//商品编号（页面展示用）
    @JsonProperty("systemType")
    private String systemType;//商品分类

    @JsonProperty("subcommonName")
    private String subcommonName;//通用名称（页面展示用）
    @JsonProperty("subattributeSpecification")
    private String subattributeSpecification;//规格（页面展示）
    @JsonProperty("subunitId")
    private String subunitId;//单位（页面展示）
    @JsonProperty("subpharmacyPref")
    private String subpharmacyPref;//商品编号（页面展示用）
    @JsonProperty("subsystemType")
    private String subsystemType;//商品分类

    @JsonProperty("createUser")
    private String createUser;//创建人
    @JsonProperty("createTime")
    private String createTime;//操作时间

    private String  drugPermissionPerson; //药品上市许可证持有人

    private String subdrugPermissionPerson;//子表药品上市许可证持有人

    public String getSubdrugPermissionPerson() {
        return subdrugPermissionPerson;
    }

    public void setSubdrugPermissionPerson(String subdrugPermissionPerson) {
        this.subdrugPermissionPerson = subdrugPermissionPerson;
    }

    public String getDrugPermissionPerson() {
        return drugPermissionPerson;
    }

    public void setDrugPermissionPerson(String drugPermissionPerson) {
        this.drugPermissionPerson = drugPermissionPerson;
    }

    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    public String getAttributeSpecification() {
        return attributeSpecification;
    }

    public void setAttributeSpecification(String attributeSpecification) {
        this.attributeSpecification = attributeSpecification;
    }

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public String getPharmacyPref() {
        return pharmacyPref;
    }

    public void setPharmacyPref(String pharmacyPref) {
        this.pharmacyPref = pharmacyPref;
    }

    public String getSystemType() {
        return systemType;
    }

    public void setSystemType(String systemType) {
        this.systemType = systemType;
    }

    public String getSubcommonName() {
        return subcommonName;
    }

    public void setSubcommonName(String subcommonName) {
        this.subcommonName = subcommonName;
    }

    public String getSubattributeSpecification() {
        return subattributeSpecification;
    }

    public void setSubattributeSpecification(String subattributeSpecification) {
        this.subattributeSpecification = subattributeSpecification;
    }

    public String getSubunitId() {
        return subunitId;
    }

    public void setSubunitId(String subunitId) {
        this.subunitId = subunitId;
    }

    public String getSubpharmacyPref() {
        return subpharmacyPref;
    }

    public void setSubpharmacyPref(String subpharmacyPref) {
        this.subpharmacyPref = subpharmacyPref;
    }

    public String getSubsystemType() {
        return subsystemType;
    }

    public void setSubsystemType(String subsystemType) {
        this.subsystemType = subsystemType;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

}
