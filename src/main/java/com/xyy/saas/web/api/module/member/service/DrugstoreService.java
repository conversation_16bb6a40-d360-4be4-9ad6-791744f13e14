package com.xyy.saas.web.api.module.member.service;

import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;

import java.util.List;
import java.util.Map;

/**
 * 封装药店机构Api服务的接口
 * <AUTHOR>
 */
public interface DrugstoreService {

    /**
     * 查询机构详情
     * @param organSign 机构标识
     * @return
     */
    SaaSDrugstoreDto getDrugstoreByOrganSign(String organSign);

    /**
     * 根据机构标识列表查询机构列表
     * @param organSigns 机构标识列表
     * @return
     */
    List<SaaSDrugstoreDto> getListByOrganSignList(List<String> organSigns);

    /**
     * 根据机构标识列表查询机构map
     * @param organSigns 机构标识列表
     * @return
     */
    Map<String, SaaSDrugstoreDto> getMapByOrganSignList(List<String> organSigns);

    /**
     * 根据总部机构标识查询所有门店
     * @param headquartersOrganSign 总部机构标识
     * @return
     */
    List<SaaSDrugstoreDto> getDrugstoreByHeadquartersOrganSign(String headquartersOrganSign);

    /**
     * 根据总部机构标识查询所有门店
     * @param headquartersOrganSign 总部机构标识
     * @return
     */
    List<SaaSDrugstoreDto> getDrugstoreByHeadquartersOrganSign(Byte isDrugstoreHidden, String headquartersOrganSign);

    /**
     * 根据总部机构标识查询所有门店map
     * @param headquartersOrganSign 总部机构标识
     * @return
     */
    Map<String, SaaSDrugstoreDto> getDrugstoreMapByHeadquartersOrganSign(String headquartersOrganSign);

    /**
     * 根据门店机构标识查询总部机构
     * @param organSign 门店机构标识
     * @return
     */
    SaaSDrugstoreDto getHeadquartersDrugstoreByOrganSign(String organSign);

//    /**
//     * 查看登录用户是否有导出明文数据权限
//     * @param operateId
//     * @return
//     */
//    Boolean getDataAuthByEmployeeId(Integer operateId);

}
