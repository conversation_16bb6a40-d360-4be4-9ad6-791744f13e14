package com.xyy.saas.web.api.module.pay.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/5/10 18:34
 */
@ApiModel(description = "商家账户提审")
@Data
public class MerchantVerifyVo {

    @ApiModelProperty(value = "商户类型   （小微 ： personal ， 个体：self-employed ,  商户 ：merchant）")
    private String accountType;

    @ApiModelProperty(value = "销售/实施工号")
    private String jobNumber;
}
