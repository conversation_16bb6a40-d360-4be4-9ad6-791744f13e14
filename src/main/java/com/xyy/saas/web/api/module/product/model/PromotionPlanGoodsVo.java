package com.xyy.saas.web.api.module.product.model;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

public class PromotionPlanGoodsVo {
	/** */
	@ApiModelProperty(value = "")
	private Long id;

	/** 商品ID */
	@ApiModelProperty(value = "商品ID")
	private Long productId;

	/** 促销活动ID */
	@ApiModelProperty(value = "促销活动ID")
	private Long promotionId;

	/** 创建人 */
	@ApiModelProperty(value = "创建人")
	private String createUser;

	/** 创建时间 */
	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	/** 更新人 */
	@ApiModelProperty(value = "更新人")
	private String updateUser;

	/** 更新时间 */
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;

	/** 逻辑删除 1 有效 0 删除 */
	@ApiModelProperty(value = "逻辑删除 1 有效 0 删除")
	private Byte yn;
	/** 数据同步字段 */
	@ApiModelProperty(value = "数据同步字段")
	private Long baseVersion;

	/** 药店机构唯一标示 */
	@ApiModelProperty(value = "药店机构唯一标示")
	private String organSign;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}

	public Long getPromotionId() {
		return promotionId;
	}

	public void setPromotionId(Long promotionId) {
		this.promotionId = promotionId;
	}

	public String getCreateUser() {
		return createUser;
	}

	public void setCreateUser(String createUser) {
		this.createUser = createUser == null ? null : createUser.trim();
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getUpdateUser() {
		return updateUser;
	}

	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser == null ? null : updateUser.trim();
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Byte getYn() {
		return yn;
	}

	public void setYn(Byte yn) {
		this.yn = yn;
	}

	public Long getBaseVersion() {
		return baseVersion;
	}

	public void setBaseVersion(Long baseVersion) {
		this.baseVersion = baseVersion;
	}

	public String getOrganSign() {
		return organSign;
	}

	public void setOrganSign(String organSign) {
		this.organSign = organSign;
	}
}
