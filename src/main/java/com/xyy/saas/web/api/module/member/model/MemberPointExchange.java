package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 积分兑换规则表
 * <AUTHOR>
 */
@ApiModel(description = "积分兑换规则表")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-06T17:16:06.377+08:00")
public class MemberPointExchange   {
  private Long id = null;

  private Integer exchangeType = null;

  private Integer memberLevelId = null;
  
  private Long[] memberLevelIds;

  private Double consumePrice = null;

  private BigDecimal point = null;

  private Double exchangePrice = null;

  private String createUser = null;

  private String createTime = null;

  private String updateUser = null;

  private String updateTime = null;

  private Integer yn = null;

  private String organsign = null;

  private Integer baseVersion = null;

  public MemberPointExchange id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * id
   * @return id
  **/
  @ApiModelProperty(value = "id")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public MemberPointExchange exchangeType(Integer exchangeType) {
    this.exchangeType = exchangeType;
    return this;
  }

   /**
   * 兑换规则  1 零售 2 兑换
   * @return exchangeType
  **/
  @ApiModelProperty(value = "兑换规则  1 零售 2 兑换   必传")
  public Integer getExchangeType() {
    return exchangeType;
  }

  public void setExchangeType(Integer exchangeType) {
    this.exchangeType = exchangeType;
  }

  public MemberPointExchange memberLevelId(Integer memberLevelId) {
    this.memberLevelId = memberLevelId;
    return this;
  }

   public Long[] getMemberLevelIds() {
	return memberLevelIds;
}

public void setMemberLevelIds(Long[] memberLevelIds) {
	this.memberLevelIds = memberLevelIds;
}

/**
   * 会员等级id
   * @return memberLevelId
  **/
  @ApiModelProperty(value = "会员等级id  必传")
  public Integer getMemberLevelId() {
    return memberLevelId;
  }

  public void setMemberLevelId(Integer memberLevelId) {
    this.memberLevelId = memberLevelId;
  }

  public MemberPointExchange consumePrice(Double consumePrice) {
    this.consumePrice = consumePrice;
    return this;
  }

   /**
   * 消费金额
   * @return consumePrice
  **/
  @ApiModelProperty(value = "消费金额 必传")
  public Double getConsumePrice() {
    return consumePrice;
  }

  public void setConsumePrice(Double consumePrice) {
    this.consumePrice = consumePrice;
  }

  public MemberPointExchange point(BigDecimal point) {
    this.point = point;
    return this;
  }

   /**
   * 积分 
   * @return point
  **/
  @ApiModelProperty(value = "积分 必传")
  public BigDecimal getPoint() {
    return point;
  }

  public void setPoint(BigDecimal point) {
    this.point = point;
  }

  public MemberPointExchange createUser(String createUser) {
    this.createUser = createUser;
    return this;
  }

   /**
   * 创建人
   * @return createUser
  **/
  @ApiModelProperty(value = "创建人 非必传")
  public String getCreateUser() {
    return createUser;
  }

  public void setCreateUser(String createUser) {
    this.createUser = createUser;
  }

  public MemberPointExchange createTime(String createTime) {
    this.createTime = createTime;
    return this;
  }

   /**
   * 创建时间
   * @return createTime
  **/
  @ApiModelProperty(value = "创建时间")
  public String getCreateTime() {
    return createTime;
  }

  public void setCreateTime(String createTime) {
    this.createTime = createTime;
  }

  public MemberPointExchange updateUser(String updateUser) {
    this.updateUser = updateUser;
    return this;
  }

   /**
   * 更新人
   * @return updateUser
  **/
  @ApiModelProperty(value = "更新人 非必传")


  public String getUpdateUser() {
    return updateUser;
  }

  public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
  }

  public MemberPointExchange updateTime(String updateTime) {
    this.updateTime = updateTime;
    return this;
  }

   /**
   * 更新时间
   * @return updateTime
  **/
  @ApiModelProperty(value = "更新时间 非必传")


  public String getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(String updateTime) {
    this.updateTime = updateTime;
  }

  public MemberPointExchange yn(Integer yn) {
    this.yn = yn;
    return this;
  }

   /**
   * 是否删除 1 有效 0 删除
   * @return yn
  **/
  @ApiModelProperty(value = "是否删除 1 有效 0 删除")


  public Integer getYn() {
    return yn;
  }

  public void setYn(Integer yn) {
    this.yn = yn;
  }

  public MemberPointExchange organsign(String organsign) {
    this.organsign = organsign;
    return this;
  }

   /**
   * 机构标识
   * @return organsign
  **/
  @ApiModelProperty(value = "机构标识 非必传")


  public String getOrgansign() {
    return organsign;
  }

  public void setOrgansign(String organsign) {
    this.organsign = organsign;
  }

  public MemberPointExchange baseVersion(Integer baseVersion) {
    this.baseVersion = baseVersion;
    return this;
  }

   /**
   * 版本号
   * @return baseVersion
  **/
  @ApiModelProperty(value = "版本号 非必传")
  public Integer getBaseVersion() {
    return baseVersion;
  }

  public void setBaseVersion(Integer baseVersion) {
    this.baseVersion = baseVersion;
  }


  @ApiModelProperty(value = "抵扣金额 必传")
  public Double getExchangePrice() {
    return exchangePrice;
  }

  public void setExchangePrice(Double exchangePrice) {
    this.exchangePrice = exchangePrice;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MemberPointExchange memberPointExchange = (MemberPointExchange) o;
    return Objects.equals(this.id, memberPointExchange.id) &&
        Objects.equals(this.exchangeType, memberPointExchange.exchangeType) &&
        Objects.equals(this.memberLevelId, memberPointExchange.memberLevelId) &&
        Objects.equals(this.consumePrice, memberPointExchange.consumePrice) &&
        Objects.equals(this.point, memberPointExchange.point) &&
        Objects.equals(this.createUser, memberPointExchange.createUser) &&
        Objects.equals(this.createTime, memberPointExchange.createTime) &&
        Objects.equals(this.updateUser, memberPointExchange.updateUser) &&
        Objects.equals(this.updateTime, memberPointExchange.updateTime) &&
        Objects.equals(this.yn, memberPointExchange.yn) &&
        Objects.equals(this.organsign, memberPointExchange.organsign) &&
        Objects.equals(this.baseVersion, memberPointExchange.baseVersion);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, exchangeType, memberLevelId, consumePrice, point, createUser, createTime, updateUser, updateTime, yn, organsign, baseVersion);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MemberPointExchange {\n");

    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    exchangeType: ").append(toIndentedString(exchangeType)).append("\n");
    sb.append("    memberLevelId: ").append(toIndentedString(memberLevelId)).append("\n");
    sb.append("    memberLevelIds: ").append(toIndentedString(memberLevelIds)).append("\n");
    sb.append("    consumePrice: ").append(toIndentedString(consumePrice)).append("\n");
    sb.append("    point: ").append(toIndentedString(point)).append("\n");
    sb.append("    createUser: ").append(toIndentedString(createUser)).append("\n");
    sb.append("    createTime: ").append(toIndentedString(createTime)).append("\n");
    sb.append("    updateUser: ").append(toIndentedString(updateUser)).append("\n");
    sb.append("    updateTime: ").append(toIndentedString(updateTime)).append("\n");
    sb.append("    yn: ").append(toIndentedString(yn)).append("\n");
    sb.append("    organsign: ").append(toIndentedString(organsign)).append("\n");
    sb.append("    baseVersion: ").append(toIndentedString(baseVersion)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

