package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.member.core.api.MemberExchangeInfoApi;
import com.xyy.saas.member.core.api.MemberPointHistoryForZBApi;
import com.xyy.saas.member.core.dto.MemberExchangeInfoDto;
import com.xyy.saas.member.core.dto.MemberPointHistoryConditionDto;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.web.api.module.member.model.MemberPointHistory;
import com.xyy.saas.web.api.module.utils.ExportExcelUtil;
import io.swagger.annotations.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 总部端会员积分变更记录
 * <AUTHOR>
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-06T17:16:06.377+08:00")

@Controller
@RequestMapping("/member/memberPointHistroy/zb")
@Api(value = "memberPointHistroyForZB", description = "总部端积分变更记录接口")
public class MemberPointHistoryForZBApiController {
    private static final Logger logger = Logger.getLogger(MemberPointHistoryForZBApiController.class);

    @Reference( version = "0.0.1")
    private MemberPointHistoryForZBApi memberPointHistoryForZBApi;

    @Reference( version = "0.0.1")
    private MemberExchangeInfoApi memberExchangeInfoApi;

    @ApiOperation(value = "总部端:积分变更记录列表", notes = "总部端:积分变更记录列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = com.xyy.saas.common.util.ResultVO.class) })
    @RequestMapping(value = "/getMemberPointHistoryPage", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> getMemberPointHistoryPageZB(@RequestHeader("commonRequestModel") String commonRequestModel, @ApiParam(value = "会员积分变动历史" ,required=true )  @RequestBody MemberPointHistoryConditionDto memberPointHistory) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(memberPointHistory.getPageNum() == null ? 1:memberPointHistory.getPageNum());
        pageInfo.setPageSize(memberPointHistory.getPageSize() == null ? 20:memberPointHistory.getPageSize());
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        memberPointHistory.setHeadquartersOrganSign(model.getOrganSign());
        PageInfo result = memberPointHistoryForZBApi.getMemberPointHistoryPagerZB(memberPointHistory, pageInfo);
        return new ResponseEntity<ResultVO>(new ResultVO(result), HttpStatus.OK);
    }

    @ApiOperation(value = "总部端:积分变更记录详情", notes = "总部端:积分变更记录详情")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberPointHistory.class) })
    @RequestMapping(value = "/getMemberPointHistoryDetail", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> getMemberPointHistoryDetail(@RequestHeader("commonRequestModel") String commonRequestModel,
                                                                @ApiParam(value = "会员积分单据编号",required=true )  @RequestBody MemberExchangeInfoDto memberExchangeInfoDto) {
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        memberExchangeInfoDto.setIsHidden(model.getIdentity().intValue());
        PageInfo pageInfo = memberExchangeInfoApi.getMemberExchangeDetailPage(memberExchangeInfoDto);
        return new ResponseEntity<ResultVO>(new ResultVO(pageInfo), HttpStatus.OK);
    }

    @ApiOperation(value = "总部端:积分变更记录导出", notes = "总部端:积分变更记录导出")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = com.xyy.saas.common.util.ResultVO.class) })
    @RequestMapping(value = "/exportProductHistory",  method = RequestMethod.POST)
    public void exportRecordExcel(@RequestHeader("commonRequestModel") String commonRequestModel, HttpServletResponse response, HttpServletRequest request,
                                  @RequestBody MemberPointHistoryConditionDto vo){
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        vo.setHeadquartersOrganSign(model.getOrganSign());
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(200);
        PageInfo result = memberPointHistoryForZBApi.getMemberPointHistoryPagerZB(vo, pageInfo);
        List<MemberPointHistoryConditionDto> pointHistoryList = new ArrayList<>();
        if (result != null && CollectionUtils.isNotEmpty(result.getList())) {
            pointHistoryList.addAll(result.getList());
            int firstPage = 1;
            int pages = result.getPages();
            /** 遍历查询数据 */
            while (firstPage < pages) {
                firstPage += 1;
                pageInfo.setPageNum(firstPage);
                PageInfo pointListPager = memberPointHistoryForZBApi.getMemberPointHistoryPagerZB(vo, pageInfo);
                pointHistoryList.addAll(pointListPager.getList());
            }
        }
        //List<MemberPointHistoryConditionDto> list = memberPointHistoryForZBApi.getHistoryListByReportZB(vo);
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String extfilename="积分变更记录"+df.format(new Date())+".xls";
        String sheetName = "积分变更记录";
        String headers[] = new String[]{"单据编号","会员姓名","会员卡号","会员手机号","来源门店","积分变动","本期结转积分",
                "交易门店","门店编码","变更类型","变更时间","变更原因","操作人"};
        String fieldNames[] = new String[]{"businessNo","memberName","cartNo","telephone","sourceOrganSignName","operatorPoint","currentPoint",
                "organSignName","organSign","operatorTypeDesc","createTime","editReason", "createUser"};
        try {
            ExportExcelUtil.createExcel(response,request,extfilename,sheetName, headers, fieldNames, pointHistoryList,true);
        } catch (Exception e) {
            logger.error("MemberPointHistoryZBApiController exportRecordExcel is error.", e);
        }
    }
}