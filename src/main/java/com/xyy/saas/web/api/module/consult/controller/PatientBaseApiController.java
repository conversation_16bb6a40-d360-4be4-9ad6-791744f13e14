package com.xyy.saas.web.api.module.consult.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.consult.cores.api.PatientBaseApi;
import com.xyy.saas.consult.cores.dto.PatientBaseDto;
import com.xyy.saas.consult.cores.dto.PatientBaseQueryDto;
import com.xyy.saas.web.api.module.consult.model.PatientBaseQueryVo;
import com.xyy.saas.web.api.module.consult.model.PatientBaseVo;
import io.swagger.annotations.*;
import org.apache.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/member/patientBase")
@Api(value = "patientBase", description = "患者API")
public class PatientBaseApiController {

	private static final Logger logger = Logger.getLogger(PatientBaseApiController.class);
    @Reference( version = "0.0.2")
    private PatientBaseApi patientBaseApi;


    @ApiOperation(value = "新增患者", notes = "返回码解释：0：保存成功", response = Boolean.class, tags = {"patientBase",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/insert", method = RequestMethod.POST,produces =  "application/json",
            consumes = "application/json")
    ResponseEntity<ResultVO> insert(@RequestHeader(name = "organSign", required = true) String organSign,
                                    @ApiParam(value = "患者信息", required = true) @RequestBody PatientBaseVo patientBaseVo) {
        if (StringUtils.isEmpty(patientBaseVo.getName())) {
            return new ResponseEntity(new ResultVO(-1, "参数name不能为空", false), HttpStatus.OK);
        }
        if (patientBaseVo.getSex() == null) {
            patientBaseVo.setSex((byte) 1);
        }
        if (patientBaseVo.getBirthday() == null) {
            return new ResponseEntity(new ResultVO(-1, "参数birthday不能为空", false), HttpStatus.OK);
        }
        if (StringUtils.isEmpty(patientBaseVo.getName())) {
            return new ResponseEntity(new ResultVO(-1, "参数name不能为空", false), HttpStatus.OK);
        }
        if (StringUtils.isEmpty(patientBaseVo.getTelephone())) {
            return new ResponseEntity(new ResultVO(-1, "参数telephone不能为空", false), HttpStatus.OK);
        }
        if (!patientBaseVo.getTelephone().replaceAll("1[0-9]{10}", "").equals("")) {
            return new ResponseEntity(new ResultVO(-1, "参数telephone不合法", false), HttpStatus.OK);
        }
        if (StringUtils.isEmpty(patientBaseVo.getCreateUser())) {
            return new ResponseEntity(new ResultVO(-1, "参数createUser不能为空", false), HttpStatus.OK);
        }
        if (patientBaseVo.getMarried() == null) {
            patientBaseVo.setMarried((byte) 0);
        }
        if (patientBaseVo.getAllergy() == null) {
            patientBaseVo.setMarried((byte) 0);
        }
        PatientBaseDto dto = new PatientBaseDto();
        BeanUtils.copyProperties(patientBaseVo, dto);
        dto.setOrganSign(organSign);
        return new ResponseEntity(ResultVO.createSuccess(patientBaseApi.insert(dto)), HttpStatus.OK);
    }

    @ApiOperation(value = "根据主键查询患者", notes = "根据主键查询患者", response = PatientBaseDto.class, tags = {"patientBase",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = PatientBaseDto.class)})
    @RequestMapping(value = "/selectById", method = RequestMethod.POST,produces =  "application/json",consumes = "application/json")
    ResponseEntity<ResultVO> selectById(@RequestHeader(name = "organSign", required = true) String organSign,
                                        @ApiParam(value = "主键", required = true) @RequestBody PatientBaseVo patientBaseVo) {
        Long id = patientBaseVo.getId();
        if (id == null || id <= 0) {
            return new ResponseEntity(new ResultVO(-1, "参数id不能为空", false), HttpStatus.OK);
        }
        PatientBaseDto dto = patientBaseApi.selectById(id, organSign);
        return new ResponseEntity(ResultVO.createSuccess(dto), HttpStatus.OK);
    }

    @ApiOperation(value = "根据主键修改患者信息", notes = "根据主键修改患者信息", response = Boolean.class, tags = {"patientBase",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/updateById", method = RequestMethod.POST,produces =  "application/json",
            consumes = "application/json")
    ResponseEntity<ResultVO> updateById(@RequestHeader(name = "organSign", required = true) String organSign,
                                        @ApiParam(value = "患者信息", required = true) @RequestBody PatientBaseVo patientBaseVo) {
        if (patientBaseVo.getId() == null || patientBaseVo.getId() == null) {
            return new ResponseEntity(new ResultVO(-1, "参数id不能为空", false), HttpStatus.OK);
        }
        if (StringUtils.isEmpty(patientBaseVo.getUpdateUser())) {
            return new ResponseEntity(new ResultVO(-1, "参数updateUser不能为空", false), HttpStatus.OK);
        }
        PatientBaseDto dto = new PatientBaseDto();
        BeanUtils.copyProperties(patientBaseVo, dto);
        dto.setOrganSign(organSign);
        return new ResponseEntity(ResultVO.createSuccess(patientBaseApi.updateById(dto)), HttpStatus.OK);
    }

    @ApiOperation(value = "根据查询条件查询患者列表", notes = "根据查询条件查询患者列表", response = JSONObject.class, tags = {"patientBase",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = JSONObject.class)})
    @RequestMapping(value = "/selectByQuery", method = RequestMethod.POST,produces =  "application/json",consumes = "application/json")
    ResponseEntity<ResultVO> selectByQuery(@RequestHeader(name = "organSign", required = true) String organSign,
                                           @ApiParam(value = "查询条件", required = true) @RequestBody PatientBaseQueryVo query) {
        PatientBaseQueryDto dto = new PatientBaseQueryDto();
        BeanUtils.copyProperties(query, dto);
        dto.setOrganSign(organSign);
        return new ResponseEntity(ResultVO.createSuccess(patientBaseApi.selectListByQuery(dto)), HttpStatus.OK);
    }

}
