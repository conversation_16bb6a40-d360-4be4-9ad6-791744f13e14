package com.xyy.saas.web.api.module.consult.model;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 远程问诊记录查询条件Vo
 * <AUTHOR>
 */
@ApiModel(description = "远程问诊记录查询条件")
public class ConsultRecordQueryVo extends PageInfo<ConsultRecordQueryVo> implements Serializable {

    private static final long serialVersionUID = -6337019077332959645L;

    /**
     * 患者信息
     */
    @ApiModelProperty(value = "患者信息")
    private String mixedQuery;

    /**
     * 查询日期
     */
    @ApiModelProperty(value = "查询日期")
    private String queryDate;

    /**
     * 查询开始时间
     */
    @ApiModelProperty(value = "查询开始时间")
    private Date startTime;

    /**
     * 查询结束时间
     */
    @ApiModelProperty(value = "查询结束时间")
    private Date endTime;

    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识")
    private String organSign;

    /**
     * 问诊状态 0-待问诊 1-问诊完成
     */
    @ApiModelProperty(value = "问诊状态 0-待问诊 1-问诊完成")
    private Byte isEnd;

    /**
     * 远程问诊记录guid
     */
    @ApiModelProperty(value = "远程问诊记录guid")
    private String guid;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 性别 1-男 2-女
     */
    @ApiModelProperty(value = "性别 1-男 2-女")
    private Byte sex;

    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码")
    private String telephone;

    /**
     * 婚否 0-未婚 1-已婚
     */
    @ApiModelProperty(value = "婚否 0-未婚 1-已婚")
    private Byte married;

    /**
     * 过敏史 0-无 1-有
     */
    @ApiModelProperty(value = "过敏史 0-无 1-有")
    private Byte allergy;

    /**
     * 处方状态  1-未审方 2-已审方
     */
    @ApiModelProperty(value = "处方状态  1-未审方 2-已审方")
    private Byte prescriptionStatus;

    /**
     * 处方过期时间
     */
    @ApiModelProperty(value = "处方过期时间")
    private Date expiredTime;

    /**
     * 患者id
     */
    @ApiModelProperty(value = "患者id")
    private Long patientId;

    /**
     * 是否开具处方 0-否 1-是
     */
    @ApiModelProperty(value = "是否开具处方 0-否 1-是")
    private Integer hasPrescription;

    /**
     * 是否被调取过 0-否 1-是
     */
    @ApiModelProperty(value = "是否被调取过 0-否 1-是")
    private Byte used;

    /**
     * 排序方式 1-时间的倒序 2-姓名拼音正序
     */
    @ApiModelProperty(value = "排序方式 1-时间的倒序 2-姓名拼音正序")
    private Byte sort;

    /**
     * 每页条数
     */
    @ApiModelProperty(value = "每页条数")
    private Integer rows;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    private Integer page;

    public String getMixedQuery() {
        return mixedQuery;
    }

    public void setMixedQuery(String mixedQuery) {
        this.mixedQuery = mixedQuery;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getQueryDate() {
        return queryDate;
    }

    public void setQueryDate(String queryDate) {
        this.queryDate = queryDate;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public Byte getIsEnd() {
        return isEnd;
    }

    public void setIsEnd(Byte isEnd) {
        this.isEnd = isEnd;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Byte getSex() {
        return sex;
    }

    public void setSex(Byte sex) {
        this.sex = sex;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public Byte getMarried() {
        return married;
    }

    public void setMarried(Byte married) {
        this.married = married;
    }

    public Byte getAllergy() {
        return allergy;
    }

    public void setAllergy(Byte allergy) {
        this.allergy = allergy;
    }

    public Byte getPrescriptionStatus() {
        return prescriptionStatus;
    }

    public void setPrescriptionStatus(Byte prescriptionStatus) {
        this.prescriptionStatus = prescriptionStatus;
    }

    public Date getExpiredTime() {
        return expiredTime;
    }

    public void setExpiredTime(Date expiredTime) {
        this.expiredTime = expiredTime;
    }

    public Long getPatientId() {
        return patientId;
    }

    public void setPatientId(Long patientId) {
        this.patientId = patientId;
    }

    public Integer getHasPrescription() {
        return hasPrescription;
    }

    public void setHasPrescription(Integer hasPrescription) {
        this.hasPrescription = hasPrescription;
    }

    public Byte getUsed() {
        return used;
    }

    public void setUsed(Byte used) {
        this.used = used;
    }

    public Byte getSort() {
        return sort;
    }

    public void setSort(Byte sort) {
        this.sort = sort;
    }

    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    @Override
    public String toString() {
        return "ConsultRecordQueryVo{" +
                "mixedQuery='" + mixedQuery + '\'' +
                ", queryDate='" + queryDate + '\'' +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", organSign='" + organSign + '\'' +
                ", isEnd=" + isEnd +
                ", guid='" + guid + '\'' +
                ", name='" + name + '\'' +
                ", sex=" + sex +
                ", telephone='" + telephone + '\'' +
                ", married=" + married +
                ", allergy=" + allergy +
                ", prescriptionStatus=" + prescriptionStatus +
                ", expiredTime=" + expiredTime +
                ", patientId=" + patientId +
                ", hasPrescription=" + hasPrescription +
                ", used=" + used +
                ", sort=" + sort +
                ", rows=" + rows +
                ", page=" + page +
                '}';
    }
}