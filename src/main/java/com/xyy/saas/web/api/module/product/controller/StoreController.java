package com.xyy.saas.web.api.module.product.controller;


import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.jm.common.result.ResultVO;
import com.xyy.saas.jm.foundation.core.api.StoreApi;
import com.xyy.saas.jm.foundation.core.vo.LoginVo;
import com.xyy.saas.web.api.module.product.model.StoreVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@Api(tags = {"基础服务-门店服务"}, description = "StoreController", produces = MediaType.APPLICATION_JSON_VALUE)
@RestController
@RequestMapping("/product/foundation/store")
public class StoreController {

    private static final Logger logger = LoggerFactory.getLogger(StoreController.class);

    @Reference(version = "0.0.1")
    private StoreApi storeApi;

    /**
     * 根据主体信息获取分店列表
     * @return
     * @throws Exception
     */
    @ApiOperation(value="根据主体信息获取分店列表", notes="根据主体信息获取分店列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = LoginVo.class) })
    @RequestMapping(value = "/getBranchStoreList",method = RequestMethod.POST)
    public ResultVO getBranchStoreList(@RequestBody StoreVo storeVo) throws Exception{
        logger.info("请求获取门店列表{}",JSONObject.toJSONString(storeVo));
        ResultVO resultVO = storeApi.getBranchStoreList(storeVo.getMainStoreGuid());
        logger.info("获取结果："+resultVO.getCode()+","+resultVO.getMsg());
        return resultVO;
    }

    /**
     * 根据单体信息获取分店列表
     * @return
     * @throws Exception
     */
    @ApiOperation(value="根据单体信息获取分店列表", notes="根据单体信息获取分店列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = LoginVo.class) })
    @RequestMapping(value = "/getBranchStoreListByMonomer",method = RequestMethod.POST)
    public ResultVO getBranchStoreListByMonomer(@RequestHeader(name = "organSign", required = true) String organSign) throws Exception{
        logger.info("请求获取门店列表{}",organSign);
        ResultVO resultVO = storeApi.getBranchStoreListByMonomer(organSign);
        logger.info("获取结果："+resultVO.getCode()+","+resultVO.getMsg());
        return resultVO;
    }


    /**
     * 判断门店数量是否超额
     * @return
     * @throws Exception
     */
    @ApiOperation(value="门店数量是否超额", notes="门店数量是否超额")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = LoginVo.class) })
    @RequestMapping(value = "/isBeyondQuota",method = RequestMethod.POST)
    public ResultVO isBeyondQuota(@RequestBody StoreVo storeVo) throws Exception{

        logger.info("判断门店数量是否超额入参：{}",JSONObject.toJSONString(storeVo));
        ResultVO resultVO = storeApi.isBeyondQuota(storeVo.getMainStoreGuid());
        logger.info("获取结果："+resultVO.getCode()+","+resultVO.getMsg());
        return resultVO;
    }


    /**
     * 根据主店guid获取剩余门店信息
     * @return
     * @throws Exception
     */
    @ApiOperation(value="根据主店guid获取剩余门店信息", notes="根据主店guid获取剩余门店信息")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = LoginVo.class) })
    @RequestMapping(value = "/getSurplusStoreCountInfo",method = RequestMethod.POST)
    public ResultVO getSurplusStoreCountInfo(@RequestBody StoreVo storeVo) throws Exception{

        logger.info("请求获取门店列表：{}",JSONObject.toJSONString(storeVo));
        ResultVO resultVO = storeApi.getSurplusStoreCountInfo(storeVo.getMainStoreGuid());
        logger.info("获取结果："+resultVO.getCode()+","+resultVO.getMsg());
        return resultVO;
    }



}
