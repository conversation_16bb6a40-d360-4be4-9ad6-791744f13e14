package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Classname SupplierCount
 * @Description TODO
 * @Date 2020/6/10 18:01
 */
@Data
@ToString
@ApiModel(description = "供应商资质信息数量")
public class SupplierCount {

    @ApiModelProperty(value = "近效期总数")
    @JsonProperty("effectiveCount")
    private Integer effectiveCount;

    @ApiModelProperty(value = "过期总数")
    @JsonProperty("expireCount")
    private Integer expireCount;
}
