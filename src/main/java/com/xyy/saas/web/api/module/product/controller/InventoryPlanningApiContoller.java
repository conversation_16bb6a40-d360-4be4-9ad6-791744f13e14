package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.inventory.core.PlanningMobileState;
import com.xyy.saas.inventory.core.dto.InventoryPlanDetailVo;
import com.xyy.saas.inventory.core.dto.InventoryPlanVo;
import com.xyy.saas.inventory.core.dto.InventoryPlanningSumVo;
import com.xyy.saas.product.core.dto.ResultCodeEnum;
import com.xyy.saas.web.api.module.product.model.InventoryPlanningInProgresVo;
import com.xyy.saas.web.api.module.product.util.DateUtil;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-01-28T15:28:18.933+08:00")
@Controller
public class InventoryPlanningApiContoller implements InventoryPlanningApi {

    private static final Logger logger = LoggerFactory.getLogger(InventoryPlanningApiContoller.class);

    @Reference(version = "0.0.1")
    private com.xyy.saas.inventory.core.api.InventoryPlanApi inventoryPlanApi;
    @Reference(version = "0.0.1")
    private com.xyy.saas.inventory.core.api.InventoryPlanDetailApi inventoryPlanDetailApi;


    @Override
    public ResponseEntity<ResultVO> findInventoryPlanningByPage(@ApiParam(value = "盘点计划单信息" ,required=true ) @RequestBody InventoryPlanVo vo) {

	    return new ResponseEntity<ResultVO>(new ResultVO(1,"APP版本过低，请在智慧脸官网下载最新版本",false), HttpStatus.OK);
//        if (StringUtils.isEmpty(vo.getOrgansign())){
//            logger.info("方法InventoryPlanningApiContoller->findInventoryPlanningByPage：关键入参organsign不存在");
//            return new ResponseEntity<ResultVO>(new ResultVO(1,"关键入参organsign不存在",false), HttpStatus.OK);
//        }
//        if (StringUtils.isEmpty(vo.getPageSize())) {
//            vo.setPageSize(20);
//        }
//        if (StringUtils.isEmpty(vo.getPageNum())) {
//            vo.setPageNum(1);
//        }
//        if (StringUtils.isEmpty(vo.getOrgansign())){
//            return new ResponseEntity<ResultVO>(new ResultVO(1,"关键入参不存在",false), HttpStatus.OK);
//        }
//        PageInfo byParams = new PageInfo();
//        try {
//            byParams = inventoryPlanApi.findMobilePageParams2(dealWithDateTime(vo));
//        }catch (Exception e){
//            logger.error("分页查询盘点计划单有误",e);
//            ResultVO<Boolean> resultVO = new ResultVO();
//            resultVO.setCode(ResultCodeEnum.ERROR.getCode());
//            resultVO.setMsg("分页查询盘点计划单有误");
//            resultVO.setResult(false);
//            return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
//        }
//        ResultVO<PageInfo> resultVO = new ResultVO();
//        resultVO.setCode(ResultCodeEnum.SUCCESS.getCode());
//        resultVO.setMsg(ResultCodeEnum.SUCCESS.getMsg());
//        resultVO.setResult(byParams);
//        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> findInventoryPlanningSum(@ApiParam(value = "盘点计划单信息" ,required=true ) @RequestBody InventoryPlanVo vo) {
        if (StringUtils.isEmpty(vo.getOrgansign())){
            logger.info("方法InventoryPlanningApiContoller->findInventoryPlanningSum：关键入参organsign不存在");
            return new ResponseEntity<ResultVO>(new ResultVO(1,"关键入参organsign不存在",false), HttpStatus.OK);
        }
        vo.setFlag(0);
        vo.setIsProductHidden((byte)0);
        InventoryPlanningSumVo inventoryPlanningSumVo = new InventoryPlanningSumVo();
        try {
            inventoryPlanningSumVo=inventoryPlanApi.findInventoryPlanningSum(dealWithDateTime(vo));
        }catch (Exception e){
            logger.error("查询盘点计划单汇总出现错误！",e);
            ResultVO<Boolean> resultVO = new ResultVO();
            resultVO.setCode(ResultCodeEnum.ERROR.getCode());
            resultVO.setMsg("查询盘点计划单汇总出现错误！");
            resultVO.setResult(false);
            return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
        }
        ResultVO<InventoryPlanningSumVo> resultVO = new ResultVO();
        resultVO.setCode(ResultCodeEnum.SUCCESS.getCode());
        resultVO.setMsg(ResultCodeEnum.SUCCESS.getMsg());
        resultVO.setResult(inventoryPlanningSumVo);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> findInventoryPlanningDetail(@ApiParam(value = "盘点计划单信息" ,required=true )  @RequestBody InventoryPlanVo vo) {
        if (StringUtils.isEmpty(vo.getOrgansign())){
            logger.info("方法InventoryPlanningApiContoller->findInventoryPlanningDetail：关键入参organsign不存在");
            return new ResponseEntity<ResultVO>(new ResultVO(1,"关键入参organsign不存在",false), HttpStatus.OK);
        }
        if (StringUtils.isEmpty(vo.getPlanningPref())){
            logger.info("方法InventoryPlanningApiContoller->findInventoryPlanningDetail：关键入参planningPref不存在");
            return new ResponseEntity<ResultVO>(new ResultVO(1,"关键入参planningPref不存在",false), HttpStatus.OK);
        }
        if (StringUtils.isEmpty(vo.getInventoryType())){
            logger.info("方法InventoryPlanningApiContoller->findInventoryPlanningDetail：关键入参inventoryType不存在");
            return new ResponseEntity<ResultVO>(new ResultVO(1,"关键入参inventoryType不存在",false), HttpStatus.OK);
        }
        vo.setFlag(1);
        List<InventoryPlanDetailVo> planDetailVos = new ArrayList<>();
        try {
            planDetailVos = inventoryPlanDetailApi.findMobileDetail(vo);
        }catch (Exception e){
            logger.error("查询盘点计划单详情出现错误！",e);
            ResultVO<Boolean> resultVO = new ResultVO();
            resultVO.setCode(ResultCodeEnum.ERROR.getCode());
            resultVO.setMsg("查询盘点计划单详情出现错误！");
            resultVO.setResult(false);
            return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
        }
        ResultVO<List<InventoryPlanDetailVo>> resultVO = new ResultVO();
        resultVO.setCode(ResultCodeEnum.SUCCESS.getCode());
        resultVO.setMsg(ResultCodeEnum.SUCCESS.getMsg());
        resultVO.setResult(planDetailVos);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> findInventoryPlanProduct(@ApiParam(value = "盘点计划单详情信息" ,required=true ) @RequestBody InventoryPlanDetailVo detailVo) {
	    return new ResponseEntity<ResultVO>(new ResultVO(1,"APP版本过低，请在智慧脸官网下载最新版本",false), HttpStatus.OK);

    	//        if (StringUtils.isEmpty(detailVo.getOrgansign())){
//            logger.info("方法InventoryPlanningApiContoller->findInventoryPlanProduct：关键入参organsign不存在");
//            return new ResponseEntity<ResultVO>(new ResultVO(1,"关键入参organsign不存在",false), HttpStatus.OK);
//        }
//        if (StringUtils.isEmpty(detailVo.getInventoryType())){
//            logger.info("方法InventoryPlanningApiContoller->findInventoryPlanProduct：关键入参inventoryType不存在");
//            return new ResponseEntity<ResultVO>(new ResultVO(1,"关键入参inventoryType不存在",false), HttpStatus.OK);
//        }
//        if (StringUtils.isEmpty(detailVo.getPageSize())) {
//            detailVo.setPageSize(20);
//        }
//        if (StringUtils.isEmpty(detailVo.getPageNum())) {
//            detailVo.setPageNum(1);
//        }
//        PageInfo inventorys = new PageInfo();
//        try{
//            inventorys = inventoryPlanDetailApi.findInventoryPlanProduct(detailVo);
//        }catch (Exception e){
//            logger.error("查询商品信息错误！",e);
//            ResultVO<Boolean> resultVO = new ResultVO();
//            resultVO.setCode(ResultCodeEnum.ERROR.getCode());
//            resultVO.setMsg("查询商品信息错误！");
//            resultVO.setResult(false);
//            return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
//        }
//        ResultVO<PageInfo> resultVO = new ResultVO();
//        resultVO.setCode(ResultCodeEnum.SUCCESS.getCode());
//        resultVO.setMsg(ResultCodeEnum.SUCCESS.getMsg());
//        resultVO.setResult(inventorys);
//        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> markPlanningInProgres(@ApiParam(value = "盘点计划单id" ,required=true )@RequestBody InventoryPlanningInProgresVo progresVo) {
        if (null==progresVo.getId()|| 0==progresVo.getId()){
            logger.info("方法InventoryPlanningApiContoller->markPlanningInProgres：关键入参id不存在");
            return new ResponseEntity<ResultVO>(new ResultVO(1,"关键入参id不存在",false), HttpStatus.OK);
        }
        Boolean res = false;
        Integer id = progresVo.getId();
        String guid = progresVo.getGuid();
        String organSign = progresVo.getOrganSign();
        try {
            res = inventoryPlanApi.changePlanningMobileState(guid, PlanningMobileState.PROCESSING.getCode(),organSign);
            logger.info("移动接口：修改盘点计划单移动端状态返回结果："+res);
        }catch (Exception e){
            logger.error("修改移动端状态失败！",e);
            ResultVO<Boolean> resultVO = new ResultVO();
            resultVO.setCode(ResultCodeEnum.ERROR.getCode());
            resultVO.setMsg("修改移动端状态失败！");
            resultVO.setResult(false);
            return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
        }
        ResultVO<Boolean> resultVO = new ResultVO();
        resultVO.setCode(ResultCodeEnum.SUCCESS.getCode());
        resultVO.setMsg(ResultCodeEnum.SUCCESS.getMsg());
        resultVO.setResult(true);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> findInventoryLotNumber(@ApiParam(value = "查询批号的条件" ,required=true ) @RequestBody InventoryPlanDetailVo detailVo) {
        /*非必须参数——planningPref*/
        if (StringUtils.isEmpty(detailVo.getOrgansign())){
            logger.info("方法InventoryPlanningApiContoller->findInventoryLotNumber：关键入参organsign不存在");
            return new ResponseEntity<ResultVO>(new ResultVO(1,"关键入参organsign不存在",false), HttpStatus.OK);
        }
        if (StringUtils.isEmpty(detailVo.getProductNumber())){
            logger.info("方法InventoryPlanningApiContoller->findInventoryLotNumber：关键入参productNumber不存在");
            return new ResponseEntity<ResultVO>(new ResultVO(1,"关键入参productNumber不存在",false), HttpStatus.OK);
        }
        detailVo.setInventoryType("0");
        List<String> logNumberList = new ArrayList<>();
        try{
            logNumberList=inventoryPlanDetailApi.findInventoryLotNumber(detailVo);
        }catch (Exception e){
            logger.error("查询商品批号失败！",e);
            ResultVO<Boolean> resultVO = new ResultVO();
            resultVO.setCode(ResultCodeEnum.ERROR.getCode());
            resultVO.setMsg("查询商品批号失败！");
            resultVO.setResult(false);
            return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
        }
        ResultVO<List<String>> resultVO = new ResultVO();
        resultVO.setCode(ResultCodeEnum.SUCCESS.getCode());
        resultVO.setMsg(ResultCodeEnum.SUCCESS.getMsg());
        resultVO.setResult(logNumberList);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    /**
     * 把timeType转换为开始结束时间-2019年1月30日20:19:10
     * @param vo 转换的实体
     * @return
     */
    private InventoryPlanVo dealWithDateTime(InventoryPlanVo vo){
        if (null!=vo){
            if (!StringUtils.isEmpty(vo.getTimeType())){
                if ("1".equals(vo.getTimeType())){
                    //当天
                    vo.setStartDte(DateUtil.getBeforeDayStartTime(new Date(),0,0,0));
                    vo.setEndDate(DateUtil.getBeforeDayEndTime(new Date(),0,0,0));
                } else if ("2".equals(vo.getTimeType())){
                    //近一周
                    vo.setStartDte(DateUtil.getBeforeDayStartTime(new Date(),0,0,-7));
                    vo.setEndDate(DateUtil.getBeforeDayEndTime(new Date(),0,0,0));
                } else if ("3".equals(vo.getTimeType())){
                    //近一月
                    vo.setStartDte(DateUtil.getBeforeDayStartTime(new Date(),0,-1,0));
                    vo.setEndDate(DateUtil.getBeforeDayEndTime(new Date(),0,0,0));
                } else if ("4".equals(vo.getTimeType())){
                    //近半年
                    vo.setStartDte(DateUtil.getBeforeDayStartTime(new Date(),0,-6,0));
                    vo.setEndDate(DateUtil.getBeforeDayEndTime(new Date(),0,0,0));
                }
            }
        }
        return vo;
    }
}
