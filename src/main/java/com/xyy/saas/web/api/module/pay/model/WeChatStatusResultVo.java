package com.xyy.saas.web.api.module.pay.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @FileName WeChatStatusResultVo
 * @Description 微信实名认证返回结果实体类
 * <AUTHOR>
 * @Date 2021/7/8 11:13
 * @Version v1.0
 */
@ApiModel(description = "微信商户认证结果确认二维码查询")
@Data
public class WeChatStatusResultVo implements Serializable {
    private static final long serialVersionUID = 8810455185712586952L;

    @ApiModelProperty(value = "乐刷商户编号")
    private String merchantId;

    /**
     * WAIT_FOR_UPLOAD_DATA: 等待上送资料
     * UPLOAD_DATA_FAIL: 上送资料失败
     * APPLYMENT_STATE_WAITTING_FOR_AUDIT: 微信审核中
     * APPLYMENT_STATE_WAITTING_FOR_CONFIRM_CONTACT: 待确认联系信息
     * APPLYMENT_STATE_WAITTING_FOR_CONFIRM_LEGALPERSON: 待账户验证
     * APPLYMENT_STATE_PASSED: 审核通过
     * APPLYMENT_STATE_REJECTED: 审核驳回
     * APPLYMENT_STATE_FREEZED: 已冻结
     * APPLYMENT_STATE_CANCELED: 已作废
     */
    @ApiModelProperty(value = "申请状态")
    private String applyStatus;

    /**
     * 当applyStatus为
     * APPLYMENT_STATE_WAITTING_FOR_CONFIRM_CONTACT 待确认联系信息
     * APPLYMENT_STATE_WAITTING_FOR_CONFIRM_LEGALPERSON 待账户验证
     * APPLYMENT_STATE_PASSED 审核通过
     * APPLYMENT_STATE_FREEZED 已冻结
     * 时会返回
     */
    @ApiModelProperty(value = "确认二维码临时路径")
    private String qrcodeData;

    /**
     * 当applyStatus为APPLYMENT_STATE_REJECTED返回，显示具体的驳回字段
     */
    @ApiModelProperty(value = "驳回字段")
    private String rejectParam;

    /**
     * 当applyStatus为APPLYMENT_STATE_REJECTED返回，显示具体的驳回原因
     */
    @ApiModelProperty(value = "驳回原因")
    private String rejectReason;
}
