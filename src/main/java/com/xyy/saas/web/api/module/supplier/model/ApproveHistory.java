package com.xyy.saas.web.api.module.supplier.model;

import com.xyy.saas.supplier.dto.ApproveHistoryDetailDto;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Classname ApproveHistory
 * @Description TODO
 * @Date 2020/6/4 14:18
 */
@Data
@ToString
public class ApproveHistory extends Page implements Serializable {
    private Long id;
    private String gspBusinessNo;
    private String businessNo;
    private String billNo;
    private Integer type;
    private String msg;
    private String username;
    private Integer status;
    private String initiatorName;
    private Date createTime;
    private String createUser;
    private Date updateTime;
    private String updateUser;
    private Byte yn;
    private String organSign;
    private String baseVersion;
    private Date startDate;
    private Date endDate;
    private String startDateStr;
    private String endDateStr;
    private String ids;
    private Byte isProductHidden;
    private String guid;
    private String guids;
    private List<ApproveHistoryDetailDto> detailList = new ArrayList();
    private Long approveUserRoleId;
    private List<Integer> statusList;
}
