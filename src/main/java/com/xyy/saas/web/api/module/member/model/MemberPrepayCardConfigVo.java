package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 会员储值设置Vo
 * <AUTHOR>
 */
@ApiModel(description = "会员储值设置")
public class MemberPrepayCardConfigVo implements Serializable {

    private static final long serialVersionUID = -8656571670528974845L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", example = "362")
    private Long id;

    /**
     * 会员等级id
     */
    @ApiModelProperty(value = "会员等级id", required = true, example = "102")
    private Long memberLevelId;

    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识", hidden = true)
    private String organsign;

    /**
     * 区间下限
     */
    @ApiModelProperty(value = "区间下限", required = true, example = "1000")
    private BigDecimal minAmount;

    /**
     * 区间上限
     */
    @ApiModelProperty(value = "区间下限", example = "2000")
    private BigDecimal maxAmount;

    /**
     * 赠送比例
     */
    @ApiModelProperty(value = "赠送比例", example = "20.12")
    private BigDecimal bonusRate;

    /**
     * 固定赠送金额
     */
    @ApiModelProperty(value = "固定赠送金额", example = "500")
    private BigDecimal bonusAmount;

    /**
     * 模式 1-按比例 2-按固定金额
     */
    @ApiModelProperty(value = "模式 1-按比例 2-按固定金额", required = true, example = "1")
    private Byte mode;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", hidden = true)
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", hidden = true)
    private String createUser;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间", hidden = true)
    private Date updateTime;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人", hidden = true)
    private String updateUser;

    /**
     * 是否删除 1-有效 0-删除
     */
    @ApiModelProperty(value = "是否删除 1-有效 0-删除", hidden = true)
    private Byte yn;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMemberLevelId() {
        return memberLevelId;
    }

    public void setMemberLevelId(Long memberLevelId) {
        this.memberLevelId = memberLevelId;
    }

    public String getOrgansign() {
        return organsign;
    }

    public void setOrgansign(String organsign) {
        this.organsign = organsign;
    }

    public BigDecimal getMinAmount() {
        return minAmount;
    }

    public void setMinAmount(BigDecimal minAmount) {
        this.minAmount = minAmount;
    }

    public BigDecimal getMaxAmount() {
        return maxAmount;
    }

    public void setMaxAmount(BigDecimal maxAmount) {
        this.maxAmount = maxAmount;
    }

    public BigDecimal getBonusRate() {
        return bonusRate;
    }

    public void setBonusRate(BigDecimal bonusRate) {
        this.bonusRate = bonusRate;
    }

    public BigDecimal getBonusAmount() {
        return bonusAmount;
    }

    public void setBonusAmount(BigDecimal bonusAmount) {
        this.bonusAmount = bonusAmount;
    }

    public Byte getMode() {
        return mode;
    }

    public void setMode(Byte mode) {
        this.mode = mode;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Byte getYn() {
        return yn;
    }

    public void setYn(Byte yn) {
        this.yn = yn;
    }

    @Override
    public String toString() {
        return "MemberPrepayCardConfig{" +
                "id=" + id +
                ", memberLevelId=" + memberLevelId +
                ", organsign='" + organsign + '\'' +
                ", minAmount=" + minAmount +
                ", maxAmount=" + maxAmount +
                ", bonusRate=" + bonusRate +
                ", bonusAmount=" + bonusAmount +
                ", mode=" + mode +
                ", createTime=" + createTime +
                ", createUser='" + createUser + '\'' +
                ", updateTime=" + updateTime +
                ", updateUser='" + updateUser + '\'' +
                ", yn=" + yn +
                '}';
    }
}