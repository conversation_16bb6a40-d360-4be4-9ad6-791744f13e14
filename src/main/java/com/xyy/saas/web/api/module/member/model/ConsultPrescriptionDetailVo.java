package com.xyy.saas.web.api.module.member.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 远程问诊第三方处方药品明细Vo
 * <AUTHOR>
 */
public class ConsultPrescriptionDetailVo implements Serializable {

    private static final long serialVersionUID = 910482972912480695L;
    /**
     * 主键
     */
    @JSONField(serialize = false)
    private Long id;

    /**
     * 远程问诊id
     */
    @JSONField(serialize = false)
    private Long consultRecordId;

    /**
     * 处方id
     */
    @JSONField(serialize = false)
    private Long prescriptionId;

    /**
     * 第三方药品编号
     */
    @JsonProperty(value = "drug_code")
    @JSONField(name = "drug_code")
    private String drugCode;

    /**
     * 标准库id
     */
    @JSONField(serialize = false)
    private String standardLibraryId;

    /**
     * 药店药品编号
     */
    @JSONField(serialize = false)
    private String productPref;

    /**
     * 药店药品编号
     */
    @JSONField(serialize = false)
    private String productId;

    /**
     * 药品名称
     */
    @JsonProperty(value = "drug_name")
    @JSONField(name = "drug_name")
    private String productName;

    /**
     * 单位
     */
    @JsonProperty(value = "unit")
    private String unit;

    /**
     * 数量
     */
    @JsonProperty(value = "quantity")
    private BigDecimal quantity;

    /**
     * 单价
     */
    @JsonProperty(value = "unit_price")
    @JSONField(name = "unit_price")
    private BigDecimal price;

    /**
     * 规格
     */
    @JsonProperty(value = "specification")
    @JSONField(name = "specification")
    private String attributeSpecification;

    /**
     * 生产厂商
     */
    @JSONField(serialize = false)
    private String manufacturer;

    /**
     * 批准文号
     */
    @JSONField(serialize = false)
    private String approvalNumber;

    /**
     * 机构标识
     */
    @JSONField(serialize = false)
    private String organSign;

    /**
     * 逻辑删除 1-有效 0-删除
     */
    @JSONField(serialize = false)
    private Byte yn;

    /**
     * 匹配完成 0-否 1-是
     */
    @JSONField(serialize = false)
    private Byte checked;

    /**
     * 创建时间
     */
    @JSONField(serialize = false)
    private Date createTime;

    /**
     * 创建人
     */
    @JSONField(serialize = false)
    private String createUser;

    /**
     * 更新时间
     */
    @JSONField(serialize = false)
    private Date updateTime;

    /**
     * 更新人
     */
    @JSONField(serialize = false)
    private String updateUser;

    /**
     * 用法
     */
    @JsonProperty(value = "usage")
    private String usage;

    /**
     * 用量
     */
    @JsonProperty(value = "dosage_info")
    @JSONField(name = "dosage_info")
    private String dosageInfo;

    /**
     * 频次
     */
    @JsonProperty(value = "frequency")
    private String frequency;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getConsultRecordId() {
        return consultRecordId;
    }

    public void setConsultRecordId(Long consultRecordId) {
        this.consultRecordId = consultRecordId;
    }

    public Long getPrescriptionId() {
        return prescriptionId;
    }

    public void setPrescriptionId(Long prescriptionId) {
        this.prescriptionId = prescriptionId;
    }

    public String getDrugCode() {
        return drugCode;
    }

    public void setDrugCode(String drugCode) {
        this.drugCode = drugCode;
    }

    public String getStandardLibraryId() {
        return standardLibraryId;
    }

    public void setStandardLibraryId(String standardLibraryId) {
        this.standardLibraryId = standardLibraryId == null ? null : standardLibraryId.trim();
    }

    public String getProductPref() {
        return productPref;
    }

    public void setProductPref(String productPref) {
        this.productPref = productPref;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getAttributeSpecification() {
        return attributeSpecification;
    }

    public void setAttributeSpecification(String attributeSpecification) {
        this.attributeSpecification = attributeSpecification == null ? null : attributeSpecification.trim();
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer == null ? null : manufacturer.trim();
    }

    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber == null ? null : approvalNumber.trim();
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign == null ? null : organSign.trim();
    }

    public Byte getYn() {
        return yn;
    }

    public void setYn(Byte yn) {
        this.yn = yn;
    }

    public Byte getChecked() {
        return checked;
    }

    public void setChecked(Byte checked) {
        this.checked = checked;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = usage;
    }

    public String getDosageInfo() {
        return dosageInfo;
    }

    public void setDosageInfo(String dosageInfo) {
        this.dosageInfo = dosageInfo;
    }

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    @Override
    public String toString() {
        return "ConsultPrescriptionDetailVo{" +
                "id=" + id +
                ", consultRecordId=" + consultRecordId +
                ", prescriptionId=" + prescriptionId +
                ", drugCode='" + drugCode + '\'' +
                ", standardLibraryId='" + standardLibraryId + '\'' +
                ", productPref='" + productPref + '\'' +
                ", productId='" + productId + '\'' +
                ", productName='" + productName + '\'' +
                ", unit='" + unit + '\'' +
                ", quantity=" + quantity +
                ", price=" + price +
                ", attributeSpecification='" + attributeSpecification + '\'' +
                ", manufacturer='" + manufacturer + '\'' +
                ", approvalNumber='" + approvalNumber + '\'' +
                ", organSign='" + organSign + '\'' +
                ", yn=" + yn +
                ", checked=" + checked +
                ", createTime=" + createTime +
                ", createUser='" + createUser + '\'' +
                ", updateTime=" + updateTime +
                ", updateUser='" + updateUser + '\'' +
                ", usage='" + usage + '\'' +
                ", dosageInfo='" + dosageInfo + '\'' +
                ", frequency='" + frequency + '\'' +
                '}';
    }
}