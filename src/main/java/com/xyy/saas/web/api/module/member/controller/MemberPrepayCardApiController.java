package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Maps;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.DateUtil;
import com.xyy.saas.encryption.cores.enums.DataTypeEnum;
import com.xyy.saas.member.core.api.DataTokenFlushApi;
import com.xyy.saas.member.core.api.MemberPrepayCardApi;
import com.xyy.saas.member.core.api.MemberShareApi;
import com.xyy.saas.member.core.dto.*;
import com.xyy.saas.member.core.enums.MemberShareStatusEnum;
import com.xyy.saas.member.core.response.ResultVO;
import com.xyy.saas.web.api.module.member.model.*;
import com.xyy.saas.web.api.module.utils.ExportExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 会员储值门店端API
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/member/memberPrepayCard")
@Api(value = "memberPrepayCard", description = "会员储值门店端API")
public class MemberPrepayCardApiController {

    private static final Logger logger = LogManager.getLogger(MemberPrepayCardApiController.class);

    @Value("${ftp.serverName}")
    public String serverName;

    @Value("${ftp.port}")
    public String port;

    @Value("${ftp.username}")
    public String username;

    @Value("${ftp.password}")
    public String password;

    @Value("${ftp.remotePath}")
    public String remotePath;

    @Value("${ftp.downLoadUrl}")
    public String downLoadUrl;

    @Reference(version = "0.0.1")
    private MemberPrepayCardApi memberPrepayCardApi;

    @Reference(version = "0.0.1")
    private DataTokenFlushApi dataTokenService;

    @Reference(version = "0.0.1")
    private MemberShareApi memberShareApi;

    @ApiOperation(value = "充值", notes = "充值，如果是第一次充值会自动创建会员储值卡", response = Boolean.class, tags = {"会员储值门店端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/deposit", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> deposit(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                     @RequestBody MemberPrepayCardVo vo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        MemberPrepayCardDto dto = new MemberPrepayCardDto();
        BeanUtils.copyProperties(vo, dto);
        dto.setOrgansign(commonRequestModel.getOrganSign());
        dto.setCreateUser(commonRequestModel.getEmployeeId());
        return new ResponseEntity(memberPrepayCardApi.deposit(dto), HttpStatus.OK);
    }

    @ApiOperation(value = "充值（加密传输）", notes = "充值（加密传输），如果是第一次充值会自动创建会员储值卡  \n" +
            "加密步骤:  \n" +
            "1.对deposit接口的请求参数json格式化为字符串  \n" +
            "2.使用AES的CBC模式 PKCS5Padding填充方式加密上述字符串  \n" +
            "3.使用BASE64进行编码  \n", response = Boolean.class, tags = {"会员储值门店端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/depositV2", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> depositV2(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                       @RequestBody MemberPrepayCardEncryptDataVo encryptDataVo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String organSign = commonRequestModel.getOrganSign();
        String headquartersOrganSign = commonRequestModel.getHeadquartersOrganSign();
        encryptDataVo.setOrganSign(organSign);
        encryptDataVo.setHeadquartersOrganSign(headquartersOrganSign);
        MemberPrepayCardEncryptDataDto dto = new MemberPrepayCardEncryptDataDto();
        BeanUtils.copyProperties(encryptDataVo, dto);
        dto.setOrganSign(organSign);
        dto.setHeadquartersOrganSign(headquartersOrganSign);
        dto.setEmployeeId(commonRequestModel.getEmployeeId());
        return new ResponseEntity(memberPrepayCardApi.depositV2(dto), HttpStatus.OK);
    }

    @ApiOperation(value = "查看会员储值余额", notes = "查看会员储值余额", response = BigDecimal.class, tags = {"会员储值门店端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = BigDecimal.class)})
    @RequestMapping(value = "/getBalance", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> getBalance(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                        @RequestBody MemberPrepayCardDetailQueryVo vo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        vo.setOrgansign(commonRequestModel.getOrganSign());
        vo.setHeadquartersOrganSign(commonRequestModel.getHeadquartersOrganSign());
        MemberPrepayCardDto dto = new MemberPrepayCardDto();
        BeanUtils.copyProperties(vo, dto);
        return new ResponseEntity(memberPrepayCardApi.getBalance(dto), HttpStatus.OK);
    }

    @ApiOperation(value = "查看会员储值卡详情", notes = "查看会员储值卡详情", response = MemberPrepayCard.class, tags = {"会员储值门店端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberPrepayCard.class)})
    @RequestMapping(value = "/getPrepayCard", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> getPrepayCard(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                           @RequestBody MemberPrepayCardDetailQueryVo vo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        vo.setOrgansign(commonRequestModel.getOrganSign());
        vo.setHeadquartersOrganSign(commonRequestModel.getHeadquartersOrganSign());
        MemberPrepayCardDto dto = new MemberPrepayCardDto();
        BeanUtils.copyProperties(vo, dto);
        return new ResponseEntity(memberPrepayCardApi.selectMemberPrepayCardBalance(dto), HttpStatus.OK);
    }

    @ApiOperation(value = "储值退款", notes = "储值退款", response = Boolean.class, tags = {"会员储值门店端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/decreaseAmountAndBonus", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> decreaseAmountAndBonus(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                    @RequestBody MemberPrepayCardVo vo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        MemberPrepayCardDto dto = new MemberPrepayCardDto();
        BeanUtils.copyProperties(vo, dto);
        dto.setOrgansign(commonRequestModel.getOrganSign());
        dto.setUpdateUser(commonRequestModel.getEmployeeId());
        return new ResponseEntity(memberPrepayCardApi.decreaseAmountAndBonus(dto), HttpStatus.OK);
    }

    @ApiOperation(value = "储值退款（加密传输）", notes = "储值退款（加密传输）  \n" +
            "加密步骤:  \n" +
            "1.对decreaseAmountAndBonus接口的请求参数json格式化为字符串  \n" +
            "2.使用AES的CBC模式 PKCS5Padding填充方式加密上述字符串  \n" +
            "3.使用BASE64进行编码", response = Boolean.class, tags = {"会员储值门店端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/decreaseAmountAndBonusV2", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> decreaseAmountAndBonusV2(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                      @RequestBody MemberPrepayCardEncryptDataVo encryptDataVo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String organSign = commonRequestModel.getOrganSign();
        String headquartersOrganSign = commonRequestModel.getHeadquartersOrganSign();
        encryptDataVo.setOrganSign(organSign);
        encryptDataVo.setHeadquartersOrganSign(headquartersOrganSign);
        MemberPrepayCardEncryptDataDto dto = new MemberPrepayCardEncryptDataDto();
        BeanUtils.copyProperties(encryptDataVo, dto);
        dto.setOrganSign(organSign);
        dto.setHeadquartersOrganSign(headquartersOrganSign);
        dto.setEmployeeId(commonRequestModel.getEmployeeId());
        return new ResponseEntity(memberPrepayCardApi.decreaseAmountAndBonusV2(dto), HttpStatus.OK);
    }

    @ApiOperation(value = "使用储值卡支付", notes = "使用储值卡支付", response = Boolean.class, tags = {"会员储值门店端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/pay", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> pay(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                 @RequestBody MemberPrepayCardPayVo vo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        MemberPrepayCardPayDto dto = new MemberPrepayCardPayDto();
        BeanUtils.copyProperties(vo, dto);
        dto.setOrgansign(commonRequestModel.getOrganSign());
        dto.setCreateUser(commonRequestModel.getEmployeeId());
        return new ResponseEntity(memberPrepayCardApi.pay(dto), HttpStatus.OK);
    }

    @ApiOperation(value = "使用储值卡支付（加密传输）", notes = "使用储值卡支付（加密传输）  \n" +
            "加密步骤:  \n" +
            "1.对pay接口的请求参数json格式化为字符串  \n" +
            "2.使用AES的CBC模式 PKCS5Padding填充方式加密上述字符串  \n" +
            "3.使用BASE64进行编码", response = Boolean.class, tags = {"会员储值门店端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/payV2", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> payV2(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                   @RequestBody MemberPrepayCardEncryptDataVo encryptDataVo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String organSign = commonRequestModel.getOrganSign();
        String headquartersOrganSign = commonRequestModel.getHeadquartersOrganSign();
        encryptDataVo.setOrganSign(organSign);
        MemberPrepayCardEncryptDataDto dto = new MemberPrepayCardEncryptDataDto();
        BeanUtils.copyProperties(encryptDataVo, dto);
        dto.setOrganSign(organSign);
        dto.setEmployeeId(commonRequestModel.getEmployeeId());
        return new ResponseEntity(memberPrepayCardApi.payV2(dto), HttpStatus.OK);
    }

    @ApiOperation(value = "获取储值流水分页", notes = "获取储值流水分页", response = MemberPrepayCardHistory.class, tags = {"会员储值门店端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberPrepayCardHistory.class)})
    @RequestMapping(value = "/getHistoryPage", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> getHistoryPage(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                            @RequestBody MemberPrepayCardHistoryQueryVo vo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        MemberPrepayCardHistoryQueryDto dto = new MemberPrepayCardHistoryQueryDto();
        String organSign = commonRequestModel.getOrganSign();
        String headquartersOrganSign = commonRequestModel.getHeadquartersOrganSign();
        BeanUtils.copyProperties(vo, dto);
        // organSign查询条件，总部机构号从登录态中获取
        // 单体门店
        if (commonRequestModel.getBizModel() == 1) {
            dto.setOrgansign(organSign);
        }
        // 联营连锁门店
        else {
            dto.setHeadquartersOrganSign(headquartersOrganSign);
            if (StringUtils.isEmpty(vo.getOrgansign())){
                //查全部带上来源门店
                dto.setSourceOrganSign(organSign);
            }
            dto.setOrgansign(organSign);
//            if (StringUtils.isEmpty(vo.getOrgansign())){
//                List<MemberShareDto> shareRel = memberShareApi.selectSharedList(headquartersOrganSign).getResult();
//                List<String> shares = shareRel.stream().map(s -> s.getOrganSign()).collect(Collectors.toList());
//                if (CollectionUtils.isNotEmpty(shares) && shares.contains(organSign)){
//                    shares.add(headquartersOrganSign);
//                    //共享门店
//                    dto.setOrganList(shares);
//                }else {
//                    //不共享门店
//                    dto.setOrgansign(organSign);
//                }
//            }

        }
        return new ResponseEntity(memberPrepayCardApi.selectHistoryListByQuery(dto), HttpStatus.OK);
    }

    @ApiOperation(value = "计算会员储值流水汇总数据", notes = "计算会员储值流水汇总数据", response = MemberPrepayCardHistoryStatisticDto.class, tags = {"会员储值门店端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberPrepayCardHistoryStatisticDto.class)})
    @RequestMapping(value = "/getHistoryStatData", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> getHistoryStatData(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                @RequestBody MemberPrepayCardHistoryQueryVo vo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        MemberPrepayCardHistoryQueryDto dto = new MemberPrepayCardHistoryQueryDto();
        String organSign = commonRequestModel.getOrganSign();
        String headquartersOrganSign = commonRequestModel.getHeadquartersOrganSign();
        BeanUtils.copyProperties(vo, dto);
        // organSign查询条件，总部机构号从登录态中获取
        // 单体门店
        if (commonRequestModel.getBizModel() == 1) {
            dto.setOrgansign(organSign);
        }
        // 联营连锁门店
        else {
            dto.setHeadquartersOrganSign(headquartersOrganSign);

            if (StringUtils.isEmpty(vo.getOrgansign())){
                List<MemberShareDto> shareRel = memberShareApi.selectSharedList(headquartersOrganSign).getResult();
                List<String> shares = shareRel.stream().map(s -> s.getOrganSign()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(shares) && shares.contains(organSign)){
                    shares.add(headquartersOrganSign);
                    //共享门店
                    dto.setOrganList(shares);
                }else {
                    //不共享门店
                    dto.setOrgansign(organSign);
                }
            }
        }
        return new ResponseEntity(memberPrepayCardApi.getHistoryStatData(dto), HttpStatus.OK);
    }

    @ApiOperation(value = "获取充值和退款汇总数据以及明细，pos交班使用", notes = "获取充值和退款汇总数据以及明细，pos交班使用", response = MemberPrepayCardStatisticDto.class, tags = {"会员储值门店端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberPrepayCardStatisticDto.class)})
    @RequestMapping(value = "/getDepositStatDataByQuery", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> getDepositStatDataByQuery(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                       @RequestBody MemberPrepayCardHistoryQueryVo vo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        MemberPrepayCardHistoryQueryDto dto = new MemberPrepayCardHistoryQueryDto();
        dto.setCreateUser(commonRequestModel.getEmployeeId());
        dto.setStartTime(vo.getStartTime());
        dto.setEndTime(vo.getEndTime());
        dto.setOrgansign(commonRequestModel.getOrganSign());
        return new ResponseEntity(memberPrepayCardApi.getDepositStatDataByQuery(dto), HttpStatus.OK);
    }


    @ApiOperation(value = "导出会员储值流水excel", notes = "导出会员储值流水excel", response = Boolean.class, tags = {"会员储值门店端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/exportHistoryExcel", method = RequestMethod.POST)
    void exportHistoryExcel(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                            @RequestBody MemberPrepayCardHistoryQueryVo vo,
                            HttpServletRequest request,
                            HttpServletResponse response) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        MemberPrepayCardHistoryQueryDto query = new MemberPrepayCardHistoryQueryDto();
        BeanUtils.copyProperties(vo, query);
        // organSign查询条件，总部机构号从登录态中获取
        // 单体门店
        if (commonRequestModel.getBizModel() == 1) {
            query.setOrgansign(commonRequestModel.getOrganSign());
        }
        // 联营连锁门店
        else {
            query.setHeadquartersOrganSign(commonRequestModel.getHeadquartersOrganSign());
        }
        ResultVO<List<MemberPrepayCardHistoryExportDto>> resultVO = memberPrepayCardApi.exportHistoryExcel(query);
        if (resultVO == null || resultVO.getCode() != 0 || resultVO.getResult() == null
                || resultVO.getResult().isEmpty()) {
            return;
        }
        List<MemberPrepayCardHistoryExportDto> list = resultVO.getResult();
        if (CollectionUtils.isNotEmpty(list)) {
            // 会员手机号脱敏
            List<MemberPrepayCardHistoryExportDto> hasTelephone = list.stream().filter(dto -> StringUtils.isNotEmpty(dto.getTelephoneEncrypted())).collect(Collectors.toList());
            Map<String, String> telephoneMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(hasTelephone)) {
                Set<String> telephoneTokens = hasTelephone.stream().map(member -> member.getTelephoneEncrypted()).collect(Collectors.toSet());
                telephoneMap = dataTokenService.getMemberSensitiveData(new ArrayList<>(telephoneTokens), Integer.valueOf(commonRequestModel.getEmployeeId()), DataTypeEnum.MOBILE_PHONE);
            }
            for (MemberPrepayCardHistoryExportDto memberPrepayCard : list) {
                if (StringUtil.isNotEmpty(memberPrepayCard.getTelephoneEncrypted())
                        && telephoneMap.containsKey(memberPrepayCard.getTelephoneEncrypted())) {
                    memberPrepayCard.setTelephone(telephoneMap.get(memberPrepayCard.getTelephoneEncrypted()));
                }
            }
        }
        SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.DATE_FORMAT_YYYY_MM_DD);
        String timeStr = "";
        try {
            timeStr = sdf.format(new Date());
        } catch (Exception e) {
            logger.warn("导出会员储值流水excel时间格式化错误");
        }

        String excelName = "储值交易记录";
        String[] headers = new String[]{"交易编号", "会员手机号", "会员姓名", "会员卡号", "来源门店", "交易金额", "交易类型", "卡内余额", "交易门店", "门店编码", "交易时间", "操作人"};
        String[] fieldNames = new String[]{"ticketNo", "telephone", "memberName", "cartNo", "originDrugstoreName", "changeTotalAmount", "operateType", "currentTotalAmount", "drugstoreName", "organSign", "createTime", "createUser"};

        try {
            ExportExcelUtil.createExcel(response, request, excelName + timeStr + ".xls", excelName, headers, fieldNames, list, true);
        } catch (IOException e) {
            logger.error("导出会员储值流水excel异常: {}", vo, e);
        }
    }

    @ApiOperation(value = "获取会员储值卡流水详情", notes = "获取会员储值卡流水详情，消费和退货明细需要单独查询", response = MemberPrepayCardHistory.class, tags = {"会员储值门店端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberPrepayCardHistoryDto.class)})
    @RequestMapping(value = "/selectById", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> selectById(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                        @RequestBody MemberPrepayCardHistoryDetailVo vo) {
        return new ResponseEntity(memberPrepayCardApi.selectHistoryById(vo.getId(), vo.getOrgansign()), HttpStatus.OK);
    }

    @ApiOperation(value = "获取指定会员充值记录分页", notes = "获取指定会员充值记录分页", response = MemberPrepayCardHistory.class, tags = {"会员储值门店端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberPrepayCardHistory.class)})
    @RequestMapping(value = "/getMemberDepositPage", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> getMemberDepositPage(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                  @RequestBody MemberPrepayCardHistoryDepositQueryVo vo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        MemberPrepayCardHistoryQueryDto dto = new MemberPrepayCardHistoryQueryDto();
        String organSign = commonRequestModel.getOrganSign();
        String headquartersOrganSign = commonRequestModel.getHeadquartersOrganSign();
        BeanUtils.copyProperties(vo, dto);
        // organSign查询条件，总部机构号从登录态中获取
        // 单体门店
        if (commonRequestModel.getBizModel() == 1) {
            dto.setOrgansign(organSign);
        }
        // 联营连锁门店
        else {
            dto.setHeadquartersOrganSign(headquartersOrganSign);
            if (StringUtils.isEmpty(vo.getOrgansign())){
                List<MemberShareDto> shareRel = memberShareApi.selectSharedList(headquartersOrganSign).getResult();
                List<String> shares = shareRel.stream().map(s -> s.getOrganSign()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(shares) && shares.contains(organSign)){
                    shares.add(headquartersOrganSign);
                    //共享门店
                    dto.setOrganList(shares);
                }else {
                    //不共享门店
                    dto.setOrgansign(organSign);
                }
            }
        }
        return new ResponseEntity(memberPrepayCardApi.getMemberDepositPage(dto), HttpStatus.OK);
    }

    @ApiOperation(value = "导出指定会员充值记录excel", notes = "导出指定会员充值记录excel", response = Boolean.class, tags = {"会员储值门店端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/exportMemberDepositExcel", method = RequestMethod.POST)
    void exportMemberDepositExcel(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                  @RequestBody MemberPrepayCardHistoryDepositQueryVo vo,
                                  HttpServletRequest request,
                                  HttpServletResponse response) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        MemberPrepayCardHistoryQueryDto query = new MemberPrepayCardHistoryQueryDto();
        BeanUtils.copyProperties(vo, query);
        // organSign查询条件，总部机构号从登录态中获取
        // 单体门店
        if (commonRequestModel.getBizModel() == 1) {
            query.setOrgansign(commonRequestModel.getOrganSign());
        }
        // 联营连锁门店
        else {
            query.setHeadquartersOrganSign(commonRequestModel.getHeadquartersOrganSign());
        }
        ResultVO<List<MemberPrepayCardHistoryExportDto>> resultVO = memberPrepayCardApi.exportMemberDepositExcel(query);
        if (resultVO == null || resultVO.getCode() != 0 || resultVO.getResult() == null
                || resultVO.getResult().isEmpty()) {
            return;
        }
        List<MemberPrepayCardHistoryExportDto> list = resultVO.getResult();
        SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.DATE_FORMAT_YYYY_MM_DD);
        String timeStr = "";
        try {
            timeStr = sdf.format(new Date());
        } catch (Exception e) {
            logger.warn("导出指定会员充值记录excel时间格式化错误");
        }

        String excelName = "会员充值记录";
        String[] headers = new String[]{"交易编号", "交易类型", "充值/退款金额", "赠送/扣减金额", "充值/退款方式", "卡内余额", "充值金额", "赠送金额", "交易时间", "充值门店", "门店编码", "操作人", "审核人", "备注"};
        String[] fieldNames = new String[]{"ticketNo", "operateType", "changeAmount", "changeBonus", "payType", "currentTotalAmount", "currentAmount", "currentBonus", "createTime", "drugstoreName", "organSign", "createUser", "checkUser", "reason"};

        try {
            ExportExcelUtil.createExcel(response, request, excelName + timeStr + ".xls", excelName, headers, fieldNames, list, true);
        } catch (IOException e) {
            logger.error("导出指定会员充值记录excel异常: {}", vo, e);
        }
    }

    @ApiOperation(value = "获取会员储值卡分页", notes = "获取会员储值卡分页", response = MemberPrepayCardDetailDto.class, tags = {"会员储值门店端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberPrepayCardDetailDto.class)})
    @RequestMapping(value = "/getMemberPrepayCardPage", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> getMemberPrepayCardPage(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                     @RequestBody MemberPrepayCardQueryVo vo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        MemberPrepayCardQueryDto dto = new MemberPrepayCardQueryDto();
        String organSign = commonRequestModel.getOrganSign();
        String headquartersOrganSign = commonRequestModel.getHeadquartersOrganSign();
        BeanUtils.copyProperties(vo, dto);
        // organSign查询条件，总部机构号从登录态中获取
        // 单体门店
        if (commonRequestModel.getBizModel() == 1) {
            dto.setOrgansign(organSign);
            dto.setOrganSigns(Arrays.asList(organSign));
        }
        // 联营连锁门店
        else {
            dto.setHeadquartersOrganSign(headquartersOrganSign);
            if (StringUtils.isEmpty(vo.getOrgansign())){
                //未指定查询门店
                //按理说应该联合saas_drugstore表确定总部下所有已共享的门店，不过垃圾数据的概率比较小
                List<MemberShareDto> shareRel = memberShareApi.selectSharedList(headquartersOrganSign).getResult();
                List<String> shares = shareRel.stream().map(s -> s.getOrganSign()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(shares) && shares.contains(organSign)){
                    //共享门店
                    dto.setOrganSigns(shares);
                }else {
                    //不共享门店
                    dto.setOrganSigns(Arrays.asList(organSign));
                }
            }
        }
        return new ResponseEntity(memberPrepayCardApi.selectMemberPrepayCardListByQuery(dto), HttpStatus.OK);
    }

    @ApiOperation(value = "导出机构下会员储值记录excel", notes = "导出机构下会员储值记录excel", response = Boolean.class, tags = {"会员储值门店端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/exportMemberPrepayCardExcel", method = RequestMethod.POST)
    void exportMemberPrepayCardExcel(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                     @RequestBody MemberPrepayCardQueryVo vo,
                                     HttpServletRequest request,
                                     HttpServletResponse response) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        MemberPrepayCardQueryDto query = new MemberPrepayCardQueryDto();
        BeanUtils.copyProperties(vo, query);
        // organSign查询条件，总部机构号从登录态中获取
        // 单体门店
        if (commonRequestModel.getBizModel() == 1) {
            query.setOrgansign(commonRequestModel.getOrganSign());
        }
        // 联营连锁门店
        else {
            query.setHeadquartersOrganSign(commonRequestModel.getHeadquartersOrganSign());
        }
        ResultVO<List<MemberPrepayCardDetailExportDto>> resultVO = memberPrepayCardApi.exportMemberPrepayCardExcel(query);
        if (resultVO == null || resultVO.getCode() != 0 || resultVO.getResult() == null
                || resultVO.getResult().isEmpty()) {
            return;
        }
        List<MemberPrepayCardDetailExportDto> list = resultVO.getResult();
        if (CollectionUtils.isNotEmpty(list)) {
            // 会员手机号脱敏
            List<MemberPrepayCardDetailExportDto> hasTelephone = list.stream().filter(dto -> StringUtils.isNotEmpty(dto.getTelephoneEncrypted())).collect(Collectors.toList());
            Map<String, String> telephoneMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(hasTelephone)) {
                Set<String> telephoneTokens = hasTelephone.stream().map(member -> member.getTelephoneEncrypted()).collect(Collectors.toSet());
                telephoneMap = dataTokenService.getMemberSensitiveData(new ArrayList<>(telephoneTokens), Integer.valueOf(commonRequestModel.getEmployeeId()), DataTypeEnum.MOBILE_PHONE);
            }
            for (MemberPrepayCardDetailExportDto memberPrepayCard : list) {
                if (StringUtil.isNotEmpty(memberPrepayCard.getTelephoneEncrypted())
                        && telephoneMap.containsKey(memberPrepayCard.getTelephoneEncrypted())) {
                    memberPrepayCard.setTelephone(telephoneMap.get(memberPrepayCard.getTelephoneEncrypted()));
                }
            }
        }
        SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.DATE_FORMAT_YYYY_MM_DD);
        String timeStr = "";
        try {
            timeStr = sdf.format(new Date());
        } catch (Exception e) {
            logger.warn("导出机构下会员储值记录excel时间格式化错误");
        }

        String excelName = "会员储值";
        String[] headers = new String[]{"会员手机号", "会员姓名", "会员等级", "会员卡号", "来源门店", "门店编码", "卡内余额", "充值余额", "赠送余额", "最近充值时间"};
        String[] fieldNames = new String[]{"telephone", "memberName", "vipLevelName", "cartNo",  "drugstoreName", "organsign", "totalAmount", "amount", "bonus", "lastDepositTime"};

        try {
            ExportExcelUtil.createExcel(response, request, excelName + timeStr + ".xls", excelName, headers, fieldNames, list, true);
        } catch (IOException e) {
            logger.error("导出机构下会员储值记录excel异常: {}", vo, e);
        }
    }

    @ApiOperation(value = "获取会员储值汇总数据", notes = "获取会员储值汇总数据", response = MemberPrepayCardDetailDto.class, tags = {"会员储值门店端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberPrepayCardDetailDto.class)})
    @RequestMapping(value = "/getPrepayCardStatData", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> getPrepayCardStatData(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                   @RequestBody MemberPrepayCardQueryVo vo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        MemberPrepayCardQueryDto dto = new MemberPrepayCardQueryDto();
        String organSign = commonRequestModel.getOrganSign();
        String headquartersOrganSign = commonRequestModel.getHeadquartersOrganSign();
        BeanUtils.copyProperties(vo, dto);
        // organSign查询条件，总部机构号从登录态中获取
        // 单体门店
        if (commonRequestModel.getBizModel() == 1) {
            dto.setOrgansign(organSign);
        }
        // 联营连锁门店
        else {
            dto.setHeadquartersOrganSign(headquartersOrganSign);
        }
        return new ResponseEntity(memberPrepayCardApi.getPrepayCardStatData(dto), HttpStatus.OK);
    }

    @ApiOperation(value = "退货", notes = "退货", response = Boolean.class, tags = {"会员储值门店端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/refund", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> refund(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                    @RequestBody MemberPrepayCardRefundVo vo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        MemberPrepayCardRefundDto dto = new MemberPrepayCardRefundDto();
        BeanUtils.copyProperties(vo, dto);
        dto.setOrgansign(commonRequestModel.getOrganSign());
        dto.setCreateUser(commonRequestModel.getEmployeeId());
        return new ResponseEntity(memberPrepayCardApi.refund(dto), HttpStatus.OK);
    }

    @ApiOperation(value = "使用储值卡支付（加密传输）", notes = "使用储值卡支付（加密传输）\n" +
            "加密步骤:  \n" +
            "1.对refund接口的请求参数json格式化为字符串  \n" +
            "2.使用AES的CBC模式 PKCS5Padding填充方式加密上述字符串  \n" +
            "3.使用BASE64进行编码", response = Boolean.class, tags = {"会员储值门店端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/refundV2", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> refundV2(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                      @RequestBody MemberPrepayCardEncryptDataVo encryptDataVo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String organSign = commonRequestModel.getOrganSign();
        encryptDataVo.setOrganSign(organSign);
        MemberPrepayCardEncryptDataDto dto = new MemberPrepayCardEncryptDataDto();
        BeanUtils.copyProperties(encryptDataVo, dto);
        dto.setEmployeeId(commonRequestModel.getEmployeeId());
        return new ResponseEntity(memberPrepayCardApi.refundV2(dto), HttpStatus.OK);
    }


    @ApiOperation(value = "根据小票号查询支付结果", notes = "根据小票号查询支付结果（加密传输）", response = Boolean.class, tags = {"会员储值门店端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/verifyPayResult", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> verifyPayResult(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                             @RequestBody MemberPrepayCardEncryptDataVo encryptDataVo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String organSign = commonRequestModel.getOrganSign();
        encryptDataVo.setOrganSign(organSign);
        MemberPrepayCardEncryptDataDto dto = new MemberPrepayCardEncryptDataDto();
        BeanUtils.copyProperties(encryptDataVo, dto);
        return new ResponseEntity(memberPrepayCardApi.verifyPayResultV2(dto), HttpStatus.OK);
    }

    @ApiOperation(value = "会员储值审核", notes = "会员储值审核", response = Boolean.class, tags = {"会员储值门店端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/check", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> check(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                   @RequestBody MemberPrepayCardVo vo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String organSign = commonRequestModel.getOrganSign();
        MemberPrepayCardDto dto = new MemberPrepayCardDto();
        BeanUtils.copyProperties(vo, dto);
        dto.setOrgansign(organSign);
        return new ResponseEntity(memberPrepayCardApi.check(dto), HttpStatus.OK);
    }


    @ApiOperation(value = "获取会员储值审核人列表", notes = "获取会员储值审核人列表", response = Object.class, tags = {"会员储值门店端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Object.class)})
    @RequestMapping(value = "/checkUserList", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> checkUserList(@RequestHeader("commonRequestModel") String commonRequestModelStr) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String organSign = commonRequestModel.getOrganSign();
        return new ResponseEntity(memberPrepayCardApi.checkUserList(organSign), HttpStatus.OK);
    }

}
