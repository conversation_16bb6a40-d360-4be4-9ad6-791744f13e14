package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * InventoryIncomeStatementVo
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-30T11:13:55.467+08:00")

public class InventoryIncomeStatementVo   {
  @JsonProperty("id")
  private Integer id = null;

  @JsonProperty("pref")
  private String pref = null;

  @JsonProperty("superiorDocPref")
  private String superiorDocPref = null;

  @JsonProperty("createUser")
  private String createUser = null;

  @JsonProperty("createTime")
  private Date createTime = null;

  @JsonProperty("updateUser")
  private String updateUser = null;

  @JsonProperty("updateTime")
  private Date updateTime = null;

  @JsonProperty("remark")
  private String remark = null;

  @JsonProperty("status")
  private Integer status = null;

  @JsonProperty("yn")
  private byte yn = 1;

  @JsonProperty("baseVersion")
  private String baseVersion = null;

  @JsonProperty("organsign")
  private String organsign = null;

  @JsonProperty("ticketUser")
  private String ticketUser = null;

  @JsonProperty("superIdStatus")
  private String superIdStatus = null;

  @JsonProperty("inventoryIncomeStatementDetailVos")
  private List<InventoryIncomeStatementDetailVo> inventoryIncomeStatementDetailVos = null;

  public InventoryIncomeStatementVo id(Integer id) {
    this.id = id;
    return this;
  }

   /**
   * 主键id
   * @return id
  **/
  @ApiModelProperty(value = "主键id")


  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public InventoryIncomeStatementVo pref(String pref) {
    this.pref = pref;
    return this;
  }

   /**
   * 编号
   * @return pref
  **/
  @ApiModelProperty(value = "编号")


  public String getPref() {
    return pref;
  }

  public void setPref(String pref) {
    this.pref = pref;
  }

  public InventoryIncomeStatementVo superiorDocPref(String superiorDocPref) {
    this.superiorDocPref = superiorDocPref;
    return this;
  }

   /**
   * 上级单据编号
   * @return superiorDocPref
  **/
  @ApiModelProperty(value = "上级单据编号")


  public String getSuperiorDocPref() {
    return superiorDocPref;
  }

  public void setSuperiorDocPref(String superiorDocPref) {
    this.superiorDocPref = superiorDocPref;
  }

  public InventoryIncomeStatementVo createUser(String createUser) {
    this.createUser = createUser;
    return this;
  }

   /**
   * 创建人
   * @return createUser
  **/
  @ApiModelProperty(value = "创建人")


  public String getCreateUser() {
    return createUser;
  }

  public void setCreateUser(String createUser) {
    this.createUser = createUser;
  }

  public InventoryIncomeStatementVo createTime(Date createTime) {
    this.createTime = createTime;
    return this;
  }

   /**
   * 创建时间
   * @return createTime
  **/
  @ApiModelProperty(value = "创建时间")

  @Valid

  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public InventoryIncomeStatementVo updateUser(String updateUser) {
    this.updateUser = updateUser;
    return this;
  }

   /**
   * 更新人
   * @return updateUser
  **/
  @ApiModelProperty(value = "更新人")


  public String getUpdateUser() {
    return updateUser;
  }

  public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
  }

  public InventoryIncomeStatementVo updateTime(Date updateTime) {
    this.updateTime = updateTime;
    return this;
  }

   /**
   * 更新时间
   * @return updateTime
  **/
  @ApiModelProperty(value = "更新时间")

  @Valid

  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public InventoryIncomeStatementVo remark(String remark) {
    this.remark = remark;
    return this;
  }

   /**
   * 备注
   * @return remark
  **/
  @ApiModelProperty(value = "备注")


  public String getRemark() {
    return remark;
  }

  public void setRemark(String remark) {
    this.remark = remark;
  }

  public InventoryIncomeStatementVo status(Integer status) {
    this.status = status;
    return this;
  }

   /**
   * 状态
   * @return status
  **/
  @ApiModelProperty(value = "状态")


  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public InventoryIncomeStatementVo yn(byte yn) {
    this.yn = yn;
    return this;
  }

   /**
   * 是否删除
   * @return yn
  **/
  @ApiModelProperty(value = "是否删除")

  public byte getYn() {
    return yn;
  }

  public void setYn(byte yn) {
    this.yn = yn;
  }

  public InventoryIncomeStatementVo baseVersion(String baseVersion) {
    this.baseVersion = baseVersion;
    return this;
  }

   /**
   * 版本号
   * @return baseVersion
  **/
  @ApiModelProperty(value = "版本号")


  public String getBaseVersion() {
    return baseVersion;
  }

  public void setBaseVersion(String baseVersion) {
    this.baseVersion = baseVersion;
  }

  public InventoryIncomeStatementVo organsign(String organsign) {
    this.organsign = organsign;
    return this;
  }

   /**
   * 机构代码
   * @return organsign
  **/
  @ApiModelProperty(value = "机构代码")


  public String getOrgansign() {
    return organsign;
  }

  public void setOrgansign(String organsign) {
    this.organsign = organsign;
  }

  public InventoryIncomeStatementVo ticketUser(String ticketUser) {
    this.ticketUser = ticketUser;
    return this;
  }

   /**
   * 开票员
   * @return ticketUser
  **/
  @ApiModelProperty(value = "开票员")


  public String getTicketUser() {
    return ticketUser;
  }

  public void setTicketUser(String ticketUser) {
    this.ticketUser = ticketUser;
  }

  public InventoryIncomeStatementVo superIdStatus(String superIdStatus) {
    this.superIdStatus = superIdStatus;
    return this;
  }

   /**
   * 上级单据标识
   * @return superIdStatus
  **/
  @ApiModelProperty(value = "上级单据标识")


  public String getSuperIdStatus() {
    return superIdStatus;
  }

  public void setSuperIdStatus(String superIdStatus) {
    this.superIdStatus = superIdStatus;
  }

  public InventoryIncomeStatementVo inventoryIncomeStatementDetailVos(List<InventoryIncomeStatementDetailVo> inventoryIncomeStatementDetailVos) {
    this.inventoryIncomeStatementDetailVos = inventoryIncomeStatementDetailVos;
    return this;
  }

  public InventoryIncomeStatementVo addInventoryIncomeStatementDetailVosItem(InventoryIncomeStatementDetailVo inventoryIncomeStatementDetailVosItem) {
    if (this.inventoryIncomeStatementDetailVos == null) {
      this.inventoryIncomeStatementDetailVos = new ArrayList<InventoryIncomeStatementDetailVo>();
    }
    this.inventoryIncomeStatementDetailVos.add(inventoryIncomeStatementDetailVosItem);
    return this;
  }

   /**
   * Get inventoryIncomeStatementDetailVos
   * @return inventoryIncomeStatementDetailVos
  **/
  @ApiModelProperty(value = "")

  @Valid

  public List<InventoryIncomeStatementDetailVo> getInventoryIncomeStatementDetailVos() {
    return inventoryIncomeStatementDetailVos;
  }

  public void setInventoryIncomeStatementDetailVos(List<InventoryIncomeStatementDetailVo> inventoryIncomeStatementDetailVos) {
    this.inventoryIncomeStatementDetailVos = inventoryIncomeStatementDetailVos;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InventoryIncomeStatementVo inventoryIncomeStatementVo = (InventoryIncomeStatementVo) o;
    return Objects.equals(this.id, inventoryIncomeStatementVo.id) &&
        Objects.equals(this.pref, inventoryIncomeStatementVo.pref) &&
        Objects.equals(this.superiorDocPref, inventoryIncomeStatementVo.superiorDocPref) &&
        Objects.equals(this.createUser, inventoryIncomeStatementVo.createUser) &&
        Objects.equals(this.createTime, inventoryIncomeStatementVo.createTime) &&
        Objects.equals(this.updateUser, inventoryIncomeStatementVo.updateUser) &&
        Objects.equals(this.updateTime, inventoryIncomeStatementVo.updateTime) &&
        Objects.equals(this.remark, inventoryIncomeStatementVo.remark) &&
        Objects.equals(this.status, inventoryIncomeStatementVo.status) &&
        Objects.equals(this.yn, inventoryIncomeStatementVo.yn) &&
        Objects.equals(this.baseVersion, inventoryIncomeStatementVo.baseVersion) &&
        Objects.equals(this.organsign, inventoryIncomeStatementVo.organsign) &&
        Objects.equals(this.ticketUser, inventoryIncomeStatementVo.ticketUser) &&
        Objects.equals(this.superIdStatus, inventoryIncomeStatementVo.superIdStatus) &&
        Objects.equals(this.inventoryIncomeStatementDetailVos, inventoryIncomeStatementVo.inventoryIncomeStatementDetailVos);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, pref, superiorDocPref, createUser, createTime, updateUser, updateTime, remark, status, yn, baseVersion, organsign, ticketUser, superIdStatus, inventoryIncomeStatementDetailVos);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InventoryIncomeStatementVo {\n");

    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    pref: ").append(toIndentedString(pref)).append("\n");
    sb.append("    superiorDocPref: ").append(toIndentedString(superiorDocPref)).append("\n");
    sb.append("    createUser: ").append(toIndentedString(createUser)).append("\n");
    sb.append("    createTime: ").append(toIndentedString(createTime)).append("\n");
    sb.append("    updateUser: ").append(toIndentedString(updateUser)).append("\n");
    sb.append("    updateTime: ").append(toIndentedString(updateTime)).append("\n");
    sb.append("    remark: ").append(toIndentedString(remark)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    yn: ").append(toIndentedString(yn)).append("\n");
    sb.append("    baseVersion: ").append(toIndentedString(baseVersion)).append("\n");
    sb.append("    organsign: ").append(toIndentedString(organsign)).append("\n");
    sb.append("    ticketUser: ").append(toIndentedString(ticketUser)).append("\n");
    sb.append("    superIdStatus: ").append(toIndentedString(superIdStatus)).append("\n");
    sb.append("    inventoryIncomeStatementDetailVos: ").append(toIndentedString(inventoryIncomeStatementDetailVos)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

