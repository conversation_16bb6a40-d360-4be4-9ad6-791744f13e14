package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Maps;
import com.xyy.common.module.api.SysConfigApi;
import com.xyy.common.module.dto.SystemConfigDto;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.encryption.cores.enums.DataTypeEnum;
import com.xyy.saas.member.core.api.DataTokenFlushApi;
import com.xyy.saas.member.core.dto.MemberConvertDto;
import com.xyy.saas.member.core.dto.MemberExchangeInfoDto;
import com.xyy.saas.web.api.module.member.model.MemberConvertVo;
import com.xyy.saas.web.api.module.member.model.MemberExchangeInfo;
import com.xyy.saas.web.api.module.utils.ExportExcelUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/member")
public class MemberExchangeInfoApiController {
    private static final Logger logger = LoggerFactory.getLogger(MemberExchangeInfoApiController.class);

    @Reference(version = "0.0.1")
    private com.xyy.saas.member.core.api.MemberExchangeInfoApi memberExchangeInfoApi;

    @Reference(version = "0.0.1")
    private DataTokenFlushApi dataTokenService;

    @Reference(version = "0.0.1")
    private SysConfigApi sysConfigApi;

    @ApiOperation(value = "保存会员积分兑换记录", notes = "保存会员积分兑换记录", response = MemberExchangeInfo.class, tags={ "memberExchangeInfo", })
    @ApiResponses(value = {
            @ApiResponse(code = 0, message = "兑换成功", response = MemberExchangeInfo.class)
    })
    @RequestMapping(value = "/memberExchangeInfo/saveMemberExchangeInfo",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> saveMemberExchangeInfo(@RequestHeader(name = "organSign", required = true)String organSign,
                                                           @RequestHeader(name = "employeeId", required = true) Integer employeeId,
                                                           @ApiParam(value = "会员积分兑换记录" ,required=true )  @RequestBody MemberConvertVo memberConvertVo) throws Exception {
        MemberConvertDto memberConvertDto = new MemberConvertDto();
        List<MemberExchangeInfoDto> list = new ArrayList<>();
        List<MemberExchangeInfo> exchangeInfos = memberConvertVo.getMemberExchangeInfoList();
        MemberExchangeInfoDto infoDto = null;
        for (MemberExchangeInfo exchangeInfo : exchangeInfos) {
            infoDto = new MemberExchangeInfoDto();
            BeanUtils.copyProperties(exchangeInfo, infoDto);
            list.add(infoDto);
        }
        BeanUtils.copyProperties(memberConvertVo,memberConvertDto);
        memberConvertDto.setMemberExchangeInfoList(list);
        memberConvertDto.setOrgansign(organSign);
        memberConvertDto.setUserId(employeeId);
        Map<String,Object> map = memberExchangeInfoApi.saveMemberExchangeInfo(memberConvertDto);
        return new ResponseEntity<ResultVO>(new ResultVO(Integer.parseInt(map.get("code").toString()),map.get("msg").toString(),null), HttpStatus.OK);
    }

    @ApiOperation(value = "获取同步会员积分记录", notes = "获取同步会员积分记录", response = MemberExchangeInfo.class, tags={ "memberExchangeInfo", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberExchangeInfo.class) })
    @RequestMapping(value = "/memberExchangeInfo/getSyncmemberExchangeInfoList",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getSyncmemberExchangeInfoList(@RequestHeader(name = "organSign", required = true)String organSign,
                                                                  @ApiParam(value = "会员积分兑换记录" ,required=true )  @RequestBody MemberExchangeInfo memberExchangeInfo) {
        MemberExchangeInfoDto infoDto = new MemberExchangeInfoDto();
        BeanUtils.copyProperties(memberExchangeInfo,infoDto);
        infoDto.setOrgansign(organSign);
        return new ResponseEntity<ResultVO>(new ResultVO(memberExchangeInfoApi.getSyncMemberExchangeInfoList(infoDto)), HttpStatus.OK);
    }

    @ApiOperation(value = "积分兑换记录列表", notes = "积分兑换记录列表", response = MemberExchangeInfo.class, tags={ "memberExchangeInfo", })
    @ApiResponses(value = { @ApiResponse(code = 200, message = "操作成功", response = MemberExchangeInfo.class) })
    @RequestMapping(value = "/memberExchangeInfo/getExchangeInfoPager", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getExchangeInfoPager(@RequestHeader("commonRequestModel") String commonRequestModel,
                                                         @ApiParam(value = "会员积分兑换记录" ,required=true )  @RequestBody MemberExchangeInfoDto dto) {
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        dto.setOrgansign(model.getOrganSign());
        dto.setHeadquartersOrganSign(model.getHeadquartersOrganSign());
        dto.setYn(1);
        dto.setType(0);
        dto.setIsHidden(model.getIdentity().intValue());
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(dto.getPageNum());
        pageInfo.setPageSize(dto.getPageSize());
        return new ResponseEntity<ResultVO>(new ResultVO(memberExchangeInfoApi.getMemberExchangeInfoPager(dto, pageInfo)),HttpStatus.OK);
    }

    @ApiOperation(value = "积分兑换记录导出", notes = "积分兑换记录导出", response = MemberExchangeInfo.class, tags={ "memberExchangeInfo", })
    @RequestMapping(value = "/memberExchangeInfo/exportExcel", method = {RequestMethod.POST})
    public void exportExcelMember(@RequestHeader("commonRequestModel") String commonRequestModel,HttpServletRequest request,
                                  HttpServletResponse response, @RequestBody MemberExchangeInfoDto dto) {

        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        dto.setOrgansign(model.getOrganSign());
        dto.setYn(1);
        dto.setType(0);
        dto.setIsHidden(model.getIdentity().intValue());
        dto.setHeadquartersOrganSign(model.getHeadquartersOrganSign());
        List<MemberExchangeInfoDto> memberExchangeList = memberExchangeInfoApi.getMemberExchangeInfoList(dto);
        if (!CollectionUtils.isEmpty(memberExchangeList)) {
            // 会员手机号脱敏
            List<MemberExchangeInfoDto> hasTelephone = memberExchangeList.stream().filter(member -> StringUtils.isNotEmpty(member.getTelephoneEncrypted())).collect(Collectors.toList());
            Map<String, String> telephoneMap = Maps.newHashMap();
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(hasTelephone)) {
                Set<String> telephoneTokens = hasTelephone.stream().map(member -> member.getTelephoneEncrypted()).collect(Collectors.toSet());
                telephoneMap = dataTokenService.getMemberSensitiveData(new ArrayList<>(telephoneTokens), Integer.valueOf(model.getEmployeeId()), DataTypeEnum.MOBILE_PHONE);
            }
            for (MemberExchangeInfoDto memberExchange : memberExchangeList) {
                if (StringUtil.isNotEmpty(memberExchange.getTelephoneEncrypted())
                        && telephoneMap.containsKey(memberExchange.getTelephoneEncrypted())) {
                    memberExchange.setTelephone(telephoneMap.get(memberExchange.getTelephoneEncrypted()));
                }
            }
        }
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String extfilename = "积分兑换记录" + df.format(new Date()) + ".xls";
        String sheetName = "积分兑换记录";
        String[] headers = new String[]{"小票号", "通用名", "商品编号", "会员手机号", "会员姓名", "会员卡号", "来源门店", "兑换数量", "消耗积分", "兑换时间", "收银员"};
        String[] fieldNames = new String[]{"ticketNo", "productName", "productPref", "telephone", "memberName", "cartNo", "sourceDrugstoreName", "number", "integral", "createTimeStr", "createUser"};

        SystemConfigDto systemConfigDto = sysConfigApi.querySystemConfig(model.getOrganSign());
        logger.info("organSign:{},机构配置信息systemConfigDto:{}", model.getOrganSign(), JSON.toJSONString(systemConfigDto));
        if(systemConfigDto != null && Byte.valueOf("1").equals(systemConfigDto.getProductTraceUploadYn())){
            headers = new String[]{"小票号", "通用名", "商品编号", "会员手机号", "会员姓名", "会员卡号", "来源门店", "兑换数量", "消耗积分", "兑换时间", "收银员", "追溯码"};
            fieldNames = new String[]{"ticketNo", "productName", "productPref", "telephone", "memberName", "cartNo", "sourceDrugstoreName", "number", "integral", "createTimeStr", "createUser", "traceCode"};
        }
        try {
            ExportExcelUtil.createExcel(response, request, extfilename, sheetName, headers, fieldNames, memberExchangeList, true);
        } catch (Exception e) {
            logger.error("memberExchangeInfo/exportExcel is error.", e);
        }
    }
}
