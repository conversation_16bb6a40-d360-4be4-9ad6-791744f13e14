package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.api.SaasAreaApi;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.dto.SaasRegionBusinessDto;
import com.xyy.saas.common.dto.XyySaasRegionParamsDto;
import com.xyy.saas.common.util.BeanUtil;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.member.api.WxMemberApi;
import com.xyy.saas.member.dto.AddApplyMaterialReqDto;
import com.xyy.saas.member.dto.PageGetMaterialInfoReqDto;
import com.xyy.saas.member.dto.PageGetMaterialInfoRespDto;
import com.xyy.saas.member.enums.ResultCodeEnum;
import com.xyy.saas.web.api.common.config.annotition.RepeatSubmitValidation;
import com.xyy.saas.web.api.module.member.model.AddApplyMaterialReqVo;
import com.xyy.saas.web.api.module.member.model.CurrentDrugStoreInfoVo;
import com.xyy.saas.web.api.module.member.model.PageGetAllMaterialInfoRespVo;
import com.xyy.saas.web.api.module.member.model.PageQueryVo;
import com.xyy.saas.web.api.module.utils.PatternUtils;
import com.xyy.saas.web.api.module.utils.StringUtils;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import com.xyy.user.module.dto.restructure.SaaSEmployeeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 小智会员API
 * @date 20210728
 */
@RestController
@RequestMapping("/member/memberWechat")
@Api(value = "memberWechat", description = "小智会员API")
public class MemberWechatApiController {

    private static final Logger logger = LoggerFactory.getLogger(MemberWechatApiController.class);

    //@Reference(version = "0.0.1",url = "dubbo://127.0.0.1:20900",timeout = 600000)
    @Reference(version = "0.0.1")
    WxMemberApi wxMemberApi;

    @Reference(version = "0.0.1")
    DrugstoreApi drugstoreApi;

    @Reference(version = "0.0.1")
    EmployeeApi employeeApi;

    @Reference(version = "0.0.1",timeout = 20000)
    private SaasAreaApi saasAreaApi;
    /**
     * 小智会员台卡申请
     * @param commonRequestModelStr
     * @param param
     * @return
     */
    @RepeatSubmitValidation(resultType = 3)
    @ApiOperation(value = "小智会员台卡申请", notes = "小智会员台卡申请")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功") })
    @PostMapping(value = "/addApplyMaterial")
    public ResultVO<Boolean> addApplyMaterial(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                     @RequestBody AddApplyMaterialReqVo param){
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        logger.info("【小智会员台卡申请】入参：{},param:{}",commonRequestModelStr, JSON.toJSONString(param));
        ResultVO<Boolean> resultVO = new ResultVO();
        try {
            ResultVO<Boolean> checkResult = checkAddApplyMaterialReqVo(param);
            if(!checkResult.getResult()){
                resultVO.setResult(false);
                resultVO.setCode(ResultCodeEnum.ERROR.getCode());
                resultVO.setMsg(checkResult.getMsg());
                return resultVO;
            }
            setAreaName(param);
            AddApplyMaterialReqDto reqDto = new AddApplyMaterialReqDto();
            BeanUtils.copyProperties(param,reqDto);
            reqDto.setOrganSign(commonRequestModel.getOrganSign());
            reqDto.setApplyEmployeeId(commonRequestModel.getEmployeeId());
            com.xyy.saas.member.result.ResultVO<Boolean> dubboResult =  wxMemberApi.addApplyMaterial(reqDto);
            BeanUtils.copyProperties(dubboResult,resultVO);
            return resultVO;
        }catch (Exception e){
            logger.info("【小智会员台卡申请】出现异常：",e);
            return ResultVO.createError("系统异常");
        }
    }

    /**
     * 获取当前药店信息
     * @param commonRequestModelStr
     * @return
     */
    @ApiOperation(value = "获取当前药店信息", notes = "获取当前药店信息")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功") })
    @PostMapping(value = "/getDrugStoreCurrentInfo")
    public ResultVO<CurrentDrugStoreInfoVo> getDrugStoreCurrentInfo(@RequestHeader("commonRequestModel") String commonRequestModelStr){
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String organSign = commonRequestModel.getOrganSign();
        logger.info("【获取当前药店信息】入参：{}",commonRequestModelStr);
        try {
            CurrentDrugStoreInfoVo respVo = new CurrentDrugStoreInfoVo();
            SaaSDrugstoreDto saaSDrugstoreDto = drugstoreApi.getDrugstoreByOrganSign(organSign);
            if(saaSDrugstoreDto == null){
                return ResultVO.createSuccess(respVo);
            }
            respVo.setOrganSign(organSign);
            respVo.setOrganSignName(saaSDrugstoreDto.getDrugstoreName());
            respVo.setProvince(saaSDrugstoreDto.getProvince());
            respVo.setProvinceCode(StringUtils.getSubStrBySeparator(saaSDrugstoreDto.getAreaCode(),1,","));
            respVo.setCity(saaSDrugstoreDto.getCity());
            respVo.setCityCode(StringUtils.getSubStrBySeparator(saaSDrugstoreDto.getAreaCode(),2,","));
            respVo.setArea(saaSDrugstoreDto.getArea());
            respVo.setAreaCode(StringUtils.getSubStrBySeparator(saaSDrugstoreDto.getAreaCode(),3,","));
            respVo.setAddress(saaSDrugstoreDto.getAddress());
            SaaSEmployeeDto saaSEmployeeDto = employeeApi.getEmployeeById(Integer.valueOf(commonRequestModel.getEmployeeId()));
            if(saaSEmployeeDto == null){
                return ResultVO.createSuccess(respVo);
            }
            respVo.setEmployeeName(saaSEmployeeDto.getName());
            respVo.setPhone(saaSEmployeeDto.getPhone());
            return ResultVO.createSuccess(respVo);
        }catch (Exception e){
            logger.info("【获取当前药店信息】出现异常：",e);
            return ResultVO.createError("系统异常");
        }

    }

    /**
     * 获取药店所有物流申请记录
     * @param commonRequestModelStr
     * @param param
     * @return
     */
    @ApiOperation(value = "获取药店所有物流申请记录", notes = "获取药店所有物流申请记录")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功") })
    @PostMapping(value = "/pageGetAllMaterialInfo")
    public ResultVO<PageInfo<PageGetAllMaterialInfoRespVo>>  pageGetAllMaterialInfo(@RequestHeader("commonRequestModel") String commonRequestModelStr, @RequestBody PageQueryVo param){
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String organSign = commonRequestModel.getOrganSign();
        logger.info("【获取药店所有物流申请记录】入参：{},param:{}",commonRequestModelStr, JSON.toJSONString(param));
        try {
            PageGetMaterialInfoReqDto reqDto = new PageGetMaterialInfoReqDto();
            BeanUtils.copyProperties(param,reqDto);
            reqDto.setOrganSign(organSign);
            com.xyy.saas.member.result.ResultVO<PageInfo<PageGetMaterialInfoRespDto>> dubboResult =  wxMemberApi.pageGetMaterialInfo(reqDto);
            ResultVO<PageInfo<PageGetAllMaterialInfoRespVo>> resultVO = new ResultVO();
            BeanUtils.copyProperties(dubboResult,resultVO);
            PageInfo<PageGetAllMaterialInfoRespVo> pageInfo = new PageInfo<>();
            BeanUtils.copyProperties(dubboResult.getResult(),pageInfo);
            pageInfo.setList(BeanUtil.copyListProperties(dubboResult.getResult().getList(),PageGetAllMaterialInfoRespVo.class));
            resultVO.setResult(pageInfo);
            return resultVO;
        }catch (Exception e){
            logger.info("【获取药店所有物流申请记录】出现异常：",e);
            return ResultVO.createError("系统异常");
        }
    }


    /**
     * 省市区赋值名字
     * @param param
     */
    private void setAreaName(AddApplyMaterialReqVo param){
        XyySaasRegionParamsDto paramsDto = new XyySaasRegionParamsDto();
        List<Integer> areaCodeList = new ArrayList<>();
        areaCodeList.add(param.getProvinceCode());
        areaCodeList.add(param.getCityCode());
        areaCodeList.add(param.getAreaCode());
        paramsDto.setAreaCodes(areaCodeList);
        List<SaasRegionBusinessDto> dtoList = saasAreaApi.queryRegionByAreaCodeList(paramsDto);
        Map<Integer,String> areaMap = dtoList.stream().collect(Collectors.toMap(SaasRegionBusinessDto::getAreaCode,SaasRegionBusinessDto::getAreaName));
        param.setProvince(areaMap.get(param.getProvinceCode()));
        param.setCity(areaMap.get(param.getCityCode()));
        param.setArea(areaMap.get(param.getAreaCode()));
    }


    /**
     * 提交申请时参数校验
     * @param reqVo
     * @return
     */
    ResultVO<Boolean> checkAddApplyMaterialReqVo(AddApplyMaterialReqVo reqVo){
        ResultVO<Boolean> resultVO = new ResultVO<>();
        resultVO.setResult(false);
        if(StringUtils.isBlank(reqVo.getOrganSignApplyName())){
            resultVO.setMsg("门店名称不能为空");
            return resultVO;
        }
        if(reqVo.getOrganSignApplyName().length() > 20){
            resultVO.setMsg("门店名称长度不能超过10");
            return resultVO;
        }
        if(StringUtils.isBlank(reqVo.getName())){
            resultVO.setMsg("收件人不能为空");
            return resultVO;
        }
        if(reqVo.getName().length()>15){
            resultVO.setMsg("收件人长度不能超过15");
            return resultVO;
        }
        if(StringUtils.isBlank(reqVo.getPhone())){
            resultVO.setMsg("收件电话不能为空");
            return resultVO;
        }

        if(!PatternUtils.isMatch(reqVo.getPhone(),PatternUtils.TELEPHONE)){
            resultVO.setMsg("请输入正确的手机号");
            return resultVO;
        }
        if(reqVo.getProvinceCode() == null){
            resultVO.setMsg("请输入收件地址");
            return resultVO;
        }
        if(reqVo.getCityCode() == null){
            resultVO.setMsg("请输入收件地址");
            return resultVO;
        }
        if(reqVo.getAreaCode() == null){
            resultVO.setMsg("请输入收件地址");
            return resultVO;
        }
        if(StringUtils.isBlank(reqVo.getAddress())){
            resultVO.setMsg("请输入收件地址");
            return resultVO;
        }
        if(reqVo.getAddress().length()>255){
            resultVO.setMsg("详情地址过长");
            return resultVO;
        }
        resultVO.setResult(true);
        return resultVO;
    }

}
