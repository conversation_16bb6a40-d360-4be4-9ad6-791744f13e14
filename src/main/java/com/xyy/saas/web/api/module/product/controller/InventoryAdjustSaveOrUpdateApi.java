/**
 * NOTE: This class is auto generated by the swagger code generator program (2.2.3).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.web.api.module.product.model.InventoryAdjustVo;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-30T12:28:35.123+08:00")
@RequestMapping("/product")
@Api(value = "inventoryAdjustSaveOrUpdate", description = "the inventoryAdjustSaveOrUpdate API")
public interface InventoryAdjustSaveOrUpdateApi {

    @ApiOperation(value = "批号效期调整保存", notes = "批号效期调整保存", response = ResultVO.class, tags={ "saveOrUpdateAdjust", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    
    @RequestMapping(value = "/inventoryAdjustSaveOrUpdate",
        produces = { "application/xml", "application/json" }, 
        consumes = { "application/json", "application/xml" },
        method = RequestMethod.POST)
    ResponseEntity<ResultVO> inventoryAdjustSaveOrUpdate(@ApiParam(value = "批号效期调整Vo", required = true) @RequestHeader("userId") Integer userId, @RequestHeader("organSign") String organSign, @Valid @RequestBody InventoryAdjustVo inventoryAdjustVo);

}
