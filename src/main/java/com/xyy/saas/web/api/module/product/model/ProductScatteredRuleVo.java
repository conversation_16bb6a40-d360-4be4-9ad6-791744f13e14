package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * @ClassName ProductScatteredRuleVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/9/24 11:19
 * @Version 1.0
 **/
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

@ApiModel(description = "商品类型实体对象")
public class ProductScatteredRuleVo {

    @JsonProperty("id")
    private Long id; // 拆零规则ID
    @JsonProperty("productPref")
    private String productPref; // 商品编号
    @JsonProperty("mixedQuery")
    private String mixedQuery;//混合查询
    @JsonProperty("organSign")
    private String organSign;//药店唯一标识    商品编号+药店唯一标识为该药店商品的唯一标识
    @JsonProperty("scatteredNumber")
    private Integer scatteredNumber;//拆零数量
    @JsonProperty("mnemonicCode")
    private String mnemonicCode;//助记码
    @JsonProperty("scatteredSpecification")
    private String scatteredSpecification;//拆零规格
    @JsonProperty("unitId")
    private Integer unitId;//拆零单位
    @JsonProperty("status")
    private Byte status;//是否启用，1--启用，0--不启用
    @JsonProperty("remark")
    private String remark;//备注
    @JsonProperty("createUser")
    private String createUser;//创建人
    @JsonProperty("createTime")
    private Date createTime;//创建时间
    @JsonProperty("updateUser")
    private String updateUser;//更新人
    @JsonProperty("updateTime")
    private Date updateTime;//更新时间
    @JsonProperty("yn")
    private Byte yn;//逻辑删除 1 有效 0 删除
    @JsonProperty("baseVersion")
    private String baseVersion;//操作版本号
    @JsonProperty("pharmacyPref")
    private String pharmacyPref; //药店内部使用的商品唯一编号
    @JsonProperty("scatteredNum")
    private Integer scatteredNum;//商品拆零次数 默认为0
    @JsonProperty("drugPermissionPerson")
    private String  drugPermissionPerson; //药品上市许可证持有人
    @JsonProperty("usageAndDosage")
    private String usageAndDosage;//用法用量

    @ApiModelProperty(value = "药品上市许可证持有人")
    public String getDrugPermissionPerson() {
        return drugPermissionPerson;
    }

    public void setDrugPermissionPerson(String drugPermissionPerson) {
        this.drugPermissionPerson = drugPermissionPerson;
    }

    @ApiModelProperty(value = "拆零规则ID")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @ApiModelProperty(value = "商品编号")
    public String getProductPref() {
        return productPref;
    }

    public void setProductPref(String productPref) {
        this.productPref = productPref;
    }

    @ApiModelProperty(value = "混合查询")
    public String getMixedQuery() {
        return mixedQuery;
    }

    public void setMixedQuery(String mixedQuery) {
        this.mixedQuery = mixedQuery;
    }

    @ApiModelProperty(value = "机构编码")
    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    @ApiModelProperty(value = "拆零数量")
    public Integer getScatteredNumber() {
        return scatteredNumber;
    }

    public void setScatteredNumber(Integer scatteredNumber) {
        this.scatteredNumber = scatteredNumber;
    }

    @ApiModelProperty(value = "助记码")
    public String getMnemonicCode() {
        return mnemonicCode;
    }

    public void setMnemonicCode(String mnemonicCode) {
        this.mnemonicCode = mnemonicCode;
    }

    @ApiModelProperty(value = "拆零规格")
    public String getScatteredSpecification() {
        return scatteredSpecification;
    }

    public void setScatteredSpecification(String scatteredSpecification) {
        this.scatteredSpecification = scatteredSpecification;
    }

    @ApiModelProperty(value = "单位id")
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @ApiModelProperty(value = "是否启用，1--启用，0--不启用")
    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    @ApiModelProperty(value = "备注")
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @ApiModelProperty(value = "创建人")
    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    @ApiModelProperty(value = "创建时间")
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @ApiModelProperty(value = "更新人")
    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    @ApiModelProperty(value = "更新时间")
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @ApiModelProperty(value = "逻辑删除 1 有效 0 删除")
    public Byte getYn() {
        return yn;
    }

    public void setYn(Byte yn) {
        this.yn = yn;
    }

    @ApiModelProperty(value = "操作版本号")
    public String getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(String baseVersion) {
        this.baseVersion = baseVersion;
    }

    @ApiModelProperty(value = "商品编号，外码做展示用")
    public String getPharmacyPref() {
        return pharmacyPref;
    }

    public void setPharmacyPref(String pharmacyPref) {
        this.pharmacyPref = pharmacyPref;
    }

    @ApiModelProperty(value = "拆零次数，默认从0开始")
    public Integer getScatteredNum() {
        return scatteredNum;
    }

    public void setScatteredNum(Integer scatteredNum) {
        this.scatteredNum = scatteredNum;
    }

    @ApiModelProperty(value = "用法用量")
    public String getUsageAndDosage() {
        return usageAndDosage;
    }

    public void setUsageAndDosage(String usageAndDosage) {
        this.usageAndDosage = usageAndDosage;
    }
}
