/**
 * NOTE: This class is auto generated by the swagger code generator program (2.2.3).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.web.api.module.product.model.InventoryVerifyVo;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T17:19:18.933+08:00")
@RequestMapping("/product")
@Api(value = "verifySaveOrUpdate", description = "the verifySaveOrUpdate API")
public interface VerifySaveOrUpdateApi {

    @ApiOperation(value = "盘点确认保存", notes = "盘点确认保存", response = ResultVO.class, tags={ "saveOrUpdateVerify", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })

    @RequestMapping(value = "/verifySaveOrUpdate",
            produces = { "application/xml", "application/json" },
            consumes = { "application/json", "application/xml" },
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> verifySaveOrUpdate(@ApiParam(value = "盘点确认Vo", required = true) @RequestHeader("userName") String userName, @RequestHeader("organSign") String organSign, @Valid @RequestBody InventoryVerifyVo inventoryVerifyVo);
}
