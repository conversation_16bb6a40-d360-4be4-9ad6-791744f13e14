package com.xyy.saas.web.api.common.config;

import com.xyy.saas.web.api.common.filter.BaseFilter;
import com.xyy.saas.web.api.common.filter.GlobalParamCheckFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 定义过滤器拦截url及顺序
 * <AUTHOR>
 * @date 2019-08-08
 * @mondify
 * @copyright
 */
@Configuration
public class FilterConfig {
    @Bean
    public GlobalParamCheckFilter globalParamCheckFilter() {
        return new GlobalParamCheckFilter();
    }


    @Bean
    public BaseFilter baseFilter(){
        return new BaseFilter();
    }


    @Bean
    public FilterRegistrationBean baseFilterRegister() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        //注入过滤器
        registration.setFilter(baseFilter());
        //拦截规则
        registration.addUrlPatterns("/*");
        //过滤器名称
        registration.setName("baseFilter");
        //过滤器顺序
//        registration.setOrder(FilterRegistrationBean.HIGHEST_PRECEDENCE);
        registration.setOrder(FilterRegistrationBean.LOWEST_PRECEDENCE +1);
        return registration;
    }

    @Bean
    public FilterRegistrationBean globalParamCheckFilterRegister() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        //注入过滤器
        registration.setFilter(globalParamCheckFilter());
        //拦截规则
        registration.addUrlPatterns("/*");
        //过滤器名称
        registration.setName("globalParamCheckFilter");
        //过滤器顺序
        registration.setOrder(FilterRegistrationBean.LOWEST_PRECEDENCE -1);
        return registration;
    }


}
