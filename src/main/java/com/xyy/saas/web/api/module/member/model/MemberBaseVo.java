package com.xyy.saas.web.api.module.member.model;

import com.xyy.saas.member.core.dto.MemberArchivesInfoDto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 会员基本信息
 * <AUTHOR>
 */
public class MemberBaseVo implements Serializable {

    private static final long serialVersionUID = 6990359343085957682L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 会员卡号
     */
    private String cartNo;

    /**
     * 混合查询
     */
    private String mixedQuery;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别 1男 2女
     */
    private Integer sex;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 级别id
     */
    private Long vipLevelId;

    /**
     * 电话
     */
    private String telephone;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 地址
     */
    private String address;

    /**
     * 微信
     */
    private String wechat;

    /**
     * qq
     */
    private String qq;

    /**
     * 可用积分
     */
    private BigDecimal point;

    /**
     * 累计积分
     */
    private BigDecimal allPoint;

    /**
     * 版本号
     */
    private Integer baseVersion;


    /**
     * 过期时间
     */
    private String expriedTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


    /**
     * 是否可用
     */
    private Integer yn;

    /**
     * 开始的页码
     */
    private Integer pageNum;

    /**
     * 每页的数量
     */
    private Integer pageSize;

    /**
     * 排序 1:asc 2:desc
     */
    private Integer sortWay;

    /**
     * 最小积分
     */
    private Integer startPoint;

    /**
     * 最大积分
     */
    private Integer endPoint;


    /**
     * 级别名称
     */
    private String levelName;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 生效时间
     */
    private String effectTime;


    /**
     * 发卡时间
     */
    private String sendCardTime;


    /**
     * 发卡状态 0 启用 1停用
     */
    private Integer state;

    /**
     * 密码
     */
    private String passwd;

    /**
     * 备注
     */
    private String remark;

    private String guid;
    
    //-----------
    /**
     * 会员guid
     */
    private String memberGuid;

    /**
     * 储值卡实充余额
     */
    private BigDecimal amount;

    /**
     * 储值卡赠送余额
     */
    private BigDecimal bonus;

    /**
     * 机构标识
     */
    private String organsign;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 审核人
     */
    private String checkUser;

    /**
     * 是否退款
     */
    private Boolean isRefunded;

    /**
     * 支付类型 1-现金 2-微信 3-支付宝 4-银联 9-其他
     * @see com.xyy.saas.member.core.enums.PayTypeEnum
     */
    private Byte payType;

    /**
     * 原因
     */
    private String reason;

    /**
     * 请求来源 web-管理端 pos-pos端
     */
    private String from;

    /**
     * 审核人密码（MD5加密）
     */
    private String checkPassword;

    /**
     * 总部机构号
     */
    private String headquartersOrganSign;

    /**
     * 省
     */
    private String provinceName;

    /**
     * 省编码
     */
    private Integer provinceCode;

    /**
     * 市
     */
    private String cityName;

    /**
     * 市编码
     */
    private Integer cityCode;

    /**
     * 区
     */
    private String areaName;

    /**
     * 区编码
     */
    private Integer areaCode;

    /**
     * 会员来源
     */
    private Byte source;

    /**
     * 白名单
     */
    private Integer whiteList;

    /**
     * 手机号是否修改
     */
    private Boolean telephoneChangeFlag;

    /**
     * 身份证号是否修改
     */
    private Boolean idCardChangeFlag;

    /** 慢病集合 chronicPrefList：[{pref:'12',chronicName:'12'},{pref:'22',chronicName:'22'}]
     */
    private List<MemberChronicConfigVo> chronicPrefList;
    /**
     * 慢病名称(逗号分隔)
     */
    private String chronicName;

    /**
     * 慢病编号(逗号分隔)
     */
    private String pref;

    // 是否为患者
    private Integer patient;
    // 身高
    private String height;
    // 体重
    private String weight;
    // 血型:A型、B型、AB型、O型；默认为：空；
    private String blood;
    // 婚姻状况:已婚、未婚、离异、丧偶；默认为：空；
    private String marriage;
    // 过敏史
    private String historyOfAllergy;
    // 既往病史
    private String jiWangBingShi;
    // 健康需求
    private String jianKangXuQiu;
    // 用药禁忌
    private String yongYaoJinJi;
    // 常用药物
    private String changYongYao;
    // 职业
    private String profession;
    // 电子邮件
    private String mailBox;
    // 关心病种
    private String guanXinBing;
    // 医保卡
    private String carteVital;
    // 患者身份证
    private  String idCards;
    // 患者病例
    private String patientCase;
    // 医疗保险特殊药品使用申请表
    private String applyForCard;
    // 享受门诊重症待遇通知单
    private String letterOfNotice;
    // 其他
    private String others;
    //档案信息
    private MemberArchivesInfoDto archivesInfo;
    //操作类型 1 病历
    private Integer operationType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCartNo() {
        return cartNo;
    }

    public void setCartNo(String cartNo) {
        this.cartNo = cartNo == null ? null : cartNo.trim();
    }

    public String getMixedQuery() {
        return mixedQuery;
    }

    public void setMixedQuery(String mixedQuery) {
        this.mixedQuery = mixedQuery == null ? null : mixedQuery.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public Integer getSex() {
        return sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public Long getVipLevelId() {
        return vipLevelId;
    }

    public void setVipLevelId(Long vipLevelId) {
        this.vipLevelId = vipLevelId;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone == null ? null : telephone.trim();
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard == null ? null : idCard.trim();
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    public String getWechat() {
        return wechat;
    }

    public void setWechat(String wechat) {
        this.wechat = wechat == null ? null : wechat.trim();
    }

    public String getQq() {
        return qq;
    }

    public void setQq(String qq) {
        this.qq = qq == null ? null : qq.trim();
    }

    public BigDecimal getPoint() {
        return point;
    }

    public void setPoint(BigDecimal point) {
        this.point = point;
    }

    public BigDecimal getAllPoint() {
        return allPoint;
    }

    public void setAllPoint(BigDecimal allPoint) {
        this.allPoint = allPoint;
    }

    public Integer getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(Integer baseVersion) {
        this.baseVersion = baseVersion;
    }

    public String getOrgansign() {
        return organsign;
    }

    public void setOrgansign(String organsign) {
        this.organsign = organsign == null ? null : organsign.trim();
    }

    public String getExpriedTime() {
        return expriedTime;
    }

    public void setExpriedTime(String expriedTime) {
        this.expriedTime = expriedTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    public Integer getYn() {
        return yn;
    }

    public void setYn(Integer yn) {
        this.yn = yn;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getSortWay() {
        return sortWay;
    }

    public void setSortWay(Integer sortWay) {
        this.sortWay = sortWay;
    }

    public Integer getStartPoint() {
        return startPoint;
    }

    public void setStartPoint(Integer startPoint) {
        this.startPoint = startPoint;
    }

    public Integer getEndPoint() {
        return endPoint;
    }

    public void setEndPoint(Integer endPoint) {
        this.endPoint = endPoint;
    }

    public String getLevelName() {
        return levelName;
    }

    public void setLevelName(String levelName) {
        this.levelName = levelName;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getEffectTime() {
        return effectTime;
    }

    public void setEffectTime(String effectTime) {
        this.effectTime = effectTime;
    }

    public String getSendCardTime() {
        return sendCardTime;
    }

    public void setSendCardTime(String sendCardTime) {
        this.sendCardTime = sendCardTime;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getPasswd() {
        return passwd;
    }

    public void setPasswd(String passwd) {
        this.passwd = passwd;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

	public String getCheckUser() {
		return checkUser;
	}

	public void setCheckUser(String checkUser) {
		this.checkUser = checkUser;
	}

	public String getCheckPassword() {
		return checkPassword;
	}

	public void setCheckPassword(String checkPassword) {
		this.checkPassword = checkPassword;
	}

	public String getMemberGuid() {
		return memberGuid;
	}

	public void setMemberGuid(String memberGuid) {
		this.memberGuid = memberGuid;
	}

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public BigDecimal getBonus() {
		return bonus;
	}

	public void setBonus(BigDecimal bonus) {
		this.bonus = bonus;
	}

	public Boolean getIsRefunded() {
		return isRefunded;
	}

	public void setIsRefunded(Boolean isRefunded) {
		this.isRefunded = isRefunded;
	}

	public Byte getPayType() {
		return payType;
	}

	public void setPayType(Byte payType) {
		this.payType = payType;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public String getFrom() {
		return from;
	}

	public void setFrom(String from) {
		this.from = from;
	}

    public Boolean getRefunded() {
        return isRefunded;
    }

    public void setRefunded(Boolean refunded) {
        isRefunded = refunded;
    }

    public String getHeadquartersOrganSign() {
        return headquartersOrganSign;
    }

    public void setHeadquartersOrganSign(String headquartersOrganSign) {
        this.headquartersOrganSign = headquartersOrganSign;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public Integer getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(Integer provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Integer getCityCode() {
        return cityCode;
    }

    public void setCityCode(Integer cityCode) {
        this.cityCode = cityCode;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public Integer getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(Integer areaCode) {
        this.areaCode = areaCode;
    }

    public Byte getSource() {
        return source;
    }

    public void setSource(Byte source) {
        this.source = source;
    }

    public Integer getWhiteList() {
        return whiteList;
    }

    public void setWhiteList(Integer whiteList) {
        this.whiteList = whiteList;
    }

    public Boolean getTelephoneChangeFlag() {
        return telephoneChangeFlag;
    }

    public void setTelephoneChangeFlag(Boolean telephoneChangeFlag) {
        this.telephoneChangeFlag = telephoneChangeFlag;
    }

    public Boolean getIdCardChangeFlag() {
        return idCardChangeFlag;
    }

    public void setIdCardChangeFlag(Boolean idCardChangeFlag) {
        this.idCardChangeFlag = idCardChangeFlag;
    }
    public List<MemberChronicConfigVo> getChronicPrefList() {
        return chronicPrefList;
    }

    public void setChronicPrefList(List<MemberChronicConfigVo> chronicPrefList) {
        this.chronicPrefList = chronicPrefList;
    }

    public String getChronicName() {
        return chronicName;
    }

    public void setChronicName(String chronicName) {
        this.chronicName = chronicName;
    }

    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    public Integer getPatient() {
        return patient;
    }

    public void setPatient(Integer patient) {
        this.patient = patient;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getBlood() {
        return blood;
    }

    public void setBlood(String blood) {
        this.blood = blood;
    }

    public String getMarriage() {
        return marriage;
    }

    public void setMarriage(String marriage) {
        this.marriage = marriage;
    }

    public String getHistoryOfAllergy() {
        return historyOfAllergy;
    }

    public void setHistoryOfAllergy(String historyOfAllergy) {
        this.historyOfAllergy = historyOfAllergy;
    }

    public String getJiWangBingShi() {
        return jiWangBingShi;
    }

    public void setJiWangBingShi(String jiWangBingShi) {
        this.jiWangBingShi = jiWangBingShi;
    }

    public String getJianKangXuQiu() {
        return jianKangXuQiu;
    }

    public void setJianKangXuQiu(String jianKangXuQiu) {
        this.jianKangXuQiu = jianKangXuQiu;
    }

    public String getYongYaoJinJi() {
        return yongYaoJinJi;
    }

    public void setYongYaoJinJi(String yongYaoJinJi) {
        this.yongYaoJinJi = yongYaoJinJi;
    }

    public String getChangYongYao() {
        return changYongYao;
    }

    public void setChangYongYao(String changYongYao) {
        this.changYongYao = changYongYao;
    }

    public String getProfession() {
        return profession;
    }

    public void setProfession(String profession) {
        this.profession = profession;
    }

    public String getMailBox() {
        return mailBox;
    }

    public void setMailBox(String mailBox) {
        this.mailBox = mailBox;
    }

    public String getGuanXinBing() {
        return guanXinBing;
    }

    public void setGuanXinBing(String guanXinBing) {
        this.guanXinBing = guanXinBing;
    }

    public String getCarteVital() {
        return carteVital;
    }

    public void setCarteVital(String carteVital) {
        this.carteVital = carteVital;
    }

    public String getIdCards() {
        return idCards;
    }

    public void setIdCards(String idCards) {
        this.idCards = idCards;
    }

    public String getPatientCase() {
        return patientCase;
    }

    public void setPatientCase(String patientCase) {
        this.patientCase = patientCase;
    }

    public String getApplyForCard() {
        return applyForCard;
    }

    public void setApplyForCard(String applyForCard) {
        this.applyForCard = applyForCard;
    }

    public String getLetterOfNotice() {
        return letterOfNotice;
    }

    public void setLetterOfNotice(String letterOfNotice) {
        this.letterOfNotice = letterOfNotice;
    }

    public String getOthers() {
        return others;
    }

    public void setOthers(String others) {
        this.others = others;
    }

    public MemberArchivesInfoDto getArchivesInfo() {
        return archivesInfo;
    }

    public void setArchivesInfo(MemberArchivesInfoDto archivesInfo) {
        this.archivesInfo = archivesInfo;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }
}