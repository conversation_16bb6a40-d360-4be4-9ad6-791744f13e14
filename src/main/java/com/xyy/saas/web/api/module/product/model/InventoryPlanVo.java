package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import org.joda.time.LocalDate;

import javax.validation.Valid;
import javax.validation.constraints.Pattern;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * InventoryPlanVo
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-29T18:34:31.623+08:00")

public class InventoryPlanVo   {
  @JsonProperty("id")
  private Integer id = null;

  @JsonProperty("pref")
  private String pref = null;

  @JsonProperty("createUser")
  private String createUser = null;

  @JsonProperty("createTime")
  private LocalDate createTime = null;

  @JsonProperty("updateUser")
  private String updateUser = null;

  @JsonProperty("updateTime")
  private LocalDate updateTime = null;

  @JsonProperty("remark")
  private String remark = null;

  @JsonProperty("status")
  private Integer status = null;

  @JsonProperty("yn")
  private byte[] yn = null;

  @JsonProperty("baseVersion")
  private String baseVersion = null;

  @JsonProperty("organsign")
  private String organsign = null;

  @JsonProperty("ticketUser")
  private String ticketUser = null;

  @JsonProperty("ticketTime")
  private String ticketTime = null;

  @JsonProperty("extractStatus")
  private String extractStatus = null;

  @JsonProperty("positionPref")
  private String positionPref = null;

  @JsonProperty("inventoryPlanDetailVos")
  private List<InventoryPlanDetailVo> inventoryPlanDetailVos = null;

  public InventoryPlanVo id(Integer id) {
    this.id = id;
    return this;
  }

   /**
   * 主键id
   * @return id
  **/
  @ApiModelProperty(value = "主键id")


  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public InventoryPlanVo pref(String pref) {
    this.pref = pref;
    return this;
  }

   /**
   * 编号
   * @return pref
  **/
  @ApiModelProperty(value = "编号")


  public String getPref() {
    return pref;
  }

  public void setPref(String pref) {
    this.pref = pref;
  }

  public InventoryPlanVo createUser(String createUser) {
    this.createUser = createUser;
    return this;
  }

   /**
   * 创建人
   * @return createUser
  **/
  @ApiModelProperty(value = "创建人")


  public String getCreateUser() {
    return createUser;
  }

  public void setCreateUser(String createUser) {
    this.createUser = createUser;
  }

  public InventoryPlanVo createTime(LocalDate createTime) {
    this.createTime = createTime;
    return this;
  }

   /**
   * 创建时间
   * @return createTime
  **/
  @ApiModelProperty(value = "创建时间")

  @Valid

  public LocalDate getCreateTime() {
    return createTime;
  }

  public void setCreateTime(LocalDate createTime) {
    this.createTime = createTime;
  }

  public InventoryPlanVo updateUser(String updateUser) {
    this.updateUser = updateUser;
    return this;
  }

   /**
   * 更新人
   * @return updateUser
  **/
  @ApiModelProperty(value = "更新人")


  public String getUpdateUser() {
    return updateUser;
  }

  public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
  }

  public InventoryPlanVo updateTime(LocalDate updateTime) {
    this.updateTime = updateTime;
    return this;
  }

   /**
   * 更新时间
   * @return updateTime
  **/
  @ApiModelProperty(value = "更新时间")

  @Valid

  public LocalDate getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(LocalDate updateTime) {
    this.updateTime = updateTime;
  }

  public InventoryPlanVo remark(String remark) {
    this.remark = remark;
    return this;
  }

   /**
   * 备注
   * @return remark
  **/
  @ApiModelProperty(value = "备注")


  public String getRemark() {
    return remark;
  }

  public void setRemark(String remark) {
    this.remark = remark;
  }

  public InventoryPlanVo status(Integer status) {
    this.status = status;
    return this;
  }

   /**
   * 状态
   * @return status
  **/
  @ApiModelProperty(value = "状态")


  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public InventoryPlanVo yn(byte[] yn) {
    this.yn = yn;
    return this;
  }

   /**
   * 是否删除
   * @return yn
  **/
  @ApiModelProperty(value = "是否删除")

 @Pattern(regexp="^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$")
  public byte[] getYn() {
    return yn;
  }

  public void setYn(byte[] yn) {
    this.yn = yn;
  }

  public InventoryPlanVo baseVersion(String baseVersion) {
    this.baseVersion = baseVersion;
    return this;
  }

   /**
   * 版本号
   * @return baseVersion
  **/
  @ApiModelProperty(value = "版本号")


  public String getBaseVersion() {
    return baseVersion;
  }

  public void setBaseVersion(String baseVersion) {
    this.baseVersion = baseVersion;
  }

  public InventoryPlanVo organsign(String organsign) {
    this.organsign = organsign;
    return this;
  }

   /**
   * 机构代码
   * @return organsign
  **/
  @ApiModelProperty(value = "机构代码")


  public String getOrgansign() {
    return organsign;
  }

  public void setOrgansign(String organsign) {
    this.organsign = organsign;
  }

  public InventoryPlanVo ticketUser(String ticketUser) {
    this.ticketUser = ticketUser;
    return this;
  }

   /**
   * 开票员
   * @return ticketUser
  **/
  @ApiModelProperty(value = "开票员")


  public String getTicketUser() {
    return ticketUser;
  }

  public void setTicketUser(String ticketUser) {
    this.ticketUser = ticketUser;
  }

  public InventoryPlanVo ticketTime(String ticketTime) {
    this.ticketTime = ticketTime;
    return this;
  }

   /**
   * 开票时间
   * @return ticketTime
  **/
  @ApiModelProperty(value = "开票时间")


  public String getTicketTime() {
    return ticketTime;
  }

  public void setTicketTime(String ticketTime) {
    this.ticketTime = ticketTime;
  }

  public InventoryPlanVo extractStatus(String extractStatus) {
    this.extractStatus = extractStatus;
    return this;
  }

   /**
   * 提取状态
   * @return extractStatus
  **/
  @ApiModelProperty(value = "提取状态")


  public String getExtractStatus() {
    return extractStatus;
  }

  public void setExtractStatus(String extractStatus) {
    this.extractStatus = extractStatus;
  }

  public InventoryPlanVo positionPref(String positionPref) {
    this.positionPref = positionPref;
    return this;
  }

   /**
   * 商品架位编号
   * @return positionPref
  **/
  @ApiModelProperty(value = "商品架位编号")


  public String getPositionPref() {
    return positionPref;
  }

  public void setPositionPref(String positionPref) {
    this.positionPref = positionPref;
  }

  public InventoryPlanVo inventoryPlanDetailVos(List<InventoryPlanDetailVo> inventoryPlanDetailVos) {
    this.inventoryPlanDetailVos = inventoryPlanDetailVos;
    return this;
  }

  public InventoryPlanVo addInventoryPlanDetailVosItem(InventoryPlanDetailVo inventoryPlanDetailVosItem) {
    if (this.inventoryPlanDetailVos == null) {
      this.inventoryPlanDetailVos = new ArrayList<InventoryPlanDetailVo>();
    }
    this.inventoryPlanDetailVos.add(inventoryPlanDetailVosItem);
    return this;
  }

   /**
   * 调整明细信息
   * @return inventoryPlanDetailVos
  **/
  @ApiModelProperty(value = "调整明细信息")

  @Valid

  public List<InventoryPlanDetailVo> getInventoryPlanDetailVos() {
    return inventoryPlanDetailVos;
  }

  public void setInventoryPlanDetailVos(List<InventoryPlanDetailVo> inventoryPlanDetailVos) {
    this.inventoryPlanDetailVos = inventoryPlanDetailVos;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InventoryPlanVo inventoryPlanVo = (InventoryPlanVo) o;
    return Objects.equals(this.id, inventoryPlanVo.id) &&
        Objects.equals(this.pref, inventoryPlanVo.pref) &&
        Objects.equals(this.createUser, inventoryPlanVo.createUser) &&
        Objects.equals(this.createTime, inventoryPlanVo.createTime) &&
        Objects.equals(this.updateUser, inventoryPlanVo.updateUser) &&
        Objects.equals(this.updateTime, inventoryPlanVo.updateTime) &&
        Objects.equals(this.remark, inventoryPlanVo.remark) &&
        Objects.equals(this.status, inventoryPlanVo.status) &&
        Objects.equals(this.yn, inventoryPlanVo.yn) &&
        Objects.equals(this.baseVersion, inventoryPlanVo.baseVersion) &&
        Objects.equals(this.organsign, inventoryPlanVo.organsign) &&
        Objects.equals(this.ticketUser, inventoryPlanVo.ticketUser) &&
        Objects.equals(this.ticketTime, inventoryPlanVo.ticketTime) &&
        Objects.equals(this.extractStatus, inventoryPlanVo.extractStatus) &&
        Objects.equals(this.positionPref, inventoryPlanVo.positionPref) &&
        Objects.equals(this.inventoryPlanDetailVos, inventoryPlanVo.inventoryPlanDetailVos);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, pref, createUser, createTime, updateUser, updateTime, remark, status, yn, baseVersion, organsign, ticketUser, ticketTime, extractStatus, positionPref, inventoryPlanDetailVos);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InventoryPlanVo {\n");

    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    pref: ").append(toIndentedString(pref)).append("\n");
    sb.append("    createUser: ").append(toIndentedString(createUser)).append("\n");
    sb.append("    createTime: ").append(toIndentedString(createTime)).append("\n");
    sb.append("    updateUser: ").append(toIndentedString(updateUser)).append("\n");
    sb.append("    updateTime: ").append(toIndentedString(updateTime)).append("\n");
    sb.append("    remark: ").append(toIndentedString(remark)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    yn: ").append(toIndentedString(yn)).append("\n");
    sb.append("    baseVersion: ").append(toIndentedString(baseVersion)).append("\n");
    sb.append("    organsign: ").append(toIndentedString(organsign)).append("\n");
    sb.append("    ticketUser: ").append(toIndentedString(ticketUser)).append("\n");
    sb.append("    ticketTime: ").append(toIndentedString(ticketTime)).append("\n");
    sb.append("    extractStatus: ").append(toIndentedString(extractStatus)).append("\n");
    sb.append("    positionPref: ").append(toIndentedString(positionPref)).append("\n");
    sb.append("    inventoryPlanDetailVos: ").append(toIndentedString(inventoryPlanDetailVos)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

