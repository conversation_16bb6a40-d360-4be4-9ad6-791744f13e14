package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.member.core.api.MemberPointExchangeForZBApi;
import com.xyy.saas.member.core.dto.MemberExchangeProductDto;
import com.xyy.saas.member.core.dto.SpecialProductDto;
import com.xyy.saas.product.core.api.ProductApi;
import com.xyy.saas.product.core.dto.MemberDayMixResultDto;
import com.xyy.saas.product.core.dto.MemberDayQueryDto;
import com.xyy.saas.product.core.dto.ProductDto;
import com.xyy.saas.web.api.module.member.model.MemberExchangeProductVo;
import com.xyy.saas.web.api.module.member.model.SpecialProductUpdateVo;
import com.xyy.saas.web.api.module.utils.ExportExcelUtil;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.aspectj.apache.bcel.Constants;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Controller
@RequestMapping("/member/memberPointExchange/zb")
@Api(value = "memberPointExchangeForZB", description = "总部端商品积分规则接口")
public class MemberPointExchangeForZBApiController {
    private static final Logger logger = LogManager.getLogger(MemberPointExchangeForZBApiController.class);
    @Reference(version = "0.0.1")
    private MemberPointExchangeForZBApi memberPointExchangeForZBApi;

    @Reference(version = "0.0.1")
    private ProductApi productApi;

    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;

    @ApiOperation(value = "总部端:商品积分规则列表", notes = "总部端:商品积分规则列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberExchangeProductDto.class) })
    @RequestMapping(value = "/getPointProductPager", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> getPointProductPagerZB(@RequestHeader("commonRequestModel") String commonRequestModel, @RequestBody MemberExchangeProductVo vo){
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        MemberExchangeProductDto exchangeProductDto = new MemberExchangeProductDto();
        BeanUtils.copyProperties(vo, exchangeProductDto);
        exchangeProductDto.setScoreProductYn(vo.getScoreProductYn());
        exchangeProductDto.setProductName(vo.getCommonName());
        if (!StringUtils.isEmpty(vo.getOrganSign())) {
            //总部查询门店
            SaaSDrugstoreDto drugstore = drugstoreApi.getDrugstoreByOrganSign(vo.getOrganSign());
            exchangeProductDto.setBizModel(drugstore.getBizModel());
            exchangeProductDto.setOrganSign(drugstore.getOrganSign());
            exchangeProductDto.setHeadquartersOrganSign(drugstore.getHeadquartersOrganSign());
            exchangeProductDto.setOrganSignType(drugstore.getOrganSignType());
        } else {
            //总部查询全部
            exchangeProductDto.setBizModel(model.getBizModel());
            exchangeProductDto.setOrganSign(model.getOrganSign());
            exchangeProductDto.setHeadquartersOrganSign(model.getHeadquartersOrganSign());
            exchangeProductDto.setOrganSignType(model.getOrganSignType());
        }
        PageInfo pageInfo = memberPointExchangeForZBApi.getPointProductListPagerZB(exchangeProductDto);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(pageInfo), HttpStatus.OK);
    }

    @ApiOperation(value = "总部端:保存商品积分规则", notes = "总部端:保存商品积分规则")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberExchangeProductDto.class) })
    @RequestMapping(value = "/savePointProduct", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> updateSpecialProduct(@RequestHeader("commonRequestModel") String commonRequestModel, @RequestBody MemberExchangeProductVo vo){
//        if(vo.getProductUpdateDtos() == null){
//            return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.ERROR,"商品编号列表为空"),HttpStatus.OK);
//        }
        int result = memberPointExchangeForZBApi.batchUpdateSpecialProductInfo(vo.getProductUpdateDtos());
        if(result<=0){
            return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.ERROR,"没发生异常，但是更新失败，需要排查原因"),HttpStatus.OK);
        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(ResultCodeEnum.SUCCESS),HttpStatus.OK);
    }

    @ApiOperation(value = "总部端:商品积分规则导出", notes = "总部端:商品积分规则导出")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功") })
    @RequestMapping(value = "/exportProductIntegral",  method = RequestMethod.POST)
    public void exportProductIntegral(@RequestHeader("commonRequestModel") String commonRequestModel, HttpServletRequest request, HttpServletResponse response,
                                      @RequestBody MemberExchangeProductVo vo){
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        MemberExchangeProductDto exchangeProductDto = new MemberExchangeProductDto();
        BeanUtils.copyProperties(vo, exchangeProductDto);
        exchangeProductDto.setScoreProductYn(vo.getScoreProductYn());
        exchangeProductDto.setProductName(vo.getCommonName());
        if (!StringUtils.isEmpty(vo.getOrganSign())) {
            //总部查询门店
            SaaSDrugstoreDto drugstore = drugstoreApi.getDrugstoreByOrganSign(vo.getOrganSign());
            exchangeProductDto.setOrganSign(drugstore.getOrganSign());
            exchangeProductDto.setBizModel(drugstore.getBizModel());
            exchangeProductDto.setHeadquartersOrganSign(drugstore.getHeadquartersOrganSign());
            exchangeProductDto.setOrganSignType(drugstore.getOrganSignType());
        } else {
            //总部查询全部
            exchangeProductDto.setBizModel(model.getBizModel());
            exchangeProductDto.setOrganSign(model.getOrganSign());
            exchangeProductDto.setHeadquartersOrganSign(model.getHeadquartersOrganSign());
            exchangeProductDto.setOrganSignType(model.getOrganSignType());
        }
        exchangeProductDto.setPageNum(1);
        exchangeProductDto.setPageSize(100000);
        PageInfo pageInfo = memberPointExchangeForZBApi.getPointProductListPagerZB(exchangeProductDto);
        List<ProductDto> list = pageInfo.getList();
        if (list != null && !list.isEmpty()) {
            list.stream().forEach(dto -> {
                if (dto.getScoreProductYn() == Constants.ATTR_CONSTANT_VALUE) {
                    dto.setBarCode("是");
                }else {
                    dto.setBarCode("否");
                }
                //保留一位小数
                dto.setScoreRate(dto.getScoreRate().setScale(1, BigDecimal.ROUND_HALF_UP));
            });
        }
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
        String extfilename="商品积分规则"+df.format(new Date())+".xls";
        String sheetName = "商品积分规则";
        String headers[];
        String fieldNames[];
        if (model.getBizModel() == 3 && model.getOrganSignType() == 3) {
            headers = new String[]{"通用名", "规格", "单位", "商品名称", "商品编号", "标准库ID", "生产厂家","所属门店", "商品分类", "是否为积分商品", "积分倍数"};
            fieldNames = new String[]{"commonName", "attributeSpecification", "unitName", "productName", "pharmacyPref", "standardLibraryId", "manufacturer", "drugstoreName","systemTypeName", "barCode", "scoreRate"};

        } else {
            headers = new String[]{"通用名", "规格", "单位", "商品名称", "商品编号", "标准库ID", "生产厂家", "商品分类", "是否为积分商品", "积分倍数"};
            fieldNames = new String[]{"commonName", "attributeSpecification", "unitName", "productName", "pharmacyPref", "standardLibraryId", "manufacturer", "systemTypeName", "barCode", "scoreRate"};
        }
       try {
            ExportExcelUtil.createExcel(response,request,extfilename,sheetName, headers, fieldNames, list,true);
        } catch (Exception e) {
            logger.error("MemberPointExchangeZBApiController exportRecordExcel is error.", e);
        }
    }
}
