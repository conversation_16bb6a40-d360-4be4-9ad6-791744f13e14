package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * @annotation:健康笔记提供接口参数实体
 * <AUTHOR>
 * @create 2019-05-08 19:19
 **/
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-05-08T11:40:30.801+08:00")
public class InventoryOrganSignDto implements Serializable {

    private static final long serialVersionUID = 3158011339135407620L;
    /*机构编码 */
    @JsonProperty("organSign")
    private String organSign;
    /*商品编号及库存信息*/
    @JsonProperty("inventoryDetails")
    private List<InventoryBaseDto> inventoryDetails;
    @ApiModelProperty(value = "机构编号")
    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }
    @ApiModelProperty(value = "商品编号库存数量明细")

    public List<InventoryBaseDto> getInventoryDetails() {
        return inventoryDetails;
    }

    public void setInventoryDetails(List<InventoryBaseDto> inventoryDetails) {
        this.inventoryDetails = inventoryDetails;
    }
}

