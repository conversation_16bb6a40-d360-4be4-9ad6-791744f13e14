package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.member.core.api.MemberChronicWarnRecordApi;
import com.xyy.saas.member.core.dto.MemberChronicWarnRecordQueryReqDto;
import com.xyy.saas.web.api.module.member.model.MemberChronicWarnRecordQueryReqVo;
import com.xyy.saas.web.api.module.member.model.MemberChronicWarnRecordVo;
import com.xyy.saas.web.api.module.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import com.xyy.saas.common.util.ResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * @Description 会员慢病提醒记录API
 * <AUTHOR>
 * @Create 2020-09-24 16:15
 * @menu 会员慢病提醒记录API
 */
@Controller
@RequestMapping(value = "/member/memberChronicWarnRecord")
@Api(value = "memberChronicWarnRecord", description = "会员慢病提醒记录API")
public class MemberChronicWarnRecordApiController {

    private static final Logger logger = LoggerFactory.getLogger(MemberChronicWarnRecordApiController.class);

    @Reference(version = "0.0.1")
    private MemberChronicWarnRecordApi memberChronicWarnRecordApi;

    /**
     * 门店机构信息
     */
    @ApiOperation(value = "分页查询会员慢病提醒记录", notes = "分页查询会员慢病提醒记录", response = MemberChronicWarnRecordVo.class, tags={ "会员慢病提醒记录", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberChronicWarnRecordVo.class) })
    @RequestMapping(value = "/queryChronicWarnRecordPage",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> queryChronicWarnRecordPage(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                    @RequestBody MemberChronicWarnRecordQueryReqVo queryVo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        //组装Dto参数
        MemberChronicWarnRecordQueryReqDto queryDto = new MemberChronicWarnRecordQueryReqDto();
        BeanUtils.copyProperties(queryVo, queryDto);

        if (StringUtils.isNotNullAndEmpty(commonRequestModel.getHeadquartersOrganSign())) {
            if (commonRequestModel.getHeadquartersOrganSign().equals(commonRequestModel.getOrganSign())) {
                queryDto.setHeadquartersOrganSign(commonRequestModel.getHeadquartersOrganSign());
            } else {
                queryDto.setHeadquartersOrganSign(commonRequestModel.getHeadquartersOrganSign());
                queryDto.setOrganSign(commonRequestModel.getOrganSign());
            }
        } else {
            queryDto.setOrganSign(commonRequestModel.getOrganSign());
        }

        try {
            PageInfo pageInfo = memberChronicWarnRecordApi.queryChronicWarnRecordPage(queryDto);
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(pageInfo),HttpStatus.OK);
        } catch (Exception e) {
            logger.error("分页查询会员慢病提醒记录 {}", JSONObject.toJSONString(queryDto), e);
        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(null),HttpStatus.OK);
    }

    /**
     * @param commonRequestModelStr 机构信息
     * @param queryVo 查询条件
     * @return
     */
    @ApiOperation(value = "导出会员慢病提醒记录", notes = "导出会员慢病提醒记录", response = MemberChronicWarnRecordVo.class, tags={ "会员慢病提醒记录", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberChronicWarnRecordVo.class) })
    @RequestMapping(value = "/exportExcel",
            method = RequestMethod.POST)
    public void exportChronicWarnRecordExcel(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                        @RequestBody MemberChronicWarnRecordQueryReqVo queryVo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        //组装Dto参数
        MemberChronicWarnRecordQueryReqDto queryDto = new MemberChronicWarnRecordQueryReqDto();
        BeanUtils.copyProperties(queryVo, queryDto);
        queryDto.setOrganSign(commonRequestModel.getOrganSign());
        //待对接电驴
    }


    //<editor-folder desc="Yapi接口文档">
//    @ApiOperation(value = "分页查询会员慢病提醒记录", notes = "分页查询会员慢病提醒记录", response = MemberChronicWarnRecordVo.class, tags={ "会员慢病提醒记录", })
//    @ApiResponses(value = {
//            @ApiResponse(code = 200, message = "操作成功", response = MemberChronicWarnRecordVo.class) })
//    @RequestMapping(value = "/queryChronicWarnRecordPage",
//            method = RequestMethod.POST)
//    public ResultVO<PageInfo<MemberChronicWarnRecordVo>> queryChronicWarnRecordPage(@RequestHeader("commonRequestModel") String commonRequestModelStr,
//                                                                             @RequestBody MemberChronicWarnRecordQueryReqVo queryVo) {
//        return null;
//    }
//    /**
//     * @param commonRequestModelStr 机构信息
//     * @param queryVo 查询条件
//     * @return
//     */
//    @ApiOperation(value = "导出会员慢病提醒记录", notes = "导出会员慢病提醒记录", response = MemberChronicWarnRecordVo.class, tags={ "会员慢病提醒记录", })
//    @ApiResponses(value = {
//            @ApiResponse(code = 200, message = "操作成功", response = MemberChronicWarnRecordVo.class) })
//    @RequestMapping(value = "/exportExcel",
//            method = RequestMethod.POST)
//    public void exportChronicWarnRecordExcel(@RequestHeader("commonRequestModel") String commonRequestModelStr,
//                                                          @RequestBody MemberChronicWarnRecordQueryReqVo queryVo) {
//    }
    //<editor-folder>




}
