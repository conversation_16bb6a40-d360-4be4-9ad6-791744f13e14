package com.xyy.saas.web.api.module.product.model;

import com.xyy.saas.medical.core.dto.SaasMedicalProductDto;
import io.swagger.annotations.ApiModel;

import java.util.List;

/**
 * <AUTHOR>
 * @title: UploadMedicalVO
 * @date 2019-09-11  19:06
 * @description: TODO
 */
@ApiModel(description = "UploadMedicalVO 上传医保商品类")
public class UploadMedicalVO {

    String organSign;

    List<MedicalProductModel> medicalProductList;

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public List<MedicalProductModel> getMedicalProductList() {
        return medicalProductList;
    }

    public void setMedicalProductList(List<MedicalProductModel> medicalProductList) {
        this.medicalProductList = medicalProductList;
    }
}
