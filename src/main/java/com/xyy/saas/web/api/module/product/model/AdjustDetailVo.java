package com.xyy.saas.web.api.module.product.model;

import java.math.BigDecimal;

/**
 * @ClassName AdjustDetailVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/1/14 15:02
 * @Version 1.0
 **/
public class AdjustDetailVo {

    private Long id;//编辑时必传
    private String pref;//":"调价单单据编号",
    private String pharmacyPref;//":"药店商品编号",
    private String productPref;//商品内部编号
    private String commonName;//":"商品通用名称",
    private String productName;//":"商品名称",
    private String attributeSpecification;//":"规格",
    private String jixingName;//":"剂型名称",
    private String unitName;//":"单位名称",
    private String manufacturer;//":"生产厂家",
    private String producingArea;//":"产地",
    private BigDecimal retailPrice;//":"原零售价",
    private BigDecimal vipPrice;//":"原会员价",
    private BigDecimal newPrice;//":"新零售价",
    private BigDecimal newMemberPrice;//":"新会员价"
    private String dosageFormName;//剂型名称

    // 连锁总部审核连锁门店调价申请单合理性
    private BigDecimal minTaxPrice; // 最低出货价
    private BigDecimal avgTaxPrice; // 平均出货价
    private BigDecimal latestTaxPrice; // 最后一次出货价
    private Boolean newPriceErrorFlag; // 调整后单价是否有异常标志（true表示有异常）
    private Boolean newMemberPriceErrorFlag; // 调整后会员价格是否有异常标志（true表示有异常）

    public BigDecimal getMinTaxPrice() {
        return minTaxPrice;
    }

    public void setMinTaxPrice(BigDecimal minTaxPrice) {
        this.minTaxPrice = minTaxPrice;
    }

    public BigDecimal getAvgTaxPrice() {
        return avgTaxPrice;
    }

    public void setAvgTaxPrice(BigDecimal avgTaxPrice) {
        this.avgTaxPrice = avgTaxPrice;
    }

    public BigDecimal getLatestTaxPrice() {
        return latestTaxPrice;
    }

    public void setLatestTaxPrice(BigDecimal latestTaxPrice) {
        this.latestTaxPrice = latestTaxPrice;
    }

    public Boolean getNewPriceErrorFlag() {
        return newPriceErrorFlag;
    }

    public void setNewPriceErrorFlag(Boolean newPriceErrorFlag) {
        this.newPriceErrorFlag = newPriceErrorFlag;
    }

    public Boolean getNewMemberPriceErrorFlag() {
        return newMemberPriceErrorFlag;
    }

    public void setNewMemberPriceErrorFlag(Boolean newMemberPriceErrorFlag) {
        this.newMemberPriceErrorFlag = newMemberPriceErrorFlag;
    }

    public String getSystemTypeName() {
        return systemTypeName;
    }

    public void setSystemTypeName(String systemTypeName) {
        this.systemTypeName = systemTypeName;
    }

    private String systemTypeName;//商品分类
    public String getDosageFormName() {
        return dosageFormName;
    }

    public void setDosageFormName(String dosageFormName) {
        this.dosageFormName = dosageFormName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    public String getPharmacyPref() {
        return pharmacyPref;
    }

    public void setPharmacyPref(String pharmacyPref) {
        this.pharmacyPref = pharmacyPref;
    }

    public String getProductPref() {
        return productPref;
    }

    public void setProductPref(String productPref) {
        this.productPref = productPref;
    }

    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getAttributeSpecification() {
        return attributeSpecification;
    }

    public void setAttributeSpecification(String attributeSpecification) {
        this.attributeSpecification = attributeSpecification;
    }

    public String getJixingName() {
        return jixingName;
    }

    public void setJixingName(String jixingName) {
        this.jixingName = jixingName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getProducingArea() {
        return producingArea;
    }

    public void setProducingArea(String producingArea) {
        this.producingArea = producingArea;
    }

    public BigDecimal getRetailPrice() {
        return retailPrice;
    }

    public void setRetailPrice(BigDecimal retailPrice) {
        this.retailPrice = retailPrice;
    }

    public BigDecimal getVipPrice() {
        return vipPrice;
    }

    public void setVipPrice(BigDecimal vipPrice) {
        this.vipPrice = vipPrice;
    }

    public BigDecimal getNewPrice() {
        return newPrice;
    }

    public void setNewPrice(BigDecimal newPrice) {
        this.newPrice = newPrice;
    }

    public BigDecimal getNewMemberPrice() {
        return newMemberPrice;
    }

    public void setNewMemberPrice(BigDecimal newMemberPrice) {
        this.newMemberPrice = newMemberPrice;
    }

}
