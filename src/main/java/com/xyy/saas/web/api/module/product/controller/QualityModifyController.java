package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.common.module.api.SysConfigApi;
import com.xyy.common.module.dto.SystemConfigWEBDto;
import com.xyy.saas.common.api.ExportExcelApi;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.api.ProductApi;
import com.xyy.saas.product.core.api.ProductUpdateMsgQualityModifyApi;
import com.xyy.saas.product.core.dto.*;
import com.xyy.saas.web.api.module.product.model.*;
import com.xyy.saas.web.api.module.product.util.CheckNull;
import com.xyy.saas.web.api.module.utils.ExportExcelUtil;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.api.EmployeefileApi;
import com.xyy.user.module.api.RoleApi;
import com.xyy.user.module.dto.EmployeeListRequestModel;
import com.xyy.user.module.dto.EmployeefileRequestModel;
import com.xyy.user.module.dto.result.EmployeeDto;
import com.xyy.user.module.dto.result.QueryEmployeePrimaryVO;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-07T16:41:52.348+08:00")

@Controller
public class QualityModifyController implements QualityModifyApi {

    private static final Logger logger = LoggerFactory.getLogger(QualityModifyController.class);

    @Reference(version = "0.0.1")
    private ProductUpdateMsgQualityModifyApi productUpdateMsgQualityModifyApi;
    @Reference(version = "0.0.1")
    private EmployeeApi employeeApi;
    @Reference(version = "0.0.1")
    private EmployeefileApi employeefileApi;
    @Reference(version = "0.0.1")
    private RoleApi roleApi;
    @Reference(version = "0.0.3")
    public ExportExcelApi exportExcelApi;

    @Reference(version = "0.0.1")
    private SysConfigApi sysConfigApi;

    @Reference(version = "0.0.1")
    private ProductApi productApi;

    @Value("${ftp.remotePath}")
    public String remotePath;

    @Value("${ftp.downLoadUrl}")
    public String downLoadUrl;

    @Value("${ftp.serverName}")
    public String serverName;

    @Value("${ftp.port}")
    public String port;

    @Value("${ftp.username}")
    public String username;

    @Value("${ftp.password}")
    public String password;

    @Override
    public ResponseEntity<ResultVO> list(@RequestHeader(value = "organSign", required = true) String organSign, @RequestHeader(name = "employeeId", required = true) Integer employeeId, @RequestBody SaasProductUpdatemsgListDto listDto) {
        logger.info("质量信息变更列表入参+" + JSONObject.toJSONString(listDto));
        ProductUpdatemsgListDto productUpdatemsgListDto = new ProductUpdatemsgListDto();
        BeanUtils.copyProperties(listDto, productUpdatemsgListDto);
        productUpdatemsgListDto.setBeginTime(listDto.getStartTime());
        productUpdatemsgListDto.setOrganSign(organSign);
        ResultVO<EmployeeDto> employeeDtoResultVO = employeeApi.queryEmployeeById(employeeId);
        ResultVO<List<Integer>> listResultVO = roleApi.queryRoleIdsByEmployeeId(employeeId);
        productUpdatemsgListDto.setRolesIds(listResultVO.getResult());
        logger.info("employeeId:"+employeeId+",roleListsids:"+listResultVO.getResult());
        if (employeeDtoResultVO.getCode() == 0) {
            logger.info("用户信息:" + JSONObject.toJSONString(employeeDtoResultVO));
            productUpdatemsgListDto.setIdentity(Integer.valueOf(employeeDtoResultVO.getResult().getIdentity()));
        } else {
            productUpdatemsgListDto.setIdentity(0);
        }
        ResultVO<PageInfo> resultVO = productUpdateMsgQualityModifyApi.list(productUpdatemsgListDto);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> echo(HttpServletRequest request, @RequestHeader(value = "organSign", required = true) String organSign, @RequestBody SaasProductUpdatemsgEchoDto echoDto) {
        ResultVO<SaasProductUpdatemsgEchoVoDto> echo = productUpdateMsgQualityModifyApi.echo(echoDto.getGuid());
        String employeeId = request.getHeader("employeeId");
        ResultVO<List<Integer>> listResultVO = roleApi.queryRoleIdsByEmployeeId(Integer.parseInt(employeeId));
        if(listResultVO != null && listResultVO.getResult().contains(echo.getResult().getTodoRoleId())){
            echo.getResult().setCanCheck(1);
        }else{
            echo.getResult().setCanCheck(0);
        }
        return new ResponseEntity<ResultVO>(echo, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> save(@RequestHeader(value = "organSign", required = true) String organSign, @RequestHeader(name = "employeeId", required = true) Integer employeeId, @RequestBody SaasProductUpdatemsgSaveDto saveDto) {
        logger.info("质量信息变更审核入参+" + JSONObject.toJSONString(saveDto));
        //验参
        if ((null ==saveDto.getOptType() || saveDto.getOptType().intValue() == 1) && (saveDto.getList() == null || saveDto.getList().isEmpty()
                || saveDto.getUpdateContent() == null)) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "参数错误"), HttpStatus.OK);
        }else {
            if ((null !=saveDto.getOptType() && saveDto.getOptType().intValue() == 2) && saveDto.getUpdateContent() == null) {
                return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "参数错误"), HttpStatus.OK);
            }
        }

        //将是否置空标准库id的标识写进update_content,后面就可以识别了
        String s = CheckNull.nullToEmpty((Map<String, Object>) saveDto.getUpdateContent());
        JSONObject jo = JSON.parseObject(s);
        jo.put("resetStandardIdFlag",saveDto.getResetStandardIdFlag());
        s = jo.toJSONString();

        ProductUpdatemsgSaveDto dto = new ProductUpdatemsgSaveDto();
        BeanUtils.copyProperties(saveDto, dto);
        dto.setApplyUserId(employeeId);
        dto.setOrganSign(organSign);
        dto.setUpdateContent(s);
        ResultVO<EmployeeDto> employeeDtoResultVO = employeeApi.queryEmployeeById(employeeId);
        if (employeeDtoResultVO != null && employeeDtoResultVO.getCode() == 0 && employeeDtoResultVO.getResult() != null) {
            dto.setApplyUser(employeeDtoResultVO.getResult().getName());
        }
        ResultVO save = productUpdateMsgQualityModifyApi.save(dto);
        return new ResponseEntity<ResultVO>(save, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> audit(HttpServletRequest request,@RequestHeader(value = "organSign", required = true) String organSign, @RequestHeader(name = "employeeId", required = true) Integer employeeId, @RequestBody SaasProductUpdatemsgAuditDto auditDto) {
        logger.info("质量信息变更审核入参+" + JSONObject.toJSONString(auditDto));
        //验参
        if (auditDto == null || StringUtils.isBlank(auditDto.getGuid())
                || auditDto.getAuditState() == null || auditDto.getAuditState() == 0
                || auditDto.getAuditUserId() == null || auditDto.getAuditUserId() == 0
                || auditDto.getAuditTime() == null) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "参数错误"), HttpStatus.OK);
        }
        ProductUpdatemsgAuditDto dto = new ProductUpdatemsgAuditDto();
        ResultVO<EmployeeDto> employeeDtoResultVO = employeeApi.queryEmployeeById(auditDto.getAuditUserId());
        if (employeeDtoResultVO != null && employeeDtoResultVO.getCode() == 0 && employeeDtoResultVO.getResult() != null) {
            dto.setAuditUser(employeeDtoResultVO.getResult().getName());
        }
        BeanUtils.copyProperties(auditDto, dto);
        dto.setOrgansign(organSign);
        dto.setUpdateUser(String.valueOf(employeeId));
        String modelJson = request.getHeader("commonRequestModel");
        Byte bizModel = null;
        if(!org.springframework.util.StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            bizModel = model.getBizModel();
        }
        ResultVO audit = productUpdateMsgQualityModifyApi.audit(dto,bizModel);
        return new ResponseEntity<ResultVO>(audit, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> verifyPassword(@RequestHeader(value = "organSign", required = true) String organSign, @RequestBody SaasProductUpdatemsgVerifyPasswordDto passwordDto) {
        logger.info("密码验证+" + JSONObject.toJSONString(passwordDto));
        //验参
        if (passwordDto == null || passwordDto.getApplyUserId() == null
                || passwordDto.getApplyUserId() == 0 || StringUtils.isBlank(passwordDto.getPassword())) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "参数错误"), HttpStatus.OK);
        }
        ProductUpdatemsgVerifyPasswordDto dto = new ProductUpdatemsgVerifyPasswordDto();
        BeanUtils.copyProperties(passwordDto, dto);
        dto.setOrganSign(organSign);
        ResultVO resultVO = productUpdateMsgQualityModifyApi.verifyPassword(dto);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> qualityUser(@RequestHeader(value = "organSign", required = true) String organSign) {
        EmployeeListRequestModel model = new EmployeeListRequestModel();
        model.setRoleId(3);
        model.setPageNo(1);
        model.setPageSize(1000);
        ResultVO resultVO = employeeApi.queryEmployeeByCondition(model, organSign);
        ResultVO<SystemConfigWEBDto> webSysConfig = sysConfigApi.getWEBSysConfig(organSign);
        if (resultVO.getCode() != 0 || webSysConfig.getCode() != 0) {
            return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
        }
        PageInfo<QueryEmployeePrimaryVO> queryEmployeePrimaryVOPageInfo = (PageInfo<QueryEmployeePrimaryVO>) resultVO.getResult();
        if (queryEmployeePrimaryVOPageInfo.getList() == null || queryEmployeePrimaryVOPageInfo.getList().size() == 0) {
            return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
        }
        PageInfo<QueryDefaultEmployeeVO> defaultEmployeeVOPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(queryEmployeePrimaryVOPageInfo, defaultEmployeeVOPageInfo);
        List<QueryEmployeePrimaryVO> list = queryEmployeePrimaryVOPageInfo.getList();
        List<QueryDefaultEmployeeVO> employeeS = new ArrayList<>();
        for (QueryEmployeePrimaryVO queryEmployeePrimaryVO : list) {
            QueryDefaultEmployeeVO vo = new QueryDefaultEmployeeVO();
            BeanUtils.copyProperties(queryEmployeePrimaryVO, vo);
            if (webSysConfig.getResult().getQualityOwnersEmployeeId().equals(queryEmployeePrimaryVO.getEmployeeId())) {
                vo.setIsdefault(1);
            } else {
                vo.setIsdefault(0);
            }
            employeeS.add(vo);
        }
        defaultEmployeeVOPageInfo.setList(employeeS);
        return new ResponseEntity<ResultVO>(new ResultVO(defaultEmployeeVOPageInfo), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> applyUser(@RequestHeader(value = "organSign", required = true) String organSign) {

        ResultVO resultVO = productUpdateMsgQualityModifyApi.applyUser(organSign);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    @Override
    public void export(HttpServletRequest request, HttpServletResponse response,@RequestHeader(value = "organSign", required = true) String organSign, @RequestHeader(name = "employeeId", required = true) Integer employeeId, @RequestBody com.xyy.saas.web.api.module.product.model.SaasProductUpdatemsgExportDto equipmentExportDto) {
        String headers[] = new String[]{};
        String fieldNames[] = new String[]{};

        String extfilename = equipmentExportDto.getExcelName() + ".xls";
        headers = new String[]{"单据编号", "变更类型", "申请人", "申请时间", "审核状态"};
        fieldNames = new String[]{"recallNo", "updateTypeName", "applyUser", "applyTime", "auditStateName"};
        EmployeefileRequestModel employeefileRequestModel = new EmployeefileRequestModel();
        employeefileRequestModel.setOrganSign(organSign);
        employeefileRequestModel.setPageNum(1);
        employeefileRequestModel.setPageSize(100);
        List<EmployeeDto> eList = employeefileApi.getEmployeefileByCondition(employeefileRequestModel).getResult().getList();
        Map<Integer, String> eMap = eList.stream().collect(Collectors.toMap(EmployeeDto::getId, p -> p.getName()));

        ProductUpdatemsgListDto productUpdatemsgListDto = new ProductUpdatemsgListDto();
        BeanUtils.copyProperties(equipmentExportDto, productUpdatemsgListDto);
        String ids = equipmentExportDto.getIds();
        String[] split = ids.split(",");
        ArrayList<String> listIds = new ArrayList<String>(Arrays.asList(split)) ;
        ArrayList<Integer> integers = new ArrayList<>();
        for(String s:listIds){
            integers.add(Integer.parseInt(s));
        }
        productUpdatemsgListDto.setDetailIds(integers);
        productUpdatemsgListDto.setOrganSign(organSign);
        ResultVO<EmployeeDto> employeeDtoResultVO = employeeApi.queryEmployeeById(employeeId);
        if (employeeDtoResultVO.getCode() == 0) {
            productUpdatemsgListDto.setIdentity(Integer.valueOf(employeeDtoResultVO.getResult().getIdentity()));
        } else {
            productUpdatemsgListDto.setIdentity(0);
        }
        if (productUpdatemsgListDto.getIdentity().equals(1)) {
            List<String> prefs = productApi.getHiddenProductsByOrgansign(productUpdatemsgListDto.getOrganSign());
            productUpdatemsgListDto.setPrefs(prefs);
        }
        List<SaasProductUpdatemsgExportVo> list = productUpdateMsgQualityModifyApi.exportExcel(productUpdatemsgListDto);
        String sheetName = equipmentExportDto.getExcelName();

        String[] finalHeaders = headers;
        String[] finalFieldNames = fieldNames;
        try {
            ExportExcelUtil.createExcel(response, request, extfilename, sheetName, headers, fieldNames, list, true);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public ResponseEntity<ResultVO> batchModifyRecordlist(@RequestHeader(value = "organSign", required = true) String organSign, @ApiParam(value = "商品批量编辑记录", required = true) @RequestBody ProductBatchUpdatemsgRecordDto recordDto) {
        recordDto.setOrganSign(organSign);
        ResultVO resultVO = productApi.getProductBatchUpdatemsgRecord(recordDto);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    /**
     * 分页查询质量信息变更记录
     *
     * @param organSign
     * @param condition
     * @return
     */
    @Override
    public ResponseEntity<ResultVO<PageInfo<SaasProductUpdatemsgRecordDto>>> recordList(@RequestHeader(value = "organSign", required = true) String organSign, @RequestBody ProductUpdatemsgRecordCondition condition){
        condition.setOrganSign(organSign);
        ResultVO<PageInfo<SaasProductUpdatemsgRecordDto>> resultVO = productUpdateMsgQualityModifyApi.recordList(condition);
        return new ResponseEntity<>(resultVO, HttpStatus.OK);
    }

}


