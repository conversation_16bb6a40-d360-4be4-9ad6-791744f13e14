package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.manufactor.core.dto.ManufactorDto;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@Api(value = "4.0生产厂商基本信息API接口", description = "4.0生产厂商基本信息API接口")
@RequestMapping("/product")
public interface ManufactorInfoApi {


    @ApiOperation(value = "生产厂商保存和编辑提交接口", notes = "生产厂商保存和编辑提交接口", response = ResultVO.class, tags = {"4.0生产厂商基本信息API接口",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class)})
    @ResponseBody
    @RequestMapping(value = "/baseinfo/manufactor/save", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> addOrUpdate(HttpServletRequest request, @ApiParam(value = "生产厂商查询对象", required = true) @Valid @RequestBody ManufactorDto manufactor);

    @ApiOperation(value = "生产厂商信息查询列表接口", notes = "生产厂商信息查询列表接口", response = ResultVO.class, tags = {"4.0生产厂商基本信息API接口",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class)})
    @RequestMapping(value = "/baseinfo/manufactor/queryList", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> queryManufactorList(HttpServletRequest request, @ApiParam(value = "生产厂商查询对象", required = true) @Valid @RequestBody ManufactorDto manufactor);

    @ApiOperation(value = "生产厂商编辑数据信息回显接口", notes = "生产厂商编辑数据信息回显接口", response = ResultVO.class, tags = {"4.0生产厂商基本信息API接口",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class)})
    @ResponseBody
    @RequestMapping(value = "/baseinfo/manufactor/toUpdate", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getManufactorInfoById(HttpServletRequest request, @ApiParam(value = "生产厂商查询对象", required = true) @Valid @RequestBody ManufactorDto manufactor);

    @ApiOperation(value = "生产厂商删除接口", notes = "生产厂商删除接口", response = ResultVO.class, tags = {"4.0生产厂商基本信息API接口",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class)})
    @ResponseBody
    @RequestMapping(value = "/baseinfo/manufactor/delete", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> deleteManufactorInfoById(HttpServletRequest request, @ApiParam(value = "生产厂商查询对象", required = true) @Valid @RequestBody ManufactorDto manufactor);


    @ApiOperation(value = "生产厂商列表导出excel接口", notes = "生产厂商列表导出excel接口", response = ResultVO.class, tags = {"4.0生产厂商基本信息API接口",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功")})
    @RequestMapping(value = "/baseinfo/manufactor/exportExcel", method = RequestMethod.POST)
    public void exportExcelManufactor(HttpServletRequest request, HttpServletResponse response, @ApiParam(value = "生产厂商查询对象", required = true) @Valid @RequestBody ManufactorDto manufactor);

    /**
     * 生产厂商信息查询不分页
     * @param
     * @return
     */
    @ApiOperation(value = "生产厂商信息查询", notes = "生产厂商信息查询", response = ResultVO.class, tags = {"4.0生产厂商基本信息API接口",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class)})
    @RequestMapping(value = "/baseinfo/manufactor/queryListNoPage", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> queryManufactorListNoPage(HttpServletRequest request, @ApiParam(value = "生产厂商查询对象", required = true) @Valid @RequestBody ManufactorDto manufactor);


}