package com.xyy.saas.web.api.module.supplier.controller;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.supplier.dto.ProviderAreaInfoDto;
import com.xyy.saas.web.api.module.supplier.model.AreaBaseinfo;
import com.xyy.saas.web.api.module.supplier.model.ProviderAreaInfo;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import javax.ws.rs.POST;
import javax.ws.rs.Path;

/**
 * <AUTHOR>
 * @Classname SupplierAreaInfoApiSwag
 * @Description 区域信息Api
 * @Date 2020/5/15 17:41
 */

@RequestMapping("/product/supplier")
@Api(value = "供应商区域信息API接口", description = "供应商区域信息API接口")
public interface SupplierAreaInfoApiSwag {


    @ApiOperation(value = "查询区域信息", notes = "查询区域信息", response = ResultVO.class, tags={ "供应商区域信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value="/areainfo/findAreaBaseinfoByCondition", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> findAreaBaseinfoByCondition(HttpServletRequest request,
                                                                @ApiParam(value = "区域信息", required = true) @NotNull @RequestBody AreaBaseinfo areaBaseinfo)  ;

    /**
     * 分页查询供应商和区域码关系
     * @param providerAreaInfo
     * @return
     */
    @ApiOperation(value = "分页查询供应商和区域码关系", notes = "分页查询供应商和区域码关系", response = ResultVO.class, tags={ "供应商区域信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ApiImplicitParams({
            @ApiImplicitParam(name="organSign", value="机构标识", required=true, paramType="header", dataType = "string")
    })
    @ResponseBody
    @RequestMapping(value="/relationship/findSupplierAreaByCondition", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> findSupplierAreaByCondition(HttpServletRequest request,
                                                                @ApiParam(value = "供应商区域信息", required = true) @NotNull @RequestBody ProviderAreaInfo providerAreaInfo);

    /**
     * 保存供应商与区域编码关系
     * @param providerAreaInfo
     * @return
     */
    @ApiOperation(value = "保存供应商与区域编码关系", notes = "保存供应商与区域编码关系", response = ResultVO.class, tags={ "供应商区域信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ApiImplicitParams({
            @ApiImplicitParam(name="organSign", value="机构标识", required=true, paramType="header", dataType = "string")
    })
    @ResponseBody
    @RequestMapping(value="/relationship/saveSupplierAreaByCondition", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> saveSupplierAreaByCondition(HttpServletRequest request,
                                                                @ApiParam(value = "供应商区域信息", required = true) @NotNull @RequestBody ProviderAreaInfo providerAreaInfo);
}
