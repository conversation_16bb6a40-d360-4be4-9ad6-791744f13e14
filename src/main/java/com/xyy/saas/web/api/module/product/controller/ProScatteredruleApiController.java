package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.dto.ProductScatteredRuleDto;
import com.xyy.saas.web.api.module.product.model.ProductScatteredRulePo;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T15:07:38.324+08:00")

@Controller
public class ProScatteredruleApiController implements ProScatteredruleApi {

    @Reference(version = "0.0.1")
    private com.xyy.saas.product.core.api.ProScatteredruleApi ruleApi;

    public ResponseEntity<ResultVO> addRule(HttpServletRequest request, @ApiParam(value = "商品拆零规则信息" ,required=true )  @Valid @RequestBody ProductScatteredRulePo productScatteredRulePo) {
        ProductScatteredRuleDto ruleDto = new ProductScatteredRuleDto();
        BeanUtils.copyProperties(productScatteredRulePo, ruleDto);

        String organSign = request.getHeader("organSign");
        String username = request.getHeader("userName");
        ruleDto.setOrganSign(organSign);
        ruleDto.setCreateUser(username);

        ResultVO result = ruleApi.save(ruleDto);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);

    }

    public ResponseEntity<ResultVO> updateRule(HttpServletRequest request, @ApiParam(value = "商品拆零规则信息" ,required=true )  @Valid @RequestBody ProductScatteredRulePo productScatteredRulePo) {
        ProductScatteredRuleDto ruleDto = new ProductScatteredRuleDto();
        BeanUtils.copyProperties(productScatteredRulePo, ruleDto);

        String organSign = request.getHeader("organSign");
        String username = request.getHeader("userName");
        ruleDto.setOrganSign(organSign);
        ruleDto.setCreateUser(username);

        ResultVO result = ruleApi.save(ruleDto);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getProScatteredById(HttpServletRequest request, Long id) {
        return null;
    }

    @Override
    public ResponseEntity<ResultVO> proRuleList(HttpServletRequest request, ProductScatteredRulePo productScatteredRulePo) {
        return null;
    }

}
