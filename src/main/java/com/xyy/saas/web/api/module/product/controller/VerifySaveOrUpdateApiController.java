package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.inventory.core.api.InventoryVerifyApi;
import com.xyy.saas.web.api.module.product.model.InventoryPlanDetailVo;
import com.xyy.saas.web.api.module.product.model.InventoryVerifyVo;
import com.xyy.saas.web.api.module.product.model.ResultCodeEnum;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T17:19:18.933+08:00")

@Controller
public class VerifySaveOrUpdateApiController implements VerifySaveOrUpdateApi {

    @Reference(version = "1.0.0")
    private InventoryVerifyApi inventoryVerifyApi;

    public ResponseEntity<ResultVO> verifySaveOrUpdate(@ApiParam(value = "盘点确认Vo" ,required=true ) @RequestHeader("userName") String userName, @RequestHeader("organSign") String organSign, @Valid @RequestBody InventoryVerifyVo inventoryVerifyVo) {
        if(inventoryVerifyVo==null){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.INVENTORY_PARAM_NULL), HttpStatus.OK);
        }
        List<com.xyy.saas.inventory.core.dto.InventoryPlanDetailVo> ipdvos = new ArrayList<>();
        if(inventoryVerifyVo!=null&&inventoryVerifyVo.getIpvos()!=null){
            for(InventoryPlanDetailVo ipvo : inventoryVerifyVo.getIpvos()){
                com.xyy.saas.inventory.core.dto.InventoryPlanDetailVo ipdvo = new com.xyy.saas.inventory.core.dto.InventoryPlanDetailVo();
                BeanUtils.copyProperties(ipvo,ipdvo);
                ipdvos.add(ipdvo);
            }
        }
        com.xyy.saas.inventory.core.dto.InventoryVerifyVo vo =new com.xyy.saas.inventory.core.dto.InventoryVerifyVo();
        BeanUtils.copyProperties(inventoryVerifyVo,vo);
        vo.setIpvos(ipdvos);
        vo.setOrgansign(organSign);
        vo.setCreateUser(userName);
        com.xyy.saas.inventory.core.dto.InventoryVerifyVo back = inventoryVerifyApi.saveOrUpdate(vo);
        if(back.getBack()>=1){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.SUCCESS), HttpStatus.OK);
        }else{
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.INVENTORY_RESULT_FAIL), HttpStatus.OK);
        }
    }
}
