package com.xyy.saas.web.api.module.pay.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/5/7 19:12
 */
@ApiModel(description = "商家开户信息")
@Data
public class MerchantCreateVo {

    @ApiModelProperty(value = "商户类型   （小微 ： personal ， 个体：self-employed ,  商户 ：merchant）")
    private String accountType;

    @ApiModelProperty(value = "店铺信息")
    private StoreInfoVo storeInfoVo;

    @ApiModelProperty(value = "执照信息")
    private LicenseVo licenseVo;

    @ApiModelProperty(value = "结算信息")
    private SettleInfoVo settleInfoVo;

    @ApiModelProperty(value = "经营信息")
    private BusinessInfoVo businessInfoVo;

    @ApiModelProperty(value = "受益人信息")
    private BeneficiaryInfoVo beneficiaryInfoVo;

    @ApiModelProperty(value = "手续费")
    private String fee;

}