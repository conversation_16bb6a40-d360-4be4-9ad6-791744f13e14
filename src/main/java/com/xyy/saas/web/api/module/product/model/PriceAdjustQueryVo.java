package com.xyy.saas.web.api.module.product.model;

import java.util.List;

/**
 * @ClassName PriceAdjustQueryVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/1/14 10:32
 * @Version 1.0
 **/
public class PriceAdjustQueryVo extends Page{
    private String mixQuery;//商品信息
    private String adjustOrganSign;//调价部门
    private String startTime;//开始时间
    private String endTime;//结束时间
    private Integer systemType;//商品类型
    private Integer productType;//自定义分类
    private String pref;//单据编号模糊查询
    private Byte aproveStatus;//审核状态
    private String userName;//创建人
    private List<String> departOrganSigns;//门店选择集合
    private String productPref;//商品内码模糊查询
    private String sidx;
    private String sord;
    public List<String> getDepartOrganSigns() {
        return departOrganSigns;
    }
    private Byte isDrugstoreHidden; // 是否隐藏门店

    public void setDepartOrganSigns(List<String> departOrganSigns) {
        this.departOrganSigns = departOrganSigns;
    }

    public String getProductPref() {
        return productPref;
    }

    public void setProductPref(String productPref) {
        this.productPref = productPref;
    }

    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    public Byte getAproveStatus() {
        return aproveStatus;
    }

    public void setAproveStatus(Byte aproveStatus) {
        this.aproveStatus = aproveStatus;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getSystemType() {
        return systemType;
    }

    public void setSystemType(Integer systemType) {
        this.systemType = systemType;
    }

    public Integer getProductType() {
        return productType;
    }

    public void setProductType(Integer productType) {
        this.productType = productType;
    }

    public String getMixQuery() {
        return mixQuery;
    }

    public void setMixQuery(String mixQuery) {
        this.mixQuery = mixQuery;
    }

    public String getAdjustOrganSign() {
        return adjustOrganSign;
    }

    public void setAdjustOrganSign(String adjustOrganSign) {
        this.adjustOrganSign = adjustOrganSign;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getSidx() {
        return sidx;
    }

    public void setSidx(String sidx) {
        this.sidx = sidx;
    }

    public String getSord() {
        return sord;
    }

    public void setSord(String sord) {
        this.sord = sord;
    }

    public Byte getIsDrugstoreHidden() {
        return isDrugstoreHidden;
    }

    public void setIsDrugstoreHidden(Byte isDrugstoreHidden) {
        this.isDrugstoreHidden = isDrugstoreHidden;
    }
}
