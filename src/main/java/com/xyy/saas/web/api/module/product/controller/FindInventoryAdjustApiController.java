package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.web.api.module.product.model.InventoryAdjustVo;
import io.swagger.annotations.ApiParam;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-30T12:28:35.123+08:00")

@Controller
public class FindInventoryAdjustApiController implements FindInventoryAdjustApi {



    public ResponseEntity<InventoryAdjustVo> findInventoryAdjust(@ApiParam(value = "批号效期调整Vo" ,required=true )  @Valid @RequestBody InventoryAdjustVo inventoryAdjustVo) {
        // do some magic!
        return new ResponseEntity<InventoryAdjustVo>(HttpStatus.OK);
    }

}
