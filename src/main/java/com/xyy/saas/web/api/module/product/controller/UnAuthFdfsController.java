package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.jm.common.result.ResultVO;
import com.xyy.saas.web.api.module.product.model.FdfsFileUploadDto;
import com.xyy.saas.web.api.module.utils.UnAuthFdfsUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * fdfs上传入口
 * <AUTHOR>
 * @date 2021/7/2
 */
@RestController
@RequestMapping("/product/unauthdfs")
public class UnAuthFdfsController {
    @Autowired
    private UnAuthFdfsUtil unAuthFdfsUtil;


    /**
     * 图片上传(无需鉴权)
     * @param uploadDto
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/upload")
    public ResultVO unAuthImgUpload(@RequestBody FdfsFileUploadDto uploadDto) {
        MultipartFile file = uploadDto.getFile();
        if (file == null || file.isEmpty()) {
            return new ResultVO(ResultCodeEnum.ERROR.getCode(),"上传失败,文件不能为空!");
        }
        try{
            String fileUrl = unAuthFdfsUtil.uploadFile(file);
            if(StringUtils.isBlank(fileUrl)) {
                return new ResultVO(ResultCodeEnum.ERROR.getCode(), "上传失败!");
            }else{
                return new ResultVO(ResultCodeEnum.SUCCESS.getCode(), fileUrl);
            }
        }catch (Exception e) {
            return new ResultVO(ResultCodeEnum.ERROR.getCode(),"上传文件异常!");
        }
    }
}
