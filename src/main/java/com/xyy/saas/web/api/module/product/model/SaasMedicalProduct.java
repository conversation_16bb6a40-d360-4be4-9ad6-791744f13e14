package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * saas_medical_product
 * <AUTHOR>
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-09-07T21:07:46.794+08:00")
@ApiModel(description = "医保商品实体对象")
public class SaasMedicalProduct implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @JsonProperty("id")
    private Integer id;

    /**
     * 机构编码
     */
    @ApiModelProperty(value = "机构编码")
    @JsonProperty("organSign")
    private String organSign;

    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称")
    @JsonProperty("organSignName")
    private String organSignName;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    @JsonProperty("productPref")
    private String productPref;

    /**
     * 医保地区
     */
    @ApiModelProperty(value = "医保地区")
    @JsonProperty("medicalProvince")
    private String medicalProvince;

    /**
     * 医保匹配的唯一编码
     */
    @ApiModelProperty(value = "医保匹配的唯一编码")
    @JsonProperty("medicalUnique")
    private String medicalUnique;

    /**
     * 医保信息存储es索引名称
     */
    @ApiModelProperty(value = "医保信息存储es索引名称")
    @JsonProperty("medicalEsName")
    private String medicalEsName;

    /**
     * 医保信息类型，1、医保药品 2、诊疗项目
     */
    @ApiModelProperty(value = "医保信息类型")
    @JsonProperty("medicalType")
    private Integer medicalType;

    /**
     * 删除状态；1有效，0删除
     */
    @ApiModelProperty(value = "删除状态")
    @JsonProperty("yn")
    private Byte yn;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonProperty("createTime")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @JsonProperty("createUser")
    private String createUser;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonProperty("updateTime")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @JsonProperty("updateUser")
    private String updateUser;

    /**
     * 状态，1医保 2非医保
     */
    @ApiModelProperty(value = "状态")
    @JsonProperty("status")
    private Integer status;

    /**
     * 同步状态，1已同步，0未同步
     */
    @ApiModelProperty(value = "同步状态")
    @JsonProperty("sync")
    private Byte sync;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public String getOrganSignName() {
        return organSignName;
    }

    public void setOrganSignName(String organSignName) {
        this.organSignName = organSignName;
    }

    public String getProductPref() {
        return productPref;
    }

    public void setProductPref(String productPref) {
        this.productPref = productPref;
    }

    public String getMedicalProvince() {
        return medicalProvince;
    }

    public void setMedicalProvince(String medicalProvince) {
        this.medicalProvince = medicalProvince;
    }

    public String getMedicalUnique() {
        return medicalUnique;
    }

    public void setMedicalUnique(String medicalUnique) {
        this.medicalUnique = medicalUnique;
    }

    public String getMedicalEsName() {
        return medicalEsName;
    }

    public void setMedicalEsName(String medicalEsName) {
        this.medicalEsName = medicalEsName;
    }

    public Integer getMedicalType() {
        return medicalType;
    }

    public void setMedicalType(Integer medicalType) {
        this.medicalType = medicalType;
    }

    public Byte getYn() {
        return yn;
    }

    public void setYn(Byte yn) {
        this.yn = yn;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Byte getSync() {
        return sync;
    }

    public void setSync(Byte sync) {
        this.sync = sync;
    }
}