package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.member.core.api.MemberExchangeInfoForZBApi;
import com.xyy.saas.member.core.dto.MemberExchangeInfoDto;
import com.xyy.saas.web.api.module.member.model.MemberExchangeInfo;
import com.xyy.saas.web.api.module.utils.ExportExcelUtil;
import com.xyy.saas.web.api.module.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Controller
@RequestMapping("/member/memberExchangeInfo/zb")
@Api(value = "MemberExchangeInfoForZB", description = "总部端积分兑换记录接口")
public class MemberExchangeInfoForZBController {

    private static final Logger logger = Logger.getLogger(MemberExchangeInfoForZBController.class);

    @Reference(version = "0.0.1")
    private MemberExchangeInfoForZBApi memberExchangeInfoForZBApi;

    @ApiOperation(value = "总部端:积分兑换记录列表", notes = "总部端:积分兑换记录列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/getExchangeInfoPagerZB", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> getExchangeInfoPagerZB(@RequestHeader("commonRequestModel") String commonRequestModel, @RequestBody MemberExchangeInfoDto dto) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(dto.getPageNum() == null ? 1 : dto.getPageNum());
        pageInfo.setPageSize(dto.getPageSize() == null ? 10 : dto.getPageSize());
        if("".equals(dto.getStartDate())){
            dto.setStartDate(null);
        }
        if("".equals(dto.getEndDate())){
            dto.setEndDate(null);
        }
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        if(StringUtils.isEmpty(dto.getOrgansign())){
            //查询全部
            dto.setHeadquartersOrganSign(model.getOrganSign());
        }else{
            //查询门店
            dto.setOrgansign(dto.getOrgansign());
        }
        dto.setType(0);
        dto.setYn(1);
        dto.setIsHidden(model.getIdentity().intValue());
        PageInfo result = memberExchangeInfoForZBApi.getMemberExchangeInfoPagerZB(dto, pageInfo);
        return new ResponseEntity<ResultVO>(new ResultVO(result), HttpStatus.OK);
    }

    @ApiOperation(value = "总部端:积分兑换记录导出", notes = "积分兑换记录导出", response = MemberExchangeInfo.class, tags={ "memberExchangeInfo", })
    @RequestMapping(value = "/exportExcel", method = {RequestMethod.POST})
    public void exportExcelMember(@RequestHeader("commonRequestModel") String commonRequestModel, HttpServletRequest request,
                                  HttpServletResponse response, @RequestBody MemberExchangeInfoDto dto) {

        logger.info("ZB exportExcelMember param:" + JSONObject.toJSONString(dto));
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        dto.setType(0);
        dto.setYn(1);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(1000000);
        dto.setIsHidden(model.getIdentity().intValue());
        if(StringUtils.isEmpty(dto.getOrgansign())){
            //查询全部
            dto.setHeadquartersOrganSign(model.getOrganSign());
        }else{
            //查询门店
            dto.setOrgansign(dto.getOrgansign());
        }
        PageInfo resultPage = memberExchangeInfoForZBApi.getMemberExchangeInfoPagerZB(dto, pageInfo);
        //List<MemberExchangeInfoDto> memberdatas = memberExchangeInfoApi.getSyncMemberExchangeInfoList(dto);
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String sheetName = "积分兑换记录";
        String extfilename = "积分兑换记录" + df.format(new Date()) + ".xls";
        List<MemberExchangeInfoDto> memberList = resultPage.getList();
        String[] headers = new String[]{"小票号", "通用名", "商品编号", "会员手机号", "会员姓名", "会员卡号", "来源门店", "兑换数量", "消耗积分","兑换门店","门店编号", "兑换时间", "收银员"};
        String[] fieldNames = new String[]{"ticketNo", "productName", "productPref", "telephone", "memberName", "cartNo", "sourceDrugstoreName", "number", "integral","organSignName","organsign", "createTimeStr", "createUser"};
        try {
            ExportExcelUtil.createExcel(response, request, extfilename, sheetName, headers, fieldNames, memberList, true);
        } catch (Exception e) {
            logger.error("memberExchangeInfo/exportExcel is error.", e);
        }
    }
}
