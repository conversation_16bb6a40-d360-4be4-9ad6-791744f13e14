package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

@ApiModel(description = "一键匹配查询结果类")
public class YbmlQuickMatchResultVo {

    @JsonProperty("jobId")
    private String jobId;

    @JsonProperty("status")
    private Integer status;

    @JsonProperty("errorMsg")
    private String errorMsg;

    @JsonProperty("matched")
    private Integer matched;

    @JsonProperty("unmatched")
    private Integer unmatched;

    @ApiModelProperty(value = "一键匹配任务id")
    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    @ApiModelProperty(value = "匹配状态：1:匹配中，2：匹配完成，3：匹配失败")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @ApiModelProperty(value = "匹配失败错误信息")
    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    @ApiModelProperty(value = "已匹配商品数")
    public Integer getMatched() {
        return matched;
    }

    public void setMatched(Integer matched) {
        this.matched = matched;
    }

    @ApiModelProperty(value = "未匹配商品数")
    public Integer getUnmatched() {
        return unmatched;
    }

    public void setUnmatched(Integer unmatched) {
        this.unmatched = unmatched;
    }
}
