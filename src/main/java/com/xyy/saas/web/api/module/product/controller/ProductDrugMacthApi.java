/**
 * NOTE: This class is auto generated by the swagger code generator program (2.2.3).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.dto.AutoMatchDto;
import com.xyy.saas.product.core.dto.DrugStandardDto;
import com.xyy.saas.product.core.dto.PageDto;
import com.xyy.saas.product.core.dto.ProductDrugStandardDto;
import com.xyy.saas.product.core.dto.ProductJobDto;
import com.xyy.saas.product.core.dto.ProductMatchConfirmDto;
import com.xyy.saas.web.api.module.product.model.ApproveHistoryVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

@Api(value = "商品与药监匹配API", description = "商品与药监匹配API")
@RequestMapping("/product")
public interface ProductDrugMacthApi {

    @ApiOperation(value = "商品匹配列表查询接口", notes = "商品匹配列表查询接口", response = ApproveHistoryVo.class, tags={ "商品与药监匹配API", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功") })

    @RequestMapping(value = "/productdrugmacth/list",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> list(HttpServletRequest request, @ApiParam(value = "商品匹配信息实体", required = true) @Valid @RequestBody ProductDrugStandardDto dto);


    @ApiOperation(value = "商品匹配药监表查询接口", notes = "商品匹配药监表查询接口", response = ApproveHistoryVo.class, tags={ "商品与药监匹配API", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功") })

    @RequestMapping(value = "/productdrugmacth/toMatch",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> matchDrugStandard(HttpServletRequest request, @ApiParam(value = "商品匹配信息实体", required = true) @Valid @RequestBody DrugStandardDto dto);

    @ApiOperation(value = "商品匹配药监表接口", notes = "商品匹配药监表接口", response = ApproveHistoryVo.class, tags={ "商品与药监匹配API", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功") })
    @RequestMapping(value = "/productdrugmacth/match",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> matchProduct(HttpServletRequest request, @ApiParam(value = "商品匹配信息实体", required = true) @Valid @RequestBody ProductDrugStandardDto dto);

    @ApiOperation(value = "自动商品匹配药监表接口", notes = "自动商品匹配药监表接口", response = ApproveHistoryVo.class, tags={ "商品与药监匹配API", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功") })
    @RequestMapping(value = "/productdrugmacth/auto/match",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> autoMatchProduct(HttpServletRequest request, @ApiParam(value = "商品匹配信息实体 只需传入matchCriteria即可 用&连接 例如：commonName&approvalNumber&attributeSpecification&manufacturer ", required = true) @Valid @RequestBody AutoMatchDto dto);

    @ApiOperation(value = "任务查询接口", notes = "任务查询接口", response = ApproveHistoryVo.class, tags={ "任务查询接口", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功") })
    @RequestMapping(value = "/job/query",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> jobQuery(HttpServletRequest request, @ApiParam(value = "任务id", required = true)@Valid @RequestBody ProductJobDto dto);

    @ApiOperation(value = "获取匹配中的商品分页", notes = "获取匹配中的商品分页", response = List.class, tags={ "商品与药监匹配API", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功") })
    @RequestMapping(value = "/productdrugmacth/queryUnConfirmedProducts",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> queryUnConfirmedProducts(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                      @ApiParam(value = "分页参数", required = true) @Valid @RequestBody PageDto pageDto);

    @ApiOperation(value = "确认匹配中的所有商品", notes = "确认匹配中的所有商品", response = Boolean.class, tags={ "商品与药监匹配API", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功") })
    @RequestMapping(value = "/productdrugmacth/confirmAllProducts",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> confirmAllProducts(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                @ApiParam(value = "确认匹配实体", required = true) @Valid @RequestBody ProductMatchConfirmDto dto);

}
