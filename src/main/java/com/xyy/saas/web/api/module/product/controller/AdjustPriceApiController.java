package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.api.SaasAreaApi;
import com.xyy.saas.common.api.SaasCustomTypeApi;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.dto.SaasRegionBusinessDto;
import com.xyy.saas.common.dto.SystemDictDto;
import com.xyy.saas.common.dto.XyySaasRegionParamsDto;
import com.xyy.saas.common.enums.CustomTypeBusinessTypeEnum;
import com.xyy.saas.common.util.DateUtil;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.inventory.core.dto.PriceAdjustDetailPoLsDto;
import com.xyy.saas.inventory.core.dto.PriceAdjustPoLsDto;
import com.xyy.saas.manufactor.core.api.ManufactorApi;
import com.xyy.saas.manufactor.core.dto.ManufactorDto;
import com.xyy.saas.product.core.api.ProductApi;
import com.xyy.saas.product.core.api.ProductDictionaryApi;
import com.xyy.saas.product.core.api.ProductPriceApi;
import com.xyy.saas.product.core.dto.*;

import com.xyy.saas.product.core.enums.DrugstoreTypeProductEnum;
import com.xyy.saas.web.api.module.product.constant.DictConstant;
import com.xyy.saas.web.api.module.product.model.*;
import com.xyy.saas.web.api.module.product.util.DrugstoreTypeUtil;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.api.RoleApi;
import com.xyy.user.module.dto.SaasDrugstoreDto;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import com.xyy.user.module.dto.result.EmployeeDto;
import com.xyy.user.module.enums.DrugstoreBizModelEnum;
import io.swagger.annotations.*;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.xyy.user.module.dto.restructure.SaaSEmployeeExtendDto;
import com.xyy.user.module.dto.restructure.SaaSEmployeeDto;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

@Controller
public class AdjustPriceApiController implements AdjustPriceApi {
    private static final Logger logger = LoggerFactory.getLogger(AdjustPriceApiController.class);
    @Reference(version = "0.0.1")
    private com.xyy.saas.product.core.api.AdjustPriceApi adjustPriceApi;

    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;

    @Reference(version = "0.0.1")
    private ProductPriceApi productPriceApi;

    @Reference(version = "0.0.1")
    private ProductDictionaryApi productDictionaryApi;

    @Reference(version = "0.0.1")
    private EmployeeApi employeeApi;

    @Reference(version = "0.0.1")
    private SaasAreaApi saasAreaApi;

    @Reference(version = "0.0.1")
    private ManufactorApi manufactorApi;

    @Reference(version = "0.0.1")
    private RoleApi roleApi;

    @Reference(version = "0.0.1",retries = -1)
    private ProductApi productApi;

    @Reference(version = "0.0.1")
    private SaasCustomTypeApi saasCustomTypeApi;

    public ResponseEntity<ResultVO> add(@ApiParam(value = "商品售价调整信息实体" ,required=true )  @Valid @RequestBody PriceAdjustVo priceAdjustVo) {
        logger.info("add  " + JSONObject.toJSON(priceAdjustVo));
        PriceAdjustDto priceAdjustDto = new PriceAdjustDto();
        BeanUtils.copyProperties(priceAdjustVo, priceAdjustDto);
        ResultVO result = adjustPriceApi.save(priceAdjustDto);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    public ResponseEntity<ResultVO> delAdjust(@ApiParam(value = "售价调整单主表ID" ,required=true )  @Valid @RequestBody Long id) {
        ResultVO result = adjustPriceApi.delAdjust(id);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    public ResponseEntity<ResultVO> exportExcelAdjustDetail(@ApiParam(value = "商品售价调整信息实体" ,required=true )  @Valid @RequestBody PriceAdjustVo priceAdjustVo) {
        PriceAdjustDto priceAdjustDto = new PriceAdjustDto();
        BeanUtils.copyProperties(priceAdjustVo, priceAdjustDto);
        ResultVO result = adjustPriceApi.exportExcelAdjustDetail(priceAdjustDto);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    public ResponseEntity<ResultVO> homepageAdjust() {
        ResultVO result = adjustPriceApi.homepageAdjust("");
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    public ResponseEntity<ResultVO> list(HttpServletRequest request,@ApiParam(value = "商品售价调整信息实体" ,required=true )  @Valid @RequestBody PriceAdjustQueryVo priceAdjustQueryVo) {
        try{
            String organSign = request.getHeader("organSign");//当前登录机构号
            String modelJson = request.getHeader("commonRequestModel");//机构通用信息
            String employee = request.getHeader("employeeId");//当前登录用户id
            if(priceAdjustQueryVo.getPage() == null || priceAdjustQueryVo.getPage() <= 0){
                priceAdjustQueryVo.setPage(1);
            }
            if(priceAdjustQueryVo.getRows() == null || priceAdjustQueryVo.getRows() <= 0){
                priceAdjustQueryVo.setRows(50);
            }
            DrugStorePrams pramsModel = processOrganJsonInfo(modelJson);//根据机构通用信息获取机构判断指标信息
            String headerOrganSign = pramsModel.getHeaderOrganSign();
            Byte bizModel = pramsModel.getBizModel();
            Byte organSignType = pramsModel.getOrganSignType();
            List<String> organSigns = new ArrayList<>();
            Byte approveOrganType = null;
            Map<String, String> organSignMap = new HashMap<>();//机构编号和机构名称对应关系map集合
            DrugstoreTypeProductEnum drugstoreTypeProductEnum = DrugstoreTypeUtil.isDrugstoreTypeEnum(bizModel, organSignType);
            logger.info("AdjustPriceApiController list bizmodel:{},organType{}:",bizModel,organSignType);
            List<String> createUsers = new ArrayList<>();
            switch (drugstoreTypeProductEnum) {
                case SINGLE_DRUGSTORE://单体
                    organSigns.add(organSign);
                    break;
                case CHAIN_DRUGSTORE://连锁门店
                    organSigns.add(organSign);
                    approveOrganType = (byte) 1;
                    break;
                case CHAIN_HEADQUARTERS://连锁总部
                    approveOrganType = (byte) 3;
                    if (-1 == processLsHeaderList(organSignMap, organSign, approveOrganType, priceAdjustQueryVo, organSigns)) {
                        return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "获取总部机构列表发生错误，请重试", null), HttpStatus.OK);
                    }
                    SaaSEmployeeExtendDto saaSEmployeeExtendDto =new SaaSEmployeeExtendDto();
                    saaSEmployeeExtendDto.setName(priceAdjustQueryVo.getUserName());
                    saaSEmployeeExtendDto.setHeadquartersOrganSign(organSign);//总部机构号
                    List<SaaSEmployeeDto> employeeDtos = employeeApi.getHeadquartersAndDepartmentAndBranchStoreEmployeeList(saaSEmployeeExtendDto);
                    for(SaaSEmployeeDto employeeDto : employeeDtos){
                        createUsers.add(employeeDto.getId().toString());
                    }
                    if(CollectionUtils.isEmpty(createUsers)){
                        return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.SUCCESS,new PageInfo(new ArrayList<AdjustBaseInfoVo>())), HttpStatus.OK);
                    }
                    logger.info("xinzengtiaojiadan:list{},organSign;{}，userName{},organSigns:{}",createUsers,organSign,priceAdjustQueryVo.getUserName(),organSigns);
                    break;
                case JOIN_DRUGSTORE://联营门店
                    organSigns.add(organSign);
                    break;
                case JOIN_HEADQUARTERS://联营总部
                    organSigns.add(organSign);
                    break;
                default://默认情况按照单店来处理
                    return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "门店类型错误，请确认门店类型是否是单店，连锁，联营", null), HttpStatus.OK);
            }
            PriceAdjustDto priceAdjustDto = new PriceAdjustDto();
            copyVoPramsToDto(priceAdjustDto,priceAdjustQueryVo,organSigns,organSign);//vo to dto prams
            priceAdjustDto.setCreateUsers(createUsers);
            ResultVO result = adjustPriceApi.list(priceAdjustDto);
            List<AdjustBaseInfoVo> results = new ArrayList<>();
            PageInfo pageInfo = (PageInfo) result.getResult();
            List<PriceAdjustDto> adjustDtos = pageInfo.getList();
            if (CollectionUtils.isNotEmpty(adjustDtos)) {
                ResultVO<List<Integer>> listResultVO = roleApi.queryRoleIdsByEmployeeId(Integer.valueOf(employee));//获取当前登录用户的权限列表
                List<Integer> currentRoleIds = listResultVO.getResult();
                Byte finalApproveOrganType = approveOrganType;
                adjustDtos.stream().forEach(dto->{
                    AdjustBaseInfoVo vo = new AdjustBaseInfoVo();
                    copyDtoToVo(dto,vo, finalApproveOrganType,currentRoleIds,organSignMap);//dto to vo
                    results.add(vo);
                });
            }
            pageInfo.setList(results);
            result.setResult(pageInfo);
            return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
        }catch (Exception e){
            logger.error("AdjustPriceApiController list occur error:",e);
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "--------服务器内部报错-------", null), HttpStatus.OK);
        }
    }

    public ResponseEntity<ResultVO> queryAdjustDetailsList(@ApiParam(value = "商品审核列表实体以及查询条件实体" ,required=true )  @Valid @RequestBody PriceAdjustVo priceAdjustVo) {
        PriceAdjustDto priceAdjustDto = new PriceAdjustDto();
        BeanUtils.copyProperties(priceAdjustVo, priceAdjustDto);
        ResultVO result = adjustPriceApi.queryAdjustDetailsList(priceAdjustDto);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    public ResponseEntity<ResultVO> submit(HttpServletRequest request,@ApiParam(value = "商品售价调整信息实体" ,required=true )  @Valid @RequestBody PriceAdjustVo priceAdjustVo) {
        logger.info("调价单 submit  {}", JSONObject.toJSON(priceAdjustVo));
        String organSign = request.getHeader("organSign");
        String modelJson = request.getHeader("commonRequestModel");
        String employee = request.getHeader("employeeId");
        String userName = "";
        try{
            userName = employeeApi.queryEmployeeById(Integer.valueOf(employee)).getResult().getName();
        }catch (Exception e){
            logger.error("employeeApi.queryEmployeeById({}),organSign:{}",employee,organSign,e);
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "用户信息存在问题，请重试", null), HttpStatus.OK);
        }
        DrugStorePrams pramsModel = processOrganJsonInfo(modelJson);//根据机构通用信息获取机构判断指标信息
        String headerOrganSign = pramsModel.getHeaderOrganSign();
        Byte bizModel = pramsModel.getBizModel();
        Byte organSignType = pramsModel.getOrganSignType();
        ResultVO result = null;
        PriceAdjustDto priceAdjustDto = new PriceAdjustDto();
        copyVoPramsToDtoSubmit(priceAdjustDto,priceAdjustVo,userName,organSign);
        List<PriceAdjustDetailPo> detailPos = priceAdjustVo.getDetails();
        List<PriceAdjustDetailDto> dtos = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(detailPos)){
            detailPos.stream().forEach(po->{
                PriceAdjustDetailDto dto = new PriceAdjustDetailDto();
                if(po.getNewMemberPrice() == null){
                    po.setNewMemberPrice(po.getOldMemberPrice());
                }
                if(po.getNewPrice() == null){
                    po.setNewPrice(po.getOldPrice());
                }
                BeanUtils.copyProperties(po,dto);
                dtos.add(dto);
            });
        }
        priceAdjustDto.setDetails(dtos);
        DrugstoreTypeProductEnum drugstoreTypeProductEnum = DrugstoreTypeUtil.isDrugstoreTypeEnum(bizModel,organSignType);
        logger.info("meiju:{},bizmodel:{},organType:{},modeljoson:{}",drugstoreTypeProductEnum,bizModel,organSignType,modelJson);
        switch (drugstoreTypeProductEnum) {
            case SINGLE_DRUGSTORE://单体
                result = submitSingleAdjust(priceAdjustDto,"单体");
                break;
            case CHAIN_DRUGSTORE://连锁门店
                priceAdjustDto.setApplicableStores(organSign);
                result = submitLsAdjust(priceAdjustDto,priceAdjustVo,bizModel,organSignType,organSign,employee,headerOrganSign,"连锁门店");
                break;
            case CHAIN_HEADQUARTERS://连锁总部
                result = submitLsAdjust(priceAdjustDto,priceAdjustVo,bizModel,organSignType,organSign,employee,headerOrganSign,"连锁总部");
                break;
            case JOIN_DRUGSTORE://联营门店
                result = submitSingleAdjust(priceAdjustDto,"联营门店");
                break;
            case JOIN_HEADQUARTERS://联营总部
                result = new ResultVO(ResultCodeEnum.ERROR, "联营总部没有创建调价单权限");
                break;
            default:
                result = new ResultVO(ResultCodeEnum.ERROR, "门店类型错误，请确认门店类型是否是单店，连锁，联营");
        }
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    public ResponseEntity<ResultVO> update(HttpServletRequest request,@ApiParam(value = "商品售价调整信息实体" ,required=true )  @Valid @RequestBody PriceAdjustVo priceAdjustVo) {
        String organSign = request.getHeader("organSign");
        String modelJson = request.getHeader("commonRequestModel");
        String employee = request.getHeader("employeeId");
        DrugStorePrams pramsModel = processOrganJsonInfo(modelJson);//根据机构通用信息获取机构判断指标信息
        String headerOrganSign = pramsModel.getHeaderOrganSign();
        Byte bizModel = pramsModel.getBizModel();
        Byte organSignType = pramsModel.getOrganSignType();
        AdjustDetailInfoVo result = new AdjustDetailInfoVo();
        List<AdjustDetailVo> vos = new ArrayList<>();
        List<SaaSDrugstoreDto> drugs = new ArrayList<>();
        Map<String,String> organSignMap = new HashMap<>();
        String organSign1 = null;
        Byte approveOrganType = null;
        DrugstoreTypeProductEnum drugstoreTypeProductEnum = DrugstoreTypeUtil.isDrugstoreTypeEnum(bizModel,organSignType);
        switch (drugstoreTypeProductEnum) {
            case SINGLE_DRUGSTORE://单体
                organSign1 = organSign;
                break;
            case CHAIN_DRUGSTORE://连锁门店
                organSign1 = headerOrganSign;
                approveOrganType = (byte)1;
                break;
            case CHAIN_HEADQUARTERS://连锁总部
                organSign1 = organSign;
                approveOrganType = (byte)3;
                try{
                    drugs = drugstoreApi.getDrugstoreByHeadquartersOrganSign(organSign);
                }catch (Exception e){
                    e.printStackTrace();
                    logger.error("call drugstoreApi.getDrugstoreByHeadquartersOrganSign occur error,organSign:{}",organSign,e);
                }
                if(drugs != null && drugs.size() > 0){
                    for(SaaSDrugstoreDto dto:drugs){
                        organSignMap.put(dto.getOrganSign(),dto.getDrugstoreName());
                    }
                }
                break;
            case JOIN_DRUGSTORE://联营门店
                organSign1 = organSign;
                break;
            case JOIN_HEADQUARTERS://联营总部
                organSign1 = organSign;
                break;
            default:
                return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "门店类型错误，请确认门店类型是否是单店，连锁，联营"), HttpStatus.OK);
        }
        PriceAdjustDto priceAdjustDto = new PriceAdjustDto();
        priceAdjustDto.setId(priceAdjustVo.getId());
        priceAdjustDto.setOrganSign(organSign);
        priceAdjustDto.setHeaderOrganSign(headerOrganSign);
        priceAdjustDto.setBizModel(bizModel);
        priceAdjustDto.setOrganSignType(organSignType);
        ResultVO resultVo = adjustPriceApi.update(priceAdjustDto);
        PriceAdjustPoLsDto dto = (PriceAdjustPoLsDto) resultVo.getResult();
        ResultVO<List<Integer>> listResultVO = roleApi.queryRoleIdsByEmployeeId(Integer.valueOf(employee));
        List<Integer> currentRoleIds = listResultVO.getResult();
        result.setPref(dto.getPref());//
        result.setAdjustReason(dto.getRemark());
        result.setApplicableStores(dto.getApplicableStores());
        result.setCanApprove((byte) processIsCanCheckLs(dto, approveOrganType, currentRoleIds));//审批权限逻辑处理
        logger.info("调价单详情审批校验，pref:{},当前机构类型:{},审核权限机构类型:{},当前用户权限列表:{},审核权限id:{}",dto.getPref(),approveOrganType,dto.getApproveOrganSign(),currentRoleIds,dto.getTodoRoleId());
        if(StringUtils.isEmpty(dto.getApplicableStores()) || dto.getApplicableStores().equals("all")){
            result.setApplicableStoresName("全部门店");
            result.setApplicableStores("all");
        }else{
            StringBuilder sb = new StringBuilder();
            String[] drugarry = dto.getApplicableStores().split(",");
            for(String organSignStr:drugarry){
                sb.append(organSignMap.get(organSignStr)).append(",");
            }
            result.setApplicableStoresName(sb.toString());
        }
        result.setCreateUserName(dto.getCreateUser());
        result.setCreateTimeStr(DateUtil.parseDateToStr(dto.getCreateTime(),DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS));
        result.setId(dto.getId());
        result.setTaskId(dto.getTaskId());
        result.setBusinessScene(dto.getBusinessScene());
        result.setAproveStatus(dto.getAuditState().intValue());
        List<PriceAdjustDetailPoLsDto> dtos = dto.getList();
        //获取字典接口，单位，剂型，商品分类，自定义分类(单体，联营连锁要区分)
        List<Integer> ids = Arrays.asList(DictConstant.unitBussinessId, DictConstant.agentBussinessId, DictConstant.productSystemType,DictConstant.PRODUCT_THIRD_BUSSINESS_ID,DictConstant.commodtyTypeBussinessId);
        long start_time1 = System.currentTimeMillis();
        List<SystemDictDto> dictDtos = productDictionaryApi.findSystemDictDtoBybussinessIds(ids,organSign1);
        Map<Integer,String> danweiMap = new HashMap<>();
        Map<Integer,String> jixingMap = new HashMap<>();
        Map<Integer,String> shangpinfenleiMap = new HashMap<>();
        Map<Integer,String> dantizidingyiMap = new HashMap<>();
        Map<Integer,String> liansuozidingyiMap = new HashMap<>();
        if(dictDtos != null && dictDtos.size() > 0){
            for(SystemDictDto dtoDict:dictDtos){
                if(dtoDict.getBussinessId().equals(DictConstant.unitBussinessId)){
                    danweiMap.put(dtoDict.getId(),dtoDict.getName());
                }
                if(dtoDict.getBussinessId().equals(DictConstant.commodtyTypeBussinessId)){
                    dantizidingyiMap.put(dtoDict.getId(),dtoDict.getName());
                }
                if(dtoDict.getBussinessId().equals(DictConstant.agentBussinessId)){
                    jixingMap.put(dtoDict.getId(),dtoDict.getName());
                }
                if(dtoDict.getBussinessId().equals(DictConstant.productSystemType)){
                    shangpinfenleiMap.put(dtoDict.getId(),dtoDict.getName());
                }
                if(dtoDict.getBussinessId().equals(DictConstant.PRODUCT_THIRD_BUSSINESS_ID)){
                    liansuozidingyiMap.put(dtoDict.getId(),dtoDict.getName());
                }
            }
        }
        Set<Integer> areaCodes = new HashSet<>();
        for(PriceAdjustDetailPoLsDto dto1:dtos){
            String areaCodeStr = dto1.getAreaCode();
            if(!StringUtils.isEmpty(areaCodeStr)){
                String[] areacodearray = areaCodeStr.split(",");
                for(String str:areacodearray){
                    areaCodes.add(Integer.valueOf(str));
                }
            }
        }
        XyySaasRegionParamsDto xyySaasRegionParamsDto = new XyySaasRegionParamsDto();
        xyySaasRegionParamsDto.setAreaCodes(new ArrayList<>(areaCodes));
        List<SaasRegionBusinessDto> saasRegionBusinessDtos = saasAreaApi.queryRegionByAreaCodeList(xyySaasRegionParamsDto);
        Map<String,String> areaMap = new HashMap<>();
        if(saasRegionBusinessDtos != null && saasRegionBusinessDtos.size() > 0){
            for(SaasRegionBusinessDto dto2:saasRegionBusinessDtos){
                areaMap.put(String.valueOf(dto2.getAreaCode()),dto2.getAreaName());
            }
        }
        ManufactorDto manufactor = new ManufactorDto();
        manufactor.setOrganSign(organSign1);
        List<ManufactorDto> manufactorDtoList= this.manufactorApi.queryManufactorListNoPage(manufactor);
        Map<String,String> manufactorMap = new HashMap<>();
        if(manufactorDtoList != null && manufactorDtoList.size() > 0){
            for(ManufactorDto mndto:manufactorDtoList){
                manufactorMap.put(String.valueOf(mndto.getId()),mndto.getName());
            }
        }
        for(PriceAdjustDetailPoLsDto dto1:dtos){
            AdjustDetailVo vo = new AdjustDetailVo();
            vo.setAttributeSpecification(dto1.getAttributeSpecification());
            vo.setCommonName(dto1.getCommonName());
            vo.setId(dto1.getId());
            vo.setJixingName(jixingMap.get(dto1.getDosageFormId()));
            vo.setDosageFormName(jixingMap.get(dto1.getDosageFormId()));
            vo.setManufacturer(manufactorMap.get(dto1.getManufacturerCode()));//生产厂商由id转name
            vo.setPharmacyPref(dto1.getPharmacyPref());
            vo.setSystemTypeName(shangpinfenleiMap.get(dto1.getSystemType()));
            StringBuilder sb1 = new StringBuilder();
            if(!StringUtils.isEmpty(dto1.getAreaCode())){
                String[] arrays = dto1.getAreaCode().split(",");
                for(String strs:arrays){
                    sb1.append(areaMap.get(strs));
                }
            }
            vo.setProducingArea(sb1.toString());
            vo.setProductName(dto1.getProductName());
            vo.setProductPref(dto1.getProductPref());
            vo.setUnitName(danweiMap.get(dto1.getUnitId()));
            vo.setRetailPrice(dto1.getOldPrice());
            vo.setNewPrice(dto1.getNewPrice());
            vo.setVipPrice(dto1.getOldMemberPrice());
            vo.setNewMemberPrice(dto1.getNewMemberPrice());

            vo.setLatestTaxPrice(dto1.getLatestTaxPrice());
            vo.setAvgTaxPrice(dto1.getAvgTaxPrice());
            vo.setMinTaxPrice(dto1.getMinTaxPrice());
            vo.setNewPriceErrorFlag(dto1.getNewPriceErrorFlag());
            vo.setNewMemberPriceErrorFlag(dto1.getNewMemberPriceErrorFlag());
            vos.add(vo);
        }
        result.setList(vos);
        return new ResponseEntity<ResultVO>(new ResultVO(result), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> findAdjustOrganSigns(HttpServletRequest request) {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        List<AdjustOrganSignVo> vos = new ArrayList<>();
        if(!StringUtils.isEmpty(organSign)){
            List<SaaSDrugstoreDto> drugs = new ArrayList<>();
            try{
                drugs = drugstoreApi.getDrugstoreByHeadquartersOrganSign(organSign);
            }catch (Exception e){
                logger.error("call drugstoreApi.getDrugstoreByHeadquartersOrganSign occur error,organSign:",organSign,e);
            }
            List<String> organSigns = new ArrayList<>();
            if(drugs != null && drugs.size() > 0){
                for(SaaSDrugstoreDto dto:drugs){
                    organSigns.add(dto.getOrganSign());
                }
            }
            List<String> adjustOrganSigns = new ArrayList<>();
            try{
                adjustOrganSigns = adjustPriceApi.getAdjustOrganSigns(organSign,organSigns);
            }catch (Exception e){
                logger.error("call adjustPriceApi occur eror,organSian:{},organSigns:{},",organSign,organSigns,e);
            }
            if(adjustOrganSigns != null && adjustOrganSigns.size() > 0){
                List<SaaSDrugstoreDto> drugstoreDtos = new ArrayList<>();
                try{
                    drugstoreDtos = drugstoreApi.getDrugstoreByMultipleOrganSign(adjustOrganSigns);
                }catch (Exception e){
                    logger.error("call drugstoreApi.getDrugstoreByMultipleOrganSign occur errro,adjustOrganSigns:{}",adjustOrganSigns,e);
                }
                for(SaaSDrugstoreDto dto:drugstoreDtos){
                    AdjustOrganSignVo vo = new AdjustOrganSignVo();
                    vo.setAdjustOrganSignName(dto.getDrugstoreName());
                    vo.setAdjustOrganSign(dto.getOrganSign());
                    vos.add(vo);
                }
            }
        }
        ResultVO result = new ResultVO(vos);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> findRecords(HttpServletRequest request, @ApiParam(value = "商品售价调整信息实体" ,required=true )  @Valid @RequestBody PriceAdjustQueryVo priceAdjustQueryVo) {
        String modelJson = request.getHeader("commonRequestModel");
        String organSign = request.getHeader("organSign");
        List<AdjustPriceInfoVo> results = new ArrayList<>();
        if(priceAdjustQueryVo.getPage() == null || priceAdjustQueryVo.getPage() <= 0){
            priceAdjustQueryVo.setPage(1);
        }
        if(priceAdjustQueryVo.getRows() == null || priceAdjustQueryVo.getRows() <= 0){
            priceAdjustQueryVo.setRows(50);
        }
        DrugStorePrams pramsModel = processOrganJsonInfo(modelJson);//根据机构通用信息获取机构判断指标信息
        String headerOrganSign = pramsModel.getHeaderOrganSign();
        Byte bizModel = pramsModel.getBizModel();
        Byte organSignType = pramsModel.getOrganSignType();
        List<String> organSigns = new ArrayList<>();
        Map<String,String> organSignMap = new HashMap<>();
        String organSign1 = null;
        Byte userSelectFlag = 0;//0表示查询总部机构号下所有的用户，1表示只查询特定机构下的所有员工
        DrugstoreTypeProductEnum drugstoreTypeProductEnum = DrugstoreTypeUtil.isDrugstoreTypeEnum(bizModel,organSignType);
        if(drugstoreTypeProductEnum == DrugstoreTypeProductEnum.SINGLE_DRUGSTORE){//单体逻辑
            userSelectFlag = 1;
            organSign1 = organSign;
            organSigns.add(organSign);
        }else if(drugstoreTypeProductEnum == DrugstoreTypeProductEnum.CHAIN_DRUGSTORE){//连锁门店
            userSelectFlag = 1;
            organSign1 = headerOrganSign;
            organSigns.add(organSign);
        }else if(drugstoreTypeProductEnum == DrugstoreTypeProductEnum.CHAIN_HEADQUARTERS){//连锁总部
            userSelectFlag = 0;
            organSign1 = organSign;
            SaaSDrugstoreDto headerDrugStore = null;
            try{
                headerDrugStore = drugstoreApi.getDrugstoreByOrganSign(organSign);
            }catch (Exception e){
                logger.error("call drugstoreApi.getDrugstoreByOrganSign occur error {}",organSign,e);
            }
            organSignMap.put(organSign,headerDrugStore.getDrugstoreName());
            List<SaaSDrugstoreDto> drugs = null;
            try{
                SaaSDrugstoreDto dto = new SaaSDrugstoreDto();
                dto.setIsDrugstoreHidden(priceAdjustQueryVo.getIsDrugstoreHidden());
                drugs = drugstoreApi.getDrugstoreByHeadquartersOrganSign(dto, organSign);
            }catch (Exception e){
                logger.error("call drugstoreApi.getDrugstoreByHeadquartersOrganSign occur error,organSign:{}",organSign,e);
            }
            if(drugs != null && drugs.size() > 0){
                for(SaaSDrugstoreDto dto:drugs){
                    organSignMap.put(dto.getOrganSign(),dto.getDrugstoreName());
                }
            }
            if(priceAdjustQueryVo.getAdjustOrganSign().equals("all") || priceAdjustQueryVo.getAdjustOrganSign().equals("")){
                if(drugs != null && drugs.size() > 0){
                    for(SaaSDrugstoreDto dto:drugs){
                        organSigns.add(dto.getOrganSign());
                    }
                }
                organSigns.add(organSign);
            }else if(priceAdjustQueryVo.getAdjustOrganSign().equals("header")){
                organSigns.add(organSign);
            }
            else {
                organSigns.add(priceAdjustQueryVo.getAdjustOrganSign());
            }
        }else if(drugstoreTypeProductEnum == DrugstoreTypeProductEnum.JOIN_DRUGSTORE){//连营门店
            userSelectFlag = 1;
            organSign1 = organSign;
            organSigns.add(organSign);
        }else if(drugstoreTypeProductEnum == DrugstoreTypeProductEnum.JOIN_HEADQUARTERS){//联营总部
            userSelectFlag = 1;
            organSign1 = organSign;
            organSigns.add(organSign);
        }
        else{//联营逻辑
            userSelectFlag = 1;
            organSign1 = organSign;
            organSigns.add(organSign);
        }
        PageInfo info = new PageInfo();
        info.setPageNum(priceAdjustQueryVo.getPage());
        info.setPageSize(priceAdjustQueryVo.getRows());
        PageInfo pageInfo = adjustPriceApi.findRecords(info,priceAdjustQueryVo.getMixQuery(),priceAdjustQueryVo.getStartTime(),priceAdjustQueryVo.getEndTime(),organSigns);
        List<AdjustPriceDto> dtos = pageInfo.getList();
        if(dtos != null && dtos.size() > 0){
            //获取字典接口，单位，剂型，商品分类，自定义分类(单体，联营连锁要区分)
            List<Integer> ids = Arrays.asList(DictConstant.unitBussinessId, DictConstant.agentBussinessId, DictConstant.productSystemType,DictConstant.PRODUCT_THIRD_BUSSINESS_ID,DictConstant.commodtyTypeBussinessId);
            long start_time1 = System.currentTimeMillis();
            List<SystemDictDto> dictDtos = productDictionaryApi.findSystemDictDtoBybussinessIds(ids,organSign1);
            Map<Integer,String> danweiMap = new HashMap<>();
            Map<Integer,String> jixingMap = new HashMap<>();
            Map<Integer,String> shangpinfenleiMap = new HashMap<>();
            Map<Integer,String> dantizidingyiMap = new HashMap<>();
            Map<Integer,String> liansuozidingyiMap = new HashMap<>();
            if(dictDtos != null && dictDtos.size() > 0){
                for(SystemDictDto dto:dictDtos){
                    if(dto.getBussinessId().equals(DictConstant.unitBussinessId)){
                        danweiMap.put(dto.getId(),dto.getName());
                    }
                    if(dto.getBussinessId().equals(DictConstant.commodtyTypeBussinessId)){
                        dantizidingyiMap.put(dto.getId(),dto.getName());
                    }
                    if(dto.getBussinessId().equals(DictConstant.agentBussinessId)){
                        jixingMap.put(dto.getId(),dto.getName());
                    }
                    if(dto.getBussinessId().equals(DictConstant.productSystemType)){
                        shangpinfenleiMap.put(dto.getId(),dto.getName());
                    }
                    if(dto.getBussinessId().equals(DictConstant.PRODUCT_THIRD_BUSSINESS_ID)){
                        liansuozidingyiMap.put(dto.getId(),dto.getName());
                    }
                }
            }
            Set<Integer> areaCodes = new HashSet<>();
            for(AdjustPriceDto dto:dtos){
                String areaCodeStr = dto.getAreaCode();
                if(!StringUtils.isEmpty(areaCodeStr)){
                    String[] areacodearray = areaCodeStr.split(",");
                    for(String str:areacodearray){
                        areaCodes.add(Integer.valueOf(str));
                    }
                }
            }
            XyySaasRegionParamsDto xyySaasRegionParamsDto = new XyySaasRegionParamsDto();
            xyySaasRegionParamsDto.setAreaCodes(new ArrayList<>(areaCodes));
            List<SaasRegionBusinessDto> regionBusinessDtos = saasAreaApi.queryRegionByAreaCodeList(xyySaasRegionParamsDto);
            Map<String,String> areaMap = new HashMap<>();
            if(regionBusinessDtos != null && regionBusinessDtos.size() > 0){
                logger.info("打印出的areacode:{}",areaCodes);
                for(SaasRegionBusinessDto dto:regionBusinessDtos){
                    logger.info("arcecode:{},name:{}",dto.getAreaCode(),dto.getAreaName());
                    areaMap.put(String.valueOf(dto.getAreaCode()),dto.getAreaName());
                }
            }
            Map<String,String> idToNameMap = new HashMap<>();
            if(userSelectFlag == 0){//查询总部机构下所有的员工
                SaaSEmployeeExtendDto saasEmployeeExtendDto = new SaaSEmployeeExtendDto();
                saasEmployeeExtendDto.setHeadquartersOrganSign(organSign);
                List<SaaSEmployeeDto> employeeList = employeeApi.getHeadquartersAndDepartmentAndBranchStoreEmployeeList(saasEmployeeExtendDto);
                if(employeeList != null && employeeList.size() > 0){
                    for(SaaSEmployeeDto dto:employeeList){
                        idToNameMap.put(String.valueOf(dto.getId()),dto.getName());
                    }
                }
            }else{//查询特定机构下的所有员工
                List<EmployeeDto> employeeList = employeeApi.queryAllEmployeeByOrganSign(organSign);
                if(employeeList != null && employeeList.size() > 0){
                    for(EmployeeDto dto:employeeList){
                        idToNameMap.put(String.valueOf(dto.getId()),dto.getName());
                    }
                }
            }
            HashMap<String, String> map = null;
            boolean manufactorFlag = false;
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(bizModel)){
                ManufactorDto manufactor = new ManufactorDto();
                manufactor.setOrganSign(organSign);
                List<ManufactorDto> manufactorDtoList= this.manufactorApi.queryManufactorListNoPage(manufactor);
                map = new HashMap<String, String>();
                if(manufactorDtoList!=null &&manufactorDtoList.size()>0){
                    manufactorFlag = true;
                    for (ManufactorDto manufactorDto:manufactorDtoList){
                        map.put(manufactorDto.getId()+"",manufactorDto.getName());
                    }
                }
            }
            for(AdjustPriceDto dto:dtos){
                AdjustPriceInfoVo vo = new AdjustPriceInfoVo();
                vo.setId(dto.getId());
                vo.setAdjustOrganSignName(organSignMap.get(dto.getOrganSign()));
                vo.setAdjustReason(dto.getRemark());
                vo.setAdjustUserName(idToNameMap.get(dto.getCreateUser()));
                StringBuilder sb = new StringBuilder();
                if(!StringUtils.isEmpty(dto.getApplicableStores())){
                    if(dto.getApplicableStores().equals("all")){
                        vo.setApplicableStores("全部门店");
                    }else{
                        String[] drugStores = dto.getApplicableStores().split(",");
                        for(String organSignStr:drugStores){
                            sb.append(organSignMap.get(organSignStr)).append(",");
                        }
                        vo.setApplicableStores(sb.toString());
                    }
                }
                vo.setAttributeSpecification(dto.getAttributeSpecification());
                vo.setCommonName(dto.getCommonName());
                vo.setJixingName(jixingMap.get(dto.getDosageFormId()));
                vo.setManufacturer(dto.getManufacturer());
                vo.setNewMemberPrice(dto.getNewMemberPrice());
                vo.setNewPrice(dto.getNewPrice());
                vo.setPharmacyPref(dto.getPharmacyPref());
                vo.setPref(dto.getPref());
                StringBuilder sb1 = new StringBuilder();
                if(!StringUtils.isEmpty(dto.getAreaCode())){
                    String[] arrays = dto.getAreaCode().split(",");
                    for(String strs:arrays){
                        sb1.append(areaMap.get(strs));
                    }
                }
                //生产厂商
                if(manufactorFlag){
                    vo.setManufacturer(map.get(dto.getManufacturerCode()));
                }
                vo.setProducingArea(sb1.toString());
                vo.setUnitName(danweiMap.get(dto.getUnitId()));
                vo.setVipPrice(dto.getOldMemberPrice());
                vo.setRetailPrice(dto.getOldPrice());
                vo.setProductName(dto.getProductName());
                vo.setCreateTime(dto.getCreateTime());
                vo.setUpdateTime(dto.getUpdateTime());
                results.add(vo);
            }
        }
        pageInfo.setList(results);
        ResultVO result = new ResultVO(pageInfo);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> findAllPartments(HttpServletRequest request) {
        String organSign = request.getHeader("organSign");
        List<AdjustOrganSignVo> vos = new ArrayList<>();
        if(!StringUtils.isEmpty(organSign)){
            List<SaaSDrugstoreDto> alldrugs = new ArrayList<>();
            SaaSDrugstoreDto headerDto = null;
            try{
                headerDto = drugstoreApi.getDrugstoreByOrganSign(organSign);
            }catch (Exception e){
                logger.error("call drugstoreApi.getDrugstoreByHeadquartersOrganSign occur error,organSign:",organSign,e);
            }
            alldrugs.add(headerDto);
            List<SaaSDrugstoreDto> drugs = new ArrayList<>();
            try{
                drugs = drugstoreApi.getDrugstoreByHeadquartersOrganSign(organSign);
            }catch (Exception e){
                logger.error("call drugstoreApi.getDrugstoreByHeadquartersOrganSign occur error,organSign:",organSign,e);
            }
            alldrugs.addAll(drugs);
            if(alldrugs != null && alldrugs.size() > 0){
                for(SaaSDrugstoreDto dto:alldrugs){
                    AdjustOrganSignVo vo = new AdjustOrganSignVo();
                    vo.setAdjustOrganSignName(dto.getDrugstoreName());
                    vo.setAdjustOrganSign(dto.getOrganSign());
                    vos.add(vo);
                }
            }

        }
        ResultVO result = new ResultVO(vos);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> findAllPartmentsV2(HttpServletRequest request, PriceAdjustQueryVo priceAdjustQueryVo) {
        logger.info("findAllPartmentsV2 priceAdjustQueryVo: {}", JSONObject.toJSON(priceAdjustQueryVo));
        String organSign = request.getHeader("organSign");
        List<AdjustOrganSignVo> vos = new ArrayList<>();
        if(!StringUtils.isEmpty(organSign)){
            List<SaaSDrugstoreDto> alldrugs = new ArrayList<>();
            SaaSDrugstoreDto headerDto = null;
            try{
                headerDto = drugstoreApi.getDrugstoreByOrganSign(organSign);
            }catch (Exception e){
                logger.error("call drugstoreApi.getDrugstoreByHeadquartersOrganSign occur error,organSign:",organSign,e);
            }
            alldrugs.add(headerDto);
            List<SaaSDrugstoreDto> drugs = new ArrayList<>();
            try{
                SaaSDrugstoreDto dto = new SaaSDrugstoreDto();
                dto.setIsDrugstoreHidden((byte) 0);
                logger.info("findAllPartmentsV2 organSign: {}, SaaSDrugstoreDto: {}", organSign, JSONObject.toJSON(dto));
                drugs = drugstoreApi.getDrugstoreByHeadquartersOrganSign(dto, organSign);
            }catch (Exception e){
                logger.error("call drugstoreApi.getDrugstoreByHeadquartersOrganSign occur error,organSign:",organSign,e);
            }
            alldrugs.addAll(drugs);
            if(alldrugs != null && alldrugs.size() > 0){
                for(SaaSDrugstoreDto dto:alldrugs){
                    AdjustOrganSignVo vo = new AdjustOrganSignVo();
                    vo.setAdjustOrganSignName(dto.getDrugstoreName());
                    vo.setAdjustOrganSign(dto.getOrganSign());
                    vos.add(vo);
                }
            }

        }
        ResultVO result = new ResultVO(vos);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> findAllDrugstores(HttpServletRequest request) {
        String organSign = request.getHeader("organSign");
        List<AdjustOrganSignVo> vos = new ArrayList<>();
        if(!StringUtils.isEmpty(organSign)){
            List<SaaSDrugstoreDto> drugs = new ArrayList<>();
            try{
                drugs = drugstoreApi.getDrugstoreByHeadquartersOrganSign(organSign);
            }catch (Exception e){
                logger.error("call drugstoreApi.getDrugstoreByHeadquartersOrganSign occur error,organSign:",organSign,e);
            }
            if(drugs != null && drugs.size() > 0){
                for(SaaSDrugstoreDto dto:drugs){
                    AdjustOrganSignVo vo = new AdjustOrganSignVo();
                    vo.setAdjustOrganSignName(dto.getDrugstoreName());
                    vo.setAdjustOrganSign(dto.getOrganSign());
                    vos.add(vo);
                }
            }

        }
        ResultVO result = new ResultVO(vos);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> queryPrice(HttpServletRequest request,@ApiParam(value = "商品价格查询实体对象" ,required=true )  @Valid @RequestBody  PriceAdjustQueryVo priceAdjustQueryVo) {
        String modelJson = request.getHeader("commonRequestModel");
        String organSign = request.getHeader("organSign");
        List<ProductPriceInfoVo> results = new ArrayList<>();
        if(priceAdjustQueryVo.getPage() == null || priceAdjustQueryVo.getPage() <= 0){
            priceAdjustQueryVo.setPage(1);
        }
        if(priceAdjustQueryVo.getRows() == null || priceAdjustQueryVo.getRows() <= 0){
            priceAdjustQueryVo.setRows(50);
        }
        DrugStorePrams pramsModel = processOrganJsonInfo(modelJson);//根据机构通用信息获取机构判断指标信息
        String headerOrganSign = pramsModel.getHeaderOrganSign();
        Byte bizModel = pramsModel.getBizModel();
        Byte organSignType = pramsModel.getOrganSignType();
        List<String> organSigns = new ArrayList<>();
        Map<String,String> organSignMap = new HashMap<>();
        DrugstoreTypeProductEnum drugstoreTypeProductEnum = DrugstoreTypeUtil.isDrugstoreTypeEnum(bizModel,organSignType);
        if(drugstoreTypeProductEnum == DrugstoreTypeProductEnum.SINGLE_DRUGSTORE){//单体逻辑
            organSigns.add(organSign);
        }else if(drugstoreTypeProductEnum == DrugstoreTypeProductEnum.CHAIN_DRUGSTORE){//连锁门店
            organSigns.add(organSign);
        }else if(drugstoreTypeProductEnum == DrugstoreTypeProductEnum.CHAIN_HEADQUARTERS){//连锁总部
            organSigns.add(priceAdjustQueryVo.getAdjustOrganSign());//查询传入的选择机构号
        }else if(drugstoreTypeProductEnum == DrugstoreTypeProductEnum.JOIN_DRUGSTORE){//连营门店

        }else if(drugstoreTypeProductEnum == DrugstoreTypeProductEnum.JOIN_HEADQUARTERS){//联营总部

        }
        else{//联营逻辑

        }
        List<SaaSDrugstoreDto> drugstoreDtos = new ArrayList<>();
        try{
            drugstoreDtos = drugstoreApi.getDrugstoreByMultipleOrganSign(organSigns);
        }catch (Exception e){
            logger.error("call drugstoreApi occur error,organSigns:{}",organSigns,e);
        }
        for(SaaSDrugstoreDto dto:drugstoreDtos){
            organSignMap.put(dto.getOrganSign(),dto.getDrugstoreName());
        }

        boolean isChainStore = false;
        if (DrugstoreBizModelEnum.CHAIN_STORE.toEquals(bizModel)) {
            isChainStore = true;
        }
        //新的商品自定义分类
        Map<Long, String> customTypeIdNameMap = new HashMap<>();
        if (isChainStore) {
            customTypeIdNameMap = saasCustomTypeApi.getCustomTypeIdNameMap(headerOrganSign == null ? organSign : headerOrganSign, CustomTypeBusinessTypeEnum.PRODUCT_TYPE.getValue());
        }

        PageInfo info = new PageInfo();
        info.setPageNum(priceAdjustQueryVo.getPage());
        info.setPageSize(priceAdjustQueryVo.getRows());
        QueryProductPriceDto reqDto = new QueryProductPriceDto();
        reqDto.setInfo(info);
        reqDto.setMixQuery(priceAdjustQueryVo.getMixQuery());
        reqDto.setProductType(priceAdjustQueryVo.getProductType());
        reqDto.setSystemType(priceAdjustQueryVo.getSystemType());
        reqDto.setOrganSigns(organSigns);
        reqDto.setSidx(priceAdjustQueryVo.getSidx());
        reqDto.setSord(priceAdjustQueryVo.getSord());
        //PageInfo pageInfo = productPriceApi.ruleProductQuery(info, priceAdjustQueryVo.getMixQuery(), priceAdjustQueryVo.getProductType(),priceAdjustQueryVo.getSystemType(), organSigns,null);
        PageInfo pageInfo = productPriceApi.ruleProductQuery(reqDto);
        List<ProductPriceDto> dtos = pageInfo.getList();
        if(dtos != null && dtos.size() > 0){
            //获取字典接口，单位，剂型，商品分类，自定义分类(单体，联营连锁要区分)
            List<Integer> ids = Arrays.asList(DictConstant.unitBussinessId, DictConstant.agentBussinessId, DictConstant.productSystemType,DictConstant.PRODUCT_THIRD_BUSSINESS_ID,DictConstant.commodtyTypeBussinessId);
            long start_time1 = System.currentTimeMillis();
            List<SystemDictDto> dictDtos = productDictionaryApi.findSystemDictDtoBybussinessIds(ids,organSign);
            Map<Integer,String> danweiMap = new HashMap<>();
            Map<Integer,String> jixingMap = new HashMap<>();
            Map<Integer,String> shangpinfenleiMap = new HashMap<>();
            Map<Integer,String> dantizidingyiMap = new HashMap<>();
            Map<Integer,String> liansuozidingyiMap = new HashMap<>();
            if(dictDtos != null && dictDtos.size() > 0){
                for(SystemDictDto dto:dictDtos){
                    if(dto.getBussinessId().equals(DictConstant.unitBussinessId)){
                        danweiMap.put(dto.getId(),dto.getName());
                    }
                    if(dto.getBussinessId().equals(DictConstant.commodtyTypeBussinessId)){
                        dantizidingyiMap.put(dto.getId(),dto.getName());
                    }
                    if(dto.getBussinessId().equals(DictConstant.agentBussinessId)){
                        jixingMap.put(dto.getId(),dto.getName());
                    }
                    if(dto.getBussinessId().equals(DictConstant.productSystemType)){
                        shangpinfenleiMap.put(dto.getId(),dto.getName());
                    }
                    if(dto.getBussinessId().equals(DictConstant.PRODUCT_THIRD_BUSSINESS_ID)){
                        liansuozidingyiMap.put(dto.getId(),dto.getName());
                    }
                }
            }
            Set<Integer> areaCodes = new HashSet<>();
            for(ProductPriceDto dto:dtos){
                String areaCodeStr = dto.getAreaCode();
                if(!StringUtils.isEmpty(areaCodeStr)){
                    String[] areacodearray = areaCodeStr.split(",");
                    for(String str:areacodearray){
                        areaCodes.add(Integer.valueOf(str));
                    }
                }
            }
            XyySaasRegionParamsDto xyySaasRegionParamsDto = new XyySaasRegionParamsDto();
            xyySaasRegionParamsDto.setAreaCodes(new ArrayList<>(areaCodes));
            List<SaasRegionBusinessDto> regionBusinessDtos = saasAreaApi.queryRegionByAreaCodeList(xyySaasRegionParamsDto);
            Map<String,String> areaMap = new HashMap<>();
            if(regionBusinessDtos != null && regionBusinessDtos.size() > 0){
                for(SaasRegionBusinessDto dto:regionBusinessDtos){
                    areaMap.put(String.valueOf(dto.getAreaCode()),dto.getAreaName());
                }
            }
            //生产厂商
            ManufactorDto manufactor = new ManufactorDto();
            manufactor.setOrganSign(organSign);
            List<ManufactorDto> manufactorDtoList= this.manufactorApi.queryManufactorListNoPage(manufactor);
            Map<String,String> manufactorMap = new HashMap<>();
            if(manufactorDtoList != null && manufactorDtoList.size() > 0){
                for(ManufactorDto mndto:manufactorDtoList){
                    manufactorMap.put(String.valueOf(mndto.getId()),mndto.getName());
                }
            }
            for(ProductPriceDto dto:dtos){
                ProductPriceInfoVo vo = new ProductPriceInfoVo();
                vo.setAttributeSpecification(dto.getAttributeSpecification());
                vo.setCommonName(dto.getCommonName());
                vo.setJixingName(jixingMap.get(dto.getDosageFormId()));
                vo.setManufacturer(manufactorMap.get(dto.getManufacturerCode()));
                vo.setPharmacyPref(dto.getPharmacyPref());
                StringBuilder sb = new StringBuilder();
                if(!StringUtils.isEmpty(dto.getAreaCode())){
                    String[] arrays = dto.getAreaCode().split(",");
                    for(String strs:arrays){
                        sb.append(areaMap.get(strs));
                    }
                }
                vo.setProducingArea(sb.toString());
                vo.setUnitName(danweiMap.get(dto.getUnitId()));
                vo.setVipPrice(dto.getVipPrice());
                vo.setRetailPrice(dto.getRetailPrice());
                vo.setOrganSignName(organSignMap.get(dto.getOrganSign()));
                vo.setProductTypeName(liansuozidingyiMap.get(dto.getProductType()));

                if (isChainStore && dto.getProductType() != null && customTypeIdNameMap.get(dto.getProductType().longValue()) != null) {
                    vo.setProductTypeName(customTypeIdNameMap.get(dto.getProductType().longValue()));
                } else {
                    vo.setProductTypeName(liansuozidingyiMap.get(dto.getProductType()));
                }

                vo.setSystemTypeName(shangpinfenleiMap.get(dto.getSystemType()));
                vo.setProductName(dto.getProductName());
                vo.setId(dto.getId());
                results.add(vo);
            }
        }
        pageInfo.setList(results);
        ResultVO result = new ResultVO(pageInfo);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> queryPriceOneProduct(HttpServletRequest request,@ApiParam(value = "商品价格查询实体对象" ,required=true )  @Valid @RequestBody  PriceAdjustQueryVo priceAdjustQueryVo) {
        String headOrganSign = request.getHeader("organSign");//总部机构号
        Integer page = null;
        Integer rows = null;
        if(priceAdjustQueryVo.getPage() == null || priceAdjustQueryVo.getPage() <= 0){
            page = 1;
        }else {
            page = priceAdjustQueryVo.getPage();
        }
        if(priceAdjustQueryVo.getRows() == null || priceAdjustQueryVo.getRows() <= 0){
            rows = 50;
        }else {
            rows = priceAdjustQueryVo.getRows();
        }
        List<String> selectOrganSigns = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(priceAdjustQueryVo.getDepartOrganSigns())){
            selectOrganSigns = priceAdjustQueryVo.getDepartOrganSigns();
        }else{
            selectOrganSigns.add(headOrganSign);//总部机构号
            List<SaaSDrugstoreDto> drugs = new ArrayList<>();
            try{
                drugs = drugstoreApi.getDrugstoreByHeadquartersOrganSign(headOrganSign);
            }catch (Exception e){
                logger.error("call drugstoreApi.getDrugstoreByHeadquartersOrganSign occur error,organSign:",drugs,e);
            }
            for(SaaSDrugstoreDto drugstoreDto:drugs){
                selectOrganSigns.add(drugstoreDto.getOrganSign());
            }
        }
        ResultVO<PageInfo<ProductPriceDto>> resultVO = productPriceApi.queryPriceByPrefAndOrganSigns(selectOrganSigns,priceAdjustQueryVo.getProductPref(),page,rows);
        PageInfo<ProductPriceDto> pages = resultVO.getResult();
        List<ProductPriceDto> productPices = pages.getList();
        if(CollectionUtils.isNotEmpty(productPices)){
            Map<String,String> organSignToNameMap = new HashMap<>();
            List<SaaSDrugstoreDto> drugstoreDtos = new ArrayList<>();
            try{
                drugstoreDtos = drugstoreApi.getDrugstoreByMultipleOrganSign(selectOrganSigns);
            }catch (Exception e){
                logger.error("call drugstoreApi.getDrugstoreByMultipleOrganSign occur errro,adjustOrganSigns:{}",drugstoreDtos,e);
            }
            for(SaaSDrugstoreDto drugstoreDto2:drugstoreDtos){
                organSignToNameMap.put(drugstoreDto2.getOrganSign(),drugstoreDto2.getDrugstoreName());
            }
            for(ProductPriceDto productPriceDto:productPices){
                productPriceDto.setOrganSignName(organSignToNameMap.get(productPriceDto.getOrganSign()));
            }
        }
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    /**
     * 统一处理相同的逻辑代码
     * @param modelJson
     * @param bizModel
     * @param organSignType
     * @param headquartersOrganSign
     */
    private DrugStorePrams processOrganJsonInfo(String modelJson){
        DrugStorePrams pramsModel = new DrugStorePrams();
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            pramsModel.setBizModel(model.getBizModel());
            pramsModel.setOrganSignType(model.getOrganSignType());
            pramsModel.setHeaderOrganSign(model.getHeadquartersOrganSign());
        }
        return pramsModel;
    }

    private int processLsHeaderList(Map<String,String> organSignMap,String organSign,Byte approveOrganType,PriceAdjustQueryVo priceAdjustQueryVo,List<String> organSigns){
        SaaSDrugstoreDto headerDrugStore = null;
        try{
            headerDrugStore = drugstoreApi.getDrugstoreByOrganSign(organSign);
        }catch (Exception e){
            logger.error("call drugstoreApi.getDrugstoreByOrganSign occur error {}",organSign,e);
            return -1;
        }
        organSignMap.put(organSign,headerDrugStore.getDrugstoreName());
        List<SaaSDrugstoreDto> drugs = null;
        try{
            SaaSDrugstoreDto dto = new SaaSDrugstoreDto();
            dto.setIsDrugstoreHidden(priceAdjustQueryVo.getIsDrugstoreHidden());
            drugs = drugstoreApi.getDrugstoreByHeadquartersOrganSign(dto, organSign);
        }catch (Exception e){
            logger.error("call drugstoreApi.getDrugstoreByHeadquartersOrganSign occur error {}",organSign,e);
            return -1;
        }
        if(CollectionUtils.isNotEmpty(drugs)){
            organSignMap.putAll(drugs.stream().collect(Collectors.toMap(dto -> dto.getOrganSign(),dto -> dto.getDrugstoreName())));
        }
        if(priceAdjustQueryVo.getAdjustOrganSign().equals("all")){
            organSigns.add(organSign);
            organSigns.addAll(drugs.stream().map(SaaSDrugstoreDto::getOrganSign).collect(Collectors.toList()));
        }else {
            organSigns.add(priceAdjustQueryVo.getAdjustOrganSign());
        }
        return 1;
    }

    /**
     * 判断用户是否有审批权限
     * @param dto
     * @param approveOrganType
     * @param currentRoleIds
     * @return  1：可以审批，2：不可以审批
     */
    private int processIsCanCheck(PriceAdjustDto dto,Byte approveOrganType,List<Integer> currentRoleIds){
        if(dto.getApproveOrganSign() == approveOrganType){
            if(dto.getAuditState() == 1){
                if(currentRoleIds.contains(dto.getTodoRoleId())){
                    return 1;
                }else{
                    return 2;
                }
            }else{
                return 2;
            }
        }else{
            return 2;
        }
    }

    /**
     * 判断用户是否有审批权限
     * @param dto
     * @param approveOrganType
     * @param currentRoleIds
     * @return  1：可以审批，2：不可以审批
     */
    private int processIsCanCheckLs(PriceAdjustPoLsDto dto,Byte approveOrganType,List<Integer> currentRoleIds){
        if(dto.getApproveOrganSign() == approveOrganType){
            if(dto.getAuditState() == 1){
                if(currentRoleIds.contains(dto.getTodoRoleId())){
                    return 1;
                }else{
                    return 2;
                }
            }else{
                return 2;
            }
        }else{
            return 2;
        }
    }

    /**
     * 处理当前代办人逻辑
     * @param approveOrganSign
     * @return
     */
    private String processApproveOrganSignName(Byte approveOrganSign){
        if(approveOrganSign == 1){
            return "门店";
        }else if(approveOrganSign == 3){
            return "总部";
        }else if(approveOrganSign == -1){
            return "自动审批";
        }else{
            return "--";
        }
    }

    /**
     *
     * @param dto
     * @param vo
     * @param approveOrganType
     * @param currentRoleIds
     * @param organSignMap
     */
    private void copyDtoToVo(PriceAdjustDto dto,AdjustBaseInfoVo vo,Byte approveOrganType,List<Integer> currentRoleIds,Map<String,String> organSignMap){
        vo.setId(dto.getId());
        if(dto.getAuditState() == 1){
            vo.setCurrentAgent(dto.getTodoRoleName());//当前角色名称
        }else{
            vo.setCurrentAgent("--");//当前角色名称
        }
        vo.setRoleId(dto.getTodoRoleId());//当前角色id
        vo.setCanApprove((byte) processIsCanCheck(dto, approveOrganType, currentRoleIds));//审批权限逻辑处理
        logger.info("调价单列表审批校验，pref:{},当前机构类型:{},审核权限机构类型:{},当前用户权限列表:{},审核权限id:{}",dto.getPref(),approveOrganType,dto.getApproveOrganSign(),currentRoleIds,dto.getTodoRoleId());
        vo.setApproveOrganSignName(processApproveOrganSignName(dto.getApproveOrganSign()));//当前代办人业务逻辑处理
        vo.setApplyState(dto.getAuditState().intValue());//审核状态字段
        vo.setAdjustReason(dto.getRemark());
        vo.setCreateUserName(dto.getCreateUserName());
        vo.setCreateTimeStr(DateUtil.parseDateToStr(dto.getCreateTime(), DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS));
        vo.setAdjustOrganSignName(organSignMap.get(dto.getOrganSign()));
        vo.setPref(dto.getPref());
        vo.setCheckId(dto.getPref());
    }

    private void copyVoPramsToDto(PriceAdjustDto priceAdjustDto,PriceAdjustQueryVo priceAdjustQueryVo,List<String> organSigns,String organSign){
        BeanUtils.copyProperties(priceAdjustQueryVo, priceAdjustDto);
        priceAdjustDto.setOrganSign(organSign);
        priceAdjustDto.setOrganSigns(organSigns);
        priceAdjustDto.setStarttime(priceAdjustQueryVo.getStartTime());
        priceAdjustDto.setEndtime(priceAdjustQueryVo.getEndTime());
        priceAdjustDto.setUsername(priceAdjustQueryVo.getUserName());
        priceAdjustDto.setAuditState(priceAdjustQueryVo.getAproveStatus());
        priceAdjustDto.setPage(priceAdjustQueryVo.getPage());
        priceAdjustDto.setRows(priceAdjustQueryVo.getRows());
    }

    private void copyVoPramsToDtoSubmit(PriceAdjustDto priceAdjustDto,PriceAdjustVo priceAdjustVo,String userName,String organSign){
        priceAdjustDto.setRemark(priceAdjustVo.getAdjustReason());
        priceAdjustDto.setOrganSign(organSign);
        priceAdjustDto.setAdjustReason(priceAdjustVo.getAdjustReason());
        priceAdjustDto.setPref(priceAdjustVo.getPref());
        priceAdjustDto.setApplicableStores(priceAdjustVo.getApplicableStores());
        priceAdjustDto.setCreateUserName(userName);//赋值用户名称
        priceAdjustDto.setUpdateBaseFlag(priceAdjustVo.getUpdateBaseFlag()==null?0:priceAdjustVo.getUpdateBaseFlag());
    }

    private ResultVO submitSingleAdjust(PriceAdjustDto priceAdjustDto,String drugType){
        ResultVO result = null;
        try{
            result = adjustPriceApi.submit(priceAdjustDto);//暂时不涉及到单体的调试逻辑
        }catch (Exception e){
            logger.error("{}调价单提交接口报错，提交信息为：{}具体错误信息如下：",drugType,JSONObject.toJSON(priceAdjustDto),e);
            result = new ResultVO(ResultCodeEnum.ERROR, drugType+"调价单提交报错,请稍后重试");
        }
        return result;
    }

    private ResultVO submitLsAdjust(PriceAdjustDto priceAdjustDto,PriceAdjustVo priceAdjustVo,Byte bizModel,Byte organSignType,String organSign,String employee,String headerOrganSign,String organType ){
        ResultVO result = null;
        priceAdjustDto.setHeaderOrganSign(headerOrganSign);
        priceAdjustDto.setUsername(employee);
        priceAdjustDto.setOrganSign(organSign);
        priceAdjustDto.setRemark(priceAdjustVo.getAdjustReason());
        try{
            result = adjustPriceApi.submitLs(priceAdjustDto,bizModel,organSignType);//暂时不涉及到单体的调试逻辑
            if((int)result.getResult() == -1){
                result = new ResultVO(ResultCodeEnum.ERROR, "提交失败,审批流生成异常,请重新提交");
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error("{}提交接口报错，提交信息为：{}具体错误信息如下：",organType,JSONObject.toJSON(priceAdjustDto),e);
            result = new ResultVO(ResultCodeEnum.ERROR, "连锁调价单提交报错", null);
        }
        return result;
    }

    @Override
    public ResponseEntity<ResultVO> query(HttpServletRequest request, @Valid @RequestBody PriceAdjustDto priceAdjustDto) {
        priceAdjustDto.setRows(priceAdjustDto.getRows()==null?10:priceAdjustDto.getRows());
        priceAdjustDto.setPage(priceAdjustDto.getPage()==null?1:priceAdjustDto.getPage());
        priceAdjustDto.setOrganSign(request.getHeader("organSign"));
        List<String> organSigns = new ArrayList<>();
        organSigns.add(request.getHeader("organSign"));
        priceAdjustDto.setOrganSigns(organSigns);
        ResultVO<PageInfo<PriceAdjustDto>> resutVO = this.adjustPriceApi.list(priceAdjustDto);
        return new ResponseEntity<ResultVO>(resutVO, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> queryAdjustDetails(HttpServletRequest request, @Valid @RequestBody PriceAdjustDto priceAdjustDto) {
        String identity = request.getHeader("identity");
        String organSign = request.getHeader("organSign");
        Byte isProductHidden = Byte.valueOf(org.apache.commons.lang.StringUtils.isBlank(identity)?"0":identity);
        if ( priceAdjustDto.getId() == null )
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(null), HttpStatus.OK);

        /*// 根据主表ID查询售价商品详情信息  返回的数据是一个售价调整单对应的多个商品信息
        PageInfo info = new PageInfo();
        info.setPageSize(null==rows?10:rows);
        info.setPageNum(null==page?1:page);*/

        List<ProPriceAdjustDto> result = this.adjustPriceApi.getProductPriceAdjustDetailById(priceAdjustDto.getId(), isProductHidden, organSign);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(new PageInfo<>(result)), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> queryProductList(HttpServletRequest request, @Valid @RequestBody PriceAdjustDto priceAdjustDto) {
        String identity = request.getHeader("identity");
        Byte isProductHidden = Byte.valueOf(identity);
//        String currentOrganSign = getCurrentOrganSign();
        String organSign = request.getHeader("organSign");
        PageInfo info = new PageInfo();
        info.setPageNum(priceAdjustDto.getPage()==null?1:priceAdjustDto.getPage());
        info.setPageSize(priceAdjustDto.getRows()==null?10:priceAdjustDto.getRows());
        // 售价变动接口
        PageInfo<ProductVoDto> result = this.adjustPriceApi.queryProductList(priceAdjustDto.getProname(), info, isProductHidden, organSign);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(result), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> adjustPriceSave(HttpServletRequest request, @Valid @RequestBody PriceAdjustDto data) {
        String organSign = request.getHeader("organSign");
        if ( org.apache.commons.lang.StringUtils.isEmpty(data.getData()) ){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, null), HttpStatus.OK);
        }
        PriceAdjustDto priceAdjustDto = JSONObject.parseObject(data.getData(), PriceAdjustDto.class);
        priceAdjustDto.setUsername(String.valueOf(priceAdjustDto.getUserId()));
        //TODO 设置机构
        priceAdjustDto.setOrganSign(organSign);
        //  售价变动接口
        ResultVO resultVO = this.adjustPriceApi.save(priceAdjustDto);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> forSaveProductPrice(HttpServletRequest request, @Valid @RequestBody PriceAdjustDto priceAdjustDto) {
        String [] result = priceAdjustDto.getData().split(",");
        List<String> strings = Arrays.asList(result);
        String organSign = request.getHeader("organSign");
        logger.info("forSaveProductPrice  productbaseinfo result product:"+strings);
        ProductDto product = new ProductDto();
        product.setProductPharmacyPrefs(strings);
        logger.info("forSaveProductPrice  productbaseinfo result product:"+product.getProductPharmacyPrefs().toString());
        String s = productApi.forSaveProductPrice(organSign, product);
        if(s!=null){
            ResultVO resultVO = JSON.parseObject(s, ResultVO.class);
            return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
        }
        String[] strings1 = new String[0];
        return new ResponseEntity<ResultVO>(new ResultVO(strings1),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> adjustPriceSubmit(HttpServletRequest request, @Valid @RequestBody PriceAdjustDto data) {
        String organSign = request.getHeader("organSign");
        if ( org.apache.commons.lang.StringUtils.isEmpty(data.getData()) ){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, null), HttpStatus.OK);
        }
        PriceAdjustDto priceAdjustDto = JSONObject.parseObject(data.getData(), PriceAdjustDto.class);
        priceAdjustDto.setOrganSign(organSign);
        priceAdjustDto.setUsername(String.valueOf(priceAdjustDto.getUserId()));
        // 售价变动接口
        ResultVO resultVO = this.adjustPriceApi.submit(priceAdjustDto);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> queryImportList(HttpServletRequest request,@RequestBody PriceImportVo param) {
        String organSign = request.getHeader("organSign");
        Long taskId = param.getTaskId();
        List<AdjustDetailVo> vos = new ArrayList<>();
        if (null == taskId){
            return new ResponseEntity<ResultVO>(new ResultVO(vos), HttpStatus.OK);
        }

        List<ProductPriceListDto> priceListDtos = productApi.queryPriceImportList(taskId);
        if (CollectionUtils.isNotEmpty(priceListDtos)) {
            for (ProductPriceListDto dto : priceListDtos) {
                AdjustDetailVo vo = new AdjustDetailVo();
                vo.setAttributeSpecification(dto.getSpec());
                vo.setCommonName(dto.getCommonName());
                vo.setId(dto.getId());
                vo.setJixingName(dto.getDosageForm());
                vo.setDosageFormName(dto.getDosageForm());
                vo.setManufacturer(dto.getManufacturer());//生产厂商由id转name
                vo.setPharmacyPref(dto.getPharmacyPref());
                vo.setProductName(dto.getProductName());
                vo.setUnitName(dto.getUnit());
                vo.setRetailPrice(dto.getRetailPrice());
                vo.setNewPrice(dto.getNewRetailPrice());
                if (null == dto.getVipPrice() || BigDecimal.ZERO.compareTo(dto.getVipPrice()) == 0){
                    vo.setVipPrice(dto.getRetailPrice());
                }else {
                    vo.setVipPrice(dto.getVipPrice());
                }
                vo.setNewMemberPrice(dto.getNewVipPrice());
                vo.setProductPref(dto.getPref());
                vo.setPref(dto.getPref());
                vo.setProducingArea(dto.getArea());
                vo.setSystemTypeName(dto.getSystemType());
                vos.add(vo);
            }
        }
        return new ResponseEntity<ResultVO>(new ResultVO(vos), HttpStatus.OK);
    }


}
