package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName ProductZbDtqApplyQueryVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/29 13:35
 * @Version 1.0
 **/
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

@ApiModel(description = "商品提报查询信息类")
public class ProductZbDtqApplyQueryVo {
    @JsonProperty("mixQuery")
    private String mixQuery;//商品信息：支持商品编号、批准文号、条形码、助记码、商品名称、通用名称进行查询，和总部商品查询保持一致

    @ApiModelProperty(value = "标准库id")
    public String getStandardLibraryId() {
        return standardLibraryId;
    }

    public void setStandardLibraryId(String standardLibraryId) {
        this.standardLibraryId = standardLibraryId;
    }

    @JsonProperty("standardLibraryId")
    private String standardLibraryId;//标准库id
    @JsonProperty("chainStoreOrgansign")
    private String chainStoreOrgansign;//来源门店
    @JsonProperty("manufacturer")
    private String manufacturer;//生产厂家

    @ApiModelProperty(value = "当前页码")
    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    @ApiModelProperty(value = "每页显示多少")
    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    @JsonProperty("page")
    private Integer page;
    @JsonProperty("rows")
    private Integer rows;

    @ApiModelProperty(value = "商品信息：支持商品编号、批准文号、条形码、助记码、商品名称、通用名称进行查询，和总部商品查询保持一致")
    public String getMixQuery() {
        return mixQuery;
    }

    public void setMixQuery(String mixQuery) {
        this.mixQuery = mixQuery;
    }

    @ApiModelProperty(value = "生产厂家")
    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    @ApiModelProperty(value = "来源门店")
    public String getChainStoreOrgansign() {
        return chainStoreOrgansign;
    }

    public void setChainStoreOrgansign(String chainStoreOrgansign) {
        this.chainStoreOrgansign = chainStoreOrgansign;
    }
}
