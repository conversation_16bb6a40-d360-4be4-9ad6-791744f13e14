package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.member.core.dto.MemberShareDto;
import com.xyy.saas.web.api.module.member.model.MemberSmsReplyParamVo;
import com.xyy.saas.web.api.module.utils.DateUtil;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.api.sms.SaasSmsBlacklistApi;
import com.xyy.user.module.api.sms.SaasSmsReplyApi;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import com.xyy.user.module.dto.result.EmployeeDto;
import com.xyy.user.module.dto.sms.SaasSmsBlacklistDto;
import com.xyy.user.module.dto.sms.SaasSmsBlacklistParamDto;
import com.xyy.user.module.dto.sms.SaasSmsReplyParamDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-08-29T21:37:06.377+08:00")

@Slf4j
@Controller
@RequestMapping("/member/memberSmsReply/zb")
@Api(value = "memberSmsReply", description = "the memberSmsReply API")
public class MemberSmsReplyForZBApiController {

    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;

    @Reference(version = "0.0.1")
    private EmployeeApi employeeApi;

    @Reference(version = "0.0.1")
    private SaasSmsReplyApi saasSmsReplyApi;

    @Reference(version = "0.0.1")
    private SaasSmsBlacklistApi saasSmsBlacklistApi;

    @ApiOperation(value = "分页获取短信回复记录列表", notes = "分页获取短信回复记录列表", response = MemberShareDto.class, tags = {"memberSmsReply",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = PageInfo.class)})
    @RequestMapping(value = "/queryList", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> queryList(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                       @RequestBody SaasSmsReplyParamDto paramDto) {
        try {
            CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
            String rootOrganSign = commonRequestModel.getOrganSign();
            if(commonRequestModel.getOrganSignType() != 3) {
                SaaSDrugstoreDto drugstoreDto = drugstoreApi.getDrugstoreByOrganSign(rootOrganSign);
                rootOrganSign = drugstoreDto.getHeadquartersOrganSign();
            }
            paramDto.setRootOrganSign(rootOrganSign);
            if (paramDto.getStartTime() != null) {
                paramDto.setStartTime(DateUtil.getDayBeginTime(paramDto.getStartTime()));
            }
            if (paramDto.getEndTime() != null) {
                paramDto.setEndTime(DateUtil.getDayEndTime(paramDto.getEndTime()));
            }
            return new ResponseEntity(saasSmsReplyApi.pageSearch(paramDto), HttpStatus.OK);
        } catch (Exception e) {
            log.error("MemberSmsReplyForZBApiController#queryList error, param:{}", JSON.toJSONString(paramDto), e);
            return new ResponseEntity(ResultVO.createError(e.getMessage()), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "加入或移除黑名单", notes = "分页获取短信回复记录列表", response = MemberShareDto.class, tags = {"memberSmsReply",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = PageInfo.class)})
    @RequestMapping(value = "/addOrRemoveToBlacklist", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> addOrRemoveToBlacklist(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                       @RequestBody MemberSmsReplyParamVo paramVo) {
        log.info("MemberSmsReplyForZBApiController#addOrRemoveToBlacklist commonRequestModelStr:{}, param:{}", commonRequestModelStr, JSON.toJSONString(paramVo));
        if (paramVo == null || paramVo.getSmsReply() == null || paramVo.getOptType() == null) {
            return new ResponseEntity(ResultVO.createError("参数不合法"), HttpStatus.OK);
        }
        try {
            CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
            String organSign = commonRequestModel.getOrganSign();

            String employeeId = commonRequestModel.getEmployeeId();
            EmployeeDto employeeDto = employeeApi.queryEmployeeById(Integer.valueOf(employeeId)).getResult();

            ResultVO resultVO = ResultVO.createSuccess();
            if (paramVo.getOptType() == 1) { // 加入黑名单
                SaasSmsBlacklistParamDto paramDto = new SaasSmsBlacklistParamDto();

                List<SaasSmsBlacklistDto> blacklistDtos = new ArrayList<>();

                SaasSmsBlacklistDto blacklistDto = new SaasSmsBlacklistDto();
                BeanUtils.copyProperties(paramVo.getSmsReply(), blacklistDto);
                blacklistDto.setId(null);
                blacklistDto.setCreateUserId(Long.valueOf(employeeId));
                blacklistDto.setCreateUserName(employeeDto.getName());
                blacklistDto.setCreateTime(new Date());

                blacklistDtos.add(blacklistDto);

                paramDto.setBlacklistDtos(blacklistDtos);
                resultVO = saasSmsBlacklistApi.batchInsert(paramDto);
            } else if (paramVo.getOptType() == 2) {
                resultVO = saasSmsBlacklistApi.deleteByMemberIdAndTelephone(paramVo.getSmsReply().getMemberId(), paramVo.getSmsReply().getTelephone());
            }

            return new ResponseEntity(resultVO, HttpStatus.OK);
        } catch (Exception e) {
            log.error("MemberSmsReplyForZBApiController#addOrRemoveToBlacklist error, param:{}", JSON.toJSONString(paramVo), e);
            return new ResponseEntity(ResultVO.createError(e.getMessage()), HttpStatus.OK);
        }
    }

}
