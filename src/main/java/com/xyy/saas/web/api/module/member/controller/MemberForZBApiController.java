package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.DateUtil;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.common.util.MD5Util;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.member.core.api.MemberBaseForZBApi;
import com.xyy.saas.member.core.api.MemberLevelForZBApi;
import com.xyy.saas.member.core.api.MemberPrepayCardForZBApi;
import com.xyy.saas.member.core.dto.*;
import com.xyy.saas.web.api.module.utils.ExportExcelUtil;
import com.xyy.user.module.dto.result.QueryEmployeeVO;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import com.xyy.saas.web.api.module.member.model.*;
import com.xyy.saas.web.api.module.member.utils.ResultCodeEnum;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/member/member/zb")
@Api(value = "会员资料（总部端）API", description = "会员资料（总部端）API")
public class MemberForZBApiController {
    private static final Logger logger = LogManager.getLogger(MemberForZBApiController.class);

    @Reference(version = "0.0.1")
    private MemberLevelForZBApi memberLevelForZBApi;

    @Reference(version = "0.0.1")
    private MemberBaseForZBApi memberBaseForZBApi;

    @Reference(version = "0.0.1")
    private MemberPrepayCardForZBApi memberPrepayCardForZBApi;

    @Reference(version = "0.0.1")
    private com.xyy.saas.member.core.api.MemberBaseApi memberBaseApi;

    //---------------------web-plus会员资料迁移的相关接口-----------------------


    @ApiOperation(value = "总部端:会员资料及储值保存,编辑", notes = "会员资料及储值保存,编辑", response = ResultVO.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class)})
    @RequestMapping(value = "/saveAndPrePay", method = RequestMethod.POST, produces = "application/json",
            consumes = "application/json")
    public ResponseEntity<ResultVO> saveAndPrePay(@RequestHeader("commonRequestModel") String commonRequestModel, @ApiParam(value = "会员基本信息", required = true) @RequestBody MemberBaseVo memberBaseVo) {
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        String organSign = model.getOrganSign();//总部机构号
        String createUser = memberBaseVo.getCreateUser();
        String updateUserId = model.getEmployeeId();
        logger.info("总部端:会员资料及储值保存,编辑: MemberBaseVo"+ JSONUtils.obj2JSON(memberBaseVo) );
        boolean flag = false;
        MemberBaseDto baseDto = new MemberBaseDto();
        BeanUtils.copyProperties(memberBaseVo, baseDto);
        if (!org.springframework.util.StringUtils.isEmpty(memberBaseVo.getSendCardTime())) {
            Date sendCardTime = DateUtil.parseStrToDate(memberBaseVo.getSendCardTime(), DateUtil.DATE_FORMAT_YYYY_MM_DD);
            baseDto.setSendCardTime(sendCardTime);
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberBaseVo.getBirthday())) {
            Date birthday = DateUtil.parseStrToDate(memberBaseVo.getBirthday(), DateUtil.DATE_FORMAT_YYYY_MM_DD);
            baseDto.setBirthday(birthday);
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberBaseVo.getEffectTime())) {
            Date effectTime = DateUtil.parseStrToDate(memberBaseVo.getEffectTime(), DateUtil.DATE_FORMAT_YYYY_MM_DD);
            baseDto.setEffectTime(effectTime);
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberBaseVo.getExpriedTime())) {
            Date expriedTime = DateUtil.parseStrToDate(memberBaseVo.getExpriedTime(), DateUtil.DATE_FORMAT_YYYY_MM_DD);
            baseDto.setExpriedTime(expriedTime);
        }
        if (memberBaseVo.getPoint() != null && memberBaseVo.getPoint().compareTo(BigDecimal.ZERO) < 0) {
            return new ResponseEntity(new ResultVO(-1, "积分不能是负数", flag), HttpStatus.OK);
        }
        if (memberBaseVo.getBonus() != null && memberBaseVo.getBonus().compareTo(BigDecimal.ZERO) < 0) {
            return new ResponseEntity(new ResultVO(-1, "赠送金额不能是负数", flag), HttpStatus.OK);
        }
        if (memberBaseVo.getAmount() != null && memberBaseVo.getAmount().compareTo(BigDecimal.ZERO) < 0) {
            return new ResponseEntity(new ResultVO(-1, "卡内余额不能是负数", flag), HttpStatus.OK);
        }
        if (StringUtils.isEmpty(memberBaseVo.getOrgansign())) {
            return new ResponseEntity(new ResultVO(-1, "来源门店不能为空", flag), HttpStatus.OK);
        }
        boolean result = false;
        if (baseDto.getId() != null) {
            baseDto.setOrgansign(memberBaseVo.getOrgansign());
            baseDto.setUpdateUser(updateUserId);
            baseDto.setHeadquartersOrganSign(organSign);
            MemberLevelDto level = memberLevelForZBApi.getMemberLevelByIdZB(baseDto.getVipLevelId());
            if (baseDto.getAllPoint().doubleValue() < level.getNeedPoint().doubleValue()) {
                return new ResponseEntity(new ResultVO(-1, "会员等级的所需积分不能大于累计积分，请修改会员等级", flag), HttpStatus.OK);
            }
            logger.info("总部端:会员资料及储值编辑: MemberBase"+ JSONUtils.obj2JSON(baseDto) );
            result = memberBaseForZBApi.updateExpireDateMemberBaseForZB(baseDto);
        } else {
            baseDto.setOrgansign(memberBaseVo.getOrgansign());//来源门店
            baseDto.setCreateUser(createUser);
            baseDto.setHeadquartersOrganSign(organSign);
            if(baseDto.getPoint() == null ){
                baseDto.setPoint(BigDecimal.ZERO);
                baseDto.setAllPoint(BigDecimal.ZERO);
            }else{
                baseDto.setAllPoint(baseDto.getPoint());
            }
            MemberLevelDto level = memberLevelForZBApi.getMemberLevelByIdZB(baseDto.getVipLevelId());
            if (baseDto.getPoint().doubleValue() < level.getNeedPoint().doubleValue()) {
                return new ResponseEntity(new ResultVO(-1, "会员等级的所需积分不能大于会员积分，请修改会员等级", flag), HttpStatus.OK);
            }
            logger.info("总部端:会员资料及储值保存: MemberBase"+ JSONUtils.obj2JSON(baseDto) );
            result = memberBaseForZBApi.saveMemberBaseForZB(baseDto);
            if (!result) {
                logger.info("MemberController.saveAndPrePay: 会员资料新增失败");
                return new ResponseEntity(new ResultVO(-1, "会员资料新增失败", flag), HttpStatus.OK);
            }
            if ((memberBaseVo.getAmount() != null && memberBaseVo.getAmount().compareTo(BigDecimal.ZERO) > 0) || (memberBaseVo.getBonus() != null && memberBaseVo.getBonus().compareTo(BigDecimal.ZERO) > 0)) {
                MemberBaseDto memberBaseDto = new MemberBaseDto();
                memberBaseDto.setCartNo(baseDto.getCartNo());
                memberBaseDto.setOrgansign(memberBaseVo.getOrgansign());
                memberBaseDto.setHeadquartersOrganSign(organSign);
                List<MemberBaseDto> baseList = memberBaseForZBApi.getMemberBaseListForZB(memberBaseDto);
                MemberBaseDto dd = null;
                if (baseList != null && baseList.size() > 0) {
                    dd = baseList.get(0);
                }
                //充值--自身发送mq
                MemberPrepayCardDto dto = new MemberPrepayCardDto();
                BeanUtils.copyProperties(memberBaseVo, dto);
                //交易门店
                dto.setOrgansign(organSign);
                //来源门店
                dto.setHeadquartersOrganSign(memberBaseVo.getOrgansign());
                dto.setMemberGuid(dd.getGuid());
                dto.setCreateUser(createUser);
                dto.setCheckUser(memberBaseVo.getCheckUser());
                com.xyy.saas.member.core.response.ResultVO resultVO = memberPrepayCardForZBApi.depositForZB(dto);
                if (!(boolean)resultVO.getResult()) {
                    //删除刚才新增的会员信息
                    int del_result = memberBaseForZBApi.deleteMemberByConditionForZB(dd);
                    if (del_result <= 0) {
                        logger.info("MemberController.saveAndPrePay: 会员储值新增失败, 删除会员信息失败");
                        return new ResponseEntity(new ResultVO(-1, "会员储值新增失败, 删除会员信息失败", flag), HttpStatus.OK);
                    }
                    logger.info("MemberController.saveAndPrePay: 会员储值新增失败");
                    return new ResponseEntity(new ResultVO(-1, "会员储值新增失败", flag), HttpStatus.OK);
                }
            }
        }
        return new ResponseEntity<>(ResultVO.createSuccess("新增成功"), HttpStatus.OK);
    }


    @ApiOperation(value = "总部端:获取会员卡号", notes = "查询会员等级列表", response = ResultVO.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class)})
    @RequestMapping(value = "/getMemberCardNo", method = RequestMethod.POST, produces = "application/json",
            consumes = "application/json")
    public ResponseEntity<ResultVO> getMemberCardNo() {
        String cardNo = memberBaseForZBApi.getMemberCardNoForZB();
        return new ResponseEntity<>(ResultVO.createSuccess(cardNo), HttpStatus.OK);
    }

    @ApiOperation(value = "总部端:根据Guid查找会员信息", notes = "根据Guid查找会员信息", response = MemberBase.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberBase.class)})
    @RequestMapping(value = "/getMemberBaseByGuid",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getMemberBaseByGuid(@RequestHeader("commonRequestModel") String commonRequestModel, @ApiParam(value = "会员基本信息", required = true) @RequestBody MemberBaseVo memberBaseVo) {
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        MemberBaseDto memberBaseDto = new MemberBaseDto();
        memberBaseDto.setGuid(memberBaseVo.getGuid());
        memberBaseDto.setHeadquartersOrganSign(model.getOrganSign());
        memberBaseDto.setOrgansign(memberBaseVo.getOrgansign());
        MemberBaseDto baseDto = memberBaseApi.getMemberByGuid(memberBaseDto);
        if (baseDto != null) {
            return new ResponseEntity(ResultVO.createSuccess(baseDto), HttpStatus.OK);
        } else {
            return new ResponseEntity(new ResultVO(-1, "查询失败", false), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "总部端:获取发卡人信息", notes = "获取发卡人信息", response = MemberBase.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberBase.class)})
    @RequestMapping(value = "/getSystemDictList",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getSystemDictList(@RequestHeader("commonRequestModel") String commonRequestModel,@ApiParam(value = "会员基本信息", required = true) @RequestBody MemberBaseVo memberBaseVo) {
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        List<QueryEmployeeVO> collect = memberBaseForZBApi.selectEmployeeForZB(memberBaseVo.getOrgansign(),model.getEmployeeId());
       return new ResponseEntity(ResultVO.createSuccess(collect), HttpStatus.OK);
    }

    @ApiOperation(value = "总部端:查看手机号、卡号是否重复", notes = "总部端:查看手机号、卡号是否重复", response = MemberBase.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberBase.class)})
    @RequestMapping(value = "/validTelephoneExist", method = RequestMethod.POST, produces = "application/json",
            consumes = "application/json")
    public ResponseEntity<ResultVO> validTelephoneExist(@RequestHeader("commonRequestModel") String commonRequestModel, @ApiParam(value = "会员基本信息", required = true) @RequestBody MemberBaseVo memberBaseVo) {
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
//          经营模式 1、单体 2、连锁 3、联营
        MemberBaseDto dto = new MemberBaseDto();
        Byte bizModel = model.getBizModel();
        if (bizModel == 1) {
            dto.setOrgansign(model.getOrganSign());
        }
        if (bizModel == 3) {
            dto.setOrgansign(memberBaseVo.getOrgansign());
        }
        if (bizModel == 2) {
            dto.setHeadquartersOrganSign(model.getOrganSign());
        }
        if(StringUtils.isEmpty(memberBaseVo.getTelephone())){
            return new ResponseEntity(new ResultVO(-1, "手机号不能为空", false), HttpStatus.OK);
        }
        if(StringUtils.isEmpty(memberBaseVo.getCartNo())){
            return new ResponseEntity(new ResultVO(-1, "会员卡号不能为空", false), HttpStatus.OK);
        }
        dto.setTelephone(memberBaseVo.getTelephone());
        dto.setGuid(memberBaseVo.getGuid());
        long existTelephone = memberBaseForZBApi.getMemberBaseByConditionZB(dto);
        if (existTelephone > 0 && memberBaseVo.getTelephone() != null) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.EXISTS_TELEPHONE.getCode(),ResultCodeEnum.EXISTS_TELEPHONE.getMsg(), null), HttpStatus.OK);
        }
        dto.setTelephone(null);
        dto.setCartNo(memberBaseVo.getCartNo());
        long existCartNo = memberBaseForZBApi.getMemberBaseByConditionZB(dto);
        if (existCartNo > 0 && memberBaseVo.getCartNo() != null) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.EXISTS_CARTNO.getCode(),ResultCodeEnum.EXISTS_CARTNO.getMsg(), null), HttpStatus.OK);
        }
        return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.SUCCESS.getCode(),ResultCodeEnum.SUCCESS.getMsg(), null), HttpStatus.OK);
    }

    @ApiOperation(value = "总部端:重置密码", notes = "总部端:重置密码", response = MemberBase.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberBase.class)})
    @RequestMapping(value = "/resetPassword",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> resetPassword(@RequestHeader("commonRequestModel") String commonRequestModel, @ApiParam(value = "会员基本信息", required = true) @RequestBody MemberBaseVo memberBaseVo) {
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        MemberBaseDto memberBaseDto = new MemberBaseDto();
        memberBaseDto.setUpdateUser(model.getEmployeeId());
        memberBaseDto.setGuid(memberBaseVo.getGuid());
        memberBaseDto.setHeadquartersOrganSign(model.getOrganSign());
        memberBaseDto.setOrgansign(memberBaseVo.getOrgansign());
        memberBaseDto.setPasswd(MD5Util.getMD5("123456"));
        memberBaseForZBApi.updateMemberBaseZB(memberBaseDto);
        return new ResponseEntity<>(ResultVO.createSuccess("重置成功"), HttpStatus.OK);

    }

    @ApiOperation(value = "总部端:增加或减少会员积分", notes = "增加或减少会员积分", response = ResultVO.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class)})
    @RequestMapping(value = "/editMemberPoint", method = RequestMethod.POST)
    ResponseEntity<ResultVO> editMemberPoint(@RequestHeader("commonRequestModel") String commonRequestModel, @ApiParam(value = "积分修改", required = true)   @RequestBody MemberChangePoint memberChangePoint) {
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        MemberChangePointDto memberChangePointDto = new MemberChangePointDto();
        BeanUtils.copyProperties(memberChangePoint, memberChangePointDto);
        memberChangePointDto.setOrganSign(model.getOrganSign());
        memberChangePointDto.setOperator(model.getEmployeeId());
        if (memberChangePointDto == null || memberChangePointDto.getEditType() == null) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.NO_EDITTYPE.getCode(),ResultCodeEnum.NO_EDITTYPE.getMsg(), null), HttpStatus.OK);
        }
        if (memberChangePointDto == null || memberChangePointDto.getGuid() == null) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.NO_MEMBERID.getCode(),ResultCodeEnum.NO_MEMBERID.getMsg(), null), HttpStatus.OK);
        }
        if (memberChangePointDto.getEditType() != 2) {
            if (memberChangePointDto == null || memberChangePointDto.getQuantity() == null) {
                return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.NO_OPERATOR_POINT.getCode(), ResultCodeEnum.NO_OPERATOR_POINT.getMsg(), null), HttpStatus.OK);
            }
        }
        String msg = memberBaseForZBApi.editMemberPointZB(memberChangePointDto);
        if (msg.equals("success")) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.SUCCESS.getCode(),ResultCodeEnum.SUCCESS.getMsg(), null), HttpStatus.OK);
        } else {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR.getCode(),ResultCodeEnum.ERROR.getMsg(), null), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "总部端:批量清零积分", notes = "总部端:批量清零积分", response = ResultVO.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class)})
    @RequestMapping(value = "/batchClearPoint", method = RequestMethod.POST)
    ResponseEntity<ResultVO> batchClearPoint(@RequestHeader("commonRequestModel") String commonRequestModel, @ApiParam(value = "批量积分清零条件", required = true)   @RequestBody MemberClearPointCondition memberClearPointCondition) {
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        if (memberClearPointCondition == null || memberClearPointCondition.getType() == null) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.NO_CLEARPOINT_TYPE.getCode(),ResultCodeEnum.NO_CLEARPOINT_TYPE.getMsg(), null), HttpStatus.OK);
        }
        memberClearPointCondition.setOrganSign(model.getOrganSign());
        memberClearPointCondition.setEmployeeId(Integer.valueOf(model.getEmployeeId()));
        boolean flag = memberBaseForZBApi.batchClearPointForZB(memberClearPointCondition);
        if (flag) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.SUCCESS.getCode(),ResultCodeEnum.SUCCESS.getMsg(), null), HttpStatus.OK);
        } else {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR.getCode(),ResultCodeEnum.ERROR.getMsg(), null), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @ApiOperation(value = "总部端:会员资料查询", notes = "门店端:会员资料查询", response = ResultVO.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class)})
    @RequestMapping(value = "/query", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    @ResponseBody
    public ResultVO query(@RequestHeader("commonRequestModel") String commonRequestModel, @ApiParam(value = "会员基本信息", required = true) @RequestBody MemberDetailExportQueryVO memberDetailExportQueryVO) {
        CommonRequestModel model = JSONObject.parseObject(commonRequestModel, CommonRequestModel.class);
        MemberBaseDto baseDto = new MemberBaseDto();
        //总部机构号
        baseDto.setHeadquartersOrganSign(model.getOrganSign());
        baseDto.setPageNum(memberDetailExportQueryVO.getPage() == null ? 1 : memberDetailExportQueryVO.getPage());
        baseDto.setPageSize(memberDetailExportQueryVO.getRows() == null ? 50 : memberDetailExportQueryVO.getRows());
        baseDto.setSortWay(1);
        baseDto.setYn(1);
        baseDto.setVipLevelId(memberDetailExportQueryVO.getVipLevelId());
        baseDto.setFlag(memberDetailExportQueryVO.getFlag());
        baseDto.setIsDrugstoreHidden(memberDetailExportQueryVO.getIsDrugstoreHidden());
        //来源门店
        if (!memberDetailExportQueryVO.getFlag() && !org.springframework.util.StringUtils.isEmpty(memberDetailExportQueryVO.getOrganSign())) {
            baseDto.setOrgansign(memberDetailExportQueryVO.getOrganSign());
        }
        //卡内余额
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportQueryVO.getMinTotalAmount())) {
            baseDto.setMinTotalAmount(memberDetailExportQueryVO.getMinTotalAmount());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportQueryVO.getMaxTotalAmount())) {
            baseDto.setMaxTotalAmount(memberDetailExportQueryVO.getMaxTotalAmount());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportQueryVO.getMixedQuery())) {
            baseDto.setMixedQuery(memberDetailExportQueryVO.getMixedQuery());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportQueryVO.getCartNo())) {
            baseDto.setCartNo(memberDetailExportQueryVO.getCartNo());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportQueryVO.getTelephone())) {
            baseDto.setTelephone(memberDetailExportQueryVO.getTelephone());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportQueryVO.getStartPoint())) {
            baseDto.setStartPoint(memberDetailExportQueryVO.getStartPoint());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportQueryVO.getEndPoint())) {
            baseDto.setEndPoint(memberDetailExportQueryVO.getEndPoint());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportQueryVO.getSidx())) {
            baseDto.setSidx(memberDetailExportQueryVO.getSidx());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportQueryVO.getSord())) {
            baseDto.setSord(memberDetailExportQueryVO.getSord());
        }
        if (memberDetailExportQueryVO.getWhiteList() != null) {
            baseDto.setWhiteList(memberDetailExportQueryVO.getWhiteList());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportQueryVO.getStartCreateDate())) {
            baseDto.setStartCreateDate(memberDetailExportQueryVO.getStartCreateDate());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportQueryVO.getEndCreateDate())) {
            baseDto.setEndCreateDate(memberDetailExportQueryVO.getEndCreateDate());
        }
        if (memberDetailExportQueryVO.getState() != null) {
            baseDto.setState(memberDetailExportQueryVO.getState());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportQueryVO.getCreateUserName())) {
            baseDto.setCreateUserName(memberDetailExportQueryVO.getCreateUserName());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportQueryVO.getChronicPref())) {
            baseDto.setPref(memberDetailExportQueryVO.getChronicPref());
        }
        baseDto.setStartAge(memberDetailExportQueryVO.getStartAge());
        baseDto.setEndAge(memberDetailExportQueryVO.getEndAge());
        baseDto.setStartBirthdayRange(memberDetailExportQueryVO.getStartBirthdayRange());
        baseDto.setEndBirthdayRange(memberDetailExportQueryVO.getEndBirthdayRange());
        return ResultVO.createSuccess(memberBaseForZBApi.getMemberBaseListPagerForZB(baseDto));
    }

    @RequestMapping(value = "/querySysTemTime", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    @ResponseBody
    public ResultVO querySysTemTime() {
        String systemTime = DateUtil.parseDateToStr(new Date(),DateUtil.DATE_FORMAT_YYYY_MM_DD);
        return ResultVO.createSuccess(systemTime);
    }

    @ApiOperation(value = "总部端:会员资料导出", notes = "门店端:会员资料导出", response = ResultVO.class, tags = {"member",})
    @RequestMapping(value = "/exportExcel", method = RequestMethod.POST)
    public void exportExcelMember(HttpServletRequest request, HttpServletResponse response, @RequestHeader("commonRequestModel") String commonRequestModel,
                                  @ApiParam(value = "会员基本信息", required = true) @RequestBody MemberDetailExportExcelVO memberDetailExportExcelVO) {
        CommonRequestModel model = JSONObject.parseObject(commonRequestModel, CommonRequestModel.class);
        MemberBaseDto baseDto = new MemberBaseDto();
        //总部机构号
        baseDto.setHeadquartersOrganSign(model.getOrganSign());
        baseDto.setSortWay(1);
        baseDto.setYn(1);
        baseDto.setVipLevelId(memberDetailExportExcelVO.getVipLevelId());
        baseDto.setFlag(memberDetailExportExcelVO.getFlag());
        //来源门店
        if (!memberDetailExportExcelVO.getFlag() && !org.springframework.util.StringUtils.isEmpty(memberDetailExportExcelVO.getOrganSign())) {
            baseDto.setOrgansign(memberDetailExportExcelVO.getOrganSign());
        }
        //卡内余额
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportExcelVO.getMinTotalAmount())) {
            baseDto.setMinTotalAmount(memberDetailExportExcelVO.getMinTotalAmount());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportExcelVO.getMaxTotalAmount())) {
            baseDto.setMaxTotalAmount(memberDetailExportExcelVO.getMaxTotalAmount());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportExcelVO.getMixedQuery())) {
            baseDto.setMixedQuery(memberDetailExportExcelVO.getMixedQuery());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportExcelVO.getCartNo())) {
            baseDto.setCartNo(memberDetailExportExcelVO.getCartNo());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportExcelVO.getTelephone())) {
            baseDto.setTelephone(memberDetailExportExcelVO.getTelephone());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportExcelVO.getStartPoint())) {
            baseDto.setStartPoint(memberDetailExportExcelVO.getStartPoint());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportExcelVO.getEndPoint())) {
            baseDto.setEndPoint(memberDetailExportExcelVO.getEndPoint());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportExcelVO.getSidx())) {
            baseDto.setSidx(memberDetailExportExcelVO.getSidx());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportExcelVO.getSord())) {
            baseDto.setSord(memberDetailExportExcelVO.getSord());
        }
        if (memberDetailExportExcelVO.getWhiteList() != null) {
            baseDto.setWhiteList(memberDetailExportExcelVO.getWhiteList());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportExcelVO.getStartCreateDate())) {
            baseDto.setStartCreateDate(memberDetailExportExcelVO.getStartCreateDate());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportExcelVO.getEndCreateDate())) {
            baseDto.setEndCreateDate(memberDetailExportExcelVO.getEndCreateDate());
        }
        if (memberDetailExportExcelVO.getState() != null) {
            baseDto.setState(memberDetailExportExcelVO.getState());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportExcelVO.getCreateUserName())) {
            baseDto.setCreateUserName(memberDetailExportExcelVO.getCreateUserName());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportExcelVO.getChronicPref())) {
            baseDto.setPref(memberDetailExportExcelVO.getChronicPref());
        }
        List<MemberBaseDto> memberdatas = memberBaseForZBApi.getMemberBaseListForZBExecl(baseDto);
        memberdatas.forEach(memberList -> {
            if (memberList.getSex() == 1) {
                memberList.setSexStr("男");
            } else {
                memberList.setSexStr("女");
            }
            if (memberList.getState() == 1) {
                memberList.setStateName("启用");
            } else {
                memberList.setStateName("禁用");
            }
        });
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String extfilename = "会员资料" + df.format(new Date()) + ".xls";
        String sheetName = "会员资料";
        String[] headers = memberDetailExportExcelVO.getHeaders().split(",");
        String[] fieldNames = memberDetailExportExcelVO.getFieldNames().split(",");
        for (int i = 0; i < fieldNames.length; i++) {
            if (fieldNames[i].equals("sex")) {
                fieldNames[i] = "sexStr";
            }
            if (fieldNames[i].equals("state")) {
                fieldNames[i] = "stateName";
            }
            if (fieldNames[i].equals("discount")) {
                fieldNames[i] = "discountName";
            }
            if (fieldNames[i].equals("areaName") || fieldNames[i].equals("cityName") || fieldNames[i].equals("provinceName")) {
                fieldNames[i] = "provinceCityAreaName";
            }
        }
        try {
            ExportExcelUtil.createExcelWithDatePattern(response, request, extfilename, sheetName, headers, fieldNames, memberdatas, true, "yyyy-MM-dd");
            logger.info("总部端:会员资料数据导出成功。");
        } catch (Exception e) {
            logger.error("总部端:会员资料数据导出异常:{}", e);
        }
    }


    /**
     * 会员积分列表导出
     *
     * @param request
     * @param response
     * @param exportVo
     */
    @ApiOperation(value = "门店端:会员积分列表导出", notes = "门店端:会员积分列表导出", response = ResultVO.class, tags = {"member",})
    @RequestMapping(value = "/exportMemberPointExcel", method = RequestMethod.POST)
    public void exportMemberPoint(HttpServletRequest request, HttpServletResponse response, @RequestHeader("commonRequestModel") String commonRequestModel,
                                  @ApiParam(value = "会员基本信息", required = true) @RequestBody MemberDetailExportExcelVO exportVo) {
        CommonRequestModel model = JSONObject.parseObject(commonRequestModel, CommonRequestModel.class);
        MemberBaseDto baseDto = new MemberBaseDto();
        BeanUtils.copyProperties(exportVo, baseDto);
        baseDto.setHeadquartersOrganSign(model.getOrganSign());
        baseDto.setSortWay(1);
        baseDto.setYn(1);
        baseDto.setFlag(exportVo.getFlag());
        //来源门店
        if (!exportVo.getFlag() && !org.springframework.util.StringUtils.isEmpty(exportVo.getOrganSign())) {
            baseDto.setOrgansign(exportVo.getOrganSign());
        }
        List<MemberBaseDto> memberList = memberBaseForZBApi.getMemberBaseListForZBExecl(baseDto);

        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String extfileName = "会员积分" + df.format(new Date()) + ".xls";
        String sheetName = "会员积分";
        String[] headers = new String[]{"会员手机号", "会员姓名", "会员等级", "会员卡号", "来源门店", "门店编码", "可用积分", "累计积分"};
        String[] fieldNames = new String[]{"telephone", "name", "levelName", "cartNo", "sourceDrugstoreName", "organsign", "point", "allPoint"};
        try {
            ExportExcelUtil.createExcelWithDatePattern(response, request, extfileName, sheetName, headers, fieldNames, memberList, true, "yyyy-MM-dd");
        } catch (Exception e) {
            logger.error("总部端:会员资料数据导出异常:{}", e);
        }
    }
}