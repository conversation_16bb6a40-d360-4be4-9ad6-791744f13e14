package com.xyy.saas.web.api.module.member.service;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.member.core.dto.MemberExchangeProductDto;

import java.util.List;

/**
 * 积分可兑换商品服务
 * 从会员模块上移到网关层，为了使会员不依赖库存
 * 此方案可能也只是临时方案
 * <AUTHOR>
 * @since 2020-05-30
 */
public interface MemberExchangeProductService {

    /**
     * 获取积分兑换商品分页列表
     * @param exchangeProductDto
     * @return
     */
    PageInfo getExchangeProductListPager(MemberExchangeProductDto exchangeProductDto);

    /**
     * 新增积分兑换 商品查询列表
     * @param exchangeProductDto
     * @return
     */
    PageInfo getShelfProductPager(MemberExchangeProductDto exchangeProductDto);

    /**
     * 为宜块钱提供不分页获取积分商品列表
     * @param exchangeProductDto
     * @return
     */
    List<MemberExchangeProductDto> getYKQExchangeProductList(MemberExchangeProductDto exchangeProductDto);
}
