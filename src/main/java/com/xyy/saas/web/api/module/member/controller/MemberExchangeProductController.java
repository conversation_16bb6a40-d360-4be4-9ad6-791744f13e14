package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.member.core.api.MemberExchangeProductApi;
import com.xyy.saas.member.core.dto.MemberExchangeProductDto;
import com.xyy.saas.web.api.module.member.model.MemberExchangeProductVo;
import com.xyy.saas.web.api.module.member.service.MemberExchangeProductService;
import com.xyy.saas.web.api.module.member.utils.ResultCodeEnum;
import com.xyy.saas.web.api.module.utils.ExportExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Controller
@RequestMapping("/member/memberExchangeProduct")
@Api(value = "memberExchangeProduct", description = "门店端积分兑换商品接口")
public class MemberExchangeProductController {
    private static final Logger logger = LogManager.getLogger(MemberExchangeProductController.class);

    @Reference(version = "0.0.1")
    private MemberExchangeProductApi memberExchangeProductApi;

    @Autowired
    private MemberExchangeProductService memberExchangeProductService;

    @ApiOperation(value = "保存积分兑换商品", notes = "保存积分兑换商品")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/saveExchangeProduct", method = RequestMethod.POST)
    @ResponseBody
    public ResultVO saveExchangeProduct(@RequestHeader("commonRequestModel") String commonRequestModel, @RequestBody MemberExchangeProductVo vo){
        if (vo == null || vo.getProductPref() == null || vo.getId() == null) {
            return new ResultVO(ResultCodeEnum.NO_EXCHANGE_PRODUCT.getCode(), ResultCodeEnum.NO_EXCHANGE_PRODUCT.getMsg(), null);
        }
        MemberExchangeProductDto exchangeProductDto = new MemberExchangeProductDto();
        BeanUtils.copyProperties(vo, exchangeProductDto);
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        exchangeProductDto.setOrganSign(model.getOrganSign());
        memberExchangeProductApi.updateExchangeProduct(exchangeProductDto);
        return new ResultVO(ResultCodeEnum.SUCCESS);
    }

    @ApiOperation(value = "批量保存积分兑换商品", notes = "批量保存积分兑换商品")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/batchSaveExchangeProduct", method = RequestMethod.POST)
    @ResponseBody
    public ResultVO batchSaveExchangeProduct(@RequestHeader("commonRequestModel") String commonRequestModel, @RequestBody MemberExchangeProductVo vo){
        if (vo == null || vo.getExchangeProductList() == null) {
            return new ResultVO(ResultCodeEnum.NO_EXCHANGE_PRODUCT.getCode(), ResultCodeEnum.NO_EXCHANGE_PRODUCT.getMsg(), null);
        }
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        List<MemberExchangeProductDto> exchangeProductDtoList = new ArrayList<>();
        for (MemberExchangeProductVo exchangeProductVo : vo.getExchangeProductList()) {
            MemberExchangeProductDto exchangeProductDto = new MemberExchangeProductDto();
            BeanUtils.copyProperties(exchangeProductVo, exchangeProductDto);
            exchangeProductDto.setOrganSign(model.getOrganSign());
            exchangeProductDtoList.add(exchangeProductDto);
        }
        memberExchangeProductApi.saveBatchExchangeProduct(exchangeProductDtoList);
        return new ResultVO(ResultCodeEnum.SUCCESS);
    }

    @ApiOperation(value = "删除积分兑换商品记录", notes = "删除积分兑换商品记录")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/deleteExchangeProduct", method = RequestMethod.POST)
    @ResponseBody
    public ResultVO deleteExchangeProduct(@RequestHeader("commonRequestModel") String commonRequestModel, @RequestBody MemberExchangeProductVo vo){
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        MemberExchangeProductDto productDto = new MemberExchangeProductDto();
        productDto.setId(vo.getId());
        productDto.setYn(0);
        productDto.setOrganSign(model.getOrganSign());
        memberExchangeProductApi.deleteExchangeProductById(productDto);
        return new ResultVO(ResultCodeEnum.SUCCESS);
    }

    @ApiOperation(value = "导出积分可兑换商品列表", notes = "导出积分可兑换商品列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberExchangeProductDto.class) })
    @RequestMapping(value = "/exportExchangeProduct", method = RequestMethod.POST)
    @ResponseBody
    public void exportExchangeProduct(@RequestHeader("commonRequestModel") String commonRequestModel, HttpServletRequest request,
                                      HttpServletResponse response, @RequestBody MemberExchangeProductVo vo){
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        MemberExchangeProductDto exchangeProductDto = new MemberExchangeProductDto();
        BeanUtils.copyProperties(vo, exchangeProductDto);
        exchangeProductDto.setOrganSign(model.getOrganSign());
        exchangeProductDto.setIshidden(model.getIdentity());
        exchangeProductDto.setPageNum(1);
        exchangeProductDto.setPageSize(100000);
        PageInfo pageInfo = memberExchangeProductService.getExchangeProductListPager(exchangeProductDto);
        List<MemberExchangeProductDto> list = pageInfo.getList();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
        String extFilename="积分可兑换商品"+df.format(new Date())+".xls";
        String sheetName = "积分可兑换商品";
        String headers[] = new String[]{"商品编号", "通用名称","规格","单位", "生产厂家","零售价","库存数量", "所需积分"};
        String fieldNames[] = new String[]{"pharmacyPref", "commonName", "attributeSpecification", "unitName", "manufacturer", "retailPrice", "inventoryNum", "integral"};
        try {
            ExportExcelUtil.createExcel(response,request,extFilename,sheetName, headers, fieldNames, list,true);
        } catch (Exception e) {
            logger.error("MemberExchangeProductController exportRecordExcel is error.", e);
        }
    }

    @ApiOperation(value = "积分可兑换商品列表", notes = "积分可兑换商品列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberExchangeProductDto.class) })
    @RequestMapping(value = "/getExchangeProductPager", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> getExchangeProductPager(@RequestHeader("commonRequestModel") String commonRequestModel, @RequestBody MemberExchangeProductVo vo){
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        MemberExchangeProductDto exchangeProductDto = new MemberExchangeProductDto();
        BeanUtils.copyProperties(vo, exchangeProductDto);
        exchangeProductDto.setOrganSign(model.getOrganSign());
        exchangeProductDto.setIshidden(model.getIdentity());
        PageInfo pageInfo = memberExchangeProductService.getExchangeProductListPager(exchangeProductDto);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(pageInfo), HttpStatus.OK);
    }

    @ApiOperation(value = "新增积分兑换 商品查询列表", notes = "新增积分兑换 商品查询列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberExchangeProductDto.class) })
    @RequestMapping(value = "/getShelfProductPager", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> getShelfProductPager(@RequestHeader("commonRequestModel") String commonRequestModel, @RequestBody MemberExchangeProductVo vo){
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        MemberExchangeProductDto exchangeProductDto = new MemberExchangeProductDto();
        BeanUtils.copyProperties(vo, exchangeProductDto);
        exchangeProductDto.setOrganSign(model.getOrganSign());
        exchangeProductDto.setIshidden(model.getIdentity());
        exchangeProductDto.setProductName(vo.getProductName());
        logger.info("新增积分兑换 商品查询列表 param:{}", JSONObject.toJSONString(exchangeProductDto));
        PageInfo pageInfo = memberExchangeProductService.getShelfProductPager(exchangeProductDto);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(pageInfo), HttpStatus.OK);
    }
}
