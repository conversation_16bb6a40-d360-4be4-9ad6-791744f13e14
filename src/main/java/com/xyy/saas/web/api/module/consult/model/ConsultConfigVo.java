package com.xyy.saas.web.api.module.consult.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 远程问诊配置Vo
 * <AUTHOR>
 */
@ApiModel(description = "远程问诊配置")
public class ConsultConfigVo implements Serializable {

    private static final long serialVersionUID = -4457738116750100945L;

    /**
     * 是否开启远程问诊，0-否 1-是
     */
    @ApiModelProperty(value = "是否开启远程问诊，0-否 1-是")
    private Byte remoteInquiry;

    public Byte getRemoteInquiry() {
        return remoteInquiry;
    }

    public void setRemoteInquiry(Byte remoteInquiry) {
        this.remoteInquiry = remoteInquiry;
    }
}
