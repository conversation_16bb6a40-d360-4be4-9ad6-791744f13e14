package com.xyy.saas.web.api.module.product.model;

import io.swagger.annotations.ApiModel;

import java.util.List;

/**
 * <AUTHOR>
 * @title: UploadMedicalVO
 * @date 2019-09-11  19:06
 * @description: TODO
 */
@ApiModel(description = "ImportMedicalVO 上传医保商品类")
public class ImportMedicalVO {

    String organSign;

    String employeeId;

    List<List<String>> list;

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public String getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }

    public List<List<String>> getList() {
        return list;
    }

    public void setList(List<List<String>> list) {
        this.list = list;
    }
}
