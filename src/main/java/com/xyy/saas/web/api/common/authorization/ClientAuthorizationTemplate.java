package com.xyy.saas.web.api.common.authorization;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.web.api.common.context.ActionContext;

/**
 * 授权-定义权限校验模板
 * <AUTHOR>
 * @date 2019-08-08
 * @mondify
 * @copyright
 */
public abstract class ClientAuthorizationTemplate implements IClientAuthorization {

    public ResultVO clientAuth(ActionContext context){
       return doClientAuth(context);
    }
    //让子类去实现
    public abstract ResultVO doClientAuth(ActionContext context);
}
