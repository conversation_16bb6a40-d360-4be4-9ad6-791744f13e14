package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * @ClassName CostPriceDetailVo
 * @Description 成本价调价方案提交信息详情对象
 * <AUTHOR>
 * @Date 2020/8/19 15:49
 * @Version 1.0
 **/
@ApiModel(description = "成本价调价方案提交信息详情对象")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")
public class CostPriceDetailVo {


    @JsonProperty("productPref")
    private String productPref;//商品编号，对应商品表的pref内码

    @JsonProperty("adjustmentRatio")
    private String adjustmentRatio;//调价比例

    @ApiModelProperty(value = "出货价")
    public String getNewCostPrice() {
        return newCostPrice;
    }

    public void setNewCostPrice(String newCostPrice) {
        this.newCostPrice = newCostPrice;
    }

    @ApiModelProperty(value = "商品取值范围：1：按比例，2按出库价取值")
    public Byte getValueType() {
        return valueType;
    }

    public void setValueType(Byte valueType) {
        this.valueType = valueType;
    }

    @JsonProperty("newCostPrice")
    private String newCostPrice;//出货价

    @JsonProperty("valueType")
    private Byte valueType;//商品取值范围：1：按比例，2按出库价取值

    @ApiModelProperty(value = "价格上限")
    public String getPriceUpper() {
        return priceUpper;
    }

    public void setPriceUpper(String priceUpper) {
        this.priceUpper = priceUpper;
    }

    private String priceUpper;//价格上限

    @ApiModelProperty(value = "价格下限")
    public String getPriceLower() {
        return priceLower;
    }

    public void setPriceLower(String priceLower) {
        this.priceLower = priceLower;
    }

    private String priceLower;//价格下限

    @ApiModelProperty(value = "商品编号，对应商品表的pref内码")
    public String getProductPref() {
        return productPref;
    }

    public void setProductPref(String productPref) {
        this.productPref = productPref;
    }

    @ApiModelProperty(value = "调价比例")
    public String getAdjustmentRatio() {
        return adjustmentRatio;
    }

    public void setAdjustmentRatio(String adjustmentRatio) {
        this.adjustmentRatio = adjustmentRatio;
    }

    @ApiModelProperty(value = "是否可要货")
    private Byte isEnquiryId;

    @ApiModelProperty(value = "最后一次采购的供应商")
    private String lastPurchaseSupplier;

    public Byte getIsEnquiryId() {
        return isEnquiryId;
    }

    public void setIsEnquiryId(Byte isEnquiryId) {
        this.isEnquiryId = isEnquiryId;
    }

    public String getLastPurchaseSupplier() {
        return lastPurchaseSupplier;
    }

    public void setLastPurchaseSupplier(String lastPurchaseSupplier) {
        this.lastPurchaseSupplier = lastPurchaseSupplier;
    }
}
