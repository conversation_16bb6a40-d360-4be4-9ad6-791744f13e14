package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;

/**
 * @ClassName CostPriceQueryProductVo
 * @Description 成本价调价增加选择商品查询类
 * <AUTHOR>
 * @Date 2020/8/19 16:29
 * @Version 1.0
 **/
@ApiModel(description = "成本价调价增加选择商品查询类")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")
public class CostPriceQueryProductVo {

    @JsonProperty("mixQuery")
    private String mixQuery;//混合查询字段：商品编号，通用名，商品名称等

    @JsonProperty("systemType")
    private Integer systemType;//商品类型,-1:全部，其他是下拉框值

    @JsonProperty("productType")
    private Integer productType;//商品自定义分类，-1：全部，其他是下拉框值

    @JsonProperty("standardLibraryId")
    private Long standardLibraryId;//标准库id

    @ApiModelProperty(value = "是否控销，空，全部，0否，1是")
    public Integer getControlSalesYn() {
        return controlSalesYn;
    }

    public void setControlSalesYn(Integer controlSalesYn) {
        this.controlSalesYn = controlSalesYn;
    }

    @JsonProperty("controlSalesYn")
    private Integer controlSalesYn;//是否控销，空，全部，0否，1是

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    @JsonProperty("manufacturer")
    private String manufacturer;//生产厂商

    @ApiModelProperty(value = "标准库id")
    public Long getStandardLibraryId() {
        return standardLibraryId;
    }

    public void setStandardLibraryId(Long standardLibraryId) {
        this.standardLibraryId = standardLibraryId;
    }

    @ApiModelProperty(value = "混合查询字段：商品编号，通用名，商品名称等")
    public String getMixQuery() {
        return mixQuery;
    }

    @ApiModelProperty(value = "页码")
    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    @ApiModelProperty(value = "每页显示多少")
    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    @JsonProperty("page")
    public Integer page;
    @JsonProperty("rows")
    public Integer rows;

    public void setMixQuery(String mixQuery) {
        this.mixQuery = mixQuery;
    }

    @ApiModelProperty(value = "商品类型,-1:全部，其他是下拉框值")
    public Integer getSystemType() {
        return systemType;
    }

    public void setSystemType(Integer systemType) {
        this.systemType = systemType;
    }

    @ApiModelProperty(value = "商品自定义分类，-1：全部，其他是下拉框值")
    public Integer getProductType() {
        return productType;
    }

    public void setProductType(Integer productType) {
        this.productType = productType;
    }

    @ApiModelProperty(value = "商品六级分类")
    private String sixLevels;

    public String getSixLevels() {
        return sixLevels;
    }

    public void setSixLevels(String sixLevels) {
        this.sixLevels = sixLevels;
    }
}
