package com.xyy.saas.web.api.module.product.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "添加商品标准库列表返回", description = "添加商品标准库列表返回")
public class AddDrugListResult {

    @ApiModelProperty(value = "标准库id",name = "standardLibraryId")
    private Long standardLibraryId;
    @ApiModelProperty(value = "条形码",name = "barCode")
    private String barCode;
    @ApiModelProperty(value = "批准文号",name = "approvalNumber")
    private String approvalNumber;
    @ApiModelProperty(value = "规格/型号",name = "attributeSpecification")
    private String attributeSpecification;
    @ApiModelProperty(value = "通用名",name = "commonName")
    private String commonName;
    @ApiModelProperty(value = "生产厂家",name = "manufacturer")
    private String manufacturer;
    @ApiModelProperty(value = "商品名称",name = "productName")
    private String productName;
    @ApiModelProperty(value = "产地",name = "producingArea")
    private String producingArea;
    @ApiModelProperty(value = "status",name = "status")
    private Byte status;
    @ApiModelProperty(value = "单位",name = "unitName")
    private String unitName;
    @ApiModelProperty(value = "剂型ID",name = "dosageFormId")
    private String dosageFormId;
    @ApiModelProperty(value = "处方分类",name = "prescriptionClassification")
    private String prescriptionClassification;
    @ApiModelProperty(value = "是否含麻",name = "containingHempYn")
    private String containingHempYn;
    @ApiModelProperty(value = "commonMixCode",name = "commonName")
    private String commonMixCode;
    @ApiModelProperty(value = "manufacturerMixCode",name = "commonName")
    private String manufacturerMixCode;
    @ApiModelProperty(value = "mixCondition",name = "commonName")
    private String mixCondition;
    @ApiModelProperty(value = "商品编码",name = "pref")
    private String pref;
    @ApiModelProperty(value = "库存数量",name = "pref")
    private String stockNum;
    @ApiModelProperty(value = "零售价",name = "pref")
    private String retailPrice;
    @ApiModelProperty(value = "会员价",name = "pref")
    private String vipPrice;

    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    public String getStockNum() {
        return stockNum;
    }

    public void setStockNum(String stockNum) {
        this.stockNum = stockNum;
    }

    public String getRetailPrice() {
        return retailPrice;
    }

    public void setRetailPrice(String retailPrice) {
        this.retailPrice = retailPrice;
    }

    public String getVipPrice() {
        return vipPrice;
    }

    public void setVipPrice(String vipPrice) {
        this.vipPrice = vipPrice;
    }

    public Long getStandardLibraryId() {
        return standardLibraryId;
    }

    public void setStandardLibraryId(Long standardLibraryId) {
        this.standardLibraryId = standardLibraryId;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    public String getAttributeSpecification() {
        return attributeSpecification;
    }

    public void setAttributeSpecification(String attributeSpecification) {
        this.attributeSpecification = attributeSpecification;
    }

    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProducingArea() {
        return producingArea;
    }

    public void setProducingArea(String producingArea) {
        this.producingArea = producingArea;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getDosageFormId() {
        return dosageFormId;
    }

    public void setDosageFormId(String dosageFormId) {
        this.dosageFormId = dosageFormId;
    }

    public String getPrescriptionClassification() {
        return prescriptionClassification;
    }

    public void setPrescriptionClassification(String prescriptionClassification) {
        this.prescriptionClassification = prescriptionClassification;
    }

    public String getContainingHempYn() {
        return containingHempYn;
    }

    public void setContainingHempYn(String containingHempYn) {
        this.containingHempYn = containingHempYn;
    }

    public String getCommonMixCode() {
        return commonMixCode;
    }

    public void setCommonMixCode(String commonMixCode) {
        this.commonMixCode = commonMixCode;
    }

    public String getManufacturerMixCode() {
        return manufacturerMixCode;
    }

    public void setManufacturerMixCode(String manufacturerMixCode) {
        this.manufacturerMixCode = manufacturerMixCode;
    }

    public String getMixCondition() {
        return mixCondition;
    }

    public void setMixCondition(String mixCondition) {
        this.mixCondition = mixCondition;
    }
}
