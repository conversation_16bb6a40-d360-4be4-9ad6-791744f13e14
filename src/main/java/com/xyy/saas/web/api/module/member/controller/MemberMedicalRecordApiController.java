package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.member.core.api.MemberMedicalRecordRelationApi;
import com.xyy.saas.member.core.dto.MemberMedicalRecordInfoDto;
import com.xyy.saas.member.core.dto.MemberMedicalRecordQueryDto;
import com.xyy.saas.member.core.response.ResultVO;
import com.xyy.saas.web.api.module.member.model.MemberChronicPatientVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;


/**
 * @Description 会员病历资料
 * @menu 病历资料API
 */
@Controller
@RequestMapping(value = "/member/memberMedicalRecord")
@Api(value = "MemberMedicalRecordAPI", description = "病历资料API")
public class MemberMedicalRecordApiController {
    private static final Logger logger = LoggerFactory.getLogger(MemberMedicalRecordApiController.class);


    @Reference(version = "0.0.1")
    private MemberMedicalRecordRelationApi memberMedicalRecordRelationApi;

    /**
     * @param commonRequestModelStr 机构信息
     * @param query 查询条件
     * @return
     */
    @ApiOperation(value = "门店分页查询病历资料", notes = "门店分页查询病历资料", response = MemberMedicalRecordInfoDto.class, tags={ "病历资料", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberMedicalRecordInfoDto.class) })
    @RequestMapping(value = "/queryMemberMedicalRecordPage",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> queryMemberMedicalRecordPage(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                                 @RequestBody MemberMedicalRecordQueryDto query) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        logger.info("queryMemberMedicalRecordPage->organSign:{},param:{}", commonRequestModel.getOrganSign(),JSON.toJSONString(query));
        //分页参数校验
        if(query.getPageNum() == null || query.getPageSize() == null) {
            return new ResponseEntity(ResultVO.createError("分页参数不能为空"), HttpStatus.OK);
        }
        PageInfo<MemberMedicalRecordInfoDto> result = memberMedicalRecordRelationApi.selectListByMemberGuid(query);
        return new ResponseEntity(ResultVO.createSuccess(result),HttpStatus.OK);
    }


    /**
     * @param commonRequestModelStr 机构信息
     * @param query 查询条件
     * @return
     */
    @ApiOperation(value = "查询病历资料", notes = "查询病历资料", response = MemberMedicalRecordInfoDto.class, tags={ "病历资料", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberMedicalRecordInfoDto.class) })
    @RequestMapping(value = "/queryMemberMedicalRecordDetail",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> queryMemberMedicalRecordDetail(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                                 @RequestBody MemberMedicalRecordQueryDto query) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        logger.info("queryMemberMedicalRecordPage->organSign:{},param:{}", commonRequestModel.getOrganSign(),JSON.toJSONString(query));
        MemberMedicalRecordInfoDto result = memberMedicalRecordRelationApi.selectDetailById(query);
        return new ResponseEntity(ResultVO.createSuccess(result),HttpStatus.OK);
    }

    /**
     * @param commonRequestModelStr 机构信息
     * @param infoDto
     * @return
     */
    @ApiOperation(value = "保存病历", notes = "保存病历", response = MemberMedicalRecordInfoDto.class, tags={ "病历资料", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberMedicalRecordInfoDto.class) })
    @RequestMapping(value = "/save",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> save(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                                 @RequestBody MemberMedicalRecordInfoDto infoDto) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        logger.info("save->organSign:{},param:{}", commonRequestModel.getOrganSign(),JSON.toJSONString(infoDto));
        if(infoDto == null){
            return new ResponseEntity(new ResultVO(-1, "参数不能为空", false), HttpStatus.OK);
        }
        infoDto.setCreateOrganSign(commonRequestModel.getOrganSign());
        infoDto.setOperationUser(Integer.valueOf(commonRequestModel.getEmployeeId()));
        boolean result = memberMedicalRecordRelationApi.save(infoDto);
        return new ResponseEntity(new ResultVO(0, "操作成功", result), HttpStatus.OK);
    }

}
