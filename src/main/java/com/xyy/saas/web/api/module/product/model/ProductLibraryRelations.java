package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 商品标准库信息批量修改实体信息
 */
@ApiModel(description = "商品标准库信息批量修改实体信息")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")
public class ProductLibraryRelations {

    @JsonProperty("relationPos")
    private List<ProductLibraryRelationPo> relationPos = null;

    public ProductLibraryRelations details(List<ProductLibraryRelationPo> relationPos) {
        this.relationPos = relationPos;
        return this;
    }

    public ProductLibraryRelations addDetailsItem(ProductLibraryRelationPo detailsItem) {
        if (this.relationPos == null) {
            this.relationPos = new ArrayList<>();
        }
        this.relationPos.add(detailsItem);
        return this;
    }

    /**
     * 商品和标准库的对应关系集合
     * @return details
     **/
    @ApiModelProperty(value = "商品和标准库的对应关系集合")

    @Valid

    public List<ProductLibraryRelationPo> getRelationPos() {
        return relationPos;
    }

    public void setRelationPos(List<ProductLibraryRelationPo> relationPos) {
        this.relationPos = relationPos;
    }
}
