package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.api.ApproveHistoryApi;
import com.xyy.saas.product.core.api.FirstCheckProductApi;
import com.xyy.saas.product.core.dto.ApproveHistoryDetailDto;
import com.xyy.saas.product.core.dto.ApproveHistoryDto;
import com.xyy.saas.product.core.dto.FirstCheckProductDto;
import com.xyy.saas.product.core.dto.FirstCheckProductQueryDto;
import com.xyy.saas.web.api.module.product.model.ApproveHistoryVo;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@Controller
@RequestMapping(value = "/gsp/product")
public class GspProductController {

    @Reference(version = "0.0.1")
    private FirstCheckProductApi firstCheckProductApi;

    @Reference(version = "0.0.1")
    private ApproveHistoryApi approveHistoryApi;

    /**
     * 首营企业
     * @param request
     * @param firstCheckProductQueryDto
     * @return
     */
    @ApiOperation(value = "首营品种 查询", notes = "首营品种 查询 ", response = ApproveHistoryVo.class, tags={ "首营品种API", })
    @ApiResponses(value = {  @ApiResponse(code = 200, message = "操作成功", response = ApproveHistoryVo.class) })
    @RequestMapping(value = "/query", method = RequestMethod. POST)
    public ResponseEntity<ResultVO> query(HttpServletRequest request, @ApiParam(value = "首营品种信息实体", required = true) @Valid @RequestBody FirstCheckProductQueryDto firstCheckProductQueryDto){
        Byte isProductHidden = Byte.valueOf(request.getHeader("identity"));
        firstCheckProductQueryDto.setOrganSign(request.getHeader("organSign"));
        if(isProductHidden!=null){
            firstCheckProductQueryDto.setIsProductIsHidden(isProductHidden);
        }
        if(firstCheckProductQueryDto.getPage()==null){
            firstCheckProductQueryDto.setPage(1);
        }
        if(firstCheckProductQueryDto.getRows()==null){
            firstCheckProductQueryDto.setRows(10);
        }
        PageInfo<FirstCheckProductDto> pageInfo=new PageInfo<>();
        pageInfo.setPageSize(firstCheckProductQueryDto.getRows());
        pageInfo.setPageNum(firstCheckProductQueryDto.getPage());
        PageInfo<FirstCheckProductDto> pList = firstCheckProductApi.findPageInfo(pageInfo, firstCheckProductQueryDto);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(pList), HttpStatus.OK);
    }

    @ApiOperation(value = "首营品种 审批流程", notes = "首营品种 审批流程 ", response = ApproveHistoryVo.class, tags={ "首营品种API", })
    @ApiResponses(value = {  @ApiResponse(code = 200, message = "操作成功", response = ApproveHistoryVo.class) })
    @RequestMapping(value = "/getApproveFlowList", method = RequestMethod. POST)
    public ResponseEntity<ResultVO> getApproveFlowList(HttpServletRequest request, @ApiParam(value = "审批流程信息实体", required = true) @Valid @RequestBody ApproveHistoryDto approveHistoryDto){
        if(approveHistoryDto.getPage()==null){
            approveHistoryDto.setPage(1);
        }
        if(approveHistoryDto.getRows()==null){
            approveHistoryDto.setRows(50);
        }
        PageInfo<ApproveHistoryDetailDto> pageInfo=new PageInfo<>();
        pageInfo.setPageSize(approveHistoryDto.getRows());
        pageInfo.setPageNum(approveHistoryDto.getPage());
        approveHistoryDto.setOrganSign(request.getHeader("organSign"));

        PageInfo<ApproveHistoryDetailDto> pList = approveHistoryApi.getDetaillist(approveHistoryDto, pageInfo);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(pList), HttpStatus.OK);
    }



}
