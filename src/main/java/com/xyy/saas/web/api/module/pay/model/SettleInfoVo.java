package com.xyy.saas.web.api.module.pay.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/5/7 19:27
 */
@Data
public class SettleInfoVo {

    @ApiModelProperty(value = "账户类型   （1：个人账户，2：公司账户）")
    private String settleType;

    @ApiModelProperty(value = "结算标志   （0：非法人结算，1：法人结算）")
    private String legalFlag;

    @ApiModelProperty(value = "开户支行联行号")
    private String unionPay;

    @ApiModelProperty(value = "开户支行名称")
    private String unionPayName;

    @ApiModelProperty(value = "开户行")
    private String bank;

    @ApiModelProperty(value = "开卡省市")
    private String openBankCardArea;

    @ApiModelProperty(value = "开户名 （法人结算：与法人姓名一致； 企业账户：与营业执照注册名称一致）")
    private String holder;

    @ApiModelProperty(value = "结算人证件(个人账户必传，非法人结算只能是身份证)，法人结算与法人证件号一致")
    private String idCardNo;

    @ApiModelProperty(value = "结算人证件类型（与法人证件参数相同，默认身份证）")
    private String idCardType;

    @ApiModelProperty(value = "结算人证件期限类型（短期 0   长期 1）")
    private String idCardEndType;

    @ApiModelProperty(value = "结算人证件开始时间 yyyy-MM-dd）")
    private String idCardBeginDate;

    @ApiModelProperty(value = "结算人证件结束时间 yyyy-MM-dd")
    private String idCardEndDate;

    @ApiModelProperty(value = "银行预留号码")
    private String mobile;

    @ApiModelProperty(value = "银行卡号")
    private String bankCardNo;

    @ApiModelProperty(value = "银行卡正面（如是对公账户，开户许可证）")
    private String bankCardFrontPic;

    @ApiModelProperty(value = "非法人结算授权书(非法人结算模式必传)")
    private String nonLegSettleAuthPic;

    @ApiModelProperty(value = "非法人身份证正面(非法人结算模式必传)")
    private String nonLegIdcardFrontPic;

    @ApiModelProperty(value = "非法人身份证背面(非法人结算模式必传)")
    private String nonLegIdcardBackPic;


}
