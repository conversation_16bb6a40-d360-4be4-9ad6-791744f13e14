package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * @ClassName MemberDayMixResultVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/8/26 18:15
 * @Version 1.0
 **/
@ApiModel(description = "会员日活动商品查询返回结果信息")
public class MemberDayMixResultVo {
    //商品编号，内码，用作查询
    @JsonProperty("pref")
    private String pref;
    //商品编号，外码，用作展示
    @JsonProperty("pharmacyPref")
    private String pharmacyPref;
    //通用名称
    @JsonProperty("commonName")
    private String commonName;
    //规格型号
    @JsonProperty("attributeSpecification")
    private String attributeSpecification;
    //单位名称
    @JsonProperty("unitName")
    private String unitName;
    //单位id
    @JsonProperty("unitId")
    private Integer unitId;
    //剂型id
    @JsonProperty("dosageFormId")
    private Integer dosageFormId;
    //剂型名称
    @JsonProperty("dosageFormName")
    private String dosageFormName;
    //生产厂家
    @JsonProperty("manufacturer")
    private String manufacturer;
    //商品分类id
    @JsonProperty("systemTypeId")
    private Integer systemTypeId;
    //商品分类名称
    @JsonProperty("systemTypeName")
    private String systemTypeName;

    @ApiModelProperty(value = "自定义分类id")
    public Integer getCustomTypeId() {
        return customTypeId;
    }

    public void setCustomTypeId(Integer customTypeId) {
        this.customTypeId = customTypeId;
    }

    @ApiModelProperty(value = "自定义分类名称")
    public String getCustomTypeName() {
        return customTypeName;
    }

    public void setCustomTypeName(String customTypeName) {
        this.customTypeName = customTypeName;
    }

    //自定义分类id
    @JsonProperty("customTypeId")
    private Integer customTypeId;
    //自定义分类名称
    @JsonProperty("customTypeName")
    private String customTypeName;
    //abc类型id
    @JsonProperty("abcTypeId")
    private Integer abcTypeId;
    //abc类型名称
    @JsonProperty("abcTypeName")
    private String abcTypeName;
    //库存数量
    @JsonProperty("stockNumber")
    private BigDecimal stockNumber;
    //成本价
    @JsonProperty("costPrice")
    private BigDecimal costPrice;
    //零售价
    @JsonProperty("retailPrice")
    private BigDecimal retailPrice;
    //vip会员价
    @JsonProperty("vipPrice")
    private BigDecimal vipPrice;
    //毛利率
    @JsonProperty("grossMargin")
    private BigDecimal grossMargin;
    //产地
    @JsonProperty("producingArea")
    private String producingArea;

    @ApiModelProperty(value = "商品内码，用于内部查询")
    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    @ApiModelProperty(value = "商品编码，做展示")
    public String getPharmacyPref() {
        return pharmacyPref;
    }

    public void setPharmacyPref(String pharmacyPref) {
        this.pharmacyPref = pharmacyPref;
    }

    @ApiModelProperty(value = "通用名称")
    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    @ApiModelProperty(value = "规格")
    public String getAttributeSpecification() {
        return attributeSpecification;
    }

    public void setAttributeSpecification(String attributeSpecification) {
        this.attributeSpecification = attributeSpecification;
    }

    @ApiModelProperty(value = "单位名称，做展示")
    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    @ApiModelProperty(value = "单位id")
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @ApiModelProperty(value = "剂型id")
    public Integer getDosageFormId() {
        return dosageFormId;
    }

    public void setDosageFormId(Integer dosageFormId) {
        this.dosageFormId = dosageFormId;
    }

    @ApiModelProperty(value = "剂型名称，做展示")
    public String getDosageFormName() {
        return dosageFormName;
    }

    public void setDosageFormName(String dosageFormName) {
        this.dosageFormName = dosageFormName;
    }

    @ApiModelProperty(value = "生产厂家")
    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    @ApiModelProperty(value = "商品分类字典id")
    public Integer getSystemTypeId() {
        return systemTypeId;
    }

    public void setSystemTypeId(Integer systemTypeId) {
        this.systemTypeId = systemTypeId;
    }

    @ApiModelProperty(value = "商品分类名称")
    public String getSystemTypeName() {
        return systemTypeName;
    }

    public void setSystemTypeName(String systemTypeName) {
        this.systemTypeName = systemTypeName;
    }

    @ApiModelProperty(value = "abc分类字典id")
    public Integer getAbcTypeId() {
        return abcTypeId;
    }

    public void setAbcTypeId(Integer abcTypeId) {
        this.abcTypeId = abcTypeId;
    }

    @ApiModelProperty(value = "abc分类名称")
    public String getAbcTypeName() {
        return abcTypeName;
    }

    public void setAbcTypeName(String abcTypeName) {
        this.abcTypeName = abcTypeName;
    }

    @ApiModelProperty(value = "当前库存数量")
    public BigDecimal getStockNumber() {
        return stockNumber;
    }

    public void setStockNumber(BigDecimal stockNumber) {
        this.stockNumber = stockNumber;
    }

    @ApiModelProperty(value = "成本价")
    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    @ApiModelProperty(value = "零售价")
    public BigDecimal getRetailPrice() {
        return retailPrice;
    }

    public void setRetailPrice(BigDecimal retailPrice) {
        this.retailPrice = retailPrice;
    }

    @ApiModelProperty(value = "会员价")
    public BigDecimal getVipPrice() {
        return vipPrice;
    }

    public void setVipPrice(BigDecimal vipPrice) {
        this.vipPrice = vipPrice;
    }

    @ApiModelProperty(value = "毛利率")
    public BigDecimal getGrossMargin() {
        return grossMargin;
    }

    public void setGrossMargin(BigDecimal grossMargin) {
        this.grossMargin = grossMargin;
    }

    @ApiModelProperty(value = "产地")
    public String getProducingArea() {
        return producingArea;
    }

    public void setProducingArea(String producingArea) {
        this.producingArea = producingArea;
    }
}
