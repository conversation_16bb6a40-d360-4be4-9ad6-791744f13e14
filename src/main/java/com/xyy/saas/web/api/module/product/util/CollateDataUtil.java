package com.xyy.saas.web.api.module.product.util;

import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

public class CollateDataUtil {

    public static <E,T> List<E> copyListProperties(List<T> sourceList, Class<E> e){
        if(sourceList == null){
            return null;
        }
        List<E> returnList = new ArrayList<>(sourceList.size());
        E tar;
        for (int i = 0; i < sourceList.size(); i++) {
            try {
                tar = e.newInstance();
                BeanUtils.copyProperties(sourceList.get(i),tar);
                returnList.add(tar);
            } catch (InstantiationException e1) {
                e1.printStackTrace();
            } catch (IllegalAccessException e1) {
                e1.printStackTrace();
            }
        }
        return returnList;
    }


    public static <E,T> PageInfo<E> copyPageProperties(PageInfo<T> sourcePage, Class<E> e){
        PageInfo<E> resultPage = new PageInfo<>();
        BeanUtils.copyProperties(sourcePage,resultPage);
        List<T> sourceList = sourcePage.getList();
        List<E>  resultList = copyListProperties(sourceList,e);
        resultPage.setList(resultList);
        return resultPage;
    }
}