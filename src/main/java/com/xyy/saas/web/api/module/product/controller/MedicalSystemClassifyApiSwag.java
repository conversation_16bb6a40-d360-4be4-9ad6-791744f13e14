package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.web.api.module.product.model.MedicalSystemClassify;
import io.swagger.annotations.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @title: MedicalProductApiSwag
 * @date 2019-09-07  20:43
 * @description: 医保商品swag
 */

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-09-07T21:07:46.794+08:00")
@Api(value = "MedicalSystemClassifyApiSwag", description = "医保分类与系统分类转换API接口")
@RequestMapping("/product/medical")
public interface MedicalSystemClassifyApiSwag {


    /**
     * 查询已同步的医保商品列表
     */
    @ApiOperation(value = "查询医保分类与系统分类", notes = "查询医保分类与系统分类", response = ResultVO.class, tags={ "医保分类与系统分类转换API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/findMedicalSystemClassify",method = RequestMethod.POST)
    public ResultVO findMedicalSystemClassify(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody MedicalSystemClassify medicalSystemClassify);


}
