package com.xyy.saas.web.api.module.pay.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/5/7 19:21
 */
@ApiModel(description = "店铺信息")
@Data
public class StoreInfoVo {

    @ApiModelProperty(value = "法人身份证正面")
    private String corporationCardFrontImg;
    @ApiModelProperty(value = "法人身份证反面")
    private String corporationCardBackImg;
    @ApiModelProperty(value = "法人姓名")
    private String corporationName;
    @ApiModelProperty(value = "法人证件类型 （  身份证： 01）")
    private String corporationCardType;
    @ApiModelProperty(value = "法人证件号码")
    private String corporationCardNo;
    @ApiModelProperty(value = "法人证件期限类型 （短期 0   长期 1）")
    private String corporationCardEndType;
    @ApiModelProperty(value = "法人证件签发日期 yyyy-MM-dd")
    private String corporationCardStartDate;
    @ApiModelProperty(value = "法人证件到期日期 yyyy-MM-dd")
    private String corporationCardEndDate;
}
