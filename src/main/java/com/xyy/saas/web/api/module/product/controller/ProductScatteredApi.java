package com.xyy.saas.web.api.module.product.controller;

import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;

@RequestMapping("/product")
@Api(value = "4.0商品拆零信息维护API接口", description = "4.0商品拆零信息维护API接口")
public interface ProductScatteredApi {

//    @ApiOperation(value = "商品拆零信息维护新增数据回显接口", notes = "商品拆零信息维护新增数据回显接口", response = ResultVO.class, tags={ "4.0商品拆零信息维护API接口", })
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
//    @ResponseBody
//    @RequestMapping(value = "/baseinfo/proScattered/toAdd",method = RequestMethod.POST)
//    public ResponseEntity<ResultVO> toAdd(HttpServletRequest request, Model model);
//
//    @ApiOperation(value = "商品拆零信息维护编辑数据回显接口", notes = "商品拆零信息维护编辑数据回显接口", response = ProductScatteredRuleVoDto.class, tags={ "4.0商品拆零信息维护API接口", })
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ProductScatteredRuleVoDto.class) })
//    @ResponseBody
//    @RequestMapping(value = "/baseinfo/proScattered/toUpdate",method = RequestMethod.POST)
//    public ResponseEntity<ResultVO> getProScatteredById(HttpServletRequest request,@ApiParam(value = "供应商查询对象" ,required=true )  @Valid @RequestBody ProviderQueryVo providerQueryVo);
//
//    @ApiOperation(value = "商品拆零信息维护新增，更新提交接口", notes = "商品拆零信息维护新增，更新提交接口", response = ResultVO.class, tags={ "4.0商品拆零信息维护API接口", })
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
//    @ResponseBody
//    @RequestMapping(value = "/baseinfo/proScattered/save", method = RequestMethod.POST)
//    public ResponseEntity<ResultVO> addOrUpdate(HttpServletRequest request,@ApiParam(value = "拆零商品信息对象" ,required=true )  @Valid @RequestBody ProductScatteredRuleDto scatteredRulePo) throws InterruptedException ;
//
//    @ApiOperation(value = "商品拆零信息维护删除接口", notes = "商品拆零信息维护删除接口", response = ResultVO.class, tags={ "4.0商品拆零信息维护API接口", })
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
//    @ResponseBody
//    @RequestMapping(value = "/baseinfo/proScattered/delete",method = RequestMethod.POST)
//    public ResponseEntity<ResultVO> deleteScatteredById(HttpServletRequest request,@ApiParam(value = "商品拆零查询对象" ,required=true )  @Valid @RequestBody ProviderQueryVo providerQueryVo);
//
//    @ApiOperation(value = "商品拆零信息维护查询页面数据回显接口", notes = "商品拆零信息维护查询页面数据回显接口", response = ResultVO.class, tags={ "4.0商品拆零信息维护API接口", })
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
//    @ResponseBody
//    @RequestMapping(value = "/baseinfo/proScattered/toList",method = RequestMethod.POST)
//    public ResponseEntity<ResultVO> toList(HttpServletRequest request,Model model);
//
//    @ApiOperation(value = "商品拆零信息维护列表查询接口", notes = "商品拆零信息维护列表查询接口", response = ResultVO.class, tags={ "4.0商品拆零信息维护API接口", })
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
//    @RequestMapping(value = "/baseinfo/proScattered/query",method = RequestMethod.POST)
//    @ResponseBody
//    public ResponseEntity<ResultVO<PageInfo>> query(HttpServletRequest request,@ApiParam(value = "商品拆零查询对象" ,required=true )  @Valid @RequestBody ProviderQueryVo providerQueryVo);
//
//    @ApiOperation(value = "商品拆零信息维护新增字典数据回显接口", notes = "商品拆零信息维护新增字典数据回显接口", response = ResultVO.class, tags={ "4.0商品拆零信息维护API接口", })
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
//    @ResponseBody
//    @RequestMapping(value = "/baseinfo/proScattered/noRulePro",method = RequestMethod.POST)
//    public ResponseEntity<ResultVO> toNoRuleProductList(HttpServletRequest request,Model model);
//
//    @ApiOperation(value = "商品拆零信息维护新增字典数据回显接口", notes = "商品拆零信息维护新增字典数据回显接口", response = ResultVO.class, tags={ "4.0商品拆零信息维护API接口", })
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
//    @ResponseBody
//    @RequestMapping(value = "/baseinfo/proScattered/productQuery",method = RequestMethod.POST)
//    public ResponseEntity<ResultVO<PageInfo>> productQuery(HttpServletRequest request,@ApiParam(value = "商品拆零查询对象" ,required=true )  @Valid @RequestBody ProviderQueryVo providerQueryVo);
//
//    @ApiOperation(value = "接口用途未知", notes = "接口用途未知", response = ResultVO.class, tags={ "4.0商品拆零信息维护API接口", })
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
//    @ResponseBody
//    @RequestMapping(value = "/baseinfo/proScattered/fightproductQuery",method = RequestMethod.POST)
//    public ResponseEntity<ResultVO> fightproductQuery(HttpServletRequest request,@ApiParam(value = "商品拆零查询对象" ,required=true )  @Valid @RequestBody ProviderQueryVo providerQueryVo);

}
