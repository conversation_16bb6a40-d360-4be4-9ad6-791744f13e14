package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.api.ExportExcelApi;
import com.xyy.saas.common.api.SystemDictApi;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.dto.ExportExcelDto;
import com.xyy.saas.common.dto.SystemDictDto;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.inventory.cores.api.SaasPoSitionApi;
import com.xyy.saas.inventory.cores.common.IRemoteBean;
import com.xyy.saas.inventory.cores.dto.SaasPositionVo;
import com.xyy.saas.product.core.api.ProductCompatibilityApi;
import com.xyy.saas.product.core.api.ProductDictionaryApi;
import com.xyy.saas.product.core.dto.*;
import com.xyy.saas.rabbitmq.core.api.MessagePushApi;
import com.xyy.saas.web.api.module.product.constant.DictConstant;
import com.xyy.saas.web.api.module.product.model.*;
import com.xyy.saas.web.api.module.product.util.DateUtil;
import com.xyy.saas.web.api.module.product.util.ExecServiceTask;
import com.xyy.saas.web.api.module.product.util.ExportExcelUtil;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.api.EmployeefileApi;
import com.xyy.user.module.api.RoleApi;
import com.xyy.user.module.dto.EmployeefileRequestModel;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import com.xyy.user.module.dto.result.EmployeeDto;
import com.xyy.user.module.enums.DrugstoreBizModelEnum;
import com.xyy.user.module.enums.DrugstoreTypeEnum;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T15:22:37.401+08:00")
@Controller
public class ProductCompatibilityApiController implements ProductCompatibilityInfoApi {

    @Reference(version = "0.0.1")
    private SystemDictApi systemDictApi;

    @Reference(version = "0.0.1")
    private ProductDictionaryApi productDictionaryApi;

    @Reference(version = "0.0.1")
    private ProductCompatibilityApi productCompatibilityApi;

    @Reference(version = "0.0.1")
    private com.xyy.saas.product.core.api.ProductApi productApi;

    @Reference(version = "0.0.2")
    private SaasPoSitionApi poSitionApi;

    @Reference(version = "0.0.1")
    private EmployeeApi employeeApi;

    @Reference(version = "0.0.1")
    private EmployeefileApi employeefileApi;

    @Reference(version = "0.0.1")
    private MessagePushApi messagePushApi;

    @Reference(version = "0.0.3")
    public ExportExcelApi exportExcelApi;

    @Reference(version = "0.0.1")
    public RoleApi roleUserApi;

    @Value("${ftp.serverName}")
    public String serverName;

    @Value("${ftp.port}")
    public String port;

    @Value("${ftp.username}")
    public String username;

    @Value("${ftp.password}")
    public String password;

    @Value("${ftp.remotePath}")
    public String remotePath;

    @Value("${ftp.downLoadUrl}")
    public String downLoadUrl;

    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;

    /**
     * 福鼎市隐藏双轨处方和处方类别配置
     */
    @Value("${product.prescriptionYn.doubleTrack.hidden:}")
    private String productPrescriptionYnDoubleTrackHidden;

    /**
     * 智鹿 医保相关字段置灰配置 (指定门店或总部机构号)
     */
    @Value("${product.medical.info.readonly.organsigns:}")
    private String productMedicalInfoReadonlyOrgansigns;

    private boolean isDoubleTrackHidden(String organSign) {
        if (StringUtils.isEmpty(productPrescriptionYnDoubleTrackHidden)) {
            return false;
        }
        if ("all".equalsIgnoreCase(productPrescriptionYnDoubleTrackHidden)) {
            return true;
        }
        String[] split = productPrescriptionYnDoubleTrackHidden.split(",");
        boolean matchOrganSign = Arrays.stream(split).anyMatch(s -> s.equalsIgnoreCase(organSign));
        if (matchOrganSign) {
            return true;
        }
        // 判断店铺areaCode
        SaaSDrugstoreDto drugstore = drugstoreApi.getDrugstoreByOrganSign(organSign);
        if (drugstore != null && drugstore.getAreaCode() != null) {
            String areaCode = drugstore.getAreaCode();
            // areaCode 例 "420000,421100,421181"
            // 判断apollo配置的areaCode 比如421100 是否在配置的areaCode中
            for (String s : areaCode.split(",")) {
                if (Arrays.asList(split).contains(s)) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean isMedicalInfoReadonly(String organSign) {
        if (StringUtils.isEmpty(productMedicalInfoReadonlyOrgansigns)) {
            return false;
        }
        if ("all".equalsIgnoreCase(productMedicalInfoReadonlyOrgansigns)) {
            return true;
        }
        String[] split = productMedicalInfoReadonlyOrgansigns.split(",");
        boolean matchOrganSign = Arrays.stream(split).anyMatch(s -> s.equalsIgnoreCase(organSign));
        if (matchOrganSign) {
            return true;
        }
        // 判断店铺总部机构号
        SaaSDrugstoreDto drugstore = drugstoreApi.getDrugstoreByOrganSign(organSign);
        if (drugstore != null && org.apache.commons.lang3.StringUtils.isNotBlank(drugstore.getHeadquartersOrganSign())) {
            return Arrays.stream(split).anyMatch(s -> s.equalsIgnoreCase(drugstore.getHeadquartersOrganSign()));
        }
        return false;
    }

    private static final Logger logger = LoggerFactory.getLogger(ProductCompatibilityApiController.class);

    @Override
    public ResponseEntity<ResultVO> toList(@ApiParam(value = "当前登录机构码" ,required=true) @RequestHeader(value="organSign", required=true) String organSign,
                                           @ApiParam(value = "当前登录员工身份" ,required=true) @RequestHeader(value="identity", required=true) String identity,
                                           @ApiParam(value = "当前登录员工的ID" ,required=true) @RequestHeader(value="employee", required=true) String employee) {
        List<ProductTypeVo> typeList = new ArrayList<>();
        ProductTypeVo type1 = new ProductTypeVo();
        type1.setValue(1);
        type1.setLabel("药品(含中成药)");
        ProductTypeVo type2 = new ProductTypeVo();
        type1.setValue(2);
        type1.setLabel("中药饮片");
        ProductTypeVo type3 = new ProductTypeVo();
        type1.setValue(3);
        type1.setLabel("化妆品");
        ProductTypeVo type4 = new ProductTypeVo();
        type1.setValue(4);
        type1.setLabel("器械");
        ProductTypeVo type5 = new ProductTypeVo();
        type1.setValue(5);
        type1.setLabel("保健品");
        ProductTypeVo type6 = new ProductTypeVo();
        type1.setValue(6);
        type1.setLabel("计生用品");
        ProductTypeVo type7 = new ProductTypeVo();
        type1.setValue(7);
        type1.setLabel("其他");
        typeList.add(type1);
        typeList.add(type2);
        typeList.add(type3);
        typeList.add(type4);
        typeList.add(type5);
        typeList.add(type6);
        typeList.add(type7);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(typeList), HttpStatus.OK);
    }


    @Override
    public ResponseEntity<ResultVO> query(HttpServletRequest request,
                                          @ApiParam(value = "请求参数体" ,required=true )  @Valid @RequestBody ProductCompatibilityQueryVo productCompatibilityQueryVo) {
        String organSign = request.getHeader("organSign");
        String employeeId = request.getHeader("employeeId");
        String identity = employeeApi.queryEmployeeById(Integer.valueOf(employeeId)).getResult().getIdentity()+"";
        logger.info("pw queryList params:mixquery:"+productCompatibilityQueryVo.getMixQueyStr()+",productType:"
                + productCompatibilityQueryVo.getProductType()+",systemType:"+productCompatibilityQueryVo.getSystemType()+",startTimeStr:"
                + productCompatibilityQueryVo.getStartTimeStr()+",endTimeStr:"+productCompatibilityQueryVo.getEndTimeStr()+",page:"
                + productCompatibilityQueryVo.getPage()+",rows:"+productCompatibilityQueryVo.getRows());
        Byte isProductHidden = Byte.valueOf(identity);
        PageInfo pageInfo = new PageInfo();
        if(productCompatibilityQueryVo.getPage() == null){
            pageInfo.setPageNum(1);
        }else{
            pageInfo.setPageNum(productCompatibilityQueryVo.getPage());
        }
        if(productCompatibilityQueryVo.getRows() == null){
            pageInfo.setPageSize(10);
        }else{
            pageInfo.setPageSize(productCompatibilityQueryVo.getRows());
        }
        String headOrganSign = organSign;
        //如果是连锁门店，需要获取总部的机构号
        String modelJson = request.getHeader("commonRequestModel");
        Byte bizModel = null;
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            bizModel = model.getBizModel();
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                headOrganSign = model.getHeadquartersOrganSign();
            }
        }
        //设置查询条件
        ProductCompatibilityQueryDto queryDto = new ProductCompatibilityQueryDto();
        queryDto.setMixQuery(productCompatibilityQueryVo.getMixQueyStr());
        queryDto.setOrganSign(organSign);
        queryDto.setHeadOrganSign(headOrganSign);
        queryDto.setSystemType(productCompatibilityQueryVo.getSystemType());
        queryDto.setProductType(productCompatibilityQueryVo.getProductType());
        queryDto.setStartTime(StringUtils.isEmpty(productCompatibilityQueryVo.getStartTimeStr())?null:productCompatibilityQueryVo.getStartTimeStr());
        queryDto.setEndTime(StringUtils.isEmpty(productCompatibilityQueryVo.getEndTimeStr())?null:productCompatibilityQueryVo.getEndTimeStr());
        queryDto.setIsProductHidden(isProductHidden);
        queryDto.setPharmacyPref(productCompatibilityQueryVo.getPharmacyPref());//设置外码查询
        queryDto.setCreateUser(productCompatibilityQueryVo.getCreateUser());
        //调用api返回结果集
        ResultVO<PageInfo<ProductCompDetailListDto>> resultVO = productCompatibilityApi.findList(pageInfo,queryDto);
        ResultVO<PageInfo<ProductCompatibilityTabooListVo>> resultVO1= new ResultVO<PageInfo<ProductCompatibilityTabooListVo>>();
        BeanUtils.copyProperties(resultVO,resultVO1);
        logger.info("pw queryList productCompatibilityApi.findList resultVO:"+resultVO);
        //对结果集进行封装，返回VO对应的字段属性值
        PageInfo<ProductCompDetailListDto> pageInfos = resultVO.getResult();
        PageInfo<ProductCompatibilityTabooListVo> pageInfos1 = new  PageInfo<ProductCompatibilityTabooListVo>();
        BeanUtils.copyProperties(pageInfos,pageInfos1);
        resultVO1.setResult(pageInfos1);
        List<ProductCompDetailListDto> listDtos = pageInfos.getList();
        List<ProductCompatibilityTabooListVo> resultListDtos = new ArrayList<>();
        pageInfos1.setList(resultListDtos);
        try{
            if(listDtos != null && listDtos.size() > 0){
                for(ProductCompDetailListDto resultPctDto:listDtos){
                    //对主表进行封装
                    ProductCompatibilityTabooListVo resultPctVo = new ProductCompatibilityTabooListVo();
                    resultPctVo.setId(resultPctDto.getId());
                    resultPctVo.setAttributeSpecification(resultPctDto.getAttributeSpecification());
                    resultPctVo.setBaseVersion(resultPctDto.getBaseVersion());
                    resultPctVo.setCommonName(resultPctDto.getCommonName());
                    resultPctVo.setCreateTime(resultPctDto.getCreateTime());
//                    resultPctVo.setCreateUser(Integer.valueOf(resultPctDto.getCreateUser()));
                    resultPctVo.setGuid(resultPctDto.getGuid());
                    resultPctVo.setMnemonicCode(resultPctDto.getMnemonicCode());
                    resultPctVo.setOrganSign(resultPctDto.getOrganSign());
                    resultPctVo.setPharmacyPref(resultPctDto.getPharmacyPref());
                    resultPctVo.setPref(resultPctDto.getPref());
                    resultPctVo.setProductName(resultPctDto.getProductName());
                    resultPctVo.setRemark(resultPctDto.getRemark());
                    resultPctVo.setUnitId(resultPctDto.getUnitId());
                    resultPctVo.setUpdateTime(resultPctDto.getUpdateTime());
                    resultPctVo.setUpdateUser(resultPctDto.getUpdateUser());
                    resultPctVo.setYn(resultPctDto.getYn());
                    resultPctVo.setSystemType(resultPctDto.getSystemType());
                    resultPctVo.setProductType(resultPctDto.getProductType());
                    resultPctVo.setDrugPermissionPerson(resultPctDto.getDrugPermissionPerson());
                    //对子表进行封装
                    List<ComDetailsDto> comDetailsDtos = resultPctDto.getComDetailsDtos();
                    List<ProductCompatibilityTabooListDetailVo> comDetailsVos = new ArrayList<>();
                    if(comDetailsDtos != null && comDetailsDtos.size() > 0){
                        for(ComDetailsDto cdd:comDetailsDtos){
                            ProductCompatibilityTabooListDetailVo vo = new ProductCompatibilityTabooListDetailVo();
                            vo.setAttributeSpecification(cdd.getAttributeSpecification());
                            vo.setBaseVersion(cdd.getBaseVersion());
                            vo.setCommonName(cdd.getCommonName());
                            vo.setCreateTime(DateUtil.parseDateToStr(cdd.getCreateTime(),DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS));
                            vo.setCreateUser(cdd.getCreateUser());
                            vo.setGuid(cdd.getGuid());
                            vo.setId(cdd.getId());
                            vo.setOrganSign(cdd.getOrganSign());
                            vo.setPharmacyPref(cdd.getPharmacyPref());
                            vo.setProductName(cdd.getProductName());
                            vo.setSubPref(cdd.getSubPref());
                            vo.setUnitId(cdd.getUnitId());
                            vo.setUpdateTime(cdd.getUpdateTime());
                            vo.setUpdateUser(cdd.getUpdateUser());
                            vo.setYn(cdd.getYn());
                            vo.setSystemType(cdd.getSystemType());
                            vo.setProductType(cdd.getProductType());
                            vo.setDrugPermissionPerson(cdd.getDrugPermissionPerson());
                            comDetailsVos.add(vo);
                        }
                    }
                    resultPctVo.setDetailDtos(comDetailsVos);
                    resultListDtos.add(resultPctVo);
                }
            }
        }catch (Exception e){
            logger.error("pw queryList occur error:"+e);
            e.printStackTrace();
        }
        logger.info("pw queryList pageResult:多少页:"+pageInfos.getPages()+",当前页："+pageInfos.getPageNum()
                +",每页信息量："+pageInfos.getPageSize()+",总量："+pageInfos.getTotal()+"");
        return new ResponseEntity<ResultVO>(resultVO1, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> delete(HttpServletRequest request,
                                           @ApiParam(value = "请求参数体" ,required=true )  @Valid @RequestBody ProductCompatibilityQueryVo productCompatibilityQueryVo) {
        String organSign = request.getHeader("organSign");
        String employeeId = request.getHeader("employeeId");
        ProductCompatibilityTabooListDto proDto = new ProductCompatibilityTabooListDto();
        proDto.setOrganSign(organSign);
        proDto.setGuid(productCompatibilityQueryVo.getGuid());
        proDto.setPref(productCompatibilityQueryVo.getPref());
        proDto.setUpdateUser(employeeId);//雇员id
        proDto.setCreateUser(employeeId);//雇员id
        logger.info("pw delete param:organSign:"+organSign+",guid:"+productCompatibilityQueryVo.getGuid()+",pref:"+productCompatibilityQueryVo.getPref()+",userid:"+employeeId);
        ResultVO resultVO = productCompatibilityApi.delProductCompatibilityTaboo(proDto);
        pushProductMessToMQ(organSign);
        logger.info("pw delete result:result:"+resultVO.getResult());
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> selectOutPW(HttpServletRequest request,
                                                @ApiParam(value = "请求参数体" ,required=true )  @Valid @RequestBody ProductCompatibilityQueryVo productCompatibilityQueryVo) {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        String identity = employeeApi.queryEmployeeById(Integer.valueOf(employee)).getResult().getIdentity()+"";
        logger.info("pw selectOutPW params:organSign:"+organSign+",employee:"+employee+",identity:"+identity
                +",mixquery:"+productCompatibilityQueryVo.getMixQueyStr()+",page:"+productCompatibilityQueryVo.getPage()+",rows:"+productCompatibilityQueryVo.getRows());
        PageInfo pageInfo = new PageInfo();
        if(null==productCompatibilityQueryVo.getPage()){
            pageInfo.setPageNum(1);
        }else {
            pageInfo.setPageNum(productCompatibilityQueryVo.getPage());
        }
        if(null == productCompatibilityQueryVo.getRows()){
            pageInfo.setPageSize(10);
        }else {
            pageInfo.setPageSize(productCompatibilityQueryVo.getRows());
        }
        String headOrganSign = organSign;
        //如果是连锁门店，需要获取总部的机构号
        String modelJson = request.getHeader("commonRequestModel");
        Byte bizModel = null;
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            bizModel = model.getBizModel();
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                headOrganSign = model.getHeadquartersOrganSign();
            }
        }
        ProductPwDto dto = new ProductPwDto();
        dto.setCommonName(productCompatibilityQueryVo.getMixQueyStr());
        dto.setIsProductHidden(Byte.valueOf(identity));
        dto.setScayn(null);
        dto.setSystemType(productCompatibilityQueryVo.getSystemType());
        dto.setOrganSign(organSign);
        dto.setPharmacyPref(productCompatibilityQueryVo.getPharmacyPref());
        dto.setProductName(productCompatibilityQueryVo.getProductName());
        dto.setApprovalNumber(productCompatibilityQueryVo.getApprovalNumber());
        dto.setHeadOrganSign(headOrganSign);

        PageInfo productDtoPageInfo = productApi.ruleOutPWProductQuery(pageInfo,dto);
//        logger.info("pw selectOutPW productApi.ruleOutPWProductQuery success"+productDtoPageInfo);
        PageInfo<ProductCompatibilityVo> resultPageInfo = new PageInfo<>();
        List<ProductDto> productDtos = productDtoPageInfo.getList();
        List<ProductCompatibilityVo> results = new ArrayList<>();
        for(ProductDto pdt:productDtos){
            ProductCompatibilityVo vo = new ProductCompatibilityVo();
            vo.setCommonName(pdt.getCommonName());
            vo.setId(pdt.getId());
            vo.setOrganSign(pdt.getOrganSign());
            vo.setApprovalNumber(pdt.getApprovalNumber());
            vo.setAttributeSpecification(pdt.getAttributeSpecification());
            vo.setDosageFormId(pdt.getDosageFormId());
            vo.setManufacturer(pdt.getManufacturer());
            vo.setPharmacyPref(pdt.getPharmacyPref());
            vo.setProductName(pdt.getProductName());
            vo.setPref(pdt.getPref());
            vo.setUnitId(pdt.getUnitId());
            vo.setSystemType(pdt.getSystemType());
            vo.setPrescriptionClassification(pdt.getPrescriptionClassification());
            vo.setMedicalInsurance(pdt.getMedicalInsurance());
            vo.setDrugPermissionPerson(pdt.getDrugPermissionPerson());
            results.add(vo);
        }
        productDtoPageInfo.setList(results);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(productDtoPageInfo), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> select(HttpServletRequest request,
                                           @ApiParam(value = "请求参数体" ,required=true )  @Valid @RequestBody ProductCompatibilityQueryVo productCompatibilityQueryVo) {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        String identity = employeeApi.queryEmployeeById(Integer.valueOf(employee)).getResult().getIdentity()+"";
        logger.info("pw select params:organSign:"+organSign+",employee:"+employee+",identity:"+identity
                +",mixQuery:"+productCompatibilityQueryVo.getMixQueyStr()+",page:"
                +productCompatibilityQueryVo.getPage()+",rows:"+productCompatibilityQueryVo.getRows()+",pref:"+productCompatibilityQueryVo.getPref());
        PageInfo pageInfo = new PageInfo();
        if(null==productCompatibilityQueryVo.getPage()){
            pageInfo.setPageNum(1);
        }else {
            pageInfo.setPageNum(productCompatibilityQueryVo.getPage());
        }
        if(null == productCompatibilityQueryVo.getRows()){
            pageInfo.setPageSize(10);
        }else {
            pageInfo.setPageSize(productCompatibilityQueryVo.getRows());
        }
        //获取配伍主商品已经配伍的商品编号列表
        List<String> notInPrefs = productCompatibilityApi.getNotPrefList(productCompatibilityQueryVo.getPref(),organSign);
        String prefStrinig = productCompatibilityQueryVo.getPwPrefListStr();
        List<String> pagePrefList = new ArrayList<>();
        if(!StringUtils.isEmpty(prefStrinig)){
            String[] prefArray = prefStrinig.split(",");
            if(prefArray != null && prefArray.length > 0){
                for(String pref:prefArray){
                    pagePrefList.add(pref.trim());
                }
            }
        }
        notInPrefs.add(productCompatibilityQueryVo.getPref());//添加主商品进不能配伍子商品列表接口
//        logger.info("pw select notInPrefs:"+notInPrefs);
        notInPrefs.addAll(pagePrefList);//集合合并处理
//        logger.info("pw select notInPrefs.addAll notInPrefs:"+notInPrefs);
        HashSet h = new HashSet(notInPrefs);//去重处理
        notInPrefs.clear();
        notInPrefs.addAll(h);
        //如果是连锁门店，需要获取总部的机构号
        String modelJson = request.getHeader("commonRequestModel");
        Byte bizModel = null;
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            bizModel = model.getBizModel();
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                organSign = model.getHeadquartersOrganSign();
            }
        }
//        logger.info("pw select notInPrefs.addAll(h) notInPrefs:"+notInPrefs);
        PageInfo<ProductDto> productDtoPageInfo = productApi.ruleProductQueryNotInList(pageInfo,productCompatibilityQueryVo.getMixQueyStr() , Byte.valueOf(identity),null,productCompatibilityQueryVo.getProductType(), organSign,notInPrefs,productCompatibilityQueryVo.getPharmacyPref(),productCompatibilityQueryVo.getProductName(),productCompatibilityQueryVo.getApprovalNumber());
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(productDtoPageInfo), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> addSaveCommit(HttpServletRequest request,
                                                  @ApiParam(value = "请求参数体" ,required=true )  @Valid @RequestBody ProductCompatiSaveUpdateVo saveVo) {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        ProductCompatibilityTabooListDto dto = new ProductCompatibilityTabooListDto();
        dto.setPref(saveVo.getPref());
        dto.setOrganSign(organSign);
        logger.info("pw addSaveCommit create user:"+saveVo.getCreateUser());
        dto.setCreateUser(StringUtils.isEmpty(saveVo.getCreateUser())?employee:saveVo.getCreateUser());
        dto.setUpdateUser(StringUtils.isEmpty(saveVo.getCreateUser())?employee:saveVo.getCreateUser());
        List<ProductComDetailUpdateSaveVo> vos = saveVo.getComDetails();
        List<ProductCompatibilityTabooListDetailDto> results = new ArrayList<>();
        for(ProductComDetailUpdateSaveVo vo:vos){
            ProductCompatibilityTabooListDetailDto dt = new ProductCompatibilityTabooListDetailDto();
            dt.setSubPref(vo.getSubPref());
            results.add(dt);
        }
        dto.setDetailDtos(results);
        ResultVO resultVO = productCompatibilityApi.saveProductCompatibilityTaboo(dto);
        pushProductMessToMQ(organSign);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> updateCommit(HttpServletRequest request,
                                                 @ApiParam(value = "请求参数体" ,required=true )  @Valid @RequestBody ProductCompatiSaveUpdateVo saveVo) {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        logger.info("pw updateCommit param:organSign:"+organSign+",employee:"+employee+",pref:"+saveVo.getPref()+",create user:"+saveVo.getCreateUser());
        ProductCompatibilityTabooListDto dto = new ProductCompatibilityTabooListDto();
        dto.setPref(saveVo.getPref());
        dto.setOrganSign(organSign);
        dto.setCreateUser(StringUtils.isEmpty(saveVo.getCreateUser())?employee:saveVo.getCreateUser());
        dto.setUpdateUser(StringUtils.isEmpty(saveVo.getCreateUser())?employee:saveVo.getCreateUser());
        List<ProductComDetailUpdateSaveVo> vos = saveVo.getComDetails();
        List<ProductCompatibilityTabooListDetailDto> results = new ArrayList<>();
        for(ProductComDetailUpdateSaveVo vo:vos){
            ProductCompatibilityTabooListDetailDto dt = new ProductCompatibilityTabooListDetailDto();
            dt.setSubPref(vo.getSubPref());
            logger.info("pw updateCommit zishangpin param::"+organSign+",employee:"+employee+",pref:"+saveVo.getPref()+",subPref:"+vo.getSubPref());
            results.add(dt);
        }
        dto.setDetailDtos(results);
        ResultVO resultVO = productCompatibilityApi.updateProductCompatibilityTaboo(dto);
        pushProductMessToMQ(organSign);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> updateDataCallback(HttpServletRequest request,
                                                       @ApiParam(value = "请求参数体" ,required=true )  @Valid @RequestBody ProductCompatibilityQueryVo productCompatibilityQueryVo) {
        String organSign = request.getHeader("organSign");
        String headOrganSign = organSign;
        //如果是连锁门店，需要获取总部的机构号
        String modelJson = request.getHeader("commonRequestModel");
        Byte bizModel = null;
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            bizModel = model.getBizModel();
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                headOrganSign = model.getHeadquartersOrganSign();
            }
        }
        ProductCompatibilityTabooListDto dto = new ProductCompatibilityTabooListDto();
        dto.setGuid(productCompatibilityQueryVo.getGuid());
        dto.setOrganSign(organSign);
        dto.setPref(productCompatibilityQueryVo.getPref());
        dto.setHeadOrganSign(headOrganSign);
        ResultVO<ProductCompDetailListDto> resultVO =  productCompatibilityApi.getProductCompatibilityTaboo(dto);
        ProductCompDetailListDto resultPctDto = resultVO.getResult();
        //进行配伍禁忌主表的字段赋值
        ProductCompatibilityTabooListVo resultPctVo = new ProductCompatibilityTabooListVo();
        resultPctVo.setId(resultPctDto.getId());
        resultPctVo.setAttributeSpecification(resultPctDto.getAttributeSpecification());
        resultPctVo.setBaseVersion(resultPctDto.getBaseVersion());
        resultPctVo.setCommonName(resultPctDto.getCommonName());
        resultPctVo.setCreateTime(resultPctDto.getCreateTime());
        resultPctVo.setCreateUser(null);
        resultPctVo.setGuid(resultPctDto.getGuid());
        resultPctVo.setMnemonicCode(resultPctDto.getMnemonicCode());
        resultPctVo.setOrganSign(resultPctDto.getOrganSign());
        resultPctVo.setPharmacyPref(resultPctDto.getPharmacyPref());
        resultPctVo.setPref(resultPctDto.getPref());
        resultPctVo.setProductName(resultPctDto.getProductName());
        resultPctVo.setRemark(resultPctDto.getRemark());
        resultPctVo.setUnitId(resultPctDto.getUnitId());
        resultPctVo.setUpdateTime(resultPctDto.getUpdateTime());
        resultPctVo.setUpdateUser(null);
        resultPctVo.setYn(resultPctDto.getYn());
        resultPctVo.setDosageFormId(resultPctDto.getDosageFormId());
        resultPctVo.setMedicalInsurance(resultPctDto.getMedicalInsurance());
        resultPctVo.setPrescriptionClassification(resultPctDto.getPrescriptionClassification());
        resultPctVo.setManufacturer(resultPctDto.getManufacturer());
        resultPctVo.setApprovalNumber(resultPctDto.getApprovalNumber());
        resultPctVo.setDrugPermissionPerson(resultPctDto.getDrugPermissionPerson());
        //进行配伍禁忌字表的赋值操作
        List<ComDetailsDto> comDetailsDtos = resultPctDto.getComDetailsDtos();
        List<ProductCompatibilityTabooListDetailVo> comDetailsVos = new ArrayList<>();
        for(ComDetailsDto cdd:comDetailsDtos){
            ProductCompatibilityTabooListDetailVo vo = new ProductCompatibilityTabooListDetailVo();
            vo.setAttributeSpecification(cdd.getAttributeSpecification());
            vo.setBaseVersion(cdd.getBaseVersion());
            vo.setCommonName(cdd.getCommonName());
            vo.setCreateTime(DateUtil.parseDateToStr(cdd.getCreateTime(),DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS));
            vo.setCreateUser(cdd.getCreateUser());
            if(resultPctVo.getCreateUser() == null){
                resultPctVo.setCreateUser(Integer.valueOf(cdd.getCreateUser()));
                resultPctVo.setUpdateUser(cdd.getCreateUser());
            }
            vo.setGuid(cdd.getGuid());
            vo.setId(cdd.getId());
            vo.setOrganSign(cdd.getOrganSign());
            vo.setPharmacyPref(cdd.getPharmacyPref());
            vo.setProductName(cdd.getProductName());
            vo.setSubPref(cdd.getSubPref());
            vo.setUnitId(cdd.getUnitId());
            vo.setUpdateTime(cdd.getUpdateTime());
            vo.setUpdateUser(cdd.getUpdateUser());
            vo.setYn(cdd.getYn());
            vo.setDosageFormId(cdd.getDosageFormId());
            vo.setManufacturer(cdd.getManufacturer());
            vo.setApprovalNumber(cdd.getApprovalNumber());
            vo.setMedicalInsurance(cdd.getMedicalInsurance());
            vo.setPrescriptionClassification(cdd.getPrescriptionClassification());
            vo.setDrugPermissionPerson(cdd.getDrugPermissionPerson());
            comDetailsVos.add(vo);
        }
        resultPctVo.setDetailDtos(comDetailsVos);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(resultPctVo), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> compatibityExportListExcel(HttpServletRequest request, @ApiParam(value = "导出配伍禁忌查询类" ,required=true )  @Valid @RequestBody ExportProductCompatibilityQueryVo exportVo) throws InterruptedException {
        logger.info("+++++++++++++++++++++配伍禁忌导出表格+++++++++++++++++++++++++++++++++");
        String organSign = request.getHeader("organSign");
        String headOrganSign = organSign;
        String employeeId = request.getHeader("employeeId");
        logger.info("+++++++++++++++++++++配伍禁忌导出表格+++++++++++++++++++++++++++++++++organ："+organSign);
        EmployeeDto employeeDto = employeeApi.queryEmployeeById(Integer.valueOf(employeeId)).getResult();
        String identity = employeeDto.getIdentity()+"";
        String createUserName = employeeDto.getName();
        logger.info("pw queryList params:mixquery:"+exportVo.getMixQueyStr()+",productType:"
                + exportVo.getProductType()+",systemType:"+exportVo.getSystemType()+",startTimeStr:"
                + exportVo.getStartTimeStr()+",endTimeStr:"+exportVo.getEndTimeStr()+",page:"
                + exportVo.getPage()+",rows:"+exportVo.getRows());
        Byte isProductHidden = Byte.valueOf(identity);
        PageInfo pageInfo = new PageInfo();
        if(exportVo.getPage() == null){
            pageInfo.setPageNum(1);
        }else{
            pageInfo.setPageNum(exportVo.getPage());
        }
        if(exportVo.getRows() == null){
            pageInfo.setPageSize(10);
        }else{
            pageInfo.setPageSize(exportVo.getRows());
        }
        //如果是连锁门店，需要获取总部的机构号
        String modelJson = request.getHeader("commonRequestModel");
        Byte bizModel = null;
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            bizModel = model.getBizModel();
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                headOrganSign = model.getHeadquartersOrganSign();
            }
        }
        //设置查询条件
        ProductCompatibilityQueryDto queryDto = new ProductCompatibilityQueryDto();
        queryDto.setOrganSign(organSign);
        queryDto.setIsProductHidden(isProductHidden);
        if(exportVo.getIsAll() == 2){
            queryDto.setPrefs(Arrays.asList(exportVo.getPrefs()));
        }else{
            queryDto.setCreateUser(exportVo.getCreateUser());
            queryDto.setMixQuery(exportVo.getMixQueyStr());
            queryDto.setSystemType(exportVo.getSystemType());
            queryDto.setProductType(exportVo.getProductType());
            queryDto.setStartTime(StringUtils.isEmpty(exportVo.getStartTimeStr())?null:exportVo.getStartTimeStr());
            queryDto.setEndTime(StringUtils.isEmpty(exportVo.getEndTimeStr())?null:exportVo.getEndTimeStr());
            queryDto.setPharmacyPref(exportVo.getPharmacyPref());//设置外码查询
        }
        queryDto.setHeadOrganSign(headOrganSign);
        //调用api返回结果集
        List<ProductCompDetailListDto> listDtos = productCompatibilityApi.findListAll(queryDto);
        List<Integer> ids = Arrays.asList(DictConstant.unitBussinessId, DictConstant.productSystemType);
        List<SystemDictDto> dictDtos = productDictionaryApi.findSystemDictDtoBybussinessIds(ids,organSign);
        Map<Integer,String> unitMap = new HashMap<>();
        Map<Integer,String> systemMap = new HashMap<>();
        for(SystemDictDto dto:dictDtos){
            logger.info("配伍导出：id:"+dto.getId()+",name:"+dto.getName()+",businessid:"+dto.getBussinessId());
            if(dto.getBussinessId().equals(DictConstant.unitBussinessId)){
                unitMap.put(dto.getId(),dto.getName());
            }
            if(dto.getBussinessId().equals(DictConstant.productSystemType)){
                systemMap.put(dto.getId(),dto.getName());
            }
        }
        //查全体员工（包括已离职及已禁用）
        EmployeefileRequestModel employeefileRequestModel = new EmployeefileRequestModel();
        employeefileRequestModel.setOrganSign(organSign);
        employeefileRequestModel.setPageNum(1);
        employeefileRequestModel.setPageSize(100);
        List<EmployeeDto> eList = employeefileApi.getEmployeefileByCondition(employeefileRequestModel).getResult().getList();
        Map<Integer, String> eMap = eList.stream().collect(Collectors.toMap(EmployeeDto::getId, p -> p.getName()));
        List<ExportProductCompatibilityVo> resultListDtos = new ArrayList<>();
        try{
            if(listDtos != null && listDtos.size() > 0){
                for(ProductCompDetailListDto resultPctDto:listDtos){
                    //对子表进行封装
                    List<ComDetailsDto> comDetailsDtos = resultPctDto.getComDetailsDtos();
                    List<ProductCompatibilityTabooListDetailVo> comDetailsVos = new ArrayList<>();
                    if(comDetailsDtos != null && comDetailsDtos.size() > 0){
                        int i = 1;
                        for(ComDetailsDto cdd:comDetailsDtos){
                            ExportProductCompatibilityVo resultPctVo = new ExportProductCompatibilityVo();
                            if(i == 1){
                                resultPctVo.setUnitId(unitMap.get(resultPctDto.getUnitId()));
                                logger.info("配伍导出主表：unitid:"+resultPctDto.getUnitId()+",name:"+unitMap.get(resultPctDto.getUnitId()));
                                resultPctVo.setAttributeSpecification(resultPctDto.getAttributeSpecification());
                                resultPctVo.setCommonName(resultPctDto.getCommonName());
                                resultPctVo.setPharmacyPref(resultPctDto.getPharmacyPref());
                                resultPctVo.setSystemType(systemMap.get(resultPctDto.getSystemType()));
                                resultPctVo.setDrugPermissionPerson(resultPctDto.getDrugPermissionPerson());
                            }else{
                                resultPctVo.setUnitId("");
                                resultPctVo.setAttributeSpecification("");
                                resultPctVo.setCommonName("");
                                resultPctVo.setPharmacyPref("");
                                resultPctVo.setSystemType("");
                                resultPctVo.setDrugPermissionPerson("");
                            }
                            resultPctVo.setSubattributeSpecification(cdd.getAttributeSpecification());
                            resultPctVo.setSubcommonName(cdd.getCommonName());
                            resultPctVo.setSubpharmacyPref(cdd.getPharmacyPref());
                            resultPctVo.setSubsystemType(systemMap.get(cdd.getSystemType()));
                            logger.info("配伍导出子表：unitid:"+cdd.getUnitId()+",name:"+unitMap.get(cdd.getUnitId()));
                            resultPctVo.setSubunitId(unitMap.get(cdd.getUnitId()));
                            resultPctVo.setCreateTime(DateUtil.parseDateToStr(cdd.getCreateTime(),DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS));
                            resultPctVo.setCreateUser(eMap.get(Integer.valueOf(cdd.getCreateUser())));
                            resultPctVo.setSubattributeSpecification(cdd.getAttributeSpecification());
                            resultPctVo.setSubdrugPermissionPerson(cdd.getDrugPermissionPerson());
                            resultListDtos.add(resultPctVo);
                            i++;
                        }
                    }
                }
            }
        }catch (Exception e){
            logger.error("pw queryList occur error:"+e);
            e.printStackTrace();
        }
        /*============================传ftp begin wht==============================*/
        String excelName=exportVo.getExcelName(); //文件名称
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
        String extfilename="";
        String uuid= ExportExcelUtil.getUUID();
        if(org.apache.commons.lang3.StringUtils.isNotBlank(excelName)){
            extfilename=excelName+".xls";
        }else{
            excelName="配伍禁忌报表"+df.format(new Date());
            extfilename="配伍禁忌报表"+df.format(new Date())+".xls";
        }
        String localPath = remotePath + "/" + uuid;
        String jsonStr = toJsonFromObj(exportVo);
        String extfilenames=extfilename;
        ExportExcelDto exportExcelDto=new ExportExcelDto();
        exportExcelDto.setSearchParamer(jsonStr);
        exportExcelDto.setExcelName(excelName);
        exportExcelDto.setFileName(uuid+"/"+extfilename);
        exportExcelDto.setCreateTime(new Date());
//        exportExcelDto.setName(eMap.get(Integer.valueOf(employee)));
        exportExcelDto.setName(createUserName);//前端必传
        exportExcelDto.setStatus((byte)0); //处理中
        exportExcelDto.setUserId(Integer.valueOf(employeeId));
        exportExcelDto.setMatchUrl("/baseinfo/compatibity/exportListExcel");
        exportExcelDto.setModuleId(10025); //父模块id
        exportExcelDto.setOperateModule("基础信息"); //父模块名称
        exportExcelDto.setSubModule("配伍禁忌"); //子模块名称
        exportExcelDto.setSubmoduleId(10190); //子模块id
        exportExcelDto.setUrl(downLoadUrl+localPath+"/"+extfilename);
        ResultVO<List<Integer>> listResultVO = roleUserApi.queryRoleIdsByEmployeeId(Integer.valueOf(employeeId));
        List<Integer> idList = listResultVO.getResult();
        String str = org.apache.commons.lang3.StringUtils.join(idList, ",");
        exportExcelDto.setRoleIdstr(str); //角色id字符串
        String headers[] = exportVo.getColNameDesc();
        String fieldNames[] = exportVo.getColName();
        String colNameDescs = org.apache.commons.lang3.StringUtils.join(exportVo.getColNameDesc(), ","); // 数组转字符串(逗号分隔)(推荐)
        String colNames = org.apache.commons.lang3.StringUtils.join(exportVo.getColName(), ","); // 数组转字符串(逗号分隔)(推荐)
        exportExcelDto.setColName(colNames);
        exportExcelDto.setColNameDesc(colNameDescs);
        exportExcelDto.setOrganSign(organSign);
        ExportExcelDto resultDto=exportExcelApi.saveExportExcel(exportExcelDto);
        /*============================传ftp end wht==============================*/
        String sheetName = "商品拆零信息维护报表";
        ExecServiceTask.execute(new Runnable(){
            @Override
            public void run() {
                try {
                    ExportExcelUtil.createExcelUpFtp(extfilenames,sheetName, headers, fieldNames, resultListDtos,true,serverName,port,username,password,localPath);
                    resultDto.setEndTime(new Date());
                    resultDto.setStatus((byte)1);
                    exportExcelApi.updateExportExcel(resultDto); //更新导出完成时间
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        });
        return new ResponseEntity(new ResultVO<>(resultDto) , HttpStatus.OK);
    }

    public static String toJsonFromObj(Object obj) {
        String outJson ="";
        try {
            ObjectMapper mapper = new ObjectMapper();
            mapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY );
            outJson = mapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return outJson;
    }

    @Override
    public ResponseEntity<ResultVO> getProductTypeList(HttpServletRequest request,@ApiParam(value = "当前登录机构码" ,required=true) @RequestHeader(value="organSign", required=true) String organSign,
                                                       @ApiParam(value = "当前登录员工的ID" ,required=true) @RequestHeader(value="employeeId", required=true) String employeeId,
                                                       @ApiParam(value = "请求字典类型" ,required=true )  @Valid @RequestBody SystemDictQueryVo systemDictQueryVo) {
        List<String> dictIds = new ArrayList<>();
        //如果是连锁门店，需要获取总部的机构号
        String modelJson = request.getHeader("commonRequestModel");
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) ){
                if(DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                    organSign = model.getHeadquartersOrganSign();
                }
                dictIds = systemDictApi.ignoreSystemDictIds();
            }
        }
        logger.info("getProductTypeList param:organSign:"+organSign);
        long start_time = System.currentTimeMillis();
        List<Integer> ids = Arrays.asList(DictConstant.unitBussinessId, DictConstant.agentBussinessId, DictConstant.commodtyTypeBussinessId, DictConstant.STORE_CONDITION_BUSINESS_ID,
                DictConstant.ABC_BUSSINESS_ID, DictConstant.prescriptionBussinessId, DictConstant.scopeOfOperation, DictConstant.maintenanceType, DictConstant.functionBussinessId,
                DictConstant.productSystemType,DictConstant.providerCompanyType,DictConstant.medicalInsuranceLevelBussinessId,DictConstant.incomeAndOuputTaxRateBussinessId,DictConstant.CosmeticsCategoryBussinessId);
        long start_time1 = System.currentTimeMillis();
        List<SystemDictDto> dictDtos = productDictionaryApi.findSystemDictDtoBybussinessIds(ids,organSign);
        List<ProductSystemDictVo> unitDict = new ArrayList<>();
        List<ProductSystemDictVo> proTypeDict = new ArrayList<>();
        List<ProductSystemDictVo> agentDict = new ArrayList<>();
        List<ProductSystemDictVo> cfDict = new ArrayList<>();
        List<ProductSystemDictVo> ptypeDict = new ArrayList<>();
        List<ProductSystemDictVo> yhDict = new ArrayList<>();
        List<ProductSystemDictVo> scopeDict = new ArrayList<>();
        List<ProductSystemDictVo> providers = new ArrayList<>();
        List<ProductSystemDictVo> abcDict = new ArrayList<>();
        List<ProductSystemDictVo> storeDict = new ArrayList<>();
        List<ProductSystemDictVo> comproDict = new ArrayList<>();
        List<ProductSystemDictVo> cflbDict = new ArrayList<>();
        List<ProductSystemDictVo> spuCategoryDict = new ArrayList<>();
        List<ProductSystemDictVo> shadingAttrDict = DictConstant.shadingAttrDictVos;
        List<ProductSystemDictVo> specialAttributesDict = new ArrayList<>();
        List<ProductSystemDictVo> manageAttrDict = new ArrayList<>();
        List<ProductSystemDictVo> managementAttrDict = new ArrayList<>();
        List<ProductSystemDictVo> medicalInsuranceLevelDict = new ArrayList<>();
        List<ProductSystemDictVo> incomeAndOuputTaxRateDict = new ArrayList<>();
        List<ProductSystemDictVo> cosmeticsCategoryDict = new ArrayList<>();

        if(dictDtos != null && dictDtos.size() > 0){
            for(SystemDictDto dto:dictDtos){
                if(dto.getBussinessId().equals(DictConstant.unitBussinessId)){
                    ProductSystemDictVo vo = new ProductSystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    if(dto.getStatus() == 0){
                        vo.setDisabled(true);
                    }else{
                        vo.setDisabled(false);
                    }
                    unitDict.add(vo);
                }
                if(dto.getBussinessId().equals(DictConstant.commodtyTypeBussinessId)){
                    ProductSystemDictVo vo = new ProductSystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    if(dto.getStatus() == 0){
                        vo.setDisabled(true);
                    }else{
                        vo.setDisabled(false);
                    }
                    proTypeDict.add(vo);
                }
                if(dto.getBussinessId().equals(DictConstant.agentBussinessId)){
                    ProductSystemDictVo vo = new ProductSystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    if(dto.getStatus() == 0){
                        vo.setDisabled(true);
                    }else{
                        vo.setDisabled(false);
                    }
                    agentDict.add(vo);
                }
                if(dto.getBussinessId().equals(DictConstant.prescriptionBussinessId)){
                    ProductSystemDictVo vo = new ProductSystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    if(dto.getStatus() == 0){
                        vo.setDisabled(true);
                    }else{
                        vo.setDisabled(false);
                    }
                    cfDict.add(vo);
                }
                if(dto.getBussinessId().equals(DictConstant.functionBussinessId)){
                    ProductSystemDictVo vo = new ProductSystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    if(dto.getStatus() == 0){
                        vo.setDisabled(true);
                    }else{
                        vo.setDisabled(false);
                    }
                    ptypeDict.add(vo);
                }
                if(dto.getBussinessId().equals(DictConstant.maintenanceType)){
                    ProductSystemDictVo vo = new ProductSystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    if(dto.getStatus() == 0){
                        vo.setDisabled(true);
                    }else{
                        vo.setDisabled(false);
                    }
                    yhDict.add(vo);
                }
                if(dto.getBussinessId().equals(DictConstant.scopeOfOperation)){
                    ProductSystemDictVo vo = new ProductSystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    if(dto.getStatus() == 0){
                        vo.setDisabled(true);
                    }else{
                        vo.setDisabled(false);
                    }
                    if (!CollectionUtils.isEmpty(dictIds)) {
                        if (!dictIds.contains(String.valueOf(dto.getId()))) {
                            scopeDict.add(vo);
                        }
                    }else {
                        scopeDict.add(vo);
                    }
                }
                if(dto.getBussinessId().equals(DictConstant.providerCompanyType)){
                    ProductSystemDictVo vo = new ProductSystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    if(dto.getStatus() == 0){
                        vo.setDisabled(true);
                    }else{
                        vo.setDisabled(false);
                    }
                    providers.add(vo);
                }
                if(dto.getBussinessId().equals(DictConstant.ABC_BUSSINESS_ID)){
                    ProductSystemDictVo vo = new ProductSystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    if(dto.getStatus() == 0){
                        vo.setDisabled(true);
                    }else{
                        vo.setDisabled(false);
                    }
                    abcDict.add(vo);
                }
                if(dto.getBussinessId().equals(DictConstant.STORE_CONDITION_BUSINESS_ID)){
                    ProductSystemDictVo vo = new ProductSystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    if(dto.getStatus() == 0){
                        vo.setDisabled(true);
                    }else{
                        vo.setDisabled(false);
                    }
                    storeDict.add(vo);
                }
                if(dto.getBussinessId().equals(DictConstant.productSystemType)){
                    ProductSystemDictVo vo = new ProductSystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    if(dto.getStatus() == 0){
                        vo.setDisabled(true);
                    }else{
                        vo.setDisabled(false);
                    }
                    comproDict.add(vo);
                    Collections.sort(comproDict, new Comparator<ProductSystemDictVo>() {
                        @Override
                        public int compare(ProductSystemDictVo vo1, ProductSystemDictVo vo2) {
                            if(vo1.getSort() < vo2.getSort()){
                                return -1;
                            }else if (vo1.getSort() == vo2.getSort()){
                                return 0;
                            }
                            return 1;
                        }
                    });
                }
                if(dto.getBussinessId().equals(DictConstant.medicalInsuranceLevelBussinessId)){
                    ProductSystemDictVo vo = new ProductSystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    if(dto.getStatus() == 0){
                        vo.setDisabled(true);
                    }else{
                        vo.setDisabled(false);
                    }
                    medicalInsuranceLevelDict.add(vo);
                }
                if(dto.getBussinessId().equals(DictConstant.incomeAndOuputTaxRateBussinessId)){
                    ProductSystemDictVo vo = new ProductSystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    if(dto.getStatus() == 0){
                        vo.setDisabled(true);
                    }else{
                        vo.setDisabled(false);
                    }
                    incomeAndOuputTaxRateDict.add(vo);
                }
                if(dto.getBussinessId().equals(DictConstant.CosmeticsCategoryBussinessId)){
                    ProductSystemDictVo vo = new ProductSystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    if(dto.getStatus() == 0){
                        vo.setDisabled(true);
                    }else{
                        vo.setDisabled(false);
                    }
                    cosmeticsCategoryDict.add(vo);
                }
            }
        }
        logger.info("getProductTypeList systemDictApi.findSystemDictDtoBybussinessIds need time:"+(System.currentTimeMillis() - start_time1)+" ms");

        // 是否展示处方类别列
        boolean showPrescriptionYnColumn = true;
        //手动写处方类别 单轨和双轨
        if (!isDoubleTrackHidden(organSign)) {
            ProductSystemDictVo doubleTrack = new ProductSystemDictVo();
            doubleTrack.setId(0);
            doubleTrack.setName("双轨处方药");
            cflbDict.add(doubleTrack);
        } else {
            showPrescriptionYnColumn = false;
        }
        ProductSystemDictVo singleTrack = new ProductSystemDictVo();
        singleTrack.setId(1);
        singleTrack.setName("单轨处方药");
        cflbDict.add(singleTrack);

        //商品大类和存储属性
        ProductSystemDictVo spuCategory1 = new ProductSystemDictVo();
        spuCategory1.setId(1);
        spuCategory1.setName("普通药品");
        ProductSystemDictVo spuCategory2 = new ProductSystemDictVo();
        spuCategory2.setId(2);
        spuCategory2.setName("中药");
        ProductSystemDictVo spuCategory3 = new ProductSystemDictVo();
        spuCategory3.setId(3);
        spuCategory3.setName("医疗器械");
        ProductSystemDictVo spuCategory4 = new ProductSystemDictVo();
        spuCategory4.setId(4);
        spuCategory4.setName("其他");
        ProductSystemDictVo spuCategory5 = new ProductSystemDictVo();
        spuCategory5.setId(5);
        spuCategory5.setName("非药");
        ProductSystemDictVo spuCategory6 = new ProductSystemDictVo();
        spuCategory6.setId(6);
        spuCategory6.setName("赠品");
        spuCategoryDict.add(spuCategory1);
        spuCategoryDict.add(spuCategory2);
        spuCategoryDict.add(spuCategory3);
        spuCategoryDict.add(spuCategory4);
        spuCategoryDict.add(spuCategory5);
        spuCategoryDict.add(spuCategory6);


        //特殊属性
        ProductSystemDictVo specialAttributes1 = new ProductSystemDictVo();
        specialAttributes1.setId(1);
        specialAttributes1.setName("抗肿瘤治疗药");
        ProductSystemDictVo specialAttributes2 = new ProductSystemDictVo();
        specialAttributes2.setId(2);
        specialAttributes2.setName("限制使用抗菌药 ");
        ProductSystemDictVo specialAttributes3 = new ProductSystemDictVo();
        specialAttributes3.setId(3);
        specialAttributes3.setName("特殊使用抗菌药");
        ProductSystemDictVo specialAttributes4 = new ProductSystemDictVo();
        specialAttributes4.setId(4);
        specialAttributes4.setName("含特殊药品复方制剂");
        ProductSystemDictVo specialAttributes5 = new ProductSystemDictVo();
        specialAttributes5.setId(5);
        specialAttributes5.setName("β-内酰胺类注射剂");
        ProductSystemDictVo specialAttributes6 = new ProductSystemDictVo();
        specialAttributes6.setId(6);
        specialAttributes6.setName("抗生素类处方药");
        specialAttributesDict.add(specialAttributes1);
        specialAttributesDict.add(specialAttributes2);
        specialAttributesDict.add(specialAttributes3);
        specialAttributesDict.add(specialAttributes4);
        specialAttributesDict.add(specialAttributes5);
        specialAttributesDict.add(specialAttributes6);
        //管理属性
        ProductSystemDictVo manageAttr1 = new ProductSystemDictVo();
        manageAttr1.setId(1);
        manageAttr1.setName("自有品牌");
        ProductSystemDictVo manageAttr2 = new ProductSystemDictVo();
        manageAttr2.setId(2);
        manageAttr2.setName("战略品种");
        ProductSystemDictVo manageAttr3 = new ProductSystemDictVo();
        manageAttr3.setId(3);
        manageAttr3.setName("统采毛利");
        ProductSystemDictVo manageAttr4 = new ProductSystemDictVo();
        manageAttr4.setId(4);
        manageAttr4.setName("统采品牌");
        ProductSystemDictVo manageAttr5 = new ProductSystemDictVo();
        manageAttr5.setId(5);
        manageAttr5.setName("地采补充");
        ProductSystemDictVo manageAttr6 = new ProductSystemDictVo();
        manageAttr6.setId(6);
        manageAttr6.setName("地采品牌");
        ProductSystemDictVo manageAttr7 = new ProductSystemDictVo();
        manageAttr7.setId(7);
        manageAttr7.setName("地采毛利");
        manageAttrDict.add(manageAttr1);
        manageAttrDict.add(manageAttr2);
        manageAttrDict.add(manageAttr3);
        manageAttrDict.add(manageAttr4);
        manageAttrDict.add(manageAttr5);
        manageAttrDict.add(manageAttr6);
        manageAttrDict.add(manageAttr7);
        //经营属性
        ProductSystemDictVo managementAttr1 = new ProductSystemDictVo();
        managementAttr1.setId(1);
        managementAttr1.setName("S");
        ProductSystemDictVo managementAttr2 = new ProductSystemDictVo();
        managementAttr2.setId(2);
        managementAttr2.setName("A");
        ProductSystemDictVo managementAttr3 = new ProductSystemDictVo();
        managementAttr3.setId(3);
        managementAttr3.setName("B");
        ProductSystemDictVo managementAttr4 = new ProductSystemDictVo();
        managementAttr4.setId(4);
        managementAttr4.setName("C");
        ProductSystemDictVo managementAttr5 = new ProductSystemDictVo();
        managementAttr5.setId(5);
        managementAttr5.setName("D");
        managementAttrDict.add(managementAttr1);
        managementAttrDict.add(managementAttr2);
        managementAttrDict.add(managementAttr3);
        managementAttrDict.add(managementAttr4);
        managementAttrDict.add(managementAttr5);

        // 查询字典表
        Map<String, Object> map = new HashMap<>();
        // 针对智鹿 - 返回标识(置灰医保相关字段)
        map.put("medicalInfoReadonly", isMedicalInfoReadonly(organSign));

        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 10004){
            // 单位
//            List<SystemDictDto> unitDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.unitBussinessId)).collect(Collectors.toList());
            map.put("unitDict", unitDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 10002){
            // 商品自定义分类
//            List<Integer> ads = Arrays.asList(DictConstant.commodtyTypeBussinessId);
//            List<SystemDictDto> proTypeDict = systemDictApi.findSystemDictDtoBybussinessIds(ads,organSign);
//            List<SystemDictDto> proTypeDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.commodtyTypeBussinessId)).collect(Collectors.toList());
            map.put("proTypeDict", proTypeDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 10003){
            // 剂型
//            List<SystemDictDto> agentDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.agentBussinessId)).collect(Collectors.toList());
            map.put("agentDict", agentDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 20013){
            // 处方分类
//            List<SystemDictDto> cfDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.prescriptionBussinessId)).collect(Collectors.toList());
            map.put("cfDict", cfDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 20020){
            // 商品功能分类
//            List<SystemDictDto> ptypeDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.functionBussinessId)).collect(Collectors.toList());
            map.put("ptypeDict", ptypeDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 20082){
            // 养护类型
//            List<SystemDictDto> yhDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.maintenanceType)).collect(Collectors.toList());
            map.put("yhDict", yhDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 10005){
            // 经营范围
//            List<SystemDictDto> scopeDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.scopeOfOperation)).collect(Collectors.toList());
            map.put("scopeDict", scopeDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 20048){//供应商类别
//            List<SystemDictDto> providers = this.systemDictApi.findSystemDictDto(DictConstant.providerCompanyType,organSign);
//            List<SystemDictDto> providers = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.providerCompanyType)).collect(Collectors.toList());
            map.put("providers", providers);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 20001){
            // ABC分类
//            List<SystemDictDto> abcDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.ABC_BUSSINESS_ID)).collect(Collectors.toList());
            map.put("abcDict", abcDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 20042){//存储条件
            // 存储条件
//            List<SystemDictDto> storeDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.STORE_CONDITION_BUSINESS_ID)).collect(Collectors.toList());
            map.put("storeDict", storeDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 10101){
            //商品系统类型
//            List<SystemDictDto> comproDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.productSystemType)).collect(Collectors.toList());
            map.put("comproDict",comproDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 50012){
            //进销项税率
            map.put("incomeAndOuputTaxRateDict",incomeAndOuputTaxRateDict);
        }
        map.put("cflbDict",cflbDict);
        // 是否展示处方类别列
        map.put("showPrescriptionYnColumn",showPrescriptionYnColumn ? 1 : 0);
        map.put("spuCategoryDict", spuCategoryDict);
        map.put("shadingAttrDict", shadingAttrDict);
        map.put("specialAttributesDict", specialAttributesDict);
        map.put("manageAttrDict",manageAttrDict);
        map.put("managementAttrDict",managementAttrDict);
        map.put("medicalInsuranceLevelDict", medicalInsuranceLevelDict);
        map.put("cosmeticsCategoryDict", cosmeticsCategoryDict);

        logger.info("getProductTypeList common api need time:"+(System.currentTimeMillis() - start_time)+"ms");
        long start_time2 = System.currentTimeMillis();
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 10000){
            //获取商品架位：
            SaasPositionVo sp = new SaasPositionVo();
            sp.setPositionStatus(1);
            sp.setOrgansign(organSign);
            IRemoteBean<List<SaasPositionVo>> iRemoteBean = poSitionApi.getPositionList(sp);
            List<SaasPositionVo> positionList = new ArrayList<>();
            if (iRemoteBean.isSuccess()) {
                positionList = iRemoteBean.getContent();
            }
            List<ProductSystemDictVo> positionJonList = new ArrayList<>();
            List<ProductSystemDictVo> zYpositionJonList = new ArrayList<>();
            if(positionList != null && positionList.size() > 0){
                for(SaasPositionVo vo:positionList){
                    if(vo.getPositionType() == 2){
                        ProductSystemDictVo systemDictDto = new ProductSystemDictVo();
                        systemDictDto.setId(vo.getId());
                        systemDictDto.setName(vo.getName());
                        systemDictDto.setDisabled(false);
                        zYpositionJonList.add(systemDictDto);
                    }
                    ProductSystemDictVo systemDictDto = new ProductSystemDictVo();
                    systemDictDto.setId(vo.getId());
                    systemDictDto.setName(vo.getName());
                    systemDictDto.setDisabled(false);
                    positionJonList.add(systemDictDto);
                }
            }
            map.put("positionList", positionJonList);//架位id，架位名称
            map.put("zYpositionJonList", zYpositionJonList);//架位id，架位名称
        }else if(systemDictQueryVo.getType() == 30000){
            //获取商品架位：
            SaasPositionVo sp = new SaasPositionVo();
            sp.setOrgansign(organSign);
            sp.setPositionType(2);
            sp.setPositionStatus(1);
            IRemoteBean<List<SaasPositionVo>> iRemoteBean = poSitionApi.getPositionList(sp);
            List<SaasPositionVo> positionList = new ArrayList<>();
            if (iRemoteBean.isSuccess()) {
                positionList = iRemoteBean.getContent();
            }
            List<ProductSystemDictVo> positionJonList = new ArrayList<>();
            if(positionList != null && positionList.size() > 0){
                for(SaasPositionVo vo:positionList){
                    ProductSystemDictVo systemDictDto = new ProductSystemDictVo();
                    systemDictDto.setId(vo.getId());
                    systemDictDto.setName(vo.getName());
                    systemDictDto.setDisabled(false);
                    positionJonList.add(systemDictDto);
                }
            }
            map.put("positionList", positionJonList);//架位id，架位名称
        }
        logger.info("getProductTypeList position api need time:"+(System.currentTimeMillis() - start_time2)+"ms");
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(map), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getProductAllTypeList(@ApiParam(value = "当前登录机构码" ,required=true) @RequestHeader(value="organSign", required=true) String organSign,
                                                          @ApiParam(value = "当前登录员工的ID" ,required=true) @RequestHeader(value="employeeId", required=true) String employeeId,
                                                          @ApiParam(value = "请求字典类型" ,required=true )  @Valid @RequestBody SystemDictQueryVo systemDictQueryVo) {
        logger.info("getProductTypeList param:organSign:"+organSign);
        long start_time = System.currentTimeMillis();
        List<Integer> ids = Arrays.asList(DictConstant.unitBussinessId, DictConstant.agentBussinessId, DictConstant.commodtyTypeBussinessId, DictConstant.STORE_CONDITION_BUSINESS_ID,
                DictConstant.ABC_BUSSINESS_ID, DictConstant.prescriptionBussinessId, DictConstant.scopeOfOperation, DictConstant.maintenanceType, DictConstant.functionBussinessId,
                DictConstant.productSystemType,DictConstant.providerCompanyType,DictConstant.incomeAndOuputTaxRateBussinessId);
        long start_time1 = System.currentTimeMillis();
        List<SystemDictDto> dictDtos = productDictionaryApi.findSystemDictDtoBybussinessIds(ids,organSign);
        logger.info("getProductTypeList systemDictApi.findSystemDictDtoBybussinessIds need time:"+(System.currentTimeMillis() - start_time1)+" ms");
        // 查询字典表
        Map<String, Object> map = new HashMap<>();
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 10004){
            // 单位
            List<SystemDictDto> unitDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.unitBussinessId)).collect(Collectors.toList());
            map.put("unitDict", unitDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 10002){
            // 商品自定义分类
//            List<Integer> ads = Arrays.asList(DictConstant.commodtyTypeBussinessId);
//            List<SystemDictDto> proTypeDict = systemDictApi.findSystemDictDtoBybussinessIds(ads,organSign);
            List<SystemDictDto> proTypeDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.commodtyTypeBussinessId)).collect(Collectors.toList());
            map.put("proTypeDict", proTypeDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 10003){
            // 剂型
            List<SystemDictDto> agentDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.agentBussinessId)).collect(Collectors.toList());
            map.put("agentDict", agentDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 20013){
            // 处方分类
            List<SystemDictDto> cfDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.prescriptionBussinessId)).collect(Collectors.toList());
            map.put("cfDict", cfDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 20020){
            // 商品功能分类
            List<SystemDictDto> ptypeDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.functionBussinessId)).collect(Collectors.toList());
            map.put("ptypeDict", ptypeDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 20082){
            // 养护类型
            List<SystemDictDto> yhDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.maintenanceType)).collect(Collectors.toList());
            map.put("yhDict", yhDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 10005){
            // 经营范围
            List<SystemDictDto> scopeDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.scopeOfOperation)).collect(Collectors.toList());
            map.put("scopeDict", scopeDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 20048){//供应商类别
//            List<SystemDictDto> providers = this.systemDictApi.findSystemDictDto(DictConstant.providerCompanyType,organSign);
            List<SystemDictDto> providers = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.providerCompanyType)).collect(Collectors.toList());
            map.put("providers", providers);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 20001){
            // ABC分类
            List<SystemDictDto> abcDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.ABC_BUSSINESS_ID)).collect(Collectors.toList());
            map.put("abcDict", abcDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 20042){//存储条件
            // 存储条件
            List<SystemDictDto> storeDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.STORE_CONDITION_BUSINESS_ID)).collect(Collectors.toList());
            map.put("storeDict", storeDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 10101){
            //商品系统类型
            List<SystemDictDto> comproDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.productSystemType)).collect(Collectors.toList());
            map.put("comproDict",comproDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 30001){
            //商品价签模板类型
            List<SystemDictDto> comproDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.productPriceLabelType)).collect(Collectors.toList());
            map.put("priceLabelTypeDict",comproDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 30002){
            //商品价签模板类型
            List<SystemDictDto> comproDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.productPriceLabelContentType)).collect(Collectors.toList());
            map.put("priceLabelContentTypeDict",comproDict);
        }
        if (systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 50012){
            List<SystemDictDto> incomeAndOuputTaxRateDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.incomeAndOuputTaxRateBussinessId)).collect(Collectors.toList());
            map.put("incomeAndOuputTaxRateDict",incomeAndOuputTaxRateDict);
        }
        logger.info("getProductTypeList common api need time:"+(System.currentTimeMillis() - start_time)+"ms");
        long start_time2 = System.currentTimeMillis();
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 10000){
            //获取商品架位：
            SaasPositionVo sp = new SaasPositionVo();
            sp.setPositionStatus(1);
            sp.setOrgansign(organSign);
            IRemoteBean<List<SaasPositionVo>> iRemoteBean = poSitionApi.getPositionList(sp);
            List<SaasPositionVo> positionList = new ArrayList<>();
            if (iRemoteBean.isSuccess()) {
                positionList = iRemoteBean.getContent();
            }
            List<SystemDictDto> positionJonList = new ArrayList<>();
            List<SystemDictDto> zYpositionJonList = new ArrayList<>();
            if(positionList != null && positionList.size() > 0){
                for(SaasPositionVo vo:positionList){
                    if(vo.getPositionType() == 2){
                        SystemDictDto systemDictDto = new SystemDictDto();
                        systemDictDto.setId(vo.getId());
                        systemDictDto.setName(vo.getName());
                        zYpositionJonList.add(systemDictDto);
                    }
                    SystemDictDto systemDictDto = new SystemDictDto();
                    systemDictDto.setId(vo.getId());
                    systemDictDto.setName(vo.getName());
                    positionJonList.add(systemDictDto);
                }
            }
            map.put("positionList", positionJonList);//架位id，架位名称
            map.put("zYpositionJonList", zYpositionJonList);//架位id，架位名称
        }else if(systemDictQueryVo.getType() == 30000){
            //获取商品架位：
            SaasPositionVo sp = new SaasPositionVo();
            sp.setOrgansign(organSign);
            sp.setPositionType(2);
            sp.setPositionStatus(1);
            IRemoteBean<List<SaasPositionVo>> iRemoteBean = poSitionApi.getPositionList(sp);
            List<SaasPositionVo> positionList = new ArrayList<>();
            if (iRemoteBean.isSuccess()) {
                positionList = iRemoteBean.getContent();
            }
            List<SystemDictDto> positionJonList = new ArrayList<>();
            if(positionList != null && positionList.size() > 0){
                for(SaasPositionVo vo:positionList){
                    SystemDictDto systemDictDto = new SystemDictDto();
                    systemDictDto.setId(vo.getId());
                    systemDictDto.setName(vo.getName());
                    positionJonList.add(systemDictDto);
                }
            }
            map.put("positionList", positionJonList);//架位id，架位名称
        }
        logger.info("getProductTypeList position api need time:"+(System.currentTimeMillis() - start_time2)+"ms");
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(map), HttpStatus.OK);
    }

    private void pushProductMessToMQ(String organSign) {
        JSONObject json = new JSONObject();
        String[] tables = {"saas_incompatibility"};
        json.put("code", "sync");
        json.put("tables", tables);

        messagePushApi.sendMsgByOrganSign(organSign, json.toJSONString());
    }
}
