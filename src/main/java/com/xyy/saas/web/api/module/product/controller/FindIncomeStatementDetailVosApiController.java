package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.web.api.module.product.model.InventoryIncomeStatementDetailVo;
import com.xyy.saas.web.api.module.product.model.InventoryIncomeStatementDetailVoList;
import io.swagger.annotations.ApiParam;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-30T11:21:29.525+08:00")

@Controller
public class FindIncomeStatementDetailVosApiController implements FindIncomeStatementDetailVosApi {



    public ResponseEntity<InventoryIncomeStatementDetailVoList> findIncomeStatementDetailVos(@ApiParam(value = "报损报溢明细Vo" ,required=true )  @Valid @RequestBody InventoryIncomeStatementDetailVo inventoryIncomeStatementDetailVo) {
        // do some magic!
        return new ResponseEntity<InventoryIncomeStatementDetailVoList>(HttpStatus.OK);
    }

}
