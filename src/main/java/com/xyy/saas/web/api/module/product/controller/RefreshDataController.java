package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.api.RefreshDataApi;
import com.xyy.saas.web.api.module.product.model.RefreshDataParamVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Api(tags = "RefreshDataController", description = "刷数据")
@RestController
@RequestMapping("/product/refreshData")
public class RefreshDataController {

    @Reference(version = "0.0.2")
    private RefreshDataApi refreshDataApi;

    @ApiOperation("同步标准库本位码")
    @PostMapping(value = "/standardLibStandardCode")
    public ResultVO<String> standardLibStandardCode(@RequestBody RefreshDataParamVO paramVo) {
        log.info("standardLibStandardCode | param={}", JSON.toJSONString(paramVo));
        if(!"product123456".equals(paramVo.getAuthCode())) {
            log.error("standardLibStandardCode | authCode error：{}", paramVo.getAuthCode());
            return new ResultVO<>(ResultCodeEnum.ERROR, "验证码错误", "");
        }
        for (String organSign : paramVo.getOrganSignList()) {
            try {
                refreshDataApi.refreshStandardLibStandardCode(organSign);
            } catch (Exception e) {
                log.error("standardLibStandardCode | organSign={}, error", organSign, e);
            }
        }
        return ResultVO.createSuccess("同步标准库本位码完成");
    }
}
