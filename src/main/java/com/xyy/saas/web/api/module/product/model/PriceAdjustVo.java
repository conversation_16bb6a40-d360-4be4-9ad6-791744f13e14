package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 商品售价调整信息实体
 */
@ApiModel(description = "商品售价调整信息实体")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

public class PriceAdjustVo extends Page implements Serializable {

  private Long id;//传值代表是编辑，不传代表是新增
  @JsonProperty("pref")
  private String pref = null;

  @JsonProperty("starttime")
  private String starttime = null;

  @JsonProperty("endtime")
  private String endtime = null;

  @JsonProperty("username")
  private String username = null;

  @JsonProperty("proname")
  private String proname = null;

  @JsonProperty("adjustId")
  private Long adjustId = null;

  @JsonProperty("remark")
  private String remark = null;

  @JsonProperty("userId")
  private Integer userId = null;

  @JsonProperty("isProductHidden")
  private Byte isProductHidden = null;

  @JsonProperty("adjustReason")
  private String adjustReason = null;

  @JsonProperty("applicableStores")
  private String applicableStores = null;

  @JsonProperty("details")
  private List<PriceAdjustDetailPo> details = null;

  public Byte getUpdateBaseFlag() {
    return updateBaseFlag;
  }

  public void setUpdateBaseFlag(Byte updateBaseFlag) {
    this.updateBaseFlag = updateBaseFlag;
  }

  @JsonProperty("updateBaseFlag")
  private Byte updateBaseFlag;//是否更新基础信息中的价格，1更新，0不更新

  public PriceAdjustVo pref(String pref) {
    this.pref = pref;
    return this;
  }

   /**
   * 售价调整单主表编号
   * @return pref
  **/
  @ApiModelProperty(value = "售价调整单主表编号")


  public String getPref() {
    return pref;
  }


  public String getAdjustReason() {
    return adjustReason;
  }

  public void setAdjustReason(String adjustReason) {
    this.adjustReason = adjustReason;
  }


  public void setPref(String pref) {
    this.pref = pref;
  }

  public PriceAdjustVo starttime(String starttime) {
    this.starttime = starttime;
    return this;
  }

   /**
   * 查询开始时间
   * @return starttime
  **/
  @ApiModelProperty(value = "查询开始时间")


  public String getStarttime() {
    return starttime;
  }

  public void setStarttime(String starttime) {
    this.starttime = starttime;
  }

  public PriceAdjustVo endtime(String endtime) {
    this.endtime = endtime;
    return this;
  }

   /**
   * 查询结束时间
   * @return endtime
  **/
  @ApiModelProperty(value = "查询结束时间")


  public String getEndtime() {
    return endtime;
  }

  public void setEndtime(String endtime) {
    this.endtime = endtime;
  }

  public PriceAdjustVo username(String username) {
    this.username = username;
    return this;
  }

   /**
   * 用户名
   * @return username
  **/
  @ApiModelProperty(value = "用户名")


  public String getUsername() {
    return username;
  }

  public void setUsername(String username) {
    this.username = username;
  }

  public PriceAdjustVo proname(String proname) {
    this.proname = proname;
    return this;
  }

   /**
   * 商品名称
   * @return proname
  **/
  @ApiModelProperty(value = "商品名称")


  public String getProname() {
    return proname;
  }

  public void setProname(String proname) {
    this.proname = proname;
  }

  public PriceAdjustVo adjustId(Long adjustId) {
    this.adjustId = adjustId;
    return this;
  }

   /**
   * 售价调整单主表ID
   * @return adjustId
  **/
  @ApiModelProperty(value = "售价调整单主表ID")


  public Long getAdjustId() {
    return adjustId;
  }

  public void setAdjustId(Long adjustId) {
    this.adjustId = adjustId;
  }

  public PriceAdjustVo remark(String remark) {
    this.remark = remark;
    return this;
  }

   /**
   * 备注
   * @return remark
  **/
  @ApiModelProperty(value = "备注")


  public String getRemark() {
    return remark;
  }

  public void setRemark(String remark) {
    this.remark = remark;
  }

  public PriceAdjustVo userId(Integer userId) {
    this.userId = userId;
    return this;
  }

   /**
   * 用户ID
   * @return userId
  **/
  @ApiModelProperty(value = "用户ID")


  public Integer getUserId() {
    return userId;
  }

  public void setUserId(Integer userId) {
    this.userId = userId;
  }

  public PriceAdjustVo isProductHidden(Byte isProductHidden) {
    this.isProductHidden = isProductHidden;
    return this;
  }

   /**
   * 隐藏标志
   * @return isProductHidden
  **/
  @ApiModelProperty(value = "隐藏标志")


  public Byte getIsProductHidden() {
    return isProductHidden;
  }

  public void setIsProductHidden(Byte isProductHidden) {
    this.isProductHidden = isProductHidden;
  }

  public PriceAdjustVo details(List<PriceAdjustDetailPo> details) {
    this.details = details;
    return this;
  }

  public PriceAdjustVo addDetailsItem(PriceAdjustDetailPo detailsItem) {
    if (this.details == null) {
      this.details = new ArrayList<>();
    }
    this.details.add(detailsItem);
    return this;
  }

   /**
   * 售价调整单明细集合
   * @return details
  **/
  @ApiModelProperty(value = "售价调整单明细集合")

  @Valid

  public List<PriceAdjustDetailPo> getDetails() {
    return details;
  }

  public void setDetails(List<PriceAdjustDetailPo> details) {
    this.details = details;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PriceAdjustVo priceAdjustVo = (PriceAdjustVo) o;
    return Objects.equals(this.pref, priceAdjustVo.pref) &&
        Objects.equals(this.starttime, priceAdjustVo.starttime) &&
        Objects.equals(this.endtime, priceAdjustVo.endtime) &&
        Objects.equals(this.username, priceAdjustVo.username) &&
        Objects.equals(this.proname, priceAdjustVo.proname) &&
        Objects.equals(this.adjustId, priceAdjustVo.adjustId) &&
        Objects.equals(this.remark, priceAdjustVo.remark) &&
        Objects.equals(this.userId, priceAdjustVo.userId) &&
        Objects.equals(this.isProductHidden, priceAdjustVo.isProductHidden) &&
        Objects.equals(this.details, priceAdjustVo.details);
  }

  @Override
  public int hashCode() {
    return Objects.hash(pref, starttime, endtime, username, proname, adjustId, remark, userId, isProductHidden, details);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PriceAdjustVo {\n");

    sb.append("    pref: ").append(toIndentedString(pref)).append("\n");
    sb.append("    starttime: ").append(toIndentedString(starttime)).append("\n");
    sb.append("    endtime: ").append(toIndentedString(endtime)).append("\n");
    sb.append("    username: ").append(toIndentedString(username)).append("\n");
    sb.append("    proname: ").append(toIndentedString(proname)).append("\n");
    sb.append("    adjustId: ").append(toIndentedString(adjustId)).append("\n");
    sb.append("    remark: ").append(toIndentedString(remark)).append("\n");
    sb.append("    userId: ").append(toIndentedString(userId)).append("\n");
    sb.append("    isProductHidden: ").append(toIndentedString(isProductHidden)).append("\n");
    sb.append("    details: ").append(toIndentedString(details)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getApplicableStores() {
    return applicableStores;
  }

  public void setApplicableStores(String applicableStores) {
    this.applicableStores = applicableStores;
  }
}

