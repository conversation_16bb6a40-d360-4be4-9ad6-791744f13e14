package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.member.core.api.MemberAssetsApi;
import com.xyy.saas.member.core.dto.MemberAssetsBaseDto;
import com.xyy.saas.member.core.dto.MemberAssetsPayDto;
import com.xyy.saas.member.core.dto.MemberAssetsReturnDto;
import com.xyy.saas.member.core.dto.MemberAssetsTokenDto;
import com.xyy.saas.member.core.response.ResultCodeEnum;
import com.xyy.saas.member.core.response.ResultVO;
import com.xyy.saas.promotion.core.dto.CouponDto;
import com.xyy.saas.web.api.module.member.model.MemberAssetsDataVo;
import com.xyy.saas.web.api.module.member.model.MemberAssetsEncryptDataVo;
import com.xyy.saas.web.api.module.member.model.MemberAssetsPayStatusDataVo;
import com.xyy.saas.web.api.module.utils.JedisUtils;
import com.xyy.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.xpath.operations.Bool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.math.BigDecimal;
import java.util.List;

/**
 * 会员资产
 */
@Controller
@RequestMapping(value = "/member/membeAssets")
@Api(value = "membeAssets", description = "会员资产门店端API")
public class MemberAssetsApiController {

    private static final Logger logger = LogManager.getLogger(MemberAssetsApiController.class);

    @Reference(version = "0.0.1")
    private MemberAssetsApi memberAssetsApi;
    @Reference(version = "0.0.1")
    private com.xyy.saas.promotion.core.api.CouponApi couponApi;
    @Autowired
    private JedisUtils jedisUtils;

    @ApiOperation(value = "会员资产支付", notes = "会员资产支付 \n" +
            "加密步骤:  \n" +
            "1.对pay接口的请求参数json格式化为字符串  \n" +
            "2.使用AES的CBC模式 PKCS5Padding填充方式加密上述字符串  \n" +
            "3.使用BASE64进行编码", response = Boolean.class, tags = {"会员资产",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/payData", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> payData(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                   @RequestBody MemberAssetsEncryptDataVo encryptDataVo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        if(null == encryptDataVo){
            return new ResponseEntity(ResultVO.create(ResultCodeEnum.PREPAY_CARD_NO_DECRYPT_DATA, false), HttpStatus.OK);
        }
        if (commonRequestModel==null || StringUtils.isEmpty(commonRequestModel.getOrganSign())) {
            return new ResponseEntity(ResultVO.create(ResultCodeEnum.PREPAY_CARD_NO_ORGANSIGN, false), HttpStatus.OK);
        }
        //参数校验
        String encryptData = encryptDataVo.getData();
        if (StringUtils.isEmpty(encryptData)) {
            return new ResponseEntity(ResultVO.create(ResultCodeEnum.PREPAY_CARD_NO_DECRYPT_DATA, false), HttpStatus.OK);
        }
        ResultVO<Boolean> validateResult = dealAssetsPayData(encryptDataVo,commonRequestModel);
        return new ResponseEntity(validateResult, HttpStatus.OK);
    }

    @ApiOperation(value = "会员资产退款（加密传输）", notes = "会员资产退款（加密传输）\n" +
            "加密步骤:  \n" +
            "1.对refund接口的请求参数json格式化为字符串  \n" +
            "2.使用AES的CBC模式 PKCS5Padding填充方式加密上述字符串  \n" +
            "3.使用BASE64进行编码", response = Boolean.class, tags = {"会员资产",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/returnData", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> returnData(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                      @RequestBody MemberAssetsEncryptDataVo encryptDataVo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        if(null == encryptDataVo){
            return new ResponseEntity(ResultVO.create(ResultCodeEnum.PREPAY_CARD_NO_DECRYPT_DATA, false), HttpStatus.OK);
        }
        if (commonRequestModel==null || StringUtils.isEmpty(commonRequestModel.getOrganSign())) {
            return new ResponseEntity(ResultVO.create(ResultCodeEnum.PREPAY_CARD_NO_ORGANSIGN, false), HttpStatus.OK);
        }
        //参数校验
        String encryptData = encryptDataVo.getData();
        if (StringUtils.isEmpty(encryptData)) {
            return new ResponseEntity(ResultVO.create(ResultCodeEnum.PREPAY_CARD_NO_DECRYPT_DATA, false), HttpStatus.OK);
        }
        ResultVO<Boolean> validateResult = dealAssetsReturnData(encryptDataVo,commonRequestModel);
        return new ResponseEntity(validateResult, HttpStatus.OK);
    }


    /**
     * pos异常订单核准，ticketNo = T+原小票号
     * @param commonRequestModelStr
     * @param encryptDataVo
     * @return
     */
    @ApiOperation(value = "会员资产核准（加密传输）", notes = "会员资产核准（加密传输）\n" +
            "加密步骤:  \n" +
            "1.对refund接口的请求参数json格式化为字符串  \n" +
            "2.使用AES的CBC模式 PKCS5Padding填充方式加密上述字符串  \n" +
            "3.使用BASE64进行编码", response = Boolean.class, tags = {"会员资产",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/approvalData", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> approvalData(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                        @RequestBody MemberAssetsEncryptDataVo encryptDataVo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        if(null == encryptDataVo){
            return new ResponseEntity(ResultVO.create(ResultCodeEnum.PREPAY_CARD_NO_DECRYPT_DATA, false), HttpStatus.OK);
        }
        if (commonRequestModel==null || StringUtils.isEmpty(commonRequestModel.getOrganSign())) {
            return new ResponseEntity(ResultVO.create(ResultCodeEnum.PREPAY_CARD_NO_ORGANSIGN, false), HttpStatus.OK);
        }
        //参数校验
        String encryptData = encryptDataVo.getData();
        if (StringUtils.isEmpty(encryptData)) {
            return new ResponseEntity(ResultVO.create(ResultCodeEnum.PREPAY_CARD_NO_DECRYPT_DATA, false), HttpStatus.OK);
        }
        ResultVO<Boolean> validateResult = dealAssetsApprovalData(encryptDataVo,commonRequestModel);
        return new ResponseEntity(validateResult, HttpStatus.OK);
    }


    @ApiOperation(value = "根据业务单号查询会员资产支付结果", notes = "根据业务单号查询会员资产支付结果", response = Boolean.class, tags = {"会员资产",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/queryPayStatusByBussinessNo", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> queryPayStatusByBussinessNo(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                             @RequestBody MemberAssetsPayStatusDataVo payStatusDataVo) {
        logger.info("queryPayStatusByBussinessNo param = {}",JSONObject.toJSONString(payStatusDataVo));
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        ResultVO resultVO = ResultVO.create(ResultCodeEnum.SYSTEM_EXCEPTION,false);
        List<Integer> needQueryPay = payStatusDataVo.getNeedPayStatus();
        if(CollectionUtils.isEmpty(needQueryPay)){
            return new ResponseEntity(ResultVO.create(ResultCodeEnum.PREPAY_CARD_NO_PAY_TYPE,false), HttpStatus.OK);
        }
        if(StringUtil.isEmpty(payStatusDataVo.getBussinessNo())){
            return new ResponseEntity(ResultVO.create(ResultCodeEnum.PREPAY_CARD_NO_TICKETNO,false), HttpStatus.OK);
        }
        logger.info("queryPayStatusByBussinessNo 会员资产支付状态 needQueryPay = {}",JSONObject.toJSONString(needQueryPay));
        //即有会员资产又有券支付的，只要会员资产支付成功，券也支付成功
        if(needQueryPay.indexOf(2) != -1){
            MemberAssetsBaseDto baseDto = new MemberAssetsBaseDto();
            baseDto.setOrgansign(commonRequestModel.getOrganSign());
            baseDto.setTicketNo(payStatusDataVo.getBussinessNo());
            try {
                ResultVO memberAssetsPay = memberAssetsApi.queryPayStatusByBussinessNo(baseDto);
                return new ResponseEntity(memberAssetsPay,HttpStatus.OK);
            }catch (Exception e){
                logger.error("queryPayStatusByBussinessNo error",e);
                return new ResponseEntity(resultVO,HttpStatus.OK);
            }
        }
        if(needQueryPay.indexOf(1) != -1) {
            CouponDto couponDto = new CouponDto();
            couponDto.setOrganSign(commonRequestModel.getOrganSign());
            couponDto.setUsedTicketNo(payStatusDataVo.getBussinessNo());
            try {
                List<CouponDto> couponDtoList = couponApi.selectCouponList(couponDto);
                logger.info("queryPayStatusByBussinessNo couponDtoList = {} ",JSONObject.toJSONString(couponDtoList));
                //支付失败
                if(CollectionUtils.isEmpty(couponDtoList)){
                    return new ResponseEntity(ResultVO.create(ResultCodeEnum.MEMBER_ASSETS_POINT_FAILD,true), HttpStatus.OK);
                }
            }catch (Exception e){
                logger.error("queryPayStatusByBussinessNo error",e);
                return new ResponseEntity(resultVO,HttpStatus.OK);
            }
            return new ResponseEntity(ResultVO.create(ResultCodeEnum.SUCCESS,true), HttpStatus.OK);
        }
        return new ResponseEntity(resultVO, HttpStatus.OK);
    }


    @ApiOperation(value = "获取会员资产token", notes = "获取会员资产token", response = String.class, tags = {"会员资产",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = String.class)})
    @RequestMapping(value = "/getToken", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> get(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                 @RequestBody MemberAssetsDataVo vo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        MemberAssetsTokenDto dto = new MemberAssetsTokenDto();
        dto.setMemberGuid(vo.getMemberGuid());
        dto.setOrgansign(commonRequestModel.getOrganSign());
        return new ResponseEntity(memberAssetsApi.getToken(dto), HttpStatus.OK);
    }

    /**
     * 校验会员资产支付数据
     * @param encryptDataVo
     * @param commonRequestModel
     */
    private ResultVO<Boolean> dealAssetsPayData(MemberAssetsEncryptDataVo encryptDataVo,CommonRequestModel commonRequestModel){
        ResultVO<Boolean> assetsResult = ResultVO.create(ResultCodeEnum.ERROR, false);
        try {
            //解密数据
            String json = decryptData(encryptDataVo.getData());
            logger.info("dealAssetsPayData param = {} ",json);
            if (StringUtils.isEmpty(json)) {
                return ResultVO.create(ResultCodeEnum.PREPAY_CARD_ERROR_DECRYPT_DATA, false);
            }
            MemberAssetsPayDto dto = JSONObject.parseObject(json, MemberAssetsPayDto.class);
            if (dto == null) {
                return ResultVO.create(ResultCodeEnum.PREPAY_CARD_ERROR_DECRYPT_DATA, false);
            }
            ResultVO<Boolean> commonValicate = commonParamValidate(dto);
            if(!commonValicate.getResult()) return commonValicate;
            dto.setOrgansign(commonRequestModel.getOrganSign());
            dto.setCreateUser(commonRequestModel.getEmployeeId());
            boolean isPrepayCardPay = false;//储值支付
            boolean isPointDeductPay = false;//积分抵现支付
            boolean isCouponPay = false;//券支付
            Long couponId = dto.getCouponId();
            BigDecimal totalAmount = dto.getTotalAmount();
            BigDecimal pointNum = dto.getPointNum();
            //判断是否只有券支付
            if (couponId != null && couponId > 0) {
                isCouponPay = true;
            }
            if (totalAmount != null && totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                isPrepayCardPay = true;
            }
            if (pointNum != null && pointNum.compareTo(BigDecimal.ZERO) > 0) {
                isPointDeductPay = true;
            }
            //是否包含券支付
            if (!isCouponPay && (isPrepayCardPay || isPointDeductPay)) {
                //使用会员资产支付
                assetsResult = memberAssetsApi.payData(dto);
                logger.info("dealAssetsPayData assetsResult = {} ",JSONObject.toJSONString(assetsResult));
                return assetsResult;
            }
            logger.info("dealAssetsPayData isCouponPay ={}  isPrepayCardPay={} isPointDeductPay={}",isCouponPay,isPrepayCardPay,isPointDeductPay);
            //不包含券支付逻辑
            //只有券支付
            if (isCouponPay && !isPrepayCardPay && !isPointDeductPay) {
                //直接调券接口进行使用
                boolean couponPayResult = dealCouponUsedData(dto);
                logger.info("dealAssetsPayData 只有券支付 dealCouponUsedData couponPayResult ={}  ",couponPayResult);
                if (couponPayResult) return ResultVO.create(ResultCodeEnum.SUCCESS, true);;
                return ResultVO.create(ResultCodeEnum.ERROR, false);
            }
            if (isCouponPay && (isPrepayCardPay || isPointDeductPay)) {
                //先锁定券 成功-->处理会员资产  失败-->直接返回
                boolean couponLockResult = dealCouponLockData(dto);
                logger.info("dealAssetsPayData dealCouponLockData couponPayResult ={}  ",couponLockResult);
                if (couponLockResult) {
                    //使用会员资产支付
                    assetsResult = memberAssetsApi.payData(dto);
                    logger.info("dealAssetsPayData 先锁定券 再会员资产支付结果 assetsResult = {} ",JSONObject.toJSONString(assetsResult));
                    if (!assetsResult.getResult()) {
                        //释放券
                        boolean couponUnlockResult = dealCouponUnLockData(dto);
                        logger.info("dealAssetsPayData dealCouponUnLockData couponPayResult ={}  ",couponUnlockResult);
                        return assetsResult;
                    } else {
                        //处理券支付
                        boolean couponUsedResult = dealCouponUsedData(dto);
                        logger.info("dealAssetsPayData dealCouponUsedData couponPayResult ={}  ",couponUsedResult);
                        return assetsResult;
                    }
                }
            }
        }catch (Exception e){
            logger.error("dealAssetsPayData error!",e);
        }
        return assetsResult;
    }


    /**
     * 统一参数校验
     * @param dto
     * @return
     */
    private ResultVO<Boolean> commonParamValidate(MemberAssetsBaseDto dto){
        ResultVO<Boolean> assetsResult = ResultVO.create(ResultCodeEnum.SUCCESS, true);
        String memberGuid = dto.getMemberGuid();
        String ticketNo = dto.getTicketNo();
        if (StringUtils.isEmpty(memberGuid)) {
            return ResultVO.create(ResultCodeEnum.PREPAY_CARD_NO_MEMBER_GUID, false);
        }
        if (StringUtils.isEmpty(ticketNo)) {
            return ResultVO.create(ResultCodeEnum.PREPAY_CARD_NO_TICKETNO, false);
        }
        return assetsResult;
    }


    /**
     * 释放券
     * @param dto
     * @return
     */
    private boolean dealCouponUnUseData(MemberAssetsReturnDto dto){
        try {
            CouponDto couponDto = new CouponDto();
            couponDto.setOrganSign(dto.getOrgansign());
            couponDto.setId(dto.getCouponId());
            return couponApi.unUseCoupon(couponDto);
        }catch (Exception e){
            logger.error("dealCouponLockData error ! param = "+JSONObject.toJSONString(dto),e);
        }
        return false;
    }
    /**
     * 锁定券
     * @param dto
     * @return
     */
    private boolean dealCouponLockData(MemberAssetsPayDto dto){
        try {
            CouponDto couponDto = new CouponDto();
            couponDto.setOrganSign(dto.getOrgansign());
            couponDto.setId(dto.getCouponId());
            dto.setTicketNo(dto.getTicketNo());
            return couponApi.lockCoupon(couponDto);
        }catch (Exception e){
            logger.error("dealCouponLockData error ! param = "+JSONObject.toJSONString(dto),e);
        }
        return false;
    }

    /**
     * 释放券
     * @param dto
     * @return
     */
    private boolean dealCouponUnLockData(MemberAssetsPayDto dto){
        try {
            CouponDto couponDto = new CouponDto();
            couponDto.setOrganSign(dto.getOrgansign());
            couponDto.setId(dto.getCouponId());
            return couponApi.unLockCoupon(couponDto);
        }catch (Exception e){
            logger.error("dealCouponUnLockData error ! param = "+JSONObject.toJSONString(dto),e);
        }
        return false;
    }

    /**
     * 处理券支付
     * @param dto
     * @return
     */
    private boolean dealCouponUsedData(MemberAssetsPayDto dto){
        try {
            CouponDto couponDto = new CouponDto();
            couponDto.setOrganSign(dto.getOrgansign());
            couponDto.setId(dto.getCouponId());
            couponDto.setUsedTicketNo(dto.getTicketNo());
            int result = couponApi.useCoupon(couponDto);
            if (result == 0) {
                logger.info("dealCouponUsedData success! couponId = {}", dto.getCouponId());
                return true;
            }
        }catch (Exception e){
            logger.error("dealCouponUsedData error ! param = "+JSONObject.toJSONString(dto),e);
        }
        return false;
    }



    /**
     * 校验会员资产退款数据
     * @param encryptData
     */
    private String decryptData(String encryptData){
        try {
            String json = memberAssetsApi.decryptData(encryptData);
            logger.info("decryptData param = {} ", json);
            return json;
        }catch (Exception e){
            logger.error("decryptData error!",e);
        }
        return null;
    }

    /**
     * 校验会员资产退款数据
     * @param encryptDataVo
     * @param commonRequestModel
     */
    private ResultVO<Boolean> dealAssetsReturnData(MemberAssetsEncryptDataVo encryptDataVo,CommonRequestModel commonRequestModel){
        ResultVO<Boolean> assetsResult = ResultVO.create(ResultCodeEnum.ERROR, false);
        try {
            String json = decryptData(encryptDataVo.getData());
            MemberAssetsReturnDto dto = JSONObject.parseObject(json, MemberAssetsReturnDto.class);
            if (dto == null) {
                return ResultVO.create(ResultCodeEnum.PREPAY_CARD_ERROR_DECRYPT_DATA, false);
            }
            String oldTicketNo = dto.getOldTicketNo();
            String organSign = commonRequestModel.getOrganSign();
            ResultVO<Boolean> commonValicate = commonParamValidate(dto);
            if(!commonValicate.getResult()) return commonValicate;
            if (StringUtils.isEmpty(oldTicketNo)) {
                return ResultVO.create(ResultCodeEnum.PREPAY_CARD_NO_OLD_TICKETNO, false);
            }
            dto.setOrgansign(organSign);
            dto.setCreateUser(commonRequestModel.getEmployeeId());
            BigDecimal totalAmount = dto.getTotalAmount();
            BigDecimal pointNum = dto.getPointNum();
            if ((totalAmount == null || totalAmount.compareTo(BigDecimal.ZERO) <= 0) && (pointNum == null || pointNum.compareTo(BigDecimal.ZERO) <= 0)) {
                return ResultVO.create(ResultCodeEnum.MEMBER_ASSETS, false);
            }
            //使用会员资产退货
            assetsResult= memberAssetsApi.returnData(dto);
        }catch (Exception e){
            logger.error("dealAssetsReturnData error!",e);
        }
        return assetsResult;
    }


    /**
     * 核准会员资产数据
     * @param encryptDataVo
     * @param commonRequestModel
     */
    private ResultVO<Boolean> dealAssetsApprovalData(MemberAssetsEncryptDataVo encryptDataVo,CommonRequestModel commonRequestModel){
        ResultVO<Boolean> assetsResult = ResultVO.create(ResultCodeEnum.ERROR, false);
        try {
            String json = decryptData(encryptDataVo.getData());
            logger.info("dealAssetsApprovalData param = {} ", json);
            if (StringUtils.isEmpty(json)) {
                return ResultVO.create(ResultCodeEnum.PREPAY_CARD_ERROR_DECRYPT_DATA, false);
            }
            MemberAssetsReturnDto dto = JSONObject.parseObject(json, MemberAssetsReturnDto.class);
            if (dto == null) {
                return ResultVO.create(ResultCodeEnum.PREPAY_CARD_ERROR_DECRYPT_DATA, false);
            }
            String oldTicketNo = dto.getOldTicketNo();
            String organSign = commonRequestModel.getOrganSign();
            ResultVO<Boolean> commonValicate = commonParamValidate(dto);
            if(!commonValicate.getResult()) return commonValicate;
            if (StringUtils.isEmpty(oldTicketNo)) {
                return ResultVO.create(ResultCodeEnum.PREPAY_CARD_NO_OLD_TICKETNO, false);
            }
            dto.setOrgansign(organSign);
            dto.setCreateUser(commonRequestModel.getEmployeeId());
            BigDecimal totalAmount = dto.getTotalAmount();
            BigDecimal pointNum = dto.getPointNum();
            boolean isCouponPay = false;//券支付
            boolean isPrepayCardPay = false;//储值支付
            boolean isPointDeductPay = false;//积分抵现支付
            Long couponId = dto.getCouponId();
            if (couponId != null && couponId > 0) {
                isCouponPay = true;
            }
            if (totalAmount != null && totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                isPrepayCardPay = true;
            }
            if (pointNum != null && pointNum.compareTo(BigDecimal.ZERO) > 0) {
                isPointDeductPay = true;
            }
            if(isCouponPay){
                //释放券
                boolean isSuccess = dealCouponUnUseData(dto);
                logger.info("dealAssetsApprovalData 释放券 isSuccess = {} ",isSuccess);
                if(!isSuccess){
                    return assetsResult;
                }
                if(!isPrepayCardPay && !isPointDeductPay){
                    return ResultVO.create(ResultCodeEnum.SUCCESS, true);
                }
            }
            //需要核准会员资产
            if(isPrepayCardPay || isPointDeductPay) {
                //核准会员资产
                assetsResult = memberAssetsApi.approvalData(dto);
                return assetsResult;
            }
        }catch (Exception e){
            logger.error("dealAssetsApprovalData error!",e);
        }
        return assetsResult;
    }


    @ApiOperation(value = "扫描已锁定的券", notes = "扫描已锁定的券", response = Boolean.class, tags = {"会员资产",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/getUnLockCoupon", method = RequestMethod.POST)
    ResponseEntity<ResultVO> getUnLockCoupon() {
        CouponDto dto = new CouponDto();
        dto.setStatus(Byte.valueOf("5"));
        List<CouponDto> couponDtoList = couponApi.selectCouponList(dto);
        logger.info("getUnLockCoupon couponDtoList = {} ",JSONObject.toJSONString(couponDtoList));
        for(CouponDto couponDto:couponDtoList){
            //获取到小票号
            String ticketNo = couponDto.getUsedTicketNo();
            String organSign = couponDto.getOrganSign();
            MemberAssetsBaseDto baseDto = new MemberAssetsBaseDto();
            baseDto.setOrgansign(couponDto.getOrganSign());
            baseDto.setTicketNo(ticketNo);
            ResultVO<Boolean> result = memberAssetsApi.queryPayStatusByBussinessNo(baseDto);
            if (!result.getResult()) {
                //释放券
                couponDto.setOrganSign(organSign);
                couponDto.setId(couponDto.getId());
                boolean unLockResult = couponApi.unLockCoupon(couponDto);
            } else {
                //处理券支付
                couponDto.setOrganSign(organSign);
                couponDto.setId(couponDto.getId());
                int useResult = couponApi.useCoupon(couponDto);
            }
        }
        return new ResponseEntity(ResultVO.createSuccess(true), HttpStatus.OK);
    }

    @ApiOperation(value = "是否开启积分抵现功能", notes = "是否开启积分抵现功能", response = Boolean.class, tags = {"会员资产",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/isOpenPointDeductConfig", method = RequestMethod.POST)
    ResponseEntity<ResultVO> isOpenPointDeductConfig(@RequestHeader("commonRequestModel") String commonRequestModelStr) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        boolean isOpen = false;
        try{
            isOpen = memberAssetsApi.isOpenPointDeductConfig(commonRequestModel.getOrganSign());
        }catch (Exception e){
            logger.error("isOpenPointDeductConfig ",e);
        }
        return new ResponseEntity(ResultVO.createSuccess(isOpen), HttpStatus.OK);
    }
}
