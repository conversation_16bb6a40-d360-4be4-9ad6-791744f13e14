package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.DateUtil;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.common.util.MD5Util;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.encryption.cores.enums.DataTypeEnum;
import com.xyy.saas.member.core.api.*;
import com.xyy.saas.member.core.dto.*;
import com.xyy.saas.rabbitmq.core.api.MessagePushApi;
import com.xyy.saas.web.api.common.config.annotition.RepeatSubmitValidation;
import com.xyy.saas.web.api.module.member.model.*;
import com.xyy.saas.web.api.module.member.service.MemberService;
import com.xyy.saas.web.api.module.member.utils.ResultCodeEnum;
import com.xyy.saas.web.api.module.utils.ExportExcelUtil;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import com.xyy.user.module.dto.result.QueryEmployeeVO;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/member")
public class MemberApiController {

    private static final Logger logger = LogManager.getLogger(MemberApiController.class);
    @Reference(version = "0.0.1")
    private com.xyy.saas.member.core.api.MemberBaseApi memberBaseApi;

    @Reference(version = "0.0.1")
    private MemberBaseRelationApi memberBaseRelationApi;

    @Autowired
    private MemberService memberService;

    @Reference(version = "0.0.1")
    private MessagePushApi messagePushApi;

    @Reference(version = "0.0.1")
    private MemberBaseRelationApi relationApi;

    @Reference(version = "0.0.1")
    private MemberPrepayCardApi memberPrepayCardApi;

    @Reference(version = "0.0.1")
    private MemberLevelApi memberLevelApi;

    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;

    @Reference(version = "0.0.1")
    private DataTokenFlushApi dataTokenService;
    @Reference(version = "0.0.1")
    private MemberChronicRelationApi chronicRelationApi;

    @ApiOperation(value = "获取会员基本信息数据同步数据集合", notes = "获取会员基本信息数据同步数据集合", response = MemberBase.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberBase.class)})
    @RequestMapping(value = "/member/getSyncMemberDataList",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getSyncMemberDataList(@RequestHeader(name = "organSign", required = true) String organSign, @ApiParam(value = "会员基本信息", required = true) @RequestBody MemberBase memberBase) {
        MemberBaseDto memberBaseDto = new MemberBaseDto();
        BeanUtils.copyProperties(memberBase, memberBaseDto);
        memberBaseDto.setOrgansign(organSign);
        return new ResponseEntity(ResultVO.createSuccess(memberBaseApi.getMemberBaseList(memberBaseDto)), HttpStatus.OK);
    }

    @ApiOperation(value = "批量保存会员基本信息", notes = "批量保存会员基本信息", response = MemberBase.class, tags = {"member",})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberBase.class)})
    @RequestMapping(value = "/member/saveBatchMemberBase", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> saveBatchMemberBase(@RequestHeader(name = "organSign", required = true) String organSign, @ApiParam(value = "会员基本信息", required = true) @RequestBody BatchMemberBase batchMemberBase) {
        List<MemberBase> memberBaseList = batchMemberBase.getMemberBases();
        List<MemberBaseDto> list = new ArrayList<>();
        MemberBaseDto mb = null;
        for (MemberBase mbs : memberBaseList) {
            mb = new MemberBaseDto();
            BeanUtils.copyProperties(mbs, mb);
            mb.setOrgansign(organSign);
            list.add(mb);
        }
        return new ResponseEntity(ResultVO.createSuccess(memberBaseApi.saveBatchMemberBaseUpload(list)), HttpStatus.OK);
    }

    @ApiOperation(value = "保存或修改会员基本信息（pos端使用）", notes = "返回码解释：0：保存成功", response = MemberBase.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberBase.class)})
    @RequestMapping(value = "/member/saveMemberBase", method = RequestMethod.POST, produces = "application/json",
            consumes = "application/json")
    @RepeatSubmitValidation(resultType = 4)
    public ResponseEntity<ResultVO> saveMemberBase(@RequestHeader(name = "organSign", required = true) String organSign, @ApiParam(value = "会员基本信息", required = true) @RequestBody MemberBaseVo memberBase) {
        boolean flag = false;
        if (StringUtils.isEmpty(memberBase.getCartNo())) {
            return new ResponseEntity(new ResultVO(-1, "参数cartNo不能为空", flag), HttpStatus.OK);
        }
        if (org.springframework.util.StringUtils.isEmpty(memberBase.getSex())) {
            return new ResponseEntity(new ResultVO(-1, "参数sex不能为空", flag), HttpStatus.OK);
        }
        if (org.springframework.util.StringUtils.isEmpty(memberBase.getName())) {
            return new ResponseEntity(new ResultVO(-1, "参数name不能为空", flag), HttpStatus.OK);
        }
        if (org.springframework.util.StringUtils.isEmpty(memberBase.getVipLevelId())) {
            return new ResponseEntity(new ResultVO(-1, "参数vipLevelId不能为空", flag), HttpStatus.OK);
        }
        if (org.springframework.util.StringUtils.isEmpty(memberBase.getTelephone())) {
            return new ResponseEntity(new ResultVO(-1, "参数telephone不能为空", flag), HttpStatus.OK);
        }
        logger.info("POS memberBase对象入参：" + JSONUtils.obj2JSON(memberBase));
        SaaSDrugstoreDto model = drugstoreApi.getDrugstoreByOrganSign(organSign);
        String headquartersOrganSign=null ;
        // 单体门店
        if (model.getBizModel() == 1) {
            organSign = model.getOrganSign();
        }
        // 联营连锁门店
        else {
            //总部
            if(model.getOrganSignType() == 3){
                headquartersOrganSign = model.getOrganSign();
            }else{
                headquartersOrganSign = model.getHeadquartersOrganSign();
            }
        }
        MemberBaseDto dto = new MemberBaseDto();
        BeanUtils.copyProperties(memberBase, dto);
        String cartNo = memberBase.getCartNo();
        if(model.getBizModel() == 2){
            dto.setHeadquartersOrganSign(headquartersOrganSign);
        }else{
            dto.setOrgansign(organSign);
        }
        String memberGuid = memberBase.getGuid();
        // 新增会员没有guid
        if (!StringUtils.isEmpty(memberGuid)) {
            // 查询来源门店
            MemberBaseDto old = memberBaseApi.getMemberBaseByGuid(memberGuid, null);
            if (old != null) {
                dto.setOrgansign(old.getOrgansign());
            }
        }
        dto.setCartNo(null);
        long existTelephone = memberBaseApi.getMemberBaseByCondition(dto);
        if (existTelephone > 0 && memberBase.getTelephone() != null) {
            return new ResponseEntity(new ResultVO(-1, "电话号码已注册，请重新输入电话号码！", flag), HttpStatus.OK);
        }
        dto.setTelephone(null);
        dto.setCartNo(cartNo);
        long existCartNo = memberBaseApi.getMemberBaseByCondition(dto);
        if (existCartNo > 0 && memberBase.getCartNo() != null) {
            return new ResponseEntity(new ResultVO(-1, "会员卡号已注册，请重新输入会员卡号！", flag), HttpStatus.OK);
        }
        MemberBaseDto mb = new MemberBaseDto();
        BeanUtils.copyProperties(memberBase, mb);
        mb.setOrgansign(organSign);
        mb.setHeadquartersOrganSign(headquartersOrganSign);
        mb.setAllPoint(null);
        if (!org.springframework.util.StringUtils.isEmpty(memberBase.getSendCardTime())) {
            Date sendCardTime = DateUtil.parseStrToDate(memberBase.getSendCardTime(), DateUtil.DATE_FORMAT_YYYY_MM_DD);
            mb.setSendCardTime(sendCardTime);
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberBase.getBirthday())) {
            Date birthday = DateUtil.parseStrToDate(memberBase.getBirthday(), DateUtil.DATE_FORMAT_YYYY_MM_DD);
            mb.setBirthday(birthday);
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberBase.getEffectTime())) {
            Date effectTime = DateUtil.parseStrToDate(memberBase.getEffectTime(), DateUtil.DATE_FORMAT_YYYY_MM_DD);
            mb.setEffectTime(effectTime);
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberBase.getExpriedTime())) {
            Date expriedTime = DateUtil.parseStrToDate(memberBase.getExpriedTime(), DateUtil.DATE_FORMAT_YYYY_MM_DD);
            mb.setExpriedTime(expriedTime);
        }
        dto = null;
        logger.info("POS memberBaseDto对象入参：" + JSONUtils.obj2JSON(mb));
        String guid = mb.getGuid();
        if (guid != null) {
            dto = memberBaseApi.getMemberBaseInfoById(guid, organSign);
            if (dto == null || dto.getYn() == null || dto.getYn() == 0) {
                return new ResponseEntity(new ResultVO(-1, "保存失败，未找到当前会员信息！", flag), HttpStatus.OK);
            }
            flag = memberBaseApi.updateMemberBase(mb);
        } else {
            if (mb.getSendCardTime() == null) {
                mb.setSendCardTime(new Date());
            }
            if (mb.getEffectTime() == null) {
                mb.setEffectTime(new Date());
            }
            if (mb.getExpriedTime() == null) {
                mb.setExpriedTime(DateUtil.addDate(new Date(), 50, 0, 0, 0, 0, 0, 0));
            }
            // 新增会员的时候会为该会员充值
            if (memberBase.getAmount() != null && memberBase.getAmount().compareTo(BigDecimal.ZERO) > 0
                    || memberBase.getBonus() != null && memberBase.getBonus().compareTo(BigDecimal.ZERO) > 0) {
                MemberPrepayCardDto memberPrepayCardDto = new MemberPrepayCardDto();
                //交易门店
                memberPrepayCardDto.setOrgansign(organSign);
                //来源门店
                memberPrepayCardDto.setHeadquartersOrganSign(organSign);
                memberPrepayCardDto.setAmount(memberBase.getAmount());
                memberPrepayCardDto.setBonus(memberBase.getBonus());
                memberPrepayCardDto.setPayType(memberBase.getPayType());
                memberPrepayCardDto.setCreateUser(memberBase.getCreateUser());
                memberPrepayCardDto.setCheckUser(memberBase.getCheckUser());
                flag = memberBaseApi.saveMemberBaseWithPrepayCard(mb, memberPrepayCardDto);
            } else {
                flag = memberBaseApi.saveMemberBase(mb);
            }
        }
        if (flag) {
            return new ResponseEntity(new ResultVO(0, "操作成功", flag), HttpStatus.OK);
        } else {
            return new ResponseEntity(new ResultVO(-1, "操作失败,请填写正确信息", flag), HttpStatus.OK);
        }

    }

    @ApiOperation(value = "会员列表查询分页", notes = "会员列表查询分页", response = MemberBase.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberBase.class)})
    @RequestMapping(value = "/member/getMemberDataListPager",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getMemberDataListPager(@RequestHeader(name = "organSign", required = true) String organSign, @ApiParam(value = "会员基本信息", required = true) @RequestBody MemberBase memberBase) {
        MemberBaseDto memberBaseDto = new MemberBaseDto();
        BeanUtils.copyProperties(memberBase, memberBaseDto);
        memberBaseDto.setOrgansign(organSign);
        return new ResponseEntity(ResultVO.createSuccess(memberBaseApi.getMemberBaseByCondition(memberBaseDto)), HttpStatus.OK);
    }

    @ApiOperation(value = "定时器扫描会员状态", notes = "定时器扫描会员状态", response = String.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = String.class)})
    @RequestMapping(value = "/member/taskUpdateMemberState",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> taskUpdateMemberState(@RequestParam(name = "startId", required = false) Long startId,
                                                          @RequestParam(name = "endId", required = false) Long endId) {
        boolean flag = memberBaseApi.taskUpdateMemberStateV2(new Date(), startId, endId);
        return new ResponseEntity<>(ResultVO.createSuccess(flag), HttpStatus.OK);
    }

    @ApiOperation(value = "根据id集合查找会员信息", notes = "根据id集合查找会员信息", response = MemberBaseRelation.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberBaseRelation.class)})
    @RequestMapping(value = "/member/getMemberRelationByIds",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getMemberRelationByIds(@RequestBody RelationCondition condition) {
        return new ResponseEntity<>(ResultVO.createSuccess(memberBaseRelationApi.getMemberRelationByIds(condition.getIds())), HttpStatus.OK);
    }

    @ApiOperation(value = "根据机构编码查询会员总数", notes = "根据机构编码查询会员总数", response = MemberBaseRelation.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberBaseRelation.class)})
    @RequestMapping(value = "/member/getMemberCountByOrgansign",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getMemberCountByOrgansign(@RequestHeader(name = "organSign", required = true) String organSign) {
        return new ResponseEntity<>(ResultVO.createSuccess(memberBaseApi.getMemberCountByOrgansign(organSign)), HttpStatus.OK);
    }

    @ApiOperation(value = "会员启用、禁用", notes = "会员启用、禁用", response = ResultVO.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class)})
    @RequestMapping(value = "/member/state", method = RequestMethod.POST, produces = "application/json",
            consumes = "application/json")
    public ResponseEntity<ResultVO> memberState(@RequestHeader(name = "organSign", required = true) String organSign,
                                                @ApiParam(value = "会员基本信息", required = true) @RequestBody MemberBase memberBase) {
        boolean flag = false;
        if (StringUtils.isEmpty(organSign)) {
            return new ResponseEntity(new ResultVO(-1, "参数organSign不能为空", flag), HttpStatus.OK);
        }
        if (memberBase.getState() == null) {
            return new ResponseEntity(new ResultVO(-1, "参数state不能为空", flag), HttpStatus.OK);
        }
        if (memberBase.getId() == null) {
            return new ResponseEntity(new ResultVO(-1, "参数id不能为空", flag), HttpStatus.OK);
        }
        if (StringUtils.isEmpty(memberBase.getUpdateUser())) {
            return new ResponseEntity(new ResultVO(-1, "参数更新人不能为空", flag), HttpStatus.OK);
        }
        flag = memberBaseApi.updateMemberState(memberBase.getId(), organSign, memberBase.getState(), memberBase.getUpdateUser(), new Date());
        if (flag) {
            syncMqPush(organSign);
            return new ResponseEntity(new ResultVO(0, "操作成功", flag), HttpStatus.OK);
        } else {
            return new ResponseEntity(new ResultVO(-1, "操作失败,请填写正确信息", flag), HttpStatus.OK);
        }
    }

//-----------------------------------------------------web-plus会员资料迁移的相关接口--------------------------------------------------------------

    @ApiOperation(value = "门店端:会员资料及储值保存,编辑", notes = "会员资料及储值保存,编辑", response = ResultVO.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class)})
    @RequestMapping(value = "/member/saveAndPrePay", method = RequestMethod.POST, produces = "application/json",
            consumes = "application/json")
    @RepeatSubmitValidation(resultType = 4)
    public ResponseEntity<ResultVO> saveAndPrePay(@RequestHeader("commonRequestModel") String commonRequestModel, @ApiParam(value = "会员基本信息", required = true) @RequestBody MemberBaseVo memberBaseVo){
        CommonRequestModel model = JSONObject.parseObject(commonRequestModel, CommonRequestModel.class);
        boolean flag = false;
        String headquartersOrganSign = null;
        String organSign = null;
        MemberBaseDto baseDto = new MemberBaseDto();
        BeanUtils.copyProperties(memberBaseVo, baseDto);
        String createUser = memberBaseVo.getCreateUser();
        String updateUserId = model.getEmployeeId();
//        //会员关联慢病和门店 (门店-操作的门店， 总部-操作的会员所属门店) add by zhuzc
//        baseDto.setChronicRelOrganSign(model.getOrganSign());
        // 单体门店
        if (model.getBizModel() == 1) {
            organSign = model.getOrganSign();
        }
        // 联营连锁门店
        else {
            organSign = memberBaseVo.getOrgansign();
            headquartersOrganSign = model.getHeadquartersOrganSign();
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberBaseVo.getSendCardTime())) {
            Date sendCardTime = DateUtil.parseStrToDate(memberBaseVo.getSendCardTime(), DateUtil.DATE_FORMAT_YYYY_MM_DD);
            baseDto.setSendCardTime(sendCardTime);
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberBaseVo.getBirthday())) {
            Date birthday = DateUtil.parseStrToDate(memberBaseVo.getBirthday(), DateUtil.DATE_FORMAT_YYYY_MM_DD);
            baseDto.setBirthday(birthday);
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberBaseVo.getEffectTime())) {
            Date effectTime = DateUtil.parseStrToDate(memberBaseVo.getEffectTime(), DateUtil.DATE_FORMAT_YYYY_MM_DD);
            baseDto.setEffectTime(effectTime);
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberBaseVo.getExpriedTime())) {
            Date expriedTime = DateUtil.parseStrToDate(memberBaseVo.getExpriedTime(), DateUtil.DATE_FORMAT_YYYY_MM_DD);
            baseDto.setExpriedTime(expriedTime);
        }

        if (memberBaseVo.getPoint() != null && memberBaseVo.getPoint().compareTo(BigDecimal.ZERO) < 0) {
            return new ResponseEntity(new ResultVO(-1, "积分不能是负数", flag), HttpStatus.OK);
        }
        if (memberBaseVo.getBonus() != null && memberBaseVo.getBonus().compareTo(BigDecimal.ZERO) < 0) {
            return new ResponseEntity(new ResultVO(-1, "赠送金额不能是负数", flag), HttpStatus.OK);
        }
        if (memberBaseVo.getAmount() != null && memberBaseVo.getAmount().compareTo(BigDecimal.ZERO) < 0) {
            return new ResponseEntity(new ResultVO(-1, "卡内余额不能是负数", flag), HttpStatus.OK);
        }
        if (StringUtils.isEmpty(model.getOrganSign())) { //会员绑定慢病的操作机构使用
            return new ResponseEntity(new ResultVO(-1, "操作机构不能为空", flag), HttpStatus.OK);
        }
        boolean result = false;
        logger.info("MemberController saveAndPrePay memberBaseVo:{}", JSONObject.toJSON(baseDto));
        if (baseDto.getId() != null) {
            baseDto.setOrgansign(organSign);
            baseDto.setUpdateUser(updateUserId);
            baseDto.setHeadquartersOrganSign(headquartersOrganSign);
            MemberLevelDto level = memberLevelApi.getMemberLevelById(baseDto.getVipLevelId());
            if(level == null){
                return new ResponseEntity(new ResultVO(-1, "会员储值新增失败, 请刷新后重试", flag), HttpStatus.OK);
            }
            if (headquartersOrganSign != null && headquartersOrganSign != "") {
                if (baseDto.getAllPoint().doubleValue() < level.getNeedPoint().doubleValue()) {
                    return new ResponseEntity(new ResultVO(-1, "会员等级的所需积分不能大于累计积分，请修改会员等级", flag), HttpStatus.OK);
                }
            } else {
                if (level.getUpPoint().compareTo(new BigDecimal(0)) == 1 && baseDto.getAllPoint().compareTo(level.getUpPoint()) > -1) {
                    return new ResponseEntity(new ResultVO(-1, "会员等级的升级积分不能小于累计积分，请修改会员等级", flag), HttpStatus.OK);
                }
            }
            result = memberBaseApi.updateExpireDateMemberBase(baseDto);
        } else {
            //来源门店
            baseDto.setOrgansign(organSign);
            baseDto.setCreateUser(createUser);
            baseDto.setHeadquartersOrganSign(headquartersOrganSign);
            if(baseDto.getPoint() == null ){
                baseDto.setPoint(BigDecimal.ZERO);
                baseDto.setAllPoint(BigDecimal.ZERO);
            }else{
                baseDto.setAllPoint(baseDto.getPoint());
            }
            MemberLevelDto level = memberLevelApi.getMemberLevelById(baseDto.getVipLevelId());
            if (headquartersOrganSign != null && headquartersOrganSign != "") {
                if (baseDto.getPoint().doubleValue() < level.getNeedPoint().doubleValue()) {
                    return new ResponseEntity(new ResultVO(-1, "会员等级的所需积分不能大于会员积分，请修改会员等级", flag), HttpStatus.OK);
                }
            } else {
                if (level.getUpPoint().compareTo(new BigDecimal(0)) == 1 && baseDto.getPoint().compareTo(level.getUpPoint()) > -1) {
                    return new ResponseEntity(new ResultVO(-1, "会员等级的升级积分不能小于会员积分，请修改会员等级", flag), HttpStatus.OK);
                }
            }
            result = memberBaseApi.saveMemberBase(baseDto);
            if (!result) {
                logger.info("MemberController saveAndPrePay: 会员资料新增失败");
                return new ResponseEntity(new ResultVO(-1, "会员资料新增失败", flag), HttpStatus.OK);
            }
            if ((memberBaseVo.getAmount() != null && memberBaseVo.getAmount().compareTo(BigDecimal.ZERO) > 0) || (memberBaseVo.getBonus() != null && memberBaseVo.getBonus().compareTo(BigDecimal.ZERO) > 0)) {
                MemberBaseDto memberBaseDto = new MemberBaseDto();
                memberBaseDto.setCartNo(baseDto.getCartNo());
                memberBaseDto.setOrgansign(baseDto.getOrgansign());
                List<MemberBaseDto> baseList = memberBaseApi.getMemberBaseList(memberBaseDto);
                MemberBaseDto dd = null;
                if (baseList != null && baseList.size() > 0) {
                    dd = baseList.get(0);
                }
                //充值-自身发送mq
                MemberPrepayCardDto dto = new MemberPrepayCardDto();
                BeanUtils.copyProperties(memberBaseVo, dto);
                dto.setOrgansign(organSign);
                dto.setHeadquartersOrganSign(headquartersOrganSign);
                //交易门店
                dto.setOrgansign(organSign);
                //来源门店
                dto.setHeadquartersOrganSign(organSign);
                dto.setMemberGuid(dd.getGuid());
                dto.setCreateUser(createUser);
                dto.setCheckUser(memberBaseVo.getCheckUser());
                dto.setCheckPassword(memberBaseVo.getCheckPassword());
                com.xyy.saas.member.core.response.ResultVO<Boolean> deposit = memberPrepayCardApi.deposit(dto);
                logger.info("MemberController saveAndPrePay prepayCardDto:{}, deposit:{}", JSONObject.toJSON(dto), JSONObject.toJSON(deposit));
                if (!deposit.getResult()) {
                    //删除刚才新增的会员信息
                    int del_result = memberBaseApi.deleteMemberByCondition(dd);
                    if (del_result <= 0) {
                        logger.info("MemberController saveAndPrePay: 会员储值新增失败, 删除会员信息失败");
                        return new ResponseEntity(new ResultVO(-1, "会员储值新增失败, 删除会员信息失败", deposit.getResult()), HttpStatus.OK);
                    }
                    return new ResponseEntity(new ResultVO(-1, deposit.getMsg(), deposit.getResult()), HttpStatus.OK);
                }
            }
        }
        return new ResponseEntity<>(ResultVO.createSuccess("新增成功"), HttpStatus.OK);
    }

    @ApiOperation(value = "门店端:获取会员卡号(新增会员时,服务端生成会员卡号)", notes = "获取会员卡号(新增会员时,服务端生成会员卡号)", response = String.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = String.class)})
    @RequestMapping(value = "/member/getMemberCardNo",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getMemberCardNo() {
        String cardNo = memberBaseApi.getMemberCardNo();
        return new ResponseEntity<>(ResultVO.createSuccess(cardNo), HttpStatus.OK);
    }

    @ApiOperation(value = "门店端:根据Guid查找会员信息", notes = "根据Guid查找会员信息", response = MemberBase.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberBase.class)})
    @RequestMapping(value = "/member/getMemberBaseByGuid",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getMemberBaseByGuid(@RequestHeader("commonRequestModel") String commonRequestModel, @ApiParam(value = "会员基本信息", required = true) @RequestBody MemberBaseVo memberBaseVo) {
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        logger.info("门店端:根据Guid查找会员信息,入参：{}", JSON.toJSONString(memberBaseVo));
        MemberBaseDto memberBaseDto = new MemberBaseDto();
        memberBaseDto.setGuid(memberBaseVo.getGuid());
        memberBaseDto.setOrgansign(model.getOrganSign());
//        if (model.getBizModel() == 1) {// 单体门店
//            memberBaseDto.setOrgansign(model.getOrganSign());
//        }else{
//            memberBaseDto.setOrgansign(memberBaseVo.getOrgansign());
//        }
        memberBaseDto.setOperationType(memberBaseVo.getOperationType());
        MemberBaseDto baseDto = memberBaseApi.getMemberByGuid(memberBaseDto);
        if (baseDto != null ) {
            return new ResponseEntity(ResultVO.createSuccess(baseDto), HttpStatus.OK);
        } else {
            return new ResponseEntity(new ResultVO(-1, "查询失败", false), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "门店端:获取发卡人信息", notes = "获取发卡人信息", response = MemberBase.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberBase.class)})
    @RequestMapping(value = "/member/getSystemDictList",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getSystemDictList(@RequestHeader("commonRequestModel") String commonRequestModel, @RequestBody MemberBaseVo memberBaseVo) {
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        String organSign = null;
        if (model.getBizModel() == 1) {// 单体门店
            organSign = model.getOrganSign();
        } else {
            organSign = memberBaseVo.getOrgansign();
        }
        List<QueryEmployeeVO> collect = memberBaseApi.selectEmployee(model.getOrganSign(), model.getEmployeeId());
        return new ResponseEntity(ResultVO.createSuccess(collect), HttpStatus.OK);
    }

    @ApiOperation(value = "门店端:查看手机号、卡号是否重复", notes = "查看手机号、卡号是否重复", response = MemberBase.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberBase.class)})
    @RequestMapping(value = "/member/validTelephoneExist", method = RequestMethod.POST, produces = "application/json",
            consumes = "application/json")
    public ResponseEntity<ResultVO> validTelephoneExist(@RequestHeader("commonRequestModel") String commonRequestModel,@ApiParam(value = "会员基本信息", required = true) @RequestBody MemberBaseVo memberBaseVo) {
        // 经营模式 1、单体 2、连锁 3、联营
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        MemberBaseDto dto = new MemberBaseDto();
        Byte bizModel = model.getBizModel();
        if (bizModel == 1) {
            dto.setOrgansign(model.getOrganSign());
        }
        if (bizModel == 3) {
            dto.setOrgansign(memberBaseVo.getOrgansign());
        }
        if (bizModel == 2) {
            dto.setHeadquartersOrganSign(model.getHeadquartersOrganSign());
        }
        if(StringUtils.isEmpty(memberBaseVo.getTelephone())){
            return new ResponseEntity(new ResultVO(-1, "手机号不能为空", false), HttpStatus.OK);
        }
        if(StringUtils.isEmpty(memberBaseVo.getCartNo())){
            return new ResponseEntity(new ResultVO(-1, "会员卡号不能为空", false), HttpStatus.OK);
        }
        dto.setTelephone(memberBaseVo.getTelephone());
        dto.setGuid(memberBaseVo.getGuid());
        long existTelephone = memberBaseApi.getMemberBaseByCondition(dto);
        if (existTelephone > 0 && memberBaseVo.getTelephone() != null) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.EXISTS_TELEPHONE.getCode(),ResultCodeEnum.EXISTS_TELEPHONE.getMsg(), null), HttpStatus.OK);
        }
        dto.setTelephone(null);
        dto.setCartNo(memberBaseVo.getCartNo());
        long existCartNo = memberBaseApi.getMemberBaseByCondition(dto);
        if (existCartNo > 0 && memberBaseVo.getCartNo() != null) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.EXISTS_CARTNO.getCode(),ResultCodeEnum.EXISTS_CARTNO.getMsg(), null), HttpStatus.OK);
        }
        if(memberBaseVo.getOperationType()!=null&&memberBaseVo.getOperationType()==1){
            String archivesNo = memberBaseVo.getArchivesInfo().getArchivesNo();
            if(memberBaseVo.getArchivesInfo()==null || StringUtils.isEmpty(archivesNo)) {
                return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.NO_MEDICAL_NO.getCode(), ResultCodeEnum.NO_MEDICAL_NO.getMsg(), null), HttpStatus.OK);
            }
            MemberChronicRelationDto memberChronicRelationDto = chronicRelationApi.selectByMedicalPref(dto.getOrgansign(),archivesNo);
            if (memberChronicRelationDto != null && !memberChronicRelationDto.getGuid().equals(memberBaseVo.getGuid())) {
                return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.EXISTS_MEDICAL_NO.getCode(),ResultCodeEnum.EXISTS_MEDICAL_NO.getMsg(), null), HttpStatus.OK);
            }
        }
        return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.SUCCESS.getCode(),ResultCodeEnum.SUCCESS.getMsg(), null), HttpStatus.OK);
    }

    @ApiOperation(value = "门店端:重置密码", notes = "总部端:重置密码", response = MemberBase.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberBase.class)})
    @RequestMapping(value = "/member/resetPassword",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> resetPassword(@RequestHeader("commonRequestModel") String commonRequestModel, @ApiParam(value = "会员基本信息", required = true) @RequestBody MemberBaseVo memberBaseVo) {
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        MemberBaseDto memberBaseDto = new MemberBaseDto();
        memberBaseDto.setUpdateUser(model.getEmployeeId());
        memberBaseDto.setGuid(memberBaseVo.getGuid());
        if (model.getBizModel() == 1) {
            memberBaseDto.setOrgansign(model.getOrganSign());
        }else{
            memberBaseDto.setOrgansign(memberBaseVo.getOrgansign());
        }
        memberBaseDto.setPasswd(MD5Util.getMD5("123456"));
        memberBaseApi.resetMemberPassword(memberBaseDto);
        return new ResponseEntity<>(ResultVO.createSuccess("重置成功"), HttpStatus.OK);

    }

    @ApiOperation(value = "门店端:修改会员积分", notes = "门店端:修改会员积分", response = MemberBase.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberBase.class)})
    @RequestMapping(value = "/member/editMemberPoint", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> editMemberPoint(@RequestHeader("commonRequestModel") String commonRequestModel,
                                                    @ApiParam(value = "积分修改", required = true) @RequestBody MemberChangePoint memberChangePoint) {
        if (memberChangePoint == null && memberChangePoint.getEditType() == null) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.NO_EDITTYPE.getCode(),ResultCodeEnum.NO_EDITTYPE.getMsg(), null), HttpStatus.OK);
        }
        if (memberChangePoint == null && memberChangePoint.getGuid() == null) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.NO_MEMBERID.getCode(), ResultCodeEnum.NO_MEMBERID.getMsg(),null), HttpStatus.OK);
        }
        if (memberChangePoint.getEditType() != 2) {
            if (memberChangePoint == null && memberChangePoint.getQuantity() == null) {
                return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.NO_OPERATOR_POINT.getCode(), ResultCodeEnum.NO_OPERATOR_POINT.getMsg(), null), HttpStatus.OK);
            }
        }
        CommonRequestModel requestModel = JSONObject.parseObject(commonRequestModel, CommonRequestModel.class);
        MemberChangePointDto memberChangePointDto = new MemberChangePointDto();
        BeanUtils.copyProperties(memberChangePoint, memberChangePointDto);
        memberChangePointDto.setOrganSign(requestModel.getOrganSign());
        memberChangePointDto.setOperator(requestModel.getEmployeeId());
        try {
            memberBaseApi.editMemberPoint(memberChangePointDto);
        } catch (Exception e) {
            logger.error("editMemberPoint error", e);
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR.getCode(),ResultCodeEnum.ERROR.getMsg(), null), HttpStatus.INTERNAL_SERVER_ERROR);
        }
        return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.SUCCESS.getCode(),ResultCodeEnum.SUCCESS.getMsg(), null), HttpStatus.OK);
    }

    @ApiOperation(value = "门店端:批量清零积分", notes = "门店端:批量清零积分", response = ResultVO.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class)})
    @RequestMapping(value = "/member/batchClearPoint", method = RequestMethod.POST)
    ResponseEntity<ResultVO> batchClearPoint(@RequestHeader("commonRequestModel") String commonRequestModel,
                                             @ApiParam(value = "批量积分清零条件", required = true)   @RequestBody MemberClearPointCondition memberClearPointCondition) {
        if (memberClearPointCondition == null || memberClearPointCondition.getType() == null) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.NO_CLEARPOINT_TYPE.getCode(), ResultCodeEnum.NO_CLEARPOINT_TYPE.getMsg(), null), HttpStatus.OK);
        }
        CommonRequestModel requestModel = JSONObject.parseObject(commonRequestModel, CommonRequestModel.class);
        memberClearPointCondition.setOrganSign(requestModel.getOrganSign());
        memberClearPointCondition.setEmployeeId(Integer.valueOf(requestModel.getEmployeeId()));
        boolean flag = memberBaseApi.batchClearPointByType(memberClearPointCondition);
        if (flag) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.SUCCESS.getCode(),ResultCodeEnum.SUCCESS.getMsg(), null), HttpStatus.OK);
        } else {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR.getCode(), ResultCodeEnum.ERROR.getMsg(),null), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @ApiOperation(value = "门店端:pos端购药赠险添加会员", notes = "门店端:pos端购药赠险添加会员", response = ResultVO.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class)})
    @RequestMapping(value = "/member/saveFromInsurance", method = RequestMethod.POST, produces = "application/json",
            consumes = "application/json")
    @RepeatSubmitValidation(resultType = 4)
    public ResponseEntity<ResultVO> saveFromInsurance(@RequestHeader("commonRequestModel") String commonRequestModel, @ApiParam(value = "会员基本信息", required = true) @RequestBody MemberBaseVo memberBaseVo) {
        CommonRequestModel model = JSONObject.parseObject(commonRequestModel, CommonRequestModel.class);
        memberBaseVo.setOrgansign(model.getOrganSign());
        memberBaseVo.setHeadquartersOrganSign(model.getHeadquartersOrganSign());
        memberBaseVo.setCreateUser(model.getEmployeeId());
        MemberBaseDto dto = new MemberBaseDto();
        BeanUtils.copyProperties(memberBaseVo, dto);
        return new ResponseEntity(memberBaseApi.saveFromInsurance(dto), HttpStatus.OK);
    }

    @ApiOperation(value = "修改会员最后消费时间", notes = "修改会员最后消费时间", response = String.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = String.class)})
    @RequestMapping(value = "/member/taskUpdateMemberLastConsumeTime",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> taskUpdateMemberLastConsumeTime(@RequestBody MemberBaseDto baseDto) {
        boolean flag = memberBaseApi.taskUpdateMemberLastConsumeTime(baseDto);
        return new ResponseEntity(ResultVO.createSuccess(flag), HttpStatus.OK);
    }

    @ApiOperation(value = "门店端:会员资料查询", notes = "门店端:会员资料查询", response = ResultVO.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class)})
    @RequestMapping(value = "/member/query", method = RequestMethod.POST, produces = "application/json",consumes = "application/json")
    @ResponseBody
    public ResultVO query(@RequestHeader("commonRequestModel") String commonRequestModel, @ApiParam(value = "会员基本信息", required = true) @RequestBody MemberDetailExportQueryVO memberDetailExportQueryVO) {
        CommonRequestModel model = JSONObject.parseObject(commonRequestModel, CommonRequestModel.class);
        MemberBaseDto baseDto = new MemberBaseDto();
        baseDto.setOrgansign(model.getOrganSign());
        baseDto.setPageNum(memberDetailExportQueryVO.getPage() == null ? 1 : memberDetailExportQueryVO.getPage());
        baseDto.setPageSize(memberDetailExportQueryVO.getRows() == null ? 10 : memberDetailExportQueryVO.getRows());
        baseDto.setSortWay(1);
        baseDto.setYn(1);
        baseDto.setVipLevelId(memberDetailExportQueryVO.getVipLevelId());
        baseDto.setFlag(memberDetailExportQueryVO.getFlag());
        //卡内余额
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportQueryVO.getMinTotalAmount())) {
            baseDto.setMinTotalAmount(memberDetailExportQueryVO.getMinTotalAmount());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportQueryVO.getMaxTotalAmount())) {
            baseDto.setMaxTotalAmount(memberDetailExportQueryVO.getMaxTotalAmount());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportQueryVO.getMixedQuery())) {
            baseDto.setMixedQuery(memberDetailExportQueryVO.getMixedQuery());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportQueryVO.getCartNo())) {
            baseDto.setCartNo(memberDetailExportQueryVO.getCartNo());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportQueryVO.getTelephone())) {
            baseDto.setTelephone(memberDetailExportQueryVO.getTelephone());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportQueryVO.getStartPoint())) {
            baseDto.setStartPoint(memberDetailExportQueryVO.getStartPoint());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportQueryVO.getEndPoint())) {
            baseDto.setEndPoint(memberDetailExportQueryVO.getEndPoint());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportQueryVO.getSidx())) {
            baseDto.setSidx(memberDetailExportQueryVO.getSidx());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportQueryVO.getSord())) {
            baseDto.setSord(memberDetailExportQueryVO.getSord());
        }
        if (memberDetailExportQueryVO.getWhiteList() != null) {
            baseDto.setWhiteList(memberDetailExportQueryVO.getWhiteList());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportQueryVO.getStartCreateDate())) {
            baseDto.setStartCreateDate(memberDetailExportQueryVO.getStartCreateDate());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportQueryVO.getEndCreateDate())) {
            baseDto.setEndCreateDate(memberDetailExportQueryVO.getEndCreateDate());
        }
        if (memberDetailExportQueryVO.getState() != null) {
            baseDto.setState(memberDetailExportQueryVO.getState());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportQueryVO.getCreateUserName())) {
            baseDto.setCreateUserName(memberDetailExportQueryVO.getCreateUserName());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportQueryVO.getChronicPref())) {
            baseDto.setPref(memberDetailExportQueryVO.getChronicPref());
        }
        baseDto.setStartAge(memberDetailExportQueryVO.getStartAge());
        baseDto.setEndAge(memberDetailExportQueryVO.getEndAge());
        baseDto.setStartBirthdayRange(memberDetailExportQueryVO.getStartBirthdayRange());
        baseDto.setEndBirthdayRange(memberDetailExportQueryVO.getEndBirthdayRange());
        return ResultVO.createSuccess(memberBaseApi.getMemberBaseListPager(baseDto));
    }

    /**
     * 根据条件导出会员信息
     */
    @ApiOperation(value = "门店端:会员资料导出", notes = "门店端:会员资料导出", response = ResultVO.class, tags = {"member",})
    @RequestMapping(value = "/member/exportExcel", method = RequestMethod.POST)
    public void exportExcelMember(HttpServletRequest request, HttpServletResponse response, @RequestHeader("commonRequestModel") String commonRequestModel,
                                  @ApiParam(value = "会员基本信息", required = true) @RequestBody MemberDetailExportExcelVO memberDetailExportExcelVO) {
        CommonRequestModel model = JSONObject.parseObject(commonRequestModel, CommonRequestModel.class);
        MemberBaseDto baseDto = new MemberBaseDto();
        baseDto.setOrgansign(model.getOrganSign());
        baseDto.setSortWay(1);
        baseDto.setYn(1);
        baseDto.setVipLevelId(memberDetailExportExcelVO.getVipLevelId());
        baseDto.setFlag(memberDetailExportExcelVO.getFlag());
        //卡内余额
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportExcelVO.getMinTotalAmount())) {
            baseDto.setMinTotalAmount(memberDetailExportExcelVO.getMinTotalAmount());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportExcelVO.getMaxTotalAmount())) {
            baseDto.setMaxTotalAmount(memberDetailExportExcelVO.getMaxTotalAmount());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportExcelVO.getMixedQuery())) {
            baseDto.setMixedQuery(memberDetailExportExcelVO.getMixedQuery());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportExcelVO.getCartNo())) {
            baseDto.setCartNo(memberDetailExportExcelVO.getCartNo());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportExcelVO.getTelephone())) {
            baseDto.setTelephone(memberDetailExportExcelVO.getTelephone());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportExcelVO.getStartPoint())) {
            baseDto.setStartPoint(memberDetailExportExcelVO.getStartPoint());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportExcelVO.getEndPoint())) {
            baseDto.setEndPoint(memberDetailExportExcelVO.getEndPoint());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportExcelVO.getSidx())) {
            baseDto.setSidx(memberDetailExportExcelVO.getSidx());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportExcelVO.getSord())) {
            baseDto.setSord(memberDetailExportExcelVO.getSord());
        }
        if (memberDetailExportExcelVO.getWhiteList() != null) {
            baseDto.setWhiteList(memberDetailExportExcelVO.getWhiteList());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportExcelVO.getStartCreateDate())) {
            baseDto.setStartCreateDate(memberDetailExportExcelVO.getStartCreateDate());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportExcelVO.getEndCreateDate())) {
            baseDto.setEndCreateDate(memberDetailExportExcelVO.getEndCreateDate());
        }
        if (memberDetailExportExcelVO.getState() != null) {
            baseDto.setState(memberDetailExportExcelVO.getState());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportExcelVO.getCreateUserName())) {
            baseDto.setCreateUserName(memberDetailExportExcelVO.getCreateUserName());
        }
        if (!org.springframework.util.StringUtils.isEmpty(memberDetailExportExcelVO.getChronicPref())) {
            baseDto.setPref(memberDetailExportExcelVO.getChronicPref());
        }
        baseDto.setPageNum(1);
        baseDto.setPageSize(200);
        List<MemberBaseDto> memberList = Lists.newArrayList();
        PageInfo<MemberBaseDto> memberBasePager = memberBaseApi.getMemberBaseListPager(baseDto);
        if (null != memberBasePager && CollectionUtils.isNotEmpty(memberBasePager.getList())) {
            memberList.addAll(memberBasePager.getList());
            int firstPage = 1;
            int pages = memberBasePager.getPages();
            /** 遍历查询数据 */
            while (firstPage < pages) {
                firstPage += 1;
                baseDto.setPageNum(firstPage);
                PageInfo<MemberBaseDto> baseListPager = memberBaseApi.getMemberBaseListPager(baseDto);
                memberList.addAll(baseListPager.getList());
            }
            // 会员手机号和身份证号脱敏
            List<MemberBaseDto> hasTelephone = memberList.stream().filter(dto -> StringUtils.isNotEmpty(dto.getTelephoneEncrypted())).collect(Collectors.toList());
            List<MemberBaseDto> hasIdCard = memberList.stream().filter(dto -> StringUtils.isNotEmpty(dto.getIdCardEncrypted())).collect(Collectors.toList());
            Map<String, String> telephoneMap = Maps.newHashMap();
            Map<String, String> idCardMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(hasTelephone)) {
                Set<String> telephoneTokens = hasTelephone.stream().map(member -> member.getTelephoneEncrypted()).collect(Collectors.toSet());
                telephoneMap = dataTokenService.getMemberSensitiveData(new ArrayList<>(telephoneTokens), Integer.valueOf(model.getEmployeeId()), DataTypeEnum.MOBILE_PHONE);
            }
            if (CollectionUtils.isNotEmpty(hasIdCard)) {
                Set<String> idCardTokens = hasIdCard.stream().map(member -> member.getIdCardEncrypted()).collect(Collectors.toSet());
                idCardMap = dataTokenService.getMemberSensitiveData(new ArrayList<>(idCardTokens), Integer.valueOf(model.getEmployeeId()), DataTypeEnum.IDENTITY_CARD);
            }

            for (MemberBaseDto memberBase : memberList) {
                if (memberBase.getState() == 1) {
                    memberBase.setStateName("启用");
                } else {
                    memberBase.setStateName("禁用");
                }
                if (memberBase.getSex() == 1) {
                    memberBase.setSexStr("男");
                } else {
                    memberBase.setSexStr("女");
                }
                if (StringUtil.isNotEmpty(memberBase.getTelephoneEncrypted())
                        && telephoneMap.containsKey(memberBase.getTelephoneEncrypted())) {
                    memberBase.setTelephone(telephoneMap.get(memberBase.getTelephoneEncrypted()));
                }
                if (StringUtil.isNotEmpty(memberBase.getIdCardEncrypted())
                        && idCardMap.containsKey(memberBase.getIdCardEncrypted())) {
                    memberBase.setIdCard(idCardMap.get(memberBase.getIdCardEncrypted()));
                }
            }
        }
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String extfilename = "会员资料" + df.format(new Date()) + ".xls";
        String sheetName = "会员资料";
        String[] headers = memberDetailExportExcelVO.getHeaders().split(",");
        String[] fieldNames = memberDetailExportExcelVO.getFieldNames().split(",");
        for (int i = 0; i < fieldNames.length; i++) {
            if (fieldNames[i].equals("state")) {
                fieldNames[i] = "stateName";
            }
            if (fieldNames[i].equals("sex")) {
                fieldNames[i] = "sexStr";
            }
            if (fieldNames[i].equals("discount")) {
                fieldNames[i] = "discountName";
            }
            if (fieldNames[i].equals("areaName") || fieldNames[i].equals("cityName") || fieldNames[i].equals("provinceName")) {
                fieldNames[i] = "provinceCityAreaName";
            }
        }
        try {
            ExportExcelUtil.createExcelWithDatePattern(response, request, extfilename, sheetName, headers, fieldNames, memberList, true, "yyyy-MM-dd");
            logger.info("门店端/单店:会员资料数据导出成功。");
        } catch (Exception e) {
            logger.error("门店端/单店:会员资料数据导出异常:{}", e);
        }
    }

    /**
     * 会员积分列表导出
     * @param request
     * @param response
     * @param exportVo
     */
    @ApiOperation(value = "门店端:会员积分列表导出", notes = "门店端:会员积分列表导出", tags = {"member",})
    @RequestMapping(value = "/member/exportMemberPointExcel", method = {RequestMethod.POST})
    public void exportMemberPoint(HttpServletRequest request, HttpServletResponse response , @RequestHeader("commonRequestModel") String commonRequestModel,
                                  @ApiParam(value = "会员基本信息", required = true) @RequestBody MemberDetailExportExcelVO exportVo) {
        CommonRequestModel model = JSONObject.parseObject(commonRequestModel, CommonRequestModel.class);
        MemberBaseDto baseDto = new MemberBaseDto();
        baseDto.setOrgansign(model.getOrganSign());
        baseDto.setSortWay(1);
        baseDto.setYn(1);
        baseDto.setVipLevelId(exportVo.getVipLevelId());
        baseDto.setFlag(exportVo.getFlag());
        if (StringUtils.isEmpty(exportVo.getMixedQuery())) {
            baseDto.setMixedQuery(exportVo.getMixedQuery());
        }
        if (StringUtils.isEmpty(exportVo.getStartPoint())) {
            baseDto.setStartPoint(exportVo.getStartPoint());
        }
        if (StringUtils.isEmpty(exportVo.getEndPoint())) {
            baseDto.setEndPoint(exportVo.getEndPoint());
        }
        baseDto.setPageNum(1);
        baseDto.setPageSize(200);
        List<MemberBaseDto> memberList = Lists.newArrayList();
        PageInfo<MemberBaseDto> memberBasePager = memberBaseApi.getMemberBaseListPager(baseDto);
        if (null != memberBasePager && CollectionUtils.isNotEmpty(memberBasePager.getList())) {
            memberList.addAll(memberBasePager.getList());
            int firstPage = 1;
            int pages = memberBasePager.getPages();
            /** 遍历查询数据 */
            while (firstPage < pages) {
                firstPage += 1;
                baseDto.setPageNum(firstPage);
                PageInfo<MemberBaseDto> baseListPager = memberBaseApi.getMemberBaseListPager(baseDto);
                memberList.addAll(baseListPager.getList());
            }
            logger.info("exportMemberPoint size:{}", memberList.size());
            // 会员手机号脱敏
            List<MemberBaseDto> hasTelephone = memberList.stream().filter(dto -> StringUtils.isNotEmpty(dto.getTelephoneEncrypted())).collect(Collectors.toList());
            Map<String, String> telephoneMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(hasTelephone)) {
                Set<String> telephoneTokens = hasTelephone.stream().map(member -> member.getTelephoneEncrypted()).collect(Collectors.toSet());
                telephoneMap = dataTokenService.getMemberSensitiveData(new ArrayList<>(telephoneTokens), Integer.valueOf(model.getEmployeeId()), DataTypeEnum.MOBILE_PHONE);
            }
            for (MemberBaseDto memberBase : memberList) {
                if (StringUtil.isNotEmpty(memberBase.getTelephoneEncrypted())
                        && telephoneMap.containsKey(memberBase.getTelephoneEncrypted())) {
                    memberBase.setTelephone(telephoneMap.get(memberBase.getTelephoneEncrypted()));
                }
            }
        }
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String extfileName = "会员积分" + df.format(new Date()) + ".xls";
        String sheetName = "会员积分";
        String [] headers = new String[]{"会员手机号","会员姓名", "会员等级","会员卡号","来源门店","门店编码","可用积分","累计积分"};
        String [] fieldNames = new String[]{"telephone","name", "levelName","cartNo","sourceDrugstoreName","organsign","point","allPoint"};
        try {
            ExportExcelUtil.createExcelWithDatePattern(response, request, extfileName, sheetName, headers, fieldNames, memberList, true, "yyyy-MM-dd");
        } catch (Exception e) {
            logger.error("门店端/单店:会员积分数据导出异常:{}",e);
        }
    }



    public void syncMqPush(String organsign) {
        JSONObject msg = new JSONObject();
        String[] table = {"saas_member_base"};
        msg.put("code", "sync");
        msg.put("tables", table);
        messagePushApi.sendMsgByOrganSign(organsign, msg.toString());
    }

}
