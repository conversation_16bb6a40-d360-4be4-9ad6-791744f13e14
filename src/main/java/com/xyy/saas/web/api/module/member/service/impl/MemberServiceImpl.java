package com.xyy.saas.web.api.module.member.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.member.core.api.MemberBaseApi;
import com.xyy.saas.member.core.api.MemberBaseRelationApi;
import com.xyy.saas.member.core.api.MemberPointHistoryApi;
import com.xyy.saas.member.core.dto.MemberBaseRelationDto;
import com.xyy.saas.member.core.dto.MemberChangePointDto;
import com.xyy.saas.member.core.dto.MemberPointHistoryConditionDto;
import com.xyy.saas.web.api.module.member.service.DrugstoreService;
import com.xyy.saas.web.api.module.member.service.MemberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class MemberServiceImpl implements MemberService {

    @Reference(version = "0.0.1")
    private MemberBaseApi memberBaseApi;

    @Reference(version = "0.0.1")
    private MemberBaseRelationApi relationApi;

    @Reference(version = "0.0.1")
    private MemberPointHistoryApi historyApi;

    @Autowired
    private DrugstoreService drugstoreService;

    @Override
    public void editMemberPoint(MemberChangePointDto changePointDto) {
       String guid =  changePointDto.getGuid();
       MemberBaseRelationDto relationDto = relationApi.getMemberIdByGuid(guid);
       if(relationDto.getMemberId() != null){
           changePointDto.setMemberId(relationDto.getMemberId());
       }
       memberBaseApi.editMemberPoint(changePointDto);
    }

    @Override
    public PageInfo<MemberPointHistoryConditionDto> getMemberPointHistoryPager(MemberPointHistoryConditionDto dto) {
        String guid =  dto.getGuid();
        MemberBaseRelationDto relationDto = relationApi.getMemberIdByGuid(guid);
        if(relationDto.getMemberId() != null){
            dto.setMemberId(relationDto.getMemberId());
        }
        return historyApi.getMemberPointHistoryPager(dto,dto.getPage());
    }
}
