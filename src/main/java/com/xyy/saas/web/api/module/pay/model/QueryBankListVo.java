package com.xyy.saas.web.api.module.pay.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/5/15 17:38
 */
@ApiModel(description = "编码查询")
@Data
public class QueryBankListVo {
    @ApiModelProperty(value = "支行名称")
    private String branchName;
    @ApiModelProperty(value = "页码")
    private int pageNo;
    @ApiModelProperty(value = "每页大小")
    private int pageSize;
}
