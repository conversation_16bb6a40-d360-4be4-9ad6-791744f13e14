package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * InventoryVerifyVo
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T17:19:18.933+08:00")

public class InventoryVerifyVo   {
  @JsonProperty("id")
  private Integer id = null;

  @JsonProperty("pref")
  private String pref = null;

  @JsonProperty("inventoryPlanningPref")
  private String inventoryPlanningPref = null;

  @JsonProperty("createUser")
  private String createUser = null;

  @JsonProperty("createTime")
  private Date createTime = null;

  @JsonProperty("updateUser")
  private String updateUser = null;

  @JsonProperty("updateTime")
  private Date updateTime = null;

  @JsonProperty("remark")
  private String remark = null;

  @JsonProperty("status")
  private Integer status = null;

  @JsonProperty("yn")
  private byte yn = 1;

  @JsonProperty("baseVersion")
  private String baseVersion = null;

  @JsonProperty("organsign")
  private String organsign = null;

  @JsonProperty("ticketUser")
  private String ticketUser = null;

  @JsonProperty("ticketTime")
  private String ticketTime = null;

  @JsonProperty("ipvos")
  private List<InventoryPlanDetailVo> ipvos = null;

  public InventoryVerifyVo id(Integer id) {
    this.id = id;
    return this;
  }

   /**
   * 主键id
   * @return id
  **/
  @ApiModelProperty(value = "主键id")


  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public InventoryVerifyVo pref(String pref) {
    this.pref = pref;
    return this;
  }

   /**
   * 编号
   * @return pref
  **/
  @ApiModelProperty(value = "编号")


  public String getPref() {
    return pref;
  }

  public void setPref(String pref) {
    this.pref = pref;
  }

  public InventoryVerifyVo inventoryPlanningPref(String inventoryPlanningPref) {
    this.inventoryPlanningPref = inventoryPlanningPref;
    return this;
  }

   /**
   * 盘点计划单编号
   * @return inventoryPlanningPref
  **/
  @ApiModelProperty(value = "盘点计划单编号")


  public String getInventoryPlanningPref() {
    return inventoryPlanningPref;
  }

  public void setInventoryPlanningPref(String inventoryPlanningPref) {
    this.inventoryPlanningPref = inventoryPlanningPref;
  }

  public InventoryVerifyVo createUser(String createUser) {
    this.createUser = createUser;
    return this;
  }

   /**
   * 创建人
   * @return createUser
  **/
  @ApiModelProperty(value = "创建人")


  public String getCreateUser() {
    return createUser;
  }

  public void setCreateUser(String createUser) {
    this.createUser = createUser;
  }

  public InventoryVerifyVo createTime(Date createTime) {
    this.createTime = createTime;
    return this;
  }

   /**
   * 创建时间
   * @return createTime
  **/
  @ApiModelProperty(value = "创建时间")

  @Valid

  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public InventoryVerifyVo updateUser(String updateUser) {
    this.updateUser = updateUser;
    return this;
  }

   /**
   * 更新人
   * @return updateUser
  **/
  @ApiModelProperty(value = "更新人")


  public String getUpdateUser() {
    return updateUser;
  }

  public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
  }

  public InventoryVerifyVo updateTime(Date updateTime) {
    this.updateTime = updateTime;
    return this;
  }

   /**
   * 更新时间
   * @return updateTime
  **/
  @ApiModelProperty(value = "更新时间")

  @Valid

  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public InventoryVerifyVo remark(String remark) {
    this.remark = remark;
    return this;
  }

   /**
   * 备注
   * @return remark
  **/
  @ApiModelProperty(value = "备注")


  public String getRemark() {
    return remark;
  }

  public void setRemark(String remark) {
    this.remark = remark;
  }

  public InventoryVerifyVo status(Integer status) {
    this.status = status;
    return this;
  }

   /**
   * 状态
   * @return status
  **/
  @ApiModelProperty(value = "状态")


  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public InventoryVerifyVo yn(byte yn) {
    this.yn = yn;
    return this;
  }

   /**
   * 是否删除
   * @return yn
  **/
  @ApiModelProperty(value = "是否删除")
  
  public byte getYn() {
    return yn;
  }

  public void setYn(byte yn) {
    this.yn = yn;
  }

  public InventoryVerifyVo baseVersion(String baseVersion) {
    this.baseVersion = baseVersion;
    return this;
  }

   /**
   * 版本号
   * @return baseVersion
  **/
  @ApiModelProperty(value = "版本号")


  public String getBaseVersion() {
    return baseVersion;
  }

  public void setBaseVersion(String baseVersion) {
    this.baseVersion = baseVersion;
  }

  public InventoryVerifyVo organsign(String organsign) {
    this.organsign = organsign;
    return this;
  }

   /**
   * 机构代码
   * @return organsign
  **/
  @ApiModelProperty(value = "机构代码")


  public String getOrgansign() {
    return organsign;
  }

  public void setOrgansign(String organsign) {
    this.organsign = organsign;
  }

  public InventoryVerifyVo ticketUser(String ticketUser) {
    this.ticketUser = ticketUser;
    return this;
  }

   /**
   * 开票员
   * @return ticketUser
  **/
  @ApiModelProperty(value = "开票员")


  public String getTicketUser() {
    return ticketUser;
  }

  public void setTicketUser(String ticketUser) {
    this.ticketUser = ticketUser;
  }

  public InventoryVerifyVo ticketTime(String ticketTime) {
    this.ticketTime = ticketTime;
    return this;
  }

   /**
   * 开票时间
   * @return ticketTime
  **/
  @ApiModelProperty(value = "开票时间")


  public String getTicketTime() {
    return ticketTime;
  }

  public void setTicketTime(String ticketTime) {
    this.ticketTime = ticketTime;
  }

  public InventoryVerifyVo ipvos(List<InventoryPlanDetailVo> ipvos) {
    this.ipvos = ipvos;
    return this;
  }

  public InventoryVerifyVo addIpvosItem(InventoryPlanDetailVo ipvosItem) {
    if (this.ipvos == null) {
      this.ipvos = new ArrayList<InventoryPlanDetailVo>();
    }
    this.ipvos.add(ipvosItem);
    return this;
  }

   /**
   * Get ipvos
   * @return ipvos
  **/
  @ApiModelProperty(value = "")

  @Valid

  public List<InventoryPlanDetailVo> getIpvos() {
    return ipvos;
  }

  public void setIpvos(List<InventoryPlanDetailVo> ipvos) {
    this.ipvos = ipvos;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InventoryVerifyVo inventoryVerifyVo = (InventoryVerifyVo) o;
    return Objects.equals(this.id, inventoryVerifyVo.id) &&
        Objects.equals(this.pref, inventoryVerifyVo.pref) &&
        Objects.equals(this.inventoryPlanningPref, inventoryVerifyVo.inventoryPlanningPref) &&
        Objects.equals(this.createUser, inventoryVerifyVo.createUser) &&
        Objects.equals(this.createTime, inventoryVerifyVo.createTime) &&
        Objects.equals(this.updateUser, inventoryVerifyVo.updateUser) &&
        Objects.equals(this.updateTime, inventoryVerifyVo.updateTime) &&
        Objects.equals(this.remark, inventoryVerifyVo.remark) &&
        Objects.equals(this.status, inventoryVerifyVo.status) &&
        Objects.equals(this.yn, inventoryVerifyVo.yn) &&
        Objects.equals(this.baseVersion, inventoryVerifyVo.baseVersion) &&
        Objects.equals(this.organsign, inventoryVerifyVo.organsign) &&
        Objects.equals(this.ticketUser, inventoryVerifyVo.ticketUser) &&
        Objects.equals(this.ticketTime, inventoryVerifyVo.ticketTime) &&
        Objects.equals(this.ipvos, inventoryVerifyVo.ipvos);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, pref, inventoryPlanningPref, createUser, createTime, updateUser, updateTime, remark, status, yn, baseVersion, organsign, ticketUser, ticketTime, ipvos);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InventoryVerifyVo {\n");

    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    pref: ").append(toIndentedString(pref)).append("\n");
    sb.append("    inventoryPlanningPref: ").append(toIndentedString(inventoryPlanningPref)).append("\n");
    sb.append("    createUser: ").append(toIndentedString(createUser)).append("\n");
    sb.append("    createTime: ").append(toIndentedString(createTime)).append("\n");
    sb.append("    updateUser: ").append(toIndentedString(updateUser)).append("\n");
    sb.append("    updateTime: ").append(toIndentedString(updateTime)).append("\n");
    sb.append("    remark: ").append(toIndentedString(remark)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    yn: ").append(toIndentedString(yn)).append("\n");
    sb.append("    baseVersion: ").append(toIndentedString(baseVersion)).append("\n");
    sb.append("    organsign: ").append(toIndentedString(organsign)).append("\n");
    sb.append("    ticketUser: ").append(toIndentedString(ticketUser)).append("\n");
    sb.append("    ticketTime: ").append(toIndentedString(ticketTime)).append("\n");
    sb.append("    ipvos: ").append(toIndentedString(ipvos)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

