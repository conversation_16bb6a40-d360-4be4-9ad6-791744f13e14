package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.web.api.module.product.model.ImportMedicalVO;
import com.xyy.saas.web.api.module.product.model.UploadMedicalVO;
import io.swagger.annotations.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @title: MedicalProductApiSwag
 * @date 2019-09-07  20:43
 * @description: 医保商品swag
 */

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-09-07T21:07:46.794+08:00")
@Api(value = "MedicalProductApiSwag", description = "医保商品关系API接口")
@RequestMapping("/product/medical")
public interface MedicalProductApiSwag {


    /**
     * 查询未同步的医保商品列表
     */
    @ApiOperation(value = "查询未同步的医保商品列表", notes = "查询未同步的医保商品", response = ResultVO.class, tags={ "医保商品关系API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/syncMedicalProducts",method = RequestMethod.POST)
    public ResultVO syncMedicalProducts(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody UploadMedicalVO uploadMedicalVO);

    /**
     * 同步数据接口
     */
    @ApiOperation(value = "同步数据接口", notes = "同步数据接口", response = ResultVO.class, tags={ "医保商品关系API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ApiImplicitParams({
            @ApiImplicitParam(name="organSign", value="机构标识", required=true, paramType="query", dataType = "string"),
            @ApiImplicitParam(name="baseVersion", value="最大版本号", required=true, paramType="query", dataType = "Integer"),
            @ApiImplicitParam(name="count", value="同步数量", required=true, paramType="query", dataType = "Integer")
    })
    @ResponseBody
    @RequestMapping(value = "/syncData",method = RequestMethod.POST)
    public ResultVO syncData(HttpServletRequest request, String organSign, Integer baseVersion, Integer count);

  /**
     * 查询未同步的医保商品列表
     */
    @ApiOperation(value = "pos端上传已经匹配好的医保商品", notes = "pos端上传已经匹配好的医保商品", response = ResultVO.class, tags={ "医保商品关系API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/uploadMedicalProducts",method = RequestMethod.POST)
    public ResultVO uploadMedicalProducts(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody UploadMedicalVO uploadMedicalVO);

    /**
     * 查询未同步的医保商品列表
     */
    @ApiOperation(value = "导入医保商品", notes = "导入医保商品", response = ResultVO.class, tags={ "医保商品关系API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/banchImportMedicalData",method = RequestMethod.POST)
    public ResultVO banchImportMedicalData(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody ImportMedicalVO importMedicalVO);


}
