package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * InventoryPlanDetailVo
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T17:19:18.933+08:00")

public class InventoryPlanDetailVo   implements Serializable{
    private static final long serialVersionUID = 3158111331162907620L;
  @JsonProperty("id")
  private Integer id = null;

  @JsonProperty("planningPref")
  private String planningPref = null;

  @JsonProperty("createUser")
  private String createUser = null;

  @JsonProperty("createTime")
  private Date createTime = null;

  @JsonProperty("updateUser")
  private String updateUser = null;

  @JsonProperty("updateTime")
  private Date updateTime = null;

  @JsonProperty("yn")
  private byte yn = 1;

  @JsonProperty("baseVersion")
  private String baseVersion = null;

  @JsonProperty("organsign")
  private String organsign = null;

  @JsonProperty("lotNumber")
  private String lotNumber = null;

  @JsonProperty("lotnumberPref")
  private String lotnumberPref = null;

  @JsonProperty("extractStatus")
  private Integer extractStatus = null;

  @JsonProperty("inventory")
  private BigDecimal inventory = null;

  @JsonProperty("actualStore")
  private BigDecimal actualStore = null;

  @JsonProperty("profitLossQuantity")
  private BigDecimal profitLossQuantity = null;

  @JsonProperty("remark")
  private String remark = null;

  public InventoryPlanDetailVo id(Integer id) {
    this.id = id;
    return this;
  }

   /**
   * 主键id
   * @return id
  **/
  @ApiModelProperty(value = "主键id")


  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public InventoryPlanDetailVo planningPref(String planningPref) {
    this.planningPref = planningPref;
    return this;
  }

   /**
   * 盘点表编号
   * @return planningPref
  **/
  @ApiModelProperty(value = "盘点表编号")


  public String getPlanningPref() {
    return planningPref;
  }

  public void setPlanningPref(String planningPref) {
    this.planningPref = planningPref;
  }

  public InventoryPlanDetailVo createUser(String createUser) {
    this.createUser = createUser;
    return this;
  }

   /**
   * 创建人
   * @return createUser
  **/
  @ApiModelProperty(value = "创建人")


  public String getCreateUser() {
    return createUser;
  }

  public void setCreateUser(String createUser) {
    this.createUser = createUser;
  }

  public InventoryPlanDetailVo createTime(Date createTime) {
    this.createTime = createTime;
    return this;
  }

   /**
   * 创建时间
   * @return createTime
  **/
  @ApiModelProperty(value = "创建时间")

  @Valid

  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public InventoryPlanDetailVo updateUser(String updateUser) {
    this.updateUser = updateUser;
    return this;
  }

   /**
   * 更新人
   * @return updateUser
  **/
  @ApiModelProperty(value = "更新人")


  public String getUpdateUser() {
    return updateUser;
  }

  public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
  }

  public InventoryPlanDetailVo updateTime(Date updateTime) {
    this.updateTime = updateTime;
    return this;
  }

   /**
   * 更新时间
   * @return updateTime
  **/
  @ApiModelProperty(value = "更新时间")

  @Valid

  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public InventoryPlanDetailVo yn(byte yn) {
    this.yn = yn;
    return this;
  }

   /**
   * 是否删除
   * @return yn
  **/
  @ApiModelProperty(value = "是否删除")

  public byte getYn() {
    return yn;
  }

  public void setYn(byte yn) {
    this.yn = yn;
  }

  public InventoryPlanDetailVo baseVersion(String baseVersion) {
    this.baseVersion = baseVersion;
    return this;
  }

   /**
   * 版本号
   * @return baseVersion
  **/
  @ApiModelProperty(value = "版本号")


  public String getBaseVersion() {
    return baseVersion;
  }

  public void setBaseVersion(String baseVersion) {
    this.baseVersion = baseVersion;
  }

  public InventoryPlanDetailVo organsign(String organsign) {
    this.organsign = organsign;
    return this;
  }

   /**
   * 机构代码
   * @return organsign
  **/
  @ApiModelProperty(value = "机构代码")


  public String getOrgansign() {
    return organsign;
  }

  public void setOrgansign(String organsign) {
    this.organsign = organsign;
  }

  public InventoryPlanDetailVo lotNumber(String lotNumber) {
    this.lotNumber = lotNumber;
    return this;
  }

   /**
   * 批号
   * @return lotNumber
  **/
  @ApiModelProperty(value = "批号")


  public String getLotNumber() {
    return lotNumber;
  }

  public void setLotNumber(String lotNumber) {
    this.lotNumber = lotNumber;
  }

  public InventoryPlanDetailVo lotnumberPref(String lotnumberPref) {
    this.lotnumberPref = lotnumberPref;
    return this;
  }

   /**
   * 批号表编号
   * @return lotnumberPref
  **/
  @ApiModelProperty(value = "批号表编号")


  public String getLotnumberPref() {
    return lotnumberPref;
  }

  public void setLotnumberPref(String lotnumberPref) {
    this.lotnumberPref = lotnumberPref;
  }

  public InventoryPlanDetailVo extractStatus(Integer extractStatus) {
    this.extractStatus = extractStatus;
    return this;
  }

   /**
   * 提取状态
   * @return extractStatus
  **/
  @ApiModelProperty(value = "提取状态")


  public Integer getExtractStatus() {
    return extractStatus;
  }

  public void setExtractStatus(Integer extractStatus) {
    this.extractStatus = extractStatus;
  }

  public InventoryPlanDetailVo inventory(BigDecimal inventory) {
    this.inventory = inventory;
    return this;
  }

   /**
   * 库存数量
   * @return inventory
  **/
  @ApiModelProperty(value = "库存数量")

  @Valid

  public BigDecimal getInventory() {
    return inventory;
  }

  public void setInventory(BigDecimal inventory) {
    this.inventory = inventory;
  }

  public InventoryPlanDetailVo actualStore(BigDecimal actualStore) {
    this.actualStore = actualStore;
    return this;
  }

   /**
   * 实际数量
   * @return actualStore
  **/
  @ApiModelProperty(value = "实际数量")

  @Valid

  public BigDecimal getActualStore() {
    return actualStore;
  }

  public void setActualStore(BigDecimal actualStore) {
    this.actualStore = actualStore;
  }

  public InventoryPlanDetailVo profitLossQuantity(BigDecimal profitLossQuantity) {
    this.profitLossQuantity = profitLossQuantity;
    return this;
  }

   /**
   * 损溢数量
   * @return profitLossQuantity
  **/
  @ApiModelProperty(value = "损溢数量")

  @Valid

  public BigDecimal getProfitLossQuantity() {
    return profitLossQuantity;
  }

  public void setProfitLossQuantity(BigDecimal profitLossQuantity) {
    this.profitLossQuantity = profitLossQuantity;
  }

  public InventoryPlanDetailVo remark(String remark) {
    this.remark = remark;
    return this;
  }

   /**
   * 备注
   * @return remark
  **/
  @ApiModelProperty(value = "备注")


  public String getRemark() {
    return remark;
  }

  public void setRemark(String remark) {
    this.remark = remark;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InventoryPlanDetailVo inventoryPlanDetailVo = (InventoryPlanDetailVo) o;
    return Objects.equals(this.id, inventoryPlanDetailVo.id) &&
        Objects.equals(this.planningPref, inventoryPlanDetailVo.planningPref) &&
        Objects.equals(this.createUser, inventoryPlanDetailVo.createUser) &&
        Objects.equals(this.createTime, inventoryPlanDetailVo.createTime) &&
        Objects.equals(this.updateUser, inventoryPlanDetailVo.updateUser) &&
        Objects.equals(this.updateTime, inventoryPlanDetailVo.updateTime) &&
        Objects.equals(this.yn, inventoryPlanDetailVo.yn) &&
        Objects.equals(this.baseVersion, inventoryPlanDetailVo.baseVersion) &&
        Objects.equals(this.organsign, inventoryPlanDetailVo.organsign) &&
        Objects.equals(this.lotNumber, inventoryPlanDetailVo.lotNumber) &&
        Objects.equals(this.lotnumberPref, inventoryPlanDetailVo.lotnumberPref) &&
        Objects.equals(this.extractStatus, inventoryPlanDetailVo.extractStatus) &&
        Objects.equals(this.inventory, inventoryPlanDetailVo.inventory) &&
        Objects.equals(this.actualStore, inventoryPlanDetailVo.actualStore) &&
        Objects.equals(this.profitLossQuantity, inventoryPlanDetailVo.profitLossQuantity) &&
        Objects.equals(this.remark, inventoryPlanDetailVo.remark);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, planningPref, createUser, createTime, updateUser, updateTime, yn, baseVersion, organsign, lotNumber, lotnumberPref, extractStatus, inventory, actualStore, profitLossQuantity, remark);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InventoryPlanDetailVo {\n");

    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    planningPref: ").append(toIndentedString(planningPref)).append("\n");
    sb.append("    createUser: ").append(toIndentedString(createUser)).append("\n");
    sb.append("    createTime: ").append(toIndentedString(createTime)).append("\n");
    sb.append("    updateUser: ").append(toIndentedString(updateUser)).append("\n");
    sb.append("    updateTime: ").append(toIndentedString(updateTime)).append("\n");
    sb.append("    yn: ").append(toIndentedString(yn)).append("\n");
    sb.append("    baseVersion: ").append(toIndentedString(baseVersion)).append("\n");
    sb.append("    organsign: ").append(toIndentedString(organsign)).append("\n");
    sb.append("    lotNumber: ").append(toIndentedString(lotNumber)).append("\n");
    sb.append("    lotnumberPref: ").append(toIndentedString(lotnumberPref)).append("\n");
    sb.append("    extractStatus: ").append(toIndentedString(extractStatus)).append("\n");
    sb.append("    inventory: ").append(toIndentedString(inventory)).append("\n");
    sb.append("    actualStore: ").append(toIndentedString(actualStore)).append("\n");
    sb.append("    profitLossQuantity: ").append(toIndentedString(profitLossQuantity)).append("\n");
    sb.append("    remark: ").append(toIndentedString(remark)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

