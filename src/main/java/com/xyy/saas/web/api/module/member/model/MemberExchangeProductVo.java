package com.xyy.saas.web.api.module.member.model;

import com.xyy.saas.product.core.dto.SpecialProductUpdateDto;

import java.math.BigDecimal;
import java.util.List;

/**
 * 积分兑换商品规则
 * <AUTHOR>
 * @date  2018/02/03
 */
public class MemberExchangeProductVo {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 商品编号
     */
    private String productPref;

    /**
     * 商品信息
     */
    private String commonName;    //商品信息(混合查询)

    private String productName;
    /**
     * 商品分类
     */
    private Integer systemType;

    /**
     * 所属门店
     */
    private String organSign;

    /**
     * 自定义分类
     */
    private Integer productType;

    /**
     * 积分商品
     */
    private Integer scoreProductYn;

    /**
     * 积分
     */
    private BigDecimal integral;

    /**
     * 是否可用 0 停用 1 启用
     */
    private Integer yn;

    /**
     * 编号集合
     */
    private List<String> prefList;

    /**
     * 分页数字
     */
    private Integer pageNum;

    /**
     * 分页编码
     */
    private Integer pageSize;

    private List<MemberExchangeProductVo> exchangeProductList;

    private List<SpecialProductUpdateDto> productUpdateDtos;

    /**
     * 是否隐藏门店
     */
    private Byte isDrugstoreHidden;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public BigDecimal getIntegral() {
        return integral;
    }

    public void setIntegral(BigDecimal integral) {
        this.integral = integral;
    }

    public String getProductPref() {
        return productPref;
    }

    public void setProductPref(String productPref) {
        this.productPref = productPref;
    }

    public Integer getSystemType() {
        return systemType;
    }

    public void setSystemType(Integer systemType) {
        this.systemType = systemType;
    }

    public Integer getProductType() {
        return productType;
    }

    public void setProductType(Integer productType) {
        this.productType = productType;
    }

    public Integer getYn() {
        return yn;
    }

    public void setYn(Integer yn) {
        this.yn = yn;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public List<String> getPrefList() {
        return prefList;
    }

    public void setPrefList(List<String> prefList) {
        this.prefList = prefList;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<MemberExchangeProductVo> getExchangeProductList() {
        return exchangeProductList;
    }

    public Integer getScoreProductYn() {
        return scoreProductYn;
    }

    public void setScoreProductYn(Integer scoreProductYn) {
        this.scoreProductYn = scoreProductYn;
    }

    public void setExchangeProductList(List<MemberExchangeProductVo> exchangeProductList) {
        this.exchangeProductList = exchangeProductList;
    }

    public List<SpecialProductUpdateDto> getProductUpdateDtos() {
        return productUpdateDtos;
    }

    public void setProductUpdateDtos(List<SpecialProductUpdateDto> productUpdateDtos) {
        this.productUpdateDtos = productUpdateDtos;
    }

    public Byte getIsDrugstoreHidden() {
        return isDrugstoreHidden;
    }

    public void setIsDrugstoreHidden(Byte isDrugstoreHidden) {
        this.isDrugstoreHidden = isDrugstoreHidden;
    }

}
