package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

@ApiModel(description = "会员日")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-08-29T19:36:06.377+08:00")
public class MemberPreferentialDaySaveVo {
	/**
     * 主键id
     */
    @ApiModelProperty(value = "主键id", required = true, example = "1")
    private Long id;

    /**
     * guid
     */
    @ApiModelProperty(value = "guid", required = true, example = "b9e9c7e7-ca4b-11e9-a2a1-0235d2b38928")
    private String guid;

    /**
     * 会员等级id
     */
    @ApiModelProperty(value = "会员日商品选择", required = true, example = "12")
    private Long memberLevelId;

    /**
     * 模板名称
     */
    @ApiModelProperty(value = "模板名称", required = true, example = "模板123")
    private String templeteName;

    /**
     * 混合查询
     */
    @ApiModelProperty(value = "模板名称,混合查询", required = true, example = "模板123, 模板12, 模板, MB")
    private String mixedQuery;
    
    /**
     * 月会员日
     */
    @ApiModelProperty(value = "会员日商品选择", required = true, example = "11")
    private Integer monthPreferentialDay;

    /**
     * 月会员日积分倍数
     */
    @ApiModelProperty(value = "会员日商品选择", required = true, example = "4.1")
    private BigDecimal monthPreferentialIntegralMultiple;

    /**
     * 月会员日折扣比率
     */
    @ApiModelProperty(value = "会员日商品选择", required = true, example = "1")
    private Integer monthPreferentialDiscountRatio;

    /**
     * 月会员日是否与会员等级折扣同时生效 0是 1否
     */
    @ApiModelProperty(value = "月会员日是否与会员等级折扣同时生效 1是 0否", required = true, example = "1")
    private Byte monthLevenYn;

    /**
     * 周会员日
     */
    @ApiModelProperty(value = "周会员日", required = true, example = "2")
    private Integer weekPreferentialDay;

    /**
     * 周会员日积分倍数
     */
    @ApiModelProperty(value = "周会员日积分倍数", required = true, example = "3.1")
    private BigDecimal weekPreferentialIntegralMultiple;

    /**
     * 月会员日折扣比率
     */
    @ApiModelProperty(value = "月会员日折扣比率", required = true, example = "1")
    private Integer weekPreferentialDiscountRatio;

    /**
     * 周会员日是否与会员等级折扣同时生效 0是 1否
     */
    @ApiModelProperty(value = "周会员日是否与会员等级折扣同时生效 1是 0否", required = true, example = "0")
    private Byte weekLevenYn;

    /**
     * 月会员日和周会员日的优先级  0 月优先 1 周优先
     */
    @ApiModelProperty(value = "月会员日和周会员日的优先级  0 月优先 1 周优先", required = true, example = "1")
    private Byte monthWeekPriority;

    /**
     * 会员生日当天 0代表是
     */
    @ApiModelProperty(value = "会员生日当天 0代表是", required = true, example = "0")
    private Integer birthdayPreferentialDay;

    /**
     * 会员生日积分倍数
     */
    @ApiModelProperty(value = "会员生日积分倍数", required = true, example = "1")
    private BigDecimal birthdayPreferentialIntegralMultiple;

    /**
     * 会员生日折扣比率
     */
    @ApiModelProperty(value = "会员生日折扣比率", required = true, example = "1")
    private Integer birthdayPreferentialDiscountRatio;

    /**
     * 会员生日当天是否与会员等级折扣同时生效 0是 1否
     */
    @ApiModelProperty(value = "会员生日当天是否与会员等级折扣同时生效 1是 0否", required = true, example = "1")
    private Byte birthdayLevenYn;


    /**
     * 商品选择模式  1 按商品选择，2 按商品分类选择，3 按自定义分类选择，4 按ABC分类选择，5 按货位选择，6 按毛利率选择，1 全部商品，2 部分商品，3 仅设置不参与会员日的商品
     */
    @ApiModelProperty(value = "商品选择模式  1 按商品选择，2 按商品分类选择，3 按自定义分类选择，4 按ABC分类选择，5 按货位选择，6 按毛利率选择，1 全部商品，2 部分商品，3 仅设置不参与会员日的商品", required = true, example = "1")
    private Integer productChoiceModel;

    /**
     * 特价商品是否参与活动 0参与，1不参与
     */
    @ApiModelProperty(value = "特价商品是否参与活动 1参与，0不参与", required = true, example = "1")
    private Byte bargainsYn;


    /**
     * 是否是模板 0是 1否
     */
    @ApiModelProperty(value = "是否是模板 1是 0否", required = true, example = "1")
    private Byte isTemplete;


    /**
     * 是否禁用模板 0 禁用 1 不禁用
     */
    @ApiModelProperty(value = "是否禁用模板 1 禁用 0 不禁用", required = true, example = "1")
    private Byte forbiddenYn;

    /**
     * 是否逻辑删除，0 有效 1 删除
     */
    @ApiModelProperty(value = "是否逻辑删除，1 有效 0 删除", required = true, example = "0")
    private Byte yn;

    /**
     * 机构码
     */
    @ApiModelProperty(value = "机构码", required = true, example = "机构码")
    private String organsign;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号", required = true, example = "版本号")
    private Integer baseVersion;

    /**
     * 同时存储为模板 0是 1否
     */
    @ApiModelProperty(value = "同时存储为模板 1是 0否", required = true, example = "同时存储为模板 0是 1否")
    private Integer meanwhileTemplete;
    
    /**
     * 会员日商品
     */
    @ApiModelProperty(value = "会员日商品", required = true, example = "会员日商品")
    private List<MemberPreferentialProductSaveVo> memberPreferentialProductVoList;

    /**
     * 是否调用逻辑删除接口，1 有效  0  删除
     */
    @ApiModelProperty(value = "是否调用逻辑删除接口，1 有效  0  删除", required = true, example = "1")
    private Byte isDelete;

    /**
     * 月会员日–会员等级对应折扣基数
     */
    private Integer monthPriceStrategy;

    /**
     * 月会员日–特价商品使用会员价
     */
    private Integer monthIsSpecial=0;

    /**
     * 周会员日–会员等级对应折扣基数
     */
    private Integer weekPriceStrategy;

    /**
     * 周会员日-特价商品使用会员价
     */
    private Integer weekIsSpecial=0;

    /**
     * 会员生日–会员等级对应折扣基数
     */
    private Integer birthdayPriceStrategy;

    /**
     * 会员生日–特价商品使用会员价
     */
    private Integer  birthdayIsSpecial=0;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public Long getMemberLevelId() {
        return memberLevelId;
    }

    public void setMemberLevelId(Long memberLevelId) {
        this.memberLevelId = memberLevelId;
    }

    public String getTempleteName() {
        return templeteName;
    }

    public void setTempleteName(String templeteName) {
        this.templeteName = templeteName;
    }

    public String getMixedQuery() {
		return mixedQuery;
	}

	public void setMixedQuery(String mixedQuery) {
		this.mixedQuery = mixedQuery;
	}

	public Integer getMonthPreferentialDay() {
        return monthPreferentialDay;
    }

    public void setMonthPreferentialDay(Integer monthPreferentialDay) {
        this.monthPreferentialDay = monthPreferentialDay;
    }

    public BigDecimal getMonthPreferentialIntegralMultiple() {
        return monthPreferentialIntegralMultiple;
    }

    public void setMonthPreferentialIntegralMultiple(BigDecimal monthPreferentialIntegralMultiple) {
        this.monthPreferentialIntegralMultiple = monthPreferentialIntegralMultiple;
    }

    public Integer getMonthPreferentialDiscountRatio() {
        return monthPreferentialDiscountRatio;
    }

    public void setMonthPreferentialDiscountRatio(Integer monthPreferentialDiscountRatio) {
        this.monthPreferentialDiscountRatio = monthPreferentialDiscountRatio;
    }

    public Byte getMonthLevenYn() {
        return monthLevenYn;
    }

    public void setMonthLevenYn(Byte monthLevenYn) {
        this.monthLevenYn = monthLevenYn;
    }

    public Integer getWeekPreferentialDay() {
        return weekPreferentialDay;
    }

    public void setWeekPreferentialDay(Integer weekPreferentialDay) {
        this.weekPreferentialDay = weekPreferentialDay;
    }

    public BigDecimal getWeekPreferentialIntegralMultiple() {
        return weekPreferentialIntegralMultiple;
    }

    public void setWeekPreferentialIntegralMultiple(BigDecimal weekPreferentialIntegralMultiple) {
        this.weekPreferentialIntegralMultiple = weekPreferentialIntegralMultiple;
    }

    public Integer getWeekPreferentialDiscountRatio() {
        return weekPreferentialDiscountRatio;
    }

    public void setWeekPreferentialDiscountRatio(Integer weekPreferentialDiscountRatio) {
        this.weekPreferentialDiscountRatio = weekPreferentialDiscountRatio;
    }

    public Byte getWeekLevenYn() {
        return weekLevenYn;
    }

    public void setWeekLevenYn(Byte weekLevenYn) {
        this.weekLevenYn = weekLevenYn;
    }

    public Byte getMonthWeekPriority() {
        return monthWeekPriority;
    }

    public void setMonthWeekPriority(Byte monthWeekPriority) {
        this.monthWeekPriority = monthWeekPriority;
    }

    public Integer getBirthdayPreferentialDay() {
        return birthdayPreferentialDay;
    }

    public void setBirthdayPreferentialDay(Integer birthdayPreferentialDay) {
        this.birthdayPreferentialDay = birthdayPreferentialDay;
    }

    public BigDecimal getBirthdayPreferentialIntegralMultiple() {
        return birthdayPreferentialIntegralMultiple;
    }

    public void setBirthdayPreferentialIntegralMultiple(BigDecimal birthdayPreferentialIntegralMultiple) {
        this.birthdayPreferentialIntegralMultiple = birthdayPreferentialIntegralMultiple;
    }

    public Integer getBirthdayPreferentialDiscountRatio() {
        return birthdayPreferentialDiscountRatio;
    }

    public void setBirthdayPreferentialDiscountRatio(Integer birthdayPreferentialDiscountRatio) {
        this.birthdayPreferentialDiscountRatio = birthdayPreferentialDiscountRatio;
    }

    public Byte getBirthdayLevenYn() {
        return birthdayLevenYn;
    }

    public void setBirthdayLevenYn(Byte birthdayLevenYn) {
        this.birthdayLevenYn = birthdayLevenYn;
    }

    public Integer getProductChoiceModel() {
        return productChoiceModel;
    }

    public void setProductChoiceModel(Integer productChoiceModel) {
        this.productChoiceModel = productChoiceModel;
    }

    public Byte getBargainsYn() {
        return bargainsYn;
    }

    public void setBargainsYn(Byte bargainsYn) {
        this.bargainsYn = bargainsYn;
    }

    public Byte getIsTemplete() {
        return isTemplete;
    }

    public void setIsTemplete(Byte isTemplete) {
        this.isTemplete = isTemplete;
    }

    public Byte getForbiddenYn() {
        return forbiddenYn;
    }

    public void setForbiddenYn(Byte forbiddenYn) {
        this.forbiddenYn = forbiddenYn;
    }

    public Byte getYn() {
        return yn;
    }

    public void setYn(Byte yn) {
        this.yn = yn;
    }

    public String getOrgansign() {
        return organsign;
    }

    public void setOrgansign(String organsign) {
        this.organsign = organsign;
    }

    public Integer getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(Integer baseVersion) {
        this.baseVersion = baseVersion;
    }

    public Integer getMeanwhileTemplete() {
        return meanwhileTemplete;
    }

    public void setMeanwhileTemplete(Integer meanwhileTemplete) {
        this.meanwhileTemplete = meanwhileTemplete;
    }

    public List<MemberPreferentialProductSaveVo> getMemberPreferentialProductVoList() {
        return memberPreferentialProductVoList;
    }

    public void setMemberPreferentialProductVoList(List<MemberPreferentialProductSaveVo> memberPreferentialProductVoList) {
        this.memberPreferentialProductVoList = memberPreferentialProductVoList;
    }

    public Byte getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Byte isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getMonthPriceStrategy() {
        return monthPriceStrategy;
    }

    public void setMonthPriceStrategy(Integer monthPriceStrategy) {
        this.monthPriceStrategy = monthPriceStrategy;
    }

    public Integer getMonthIsSpecial() {
        return monthIsSpecial;
    }

    public void setMonthIsSpecial(Integer monthIsSpecial) {
        this.monthIsSpecial = monthIsSpecial;
    }

    public Integer getWeekPriceStrategy() {
        return weekPriceStrategy;
    }

    public void setWeekPriceStrategy(Integer weekPriceStrategy) {
        this.weekPriceStrategy = weekPriceStrategy;
    }

    public Integer getWeekIsSpecial() {
        return weekIsSpecial;
    }

    public void setWeekIsSpecial(Integer weekIsSpecial) {
        this.weekIsSpecial = weekIsSpecial;
    }

    public Integer getBirthdayPriceStrategy() {
        return birthdayPriceStrategy;
    }

    public void setBirthdayPriceStrategy(Integer birthdayPriceStrategy) {
        this.birthdayPriceStrategy = birthdayPriceStrategy;
    }

    public Integer getBirthdayIsSpecial() {
        return birthdayIsSpecial;
    }

    public void setBirthdayIsSpecial(Integer birthdayIsSpecial) {
        this.birthdayIsSpecial = birthdayIsSpecial;
    }
}