package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

@ApiModel(description = "药店商品与医保目录列表查询参数类")
public class ProductYbmlRelationQueryVo {

    @JsonProperty("mixQuery")
    private String mixQuery;

    @JsonProperty("matchState")
    private String matchState;

    @JsonProperty("page")
    private Integer page;

    @JsonProperty("rows")
    private Integer rows;

    @ApiModelProperty(value = "商品信息：支持通用名称、国家代码、当地代码、商品编号来查询；最多允许输入20个字符；支持全模糊搜索")
    public String getMixQuery() {
        return mixQuery;
    }

    public void setMixQuery(String mixQuery) {
        this.mixQuery = mixQuery;
    }

    @ApiModelProperty(value = "匹配状态：全部、已匹配、未匹配")
    public String getMatchState() {
        return matchState;
    }

    public void setMatchState(String matchState) {
        this.matchState = matchState;
    }

    @ApiModelProperty(value = "当前页码")
    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    @ApiModelProperty(value = "行数")
    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }
}
