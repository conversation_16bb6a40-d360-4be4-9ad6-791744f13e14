package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName CostAdjustsByPrefQueryVo
 * @Description 成本调价获取详情查询信息类
 * <AUTHOR>
 * @Date 2020/8/19 18:51
 * @Version 1.0
 **/
@ApiModel(description = "成本调价获取详情查询信息类")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")
public class CostAdjustsByPrefQueryVo {

    @JsonProperty("pref")
    private String pref;//调价方案编号

    @ApiModelProperty(value = "调价方案编号")
    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }


}
