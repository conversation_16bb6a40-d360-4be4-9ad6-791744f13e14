package com.xyy.saas.web.api.module.consult.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 远程问诊记录Dto
 * <AUTHOR>
 */
@ApiModel(description = "远程问诊记录")
public class ConsultRecordVo implements Serializable {

    private static final long serialVersionUID = 2166880280395388958L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 患者id
     */
    @ApiModelProperty(value = "患者id")
    private Long patientId;

    /**
     * 既往病史
     */
    @ApiModelProperty(value = "既往病史")
    private String history;

    /**
     * 病情描述
     */
    @ApiModelProperty(value = "病情描述", required = true)
    private String patientCondition;

    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄", example = "1")
    private Integer age;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新人
      */
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer baseVersion;

    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识", required = true)
    private String organSign;

    /**
     * 问诊状态 0-待问诊 1-问诊完成
     */
    @ApiModelProperty(value = "问诊状态 0-待问诊 1-问诊完成")
    private Byte isEnd;

    /**
     * 处方guid
     */
    @ApiModelProperty(value = "处方guid")
    private String prescriptionGuid;

    /**
     * 逻辑删除 1-有效 0-删除
     */
    @ApiModelProperty(value = "逻辑删除 1-有效 0-删除")
    private Byte yn;

    /**
     * 远程问诊记录guid
     */
    @ApiModelProperty(value = "远程问诊记录guid")
    private String guid;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPatientId() { return patientId; }

    public void setPatientId(Long patientId) { this.patientId = patientId; }

    public String getHistory() {
        return history;
    }

    public void setHistory(String history) {
        this.history = history == null ? null : history.trim();
    }

    public String getPatientCondition() {
        return patientCondition;
    }

    public void setPatientCondition(String patientCondition) {
        this.patientCondition = patientCondition == null ? null : patientCondition.trim();
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(Integer baseVersion) {
        this.baseVersion = baseVersion;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign == null ? null : organSign.trim();
    }

    public Byte getIsEnd() {
        return isEnd;
    }

    public void setIsEnd(Byte isEnd) {
        this.isEnd = isEnd;
    }

    public String getPrescriptionGuid() {
        return prescriptionGuid;
    }

    public void setPrescriptionGuid(String prescriptionGuid) {
        this.prescriptionGuid = prescriptionGuid == null ? null : prescriptionGuid.trim();
    }

    public Byte getYn() {
        return yn;
    }

    public void setYn(Byte yn) {
        this.yn = yn;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    @Override
    public String toString() {
        return "ConsultRecordVo{" +
                "id=" + id +
                ", patientId=" + patientId +
                ", history='" + history + '\'' +
                ", patientCondition='" + patientCondition + '\'' +
                ", age=" + age +
                ", createUser='" + createUser + '\'' +
                ", createTime=" + createTime +
                ", updateUser='" + updateUser + '\'' +
                ", updateTime=" + updateTime +
                ", baseVersion=" + baseVersion +
                ", organSign='" + organSign + '\'' +
                ", isEnd=" + isEnd +
                ", prescriptionGuid='" + prescriptionGuid + '\'' +
                ", yn=" + yn +
                ", guid='" + guid + '\'' +
                '}';
    }
}