package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.control.dto.costprice.*;
import com.xyy.saas.web.api.module.product.model.*;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RequestMapping("/product")
@Api(value = "商品调价方案统一接口", description = "连锁总部为连锁门店配置调价方案，支持连锁门店向总部请货功能")
public interface ProductCostPriceApi {

    /**
     * 成本价后端计算器，单条实现接口
     * @param request
     * @param computeCostPriceVo
     * @return
     */
    @ApiOperation(value = "成本价后端计算器，单条实现接口", notes = "成本价后端计算器，单条实现接口", response = ResultVO.class, tags={ "商品调价方案统一接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ComputeCostPriceVo.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/computeCostPrice", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> computeCostPrice(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody ComputeCostPriceVo computeCostPriceVo);

    /**
     * 成本价后端计算器接口，批量实现接口
     * @param request
     * @param computeCostPriceBatchVo
     * @return
     */
    @ApiOperation(value = "成本价后端计算器接口，批量实现接口", notes = "成本价后端计算器接口，批量实现接口", response = ResultVO.class, tags={ "商品调价方案统一接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ComputeCostPriceBatchVo.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/computeCostPriceBatch", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> computeCostPriceBatch(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody ComputeCostPriceBatchVo computeCostPriceBatchVo);

    /**
     * 调价方案列表页接口
     * @param request
     * @param costPriceListQueryVo
     * @return
     */
    @ApiOperation(value = "调价方案列表页接口", notes = "调价方案列表页接口", response = ResultVO.class, tags={ "商品调价方案统一接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = CostPriceListResultVo.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/list", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> costPriceList(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody CostPriceListQueryVo costPriceListQueryVo);

    /**
     * 调价方案暂存功能接口
     * @param request
     * @param costPriceVo
     * @return
     */
    @ApiOperation(value = "调价方案暂存功能接口", notes = "调价方案暂存功能接口", response = ResultVO.class, tags={ "商品调价方案统一接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/temporaryStorage", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> temporaryStorage(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody CostPriceVo costPriceVo);

    /**
     * 调价方案提交功能接口
     * @param request
     * @param costPriceVo
     * @return
     */
    @ApiOperation(value = "调价方案提交功能接口", notes = "调价方案提交功能接口", response = ResultVO.class, tags={ "商品调价方案统一接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/submit", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> submit(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody CostPriceVo costPriceVo);

    /**
     * 调价方案提交功能禁用启用接口
     * @param request
     * @param disableOrenableVo
     * @return
     */
    @ApiOperation(value = "调价方案提交功能禁用启用接口", notes = "调价方案提交功能禁用启用接口", response = ResultVO.class, tags={ "商品调价方案统一接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/disableOrenable", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> disableOrenable(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody CostPriceDisableOrenableVo disableOrenableVo);

    /**
     * 调价方案添加商品查询列表接口
     * @param request
     * @param costPriceQueryProductVo
     * @return
     */
    @ApiOperation(value = "调价方案添加商品查询列表接口", notes = "调价方案添加商品查询列表接口", response = ResultVO.class, tags={ "商品调价方案统一接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = CostPriceQueryProductResultVo.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/selectProductsForAdd", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> selectProductsForAdd(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody CostPriceQueryProductVo costPriceQueryProductVo);

    /**
     * 根据方案编号获取方案详情信息接口
     * @param request
     * @param costAdjustsByPrefQueryVo
     * @return
     */
    @Deprecated
    @ApiOperation(value = "根据方案编号获取方案详情信息接口", notes = "根据方案编号获取方案详情信息接口", response = ResultVO.class, tags={ "商品调价方案统一接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = CostAdjustsByPrefResultVo.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/selectCostAdjustsByPref", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> selectCostAdjustsByPref(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody CostAdjustsByPrefQueryVo costAdjustsByPrefQueryVo);

    /**
     * 配置调价方案，获取启用未过期调价方案下拉列表接口
     * @param request
     * @return
     */
    @ApiOperation(value = "配置调价方案，获取启用未过期调价方案下拉列表接口(此接口可以复用调价方案列表页分页查询接口)", notes = "配置调价方案，获取启用未过期调价方案下拉列表接口(此接口可以复用调价方案列表页分页查询接口)", response = ResultVO.class, tags={ "商品调价方案统一接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = CostPriceListResultVo.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/selectUsableAdjusts", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> selectUsableAdjusts(HttpServletRequest request);

    /**
     * 配置调价方案，列表查询接口
     * @param request
     * @return
     */
    @ApiOperation(value = "配置调价方案，列表查询接口", notes = "配置调价方案，列表查询接口", response = ResultVO.class, tags={ "商品调价方案统一接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ConfigurationSchemesResultVo.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/selectConfigurationSchemes", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> selectConfigurationSchemes(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody ConfigurationSchemesQueryVo configurationSchemesQueryVo);

    /**
     * 配置调价方案--批量清除方案接口
     * @param request
     * @return
     */
    @ApiOperation(value = "配置调价方案--批量清除方案接口", notes = "配置调价方案--批量清除方案接口", response = ResultVO.class, tags={ "商品调价方案统一接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ConfigurationSchemesResultVo.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/deleteConfigurationSchemes", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> deleteConfigurationSchemes(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody ConfigureAdjustsDto configureAdjustsDto);

    /**
     * 配置调价方案提交接口,支持单条配置和批量配置
     * @param request
     * @return
     */
    @ApiOperation(value = "配置调价方案提交接口", notes = "配置调价方案提交接口", response = ResultVO.class, tags={ "商品调价方案统一接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/configureAdjusts", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> configureAdjusts(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody ConfigureAdjustsVo configureAdjustsVo);

    /**
     * 调价方案暂存，提交，配置调价方案编辑提交，任务轮询接口
     * @param request
     * @return
     */
    @ApiOperation(value = "价方案暂存，提交，配置调价方案编辑提交，任务轮询接口：result：0处理成功，1,处理中,-1失败", notes = "价方案暂存，提交，配置调价方案编辑提交，任务轮询接口：result：0处理成功，1,处理中", response = ResultVO.class, tags={ "商品调价方案统一接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/checkTaskStatus", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> checkTaskStatus(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody CheckTaskStatusQueryVo checkTaskStatusQueryVo);

    /**
     * 查询门店调价方案列表
     * @param request
     * @return
     */
    @ApiOperation(value = "查询门店调价方案列表", notes = "查询门店调价方案列表", response = ResultVO.class, tags={ "商品调价方案统一接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ConfigurationSchemesResultVo.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/listByOrganSign", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getAdjustByLocalOrganSign(HttpServletRequest request);

    /**
     * 价格上下限商品查询模块
     * @param request
     * @param costPriceQueryProductVo
     * @return
     */
    @ApiOperation(value = "价格上下限商品查询接口", notes = "价格上下限商品查询接口", response = ResultVO.class, tags={ "商品调价方案统一接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = CostPriceQueryProductResultVo.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/priceUperLower/selectProducts", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> selectProductsForPriceUpperLower(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody CostPriceQueryProductVo costPriceQueryProductVo);


    /**
     * 价格上下限商品查询模块
     * @param request
     * @param costPriceDetailWrapperDto
     * @return
     */
    @ApiOperation(value = "缓存前端添加的商品,仅限前端使用", notes = "缓存前端添加的商品,仅限前端使用", response = ResultVO.class, tags={ "商品调价方案统一接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = CostAdjustsByPrefResultDetailDto.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/cache/addProducts", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> cacheAddProducts(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody CostPriceDetailWrapperDto costPriceDetailWrapperDto);


    /**
     * 价格上下限商品查询模块
     * @param request
     * @param costPriceDetailWrapperDto
     * @return
     */
    @ApiOperation(value = "编辑商品，缓存到后台,仅限前端使用", notes = "编辑商品，缓存到后台,仅限前端使用", response = ResultVO.class, tags={ "商品调价方案统一接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = CostAdjustsByPrefResultDetailDto.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/cache/editProducts", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> cacheEditProducts(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody CostPriceDetailWrapperDto costPriceDetailWrapperDto);


    /**
     * 价格上下限商品查询模块
     * @param request
     * @param costPriceDetailWrapperDto
     * @return
     */
    @ApiOperation(value = "-删除商品，缓存到后台,仅限前端使用", notes = "-删除商品，缓存到后台,仅限前端使用", response = ResultVO.class, tags={ "商品调价方案统一接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = CostAdjustsByPrefResultDetailDto.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/cache/deleteProducts", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> cacheDeleteProducts(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody CostPriceDetailWrapperDto costPriceDetailWrapperDto);


    /**
     * 清除管控方案后台缓存
     * @param request
     * @return
     */
    @ApiOperation(value = "清除管控方案后台缓存,仅限前端使用", notes = "清除管控方案后台缓存,仅限前端使用", response = ResultVO.class, tags={ "商品调价方案统一接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/cache/clear", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> clearCache(HttpServletRequest request);


    /**
     *查询方案详情，不含所包含的商品列表
     * @param request
     * @param
     * @return
     */
    @ApiOperation(value = "查询方案详情，不含所包含的商品列表", notes = "查询方案详情，不含所包含的商品列表", response = ResultVO.class, tags={ "商品调价方案统一接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = CostPriceQueryProductResultVo.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/getCostAdjustInfo" , method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getCostAdjustInfo(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody AdjustPriceInfoVo adjustPriceInfoVo);


    /**
     * 查询管控方案产品列表,带缓存数据
     * @param request
     * @param costPriceAdjustDetailQueyDto
     * @return
     */
    @ApiOperation(value = "查询管控方案产品列表", notes = "查询管控方案产品列表,仅限前端使用", response = ResultVO.class, tags={ "商品调价方案统一接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = CostPriceQueryProductResultVo.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/selectCostAdjustProductList", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> selectCostAdjustProductListWithCache(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody CostPriceAdjustDetailQueyDto costPriceAdjustDetailQueyDto);


    /**
     * .提交管控方案 带缓存数据
     * @param request
     * @return
     */
    @ApiOperation(value = "提交管控方案 带缓存数据,仅限前端使用", notes = "缓存提交管控方案 带缓存数据,仅限前端使用", response = ResultVO.class, tags={ "商品调价方案统一接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = CostPriceQueryProductResultVo.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/submitAdjustByCache", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> submitAdjustByCache(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody CostPriceDto costPriceDto);

    /**
     * 暂存管控方案 带缓存数据
     * @param request
     * @return
     */
    @ApiOperation(value = "暂存管控方案 带缓存数据,仅限前端使用", notes = "暂存管控方案 带缓存数据,仅限前端使用", response = ResultVO.class, tags={ "商品调价方案统一接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = CostPriceQueryProductResultVo.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/temporaryStorageAdjustByCache", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> temporaryStorageAdjustByCache(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody CostPriceDto costPriceDto);

    /**
     * 根据产品编号查询管控方案产品列表
     * @param request
     * @param costPriceQueryProductDto
     * @return
     */
    @ApiOperation(value = "根据产品编号查询管控方案产品列表", notes = "根据产品编号查询管控方案产品列表", response = ResultVO.class, tags={ "商品调价方案统一接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = CostAdjustsByPrefResultDetailDto.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/getProductPriceInfosByPrefs", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getProductPriceInfosByPrefs(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody CostPriceQueryProductDto costPriceQueryProductDto);

    /**
     * 获取当前客户是否是智鹿客户
     * @param request
     *
     */
    @ApiOperation(value = "获取当前客户是否是智鹿客户", notes = "获取当前客户是否是智鹿客户", response = ResultVO.class, tags={ "商品调价方案统一接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Integer.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/getCurOrganIsZhiLu", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getCurOrganIsZhiLu(HttpServletRequest request);
    /**
     * 获取当前客户是否是智鹿客户
     * @param request
     *
     */
    @ApiOperation(value = "获取当前客户支持的取值方式", notes = "获取当前客户支持的取值方式", response = ResultVO.class, tags={ "商品调价方案统一接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Integer.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/getCurOrganValueTypes", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getCurOrganValueTypes(HttpServletRequest request);


    /**
     * 提交批量导入时的方案信息
     * @param costPriceDto
     * @return
     */
    @ApiOperation(value = "提交批量导入时的方案信息", notes = "提交批量导入时的方案信息", response = ResultVO.class, tags={ "提交批量导入时的方案信息", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/submitImportedAdjust", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> submitImportedAdjust (  HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody CostPriceDto costPriceDto);

    /**
     * 导入任务，进展状态查询
     * @return
     */
    @ApiOperation(value = "导入任务，进展状态查询", notes = "导入任务，进展状态查询", response = ResultVO.class, tags={ "导入任务，进展状态查询", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/checkImportTaskStatus", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> checkImportTaskStatus (HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody CheckTaskStatusQueryVo checkTaskStatusQueryVo);

    /**
     * 提交批量导入时的方案信息
     * @param costPriceDetailWrapperDto
     * @return
     */
    @ApiOperation(value = "导入缓存方案编码", notes = "导入缓存方案编码", response = ResultVO.class, tags={ "导入缓存方案编码", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/cacheAdjustPref", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> cacheAdjustPref (  HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody CostPriceDetailWrapperDto costPriceDetailWrapperDto);

    /**
     * 提交批量导入时的方案信息
     * @return
     */
    @ApiOperation(value = "导入缓存方案编码", notes = "导入缓存方案编码", response = ResultVO.class, tags={ "导入缓存方案编码", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/costPrice/getCacheAdjustPref", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getCacheAdjustPref (HttpServletRequest request);
}
