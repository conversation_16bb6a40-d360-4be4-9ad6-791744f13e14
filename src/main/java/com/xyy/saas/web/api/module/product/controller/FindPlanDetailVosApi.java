/**
 * NOTE: This class is auto generated by the swagger code generator program (2.2.3).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.web.api.module.product.model.InventoryPlanDetailVo;
import com.xyy.saas.web.api.module.product.model.InventoryPlanDetailVoList;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-29T18:35:09.839+08:00")
@RequestMapping("/product")
@Api(value = "findPlanDetailVos", description = "the findPlanDetailVos API")
public interface FindPlanDetailVosApi {

    @ApiOperation(value = "查询所有盘点单明细", notes = "查询所有盘点单明细", response = InventoryPlanDetailVoList.class, tags={ "InventoryPlanDetailVo", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "操作成功", response = InventoryPlanDetailVoList.class) })
    
    @RequestMapping(value = "/findPlanDetailVos",
        produces = { "application/xml", "application/json" }, 
        consumes = { "application/json", "application/xml" },
        method = RequestMethod.POST)
    ResponseEntity<InventoryPlanDetailVoList> findPlanDetailVos(@ApiParam(value = "盘点单明细Vo", required = true) @Valid @RequestBody InventoryPlanDetailVo inventoryPlanDetailVo);

}
