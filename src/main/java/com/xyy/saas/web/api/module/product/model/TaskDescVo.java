package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName TaskDescVo
 * @Description 任务描述返回信息类
 * <AUTHOR>
 * @Date 2021/2/3 14:10
 * @Version 1.0
 **/
@ApiModel(description = "任务描述返回类")
public class TaskDescVo {

    @JsonProperty("businessCode")
    private Integer businessCode;//业务状态码
    @JsonProperty("msg1")
    private String msg1;
    @JsonProperty("msg2")
    private String msg2;

    @ApiModelProperty(value = "任务id")
    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    @JsonProperty("taskId")
    private String taskId;

    @ApiModelProperty(value = "业务状态码")
    public Integer getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(Integer businessCode) {
        this.businessCode = businessCode;
    }

    @ApiModelProperty(value = "描述字段1")
    public String getMsg1() {
        return msg1;
    }

    public void setMsg1(String msg1) {
        this.msg1 = msg1;
    }

    @ApiModelProperty(value = "描述字段2")
    public String getMsg2() {
        return msg2;
    }

    public void setMsg2(String msg2) {
        this.msg2 = msg2;
    }

}
