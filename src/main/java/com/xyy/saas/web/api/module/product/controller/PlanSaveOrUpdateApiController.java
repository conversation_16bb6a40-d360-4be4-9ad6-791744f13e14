package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.inventory.core.api.InventoryPlanApi;
import com.xyy.saas.web.api.module.product.model.InventoryPlanDetailVo;
import com.xyy.saas.web.api.module.product.model.InventoryPlanVo;
import com.xyy.saas.web.api.module.product.model.ResultCodeEnum;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-29T18:34:31.623+08:00")

@Controller
public class PlanSaveOrUpdateApiController implements PlanSaveOrUpdateApi {

    @Reference(version = "1.0.0")
    private InventoryPlanApi inventoryPlanApi;
    @ResponseBody
    public ResponseEntity<ResultVO> planSaveOrUpdate(@ApiParam(value = "盘点计划Vo" ,required=true ) @RequestHeader("userName") String userName, @RequestHeader("organSign") String organSign, @Valid @RequestBody InventoryPlanVo inventoryPlanVo) {
        if(inventoryPlanVo==null){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.INVENTORY_PARAM_NULL), HttpStatus.INTERNAL_SERVER_ERROR);
        }
        com.xyy.saas.inventory.core.dto.InventoryPlanVo vo = new com.xyy.saas.inventory.core.dto.InventoryPlanVo();
        List<com.xyy.saas.inventory.core.dto.InventoryPlanDetailVo> ipvos = new ArrayList<>();
        if(inventoryPlanVo!=null&&inventoryPlanVo.getInventoryPlanDetailVos()!=null){
            for(InventoryPlanDetailVo ivo : inventoryPlanVo.getInventoryPlanDetailVos()){
                com.xyy.saas.inventory.core.dto.InventoryPlanDetailVo idvo = new com.xyy.saas.inventory.core.dto.InventoryPlanDetailVo();
                BeanUtils.copyProperties(ivo,idvo);
                ipvos.add(idvo);
            }
        }
        BeanUtils.copyProperties(inventoryPlanVo,vo);
        vo.setInventoryPlanDetailVos(ipvos);
        vo.setOrgansign(organSign);
        vo.setCreateUser(userName);
        com.xyy.saas.inventory.core.dto.InventoryPlanVo back = inventoryPlanApi.saveOrUpdate(vo);
        if(back.getBack()>=1){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.SUCCESS), HttpStatus.OK);
        }else{
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.INVENTORY_RESULT_FAIL), HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }

}
