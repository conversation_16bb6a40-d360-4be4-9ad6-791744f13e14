package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 会员充值和扣款流水查询Vo
 * <AUTHOR>
 */
@ApiModel(description = "会员充值和扣款流水查询")
public class MemberPrepayCardHistoryDepositQueryVo implements Serializable {


    private static final long serialVersionUID = -6512482957971105625L;

    /**
     * 会员guid
     */
    @ApiModelProperty(value = "会员guid", example = "584a4460-2144-47c6-b7f7-d85b33d3322c")
    private String memberGuid;

    /**
     * 操作类型 1-储值 2-消费 3-扣款 4-退货
     */
    @ApiModelProperty(value = "操作类型 1-储值 2-消费 3-扣款 4-退货", example = "1")
    private Byte operateType;

    /**
     * 支付类型 1-现金 2-微信 3-支付宝 4-银联 9-其他
     * @see com.xyy.saas.member.core.enums.PayTypeEnum
     */
    @ApiModelProperty(value = "支付类型 1-现金 2-微信 3-支付宝 4-银联 9-其他", example = "1")
    private Byte payType;

    /**
     * 查询开始时间
     */
    @ApiModelProperty(value = "查询开始时间", example = "1563552000000")
    private Date startTime;

    /**
     * 查询结束时间
     */
    @ApiModelProperty(value = "查询结束时间", example = "1563638399000")
    private Date endTime;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间", hidden = true, example = "1563552000000")
    private Date operateDate;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人", example = "12")
    private String createUser;

    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识", hidden = true)
    private String organsign;

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码", required = true, example = "1")
    private Integer pageNum;

    /**
     * 每页数量
     */
    @ApiModelProperty(value = "每页数量", required = true, example = "10")
    private Integer pageSize;

    /**
     * 导出excel文件名称
     */
    @ApiModelProperty(value = "导出excel文件名称", example = "汇总数据2019-08-01")
    private String excelName;

    public String getMemberGuid() {
        return memberGuid;
    }

    public void setMemberGuid(String memberGuid) {
        this.memberGuid = memberGuid;
    }

    public Byte getOperateType() {
        return operateType;
    }

    public void setOperateType(Byte operateType) {
        this.operateType = operateType;
    }

    public Byte getPayType() {
        return payType;
    }

    public void setPayType(Byte payType) {
        this.payType = payType;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getOperateDate() {
        return operateDate;
    }

    public void setOperateDate(Date operateDate) {
        this.operateDate = operateDate;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getOrgansign() {
        return organsign;
    }

    public void setOrgansign(String organsign) {
        this.organsign = organsign;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getExcelName() {
        return excelName;
    }

    public void setExcelName(String excelName) {
        this.excelName = excelName;
    }

    @Override
    public String toString() {
        return "MemberPrepayCardHistoryDepositQueryVo{" +
                "memberGuid='" + memberGuid + '\'' +
                ", operateType=" + operateType +
                ", payType=" + payType +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", operateDate=" + operateDate +
                ", createUser='" + createUser + '\'' +
                ", organsign='" + organsign + '\'' +
                ", pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                ", excelName='" + excelName + '\'' +
                '}';
    }
}