package com.xyy.saas.web.api.module.utils;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

public class JsonUtil {

    /**
     * 将Object对象转换成Json格式的数据
     *
     * 不忽略Null值
     * @param obj
     * @return
     * @throws Exception
     */
    public static String toJsonNotIgnoreNull(Object obj) throws Exception {
       if(obj != null){
    	   return JSONObject.toJSONString(obj,SerializerFeature.WriteMapNullValue);
       }
       return null;
    }
    
    
    /**
     * 将Object对象转换成Json格式的数据
     *
     * @param obj
     * @return
     * @throws Exception
     */
    public static String toJson(Object obj) throws Exception {
       if(obj != null){
    	   return JSONObject.toJSONString(obj);
       }
       return null;
    }

    /**
     * 将Json字符串转换成相应的
     *
     * @param json
     * @return
     * @throws Exception
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public static Object toObjectFromJson(String json, Class objClass) {
        if(json != null && !"".equals(json) && objClass != null){
        	return JSONObject.parseObject(json, objClass);
        }
        return null;
    }


    /**
     * 将Json字符串转换成相应的
     *
     * @param obj
     * @return
     * @throws Exception
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public static String toJsonFromObj(Object obj) {
        String outJson ="";
        try {
            ObjectMapper mapper = new ObjectMapper();
            mapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY );
            outJson = mapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return outJson;
    }
}
