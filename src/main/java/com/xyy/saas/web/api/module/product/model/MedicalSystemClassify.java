package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * saas_medical_system_classify
 * <AUTHOR>
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-09-11T21:07:46.794+08:00")
@ApiModel(description = "分类转换实体对象")
public class MedicalSystemClassify implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 区域编码
     */
    @ApiModelProperty(value = "区域编码")
    @JsonProperty("cityId")
    private String cityId;

    /**
     * 地区分类
     */
    @ApiModelProperty(value = "地区分类")
    @JsonProperty("regionClassify")
    private String regionClassify;

    /**
     * 系统分类
     */
    @ApiModelProperty(value = "系统分类")
    @JsonProperty("systemClassify")
    private Integer systemClassify;

    /**
     * 状态，1正常 2禁用
     */
    @ApiModelProperty(value = "状态")
    @JsonProperty("status")
    private Integer status;

    /**
     * 类别 1 商品  2 诊疗项目
     */
    @ApiModelProperty(value = "类别")
    @JsonProperty("type")
    private Integer type;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getRegionClassify() {
        return regionClassify;
    }

    public void setRegionClassify(String regionClassify) {
        this.regionClassify = regionClassify;
    }

    public Integer getSystemClassify() {
        return systemClassify;
    }

    public void setSystemClassify(Integer systemClassify) {
        this.systemClassify = systemClassify;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}