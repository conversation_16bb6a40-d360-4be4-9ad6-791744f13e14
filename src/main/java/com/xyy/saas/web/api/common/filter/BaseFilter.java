package com.xyy.saas.web.api.common.filter;


import com.dianping.cat.message.Transaction;
import com.xyy.cat.util.CatUtil;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.web.api.common.constants.ResultCodeMessage;
import com.xyy.saas.web.api.common.context.ActionContext;
import com.xyy.saas.web.api.common.context.ActionContextSupport;
import com.xyy.saas.web.api.common.log.LogStrategy;
import com.xyy.saas.web.api.common.swagger2.SwaggerUrlFilterHelper;
import com.xyy.saas.web.api.module.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 第一个基础过滤器，用于解决跨域，参数封装及转换，日志记录
 * <AUTHOR>
 * @date 2019-08-12
 * @mondify
 * @copyright
 */
@Component
public class BaseFilter implements Filter {

    private static final int MAX_CHAR_LENGTH = 1024;

    private static final Logger logger = LoggerFactory.getLogger(BaseFilter.class);

    @Autowired
    private LogStrategy logStrategy;

    /**
     * 获取启动环境
     * */
    @Value("${gateway.env}")
    public String env = null;

    /**
     * 启动线上环境
     * */
    private static final String PROD_ENV = "prod";
//    private static final String PROD_ENV = "dev";

    /**
     * 需要校验入参是否含有生僻字或者emoji的url请求
     * */
    @Value("#{'${gateway.params.utf8mb4.filter.url:}'.split(',')}")
    private List<String> PARAMS_UTF8MB4_FILTER_URL = new ArrayList<>();
    /**
     * 是否开启全局校验入参是否含有生僻字或者emoji的开关, 默认关闭
     */
    @Value("${gateway.params.utf8mb4.filter.all:false}")
    private boolean PARAMS_UTF8MB4_FILTER_ALL;


    @Override
    public void init(FilterConfig filterConfig) {

    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain) throws IOException, ServletException {
        Transaction transaction = null;
        try {
            HttpServletResponse response = (HttpServletResponse) servletResponse;
            HttpServletRequest request = (HttpServletRequest) servletRequest;
            String requestPath = request.getRequestURI();
            ResultVO result = new ResultVO();
            if("/".equals(requestPath)){
                result.setCode(ResultCodeMessage.USER_ROOT_URL_FAIL_CODE);
                result.setMsg(ResultCodeMessage.USER_ROOT_URL_FAIL_MESSAGE);
                response.getWriter().write(JSONUtils.obj2JSON(result));
                return;
            }

            //线上环境，拦截swagger请求规则的request，后续filter,interceptor不需要再校验是否是生产环境
            if(SwaggerUrlFilterHelper.containsSwaggerUrl(request)){
                if(PROD_ENV.equals(env)){
                    result.setCode(ResultCodeMessage.GATEWAY_SUCCESS_CODE);
                    result.setMsg(ResultCodeMessage.GATEWAY_SUCCESS_MESSAGE);
                    response.getWriter().write(JSONUtils.obj2JSON(result));
                    return;
                }else{
                    chain.doFilter(servletRequest, servletResponse);
                    return;
                }
            }
            response.setHeader("Access-Control-Allow-Origin", request.getHeader("Origin"));
            response.setHeader("Access-Control-Allow-Methods", "POST, GET, OPTIONS, DELETE");
            response.setHeader("Access-Control-Allow-Credentials", "true");
            response.setHeader("Access-Control-Max-Age", "3600");
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization,x-requested-with,client,sign,token,Accept,Origin,Referer,User-Agent,Cookie,Cookie3,AppVer,timestamp,abtest,clientType");
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/json; charset=UTF-8");
            //网关访问URL CAT埋点
            String requestMethod = request.getMethod();
            logger.info("BaseFilter doFilter start"+request.getRequestURI()+"|"+requestMethod);
            transaction = CatUtil.initTransaction("web.api.url", requestPath );
            CatUtil.logEvent("URL", requestPath);
            CatUtil.logEvent("requestMethod", requestMethod);
            CatUtil.logEvent("client.ip", request.getRemoteHost());
            if (HttpMethod.OPTIONS.name().equals(request.getMethod())) {
                response.setStatus(HttpServletResponse.SC_OK, "OK");
                CatUtil.successCat(transaction);
                return ;
            }

            ParameterCheckServletRequestWrapper myWrapper = new ParameterCheckServletRequestWrapper((HttpServletRequest) servletRequest);
            String paramsJson = myWrapper.getAllParams();


            // 检查参数是否存在生僻字
            if ((PARAMS_UTF8MB4_FILTER_ALL || PARAMS_UTF8MB4_FILTER_URL.contains(request.getRequestURI()))
                    && StringUtils.requiresMb4(paramsJson)) {
                result.setCode(ResultCodeMessage.PARAMS_HAS_UTF8MB4_CODE);
                result.setMsg(ResultCodeMessage.PARAMS_HAS_UTF8MB4_MESSAGE);
                response.getWriter().write(JSONUtils.obj2JSON(result));
                return;
            }


            //请求数据绑定到Threadlocal上下文
            ActionContextSupport.bind(request,paramsJson);
            chain.doFilter(myWrapper, servletResponse);

            ActionContext current = ActionContextSupport.getCurrent();
            if(current!=null){
                current.setResponseTime(System.currentTimeMillis());
                current.setTimeCost(current.getResponseTime()-current.getRequestTime());
            }
//            processLogMap(current);
            logStrategy.doLog(current);
            if (transaction != null){
                CatUtil.successCat(transaction);
            }
        } catch (Exception e) {
            CatUtil.errorCat(transaction, e);
            logger.error("web-api access log error", e);
            ActionContext current = ActionContextSupport.getCurrent();
            if(current!=null){
                current.setResponseTime(System.currentTimeMillis());
                current.setTimeCost(current.getResponseTime()-current.getRequestTime());
            }
            logStrategy.doLog(current);
            servletResponse.getWriter().write(JSONUtils.obj2JSON(new ResultVO(9999, "API异常，异常信息：" + e.getMessage(),"")));
        } finally {
            ActionContextSupport.unset();
        }
    }

    @Override
    public void destroy() {

    }

}
