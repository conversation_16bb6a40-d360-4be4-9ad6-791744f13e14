package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.api.SystemDictApi;
import com.xyy.saas.common.dto.SystemDictDto;
import com.xyy.saas.common.dto.SystemDictQueryDto;
import com.xyy.saas.common.enums.BusinessFlowEnum;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.provider.core.api.FirstCheckProviderApi;
import com.xyy.saas.provider.core.api.ProviderApi;
import com.xyy.saas.supplier.dto.FirstCheckProviderDto;
import com.xyy.saas.supplier.dto.FirstCheckProviderVoDto;
import com.xyy.saas.supplier.dto.ProviderDto;
import com.xyy.saas.supplier.api.FirstCheckSupplierApi;
import com.xyy.saas.supplier.api.SupplierBasicInfoApi;
import com.xyy.saas.supplier.dto.ApproveHistoryDto;
import com.xyy.saas.web.api.module.product.constant.DictConstant;
import com.xyy.saas.workflow.api.WorkflowAuditTaskService;
import com.xyy.saas.workflow.exception.WorkflowException;
import com.xyy.saas.workflow.model.dto.WorkflowAuditProcessQueryDto;
import com.xyy.saas.workflow.model.dto.WorkflowAuditTaskDto;
import com.xyy.saas.workflow.model.meta.TenantIdEnum;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.api.RoleApi;
import com.xyy.user.module.dto.SaasRoleDto;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import com.xyy.user.module.dto.restructure.SaaSEmployeeDto;
import com.xyy.user.module.enums.DrugstoreBizModelEnum;
import com.xyy.user.module.enums.DrugstoreTypeEnum;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
public class FirstProviderInfoController implements FirstProviderInfoApi {

    private static final Logger logger = LoggerFactory.getLogger(ProductApiController.class);

    @Reference(version = "0.0.1")
    private ProviderApi providerApi;

    @Reference(version = "0.0.1")
    private SupplierBasicInfoApi supplierBasicInfoApi;

    @Reference(version = "0.0.1")
    private SystemDictApi systemDictApi;

    @Reference(version = "0.0.1")
    private EmployeeApi employeeApi;

    @Reference(version = "1.0.0")
    private WorkflowAuditTaskService workflowAuditTaskService;

    @Reference(version = "0.0.1")
    private FirstCheckProviderApi firstCheckProviderApi;

    @Reference(version = "0.0.1")
    private FirstCheckSupplierApi firstCheckSupplierApi;

    @Reference(version = "0.0.1")
    private RoleApi roleApi;

    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;

    @Override
    public ResponseEntity<ResultVO> queryFirstProviderList(HttpServletRequest request, @ApiParam(value = "首营供应商查询对象", required = true) @Valid @RequestBody FirstCheckProviderDto firstProvider) {
        String organSign = request.getHeader("organSign");
        String employeeId = request.getHeader("employeeId");
        ResultVO<List<Integer>> listResultVO = roleApi.queryRoleIdsByEmployeeId(Integer.parseInt(employeeId));
        List<Integer> result = listResultVO.getResult();
        Integer page = firstProvider.getPage();
        Integer rows = firstProvider.getRows();
        if (page == null) {
            page = 1;
        }
        if (rows == null) {
            rows = 10;
        }
        if(!StringUtils.isEmpty(firstProvider.getStartTimeStr())){
            firstProvider.setStartTimeStr(firstProvider.getStartTimeStr()+" 00:00:00");
        }
        if(!StringUtils.isEmpty(firstProvider.getEndTimeStr())){
            firstProvider.setEndTimeStr(firstProvider.getEndTimeStr()+" 23:59:59");
        }
        PageInfo<FirstCheckProviderDto> pageInfo = new PageInfo<>();
        pageInfo.setPageSize(rows);
        pageInfo.setPageNum(page);
        firstProvider.setOrgansign(organSign);
        PageInfo<FirstCheckProviderDto> pList = firstCheckSupplierApi.findHeadquartersPageInfo(pageInfo, firstProvider);
        List<FirstCheckProviderDto> firstCheckProviderVoList = pList.getList();

        List<SystemDictDto> dictDtos = getSystemDict(DictConstant.PROVIDER_TYPE, organSign);
        Map<Integer,String> providerType = new HashMap<>();
        for(SystemDictDto sdd:dictDtos) {
            if (sdd.getBussinessId().equals(DictConstant.PROVIDER_TYPE)) {
                providerType.put(sdd.getId(), sdd.getName());
            }
        }
        for (FirstCheckProviderDto vo : firstCheckProviderVoList) {
            ProviderDto providerinfo = vo.getProviderinfo();
            if (providerinfo != null && providerinfo.getProviderType() != null) {
                providerinfo.setProviderTypeStr(providerType.get(providerinfo.getProviderType()));
                vo.setProviderTypeStr(providerType.get(providerinfo.getProviderType()));
                logger.info("providerType的name:"+providerType.get(providerinfo.getProviderType()));
                logger.info("FirstCheckProviderDto的providerTypeStr:"+vo.getProviderTypeStr());
            }
            if(providerinfo.getUsed()!=null &&providerinfo.getUsed()==1){
                providerinfo.setUsedStr("是");
                vo.setUsedStr("是");
            }else if(providerinfo.getUsed()!=null &&providerinfo.getUsed()==0){
                providerinfo.setUsedStr("否");
                vo.setUsedStr("否");
            }
            if(vo.getStatus()!=null &&vo.getStatus()==1){
                providerinfo.setStatusStr("审批中");
                vo.setStatusStr("审批中");
            }else if(vo.getStatus()!=null &&vo.getStatus()==6){
                providerinfo.setStatusStr("已驳回");
                vo.setStatusStr("已驳回");
            }else if(vo.getStatus()!=null &&vo.getStatus()==7){
                providerinfo.setStatusStr("已通过");
                vo.setStatusStr("已通过");
            }
            String createUser = vo.getCreateUser();
            SaaSEmployeeDto employee= employeeApi.getEmployeeById(Integer.parseInt(createUser));
            if(employee!=null ){
                vo.setCreateUser(employee.getName());

            }else {
                vo.setCreateUser("");
            }

            if(vo.getApproveUserRoleId()!=null){
                logger.info("queryFirstProviderList result:{},roleid:{}",result,vo.getApproveUserRoleId());
                SaasRoleDto role= roleApi.getRoleById(Integer.parseInt(vo.getApproveUserRoleId().toString()));
                if(role!=null) {
                    vo.setApproveUserRoleName(role.getName());
                }
                Integer roleId = Integer.parseInt(vo.getApproveUserRoleId().toString());
                if(result.contains(roleId)){
                    vo.setApproveYn((byte)1);
                }else{
                    vo.setApproveYn((byte)0);
                }
            }else{
                vo.setApproveYn((byte)-1);
            }

        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(pList), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getFirstProvider(HttpServletRequest request,  @ApiParam(value = "首营供应商查询对象", required = true) @Valid @RequestBody FirstCheckProviderDto firstProvider) {
        if ( StringUtils.isEmpty(firstProvider.getId()) ||StringUtils.isEmpty(firstProvider.getProviderPref()))
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(""), HttpStatus.OK);
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        ResultVO<List<Integer>> listResultVO = roleApi.queryRoleIdsByEmployeeId(Integer.parseInt(employee));
        Long providerId = supplierBasicInfoApi.getProviderIdByPref(firstProvider.getProviderPref(), organSign);
        com.xyy.saas.supplier.dto.ProviderVoDto provider = supplierBasicInfoApi.getProviderInfoById(providerId, organSign);
        ApproveHistoryDto approveHistory = firstCheckSupplierApi.getApproveHistoryByProviderPrefOrganSign(firstProvider.getProviderPref(), organSign);
        com.xyy.saas.supplier.dto.FirstProviderQueryDto firstProviderQueryDto = new com.xyy.saas.supplier.dto.FirstProviderQueryDto();
        firstProviderQueryDto.setId(firstProvider.getId());
        firstProviderQueryDto.setProviderPref(firstProvider.getProviderPref());
        firstProviderQueryDto.setProviderinfo(provider);
        firstProviderQueryDto.setApproveHistoryDto(approveHistory);
        List<Integer> result = listResultVO.getResult();
            try {
                WorkflowAuditProcessQueryDto workflowAuditProcessQueryDto = workflowAuditTaskService.queryAuditTask(approveHistory.getBillNo(), BusinessFlowEnum.SAAS_Commodity_FirstSale_Supplier.getValue(), TenantIdEnum.SAAS.getValue());
               if(workflowAuditProcessQueryDto!=null){
                   WorkflowAuditTaskDto activeTask = workflowAuditProcessQueryDto.getActiveTask();
                   if(activeTask!=null){
                       Long candidateGroupId = activeTask.getCandidateGroupId();
                       String taskId = activeTask.getTaskId();
                       approveHistory.setApproveUserRoleId(candidateGroupId);
                       firstProviderQueryDto.setTaskId(taskId);
                   }
                }

            } catch (WorkflowException e) {
                logger.error("Controller获取当前审批任务失败e:{}",e);
            }
        logger.info("result:"+ JSON.toJSONString(result));
        Long approveUserRoleId = approveHistory.getApproveUserRoleId();
        logger.info("Controller首营供应商当前登录人拥有的角色:"+JSON.toJSONString(result));
        logger.info("Controller首营供应商获取当前代办角色:"+approveUserRoleId);
        firstProviderQueryDto.setApproveYn((byte)0);
            if(approveUserRoleId!=null){
                Integer roleId = Integer.parseInt(approveUserRoleId.toString());
                if(result.contains(roleId)){
                    firstProviderQueryDto.setApproveYn((byte)1);

                }
            }
        logger.info("approveUserRoleId:"+ approveUserRoleId);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(firstProviderQueryDto), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> printProvider(HttpServletRequest request,@ApiParam(value = "首营供应商查询对象", required = true) @Valid @RequestBody FirstCheckProviderDto firstProvider) {
        String pref = firstProvider.getProviderPref();
        String organSign = firstProvider.getOrgansign();
        if(StringUtils.isEmpty(pref) || StringUtils.isEmpty(organSign)){
            return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.ERROR,"参数错误"), HttpStatus.BAD_REQUEST);
        }
        FirstCheckProviderVoDto dto = firstCheckSupplierApi.getFirstCheckProviderChainByPref(pref,organSign);

        List<SystemDictDto> dictDtos = getSystemDict(DictConstant.PROVIDER_TYPE, organSign);
        Map<Integer,String> providerType = new HashMap<>();
        for(SystemDictDto sdd:dictDtos) {
            if (sdd.getBussinessId().equals(DictConstant.PROVIDER_TYPE)) {
                providerType.put(sdd.getId(), sdd.getName());
            }
        }
        ProviderDto providerinfo = dto.getProviderinfo();
        if (providerinfo != null && providerinfo.getProviderType() != null) {
            providerinfo.setProviderTypeStr(providerType.get(providerinfo.getProviderType()));
            dto.setProviderTypeStr(providerType.get(providerinfo.getProviderType()));

        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(dto), HttpStatus.OK);
    }


    /**
     * 如果是门店机构号，则获取对应的总部机构号
     *
     * @param organSign
     * @return
     */
    private String getHeadquartersOrg(String organSign) {
        String headquartersOrg = "";
        SaaSDrugstoreDto drugstore = drugstoreApi.getDrugstoreByOrganSign(organSign);
        // 兼容连锁门店，根据连锁门店获取总部的机构号
        if (drugstore != null && drugstore.getBizModel() == DrugstoreBizModelEnum.CHAIN_STORE.getKey()
                && drugstore.getOrganSignType() == DrugstoreTypeEnum.DRUGSTORE.getKey()) {
            headquartersOrg = drugstore.getHeadquartersOrganSign();
            logger.info("当前机构为连锁门店organSign:{},总部机构headquartersOrg:{}",organSign,headquartersOrg);
        }
        return headquartersOrg;
    }

    /**
     * 根据businessId查询对应字典数据
     *
     * @param businessId
     * @return
     */
    private List<SystemDictDto> getSystemDict(Integer businessId,String organSign) {

        String headquartersOrg = getHeadquartersOrg(organSign);
        if (!StringUtils.isEmpty(headquartersOrg)) {
            organSign = headquartersOrg;
        }
        List<SystemDictDto> dictDtos= new ArrayList<>();
        SystemDictQueryDto queryDto = new SystemDictQueryDto();
        queryDto.setBusinessId(businessId);
        queryDto.setStatus((byte)1);
        queryDto.setPageNo(1);
        queryDto.setPageSize(1000);
        queryDto.setYn((byte)1);
        PageInfo<SystemDictDto> pageByCondition = systemDictApi.findPageByCondition(queryDto, organSign);
        if(pageByCondition!=null&&!CollectionUtils.isEmpty(pageByCondition.getList())){
            dictDtos.addAll(pageByCondition.getList());
            int pages = pageByCondition.getPages();
            for (int i=1;i<pages;i++){
                queryDto.setPageNo(i+1);
                PageInfo<SystemDictDto> pageInfo = systemDictApi.findPageByCondition(queryDto, organSign);
                if(pageInfo!=null&&!CollectionUtils.isEmpty(pageInfo.getList())) {
                    dictDtos.addAll(pageInfo.getList());
                }
            }
        }
        return  dictDtos;
    }
}
