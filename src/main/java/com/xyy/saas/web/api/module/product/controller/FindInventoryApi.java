/**
 * NOTE: This class is auto generated by the swagger code generator program (2.2.3).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.web.api.module.product.model.InventoryVo;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T18:13:39.358+08:00")
@RequestMapping("/product")
@Api(value = "findInventory", description = "the findInventory API")
public interface FindInventoryApi {

    @ApiOperation(value = "查询所有商品库存", notes = "查询所有商品库存", response = ResultVO.class, tags={ "findInventory", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Invalid input", response = ResultVO.class) })
    
    @RequestMapping(value = "/findInventory",
        produces = { "application/xml", "application/json" }, 
        consumes = { "application/json", "application/xml" },
        method = RequestMethod.POST)
    ResponseEntity<ResultVO> findInventory(@ApiParam(value = "库存Vo", required = true) @Valid @RequestBody InventoryVo inventory);

    @ApiOperation(value = "修改库存错误数据", notes = "修改库存错误数据", response = ResultVO.class, tags={ "updateInventoryNew", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "Invalid input", response = ResultVO.class) })

    @RequestMapping(value = "/updateInventoryNew",
            produces = { "application/xml", "application/json" },
            consumes = { "application/json", "application/xml" },
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> updateInventoryNew();


}
