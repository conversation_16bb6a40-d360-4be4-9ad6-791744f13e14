package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

//import sun.tools.tree.ByteExpression;

/**
 * 商品信息实体
 */
@ApiModel(description = "商品信息实体")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T15:22:37.401+08:00")

public class SaasProductBaseInfo extends Page {
  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("pref")
  private String pref = null;

  @JsonProperty("mnemonicCode")
  private String mnemonicCode = null;

  @JsonProperty("mixedQuery")
  private String mixedQuery = null;

  @JsonProperty("commonName")
  private String commonName = null;

  @JsonProperty("productName")
  private String productName = null;

  @JsonProperty("standardLibraryId")
  private Long standardLibraryId = null;

  @JsonProperty("attributeSpecification")
  private String attributeSpecification = null;

  @JsonProperty("unitId")
  private Integer unitId = null;

  @JsonProperty("dosageFormId")
  private Long dosageFormId = null;

  @JsonProperty("barCode")
  private String barCode = null;

  @JsonProperty("approvalNumber")
  private String approvalNumber = null;

  @JsonProperty("manufacturer")
  private String manufacturer = null;

  @JsonProperty("producingArea")
  private String producingArea = null;

  @JsonProperty("productType")
  private Integer productType = null;

  @JsonProperty("storageCondition")
  private Integer storageCondition = null;

  @JsonProperty("abcDividing")
  private Integer abcDividing = null;

  @JsonProperty("shelfPosition")
  private Integer shelfPosition = null;

  @JsonProperty("containingHempYn")
  private Integer containingHempYn = null;

  @JsonProperty("prescriptionClassification")
  private Integer prescriptionClassification = null;

  @JsonProperty("prescriptionYn")
  private Byte prescriptionYn = null;

  @JsonProperty("businessScope")
  private Integer businessScope = null;

  @JsonProperty("maintenanceType")
  private Byte maintenanceType = null;

  @JsonProperty("productFunctionCatagory")
  private Integer productFunctionCatagory = null;

  @JsonProperty("incomeTaxRate")
  private BigDecimal incomeTaxRate = null;

  @JsonProperty("ouputTaxRate")
  private BigDecimal ouputTaxRate = null;

  @JsonProperty("retailPrice")
  private BigDecimal retailPrice = null;

  @JsonProperty("vipPrice")
  private BigDecimal vipPrice = null;

  @JsonProperty("costPrice")
  private BigDecimal costPrice = null;

  @JsonProperty("scoreProductYn")
  private Byte scoreProductYn = null;

  @JsonProperty("scoreRate")
  private BigDecimal scoreRate = null;

  @JsonProperty("storeMaxLimit")
  private BigDecimal storeMaxLimit = null;

  @JsonProperty("storeMinLimit")
  private BigDecimal storeMinLimit = null;

  @JsonProperty("parentProductId")
  private Long parentProductId = null;

  @JsonProperty("status")
  private Byte status = null;

  @JsonProperty("scatteredYn")
  private Byte scatteredYn = null;

  @JsonProperty("nodeType")
  private Byte nodeType = null;

  @JsonProperty("collectType")
  private Byte collectType = null;

  @JsonProperty("freezeType")
  private Byte freezeType = null;

  @JsonProperty("createType")
  private Byte createType = null;

  @JsonProperty("createUser")
  private String createUser = null;

  @JsonProperty("createTime")
  private Date createTime = null;

  @JsonProperty("updateUser")
  private String updateUser = null;

  @JsonProperty("yn")
  private Byte yn = null;

  @JsonProperty("remark")
  private String remark = null;

  @JsonProperty("used")
  private Byte used = null;

  @JsonProperty("special")
  private Byte special = null;

  @JsonProperty("imgUrl")
  private String imgUrl = null;

  @JsonProperty("lastPurchase")
  private String lastPurchase = null;

  @JsonProperty("baseVersion")
  private String baseVersion = null;

  @JsonProperty("organSign")
  private String organSign = null;

  @JsonProperty("isHidden")
  private Byte isHidden = null;

  public SaasProductBaseInfo id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * 商品id
   * @return id
  **/
  @ApiModelProperty(value = "商品id")


  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public SaasProductBaseInfo pref(String pref) {
    this.pref = pref;
    return this;
  }

   /**
   * 商品编号
   * @return pref
  **/
  @ApiModelProperty(value = "商品编号")


  public String getPref() {
    return pref;
  }

  public void setPref(String pref) {
    this.pref = pref;
  }

  public SaasProductBaseInfo mnemonicCode(String mnemonicCode) {
    this.mnemonicCode = mnemonicCode;
    return this;
  }

   /**
   * 助记码
   * @return mnemonicCode
  **/
  @ApiModelProperty(value = "助记码")


  public String getMnemonicCode() {
    return mnemonicCode;
  }

  public void setMnemonicCode(String mnemonicCode) {
    this.mnemonicCode = mnemonicCode;
  }

  public SaasProductBaseInfo mixedQuery(String mixedQuery) {
    this.mixedQuery = mixedQuery;
    return this;
  }

   /**
   * 混合查询
   * @return mixedQuery
  **/
  @ApiModelProperty(value = "混合查询")


  public String getMixedQuery() {
    return mixedQuery;
  }

  public void setMixedQuery(String mixedQuery) {
    this.mixedQuery = mixedQuery;
  }

  public SaasProductBaseInfo commonName(String commonName) {
    this.commonName = commonName;
    return this;
  }

   /**
   * 通用名 
   * @return commonName
  **/
  @ApiModelProperty(value = "通用名 ")


  public String getCommonName() {
    return commonName;
  }

  public void setCommonName(String commonName) {
    this.commonName = commonName;
  }

  public SaasProductBaseInfo productName(String productName) {
    this.productName = productName;
    return this;
  }

   /**
   * 商品名称
   * @return productName
  **/
  @ApiModelProperty(value = "商品名称")


  public String getProductName() {
    return productName;
  }

  public void setProductName(String productName) {
    this.productName = productName;
  }

  public SaasProductBaseInfo standardLibraryId(Long standardLibraryId) {
    this.standardLibraryId = standardLibraryId;
    return this;
  }

   /**
   * 发票内容，下拉选择
   * @return standardLibraryId
  **/
  @ApiModelProperty(value = "发票内容，下拉选择")


  public Long getStandardLibraryId() {
    return standardLibraryId;
  }

  public void setStandardLibraryId(Long standardLibraryId) {
    this.standardLibraryId = standardLibraryId;
  }

  public SaasProductBaseInfo attributeSpecification(String attributeSpecification) {
    this.attributeSpecification = attributeSpecification;
    return this;
  }

   /**
   * 规格/型号
   * @return attributeSpecification
  **/
  @ApiModelProperty(value = "规格/型号")


  public String getAttributeSpecification() {
    return attributeSpecification;
  }

  public void setAttributeSpecification(String attributeSpecification) {
    this.attributeSpecification = attributeSpecification;
  }

  public SaasProductBaseInfo unitId(Integer unitId) {
    this.unitId = unitId;
    return this;
  }

   /**
   * 单位ID
   * @return unitId
  **/
  @ApiModelProperty(value = "单位ID")


  public Integer getUnitId() {
    return unitId;
  }

  public void setUnitId(Integer unitId) {
    this.unitId = unitId;
  }

  public SaasProductBaseInfo dosageFormId(Long dosageFormId) {
    this.dosageFormId = dosageFormId;
    return this;
  }

   /**
   * 剂型ID
   * @return dosageFormId
  **/
  @ApiModelProperty(value = "剂型ID")


  public Long getDosageFormId() {
    return dosageFormId;
  }

  public void setDosageFormId(Long dosageFormId) {
    this.dosageFormId = dosageFormId;
  }

  public SaasProductBaseInfo barCode(String barCode) {
    this.barCode = barCode;
    return this;
  }

   /**
   * 条形码
   * @return barCode
  **/
  @ApiModelProperty(value = "条形码")


  public String getBarCode() {
    return barCode;
  }

  public void setBarCode(String barCode) {
    this.barCode = barCode;
  }

  public SaasProductBaseInfo approvalNumber(String approvalNumber) {
    this.approvalNumber = approvalNumber;
    return this;
  }

   /**
   * 批准文号
   * @return approvalNumber
  **/
  @ApiModelProperty(value = "批准文号")


  public String getApprovalNumber() {
    return approvalNumber;
  }

  public void setApprovalNumber(String approvalNumber) {
    this.approvalNumber = approvalNumber;
  }

  public SaasProductBaseInfo manufacturer(String manufacturer) {
    this.manufacturer = manufacturer;
    return this;
  }

   /**
   * 生产厂家
   * @return manufacturer
  **/
  @ApiModelProperty(value = "生产厂家")


  public String getManufacturer() {
    return manufacturer;
  }

  public void setManufacturer(String manufacturer) {
    this.manufacturer = manufacturer;
  }

  public SaasProductBaseInfo producingArea(String producingArea) {
    this.producingArea = producingArea;
    return this;
  }

   /**
   * 产地
   * @return producingArea
  **/
  @ApiModelProperty(value = "产地")


  public String getProducingArea() {
    return producingArea;
  }

  public void setProducingArea(String producingArea) {
    this.producingArea = producingArea;
  }

  public SaasProductBaseInfo productType(Integer productType) {
    this.productType = productType;
    return this;
  }

   /**
   * 商品类别ID
   * @return productType
  **/
  @ApiModelProperty(value = "商品类别ID")


  public Integer getProductType() {
    return productType;
  }

  public void setProductType(Integer productType) {
    this.productType = productType;
  }

  public SaasProductBaseInfo storageCondition(Integer storageCondition) {
    this.storageCondition = storageCondition;
    return this;
  }

   /**
   * 存储条件
   * @return storageCondition
  **/
  @ApiModelProperty(value = "存储条件")


  public Integer getStorageCondition() {
    return storageCondition;
  }

  public void setStorageCondition(Integer storageCondition) {
    this.storageCondition = storageCondition;
  }

  public SaasProductBaseInfo abcDividing(Integer abcDividing) {
    this.abcDividing = abcDividing;
    return this;
  }

   /**
   * ABC分类
   * @return abcDividing
  **/
  @ApiModelProperty(value = "ABC分类")


  public Integer getAbcDividing() {
    return abcDividing;
  }

  public void setAbcDividing(Integer abcDividing) {
    this.abcDividing = abcDividing;
  }

  public SaasProductBaseInfo shelfPosition(Integer shelfPosition) {
    this.shelfPosition = shelfPosition;
    return this;
  }

   /**
   * 架位
   * @return shelfPosition
  **/
  @ApiModelProperty(value = "架位")


  public Integer getShelfPosition() {
    return shelfPosition;
  }

  public void setShelfPosition(Integer shelfPosition) {
    this.shelfPosition = shelfPosition;
  }

  public SaasProductBaseInfo containingHempYn(Integer containingHempYn) {
    this.containingHempYn = containingHempYn;
    return this;
  }

   /**
   * 是否含麻
   * @return containingHempYn
  **/
  @ApiModelProperty(value = "是否含麻")


  public Integer getContainingHempYn() {
    return containingHempYn;
  }

  public void setContainingHempYn(Integer containingHempYn) {
    this.containingHempYn = containingHempYn;
  }

  public SaasProductBaseInfo prescriptionClassification(Integer prescriptionClassification) {
    this.prescriptionClassification = prescriptionClassification;
    return this;
  }

   /**
   * 处方分类
   * @return prescriptionClassification
  **/
  @ApiModelProperty(value = "处方分类")


  public Integer getPrescriptionClassification() {
    return prescriptionClassification;
  }

  public void setPrescriptionClassification(Integer prescriptionClassification) {
    this.prescriptionClassification = prescriptionClassification;
  }

  public SaasProductBaseInfo prescriptionYn(Byte prescriptionYn) {
    this.prescriptionYn = prescriptionYn;
    return this;
  }

   /**
   * 是否处方
   * @return prescriptionYn
  **/
  @ApiModelProperty(value = "是否处方")


  public Byte getPrescriptionYn() {
    return prescriptionYn;
  }

  public void setPrescriptionYn(Byte prescriptionYn) {
    this.prescriptionYn = prescriptionYn;
  }

  public SaasProductBaseInfo businessScope(Integer businessScope) {
    this.businessScope = businessScope;
    return this;
  }

   /**
   * 经营范围
   * @return businessScope
  **/
  @ApiModelProperty(value = "经营范围")


  public Integer getBusinessScope() {
    return businessScope;
  }

  public void setBusinessScope(Integer businessScope) {
    this.businessScope = businessScope;
  }

  public SaasProductBaseInfo maintenanceType(Byte maintenanceType) {
    this.maintenanceType = maintenanceType;
    return this;
  }

   /**
   * 养护类型
   * @return maintenanceType
  **/
  @ApiModelProperty(value = "养护类型")


  public Byte getMaintenanceType() {
    return maintenanceType;
  }

  public void setMaintenanceType(Byte maintenanceType) {
    this.maintenanceType = maintenanceType;
  }

  public SaasProductBaseInfo productFunctionCatagory(Integer productFunctionCatagory) {
    this.productFunctionCatagory = productFunctionCatagory;
    return this;
  }

   /**
   * 商品功能分类
   * @return productFunctionCatagory
  **/
  @ApiModelProperty(value = "商品功能分类")


  public Integer getProductFunctionCatagory() {
    return productFunctionCatagory;
  }

  public void setProductFunctionCatagory(Integer productFunctionCatagory) {
    this.productFunctionCatagory = productFunctionCatagory;
  }

  public SaasProductBaseInfo incomeTaxRate(BigDecimal incomeTaxRate) {
    this.incomeTaxRate = incomeTaxRate;
    return this;
  }

   /**
   * 进项税率
   * @return incomeTaxRate
  **/
  @ApiModelProperty(value = "进项税率")

  @Valid

  public BigDecimal getIncomeTaxRate() {
    return incomeTaxRate;
  }

  public void setIncomeTaxRate(BigDecimal incomeTaxRate) {
    this.incomeTaxRate = incomeTaxRate;
  }

  public SaasProductBaseInfo ouputTaxRate(BigDecimal ouputTaxRate) {
    this.ouputTaxRate = ouputTaxRate;
    return this;
  }

   /**
   * 销项税率
   * @return ouputTaxRate
  **/
  @ApiModelProperty(value = "销项税率")

  @Valid

  public BigDecimal getOuputTaxRate() {
    return ouputTaxRate;
  }

  public void setOuputTaxRate(BigDecimal ouputTaxRate) {
    this.ouputTaxRate = ouputTaxRate;
  }

  public SaasProductBaseInfo retailPrice(BigDecimal retailPrice) {
    this.retailPrice = retailPrice;
    return this;
  }

   /**
   * 零售价
   * @return retailPrice
  **/
  @ApiModelProperty(value = "零售价")

  @Valid

  public BigDecimal getRetailPrice() {
    return retailPrice;
  }

  public void setRetailPrice(BigDecimal retailPrice) {
    this.retailPrice = retailPrice;
  }

  public SaasProductBaseInfo vipPrice(BigDecimal vipPrice) {
    this.vipPrice = vipPrice;
    return this;
  }

   /**
   * 会员价
   * @return vipPrice
  **/
  @ApiModelProperty(value = "会员价")

  @Valid

  public BigDecimal getVipPrice() {
    return vipPrice;
  }

  public void setVipPrice(BigDecimal vipPrice) {
    this.vipPrice = vipPrice;
  }

  public SaasProductBaseInfo costPrice(BigDecimal costPrice) {
    this.costPrice = costPrice;
    return this;
  }

   /**
   * 成本价
   * @return costPrice
  **/
  @ApiModelProperty(value = "成本价")

  @Valid

  public BigDecimal getCostPrice() {
    return costPrice;
  }

  public void setCostPrice(BigDecimal costPrice) {
    this.costPrice = costPrice;
  }

  public SaasProductBaseInfo scoreProductYn(Byte scoreProductYn) {
    this.scoreProductYn = scoreProductYn;
    return this;
  }

   /**
   * 是否积分商品
   * @return scoreProductYn
  **/
  @ApiModelProperty(value = "是否积分商品")


  public Byte getScoreProductYn() {
    return scoreProductYn;
  }

  public void setScoreProductYn(Byte scoreProductYn) {
    this.scoreProductYn = scoreProductYn;
  }

  public SaasProductBaseInfo scoreRate(BigDecimal scoreRate) {
    this.scoreRate = scoreRate;
    return this;
  }

   /**
   * 积分倍率
   * @return scoreRate
  **/
  @ApiModelProperty(value = "积分倍率")

  @Valid

  public BigDecimal getScoreRate() {
    return scoreRate;
  }

  public void setScoreRate(BigDecimal scoreRate) {
    this.scoreRate = scoreRate;
  }

  public SaasProductBaseInfo storeMaxLimit(BigDecimal storeMaxLimit) {
    this.storeMaxLimit = storeMaxLimit;
    return this;
  }

   /**
   * 库存上限
   * @return storeMaxLimit
  **/
  @ApiModelProperty(value = "库存上限")

  @Valid

  public BigDecimal getStoreMaxLimit() {
    return storeMaxLimit;
  }

  public void setStoreMaxLimit(BigDecimal storeMaxLimit) {
    this.storeMaxLimit = storeMaxLimit;
  }

  public SaasProductBaseInfo storeMinLimit(BigDecimal storeMinLimit) {
    this.storeMinLimit = storeMinLimit;
    return this;
  }

   /**
   * 库存下限
   * @return storeMinLimit
  **/
  @ApiModelProperty(value = "库存下限")

  @Valid

  public BigDecimal getStoreMinLimit() {
    return storeMinLimit;
  }

  public void setStoreMinLimit(BigDecimal storeMinLimit) {
    this.storeMinLimit = storeMinLimit;
  }

  public SaasProductBaseInfo parentProductId(Long parentProductId) {
    this.parentProductId = parentProductId;
    return this;
  }

   /**
   * 拆零商品关联的源id
   * @return parentProductId
  **/
  @ApiModelProperty(value = "拆零商品关联的源id")


  public Long getParentProductId() {
    return parentProductId;
  }

  public void setParentProductId(Long parentProductId) {
    this.parentProductId = parentProductId;
  }

  public SaasProductBaseInfo status(Byte status) {
    this.status = status;
    return this;
  }

   /**
   * 状态：1--审核通过，2--审核中，3,--审核未通过
   * @return status
  **/
  @ApiModelProperty(value = "状态：1--审核通过，2--审核中，3,--审核未通过")


  public Byte getStatus() {
    return status;
  }

  public void setStatus(Byte status) {
    this.status = status;
  }

  public SaasProductBaseInfo scatteredYn(Byte scatteredYn) {
    this.scatteredYn = scatteredYn;
    return this;
  }

   /**
   * 是否拆零，1--是，0--否
   * @return scatteredYn
  **/
  @ApiModelProperty(value = "是否拆零，1--是，0--否")


  public Byte getScatteredYn() {
    return scatteredYn;
  }

  public void setScatteredYn(Byte scatteredYn) {
    this.scatteredYn = scatteredYn;
  }

  public SaasProductBaseInfo nodeType(Byte nodeType) {
    this.nodeType = nodeType;
    return this;
  }

   /**
   * 节点类型
   * @return nodeType
  **/
  @ApiModelProperty(value = "节点类型")


  public Byte getNodeType() {
    return nodeType;
  }

  public void setNodeType(Byte nodeType) {
    this.nodeType = nodeType;
  }

  public SaasProductBaseInfo collectType(Byte collectType) {
    this.collectType = collectType;
    return this;
  }

   /**
   * 操作：0.停采 1.正常
   * @return collectType
  **/
  @ApiModelProperty(value = "操作：0.停采 1.正常")


  public Byte getCollectType() {
    return collectType;
  }

  public void setCollectType(Byte collectType) {
    this.collectType = collectType;
  }

  public SaasProductBaseInfo freezeType(Byte freezeType) {
    this.freezeType = freezeType;
    return this;
  }

   /**
   * 操作：0.冻结 1.解冻
   * @return freezeType
  **/
  @ApiModelProperty(value = "操作：0.冻结 1.解冻")


  public Byte getFreezeType() {
    return freezeType;
  }

  public void setFreezeType(Byte freezeType) {
    this.freezeType = freezeType;
  }

  public SaasProductBaseInfo createType(Byte createType) {
    this.createType = createType;
    return this;
  }

   /**
   * 创建类型：0.首营创建 1.拆零创建
   * @return createType
  **/
  @ApiModelProperty(value = "创建类型：0.首营创建 1.拆零创建")


  public Byte getCreateType() {
    return createType;
  }

  public void setCreateType(Byte createType) {
    this.createType = createType;
  }

  public SaasProductBaseInfo createUser(String createUser) {
    this.createUser = createUser;
    return this;
  }

   /**
   * 创建人
   * @return createUser
  **/
  @ApiModelProperty(value = "创建人")


  public String getCreateUser() {
    return createUser;
  }

  public void setCreateUser(String createUser) {
    this.createUser = createUser;
  }

  public SaasProductBaseInfo createTime(Date createTime) {
    this.createTime = createTime;
    return this;
  }

   /**
   * 创建时间
   * @return createTime
  **/
  @ApiModelProperty(value = "创建时间")


  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public SaasProductBaseInfo updateUser(String updateUser) {
    this.updateUser = updateUser;
    return this;
  }

   /**
   * 更新人
   * @return updateUser
  **/
  @ApiModelProperty(value = "更新人")


  public String getUpdateUser() {
    return updateUser;
  }

  public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
  }

  public SaasProductBaseInfo yn(Byte yn) {
    this.yn = yn;
    return this;
  }

   /**
   * 逻辑删除 1 有效 0 删除
   * @return yn
  **/
  @ApiModelProperty(value = "逻辑删除 1 有效 0 删除")


  public Byte getYn() {
    return yn;
  }

  public void setYn(Byte yn) {
    this.yn = yn;
  }

  public SaasProductBaseInfo remark(String remark) {
    this.remark = remark;
    return this;
  }

   /**
   * 备注
   * @return remark
  **/
  @ApiModelProperty(value = "备注")


  public String getRemark() {
    return remark;
  }

  public void setRemark(String remark) {
    this.remark = remark;
  }

  public SaasProductBaseInfo used(Byte used) {
    this.used = used;
    return this;
  }

   /**
   * 是否启用：0---未启用   1---启用
   * @return used
  **/
  @ApiModelProperty(value = "是否启用：0---未启用   1---启用")


  public Byte getUsed() {
    return used;
  }

  public void setUsed(Byte used) {
    this.used = used;
  }

  public SaasProductBaseInfo special(Byte special) {
    this.special = special;
    return this;
  }

   /**
   * 是否特价：0---否 1---是
   * @return special
  **/
  @ApiModelProperty(value = "是否特价：0---否 1---是")


  public Byte getSpecial() {
    return special;
  }

  public void setSpecial(Byte special) {
    this.special = special;
  }

  public SaasProductBaseInfo imgUrl(String imgUrl) {
    this.imgUrl = imgUrl;
    return this;
  }

   /**
   * 附件路径
   * @return imgUrl
  **/
  @ApiModelProperty(value = "附件路径")


  public String getImgUrl() {
    return imgUrl;
  }

  public void setImgUrl(String imgUrl) {
    this.imgUrl = imgUrl;
  }

  public SaasProductBaseInfo lastPurchase(String lastPurchase) {
    this.lastPurchase = lastPurchase;
    return this;
  }

   /**
   * 最后一次采购
   * @return lastPurchase
  **/
  @ApiModelProperty(value = "最后一次采购")


  public String getLastPurchase() {
    return lastPurchase;
  }

  public void setLastPurchase(String lastPurchase) {
    this.lastPurchase = lastPurchase;
  }

  public SaasProductBaseInfo baseVersion(String baseVersion) {
    this.baseVersion = baseVersion;
    return this;
  }

   /**
   * 操作版本号
   * @return baseVersion
  **/
  @ApiModelProperty(value = "操作版本号")


  public String getBaseVersion() {
    return baseVersion;
  }

  public void setBaseVersion(String baseVersion) {
    this.baseVersion = baseVersion;
  }

  public SaasProductBaseInfo isHidden(Byte isHidden) {
    this.isHidden = isHidden;
    return this;
  }

   /**
   * 是否隐藏
   * @return isHidden
  **/
  @ApiModelProperty(value = "是否隐藏")


  public Byte getIsHidden() {
    return isHidden;
  }

  public void setIsHidden(Byte isHidden) {
    this.isHidden = isHidden;
  }

  public SaasProductBaseInfo organSign(String organSign) {
    this.organSign = organSign;
    return this;
  }

  /**
   * 药店唯一标识
   * @return organSign
   **/
  @ApiModelProperty(value = "药店唯一标识")


  public String getOrganSign() {
    return organSign;
  }

  public void setOrganSign(String organSign) {
    this.organSign = organSign;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SaasProductBaseInfo saasProductBaseInfo = (SaasProductBaseInfo) o;
    return Objects.equals(this.id, saasProductBaseInfo.id) &&
        Objects.equals(this.pref, saasProductBaseInfo.pref) &&
        Objects.equals(this.mnemonicCode, saasProductBaseInfo.mnemonicCode) &&
        Objects.equals(this.mixedQuery, saasProductBaseInfo.mixedQuery) &&
        Objects.equals(this.commonName, saasProductBaseInfo.commonName) &&
        Objects.equals(this.productName, saasProductBaseInfo.productName) &&
        Objects.equals(this.standardLibraryId, saasProductBaseInfo.standardLibraryId) &&
        Objects.equals(this.attributeSpecification, saasProductBaseInfo.attributeSpecification) &&
        Objects.equals(this.unitId, saasProductBaseInfo.unitId) &&
        Objects.equals(this.dosageFormId, saasProductBaseInfo.dosageFormId) &&
        Objects.equals(this.barCode, saasProductBaseInfo.barCode) &&
        Objects.equals(this.approvalNumber, saasProductBaseInfo.approvalNumber) &&
        Objects.equals(this.manufacturer, saasProductBaseInfo.manufacturer) &&
        Objects.equals(this.producingArea, saasProductBaseInfo.producingArea) &&
        Objects.equals(this.productType, saasProductBaseInfo.productType) &&
        Objects.equals(this.storageCondition, saasProductBaseInfo.storageCondition) &&
        Objects.equals(this.abcDividing, saasProductBaseInfo.abcDividing) &&
        Objects.equals(this.shelfPosition, saasProductBaseInfo.shelfPosition) &&
        Objects.equals(this.containingHempYn, saasProductBaseInfo.containingHempYn) &&
        Objects.equals(this.prescriptionClassification, saasProductBaseInfo.prescriptionClassification) &&
        Objects.equals(this.prescriptionYn, saasProductBaseInfo.prescriptionYn) &&
        Objects.equals(this.businessScope, saasProductBaseInfo.businessScope) &&
        Objects.equals(this.maintenanceType, saasProductBaseInfo.maintenanceType) &&
        Objects.equals(this.productFunctionCatagory, saasProductBaseInfo.productFunctionCatagory) &&
        Objects.equals(this.incomeTaxRate, saasProductBaseInfo.incomeTaxRate) &&
        Objects.equals(this.ouputTaxRate, saasProductBaseInfo.ouputTaxRate) &&
        Objects.equals(this.retailPrice, saasProductBaseInfo.retailPrice) &&
        Objects.equals(this.vipPrice, saasProductBaseInfo.vipPrice) &&
        Objects.equals(this.costPrice, saasProductBaseInfo.costPrice) &&
        Objects.equals(this.scoreProductYn, saasProductBaseInfo.scoreProductYn) &&
        Objects.equals(this.scoreRate, saasProductBaseInfo.scoreRate) &&
        Objects.equals(this.storeMaxLimit, saasProductBaseInfo.storeMaxLimit) &&
        Objects.equals(this.storeMinLimit, saasProductBaseInfo.storeMinLimit) &&
        Objects.equals(this.parentProductId, saasProductBaseInfo.parentProductId) &&
        Objects.equals(this.status, saasProductBaseInfo.status) &&
        Objects.equals(this.scatteredYn, saasProductBaseInfo.scatteredYn) &&
        Objects.equals(this.nodeType, saasProductBaseInfo.nodeType) &&
        Objects.equals(this.collectType, saasProductBaseInfo.collectType) &&
        Objects.equals(this.freezeType, saasProductBaseInfo.freezeType) &&
        Objects.equals(this.createType, saasProductBaseInfo.createType) &&
        Objects.equals(this.createUser, saasProductBaseInfo.createUser) &&
        Objects.equals(this.createTime, saasProductBaseInfo.createTime) &&
        Objects.equals(this.updateUser, saasProductBaseInfo.updateUser) &&
        Objects.equals(this.yn, saasProductBaseInfo.yn) &&
        Objects.equals(this.remark, saasProductBaseInfo.remark) &&
        Objects.equals(this.used, saasProductBaseInfo.used) &&
        Objects.equals(this.special, saasProductBaseInfo.special) &&
        Objects.equals(this.imgUrl, saasProductBaseInfo.imgUrl) &&
        Objects.equals(this.lastPurchase, saasProductBaseInfo.lastPurchase) &&
        Objects.equals(this.baseVersion, saasProductBaseInfo.baseVersion) &&
        Objects.equals(this.isHidden, saasProductBaseInfo.isHidden) &&
        Objects.equals(this.organSign, saasProductBaseInfo.organSign);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, pref, mnemonicCode, mixedQuery, commonName, productName, standardLibraryId, attributeSpecification, unitId, dosageFormId, barCode, approvalNumber, manufacturer, producingArea, productType, storageCondition, abcDividing, shelfPosition, containingHempYn, prescriptionClassification, prescriptionYn, businessScope, maintenanceType, productFunctionCatagory, incomeTaxRate, ouputTaxRate, retailPrice, vipPrice, costPrice, scoreProductYn, scoreRate, storeMaxLimit, storeMinLimit, parentProductId, status, scatteredYn, nodeType, collectType, freezeType, createType, createUser, createTime, updateUser, yn, remark, used, special, imgUrl, lastPurchase, baseVersion, isHidden, organSign);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SaasProductBaseInfo {\n");

    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    pref: ").append(toIndentedString(pref)).append("\n");
    sb.append("    mnemonicCode: ").append(toIndentedString(mnemonicCode)).append("\n");
    sb.append("    mixedQuery: ").append(toIndentedString(mixedQuery)).append("\n");
    sb.append("    commonName: ").append(toIndentedString(commonName)).append("\n");
    sb.append("    productName: ").append(toIndentedString(productName)).append("\n");
    sb.append("    standardLibraryId: ").append(toIndentedString(standardLibraryId)).append("\n");
    sb.append("    attributeSpecification: ").append(toIndentedString(attributeSpecification)).append("\n");
    sb.append("    unitId: ").append(toIndentedString(unitId)).append("\n");
    sb.append("    dosageFormId: ").append(toIndentedString(dosageFormId)).append("\n");
    sb.append("    barCode: ").append(toIndentedString(barCode)).append("\n");
    sb.append("    approvalNumber: ").append(toIndentedString(approvalNumber)).append("\n");
    sb.append("    manufacturer: ").append(toIndentedString(manufacturer)).append("\n");
    sb.append("    producingArea: ").append(toIndentedString(producingArea)).append("\n");
    sb.append("    productType: ").append(toIndentedString(productType)).append("\n");
    sb.append("    storageCondition: ").append(toIndentedString(storageCondition)).append("\n");
    sb.append("    abcDividing: ").append(toIndentedString(abcDividing)).append("\n");
    sb.append("    shelfPosition: ").append(toIndentedString(shelfPosition)).append("\n");
    sb.append("    containingHempYn: ").append(toIndentedString(containingHempYn)).append("\n");
    sb.append("    prescriptionClassification: ").append(toIndentedString(prescriptionClassification)).append("\n");
    sb.append("    prescriptionYn: ").append(toIndentedString(prescriptionYn)).append("\n");
    sb.append("    businessScope: ").append(toIndentedString(businessScope)).append("\n");
    sb.append("    maintenanceType: ").append(toIndentedString(maintenanceType)).append("\n");
    sb.append("    productFunctionCatagory: ").append(toIndentedString(productFunctionCatagory)).append("\n");
    sb.append("    incomeTaxRate: ").append(toIndentedString(incomeTaxRate)).append("\n");
    sb.append("    ouputTaxRate: ").append(toIndentedString(ouputTaxRate)).append("\n");
    sb.append("    retailPrice: ").append(toIndentedString(retailPrice)).append("\n");
    sb.append("    vipPrice: ").append(toIndentedString(vipPrice)).append("\n");
    sb.append("    costPrice: ").append(toIndentedString(costPrice)).append("\n");
    sb.append("    scoreProductYn: ").append(toIndentedString(scoreProductYn)).append("\n");
    sb.append("    scoreRate: ").append(toIndentedString(scoreRate)).append("\n");
    sb.append("    storeMaxLimit: ").append(toIndentedString(storeMaxLimit)).append("\n");
    sb.append("    storeMinLimit: ").append(toIndentedString(storeMinLimit)).append("\n");
    sb.append("    parentProductId: ").append(toIndentedString(parentProductId)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    scatteredYn: ").append(toIndentedString(scatteredYn)).append("\n");
    sb.append("    nodeType: ").append(toIndentedString(nodeType)).append("\n");
    sb.append("    collectType: ").append(toIndentedString(collectType)).append("\n");
    sb.append("    freezeType: ").append(toIndentedString(freezeType)).append("\n");
    sb.append("    createType: ").append(toIndentedString(createType)).append("\n");
    sb.append("    createUser: ").append(toIndentedString(createUser)).append("\n");
    sb.append("    createTime: ").append(toIndentedString(createTime)).append("\n");
    sb.append("    updateUser: ").append(toIndentedString(updateUser)).append("\n");
    sb.append("    yn: ").append(toIndentedString(yn)).append("\n");
    sb.append("    remark: ").append(toIndentedString(remark)).append("\n");
    sb.append("    used: ").append(toIndentedString(used)).append("\n");
    sb.append("    special: ").append(toIndentedString(special)).append("\n");
    sb.append("    imgUrl: ").append(toIndentedString(imgUrl)).append("\n");
    sb.append("    lastPurchase: ").append(toIndentedString(lastPurchase)).append("\n");
    sb.append("    baseVersion: ").append(toIndentedString(baseVersion)).append("\n");
    sb.append("    isHidden: ").append(toIndentedString(isHidden)).append("\n");
    sb.append("    organSign: ").append(toIndentedString(organSign)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

