package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.api.TjOrganSignApi;
import com.xyy.saas.web.api.module.product.model.TjCheckQueryVo;
import com.xyy.saas.web.api.module.product.model.TjCheckResultVo;
import io.swagger.annotations.Api;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;

/**
 * @ClassName ProductTjCheckApiController
 * @Description 天津静海过检需求接口api实现类
 * <AUTHOR>
 * @Date 2019/10/25 13:10
 * @Version 1.0
 **/
@Api(value = "天津过检", description = "天津过检", tags = "天津过检")
@Controller
public class ProductTjCheckApiController implements ProductTjCheckApi {

    @Reference(version = "0.0.1")
    private TjOrganSignApi tjOrganSignApi;

    @Override
    public ResponseEntity<ResultVO> firstCheckToUser(HttpServletRequest request, @RequestBody TjCheckQueryVo queryVo) {
        Integer count = tjOrganSignApi.getCountByOrganSign(queryVo.getOrganSign());
        TjCheckResultVo tjCheckResultVo = new TjCheckResultVo();
        if (count != null && count > 0) {
            tjCheckResultVo.setIsOpen((byte) 1);
        }else{
            tjCheckResultVo.setIsOpen((byte) 0);
        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(tjCheckResultVo), HttpStatus.OK);
    }
}
