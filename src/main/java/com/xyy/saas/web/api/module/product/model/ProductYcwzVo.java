package com.xyy.saas.web.api.module.product.model;

import io.swagger.annotations.ApiModel;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ProductYcwzVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/6/12 15:42
 * @Version 1.0
 **/
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

@ApiModel(description = "商品类型实体对象")
public class ProductYcwzVo {
    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getAttributeSpecification() {
        return attributeSpecification;
    }

    public void setAttributeSpecification(String attributeSpecification) {
        this.attributeSpecification = attributeSpecification;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public BigDecimal getStockNum() {
        return stockNum;
    }

    public void setStockNum(BigDecimal stockNum) {
        this.stockNum = stockNum;
    }

    public BigDecimal getRetailPrice() {
        return retailPrice;
    }

    public void setRetailPrice(BigDecimal retailPrice) {
        this.retailPrice = retailPrice;
    }

    public BigDecimal getVipPrice() {
        return vipPrice;
    }

    public void setVipPrice(BigDecimal vipPrice) {
        this.vipPrice = vipPrice;
    }

    public String getProducingArea() {
        return producingArea;
    }

    public void setProducingArea(String producingArea) {
        this.producingArea = producingArea;
    }

    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    public String getInventoryNumber() {
        return inventoryNumber;
    }

    public void setInventoryNumber(String inventoryNumber) {
        this.inventoryNumber = inventoryNumber;
    }

    public Date getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }

    public Long getInventoryId() {
        return inventoryId;
    }

    public void setInventoryId(Long inventoryId) {
        this.inventoryId = inventoryId;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getCommodityVal() {
        return commodityVal;
    }

    public void setCommodityVal(String commodityVal) {
        this.commodityVal = commodityVal;
    }

    public List<String> getLotNumbers() {
        return lotNumbers;
    }

    public void setLotNumbers(List<String> lotNumbers) {
        this.lotNumbers = lotNumbers;
    }

    public Integer getMaintenanceType() {
        return maintenanceType;
    }

    public void setMaintenanceType(Integer maintenanceType) {
        this.maintenanceType = maintenanceType;
    }

    public Byte getIsHidden() {
        return isHidden;
    }

    public void setIsHidden(Byte isHidden) {
        this.isHidden = isHidden;
    }

    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    public String getPharmacyPref() {
        return pharmacyPref;
    }

    public void setPharmacyPref(String pharmacyPref) {
        this.pharmacyPref = pharmacyPref;
    }

    public Integer getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(Integer businessScope) {
        this.businessScope = businessScope;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Byte getPrescriptionYn() {
        return prescriptionYn;
    }

    public void setPrescriptionYn(Byte prescriptionYn) {
        this.prescriptionYn = prescriptionYn;
    }

    public String getRuleSpecification() {
        return ruleSpecification;
    }

    public void setRuleSpecification(String ruleSpecification) {
        this.ruleSpecification = ruleSpecification;
    }

    public Integer getRuleUnitId() {
        return ruleUnitId;
    }

    public void setRuleUnitId(Integer ruleUnitId) {
        this.ruleUnitId = ruleUnitId;
    }

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public Integer getAgentId() {
        return agentId;
    }

    public void setAgentId(Integer agentId) {
        this.agentId = agentId;
    }

    public Integer getPrescriptionId() {
        return prescriptionId;
    }

    public void setPrescriptionId(Integer prescriptionId) {
        this.prescriptionId = prescriptionId;
    }

    public Integer getContainingHempYn() {
        return containingHempYn;
    }

    public void setContainingHempYn(Integer containingHempYn) {
        this.containingHempYn = containingHempYn;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getLastPurchase() {
        return lastPurchase;
    }

    public void setLastPurchase(String lastPurchase) {
        this.lastPurchase = lastPurchase;
    }

    public BigDecimal getLastCostPrice() {
        return lastCostPrice;
    }

    public void setLastCostPrice(BigDecimal lastCostPrice) {
        this.lastCostPrice = lastCostPrice;
    }

    public Long getLotId() {
        return lotId;
    }

    public void setLotId(Long lotId) {
        this.lotId = lotId;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getProdFuncType() {
        return prodFuncType;
    }

    public void setProdFuncType(Integer prodFuncType) {
        this.prodFuncType = prodFuncType;
    }

    public BigDecimal getIncomeTaxRate() {
        return incomeTaxRate;
    }

    public void setIncomeTaxRate(BigDecimal incomeTaxRate) {
        this.incomeTaxRate = incomeTaxRate;
    }

    public BigDecimal getOuputTaxRate() {
        return ouputTaxRate;
    }

    public void setOuputTaxRate(BigDecimal ouputTaxRate) {
        this.ouputTaxRate = ouputTaxRate;
    }

    public BigDecimal getScoreRate() {
        return scoreRate;
    }

    public void setScoreRate(BigDecimal scoreRate) {
        this.scoreRate = scoreRate;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Byte getUsed() {
        return used;
    }

    public void setUsed(Byte used) {
        this.used = used;
    }

    public Byte getScoreProductYn() {
        return scoreProductYn;
    }

    public void setScoreProductYn(Byte scoreProductYn) {
        this.scoreProductYn = scoreProductYn;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public Integer getShelfPosition() {
        return shelfPosition;
    }

    public void setShelfPosition(Integer shelfPosition) {
        this.shelfPosition = shelfPosition;
    }

    public String getShelfPostionName() {
        return shelfPostionName;
    }

    public void setShelfPostionName(String shelfPostionName) {
        this.shelfPostionName = shelfPostionName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Integer getLoseEfficacyDay() {
        return loseEfficacyDay;
    }

    public void setLoseEfficacyDay(Integer loseEfficacyDay) {
        this.loseEfficacyDay = loseEfficacyDay;
    }

    public Date getProducedDate() {
        return producedDate;
    }

    public void setProducedDate(Date producedDate) {
        this.producedDate = producedDate;
    }

    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getPrescriptionName() {
        return prescriptionName;
    }

    public void setPrescriptionName(String prescriptionName) {
        this.prescriptionName = prescriptionName;
    }

    public String getFlagStr() {
        return flagStr;
    }

    public void setFlagStr(String flagStr) {
        this.flagStr = flagStr;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public Integer getStandardLibraryId() {
        return standardLibraryId;
    }

    public void setStandardLibraryId(Integer standardLibraryId) {
        this.standardLibraryId = standardLibraryId;
    }

    public BigDecimal getProductTaxPrice() {
        return productTaxPrice;
    }

    public void setProductTaxPrice(BigDecimal productTaxPrice) {
        this.productTaxPrice = productTaxPrice;
    }

    public String getSupplierMnemonicCode() {
        return supplierMnemonicCode;
    }

    public void setSupplierMnemonicCode(String supplierMnemonicCode) {
        this.supplierMnemonicCode = supplierMnemonicCode;
    }

    public BigDecimal getStoreMinLimit() {
        return storeMinLimit;
    }

    public void setStoreMinLimit(BigDecimal storeMinLimit) {
        this.storeMinLimit = storeMinLimit;
    }

    public BigDecimal getStoreMaxLimit() {
        return storeMaxLimit;
    }

    public void setStoreMaxLimit(BigDecimal storeMaxLimit) {
        this.storeMaxLimit = storeMaxLimit;
    }

    public String getGrossMargin() {
        return grossMargin;
    }

    public void setGrossMargin(String grossMargin) {
        this.grossMargin = grossMargin;
    }

    public String getCategoryIds() {
        return categoryIds;
    }

    public void setCategoryIds(String categoryIds) {
        this.categoryIds = categoryIds;
    }

    public String getUnmarketableDays() {
        return unmarketableDays;
    }

    public void setUnmarketableDays(String unmarketableDays) {
        this.unmarketableDays = unmarketableDays;
    }

    public Long getDosageFormId() {
        return dosageFormId;
    }

    public void setDosageFormId(Long dosageFormId) {
        this.dosageFormId = dosageFormId;
    }

    public String getDosageFormName() {
        return dosageFormName;
    }

    public void setDosageFormName(String dosageFormName) {
        this.dosageFormName = dosageFormName;
    }

    public Integer getPositionId() {
        return positionId;
    }

    public void setPositionId(Integer positionId) {
        this.positionId = positionId;
    }

    public String getPositionName() {
        return positionName;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public String getProductValidity() {
        return productValidity;
    }

    public void setProductValidity(String productValidity) {
        this.productValidity = productValidity;
    }

    public Byte getMedicalInsurance() {
        return medicalInsurance;
    }

    public void setMedicalInsurance(Byte medicalInsurance) {
        this.medicalInsurance = medicalInsurance;
    }

    public BigDecimal getStockAmount() {
        return stockAmount;
    }

    public void setStockAmount(BigDecimal stockAmount) {
        this.stockAmount = stockAmount;
    }

    public Integer getPrescriptionClassification() {
        return prescriptionClassification;
    }

    public void setPrescriptionClassification(Integer prescriptionClassification) {
        this.prescriptionClassification = prescriptionClassification;
    }

    public String getProviderPref() {
        return providerPref;
    }

    public void setProviderPref(String providerPref) {
        this.providerPref = providerPref;
    }

    public String getProviderName() {
        return providerName;
    }

    public void setProviderName(String providerName) {
        this.providerName = providerName;
    }

    public String getDrugPermissionPerson() {
        return drugPermissionPerson;
    }

    public void setDrugPermissionPerson(String drugPermissionPerson) {
        this.drugPermissionPerson = drugPermissionPerson;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getSystemTypeName() {
        return systemTypeName;
    }

    public void setSystemTypeName(String systemTypeName) {
        this.systemTypeName = systemTypeName;
    }

    public Integer getSystemType() {
        return systemType;
    }

    public void setSystemType(Integer systemType) {
        this.systemType = systemType;
    }

    public Integer getCheckType() {
        return checkType;
    }

    public void setCheckType(Integer checkType) {
        this.checkType = checkType;
    }

    public Date getLastInTime() {
        return lastInTime;
    }

    public void setLastInTime(Date lastInTime) {
        this.lastInTime = lastInTime;
    }

    public String getSord() {
        return sord;
    }

    public void setSord(String sord) {
        this.sord = sord;
    }

    public String getSidx() {
        return sidx;
    }

    public void setSidx(String sidx) {
        this.sidx = sidx;
    }

    public Integer getTotalAcount() {
        return totalAcount;
    }

    public void setTotalAcount(Integer totalAcount) {
        this.totalAcount = totalAcount;
    }

    public BigDecimal getRetailPriceAmount() {
        return retailPriceAmount;
    }

    public void setRetailPriceAmount(BigDecimal retailPriceAmount) {
        this.retailPriceAmount = retailPriceAmount;
    }

    private Integer page;
    private Integer rows;
    private Long productId;
    private String pref;
    private String productName;
    private String attributeSpecification;
    private Integer unitId;
    private String manufacturer;
    private BigDecimal stockNum;
    private BigDecimal retailPrice;
    private BigDecimal vipPrice;
    private String producingArea;
    private String approvalNumber;
    private String inventoryNumber;
    private Date expirationDate;
    private Long inventoryId;
    private String guid;
    private String commodityVal;
    private List<String> lotNumbers;
    private Integer maintenanceType;
    private Byte isHidden;
    private String commonName;
    private String pharmacyPref;
    private Integer businessScope;
    private Long id;
    private Byte prescriptionYn;
    private String ruleSpecification;
    private Integer ruleUnitId;
    private BigDecimal costPrice;
    private Integer agentId;
    private Integer prescriptionId;
    private Integer containingHempYn;
    private String remark;
    private String lastPurchase;
    private BigDecimal lastCostPrice;
    private Long lotId;
    private Integer categoryId;
    private Integer prodFuncType;
    private BigDecimal incomeTaxRate;
    private BigDecimal ouputTaxRate;
    private BigDecimal scoreRate;
    private Byte status;
    private Byte used;
    private Byte scoreProductYn;
    private String barCode;
    private Integer shelfPosition;
    private String shelfPostionName;
    private String unitName;
    private Integer loseEfficacyDay;
    private Date producedDate;
    private Integer flag;
    private String categoryName;
    private String agentName;
    private String prescriptionName;
    private String flagStr;
    private String organSign;
    private Integer standardLibraryId;
    private BigDecimal productTaxPrice;
    private String supplierMnemonicCode;
    private BigDecimal storeMinLimit;
    private BigDecimal storeMaxLimit;
    private String grossMargin;
    private String categoryIds;
    private String unmarketableDays;
    private Long dosageFormId;
    private String dosageFormName;
    private Integer positionId;
    private String positionName;
    private String productValidity;
    private Byte medicalInsurance;
    private BigDecimal stockAmount;
    private Integer prescriptionClassification;
    private String providerPref;
    private String providerName;
    private String drugPermissionPerson;
    private String areaCode;
    private String systemTypeName;
    private Integer systemType;
    private Integer checkType;
    private Date lastInTime;
    private String sord;
    private String sidx;
    private Integer totalAcount;
    private BigDecimal retailPriceAmount;
}
