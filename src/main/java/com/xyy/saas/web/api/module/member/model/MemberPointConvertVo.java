package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

/**
 * 会员积分兑换
 * <AUTHOR>
 */
public class MemberPointConvertVo {

	private  BigDecimal countPoint;
	private  List<ExchangeProductVo> productVoList;
	private  String guid;
	private  String organsign;
	private  String salesman;
	private  String ticketNo;

	@ApiModelProperty(value = "总计积分")
	public BigDecimal getCountPoint() {
		return countPoint;
	}

	public void setCountPoint(BigDecimal countPoint) {
		this.countPoint = countPoint;
	}


	@ApiModelProperty(value = "guid")
	public String getGuid() {
		return guid;
	}

	public void setGuid(String guid) {
		this.guid = guid;
	}


	@ApiModelProperty(value = "机构标识")
	public String getOrgansign() {
		return organsign;
	}

	public void setOrgansign(String organsign) {
		this.organsign = organsign;
	}


	@ApiModelProperty(value = "销售员 员工id")
	public String getSalesman() {
		return salesman;
	}

	public void setSalesman(String salesman) {
		this.salesman = salesman;
	}

	@ApiModelProperty(value = "兑换商品集合")
	public List<ExchangeProductVo> getProductVoList() {
		return productVoList;
	}

	public void setProductVoList(List<ExchangeProductVo> productVoList) {
		this.productVoList = productVoList;
	}


	@ApiModelProperty(value = "小票号")
	public String getTicketNo() {
		return ticketNo;
	}

	public void setTicketNo(String ticketNo) {
		this.ticketNo = ticketNo;
	}
}
