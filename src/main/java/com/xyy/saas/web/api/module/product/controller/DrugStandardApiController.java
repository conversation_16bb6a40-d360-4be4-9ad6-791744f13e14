package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.api.ProductDrugStandardApi;
import com.xyy.saas.product.core.dto.DrugStandardDto;
import com.xyy.saas.web.api.module.product.model.DrugStandardAreaVo;
import com.xyy.saas.web.api.module.product.model.DrugStandardQueryVo;
import com.xyy.saas.web.api.module.product.model.DrugStandardVo;
import com.xyy.saas.web.api.module.utils.DateUtil;
import com.xyy.saas.web.api.module.utils.StringUtils;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName DrugStandardApiController
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/16 12:24
 * @Version 1.0
 **/
@Controller
public class DrugStandardApiController implements DrugStandardApi {

    @Reference(version = "0.0.1")
    private ProductDrugStandardApi productDrugStandardApi;

    @Override
    public ResponseEntity<ResultVO> areaList(HttpServletRequest request) {
        List<DrugStandardAreaVo> areaVoList = new ArrayList<>();
        DrugStandardAreaVo vo = new DrugStandardAreaVo();
        vo.setAreaCode("1");
        vo.setAreaName("河北");
        areaVoList.add(vo);
        return new ResponseEntity<ResultVO>(new ResultVO(areaVoList), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> proList(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody DrugStandardQueryVo drugStandardQueryVo) {
        if(StringUtils.isEmpty(drugStandardQueryVo.getAreaCode())){
            new ResponseEntity<ResultVO>(ResultVO.createError("地区code必须填写"), HttpStatus.OK);
        }
        if(!drugStandardQueryVo.getAreaCode().equals("1")){
            new ResponseEntity<ResultVO>(ResultVO.createError("目前没有开通此地区药监数据导入功能"), HttpStatus.OK);
        }
        PageInfo pageInfo = new PageInfo();
        if(drugStandardQueryVo.getPage() == null){
            pageInfo.setPageNum(1);
        }else{
            pageInfo.setPageNum(drugStandardQueryVo.getPage());
        }
        if(drugStandardQueryVo.getRows() == null){
            pageInfo.setPageSize(50);
        }else{
            pageInfo.setPageSize(drugStandardQueryVo.getRows());
        }
        DrugStandardDto dto = new DrugStandardDto();
        ResultVO<PageInfo<DrugStandardDto>> resultVO = productDrugStandardApi.getDrugStandardPageByParams(pageInfo,dto);
        PageInfo<DrugStandardDto> dtoPageInfo = resultVO.getResult();
        PageInfo<DrugStandardVo> voPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(dtoPageInfo,voPageInfo);
        List<DrugStandardDto> dtos = dtoPageInfo.getList();
        List<DrugStandardVo> vos = new ArrayList<>();
        if(!CollectionUtils.isEmpty(dtos)){
            for(DrugStandardDto dto1:dtos){
                DrugStandardVo vo1 = new DrugStandardVo();
                BeanUtils.copyProperties(dto1,vo1);
//                vo1.setUpdateTimeStr(DateUtil.parseDateToStr(dto1.getUpdateTime(),DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS));
                vos.add(vo1);
            }
        }
        voPageInfo.setList(vos);
        return new ResponseEntity<ResultVO>(new ResultVO(voPageInfo), HttpStatus.OK);
    }
}
