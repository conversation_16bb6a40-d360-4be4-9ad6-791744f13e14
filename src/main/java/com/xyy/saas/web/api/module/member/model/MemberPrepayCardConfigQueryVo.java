package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 会员储值设置查询Vo
 * <AUTHOR>
 */
@ApiModel(description = "会员储值设置查询")
public class MemberPrepayCardConfigQueryVo implements Serializable {

    private static final long serialVersionUID = -1303831178297473789L;
    /**
     * 会员等级id
     */
    @ApiModelProperty(value = "会员等级id", required = true, example = "102")
    private Long memberLevelId;

    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识", hidden = true)
    private String organSign;

    public Long getMemberLevelId() {
        return memberLevelId;
    }

    public void setMemberLevelId(Long memberLevelId) {
        this.memberLevelId = memberLevelId;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    @Override
    public String toString() {
        return "MemberPrepayCardConfigQueryVo{" +
                "memberLevelId='" + memberLevelId + '\'' +
                ", organSign='" + organSign + '\'' +
                '}';
    }
}