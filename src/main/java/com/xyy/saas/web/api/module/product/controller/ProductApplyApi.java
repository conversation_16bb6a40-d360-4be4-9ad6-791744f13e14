package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.dto.ProductApplyDetailDto;
import com.xyy.saas.product.core.dto.ProductDto;
import com.xyy.saas.web.api.module.product.model.*;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RequestMapping("/product")
@Api(value = "连锁门店商品提报接口", description = "连锁门店商品提报接口")
public interface ProductApplyApi {

    /**
     * 门店发起商品提报列表查询接口
     * @return
     */
    @ApiOperation(value = "门店发起商品提报列表查询接口", notes = "门店发起商品提报列表查询接口", response = ProductApplyVo.class, tags={ "连锁门店商品提报接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ProductApplyVo.class) })
    @RequestMapping(value = "/productApply/query",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> proList(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody ProductApplyQueryVo queryVo);

    /**
     * 总部发起门店商品提报列表
     * @return
     */
    @ApiOperation(value = "总部发起门店商品提报列表查询接口", notes = "总部发起门店商品提报列表查询接口", response = ProductApplyZbVo.class, tags={ "连锁门店商品提报接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ProductApplyZbVo.class) })
    @RequestMapping(value = "/zBproductApply/query",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> zBproList(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody ProductZbApplyQueryVo queryVo);

    /**
     * 总部发起门店商品提报列表
     * @return
     */
    @ApiOperation(value = "总部发起提报商品提取弹窗列表查询接口", notes = "总部发起提报商品提取弹窗列表查询接口", response = ProductApplyZbDtqVo.class, tags={ "连锁门店商品提报接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ProductApplyZbDtqVo.class) })
    @RequestMapping(value = "/zBdtqProductApply/query",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> zBdtqProList(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody ProductZbDtqApplyQueryVo queryVo);

    /**
     * 获取来源门店下拉列表接口
     * @param request
     * @return
     */
    @ApiOperation(value = "获取来源门店下拉列表接口", notes = "获取来源门店下拉列表接口", response = AdjustOrganSignVo.class, tags={ "连锁门店商品提报接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = AdjustOrganSignVo.class) })
    @RequestMapping(value = "/zBdtqProductApply/findAllOrganSigns",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> findAllOrganSigns(HttpServletRequest request);

    /**
     * 总部获取待提取数量接口
     * @param request
     * @return
     */
    @ApiOperation(value = "总部获取待提取数量接口", notes = "总部获取待提取数量接口", response = AllDtqProductNumVo.class, tags={ "连锁门店商品提报接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = AllDtqProductNumVo.class) })
    @RequestMapping(value = "/zBdtqProductApply/findAllDtqProductNum",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> findAllDtqProductNum(HttpServletRequest request);

    /**
     * 总部商品提报单作废接口
     * @param request
     * @return
     */
    @ApiOperation(value = "总部商品提报单作废接口", notes = "总部商品提报单作废接口", response = ResultVO.class, tags={ "连锁门店商品提报接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/zBdtqProductApply/zuofei",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> zuofei(HttpServletRequest request,@ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody ProductApplyZfVo queryVo);

    /**
     * 商品提报单详情接口(连锁门店详情，编辑，重新提报获取详情，连锁门店提取商品进入编辑页面都是用这个接口)
     * @param request
     * @return
     */
    @ApiOperation(value = "商品提报单详情接口(连锁门店详情，编辑，重新提报获取详情，连锁门店提取商品进入编辑页面都是用这个接口)", notes = "商品提报单详情接口(连锁门店详情，编辑，重新提报获取详情，连锁门店提取商品进入编辑页面都是用这个接口)", response = ProductApplyDetailDto.class, tags={ "连锁门店商品提报接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ProductApplyDetailDto.class) })
    @RequestMapping(value = "/productApply/detailinfo",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getDetailinfo(HttpServletRequest request, @ApiParam(value = "商品详情信息对象", required = true) @Valid @RequestBody ProductApplyQueryDetailVo productQueryVo);

    /**
     * 连锁门店商品提报单保存提交，编辑提交接口，重新提报提交接口，都用这个接口
     * @param request
     * @return
     */
    @ApiOperation(value = "连锁门店商品提报单保存提交，编辑提交接口，重新提报提交接口，都用这个接口", notes = "连锁门店商品提报单保存提交，编辑提交接口，重新提报提交接口，都用这个接口", response = ResultVO.class, tags={ "连锁门店商品提报接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/productApply/saveOrUpdate",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> saveOrUpdateProductApply(HttpServletRequest request, @ApiParam(value = "商品详情信息对象", required = true) @Valid @RequestBody ProductApplyDetailDto productApplyDetailDto);

    /**
     * 连锁总部暂存编辑接口
     * @param request
     * @return
     */
    @ApiOperation(value = "连锁总部暂存编辑接口", notes = "连锁总部暂存编辑接口", response = ResultVO.class, tags={ "连锁门店商品提报接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/productTemporary/saveOrUpdate",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> saveOrUpdateProductTemporary(HttpServletRequest request, @ApiParam(value = "商品详情信息对象", required = true) @Valid @RequestBody ProductDto productApplyDetailDto);

    /**
     * 获取商品暂存详情接口
     * @param request
     * @return
     */
    @ApiOperation(value = "商品暂存获取详情接口", notes = "商品暂存获取详情接口", response = ProductApplyDetailDto.class, tags={ "连锁门店商品提报接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ProductDto.class) })
    @RequestMapping(value = "/productTemporary/detailinfo",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getTemporaryDetailinfo(HttpServletRequest request, @ApiParam(value = "商品详情信息对象", required = true) @Valid @RequestBody ProductApplyQueryDetailVo productQueryVo);

    /**
     * 获取商品暂存删除接口
     * @param request
     * @return
     */
    @ApiOperation(value = "获取商品暂存删除接口", notes = "获取商品暂存删除接口", response = ProductApplyDetailDto.class, tags={ "连锁门店商品提报接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/productTemporary/delete",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> temporaryDelete(HttpServletRequest request, @ApiParam(value = "商品详情信息对象", required = true) @Valid @RequestBody ProductApplyQueryDetailVo productQueryVo);

    /**
     * 总部获取商品暂存列表接口
     * @return
     */
    @ApiOperation(value = "总部获取商品暂存列表接口", notes = "总部获取商品暂存列表接口", response = ProductApplyZbDtqVo.class, tags={ "连锁门店商品提报接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ProductDto.class) })
    @RequestMapping(value = "/productTemprory/query",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getProductTemproryList(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody ProductTemproryQueryVo queryVo);

    /**
     * 总部获取暂存数量接口
     * @return
     */
    @ApiOperation(value = "总部获取暂存数量接口", notes = "总部获取暂存数量接口", response = ResultVO.class, tags={ "连锁门店商品提报接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/productTemprory/count",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getProductTemproryCount(HttpServletRequest request);

    /**
     * 总部获取神农商品暂存列表接口
     * @return
     */
    @ApiOperation(value = "总部获取商品暂存列表接口", notes = "总部获取商品暂存列表接口", response = ProductApplyZbDtqVo.class, tags={ "连锁门店商品提报接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ProductDto.class) })
    @RequestMapping(value = "/snProductTemprory/query",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getSnProductTemproryList(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody ProductTemproryQueryVo queryVo);

    /**
     * 总部获取神农暂存数量接口
     * @return
     */
    @ApiOperation(value = "总部获取暂存数量接口", notes = "总部获取暂存数量接口", response = ResultVO.class, tags={ "连锁门店商品提报接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/snProductTemprory/count",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getSnProductTemproryCount(HttpServletRequest request);

    /**
     * 获取商品暂存详情接口
     * @param request
     * @return
     */
    @ApiOperation(value = "商品暂存获取详情接口", notes = "商品暂存获取详情接口", response = ProductApplyDetailDto.class, tags={ "连锁门店商品提报接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ProductDto.class) })
    @RequestMapping(value = "/snProductTemporary/detailinfo",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getSnProductTemporary(HttpServletRequest request, @ApiParam(value = "商品详情信息对象", required = true) @Valid @RequestBody ProductApplyQueryDetailVo productQueryVo);


}
