package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

@ApiModel(description = "医保目录列表查询参数类")
public class YbmlListQueryVo {

    @JsonProperty("registerName")
    private String registerName;

    @JsonProperty("manufacturer")
    private String manufacturer;

    @JsonProperty("specification")
    private String specification;

    @JsonProperty("approvalNumber")
    private String approvalNumber;

    @JsonProperty("pageNum")
    private Integer pageNum;

    @JsonProperty("pageSize")
    private Integer pageSize;

    @ApiModelProperty(value = "国家地区医保目录商品的注册名称")
    public String getRegisterName() {
        return registerName;
    }

    public void setRegisterName(String registerName) {
        this.registerName = registerName;
    }

    @ApiModelProperty(value = "国家地区医保目录商品的生产厂家")
    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    @ApiModelProperty(value = "国家地区医保目录商品的申报规格")
    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    @ApiModelProperty(value = "当前页码")
    public Integer getPage() {
        return pageNum;
    }

    public void setPage(Integer page) {
        this.pageNum = page;
    }

    @ApiModelProperty(value = "行数")
    public Integer getRows() {
        return pageSize;
    }

    public void setRows(Integer rows) {
        this.pageSize = rows;
    }

    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }
}
