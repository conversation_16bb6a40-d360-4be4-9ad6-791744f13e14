package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

/**
 * 会员积分兑换业务参数
 * <AUTHOR>
 */
@ApiModel(description = "会员积分兑换参数")
public class MemberConvertVo {

    /**
     * 消费积分
     */
    private BigDecimal countPoint;

    /**
     * 兑换商品
     */
    private List<MemberExchangeInfo> memberExchangeInfoList;


    /**
     * 会员Id
     */
    private Long memberId;


    /**
     * 机构标识
     */
    private String organsign;

    /**
     * 用户id
     */
    private Integer userId;

    @ApiModelProperty(value = "共计积分(必传)")
    public BigDecimal getCountPoint() {
        return countPoint;
    }

    public void setCountPoint(BigDecimal countPoint) {
        this.countPoint = countPoint;
    }

    @ApiModelProperty(value = "积分兑换商品(必传)")
    public List<MemberExchangeInfo> getMemberExchangeInfoList() {
        return memberExchangeInfoList;
    }

    public void setMemberExchangeInfoList(List<MemberExchangeInfo> memberExchangeInfoList) {
        this.memberExchangeInfoList = memberExchangeInfoList;
    }

    @ApiModelProperty(value = "积分兑换会员id 必传")
    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    @ApiModelProperty(value = "机构标识 (非必传)")
    public String getOrgansign() {
        return organsign;
    }

    public void setOrgansign(String organsign) {
        this.organsign = organsign;
    }

    @ApiModelProperty( value= "当前登录用户id (非必传)")
    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }
}
