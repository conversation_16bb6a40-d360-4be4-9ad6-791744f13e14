package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

@ApiModel(description = "手动匹配药店商品与医保目录参数类")
public class YbmlManualMatchParamVo {
    @JsonProperty("productPref")
    private String productPref;

    @JsonProperty("nationalId")
    private Long nationalId;

    @ApiModelProperty(value = "商品内码")
    public String getProductPref() {
        return productPref;
    }

    public void setProductPref(String productPref) {
        this.productPref = productPref;
    }

    @ApiModelProperty(value = "国家地区医保目录表id")
    public Long getNationalId() {
        return nationalId;
    }

    public void setNationalId(Long nationalId) {
        this.nationalId = nationalId;
    }
}
