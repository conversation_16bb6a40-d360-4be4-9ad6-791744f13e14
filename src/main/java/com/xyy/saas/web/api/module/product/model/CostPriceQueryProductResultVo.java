package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * @ClassName CostPriceQueryProductResultVo
 * @Description 成本价调价增加选择商品结果类
 * <AUTHOR>
 * @Date 2020/8/19 16:36
 * @Version 1.0
 **/
@ApiModel(description = "成本价调价增加选择商品结果类")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")
public class CostPriceQueryProductResultVo {

    @ApiModelProperty(value = "商品编号，用于系统之间交互,不可做页面展示")
    public String getProductPref() {
        return productPref;
    }

    public void setProductPref(String productPref) {
        this.productPref = productPref;
    }

    @JsonProperty("productPref")
    private String productPref;//商品编号，用于系统之间交互,不可做页面展示
    @JsonProperty("pharmacyPref")
    private String pharmacyPref;//商品编号，用于页面展示，不可做系统之间交互
    @JsonProperty("commonName")
    private String commonName;//通用名
    @JsonProperty("productName")
    private String productName;//商品名称
    @JsonProperty("manufacturer")
    private String manufacturer;//生产厂商
    @JsonProperty("attributeSpecification")
    private String attributeSpecification;//规格型号
    @JsonProperty("producingArea")
    private String producingArea;//产地
    @JsonProperty("approvalNumber")
    private String approvalNumber;//批准文号
    @JsonProperty("dosageFormName")
    private String dosageFormName;//剂型名称
    @JsonProperty("unitName")
    private String unitName;//单位名称
    @JsonProperty("systemTypeName")
    private String systemTypeName;//商品分类
    @JsonProperty("oldCostPrice")
    private String oldCostPrice;//原成本价
    @JsonProperty("controlSalesYnName")
    private String controlSalesYnName;//是否控销，是，否
    @JsonProperty("sid")
    private Integer sid;
    @ApiModelProperty(value = "最后一次采购的供应商")
    private String lastPurchaseSupplier;
    @ApiModelProperty(value = "商品六级分类")
    private String sixLevels;

    @ApiModelProperty(value = "出货价")
    public String getNewCostPrice() {
        return newCostPrice;
    }

    public void setNewCostPrice(String newCostPrice) {
        this.newCostPrice = newCostPrice;
    }

    @JsonProperty("newCostPrice")
    private String newCostPrice;//出货价
    @ApiModelProperty(value = "商品分类")
    public Integer getSystemType() {
        return systemType;
    }

    public void setSystemType(Integer systemType) {
        this.systemType = systemType;
    }

    @JsonProperty("systemType")
    private Integer systemType;//商品分类

    @ApiModelProperty(value = "是否控销")
    public Integer getControlSalesYn() {
        return controlSalesYn;
    }

    public void setControlSalesYn(Integer controlSalesYn) {
        this.controlSalesYn = controlSalesYn;
    }

    @JsonProperty("controlSalesYn")
    private Integer controlSalesYn;//是否控销

    @ApiModelProperty(value = "前端渲染页面唯一码")
    public Integer getSid() {
        return sid;
    }

    public void setSid(Integer sid) {
        this.sid = sid;
    }

    @ApiModelProperty(value = "是否控销，是，否")
    public String getControlSalesYnName() {
        return controlSalesYnName;
    }

    public void setControlSalesYnName(String controlSalesYnName) {
        this.controlSalesYnName = controlSalesYnName;
    }

    @ApiModelProperty(value = "价格上限")
    public String getPriceUpper() {
        return priceUpper;
    }

    public void setPriceUpper(String priceUpper) {
        this.priceUpper = priceUpper;
    }

    @ApiModelProperty(value = "价格下限")
    public String getPriceLower() {
        return priceLower;
    }

    public void setPriceLower(String priceLower) {
        this.priceLower = priceLower;
    }

    @JsonProperty("priceUpper")
    private String priceUpper;//价格上限
    @JsonProperty("priceLower")
    private String priceLower;//价格下限

    @ApiModelProperty(value = "采购含税价")
    public String getTaxCost() {
        return taxCost;
    }

    public void setTaxCost(String taxCost) {
        this.taxCost = taxCost;
    }

    @JsonProperty("taxCost")
    private String taxCost;//采购含税价

    @ApiModelProperty(value = "调价比例")
    public String getAdjustmentRatio() {
        return adjustmentRatio;
    }

    public void setAdjustmentRatio(String adjustmentRatio) {
        this.adjustmentRatio = adjustmentRatio;
    }

    @JsonProperty("adjustmentRatio")
    private String adjustmentRatio;//调价比例

    public Long getStandardLibraryId() {
        return standardLibraryId;
    }

    @ApiModelProperty(value = "标准库id")
    public void setStandardLibraryId(Long standardLibraryId) {
        this.standardLibraryId = standardLibraryId;
    }

    @JsonProperty("standardLibraryId")
    private Long standardLibraryId;//标准库id

    @ApiModelProperty(value = "商品编号，用于页面展示，不可做系统之间交互")
    public String getPharmacyPref() {
        return pharmacyPref;
    }

    public void setPharmacyPref(String pharmacyPref) {
        this.pharmacyPref = pharmacyPref;
    }

    @ApiModelProperty(value = "通用名")
    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    @ApiModelProperty(value = "商品名称")
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    @ApiModelProperty(value = "生产厂商")
    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    @ApiModelProperty(value = "规格型号")
    public String getAttributeSpecification() {
        return attributeSpecification;
    }

    public void setAttributeSpecification(String attributeSpecification) {
        this.attributeSpecification = attributeSpecification;
    }

    @ApiModelProperty(value = "产地")
    public String getProducingArea() {
        return producingArea;
    }

    public void setProducingArea(String producingArea) {
        this.producingArea = producingArea;
    }

    @ApiModelProperty(value = "批准文号")
    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    @ApiModelProperty(value = "剂型名称")
    public String getDosageFormName() {
        return dosageFormName;
    }

    public void setDosageFormName(String dosageFormName) {
        this.dosageFormName = dosageFormName;
    }

    @ApiModelProperty(value = "单位名称")
    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    @ApiModelProperty(value = "商品分类")
    public String getSystemTypeName() {
        return systemTypeName;
    }

    public void setSystemTypeName(String systemTypeName) {
        this.systemTypeName = systemTypeName;
    }

    @ApiModelProperty(value = "原成本价")
    public String getOldCostPrice() {
        return oldCostPrice;
    }

    public void setOldCostPrice(String oldCostPrice) {
        this.oldCostPrice = oldCostPrice;
    }

    public String getLastPurchaseSupplier() {
        return lastPurchaseSupplier;
    }

    public void setLastPurchaseSupplier(String lastPurchaseSupplier) {
        this.lastPurchaseSupplier = lastPurchaseSupplier;
    }

    public String getSixLevels() {
        return sixLevels;
    }

    public void setSixLevels(String sixLevels) {
        this.sixLevels = sixLevels;
    }
}
