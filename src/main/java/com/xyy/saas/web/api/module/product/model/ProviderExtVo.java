package com.xyy.saas.web.api.module.product.model;

/**  
* <p>Title: ProviderExtVo</p>  
* <p>Description: 供应商信息额外字段</p>  
* <AUTHOR>  
* @date 2018年4月26日  
*/ 
public class ProviderExtVo {

	/**
	 * 委托人姓名
	 */
	private String proxyName;
	
	/**
	 * 委托书编号
	 */
	private String proxyCode;
	
	/**
	 * 委托书效期
	 */
	private String proxyExpiredDate;
	
	/**
	 * 药品经营许可证
	 */
	private String drugBusinessPermit;
	
	/**
	 * 药品经营许可效期
	 */
	private String drugBusinessExpiredDate;
	
	
	/**
	 * 质量保证协议
	 */
	private String qualityAgreement;
	
	/**
	 * 质量保证协议效期
	 */
	private String qualityAgreementExpiredDate;
	
	
	/**
	 * GSP证书
	 */
	private String gSPCertificate;
	
	
	/**
	 * GSP证书效期
	 */
	private String gSPCertificateExpiredDate;
	
	
	/**
	 * 卫生许可证
	 */
	private String hygienicPermit;
	
	
	/**
	 * 卫生许可证效期
	 */
	private String hygienicLicenseExpiredDate;
	
	/**
	 * 食品流通许可证
	 */
	private String foodCirculationPermit;
	
	/**
	 * 食品流通许可效期
	 */
	private String foodCirculationPermitExpiredDate;
	
	
	/**
	 * 医疗器械经营许可证
	 */
	private String medicalDeviceBusinessPermit;

	/**
	 * 医疗器械经营许可证效期
	 */
	private String medicalDeviceBusinessPermitExpiredDate;
	
	/**
	 * 食品经营许可证
	 */
	private String foodBusinessPermit;
	
	/**
	 * 食品经营许可证效期
	 */
	private String foodBusinessPermitExpiredDate;
	
	/**
	 * 二类器械备案凭证
	 */
	private String secondDeviceRecord;
	
	/**
	 * 二类器械备案凭证效期
	 */
	private String secondDeviceRecordExpiredDate;
	
	
	/**
	 * GMP证书
	 */
	private String gMPPCertificate;
	
	/**
	 * GMP证书有效期
	 */
	private String gMPPCertificateExpiredDate;
	
	/**
	 * 药品生产许可证
	 */
	private String drugProducePermit;
	
	/**
	 * 药品生产许可证效期
	 */
	private String drugProducePermitExpiredDate;

	
	public ProviderExtVo() {
		super();
	}

	public ProviderExtVo(String proxyName, String proxyCode, String proxyExpiredDate, String drugBusinessPermit,
			String drugBusinessExpiredDate, String qualityAgreement, String qualityAgreementExpiredDate,
			String gSPCertificate, String gSPCertificateExpiredDate, String hygienicPermit,
			String hygienicLicenseExpiredDate, String foodCirculationPermit, String foodCirculationPermitExpiredDate,
			String medicalDeviceBusinessPermit, String medicalDeviceBusinessPermitExpiredDate,
			String foodBusinessPermit, String foodBusinessPermitExpiredDate, String secondDeviceRecord,
			String secondDeviceRecordExpiredDate, String gMPPCertificate, String gMPPCertificateExpiredDate,
			String drugProducePermit, String drugProducePermitExpiredDate) {
		super();
		this.proxyName = proxyName;
		this.proxyCode = proxyCode;
		this.proxyExpiredDate = proxyExpiredDate;
		this.drugBusinessPermit = drugBusinessPermit;
		this.drugBusinessExpiredDate = drugBusinessExpiredDate;
		this.qualityAgreement = qualityAgreement;
		this.qualityAgreementExpiredDate = qualityAgreementExpiredDate;
		this.gSPCertificate = gSPCertificate;
		this.gSPCertificateExpiredDate = gSPCertificateExpiredDate;
		this.hygienicPermit = hygienicPermit;
		this.hygienicLicenseExpiredDate = hygienicLicenseExpiredDate;
		this.foodCirculationPermit = foodCirculationPermit;
		this.foodCirculationPermitExpiredDate = foodCirculationPermitExpiredDate;
		this.medicalDeviceBusinessPermit = medicalDeviceBusinessPermit;
		this.medicalDeviceBusinessPermitExpiredDate = medicalDeviceBusinessPermitExpiredDate;
		this.foodBusinessPermit = foodBusinessPermit;
		this.foodBusinessPermitExpiredDate = foodBusinessPermitExpiredDate;
		this.secondDeviceRecord = secondDeviceRecord;
		this.secondDeviceRecordExpiredDate = secondDeviceRecordExpiredDate;
		this.gMPPCertificate = gMPPCertificate;
		this.gMPPCertificateExpiredDate = gMPPCertificateExpiredDate;
		this.drugProducePermit = drugProducePermit;
		this.drugProducePermitExpiredDate = drugProducePermitExpiredDate;
	}

	public String getProxyName() {
		return proxyName;
	}

	public void setProxyName(String proxyName) {
		this.proxyName = proxyName;
	}

	public String getProxyCode() {
		return proxyCode;
	}

	public void setProxyCode(String proxyCode) {
		this.proxyCode = proxyCode;
	}

	public String getProxyExpiredDate() {
		return proxyExpiredDate;
	}

	public void setProxyExpiredDate(String proxyExpiredDate) {
		this.proxyExpiredDate = proxyExpiredDate;
	}

	public String getDrugBusinessPermit() {
		return drugBusinessPermit;
	}

	public void setDrugBusinessPermit(String drugBusinessPermit) {
		this.drugBusinessPermit = drugBusinessPermit;
	}

	public String getDrugBusinessExpiredDate() {
		return drugBusinessExpiredDate;
	}

	public void setDrugBusinessExpiredDate(String drugBusinessExpiredDate) {
		this.drugBusinessExpiredDate = drugBusinessExpiredDate;
	}

	public String getQualityAgreement() {
		return qualityAgreement;
	}

	public void setQualityAgreement(String qualityAgreement) {
		this.qualityAgreement = qualityAgreement;
	}

	public String getQualityAgreementExpiredDate() {
		return qualityAgreementExpiredDate;
	}

	public void setQualityAgreementExpiredDate(String qualityAgreementExpiredDate) {
		this.qualityAgreementExpiredDate = qualityAgreementExpiredDate;
	}

	public String getgSPCertificate() {
		return gSPCertificate;
	}

	public void setgSPCertificate(String gSPCertificate) {
		this.gSPCertificate = gSPCertificate;
	}

	public String getgSPCertificateExpiredDate() {
		return gSPCertificateExpiredDate;
	}

	public void setgSPCertificateExpiredDate(String gSPCertificateExpiredDate) {
		this.gSPCertificateExpiredDate = gSPCertificateExpiredDate;
	}

	public String getHygienicPermit() {
		return hygienicPermit;
	}

	public void setHygienicPermit(String hygienicPermit) {
		this.hygienicPermit = hygienicPermit;
	}

	public String getHygienicLicenseExpiredDate() {
		return hygienicLicenseExpiredDate;
	}

	public void setHygienicLicenseExpiredDate(String hygienicLicenseExpiredDate) {
		this.hygienicLicenseExpiredDate = hygienicLicenseExpiredDate;
	}

	public String getFoodCirculationPermit() {
		return foodCirculationPermit;
	}

	public void setFoodCirculationPermit(String foodCirculationPermit) {
		this.foodCirculationPermit = foodCirculationPermit;
	}

	public String getFoodCirculationPermitExpiredDate() {
		return foodCirculationPermitExpiredDate;
	}

	public void setFoodCirculationPermitExpiredDate(String foodCirculationPermitExpiredDate) {
		this.foodCirculationPermitExpiredDate = foodCirculationPermitExpiredDate;
	}

	public String getMedicalDeviceBusinessPermit() {
		return medicalDeviceBusinessPermit;
	}

	public void setMedicalDeviceBusinessPermit(String medicalDeviceBusinessPermit) {
		this.medicalDeviceBusinessPermit = medicalDeviceBusinessPermit;
	}

	public String getMedicalDeviceBusinessPermitExpiredDate() {
		return medicalDeviceBusinessPermitExpiredDate;
	}

	public void setMedicalDeviceBusinessPermitExpiredDate(String medicalDeviceBusinessPermitExpiredDate) {
		this.medicalDeviceBusinessPermitExpiredDate = medicalDeviceBusinessPermitExpiredDate;
	}

	public String getFoodBusinessPermit() {
		return foodBusinessPermit;
	}

	public void setFoodBusinessPermit(String foodBusinessPermit) {
		this.foodBusinessPermit = foodBusinessPermit;
	}

	public String getFoodBusinessPermitExpiredDate() {
		return foodBusinessPermitExpiredDate;
	}

	public void setFoodBusinessPermitExpiredDate(String foodBusinessPermitExpiredDate) {
		this.foodBusinessPermitExpiredDate = foodBusinessPermitExpiredDate;
	}

	public String getSecondDeviceRecord() {
		return secondDeviceRecord;
	}

	public void setSecondDeviceRecord(String secondDeviceRecord) {
		this.secondDeviceRecord = secondDeviceRecord;
	}

	public String getSecondDeviceRecordExpiredDate() {
		return secondDeviceRecordExpiredDate;
	}

	public void setSecondDeviceRecordExpiredDate(String secondDeviceRecordExpiredDate) {
		this.secondDeviceRecordExpiredDate = secondDeviceRecordExpiredDate;
	}

	public String getgMPPCertificate() {
		return gMPPCertificate;
	}

	public void setgMPPCertificate(String gMPPCertificate) {
		this.gMPPCertificate = gMPPCertificate;
	}

	public String getgMPPCertificateExpiredDate() {
		return gMPPCertificateExpiredDate;
	}

	public void setgMPPCertificateExpiredDate(String gMPPCertificateExpiredDate) {
		this.gMPPCertificateExpiredDate = gMPPCertificateExpiredDate;
	}

	public String getDrugProducePermit() {
		return drugProducePermit;
	}

	public void setDrugProducePermit(String drugProducePermit) {
		this.drugProducePermit = drugProducePermit;
	}

	public String getDrugProducePermitExpiredDate() {
		return drugProducePermitExpiredDate;
	}

	public void setDrugProducePermitExpiredDate(String drugProducePermitExpiredDate) {
		this.drugProducePermitExpiredDate = drugProducePermitExpiredDate;
	}
	
}
