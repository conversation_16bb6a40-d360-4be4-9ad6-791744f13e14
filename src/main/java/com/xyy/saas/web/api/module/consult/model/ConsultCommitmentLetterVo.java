package com.xyy.saas.web.api.module.consult.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 远程问诊上传承诺函Vo
 * <AUTHOR>
 */
@ApiModel(description = "远程问诊上传承诺函")
public class ConsultCommitmentLetterVo {

    /**
     * 承诺函文件地址
     */
    @ApiModelProperty(value = "承诺函文件地址", required = true, example = "http://www.sss.com/sdgfsdfsd.pdf")
    private String file;


    public String getFile() {
        return file;
    }

    public void setFile(String file) {
        this.file = file;
    }

    @Override
    public String toString() {
        return "ConsultCommitmentLetterVo{" +
                "file='" + file + '\'' +
                '}';
    }
}