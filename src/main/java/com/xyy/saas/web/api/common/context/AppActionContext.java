package com.xyy.saas.web.api.common.context;

public class AppActionContext {

	private String appOs;
	private String appVersion;
	private String appImei;
	private String appChannelId;
	private String appUid;
	private String appLat;
	private String appLon;
	private String appIp;
	private String appUa;
	private String appMac;
	private String appUniqueId;

	public AppActionContext() {
	}

	public String getAppOs() {
		return appOs;
	}

	public void setAppOs(String appOs) {
		this.appOs = appOs;
	}

	public String getAppVersion() {
		return appVersion;
	}

	public void setAppVersion(String appVersion) {
		this.appVersion = appVersion;
	}

	public String getAppImei() {
		return appImei;
	}

	public void setAppImei(String appImei) {
		this.appImei = appImei;
	}

	public String getAppChannelId() {
		return appChannelId;
	}

	public void setAppChannelId(String appChannelId) {
		this.appChannelId = appChannelId;
	}

	public String getAppUid() {
		return appUid;
	}

	public void setAppUid(String appUid) {
		this.appUid = appUid;
	}

	public String getAppLat() {
		return appLat;
	}

	public void setAppLat(String appLat) {
		this.appLat = appLat;
	}

	public String getAppLon() {
		return appLon;
	}

	public void setAppLon(String appLon) {
		this.appLon = appLon;
	}

	public String getAppIp() {
		return appIp;
	}

	public void setAppIp(String appIp) {
		this.appIp = appIp;
	}

	public String getAppUa() {
		return appUa;
	}

	public void setAppUa(String appUa) {
		this.appUa = appUa;
	}

	public String getAppMac() {
		return appMac;
	}

	public void setAppMac(String appMac) {
		this.appMac = appMac;
	}

	public String getAppUniqueId() {
		return appUniqueId;
	}

	public void setAppUniqueId(String appUniqueId) {
		this.appUniqueId = appUniqueId;
	}

}
