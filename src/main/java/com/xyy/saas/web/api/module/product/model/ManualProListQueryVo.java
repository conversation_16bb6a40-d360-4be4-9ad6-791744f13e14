package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description:
 * @Author: Administrator
 * @Date 2022/08/19 18:12
 */
@Data
public class ManualProListQueryVo implements Serializable {
    @JsonProperty("rows")
    private Integer rows;
    @JsonProperty("page")
    private Integer page;
    @JsonProperty("productName")
    private String productName;
    @JsonProperty("manufacturer")
    private String manufacturer;
    @JsonProperty("approvalNumber")
    private String approvalNumber;
    @JsonProperty("standardLibraryIdYn")
    private String standardLibraryIdYn;
}
