package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *会员慢病配置/慢病种类表
 *<AUTHOR>
 */
@Data
public class MemberChronicConfigVo implements Serializable {
    //序号、编号√、种类名称√、创建类型√、药品列表、操作；
    @ApiModelProperty(value = "主键")
    private Long id; //id 自增
    @ApiModelProperty(value = "慢病编号")
    private String pref; //慢病编号√
    //@ApiModelProperty(value = "所属机构")
    //private String organSign; //所属机构
    @ApiModelProperty(value = "慢病名称(1-40字符)")
    private String chronicName; //慢病名称(1-40字符)√
    @ApiModelProperty(value = "慢病类别：0 系统默认，1 自定义")
    private Byte chronicType; //慢病类别：0 系统默认，1 自定义√
    //@ApiModelProperty(value = "创建人")
    //private Integer createUser; //创建人
    //@ApiModelProperty(value = "创建时间")
    //private Date createTime; //创建时间
    //@ApiModelProperty(value = "修改人")
    //private Integer updateUser; //修改人
    //@ApiModelProperty(value = "修改时间")
    //private Date updateTime; //修改时间
}