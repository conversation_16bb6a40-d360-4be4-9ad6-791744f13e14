package com.xyy.saas.web.api.module.product.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @<PERSON> <PERSON><PERSON>
 * @Date 2021/8/3 13:29
 * @Version 1.0
 * 红尘轮回众生顾,因果循环有定数
 * 放下屠刀虽成佛,愿坠三途灭千魔
 */
@Data
public class PriceImportVo implements Serializable {

    private static final long serialVersionUID = -3672813864449835046L;

    @JsonProperty("taskId")
    private Long taskId;
}
