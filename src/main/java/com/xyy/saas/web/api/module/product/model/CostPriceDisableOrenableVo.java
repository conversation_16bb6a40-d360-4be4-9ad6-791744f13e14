package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName CostPriceDisableOrenableVo
 * @Description 成本价调价方案禁用启用信息封装类
 * <AUTHOR>
 * @Date 2020/8/19 16:18
 * @Version 1.0
 **/
@ApiModel(description = "成本价调价方案禁用启用信息封装类")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")
public class CostPriceDisableOrenableVo {

    @JsonProperty("pref")
    private String pref;//方案编号

    @JsonProperty("disableOrenable")
    private Byte disableOrenable;//启用禁用：启用传1，禁用传0

    @ApiModelProperty(value = "方案编号")
    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    @ApiModelProperty(value = "启用禁用：启用传1，禁用传0")
    public Byte getDisableOrenable() {
        return disableOrenable;
    }

    public void setDisableOrenable(Byte disableOrenable) {
        this.disableOrenable = disableOrenable;
    }
}
