package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName CostPriceListQueryVo
 * @Description 调价方案列表查询前端信息封装类
 * <AUTHOR>
 * @Date 2020/8/19 10:08
 * @Version 1.0
 **/
@ApiModel(description = "调价方案列表查询前端信息封装类")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")
public class CostPriceListQueryVo {

    @JsonProperty("queryMix")
    private String queryMix; //方案信息(方案编号，方案名称)

    @JsonProperty("showStatus")
    private Byte showStatus;//状态-1：全部，1：暂存，2：禁用，3：启用

    @JsonProperty("expirationStatus")
    private Byte expirationStatus;//过期状态，-1：全部，0：否，1：是

    @JsonProperty("createUserName")
    private String createUserName; //创建人

    @JsonProperty("timeBegin")
    private String timeBegin; //创建时间：查询开始时间，格式：yyyy-MM-dd

    @JsonProperty("timeEnd")
    private String timeEnd; //创建时间：查询结束时间，格式：yyyy-MM-dd

    @JsonProperty("sixLevels")
    private String sixLevels;//六级分类

    @JsonProperty("manufacturer")
    private String manufacturer;//生产厂家

    @JsonProperty("mixedQuery")
    private String mixedQuery;//模糊查询

    @JsonProperty("systemType")
    private String systemType;//商品分类

    @JsonProperty("standardLibraryId")
    private String standardLibraryId;//标准库id

    @ApiModelProperty(value = "当前页码")
    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    @ApiModelProperty(value = "每页显示多少数据")
    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    @JsonProperty("page")
    private Integer page;
    @JsonProperty("rows")
    private Integer rows;

    @ApiModelProperty(value = "方案信息(方案编号，方案名称)")
    public String getQueryMix() {
        return queryMix;
    }

    public void setQueryMix(String queryMix) {
        this.queryMix = queryMix;
    }

    @ApiModelProperty(value = "状态-1：全部，1：暂存，2：禁用，3：启用")
    public Byte getShowStatus() {
        return showStatus;
    }

    public void setShowStatus(Byte showStatus) {
        this.showStatus = showStatus;
    }

    @ApiModelProperty(value = "过期状态，-1：全部，0：否，1：是")
    public Byte getExpirationStatus() {
        return expirationStatus;
    }

    public void setExpirationStatus(Byte expirationStatus) {
        this.expirationStatus = expirationStatus;
    }

    @ApiModelProperty(value = "创建人名称")
    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    @ApiModelProperty(value = "创建时间：查询开始时间，格式：yyyy-MM-dd")
    public String getTimeBegin() {
        return timeBegin;
    }

    public void setTimeBegin(String timeBegin) {
        this.timeBegin = timeBegin;
    }

    @ApiModelProperty(value = "创建时间：查询结束时间，格式：yyyy-MM-dd")
    public String getTimeEnd() {
        return timeEnd;
    }

    public void setTimeEnd(String timeEnd) {
        this.timeEnd = timeEnd;
    }

    public String getSixLevels() {
        return sixLevels;
    }

    public void setSixLevels(String sixLevels) {
        this.sixLevels = sixLevels;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getMixedQuery() {
        return mixedQuery;
    }

    public void setMixedQuery(String mixedQuery) {
        this.mixedQuery = mixedQuery;
    }

    public String getSystemType() {
        return systemType;
    }

    public void setSystemType(String systemType) {
        this.systemType = systemType;
    }

    public String getStandardLibraryId() {
        return standardLibraryId;
    }

    public void setStandardLibraryId(String standardLibraryId) {
        this.standardLibraryId = standardLibraryId;
    }
}
