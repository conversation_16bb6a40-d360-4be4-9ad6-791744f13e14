package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 会员储值卡支付Vo
 * <AUTHOR>
 */
@ApiModel(description = "会员储值卡支付")
public class MemberPrepayCardPayVo extends MemberPrepayCardTokenVo implements Serializable {


    private static final long serialVersionUID = -8975896995611882278L;
    /**
     * 会员guid
     */
    @ApiModelProperty(value = "会员guid", required = true, example = "584a4460-2144-47c6-b7f7-d85b33d3322c")
    private String memberGuid;

    /**
     * 支付总额
     */
    @ApiModelProperty(value = "支付总额", required = true, example = "20.00")
    private BigDecimal totalAmount;

    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识", hidden = true)
    private String organsign;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", hidden = true, example = "张三")
    private String createUser;

    /**
     * 订单id
     */
    @ApiModelProperty(value = "订单id", required = true, example = "25532")
    private Integer orderId;

    /**
     * 小票号
     */
    @ApiModelProperty(value = "小票号", required = true, example = "ZHL000002711551180141")
    private String ticketNo;

    /**
     * 消费密码（MD5加密）
     */
    @ApiModelProperty(value = "消费密码（MD5加密）", required = true, example = "e10adc3949ba59abbe56e057f20f883e")
    private String password;

    public String getMemberGuid() {
        return memberGuid;
    }

    public void setMemberGuid(String memberGuid) {
        this.memberGuid = memberGuid;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getOrgansign() {
        return organsign;
    }

    public void setOrgansign(String organsign) {
        this.organsign = organsign;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getTicketNo() {
        return ticketNo;
    }

    public void setTicketNo(String ticketNo) {
        this.ticketNo = ticketNo;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    @Override
    public String toString() {
        return "MemberPrepayCardPayVo{" +
                "memberGuid='" + memberGuid + '\'' +
                ", totalAmount=" + totalAmount +
                ", organsign='" + organsign + '\'' +
                ", createUser='" + createUser + '\'' +
                ", orderId=" + orderId +
                ", ticketNo='" + ticketNo + '\'' +
                ", password='" + password + '\'' +
                '}';
    }
}