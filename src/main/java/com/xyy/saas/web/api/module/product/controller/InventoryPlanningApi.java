package com.xyy.saas.web.api.module.product.controller;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.inventory.core.dto.InventoryPlanDetailVo;
import com.xyy.saas.inventory.core.dto.InventoryPlanVo;
import com.xyy.saas.inventory.core.dto.InventoryPlanningSumVo;
import com.xyy.saas.web.api.module.product.model.InventoryPlanningInProgresVo;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-01-28T15:28:18.933+08:00")
@RequestMapping("/product/inventoryPlanning")
@Api(value = "InventoryPlanning", description = "the InventoryPlanning API")
public interface InventoryPlanningApi {

    /**
     * 分页查询盘点计划单列表
     * @param vo 查询条件集锦
     * @return
     */
    @ApiOperation(value = "分页查询盘点计划单列表", notes = "分页查询盘点计划单列表", response = PageInfo.class, tags = {"inventoryPlanning", })
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "操作成功", response = PageInfo.class)})
    @RequestMapping(value = "/findInventoryPlanningByPage",method = RequestMethod.POST)
    @ResponseBody
    ResponseEntity<ResultVO> findInventoryPlanningByPage(@ApiParam(value = "盘点计划单信息", required = true) @Valid @RequestBody InventoryPlanVo vo);


    /**
     * 查询盘点计划单汇总数据-2019年1月29日18:05:52
     * @param vo 查询条件集锦
     * @return
     */
    @ApiOperation(value = "查询盘点计划单汇总数据", notes = "查询盘点计划单汇总数据", response = InventoryPlanningSumVo.class, tags = {"inventoryPlanning", })
    @ApiResponses(value = {
            @ApiResponse(code = 1, message = "操作成功", response = InventoryPlanningSumVo.class)})
    @RequestMapping(value = "/findInventoryPlanningSum",method = RequestMethod.POST)
    @ResponseBody
    ResponseEntity<ResultVO> findInventoryPlanningSum(@ApiParam(value = "盘点计划单信息", required = true) @Valid @RequestBody InventoryPlanVo vo);

    /**
     * 查询盘点计划单详情-2019年1月29日01:07:34
     * @param organSign
     * @param planningPref
     * @return
     */
    @ApiOperation(value = "查询盘点计划单详情", notes = "查询盘点计划单详情", response = PageInfo.class, tags = {"inventoryPlanning", })
    @ApiResponses(value = {
            @ApiResponse(code = 0, message = "操作成功", response = PageInfo.class)})
    @RequestMapping(value = "/findInventoryPlanningDetail",method = RequestMethod.POST)
    @ResponseBody
    ResponseEntity<ResultVO> findInventoryPlanningDetail(@ApiParam(value = "盘点计划单信息", required = true) @Valid @RequestBody InventoryPlanVo vo);

    /**
     * 查询盘点计划单商品-2019年1月31日14:45:11
     * @param detailVo
     * @return
     */
    @ApiOperation(value = "查询盘点计划单商品", notes = "查询盘点计划单商品", response = PageInfo.class, tags = {"inventoryPlanning", })
    @ApiResponses(value = {
            @ApiResponse(code = 0, message = "操作成功", response = PageInfo.class)})
    @RequestMapping(value = "/findInventoryPlanProduct",method = RequestMethod.POST)
    @ResponseBody
    ResponseEntity<ResultVO> findInventoryPlanProduct(@ApiParam(value = "盘点计划单详情信息", required = true) @RequestBody InventoryPlanDetailVo detailVo);

    /**
     * 标记盘点计划单为进行中状态-2019年1月29日15:08:41
     * @param id
     * @param organSign
     * @return
     */
    @ApiOperation(value = "标记盘点计划单为进行中状态", notes = "标记盘点计划单为进行中状态", response = Boolean.class, tags = {"inventoryPlanning", })
    @ApiResponses(value = {
            @ApiResponse(code = 0, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/markPlanningInProgres",method = RequestMethod.POST)
    @ResponseBody
    ResponseEntity<ResultVO> markPlanningInProgres(@ApiParam(value = "盘点计划单id", required = true) @RequestBody InventoryPlanningInProgresVo progresVo);

    /**
     * 标记盘点计划单为进行中状态-2019年1月29日15:08:41
     * @param id
     * @param organSign
     * @return
     */
    @ApiOperation(value = "查询商品批号-正常盘点用", notes = "查询商品批号", response = Boolean.class, tags = {"inventoryPlanning", })
    @ApiResponses(value = {
            @ApiResponse(code = 0, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/findInventoryLotNumber",method = RequestMethod.POST)
    @ResponseBody
    ResponseEntity<ResultVO> findInventoryLotNumber(@ApiParam(value = "查询条件", required = true) @RequestBody InventoryPlanDetailVo detailVo);
}
