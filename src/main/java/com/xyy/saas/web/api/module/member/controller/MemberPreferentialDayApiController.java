package com.xyy.saas.web.api.module.member.controller;


import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.member.core.api.MemberPreferentialDayApi;
import com.xyy.saas.member.core.common.ResultCodeMessage;
import com.xyy.saas.member.core.dto.MemberPreferentialDayDto;
import com.xyy.saas.member.core.dto.MemberPreferentialProductDto;
import com.xyy.saas.member.core.enums.LogicallyDeletedEnum;
import com.xyy.saas.member.core.enums.MemberPreferentialDayEnum;
import com.xyy.saas.web.api.module.member.model.MemberPreferentialDaySaveVo;
import com.xyy.saas.web.api.module.member.model.MemberPreferentialProductSaveVo;
import com.xyy.saas.web.api.module.promotion.model.GetApolloConfigVo;
import io.swagger.annotations.*;
import org.apache.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/member/memberPreferentialDay")
@Api(value = "memberPreferentialDay", description = "会员日API")
public class MemberPreferentialDayApiController {

    private static final Logger logger = Logger.getLogger(MemberPreferentialDayApiController.class);

    @Reference(version = "0.0.1")
    private MemberPreferentialDayApi memberPreferentialDayApi;

    @ApiOperation(value = "获取会员日设置", notes = "获取会员日设置  \n" +
            "错误码:  \n" +
            "code: -99 参数不正确", response = MemberPreferentialDayDto.class, tags = {"memberPreferentialDay",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberPreferentialDayDto.class)})
    @RequestMapping(value = "/get", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> get(@RequestHeader(name = "organSign", required = true) String organSign,
                                 @RequestBody MemberPreferentialDayDto memberPreferentialDayDto) {
        if ((StringUtils.isEmpty(memberPreferentialDayDto.getId()) || memberPreferentialDayDto.getId() < 0)
        		&& (StringUtils.isEmpty(memberPreferentialDayDto.getMemberLevelId()) || memberPreferentialDayDto.getMemberLevelId() < 0)) {
            return new ResponseEntity(new ResultVO(-1, "参数id和memberLevelId不能同时为空", false), HttpStatus.OK);
        }
        memberPreferentialDayDto.setOrgansign(organSign);

        MemberPreferentialDayDto dto = memberPreferentialDayApi.get(memberPreferentialDayDto);
        return new ResponseEntity(ResultVO.createSuccess(dto), HttpStatus.OK);
    }

    @ApiOperation(value = "会员日模板列表", notes = "会员日模板列表  \n" +
            "错误码:  \n" +
            "code: -99 参数不正确", response = Boolean.class, tags = {"memberPreferentialDay",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/getMemberPreferentialDayTempletePageList", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> getMemberPreferentialDayTempletePageList(@RequestHeader(name = "organSign", required = true) String organSign,
                                                                      @RequestHeader("employeeId") Integer employeeId,
                                                                      @RequestBody MemberPreferentialDayDto memberPreferentialDayDto) {
    	memberPreferentialDayDto.setOrgansign(organSign);
    	memberPreferentialDayDto.setYn((byte) LogicallyDeletedEnum.EFFECTIVE.getKey());
    	PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(memberPreferentialDayDto.getPage() == null ? 1:memberPreferentialDayDto.getPage());
        pageInfo.setPageSize(memberPreferentialDayDto.getRows() == null ? 50:memberPreferentialDayDto.getRows());
    	PageInfo<MemberPreferentialDayDto> pageList = memberPreferentialDayApi.getMemberPreferentialDayTempletePageList(pageInfo, memberPreferentialDayDto);
        return new ResponseEntity(ResultVO.createSuccess(pageList), HttpStatus.OK);
    }

    @ApiOperation(value = "校验会员日模板名称", notes = "校验会员日模板名称  \n" +
            "错误码:  \n" +
            "code: -99 参数不正确", response = MemberPreferentialDayDto.class, tags = {"memberPreferentialDay",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberPreferentialDayDto.class)})
    @RequestMapping(value = "/checkTempleteName", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> checkTempleteName(@RequestHeader(name = "organSign", required = true) String organSign,
                                               @RequestBody MemberPreferentialDayDto memberPreferentialDayDto) {
        if (StringUtils.isEmpty(memberPreferentialDayDto.getTempleteName())) {
            return new ResponseEntity(new ResultVO(ResultCodeMessage.PARAM_ERROR, "参数templeteName不能为空", false), HttpStatus.OK);
        }
        memberPreferentialDayDto.setOrgansign(organSign);
        memberPreferentialDayDto.setIsTemplete((byte) MemberPreferentialDayEnum.YES.getKey());
        memberPreferentialDayDto.setYn((byte) LogicallyDeletedEnum.EFFECTIVE.getKey());

        int flag = memberPreferentialDayApi.checkTempleteName(memberPreferentialDayDto);
        String text = flag == 1 ? "会员日模板名称与其他模板重复" : "模板名称未存在";
        return new ResponseEntity(new ResultVO(flag, text, false), HttpStatus.OK);
    }

    @ApiOperation(value = "禁用会员日", notes = "禁用会员日  \n" +
            "错误码:  \n" +
            "code: -99 参数不正确", response = MemberPreferentialDayDto.class, tags = {"memberPreferentialDay",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberPreferentialDayDto.class)})
    @RequestMapping(value = "/forbiddenTemplete", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> forbiddenTemplete(@RequestHeader(name = "organSign", required = true) String organSign,
                                               @RequestBody MemberPreferentialDayDto memberPreferentialDayDto) {
        if (StringUtils.isEmpty(memberPreferentialDayDto.getId())) {
            return new ResponseEntity(new ResultVO(ResultCodeMessage.PARAM_ERROR, "参数id不能为空", false), HttpStatus.OK);
        }
        if (StringUtils.isEmpty(memberPreferentialDayDto.getForbiddenYn())) {
        	return new ResponseEntity(new ResultVO(ResultCodeMessage.PARAM_ERROR, "参数forbiddenYn不能为空", false), HttpStatus.OK);
        }
        memberPreferentialDayDto.setOrgansign(organSign);
        memberPreferentialDayDto.setIsTemplete((byte) MemberPreferentialDayEnum.YES.getKey());
        memberPreferentialDayDto.setYn((byte) LogicallyDeletedEnum.EFFECTIVE.getKey());
        StringBuffer msg = new StringBuffer("操作");
        int flag = memberPreferentialDayApi.forbiddenTemplete(memberPreferentialDayDto);
        if(flag == ResultCodeMessage.SUCCESS) { msg.append("成功");}else {msg.append("失败");}
        return new ResponseEntity(new ResultVO(flag, msg.toString(), null), HttpStatus.OK);
    }

    @ApiOperation(value = "新增会员日模板", notes = "新增会员日模板  \n" +
            "错误码:  \n" +
            "code: -99 参数不正确", response = MemberPreferentialDayDto.class, tags = {"memberPreferentialDay",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberPreferentialDayDto.class)})
    @RequestMapping(value = "/saveTemplete", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> saveTemplete(@RequestHeader(name = "organSign", required = true) String organSign,
                                          @RequestBody MemberPreferentialDayDto memberPreferentialDayDto) {
    	if (StringUtils.isEmpty(memberPreferentialDayDto.getTempleteName())) {
    		return new ResponseEntity(new ResultVO(-1, "会员日模板名称templeteName不能为空", false), HttpStatus.OK);
    	}
    	if (StringUtils.isEmpty(memberPreferentialDayDto.getMonthPreferentialDay()) && StringUtils.isEmpty(memberPreferentialDayDto.getWeekPreferentialDay())
    		&& StringUtils.isEmpty(memberPreferentialDayDto.getBirthdayPreferentialDay())
    			) {
    		return new ResponseEntity(new ResultVO(-1, "月会员日、周会员日、生日当天，不能同时为空", false), HttpStatus.OK);
    	}
    	if (StringUtils.isEmpty(memberPreferentialDayDto.getProductChoiceModel())) {
    		return new ResponseEntity(new ResultVO(-1, "参数productChoiceModel不能为空", false), HttpStatus.OK);
    	}
        if (!memberPreferentialDayDto.getProductChoiceModel().equals(11) && StringUtils.isEmpty(memberPreferentialDayDto.getMemberPreferentialProductDtoList())) {
            return new ResponseEntity(new ResultVO(-1, "参数memberPreferentialProductDtoList不能为空", false), HttpStatus.OK);
        }
        memberPreferentialDayDto.setOrgansign(organSign);
        memberPreferentialDayDto.setIsTemplete((byte) MemberPreferentialDayEnum.YES.getKey());
        memberPreferentialDayDto.setForbiddenYn((byte) MemberPreferentialDayEnum.NO.getKey());
        memberPreferentialDayDto.setYn((byte) LogicallyDeletedEnum.DELETED.getKey());
        int flag = 0;
        StringBuffer msg = new StringBuffer();
        if(memberPreferentialDayDto.getId() == null) {
        	flag = memberPreferentialDayApi.create(memberPreferentialDayDto);
        	msg.append("会员日模板新增");
        }else {
        	flag = memberPreferentialDayApi.update(memberPreferentialDayDto);
        	msg.append("会员日模板编辑");
        }
        if(flag == ResultCodeMessage.SUCCESS) { msg.append("成功");}else {msg.append("失败");}
        return new ResponseEntity(new ResultVO(flag, msg.toString(), null), HttpStatus.OK);
    }

    @ApiOperation(value = "删除会员日", notes = "删除会员日  \n" +
            "错误码:  \n" +
            "code: -99 参数不正确", response = MemberPreferentialDayDto.class, tags = {"memberPreferentialDay",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberPreferentialDayDto.class)})
    @RequestMapping(value = "/deleteTemplete", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> deleteTemplete(@RequestHeader(name = "organSign", required = true) String organSign,
                                            @RequestBody MemberPreferentialDayDto memberPreferentialDayDto) {
        if (StringUtils.isEmpty(memberPreferentialDayDto.getId())) {
            return new ResponseEntity(new ResultVO(-1, "参数id不能为空", false), HttpStatus.OK);
        }
        memberPreferentialDayDto.setOrgansign(organSign);
        memberPreferentialDayDto.setIsTemplete((byte) MemberPreferentialDayEnum.YES.getKey());
        memberPreferentialDayDto.setYn((byte) LogicallyDeletedEnum.DELETED.getKey());
        StringBuffer msg = new StringBuffer("删除会员日");
        int flag = memberPreferentialDayApi.delete(memberPreferentialDayDto);
        if(flag == ResultCodeMessage.SUCCESS) { msg.append("成功");}else {msg.append("失败");}
        return new ResponseEntity(new ResultVO(flag, msg.toString(), null), HttpStatus.OK);
    }

    @ApiOperation(value = "门店端:保存会员日", notes = "保存会员日", response = MemberPreferentialDaySaveVo.class, tags = {"memberPreferentialDaySaveVo",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberPreferentialDaySaveVo.class)})
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> saveMemberPreferentialDay(@RequestBody MemberPreferentialDaySaveVo memberPreferentialDaySaveVo, @RequestHeader("commonRequestModel") String commonRequestModel) {
        CommonRequestModel model = JSONObject.parseObject(commonRequestModel, CommonRequestModel.class);
        String organSign = model.getOrganSign();
        List<MemberPreferentialProductDto> proDtoList;
        int flag = 0;
        if (memberPreferentialDaySaveVo != null) {
            MemberPreferentialDayDto dayDto = new MemberPreferentialDayDto();
            BeanUtils.copyProperties(memberPreferentialDaySaveVo, dayDto);
            dayDto.setOrgansign(organSign);
            Byte isDelete = memberPreferentialDaySaveVo.getIsDelete();
            if (isDelete == null) {
                isDelete = (byte) 1;
                dayDto.setIsDelete(isDelete);
            }
            if (isDelete.equals((byte) 1)) {
                if (memberPreferentialDaySaveVo.getMeanwhileTemplete() != null && memberPreferentialDaySaveVo.getMeanwhileTemplete().equals(MemberPreferentialDayEnum.YES.getKey())) {
                    if (StringUtil.isEmpty(memberPreferentialDaySaveVo.getTempleteName())) {
                        logger.info("模板名称空！");
                        return new ResponseEntity<ResultVO>(new ResultVO(-1, "模板名称不能为空", flag), HttpStatus.OK);
                    }
                }
                if (StringUtils.isEmpty(memberPreferentialDaySaveVo.getProductChoiceModel())) {
                    return new ResponseEntity(new ResultVO(-1, "适用商品不能为空", false), HttpStatus.OK);
                }
                List<MemberPreferentialProductSaveVo> productList = memberPreferentialDaySaveVo.getMemberPreferentialProductVoList();
                if (productList == null || productList.isEmpty()) {
                    if (!memberPreferentialDaySaveVo.getProductChoiceModel().equals(11)) {
                        logger.info("会员日对应商品列表为空！");
                        return new ResponseEntity<ResultVO>(new ResultVO(-1, "会员日对应商品列表不能为空", flag), HttpStatus.OK);
                    }
                }
                proDtoList = Lists.newArrayList();
                List<MemberPreferentialProductSaveVo> proVoList = memberPreferentialDaySaveVo.getMemberPreferentialProductVoList();
                if (proVoList != null && proVoList.size() > 0) {
                    for (MemberPreferentialProductSaveVo proVo : proVoList) {
                        MemberPreferentialProductDto proDto = new MemberPreferentialProductDto();
                        BeanUtils.copyProperties(proVo, proDto);
                        proDto.setOrgansign(organSign);
                        proDtoList.add(proDto);
                    }
                }
                dayDto.setMemberPreferentialProductDtoList(proDtoList);
            }

            if (dayDto.getId() != null && dayDto.getId() > 0) {
                //是否调用逻辑删除接口，1 有效  0  删除
                if (dayDto.getIsDelete().equals((byte) 1)) {
                    flag = memberPreferentialDayApi.update(dayDto);
                } else {
                    flag = memberPreferentialDayApi.delete(dayDto);
                }
            } else {
                flag = memberPreferentialDayApi.create(dayDto);
            }
        }
        if (flag == ResultCodeMessage.SUCCESS) {
            return new ResponseEntity(new ResultVO(flag, "操作成功", true), HttpStatus.OK);
        } else {
            return new ResponseEntity(new ResultVO(flag,"操作失败", false), HttpStatus.OK);
        }
    }


    @ApiOperation(value = "刷新会员日数据", notes = "刷新会员日数据  \n" +
            "错误码:  \n" +
            "code: -99 参数不正确", response = MemberPreferentialDayDto.class, tags = {"memberPreferentialDay",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberPreferentialDayDto.class)})
    @RequestMapping(value = "/flush", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> flushMemberPreferentialDayData(@RequestBody MemberPreferentialDayDto memberPreferentialDayDto) {
        memberPreferentialDayApi.flushMemberPreferentialDay(memberPreferentialDayDto.getOrganSignList());
        return new ResponseEntity(new ResultVO("成功"), HttpStatus.OK);
    }

    @ApiOperation(value = "获取apollo配置", notes = "获取apollo配置  \n" +
            "错误码:  \n" +
            "code: -99 参数不正确", response = MemberPreferentialDayDto.class, tags = {"memberPreferentialDay",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberPreferentialDayDto.class)})
    @RequestMapping(value = "/getConfig", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> getApolloConfig(@RequestBody GetApolloConfigVo getApolloConfigVo, @RequestHeader("commonRequestModel") String commonRequestModel) {
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        Boolean isShow = memberPreferentialDayApi.getApolloConfig(model.getOrganSign(), getApolloConfigVo.getConfigKey());
        return new ResponseEntity(new ResultVO(isShow), HttpStatus.OK);
    }
}
