package com.xyy.saas.web.api.module.utils;

import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Strings;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.web.api.common.constants.CacheKeyPreConstant;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * token验证 还是有问题 暂不使用
 * <AUTHOR>
 * @date 2019-08-07
 * @mondify
 * @copyright
 */
@Component
public class TokenUtil {
    private static final String TOKEN_SALT = "ybm100.com";

    private static Logger logger = Logger.getLogger(TokenUtil.class);
    @Autowired
    private JedisUtils jedisUtil;
    /**
     * 生成token
     */
    public String generalToken(String employeeId, String deviceID) throws Exception {
        if (!Strings.isNullOrEmpty(employeeId)&&!Strings.isNullOrEmpty( deviceID)) {
            long timeStamp = System.currentTimeMillis() / 1000;
            String str = employeeId + TOKEN_SALT + deviceID + timeStamp;
            String token = Md5Util.getMd5Hex(str);
            System.out.print(token);
            saveToken(employeeId, deviceID, token);
            return token;
        } else {
            throw new Exception("生成token失败");
        }
    }

    /**
     * 将用户的token以hash结构存储在redis中，hash的key为设备ID
     *
     */
    private void saveToken(String employeeId, String deviceID, String token) {
        String key = CacheKeyPreConstant.TOKEN_KEY + employeeId;
        long timestramp = System.currentTimeMillis()/1000;
        Map<String,String> map = new HashMap<>();
        map.put("token", token);
        map.put("timestamp", String.valueOf(timestramp));
        jedisUtil.hset(key, deviceID, JSONUtils.obj2JSON(map));

    }

    /**
     * 清除用户的token
     *
     */
    public boolean clearToken(String employeeId, String deviceID) {
        if (!Strings.isNullOrEmpty(employeeId)) {
            String key = CacheKeyPreConstant.TOKEN_KEY + employeeId;
            return jedisUtil.hdel(key,deviceID);
        } else {
            return false;
        }
    }

    /**
     * 检测token
     *
     */
    public boolean checkToken(String employeeId, String deviceID, String accessToken) {
        boolean flag = false;
        logger.info("the check token is :" + accessToken);
        if (!Strings.isNullOrEmpty(employeeId)&&!Strings.isNullOrEmpty( deviceID)
        &&!Strings.isNullOrEmpty(accessToken )) {
            String key = CacheKeyPreConstant.TOKEN_KEY + employeeId;
            String tokenInfoStr = jedisUtil.hget(key, deviceID);
            logger.info("the check token is :" + tokenInfoStr);
            if (!Strings.isNullOrEmpty(tokenInfoStr)) {
                HashMap<String,String> tokenMap = JSONUtils.json2Obj(tokenInfoStr, new TypeReference<HashMap<String,String>>(){});
                String token = tokenMap.get("token");
                flag = accessToken.equals(token);
                logger.info("the check token is flag :" + flag);
            }
        }
        return flag;
    }


}
