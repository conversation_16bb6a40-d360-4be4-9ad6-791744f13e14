package com.xyy.saas.web.api.module.product.model;

import io.swagger.annotations.ApiModel;

import java.util.List;

/**
 * @ClassName YkqDataVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/2/8 23:32
 * @Version 1.0
 **/
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-07T14:34:49.141+08:00")

@ApiModel(description = "宜块钱导入信息类")
public class YkqDataVo {
    public List<List<String>> getDatas() {
        return datas;
    }

    public void setDatas(List<List<String>> datas) {
        this.datas = datas;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    private List<List<String>> datas;
    private String createUser;
    private String organSign;
}
