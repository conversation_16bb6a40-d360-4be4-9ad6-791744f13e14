package com.xyy.saas.web.api.module.member.model;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "会员短信充值")
public class MemberSmsRechargeVo implements Serializable {

    private static final long serialVersionUID = -6909463946068181030L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long id;
    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderId;
    /**
     * 短信id
     */
    @ApiModelProperty(value = "短信id")
    private Long smsId;
    /**
     * 短信套餐id
     */
    @ApiModelProperty(value = "短信套餐id")
    private Long smsPackageId;
    /**
     * 充值金额
     */
    @ApiModelProperty(value = "充值金额")
    private BigDecimal price;
    /**
     * 购买条数
     */
    @ApiModelProperty(value = "购买条数")
    private Integer count;
    /**
     * 原价
     */
    @ApiModelProperty(value = "原价")
    private BigDecimal costPrice;
    /**
     * 套餐类型
     */
    @ApiModelProperty(value = "套餐类型")
    private String smsPackage;
    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识")
    private String organSign;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
     * 促销  0、否  1、是
     */
    @ApiModelProperty(value = "促销  0、否  1、是")
    private Byte isSale;
    /**
     * 失败原因
     */
    @ApiModelProperty(value = "失败原因")
    private String failDesc;
    /**
     * 版本控制
     */
    @ApiModelProperty(value = "版本控制")
    private Integer baseVersion;
    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式")
    private Byte payType;
    /**
     * 支付状态 1-支付成功 2-退款成功  11-未支付 12-支付失败  13-退款失败
     */
    @ApiModelProperty(value = "支付状态 1-支付成功 2-退款成功  11-未支付 12-支付失败  13-退款失败", example = "1")
    private Byte payStatus;
    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
    private Date payTime;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private String startDate;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String endDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Long getSmsId() {
        return smsId;
    }

    public void setSmsId(Long smsId) {
        this.smsId = smsId;
    }

    public Long getSmsPackageId() {
        return smsPackageId;
    }

    public void setSmsPackageId(Long smsPackageId) {
        this.smsPackageId = smsPackageId;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public String getSmsPackage() {
        return smsPackage;
    }

    public void setSmsPackage(String smsPackage) {
        this.smsPackage = smsPackage;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Byte getIsSale() {
        return isSale;
    }

    public void setIsSale(Byte isSale) {
        this.isSale = isSale;
    }

    public String getFailDesc() {
        return failDesc;
    }

    public void setFailDesc(String failDesc) {
        this.failDesc = failDesc;
    }

    public Integer getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(Integer baseVersion) {
        this.baseVersion = baseVersion;
    }

    public Byte getPayType() {
        return payType;
    }

    public void setPayType(Byte payType) {
        this.payType = payType;
    }

    public Byte getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(Byte payStatus) {
        this.payStatus = payStatus;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    @Override
    public String toString() {
        return "MemberSmsRechargeVo{" +
                "id=" + id +
                ", orderId='" + orderId + '\'' +
                ", smsId=" + smsId +
                ", smsPackageId=" + smsPackageId +
                ", price=" + price +
                ", count=" + count +
                ", costPrice=" + costPrice +
                ", smsPackage='" + smsPackage + '\'' +
                ", organSign='" + organSign + '\'' +
                ", createTime=" + createTime +
                ", createUser='" + createUser + '\'' +
                ", isSale=" + isSale +
                ", failDesc='" + failDesc + '\'' +
                ", baseVersion=" + baseVersion +
                ", payType=" + payType +
                ", payStatus=" + payStatus +
                ", payTime=" + payTime +
                ", startDate='" + startDate + '\'' +
                ", endDate='" + endDate + '\'' +
                '}';
    }
}
