package com.xyy.saas.web.api.common.log;


import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.web.api.common.context.ActionContext;
import com.xyy.saas.web.api.common.context.AppActionContext;
import com.xyy.saas.web.api.common.entity.OperationLog;
import com.xyy.saas.web.api.module.utils.JedisUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;


/**
 *
 */
@Component
public class LogStrategy  {

    private static Logger logger = LoggerFactory.getLogger(LogStrategy.class);
    private static org.slf4j.Logger access_logger = LoggerFactory.getLogger("ACCESS_LOGGER");
    private static String HEARTBEAT_URL = "/product/saasheartbeat";

    @Autowired
    private JedisUtils jedisUtil;


    public void doLog(ActionContext context) {
        //心跳检测不记录日志
        if(context.getUrl().contains(HEARTBEAT_URL)){
            return ;
        }
        OperationLog operationLog = new OperationLog();
        CommonRequestModel model = context.getModel();
        if(model==null){
            model = new CommonRequestModel();
        }
        AppActionContext appContext = context.getAppContext();
        if(appContext == null){
            appContext = new AppActionContext();
        }
        operationLog.setId((int) jedisUtil.incr("operation:log:id:long"));
        operationLog.setOrganSign(model.getOrganSign());
        operationLog.setVersion(appContext.getAppVersion());
        operationLog.setMac( appContext.getAppMac());
        operationLog.setIp(context.getIp());
        operationLog.setRequestPath(context.getUrl());
        operationLog.setEmployeeId(String.valueOf(model.getEmployeeId()));
        operationLog.setOperationTime(new Date());
        operationLog.setTimeConsuming(String.valueOf(context.getTimeCost()));
        operationLog.setErrorMsg(context.getErrorMsg());
        operationLog.setResponseResult(context.getResponseResult());
        operationLog.setClient(context.getClient());
        operationLog.setClientParams(context.getClientParams());


        // 输出日志
        access_logger.info(JSONUtils.obj2JSON(operationLog));

    }
}
