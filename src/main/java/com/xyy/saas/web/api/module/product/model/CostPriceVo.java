package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * @ClassName CostPriceVo
 * @Description 成本价调价提交信息封装对象
 * <AUTHOR>
 * @Date 2020/8/19 15:33
 * @Version 1.0
 **/
@ApiModel(description = "成本价调价提交信息封装对象")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")
public class CostPriceVo {

    @JsonProperty("pref")
    private String pref;//调价方案编号

    @JsonProperty("name")
    private String name;//方案名称

    @JsonProperty("expirationTime")
    private String expirationTime;//过期时间

    @JsonProperty("remarks")
    private String remarks;//备注

    @JsonProperty("flag")
    private Byte flag;//配置调价方案编辑传1，其他提交不传

    @JsonProperty("detailVos")
    private List<CostPriceDetailVo> detailVos;

    @JsonProperty("chainStoreOrgansign")
    private String chainStoreOrgansign;//配置调价方案门店机构编号

    @ApiModelProperty(value = "配置调价方案编号")
    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    @JsonProperty("businessKey")
    private String businessKey;//配置调价方案编号

    @ApiModelProperty(value = "配置调价方案门店机构编号")
    public String getChainStoreOrgansign() {
        return chainStoreOrgansign;
    }

    public void setChainStoreOrgansign(String chainStoreOrgansign) {
        this.chainStoreOrgansign = chainStoreOrgansign;
    }

    @ApiModelProperty(value = "方案编号")
    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    @ApiModelProperty(value = "方案名称")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @ApiModelProperty(value = "有效期截止至")
    public String getExpirationTime() {
        return expirationTime;
    }

    public void setExpirationTime(String expirationTime) {
        this.expirationTime = expirationTime;
    }

    @ApiModelProperty(value = "备注")
    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    @ApiModelProperty(value = "调价商品列表详情")
    public List<CostPriceDetailVo> getDetailVos() {
        return detailVos;
    }

    public void setDetailVos(List<CostPriceDetailVo> detailVos) {
        this.detailVos = detailVos;
    }

    @ApiModelProperty(value = "配置编辑调价方案传1，其他提交不传")
    public Byte getFlag() {
        return flag;
    }

    public void setFlag(Byte flag) {
        this.flag = flag;
    }
}
