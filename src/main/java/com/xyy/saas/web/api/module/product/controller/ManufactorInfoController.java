package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.api.SystemDictApi;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.manufactor.core.api.ManufactorApi;
import com.xyy.saas.manufactor.core.dto.ManufactorDto;
import com.xyy.saas.product.core.dto.ManufactorPrefDto;
import com.xyy.saas.web.api.module.product.util.ExportExcelUtil;
import com.xyy.saas.rabbitmq.core.api.MessagePushApi;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.enums.DrugstoreBizModelEnum;
import com.xyy.user.module.enums.DrugstoreTypeEnum;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Controller
public class ManufactorInfoController implements ManufactorInfoApi {

    private static final Logger logger = LoggerFactory.getLogger(ManufactorInfoController.class);

    @Reference(version = "0.0.1")
    private ManufactorApi manufactorApi;

    @Reference(version = "0.0.1")
    private SystemDictApi systemDictApi;

    @Reference(version = "0.0.1")
    private MessagePushApi messagePushApi;

    @Reference(version = "0.0.1")
    private EmployeeApi employeeApi;

    @Reference(version = "0.0.1")
    private com.xyy.saas.product.core.api.ProductApi productApi;

    @Override
    public ResponseEntity<ResultVO> addOrUpdate(HttpServletRequest request, @ApiParam(value = "生产厂商查询对象", required = true) @Valid @RequestBody ManufactorDto manufactor) {
        String organSign = request.getHeader("organSign");
        String employeeId = request.getHeader("employeeId");
        logger.info("addOrUpdate manufactorInfo organSign:" + organSign + ",employeeId:" + employeeId + ",manufactor id:" + manufactor.getId());
        if (StringUtils.isEmpty(manufactor)) {
            logger.error("==========================参数不合法");
            return new ResponseEntity<ResultVO>(new ResultVO<>(ResultCodeEnum.ERROR, null), HttpStatus.OK);
        }

        if (StringUtils.isEmpty(manufactor.getName())) {
            logger.error("==========================生产厂商名称未填写");
            return new ResponseEntity<ResultVO>(new ResultVO<>(ResultCodeEnum.ERROR, "nomanufactorName"), HttpStatus.OK);
        }

            String username = employeeId;
            manufactor.setOrganSign(organSign);

        ManufactorPrefDto manufactorPrefDto = manufactorApi.saveBaseInfo(manufactor,username);
            if (manufactorPrefDto.getStatus() > 0) {

                return new ResponseEntity<ResultVO>(ResultVO.createSuccess(null), HttpStatus.OK);
            }
            if (-4 == manufactorPrefDto.getStatus()) {
                String msg = "生产厂商已存在（生产厂商编号：" + manufactorPrefDto.getPref() + "），请核对后重新提交！";
                return new ResponseEntity<ResultVO>(new ResultVO<>(ResultCodeEnum.ERROR, msg,-4), HttpStatus.OK);
            }

            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, manufactorPrefDto.getStatus()), HttpStatus.OK);
        }

    @Override
    public ResponseEntity<ResultVO> queryManufactorList(HttpServletRequest request,@ApiParam(value = "生产厂商查询对象", required = true) @Valid @RequestBody ManufactorDto manufactor) {
        Integer page = manufactor.getPage();
        Integer rows = manufactor.getRows();
        if(page==null){
            page=1;
        }
        if(rows==null){
            rows=50;
        }
        String organSign = request.getHeader("organSign");
        manufactor.setOrganSign(organSign);
        PageInfo pageInfo=new PageInfo();
        pageInfo.setPageSize(rows);
        pageInfo.setPageNum(page);

        if(!StringUtils.isEmpty(manufactor.getStartTimeStr())){
            manufactor.setStartTimeStr(manufactor.getStartTimeStr()+" 00:00:00");
        }
        if(!StringUtils.isEmpty(manufactor.getEndTimeStr())){
            manufactor.setEndTimeStr(manufactor.getEndTimeStr()+" 23:59:59");
        }
        PageInfo info = this.manufactorApi.queryManufactorList(pageInfo, manufactor);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(info),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getManufactorInfoById(HttpServletRequest request, @ApiParam(value = "生产厂商查询对象", required = true) @Valid @RequestBody ManufactorDto manufactor) {
        if ( StringUtils.isEmpty(manufactor.getId()) )
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(""), HttpStatus.OK);
        String organSign = request.getHeader("organSign");
        manufactor.setOrganSign(organSign);
        ManufactorDto manufactorInfo   =  manufactorApi.getManufactorInfoById(manufactor);

      return new ResponseEntity<ResultVO>(ResultVO.createSuccess(manufactorInfo),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> deleteManufactorInfoById(HttpServletRequest request, @ApiParam(value = "生产厂商查询对象", required = true) @Valid @RequestBody ManufactorDto manufactor) {
        if ( StringUtils.isEmpty(manufactor.getId()) )
            return new ResponseEntity<ResultVO>(new ResultVO<>(ResultCodeEnum.ERROR, null),HttpStatus.OK);
            String organSign = request.getHeader("organSign");
            String employeeId = request.getHeader("employeeId");
            String username = employeeId;
            manufactor.setUpdateUser(username);
            manufactor.setOrganSign(organSign);
              int  result=    productApi.getProductInfoByManufactor(manufactor.getId(),organSign);
              if(result>0){
                  return new ResponseEntity<ResultVO>(new ResultVO<>(ResultCodeEnum.ERROR, "生产厂商信息已被商品引用, 不允许删除"),HttpStatus.OK);

              }
        boolean flag=   manufactorApi.deleteManufactorInfoById(manufactor);
        if ( flag )
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.SUCCESS, null),HttpStatus.OK);
        else
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, null),HttpStatus.OK);
    }


    @Override
    public void exportExcelManufactor(HttpServletRequest request, HttpServletResponse response, @ApiParam(value = "生产厂商查询对象", required = true) @Valid @RequestBody ManufactorDto manufactor) {

        String organSign = request.getHeader("organSign");
        manufactor.setOrganSign(organSign);
        List<ManufactorDto> list = this.manufactorApi.exportManufactorList(manufactor);
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
        String extfilename="生产厂商基本信息"+df.format(new Date())+".xls";
        String sheetName = "生产厂商基本信息";
        String headers[] = new String[]{"生产厂商编号","生产厂商名称", "联系人姓名", "联系人电话","邮箱", "地址","创建时间", "备注"};
        String fieldNames[] = new String[]{ "pref","name","contactName", "contactPhone", "email", "detailedAddress","createTime","remark"};
        try {
            ExportExcelUtil.createExcel(response,request,extfilename,sheetName, headers, fieldNames, list,true);
        } catch (Exception e) {
           logger.error("生产厂商导出异常信息:{}",e);
        }
    }

    @Override
    public ResponseEntity<ResultVO> queryManufactorListNoPage(HttpServletRequest request,@ApiParam(value = "生产厂商查询对象", required = true) @Valid @RequestBody  ManufactorDto manufactor) {
        String organSign = request.getHeader("organSign");
        String modelJson = request.getHeader("commonRequestModel");
        Byte bizModel = null;
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                organSign = model.getHeadquartersOrganSign();
            }
        }
        manufactor.setOrganSign(organSign);

        List<ManufactorDto> manufactorDtoList= this.manufactorApi.queryManufactorListNoPage(manufactor);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(manufactorDtoList),HttpStatus.OK);


    }


}





