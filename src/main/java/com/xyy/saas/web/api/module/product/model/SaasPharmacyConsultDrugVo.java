package com.xyy.saas.web.api.module.product.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-30T11:33:52.138+08:00")
@ApiModel(description = "药品集合")
public class SaasPharmacyConsultDrugVo implements Serializable {

    @ApiModelProperty(value = "混合查询")
    private String mixCondition;
    @ApiModelProperty(value = "通用名")
    private String commonName;
    @ApiModelProperty(value = "单位")
    private String unitName;
    @ApiModelProperty(value = "规格/型号")
    private String attributeSpecification;
    @ApiModelProperty(value = "批准文号")
    private String approvalNumber;
    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "生产厂家")
    private String manufacturer;
    @ApiModelProperty(value = "条形码")
    private String barCode;
    @ApiModelProperty(value = "是否含麻")
    private String containingHempYn;
    @ApiModelProperty(value = "标准库ID")
    private Long standardLibraryId;
    @ApiModelProperty(value = "处方分类")
    private String prescriptionClassification;
    @ApiModelProperty(value = "剂型ID")
    private String dosageFormId;
    private String commonMixCode;
    private String manufacturerMixCode;
    private Byte status;

    public String getCommonMixCode() {
        return commonMixCode;
    }

    public void setCommonMixCode(String commonMixCode) {
        this.commonMixCode = commonMixCode;
    }

    public String getManufacturerMixCode() {
        return manufacturerMixCode;
    }

    public void setManufacturerMixCode(String manufacturerMixCode) {
        this.manufacturerMixCode = manufacturerMixCode;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getMixCondition() {
        return mixCondition;
    }

    public void setMixCondition(String mixCondition) {
        this.mixCondition = mixCondition;
    }

    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getAttributeSpecification() {
        return attributeSpecification;
    }

    public void setAttributeSpecification(String attributeSpecification) {
        this.attributeSpecification = attributeSpecification;
    }

    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getContainingHempYn() {
        return containingHempYn;
    }

    public void setContainingHempYn(String containingHempYn) {
        this.containingHempYn = containingHempYn;
    }

    public Long getStandardLibraryId() {
        return standardLibraryId;
    }

    public void setStandardLibraryId(Long standardLibraryId) {
        this.standardLibraryId = standardLibraryId;
    }

    public String getPrescriptionClassification() {
        return prescriptionClassification;
    }

    public void setPrescriptionClassification(String prescriptionClassification) {
        this.prescriptionClassification = prescriptionClassification;
    }

    public String getDosageFormId() {
        return dosageFormId;
    }

    public void setDosageFormId(String dosageFormId) {
        this.dosageFormId = dosageFormId;
    }
}
