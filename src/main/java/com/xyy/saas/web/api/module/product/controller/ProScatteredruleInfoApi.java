package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.dto.ProductScatteredRuleVoDto;
import com.xyy.saas.web.api.common.config.annotition.RepeatSubmitValidation;
import com.xyy.saas.web.api.module.product.model.ProductScatteredRuleVo;
import com.xyy.saas.web.api.module.product.model.ScatteredRuleQueryExportVo;
import com.xyy.saas.web.api.module.product.model.ScatteredRuleQueryVo;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
@RequestMapping("/product")
@Api(value = "4.0.1商品拆零信息维护接口", description = "4.0.1商品拆零信息维护接口")
public interface ProScatteredruleInfoApi {

    @ApiOperation(value = "商品拆零信息维护 查询", notes = "商品拆零信息维护 查询", response = ResultVO.class, tags={ "4.0.1商品拆零信息维护接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/baseinfo/proScattered/query",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> query(HttpServletRequest request, @ApiParam(value = "商品拆零信息维护 查询对象", required = true) @Valid @RequestBody ScatteredRuleQueryVo scatteredRuleQueryVo);

    @ApiOperation(value = "商品拆零信息维护 新增 查询商品", notes = "商品拆零信息维护 查询", response = ResultVO.class, tags={ "4.0.1商品拆零信息维护接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/baseinfo/proScattered/productQuery",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> productQuery(HttpServletRequest request, @ApiParam(value = "商品拆零信息维护 新增查询对象", required = true) @Valid @RequestBody ScatteredRuleQueryVo scatteredRuleQueryVo);

    @ApiOperation(value = "连锁商品拆零信息维护 新增 查询商品", notes = "连锁商品拆零信息维护 查询", response = ResultVO.class, tags={ "4.0.1商品拆零信息维护接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/baseinfo/proScattered/chainProductQuery",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> chainProductQuery(HttpServletRequest request, @ApiParam(value = "连锁商品拆零信息维护 新增查询对象", required = true) @Valid @RequestBody ScatteredRuleQueryVo scatteredRuleQueryVo);

    @ApiOperation(value = "商品拆零信息维护 更新数据回显", notes = "商品拆零信息维护 更新数据回显", response = ProductScatteredRuleVoDto.class, tags={ "4.0.1商品拆零信息维护接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ProductScatteredRuleVoDto.class) })
    @RequestMapping(value = "/baseinfo/proScattered/toUpdate",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getProScatteredById(HttpServletRequest request, @ApiParam(value = "商品拆零信息维护 新增查询对象", required = true) @Valid @RequestBody ScatteredRuleQueryVo scatteredRuleQueryVo);

    @ApiOperation(value = "商品拆零信息维护 更新保存接口", notes = "商品拆零信息维护 更新保存接口", response = ResultVO.class, tags={ "4.0.1商品拆零信息维护接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/proScattered/save", method = RequestMethod.POST)
    @RepeatSubmitValidation(resultType = 4)
    public ResponseEntity<ResultVO> addOrUpdate(HttpServletRequest request, @ApiParam(value = "拆零商品信息封装对象", required = true) @Valid @RequestBody ProductScatteredRuleVo scatteredRulePo) throws InterruptedException;

    @ApiOperation(value = "商品拆零导出excel接口", notes = "商品拆零导出excel接口", response = ResultVO.class, tags={ "4.0.1商品拆零信息维护接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/proScattered/exportListExcel", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> proScatteredExportListExcel(HttpServletRequest request, @ApiParam(value = "拆零商品信息封装对象", required = true) @Valid @RequestBody ScatteredRuleQueryExportVo exportVo) throws InterruptedException;

    @ApiOperation(value = "商品拆零规则启用禁用接口", notes = "商品拆零规则启用禁用接口", response = ResultVO.class, tags={ "4.0.1商品拆零信息维护接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/proScattered/updateStatus", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> proScatteredUpdateStatus(HttpServletRequest request, @ApiParam(value = "参数只需要传id，status参数即可", required = true) @Valid @RequestBody ScatteredRuleQueryVo updateVo) throws InterruptedException;

}
