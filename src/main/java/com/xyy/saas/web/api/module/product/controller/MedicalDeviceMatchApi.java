package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.dto.ProductCountryDrugStandardDto;
import com.xyy.saas.product.core.dto.ProductDrugStandardDto;
import com.xyy.saas.udi.core.dto.MedicalDeviceQueryDto;
import com.xyy.saas.udi.core.dto.MedicalDeviceUdiQueryDto;
import com.xyy.saas.web.api.module.product.model.ApproveHistoryVo;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;


@Api(value = "商品与医疗器械匹配API", description = "商品与医疗器械匹配API")
@RequestMapping("/product")
public interface MedicalDeviceMatchApi {

    @ApiOperation(value = "商品匹配列表查询接口", notes = "商品匹配列表查询接口", response = ResultVO.class, tags={ "商品与医疗器械匹配API", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功") })
    @RequestMapping(value = "/medical/device/match/list", method = RequestMethod.POST)
    ResponseEntity<ResultVO> list(HttpServletRequest request, @ApiParam(value = "医疗器械匹配信息实体", required = true) @Valid @RequestBody MedicalDeviceQueryDto dto);

    @ApiOperation(value = "商品匹配医疗器械表查询接口", notes = "商品匹配医疗器械表查询接口", response = ResultVO.class, tags={ "商品与医疗器械匹配API", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功") })
    @ResponseBody
    @RequestMapping(value = "/medical/device/toMatch", method = RequestMethod.POST)
    ResponseEntity<ResultVO> matchMedicalDevice(HttpServletRequest request, @ApiParam(value = "医疗器械匹配信息实体", required = true) @Valid @RequestBody MedicalDeviceUdiQueryDto dto);

    @ApiOperation(value = "商品匹配医疗器械表接口", notes = "商品匹配医疗器械表接口", response = ApproveHistoryVo.class, tags={ "商品与药监匹配API", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功") })
    @RequestMapping(value = "/medical/device/match",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> matchProduct(HttpServletRequest request, @ApiParam(value = "商品匹配信息实体", required = true) @Valid @RequestBody ProductDrugStandardDto dto);

    @ApiOperation(value = "商品匹配国家医疗器械表接口", notes = "商品匹配国家医疗器械表接口", response = ApproveHistoryVo.class, tags={ "商品与药监匹配API", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功") })
    @RequestMapping(value = "/medical/countryDevice/match",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> countryMatchProduct(HttpServletRequest request, @ApiParam(value = "商品匹配信息实体", required = true) @Valid @RequestBody ProductCountryDrugStandardDto dto);

}

