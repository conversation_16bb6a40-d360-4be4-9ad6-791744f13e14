package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description 会员慢病种类关联药品-列表查询条件QueryVo（无须分页）
 * <AUTHOR>
 * @Create 2020-09-24 20:13
 */
@Data
public class MemberChronicProductQueryReqVo{
    //种类名称：1-40位任意字符  筛选项：商品信息、生产厂家 1）商品信息：可输入通用名称、助记码和商品编号进行查询。  2）生产厂家：可输入厂家名称进行查询
    @ApiModelProperty(value = "混合查询")
    private String mixedQuery;
    @ApiModelProperty(value = "生产厂家")
    private String manufacturer;
    @ApiModelProperty(value = "慢病编号")
    private String chronicPref;
    @ApiModelProperty(value = "联营门店-仅联营总部用")
    private String joinDrugstore;

    /**
     * 开始的页码
     */
    private Integer pageNum;

    /**
     * 每页的数量
     */
    private Integer pageSize;
}
