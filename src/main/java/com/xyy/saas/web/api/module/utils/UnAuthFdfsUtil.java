package com.xyy.saas.web.api.module.utils;

import com.github.tobato.fastdfs.domain.fdfs.StorePath;
import com.github.tobato.fastdfs.service.FastFileStorageClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/7/2
 */
@Slf4j
@Component
public class UnAuthFdfsUtil {
    @Resource
    @Qualifier("unAuthFastFileStorageClient")
    private FastFileStorageClient fastFileStorageClient;
    @Value("${unauthfdfs.upload.url}")
    private String uploadFileUrl;

    /**
     * fastdfs上传文件
     *
     * @param file
     * @return
     */
    public String uploadFile(MultipartFile file){
        if (StringUtils.isEmpty(uploadFileUrl)) {
            throw new RuntimeException("uploadUrl can not be null");
        }
        String url = "";
        String fileName = file.getOriginalFilename();
        StorePath storePath = null;
        try {
            storePath = fastFileStorageClient.uploadFile(file.getInputStream(), file.getSize(), FilenameUtils.getExtension(fileName), null);
            log.info("上传地址" + storePath.getFullPath());
            url = uploadFileUrl + storePath.getFullPath();
        } catch (Exception e) {
            log.error("文件名为" + fileName + "上传报错:", e);
        }
        return url;
    }
}
