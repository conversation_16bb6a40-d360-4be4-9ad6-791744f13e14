package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.control.dto.costprice.*;
import com.xyy.saas.rabbitmq.core.api.MessagePushApi;
import com.xyy.saas.web.api.module.product.model.*;
import com.xyy.user.module.enums.DrugstoreBizModelEnum;
import com.xyy.user.module.enums.DrugstoreTypeEnum;
import com.xyy.util.StringUtil;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName ProductCostPriceApiController
 * @Description 商品调价方案统一接口实现类
 * <AUTHOR>
 * @Date 2020/8/19 12:53
 * @Version 1.0
 **/
@Controller
public class ProductCostPriceApiController implements ProductCostPriceApi {

    private static final Logger logger = LoggerFactory.getLogger(ProductInfoApiController.class);

    @Reference(version = "0.0.1")
    private com.xyy.saas.product.control.api.costprice.ProductCostPriceApi productCostPriceApi;

    @Reference(version = "0.0.1")
    private MessagePushApi messagePushApi;

    @Override
    public ResponseEntity<ResultVO> computeCostPrice(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody ComputeCostPriceVo computeCostPriceVo) {
        logger.info("computeCostPrice:param:{}", JSON.toJSONString(computeCostPriceVo));
        if(computeCostPriceVo == null){//参数非空判断
            return new ResponseEntity(new ResultVO<ComputeCostPriceVo>(ResultCodeEnum.ERROR, "参数为空，请填写", null),  HttpStatus.OK);
        }
        BigDecimal newCostPrice = null;
        BigDecimal oldCostPrice = null;
        BigDecimal adjustmentRatio = null;
        if(!StringUtils.isEmpty(computeCostPriceVo.getNewCostPrice())){
            newCostPrice = new BigDecimal(computeCostPriceVo.getNewCostPrice());//新成本价
        }
        if(!StringUtils.isEmpty(computeCostPriceVo.getOldCostPrice())){
            oldCostPrice = new BigDecimal(computeCostPriceVo.getOldCostPrice());//原成本价
        }
        if(!StringUtils.isEmpty(computeCostPriceVo.getAdjustmentRatio())){
            adjustmentRatio = new BigDecimal(computeCostPriceVo.getAdjustmentRatio());//调整比例
        }
        if(computeCostPriceVo.getFlag() == null){
            return new ResponseEntity(new ResultVO<ComputeCostPriceVo>(ResultCodeEnum.ERROR, "计算方向没设定", null),  HttpStatus.OK);
        }
        if(computeCostPriceVo.getFlag() == 1){//计算新成本价
            if(adjustmentRatio != null){
                if(oldCostPrice != null ){
                    newCostPrice = oldCostPrice.multiply(adjustmentRatio).divide(new BigDecimal(100)).add(oldCostPrice).setScale(6,BigDecimal.ROUND_HALF_UP);
                    computeCostPriceVo.setNewCostPrice(String.valueOf(newCostPrice));
                }
            }else{
                if(oldCostPrice != null && oldCostPrice.compareTo(new BigDecimal("0.00"))!=0){
                    adjustmentRatio = newCostPrice.subtract(oldCostPrice).divide(oldCostPrice,6,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2,BigDecimal.ROUND_HALF_UP);
                    computeCostPriceVo.setAdjustmentRatio(String.valueOf(adjustmentRatio));
                }
            }
        }else{//计算调价比例
            if(newCostPrice != null){
                if(oldCostPrice != null && oldCostPrice.compareTo(new BigDecimal("0.00"))!=0){
                    adjustmentRatio = newCostPrice.subtract(oldCostPrice).divide(oldCostPrice,6,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2,BigDecimal.ROUND_HALF_UP);
                    computeCostPriceVo.setAdjustmentRatio(String.valueOf(adjustmentRatio));
                }
            }else{
                if(oldCostPrice != null){
                    newCostPrice = oldCostPrice.multiply(adjustmentRatio).divide(new BigDecimal(100)).add(oldCostPrice).setScale(6,BigDecimal.ROUND_HALF_UP);
                    computeCostPriceVo.setNewCostPrice(String.valueOf(newCostPrice));
                }
            }
        }
        logger.info("computeCostPrice:result:{}", JSON.toJSONString(computeCostPriceVo));
        return new ResponseEntity(new ResultVO<ComputeCostPriceVo>(computeCostPriceVo),  HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> computeCostPriceBatch(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody ComputeCostPriceBatchVo computeCostPriceBatchVo) {
        logger.info("computeCostPriceBatch:param:{}",JSON.toJSONString(computeCostPriceBatchVo));
        if(computeCostPriceBatchVo == null){
            return new ResponseEntity(new ResultVO<ComputeCostPriceBatchVo>(ResultCodeEnum.ERROR, "参数为空，请填写", null),  HttpStatus.OK);
        }
        computeCostPriceBatchVo.getDetails().stream().forEach(vo -> {
            BigDecimal adjustRatio = new BigDecimal(computeCostPriceBatchVo.getAdjustmentRatio());
            if(!StringUtils.isEmpty(vo.getOldCostPrice())){
                BigDecimal oldPrice = new BigDecimal(vo.getOldCostPrice());
                vo.setNewCostPrice(String.valueOf(oldPrice.multiply(adjustRatio).divide(new BigDecimal(100)).add(oldPrice).setScale(6,BigDecimal.ROUND_HALF_UP)));
            }
            vo.setAdjustmentRatio(computeCostPriceBatchVo.getAdjustmentRatio());
        });
        logger.info("computeCostPriceBatch result:{}",JSON.toJSONString(computeCostPriceBatchVo));
        return new ResponseEntity(new ResultVO<ComputeCostPriceBatchVo>(computeCostPriceBatchVo),  HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> costPriceList(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody CostPriceListQueryVo costPriceListQueryVo) {
        logger.info("costPriceList : param:{}",JSON.toJSONString(costPriceListQueryVo));
        String organSign = request.getHeader("organSign");//获取当前机构号
        CostPriceAdjustQueryDto paramDto = new CostPriceAdjustQueryDto();
        BeanUtils.copyProperties(costPriceListQueryVo, paramDto);
        paramDto.setOrganSign(organSign);//当前机构号
        PageInfo pageInfo = new PageInfo();//组装分页信息
        if(costPriceListQueryVo.getPage() == null || costPriceListQueryVo.getPage() <= 0){
            pageInfo.setPageNum(1);
        }else{
            pageInfo.setPageNum(costPriceListQueryVo.getPage());
        }
        if(costPriceListQueryVo.getRows() == null || costPriceListQueryVo.getRows() <= 0){
            pageInfo.setPageSize(50);
        }else{
            pageInfo.setPageSize(costPriceListQueryVo.getRows());
        }
        ResultVO resultVO = productCostPriceApi.getCostPriceAdjustPage(paramDto,pageInfo);
        PageInfo pageinforesult = (PageInfo) resultVO.getResult();
        List<CostPriceListResultVo> vos = new ArrayList<>();
        List<CostPriceAdjustDto> dtos = pageinforesult.getList();
        if(!CollectionUtils.isEmpty(dtos)){
            dtos.stream().forEach(dto -> {
                CostPriceListResultVo vo = new CostPriceListResultVo();
                BeanUtils.copyProperties(dto,vo);
                vos.add(vo);
            });
        }
        pageinforesult.setList(vos);
        ResultVO resultVOresult = new ResultVO(pageinforesult);
        logger.info("costPriceList : result:{}",JSON.toJSONString(resultVOresult));
        return new ResponseEntity(resultVOresult,  HttpStatus.OK);
    }
    private void refreshDataSuccToMQ(String organSign) {
        JSONObject json = new JSONObject();
        String[] tables = {"saas_product_price_policy"};
        json.put("code", "sync");
        json.put("tables", tables);

        messagePushApi.sendMsgByOrganSign(organSign, json.toJSONString());
    }

    @Override
    @Deprecated
    public ResponseEntity<ResultVO> temporaryStorage(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody CostPriceVo costPriceVo) {
        logger.info("temporaryStorage param:{}", JSON.toJSONString(costPriceVo));
        String organSign = request.getHeader("organSign");//获取当前机构号
        String userId = request.getHeader("employeeId");
        CostPriceDto costPriceDto = new CostPriceDto();
        BeanUtils.copyProperties(costPriceVo,costPriceDto);
        costPriceDto.setOrganSign(organSign);
        costPriceDto.setCreateUser(userId);
        List<CostPriceDetailDto> detailVos = new ArrayList<>();
        if(!CollectionUtils.isEmpty(costPriceVo.getDetailVos())){
            costPriceVo.getDetailVos().stream().forEach(vo ->{
                CostPriceDetailDto dto = new CostPriceDetailDto();
                BeanUtils.copyProperties(vo,dto);
                if(!StringUtils.isEmpty(vo.getPriceLower())){
                    dto.setPriceLower(new BigDecimal(vo.getPriceLower()));//价格下限
                }
                if(!StringUtils.isEmpty(vo.getPriceUpper())){
                    dto.setPriceUpper(new BigDecimal(vo.getPriceUpper()));//价格上限
                }
                if(!StringUtils.isEmpty(vo.getNewCostPrice())){
                    dto.setNewCostPrice(new BigDecimal(vo.getNewCostPrice()));
                }
                dto.setIsEnquiry(vo.getIsEnquiryId());
                detailVos.add(dto);
            });
        }
        costPriceDto.setDetailVos(detailVos);
        ResultVO resultVO = productCostPriceApi.temporaryStorage(costPriceDto);
        logger.info("temporaryStorage result:{}",resultVO);
        return new ResponseEntity(resultVO,  HttpStatus.OK);
    }

    @Override
    @Deprecated
    public ResponseEntity<ResultVO> submit(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody CostPriceVo costPriceVo) {
        String organSign = request.getHeader("organSign");//获取当前机构号
        String userId = request.getHeader("employeeId");
        CostPriceDto costPriceDto = new CostPriceDto();
        costPriceDto.setOrganSign(organSign);
        costPriceDto.setCreateUser(userId);
        BeanUtils.copyProperties(costPriceVo,costPriceDto);
        List<CostPriceDetailDto> detailVos = new ArrayList<>();
        costPriceVo.getDetailVos().stream().forEach(vo ->{
            CostPriceDetailDto dto = new CostPriceDetailDto();
            BeanUtils.copyProperties(vo,dto);
            if(!StringUtils.isEmpty(vo.getPriceLower())){
                dto.setPriceLower(new BigDecimal(vo.getPriceLower()));//价格下限
            }
            if(!StringUtils.isEmpty(vo.getPriceUpper())){
                dto.setPriceUpper(new BigDecimal(vo.getPriceUpper()));//价格上限
            }
            if(!StringUtils.isEmpty(vo.getNewCostPrice())){
                dto.setNewCostPrice(new BigDecimal(vo.getNewCostPrice()));
            }
            dto.setIsEnquiry(vo.getIsEnquiryId());
            detailVos.add(dto);
        });
        costPriceDto.setDetailVos(detailVos);
        ResultVO resultVO = productCostPriceApi.submit(costPriceDto);
        return new ResponseEntity(resultVO,  HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> disableOrenable(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody CostPriceDisableOrenableVo vo) {
        logger.info("disableOrenable params:{}",JSON.toJSONString(vo));
        String organSign = request.getHeader("organSign");//获取当前机构号
        DisableOrenableQueryDto dto = new DisableOrenableQueryDto();
        dto.setOrganSign(organSign);//机构号
        dto.setDisableOrenable(vo.getDisableOrenable());//是否禁用
        dto.setPref(vo.getPref());//方案编号
        ResultVO resultVO = null;
        ResultVO apiResult = productCostPriceApi.disableOrenable(dto);
        if((Integer)apiResult.getResult() == -1){
            resultVO = new ResultVO(ResultCodeEnum.ERROR, "方案已被引用，不允许禁用", -1);
        }else if((Integer)apiResult.getResult() == 1){
            resultVO = new ResultVO(ResultCodeEnum.SUCCESS, "成功", -1);
        }
        logger.info("disableOrenable result:{}",JSON.toJSONString(resultVO));
        return new ResponseEntity(resultVO,  HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> selectProductsForAdd(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody CostPriceQueryProductVo costPriceQueryProductVo) {
        logger.info("selectProductsForAdd param:{}",JSON.toJSONString(costPriceQueryProductVo));
        String organSign = request.getHeader("organSign");//获取当前机构号
        CostPriceQueryProductDto queryProductDto = new CostPriceQueryProductDto();
        queryProductDto.setOrganSign(organSign);//机构号
        queryProductDto.setMixQuery(costPriceQueryProductVo.getMixQuery());//混合查询
        queryProductDto.setProductType(costPriceQueryProductVo.getProductType());//自定义分类
        queryProductDto.setSystemType(costPriceQueryProductVo.getSystemType());//商品分类
        queryProductDto.setStandardLibraryId(costPriceQueryProductVo.getStandardLibraryId());//标准库id
        queryProductDto.setControlSalesYn(costPriceQueryProductVo.getControlSalesYn());//是否控销
        queryProductDto.setManufacturer(costPriceQueryProductVo.getManufacturer());
        queryProductDto.setSixLevels(costPriceQueryProductVo.getSixLevels());
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(costPriceQueryProductVo.getPage() == null?1:costPriceQueryProductVo.getPage());
        pageInfo.setPageSize(costPriceQueryProductVo.getRows() == null?50:costPriceQueryProductVo.getRows());
        ResultVO resultVO = productCostPriceApi.selectProductsForAdd(queryProductDto,pageInfo);
        PageInfo pageProduct = (PageInfo) resultVO.getResult();
        List<CostPriceQueryProductResultDto> dtos = pageProduct.getList();
        List<CostPriceQueryProductResultVo> vos = new ArrayList<>();
        if(!CollectionUtils.isEmpty(dtos)){
            int i = 1;
            for(CostPriceQueryProductResultDto dto:dtos){
                CostPriceQueryProductResultVo vo = new CostPriceQueryProductResultVo();
                BeanUtils.copyProperties(dto,vo);
                vo.setSid((pageInfo.getPageNum()-1)*pageInfo.getPageSize()+(i++));
                vo.setAdjustmentRatio(String.valueOf(dto.getAdjustmentRatio()));//调价比例转换成字符串
                if(dto.getOldCostPrice() != null){//原成本价判空
                    vo.setOldCostPrice(String.valueOf(dto.getOldCostPrice()));//原成本价
                }
                if(dto.getNewCostPrice() != null){
                    vo.setNewCostPrice(String.valueOf(dto.getNewCostPrice()));
                }
                vo.setProductPref(dto.getPref());
                if(dto.getTaxCost() != null){//采购含税价判空处理
                    vo.setTaxCost(String.valueOf(dto.getTaxCost()));//采购含税价
                }
                vo.setSystemType(dto.getSystemType());
                vo.setControlSalesYn(dto.getControlSalesYn());
                vos.add(vo);
            }
        }
        pageProduct.setList(vos);
        logger.info("selectProductsForAdd result:{}",JSON.toJSONString(pageProduct));
        return new ResponseEntity(new ResultVO<>(pageProduct),  HttpStatus.OK);
    }
    @Deprecated
    @Override
    public ResponseEntity<ResultVO> selectCostAdjustsByPref(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody CostAdjustsByPrefQueryVo costAdjustsByPrefQueryVo) {
        logger.info("selectCostAdjustsByPref param:{}",JSON.toJSONString(costAdjustsByPrefQueryVo));
        String organSign = request.getHeader("organSign");//获取当前机构号
        CostAdjustsByPrefQueryDto paramDto = new CostAdjustsByPrefQueryDto();
        //vo拷贝到dto (source, target)
        BeanUtils.copyProperties(costAdjustsByPrefQueryVo, paramDto);
        paramDto.setOrganSign(organSign);//机构号
        ResultVO resultVO = productCostPriceApi.selectCostAdjustsByPref(paramDto);
        //dto拷贝到vo,返回给前端
        CostAdjustsByPrefResultDto costAdjustsByPrefResultDto = (CostAdjustsByPrefResultDto) resultVO.getResult();
        List<CostAdjustsByPrefResultDetailDto> detailDtoList = costAdjustsByPrefResultDto.getDetailDtoList();
        CostAdjustsByPrefResultVo costAdjustsByPrefResultVo = new CostAdjustsByPrefResultVo();
        BeanUtils.copyProperties(costAdjustsByPrefResultDto,costAdjustsByPrefResultVo);
        List<CostAdjustsByPrefResultDetailVo> detailVoList = new ArrayList<CostAdjustsByPrefResultDetailVo>();
        int i = 0;
        if(!CollectionUtils.isEmpty(detailDtoList)){
            for(CostAdjustsByPrefResultDetailDto dto:detailDtoList){
                CostAdjustsByPrefResultDetailVo vo = new CostAdjustsByPrefResultDetailVo();
                BeanUtils.copyProperties(dto,vo);
                vo.setSid(++i);
                vo.setAdjustmentRatio(String.valueOf(dto.getAdjustmentRatio()).equals("null")?"":String.valueOf(dto.getAdjustmentRatio()));//调价比例
                vo.setOldCostPrice(String.valueOf(dto.getOldCostPrice()).equals("null")?"":String.valueOf(dto.getOldCostPrice()));//原成本价
                vo.setNewCostPrice(String.valueOf(dto.getNewCostPrice()).equals("null")?"":String.valueOf(dto.getNewCostPrice()));//新成本价
                vo.setTaxCost(String.valueOf(dto.getTaxCost()).equals("null")?"":String.valueOf(dto.getTaxCost()));//采购含税价
                if(dto.getPriceUpper() != null){
                    vo.setPriceUpper(String.valueOf(dto.getPriceUpper()));//价格上限
                }
                if(dto.getPriceLower() != null){
                    vo.setPriceLower(String.valueOf(dto.getPriceLower()));//价格下限
                }
                if(dto.getNewCostPrice() != null){//出货价
                    vo.setNewCostPrice(String.valueOf(dto.getNewCostPrice()));//出货价
                }
                if(dto.getValueType() == 3){
                    vo.setValueTypeName("按采购价加点");
                }else if(dto.getValueType()==2){
                    vo.setValueTypeName("按出货价取值");
                }else if(dto.getValueType()==4){
                    vo.setValueTypeName("按外采采购价价点");
                }
                vo.setValueType(dto.getValueType());
                vo.setSystemType(dto.getSystemType());
                vo.setSystemTypeName(dto.getSystemTypeName());
                detailVoList.add(vo);
            }
        }
        costAdjustsByPrefResultVo.setDetailVoList(detailVoList);
        ResultVO resultVOresult = new ResultVO(costAdjustsByPrefResultVo);
        logger.info("selectCostAdjustsByPref : result:{}",JSON.toJSONString(resultVOresult));
        return new ResponseEntity(resultVOresult,  HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> selectUsableAdjusts(HttpServletRequest request) {
        //此接口无需实现，可以复用调价方案列表页查询接口
        List<CostPriceListResultVo> lists = new ArrayList<>();
        ResultVO resultVO = new ResultVO(lists);
        return new ResponseEntity(resultVO,  HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> selectConfigurationSchemes(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody ConfigurationSchemesQueryVo costPriceQueryProductVo) {
        logger.info("selectConfigurationSchemes param:{}",JSON.toJSONString(costPriceQueryProductVo));
        String organSign = request.getHeader("organSign");//获取当前机构号
        ConfigurationSchemesQueryDto queryDto = new ConfigurationSchemesQueryDto();
        BeanUtils.copyProperties(costPriceQueryProductVo,queryDto);
        queryDto.setOrganSign(organSign);//机构号
        PageInfo pageInfo = new PageInfo();//组装分页信息
        if(costPriceQueryProductVo.getPage() == null || costPriceQueryProductVo.getPage() <= 0){
            pageInfo.setPageNum(1);
        }else{
            pageInfo.setPageNum(costPriceQueryProductVo.getPage());
        }
        if(costPriceQueryProductVo.getRows() == null || costPriceQueryProductVo.getRows() <= 0){
            pageInfo.setPageSize(50);
        }else{
            pageInfo.setPageSize(costPriceQueryProductVo.getRows());
        }
        ResultVO resultVO = productCostPriceApi.selectAdjustConfigurationsPage(queryDto,pageInfo);
       /* PageInfo pageInfo1 = (PageInfo) resultVO.getResult();
        List<ConfigurationSchemesResultVo> vos = new ArrayList<>();
        List<ConfigurationSchemesResultDto> dtos = pageInfo1.getList();
        if(!CollectionUtils.isEmpty(dtos)){
            dtos.stream().forEach(dto ->{
                ConfigurationSchemesResultVo vo = new ConfigurationSchemesResultVo();
                BeanUtils.copyProperties(dto,vo);
                vos.add(vo);
            });
        }
        pageInfo1.setList(vos);*/
        logger.info("selectConfigurationSchemes result:{}",JSON.toJSONString(resultVO));
        return new ResponseEntity(resultVO,  HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> deleteConfigurationSchemes(HttpServletRequest request, @Valid @RequestBody ConfigureAdjustsDto configureAdjustsDto) {
        String organSign = request.getHeader("organSign");//获取当前机构号
        configureAdjustsDto.setOrganSign(organSign);
        logger.info("deleteConfigurationSchemes param:{}", JSON.toJSONString(configureAdjustsDto));
        ResultVO resultVO;
        try {
            resultVO = productCostPriceApi.deleteConfigurationSchemes(configureAdjustsDto);
        } catch (Exception e) {
            logger.error("deleteConfigurationSchemes error, params:{}", JSON.toJSONString(configureAdjustsDto), e);
            resultVO = ResultVO.createError();
        }
        return new ResponseEntity(resultVO,  HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> configureAdjusts(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody ConfigureAdjustsVo configureAdjustsVo) {
        logger.info("configureAdjusts param:{}",JSON.toJSONString(configureAdjustsVo));
        String organSign = request.getHeader("organSign");//获取当前机构号
        String userId = request.getHeader("employeeId");
        ConfigureAdjustsDto configureAdjustsDto = new ConfigureAdjustsDto();
        BeanUtils.copyProperties(configureAdjustsVo,configureAdjustsDto);
        configureAdjustsDto.setOrganSign(organSign);//当前机构号
        configureAdjustsDto.setCreateUser(userId);
        ResultVO resultVO = productCostPriceApi.configureAdjusts(configureAdjustsDto);//配置调价方案
        return new ResponseEntity(resultVO,  HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> checkTaskStatus(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody CheckTaskStatusQueryVo checkTaskStatusQueryVo) {
        TaskQueryDto queryDto = new TaskQueryDto();
        queryDto.setTaskNo(checkTaskStatusQueryVo.getTaskNo());
        return new ResponseEntity(productCostPriceApi.checkTaskStatus(queryDto),  HttpStatus.OK);//0处理成功，1,处理中
    }

    @Override
    public ResponseEntity<ResultVO> getAdjustByLocalOrganSign(HttpServletRequest request) {
        String organSign = request.getHeader("organSign");//获取当前机构号
        CostPriceQueryProductDto queryDto = new CostPriceQueryProductDto();
        queryDto.setChainOrganSign(organSign);//门店机构号
        ResultVO resultVO = productCostPriceApi.getAdjustByLocalOrganSign(queryDto);
        List<ConfigurationSchemesResultVo> vos = new ArrayList<>();
        List<ConfigurationSchemesResultDto> dtos = (List<ConfigurationSchemesResultDto>) resultVO.getResult();
        if(!CollectionUtils.isEmpty(dtos)){
            dtos.stream().forEach(dto ->{
                ConfigurationSchemesResultVo vo = new ConfigurationSchemesResultVo();
                BeanUtils.copyProperties(dto,vo);
                vos.add(vo);
            });
        }
        logger.info("selectConfigurationSchemes result:{}",JSON.toJSONString(vos));
        return new ResponseEntity(new ResultVO<>(vos),  HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> selectProductsForPriceUpperLower(HttpServletRequest request,  @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody CostPriceQueryProductVo costPriceQueryProductVo) {
        String organSign = request.getHeader("organSign");//获取当前机构号
        CostPriceQueryProductDto queryProductDto = new CostPriceQueryProductDto();
        queryProductDto.setMixQuery(costPriceQueryProductVo.getMixQuery());//混合查询
        queryProductDto.setSystemType(costPriceQueryProductVo.getSystemType());//商品分类
        queryProductDto.setStandardLibraryId(costPriceQueryProductVo.getStandardLibraryId());//标准库id
        PageInfo pageInfo = new PageInfo();//组装分页信息
        if(costPriceQueryProductVo.getPage() == null || costPriceQueryProductVo.getPage() <= 0){
            pageInfo.setPageNum(1);
        }else{
            pageInfo.setPageNum(costPriceQueryProductVo.getPage());
        }
        if(costPriceQueryProductVo.getRows() == null || costPriceQueryProductVo.getRows() <= 0){
            pageInfo.setPageSize(50);
        }else{
            pageInfo.setPageSize(costPriceQueryProductVo.getRows());
        }
        String modelJson = request.getHeader("commonRequestModel");
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                queryProductDto.setChainOrganSign(organSign);
                queryProductDto.setOrganSign(model.getHeadquartersOrganSign());
            }
        }
        ResultVO resultVO =  productCostPriceApi.selectProductsForUpperLower(queryProductDto,pageInfo);
        PageInfo pageProduct = (PageInfo) resultVO.getResult();
        List<CostPriceQueryProductResultDto> dtos = pageProduct.getList();
        List<CostPriceQueryProductResultVo> vos = new ArrayList<>();
        if(!CollectionUtils.isEmpty(dtos)){
            dtos.stream().forEach(dto ->{
                CostPriceQueryProductResultVo vo = new CostPriceQueryProductResultVo();
                BeanUtils.copyProperties(dto,vo);
                vo.setProductPref(dto.getPref());//商品内码
                vo.setAdjustmentRatio(String.valueOf(dto.getAdjustmentRatio()));//调价比例转换成字符串
                if(dto.getOldCostPrice() != null){//原成本价判空
                    vo.setOldCostPrice(String.valueOf(dto.getOldCostPrice()));//原成本价
                }
                if(dto.getPriceUpper() != null){
                    vo.setPriceUpper(String.valueOf(dto.getPriceUpper()));
                }
                if(dto.getPriceLower() != null){
                    vo.setPriceLower(String.valueOf(dto.getPriceLower()));
                }
                vo.setProductPref(dto.getPref());
                if(dto.getTaxCost() != null){//采购含税价判空处理
                    vo.setTaxCost(String.valueOf(dto.getTaxCost()));//采购含税价
                }
                vos.add(vo);
            });
        }
        pageProduct.setList(vos);
        logger.info("selectProductsForAdd result:{}",JSON.toJSONString(pageProduct));
        return new ResponseEntity(new ResultVO<>(pageProduct),  HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> cacheAddProducts(HttpServletRequest request, @RequestBody CostPriceDetailWrapperDto costPriceDetailWrapperDto) {
        logger.info("cacheAddProducts para:{}",costPriceDetailWrapperDto);
        String userId = request.getHeader("employeeId");
        String organSign = request.getHeader("organSign");//获取当前机构号
        ResultVO resultVO = productCostPriceApi.cacheAddProducts(costPriceDetailWrapperDto,userId,organSign);
        return new ResponseEntity(resultVO,  HttpStatus.OK);

    }

    @Override
    public ResponseEntity<ResultVO> cacheEditProducts(HttpServletRequest request,  @RequestBody CostPriceDetailWrapperDto costPriceDetailWrapperDto) {
        logger.info("cacheEditProducts para:{}",costPriceDetailWrapperDto);
        String userId = request.getHeader("employeeId");
        String organSign = request.getHeader("organSign");//获取当前机构号
        ResultVO resultVO = productCostPriceApi.cacheEditProducts(costPriceDetailWrapperDto,userId,organSign);
        return new ResponseEntity(resultVO,  HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> cacheDeleteProducts(HttpServletRequest request,  @RequestBody CostPriceDetailWrapperDto costPriceDetailWrapperDto) {
        logger.info("cacheDeleteProducts para:{}",costPriceDetailWrapperDto);
        String userId = request.getHeader("employeeId");
        String organSign = request.getHeader("organSign");//获取当前机构号
        ResultVO resultVO = productCostPriceApi.cacheDeleteProducts(costPriceDetailWrapperDto,userId,organSign);
        return new ResponseEntity(resultVO,  HttpStatus.OK);


    }

    @Override
    public ResponseEntity<ResultVO> clearCache(HttpServletRequest request) {
        String userId = request.getHeader("employeeId");
        String organSign = request.getHeader("organSign");
        logger.info("clearCache para:{}",userId);
        ResultVO resultVO = productCostPriceApi.clearDetailCache(userId);
        productCostPriceApi.clearAdjustPref(organSign,userId);
        return new ResponseEntity(resultVO,  HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getCostAdjustInfo(HttpServletRequest request,  @RequestBody AdjustPriceInfoVo adjustPriceInfoVo) {
        logger.info("getCostAdjustInfo para:{}",JSONObject.toJSONString(adjustPriceInfoVo));
        String userId = request.getHeader("employeeId");
        String organSign = request.getHeader("organSign");//获取当前机构号
        return new ResponseEntity(productCostPriceApi.getCostAdjustInfoBypref(adjustPriceInfoVo.getPref()),  HttpStatus.OK);

    }

    @Override
    public ResponseEntity<ResultVO> selectCostAdjustProductListWithCache(HttpServletRequest request, @RequestBody CostPriceAdjustDetailQueyDto costPriceAdjustDetailQueyDto) {
        logger.info("selectCostAdjustProductListWithCache para:{}",JSONObject.toJSONString(costPriceAdjustDetailQueyDto));
        String userId = request.getHeader("employeeId");
        String organSign = request.getHeader("organSign");//获取当前机构号
        costPriceAdjustDetailQueyDto.setOperationUser(userId);
        costPriceAdjustDetailQueyDto.setHeaderOrganSign(organSign);
        ResultVO resultVO = productCostPriceApi.pageQueryAjustDetailWithCache(costPriceAdjustDetailQueyDto);
        return new ResponseEntity(resultVO,  HttpStatus.OK);

    }

    @Override
    public ResponseEntity<ResultVO> submitAdjustByCache(HttpServletRequest request, @RequestBody CostPriceDto costPriceDto) {
        logger.info("submitAdjustByCache para:{}",JSONObject.toJSONString(costPriceDto));

        String userId = request.getHeader("employeeId");
        String organSign = request.getHeader("organSign");//获取当前机构号
        costPriceDto.setOrganSign(organSign);


        costPriceDto.setSaveStatus(CostPriceDto.SAVE_STATUS_FORMAL);
        ResultVO resultVO = productCostPriceApi.submitAdjustByCache(costPriceDto,userId);
        productCostPriceApi.clearAdjustPref(organSign,userId);
        return new ResponseEntity(resultVO,  HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> temporaryStorageAdjustByCache(HttpServletRequest request, @RequestBody CostPriceDto costPriceDto) {
        logger.info("temporaryStorageAdjustByCache para:{}",JSONObject.toJSONString(costPriceDto));
        String userId = request.getHeader("employeeId");
        String organSign = request.getHeader("organSign");//获取当前机构号
        costPriceDto.setOrganSign(organSign);

        costPriceDto.setSaveStatus(CostPriceDto.SAVE_STATUS_TEMP);
        ResultVO resultVO = productCostPriceApi.submitAdjustByCache(costPriceDto,userId);
        productCostPriceApi.clearAdjustPref(organSign,userId);
        return new ResponseEntity(resultVO,  HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getProductPriceInfosByPrefs(HttpServletRequest request, @RequestBody CostPriceQueryProductDto costPriceQueryProductDto) {
        logger.info("getProductPriceInfosByPrefs para:{}",JSONObject.toJSONString(costPriceQueryProductDto));
        if(StringUtil.isEmpty(costPriceQueryProductDto.getChainOrganSign())){
            return new ResponseEntity(ResultVO.createError("购货单位机构号不能为空"),  HttpStatus.OK);
        }
        return new ResponseEntity(productCostPriceApi.getProductPriceInfosByPrefs(costPriceQueryProductDto),  HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getCurOrganIsZhiLu(HttpServletRequest request) {
        String organSign = request.getHeader("organSign");
        logger.info("getCurOrganIsZhiLu para:{}",organSign);
        return new ResponseEntity(productCostPriceApi.getOrganIsZhiLu(organSign),  HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getCurOrganValueTypes(HttpServletRequest request) {
        String organSign = request.getHeader("organSign");
        logger.info("getCurOrganValueTypes para:{}",organSign);
        return new ResponseEntity(productCostPriceApi.getValueTypesByOrgan(organSign),  HttpStatus.OK);
    }


    @Override
    public ResponseEntity<ResultVO> checkImportTaskStatus(HttpServletRequest request,  @RequestBody  CheckTaskStatusQueryVo checkTaskStatusQueryVo) {
        logger.info("checkImportTaskStatus para:{}",JSONObject.toJSONString(checkTaskStatusQueryVo));
        if(StringUtils.isEmpty(checkTaskStatusQueryVo.getTaskNo())){
            return new ResponseEntity(ResultVO.createError("taskNo 不能为空"),HttpStatus.OK);
        }
        String userId = request.getHeader("employeeId");
        String organSign = request.getHeader("organSign");//获取当前机构号
        ResultVO resultVO = productCostPriceApi.checkImportTaskStatus(checkTaskStatusQueryVo.getTaskNo());
        return new ResponseEntity(resultVO,  HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> cacheAdjustPref(HttpServletRequest request, @RequestBody CostPriceDetailWrapperDto costPriceDetailWrapperDto) {
        logger.info("cacheAdjustPref param:{}",JSON.toJSONString(costPriceDetailWrapperDto));
        String organSign = request.getHeader("organSign");//获取当前机构号
        String userId = request.getHeader("employeeId");
        ResultVO resultVO = productCostPriceApi.cacheAdjustPref(costPriceDetailWrapperDto,organSign,userId);
        return new ResponseEntity(resultVO,HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getCacheAdjustPref(HttpServletRequest request) {
        String organSign = request.getHeader("organSign");//获取当前机构号
        String userId = request.getHeader("employeeId");
        ResultVO resultVO = productCostPriceApi.getCacheAdjustPref(organSign,userId);
        return new ResponseEntity(resultVO,HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> submitImportedAdjust(HttpServletRequest request,  @RequestBody  CostPriceDto costPriceDto) {
        logger.info("submitImportedAdjust para:{}",JSONObject.toJSONString(costPriceDto));
        String userId = request.getHeader("employeeId");
        String organSign = request.getHeader("organSign");//获取当前机构号
        costPriceDto.setOrganSign(organSign);
        ResultVO resultVO = productCostPriceApi.submitImportedAdjust(costPriceDto ,userId);
        return new ResponseEntity(resultVO,  HttpStatus.OK);
    }


}
