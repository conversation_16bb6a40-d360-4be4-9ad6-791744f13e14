package com.xyy.saas.web.api.module.member.controller;


import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.util.StringUtil;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.member.core.api.*;
import com.xyy.saas.member.core.dto.MemberBaseDto;
import com.xyy.saas.member.core.dto.MemberSmsRecordDto;
import com.xyy.saas.member.core.dto.MemberSmsRelationDto;
import com.xyy.saas.web.api.module.member.model.MemberSmsRecordVo;
import com.xyy.saas.web.api.module.member.model.MemberSmsRelationVo;
import com.xyy.saas.web.api.module.member.utils.BadWordUtil;
import com.xyy.saas.web.api.module.member.utils.ToolUtil;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.dto.result.EmployeeDto;
import com.xyy.user.module.dto.result.QueryDrugstoreDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-08-27T15:28:06.377+08:00")

@Controller
@RequestMapping("/member/memberSmsRecord/zb")
@Api(value = "memberSmsRecord", description = "the memberSmsRecord API")
public class MemberSmsRecordForZBApiController {

    private static final Logger logger = LogManager.getLogger(MemberSmsRecordForZBApiController.class);

    @Reference(version = "0.0.1")
    private MemberSmsRecordForZBApi memberSmsRecordForZBApi;

//    @Reference(version = "0.0.1", url="dubbo://localhost:20848")
    @Reference(version = "0.0.1")
    private MemberSmsRelationApi memberSmsRelationApi;
    @Reference(version = "0.0.1")
    public DrugstoreApi drugstoreApi;

    @Reference(version = "0.0.1")
    private MemberSmsApi memberSmsApi;
    @Reference(version = "0.0.1")
    public EmployeeApi employeeApi;
    @Reference(version = "0.0.1")
    public MemberBaseApi memberBaseApi;
    @Reference(version = "0.0.1")
    private MemberSmsTemplateApi memberSmsTemplateApi;


    @ApiOperation(value = "短信记录", notes = "短信记录", response = MemberSmsRecordVo.class, tags={ "memberSmsRecord", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberSmsRecordVo.class) })
    @RequestMapping(value = "/getMemberSmsRecordList",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getMemberSmsRecordList(@RequestHeader(name = "organSign", required = true) String organSign,
                                                           @RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                           @RequestBody MemberSmsRecordVo smsRecordVo){
        CommonRequestModel commonRequestModel = com.alibaba.fastjson.JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        MemberSmsRecordDto smsRecordDto = new MemberSmsRecordDto();
        BeanUtils.copyProperties(smsRecordVo, smsRecordDto);
        if(commonRequestModel.getOrganSignType() == 1){
            smsRecordDto.setOrganSign(commonRequestModel.getOrganSign());
        }else{
            smsRecordDto.setHeadquartersOrganSign(commonRequestModel.getOrganSign());
        }
        smsRecordDto.setPageNum(smsRecordDto.getPageNum() == null ? 1 : smsRecordDto.getPageNum());
        smsRecordDto.setPageSize(smsRecordDto.getPageSize() == null ? 10 : smsRecordDto.getPageSize());
        if(!StringUtil.isEmpty(smsRecordVo.getStartDate())) {
            smsRecordDto.setStartDate(smsRecordVo.getStartDate() + " 00:00:00");
        }
        if(!StringUtil.isEmpty(smsRecordVo.getEndDate())) {
            smsRecordDto.setEndDate(smsRecordVo.getEndDate() + " 23:59:59");
        }
        return new ResponseEntity(ResultVO.createSuccess(memberSmsRecordForZBApi.getMemberSmsRecordList(smsRecordDto)), HttpStatus.OK);
    }


    @ApiOperation(value = "跳转到短信详情", notes = "跳转到短信详情", response = MemberSmsRecordVo.class, tags={ "memberSmsRecord", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberSmsRecordVo.class) })
    @RequestMapping(value = "/toMemberSmsRecord",  method = RequestMethod.POST)
    public ResponseEntity<ResultVO> toMemberSmsRecord(@RequestHeader(name = "organSign", required = true) String organSign,
                                                      @RequestBody MemberSmsRecordVo vo){
        MemberSmsRecordDto recordDto = new MemberSmsRecordDto();
        BeanUtils.copyProperties(vo,recordDto);
        return new ResponseEntity(ResultVO.createSuccess(memberSmsRecordForZBApi.getMemberSmsRecordBy(recordDto)), HttpStatus.OK);
    }


    @ApiOperation(value = "短信详情通知对象", notes = "短信详情通知对象", response = MemberBaseDto.class, tags={ "memberSmsRecord", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberBaseDto.class) })
    @RequestMapping(value = "/getRecordMemberBaseList",  method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getRecordMemberBaseList(@RequestHeader(name = "organSign", required = true) String organSign,
                                                            @RequestBody MemberSmsRelationVo smsRelationVo){
        MemberSmsRelationDto relationDto = new MemberSmsRelationDto();
        BeanUtils.copyProperties(smsRelationVo,relationDto);
        relationDto.setOrganSign(organSign);
        relationDto.setPage(relationDto.getPage() == null ? 1 : relationDto.getPage());
        relationDto.setRows(relationDto.getRows() == null ? 10 : relationDto.getRows());
        return new ResponseEntity(ResultVO.createSuccess(memberSmsRelationApi.getMemberBaseBySmsRecordIdList(relationDto)), HttpStatus.OK);
    }

    @ApiOperation(value = "短信发送", notes = "短信发送", response = MemberBaseDto.class, tags={ "memberSmsRecord", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberBaseDto.class) })
    @RequestMapping(value = "/sendSMS",  method = RequestMethod.POST)
    public ResponseEntity<ResultVO> sendSMS(@RequestHeader(name = "organSign", required = true) String organSign,
                                            @RequestHeader(name = "employeeId", required = true) Integer employeeId,
                                            @RequestHeader("commonRequestModel") String commonRequestModelStr,
                                            @RequestBody MemberSmsRecordVo smsRecordVo){
        CommonRequestModel commonRequestModel = com.alibaba.fastjson.JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String headquartersOrganSign = "";
        if(commonRequestModel.getBizModel() == 1){
            //单体
            headquartersOrganSign = commonRequestModel.getOrganSign();
        }else{
            if(commonRequestModel.getOrganSignType() == 1){
                //门店
                headquartersOrganSign = commonRequestModel.getHeadquartersOrganSign();
            }else{
                //总部, 总部的headquartersOrganSign是空的,所以取自己的机构号就可以了
                headquartersOrganSign = commonRequestModel.getOrganSign();
            }
        }

        //验证短信验证码
        ResultVO checkResu = memberSmsRecordForZBApi.checkVcode(organSign,smsRecordVo.getVcode());
        if(Objects.nonNull(checkResu.getResult())){
            return new ResponseEntity(checkResu, HttpStatus.OK);
        }
        if(StringUtils.isEmpty(smsRecordVo.getSignature())){
        	return new ResponseEntity(new ResultVO(1, "短信签名为空", false),HttpStatus.OK);
        }
        if(StringUtils.isEmpty(smsRecordVo.getContent())){
            return new ResponseEntity(new ResultVO(1, "短信内容为空", false),HttpStatus.OK);
        }
//        if(StringUtils.isEmpty(smsRecordVo.getContentEnd())){
//        	return new ResponseEntity(new ResultVO(1, "短信结尾为空", false),HttpStatus.OK);
//        }
        //短信签名长度超出限制
        if(smsRecordVo.getSignature().length() > 10 ? true : false){
            return new ResponseEntity(new ResultVO(1, "短信签名超长", false),HttpStatus.OK);
        }
        //短信内容长度超出限制
        if(ToolUtil.checkTemplateLength(smsRecordVo.getContent())){
            return new ResponseEntity(new ResultVO(1, "短信内容超长", false),HttpStatus.OK);
        }
        MemberSmsRecordDto recordDto = new MemberSmsRecordDto();
        BeanUtils.copyProperties(smsRecordVo,recordDto);
        recordDto.setOrganSign(organSign);
        ResultVO<QueryDrugstoreDto> result  = drugstoreApi.queryDrugstoreByOrganSign(organSign);
        if(0 != result.getCode()){
            return new ResponseEntity(new ResultVO(1, result.getMsg(), false), HttpStatus.OK);
        }
        ResultVO<EmployeeDto> resultVO= employeeApi.queryEmployeeById(employeeId);
        if(Objects.nonNull(resultVO) && Objects.nonNull(resultVO.getResult())){
            recordDto.setCreateUser(((EmployeeDto)resultVO.getResult()).getName());
        }else{
            return new ResponseEntity(new ResultVO(-1, "用户不存在", false), HttpStatus.OK);
        }
        recordDto.setOrganName(result.getResult().getDrugstoreName());
        return new ResponseEntity(ResultVO.createSuccess(memberSmsRecordForZBApi.sendSMSToUser(recordDto,smsRecordVo.getType(),headquartersOrganSign)), HttpStatus.OK);
    }


    @ApiOperation(value = "检查剩余条数是否满足", notes = "检查剩余条数是否满足", response = MemberBaseDto.class, tags={ "memberSmsRecord", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberBaseDto.class) })
    @RequestMapping(value = "/checkCount",  method = RequestMethod.POST)
    public ResponseEntity<ResultVO> checkCount(@RequestHeader(name = "organSign", required = true) String organSign,
                                               @RequestHeader(name = "employeeId", required = true) Integer employeeId,
                                               @RequestBody MemberSmsRecordVo smsRecordVo){
        MemberSmsRecordDto recordDto = new MemberSmsRecordDto();
        BeanUtils.copyProperties(smsRecordVo,recordDto);
        recordDto.setOrganSign(organSign);
        return new ResponseEntity(ResultVO.createSuccess(memberSmsRecordForZBApi.checkSmsContent(recordDto,smsRecordVo.getType(),2)), HttpStatus.OK);
    }



    @ApiOperation(value = "获取短信验证码", notes = "获取短信验证码", response = MemberBaseDto.class, tags={ "memberSmsRecord", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberBaseDto.class) })
    @RequestMapping(value = "/getVcode",  method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getVcode(@RequestHeader(name = "organSign", required = true) String organSign){
        return new ResponseEntity(memberSmsRecordForZBApi.createVcode(organSign),HttpStatus.OK);
    }


    @ApiOperation(value = "发送选择会员(会员列表)", notes = "发送选择会员（会员列表）", response = MemberBaseDto.class, tags={ "memberSmsRecord", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberBaseDto.class) })
    @RequestMapping(value = "/getMemberBaseList",  method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getMemberBaseList(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                      @RequestBody MemberSmsRecordDto recordDto){
        CommonRequestModel commonRequestModel = com.alibaba.fastjson.JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        //总部的 sourceDrugstoreType 单独处理
        if(StringUtil.isEmpty(recordDto.getOrganSign())) {
            //前端有传入OrganSign, 说明要查全部, sourceDrugstoreType为空标识查询全部
            recordDto.setOrganSign(commonRequestModel.getOrganSign());
        }else{
            //前端有传入OrganSign, 说明要查单店
            recordDto.setSourceDrugstoreType(2);
        }
        logger.info("getMemberBaseList请求参数:"+JSONObject.fromObject(recordDto).toString());
        return new ResponseEntity(ResultVO.createSuccess(memberSmsRelationApi.getMemberBaseByParamList(recordDto)), HttpStatus.OK);
    }


    @ApiOperation(value = "机构下所有会员条数", notes = "机构下所有会员条数", response = JSONObject.class, tags={ "memberSmsRecord", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = JSONObject.class) })
    @RequestMapping(value = "/getMemberBaseCount",  method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getMemberBaseCount(@RequestHeader(name = "organSign", required = true) String organSign){
        MemberBaseDto memberBaseDto = new MemberBaseDto();
        memberBaseDto.setOrgansign(organSign);
        memberBaseDto.setYn(1);
        memberBaseDto.setState(1);//1代表启用
        return new ResponseEntity(ResultVO.createSuccess(memberBaseApi.getSMSMemberCountByOrgansignByCondation(memberBaseDto)), HttpStatus.OK);
    }

    @ApiOperation(value = "铭感词检查", notes = "铭感词检查", response = MemberBaseDto.class, tags={ "memberSmsRecord", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberBaseDto.class) })
    @RequestMapping(value = "/checkContent",  method = RequestMethod.POST)
    public ResponseEntity<ResultVO> checkContent(@RequestHeader(name = "organSign", required = true) String organSign,
                                                 @RequestBody MemberSmsRecordVo smsRecordVo){
        if(StringUtils.isBlank(smsRecordVo.getContent())){
            return  new ResponseEntity(new ResultVO(ResultCodeEnum.ERROR,"检查内容为空！"),HttpStatus.OK);
        }

        Map result = memberSmsTemplateApi.checkTemplateContent(smsRecordVo.getContent());
        
        if((Boolean) result.get("status")){
            return  new ResponseEntity(new ResultVO(ResultCodeEnum.SUCCESS, result),HttpStatus.OK);
        }else{
            return new ResponseEntity(new ResultVO(ResultCodeEnum.SUCCESS, "", true),HttpStatus.OK);
        }
    }


    /**
     * 查询短信信息
     * @param organSign
     * @return
     */
    @ApiOperation(value = "查询短信信息", notes = "查询短信信息", response = MemberBaseDto.class, tags={ "memberSmsRecharge", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberBaseDto.class) })
    @RequestMapping(value = "/getMemberSms",  method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getMemberSms(@RequestHeader("commonRequestModel") String commonRequestModelStr){
        CommonRequestModel commonRequestModel = com.alibaba.fastjson.JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String headquartersOrganSign = commonRequestModel.getHeadquartersOrganSign();
        if(StringUtil.isEmpty(headquartersOrganSign)){
            headquartersOrganSign = commonRequestModel.getOrganSign();
        }
        return new ResponseEntity(ResultVO.createSuccess(memberSmsApi.getMemberSmsDtoByOrganSign(headquartersOrganSign)), HttpStatus.OK);
    }

}
