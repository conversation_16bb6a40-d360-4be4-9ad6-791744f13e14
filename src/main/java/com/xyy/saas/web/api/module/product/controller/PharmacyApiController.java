package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.member.core.api.MemberChronicProductApi;
import com.xyy.saas.member.core.dto.MemberChronicProductDto;
import com.xyy.saas.pharmacy.core.api.SaasPharmacyApi;
import com.xyy.saas.pharmacy.core.dto.*;
import com.xyy.saas.product.core.dto.ProductDto;
import com.xyy.saas.product.core.dto.ResultCodeEnum;
import com.xyy.saas.product.core.dto.StandardLibaryProductVoDto;
import com.xyy.saas.web.api.module.product.model.*;
import com.xyy.saas.web.api.module.product.util.CollateDataUtil;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.dto.result.EmployeeDto;
import com.xyy.user.module.enums.DrugstoreBizModelEnum;
import com.xyy.user.module.enums.DrugstoreTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-10-21T10:11:31.623+08:00")

@Controller
public class PharmacyApiController implements PharmacyApi {

    @Reference(version = "0.0.1")
    private SaasPharmacyApi saasPharmacyApi;

    @Reference(version = "0.0.1")
    private EmployeeApi employeeApi;

    @Reference(version = "0.0.1")
    private com.xyy.saas.product.core.api.ProductApi productApi;

    @Reference(version = "0.0.1")
    private MemberChronicProductApi memberChronicProductApi;

    private static final Logger logger = LoggerFactory.getLogger(PharmacyApiController.class);

    @Override
    @ResponseBody
    public ResponseEntity<ResultVO> savePharmacyConsult(HttpServletRequest request, @RequestBody PharmacyConsultSaveVo pharmacyConsultSaveVo) {
        logger.info("新增药学咨询记录：传入参数为===>>>" + JSONObject.toJSONString(pharmacyConsultSaveVo));
        if (pharmacyConsultSaveVo == null || StringUtils.isBlank(pharmacyConsultSaveVo.getUserName())
                || pharmacyConsultSaveVo.getConsultTime() == null) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.PRODUCT_REQUIRED_PARAMS_NULL), HttpStatus.SERVICE_UNAVAILABLE);
        }
        PharmacyConsultSaveDto dto = new PharmacyConsultSaveDto();
        BeanUtils.copyProperties(pharmacyConsultSaveVo, dto);
        String organSign = request.getHeader("organSign");
        String username = request.getHeader("userName");
        dto.setOrganSign(organSign);
        dto.setCreateUser(username);
        String guid = saasPharmacyApi.savePharmacy(dto, pharmacyConsultSaveVo.getIds());
        return new ResponseEntity<ResultVO>(new ResultVO(guid), HttpStatus.OK);

    }

    @Override
    public ResponseEntity<ResultVO> incompatibility(HttpServletRequest request, @RequestBody IncompatibilityVo vo) {
        IncompatibilityDto dto = new IncompatibilityDto();
        BeanUtils.copyProperties(vo, dto);
        String organSign = request.getHeader("organSign");
        dto.setOrganSign(organSign);
        //连锁门店获取总部机构号 需要获取总部的机构号
        String headOrganSign = organSign;
        String modelJson = request.getHeader("commonRequestModel");
        if(!org.springframework.util.StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                headOrganSign = model.getHeadquartersOrganSign();
            }
        }
        dto.setHeadOrganSign(headOrganSign);
        ResultVO result = saasPharmacyApi.incompatibility(dto);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> addDrugList(@RequestBody AddDrugListVo vo, HttpServletRequest request) {
        logger.info("添加药品标准库列表：传入参数为===>>>" + JSONObject.toJSONString(vo));
        if (vo.getType() == null) {
            vo.setType(0);
        }
        if (vo.getPage() == null) {
            vo.setPage(1);
        }
        if (vo.getRows() == null || vo.getRows() == 0) {
            vo.setRows(10);
        }
        AddDrugListDto drugListDto = new AddDrugListDto();
        BeanUtils.copyProperties(vo, drugListDto);
        ResultVO result = new ResultVO();
        if (vo.getType() == 0) {
            result = saasPharmacyApi.addDrugList(drugListDto);
        } else {
            String organSign = request.getHeader("organSign");
            //如果是连锁门店，需要获取总部的机构号
            String modelJson = request.getHeader("commonRequestModel");
            Byte bizModel = null;
            if(!org.springframework.util.StringUtils.isEmpty(modelJson)){
                CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
                bizModel = model.getBizModel();
                if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                    organSign = model.getHeadquartersOrganSign();
                }
            }
            MemberChronicProductDto memberChronicProductDto = new MemberChronicProductDto();
            memberChronicProductDto.setOrganSign(organSign);
            memberChronicProductDto.setPageNum(vo.getPage());//当前页码
            memberChronicProductDto.setPageSize(vo.getRows());//每页显示多少
            memberChronicProductDto.setPref(vo.getSymptomPref());//适应病种编号
            memberChronicProductDto.setMixedQuery(vo.getCommonName());//混合查询字段
            ResultVO<PageInfo<MemberChronicProductDto>> chronicProducts = memberChronicProductApi.queryChronicProducts(memberChronicProductDto);
            logger.info("memberChronicProductApi.queryChronicProducts,params:{},result:{}",JSON.toJSONString(memberChronicProductDto),JSON.toJSONString(chronicProducts));
            PageInfo chronicProductsPageinfo = chronicProducts.getResult();
            List<MemberChronicProductDto> chronicProductslist = chronicProductsPageinfo.getList();
            List<StandardLibaryProductVoDto> chronicProductsResult = new ArrayList<>();
            if(!CollectionUtils.isEmpty(chronicProductslist)){
                List<String> prefs = new ArrayList<>();
                chronicProductslist.stream().forEach(chronic ->{
                    prefs.add(chronic.getProductPref());
                });
                List<ProductDto> productDtos = productApi.getProductsByPrefsAndOrganSign(prefs, organSign);
                Map<String,ProductDto> productDtoHashMap = new HashMap<>();
                if(!CollectionUtils.isEmpty(productDtos)){
                    productDtos.stream().forEach(productDto -> {
                        productDtoHashMap.put(productDto.getPref(),productDto);
                    });
                }
                for(MemberChronicProductDto mcpdto:chronicProductslist){
                    StandardLibaryProductVoDto svdto = new StandardLibaryProductVoDto();
                    //标准库id
                    svdto.setStandardLibraryId(productDtoHashMap.get(mcpdto.getProductPref()) != null?productDtoHashMap.get(mcpdto.getProductPref()).getStandardLibraryId():null);
                    svdto.setCommonName(mcpdto.getCommonName());//通用名称
                    svdto.setProductName(mcpdto.getProductName());//商品名称
                    svdto.setDosageFormId(mcpdto.getDosageFormName());//剂型名称
                    svdto.setManufacturer(mcpdto.getManufacturer());//生产厂商
                    svdto.setApprovalNumber(mcpdto.getApprovalNumber());//批准文号
                    svdto.setPharmacyPref(mcpdto.getPharmacyPref());//商品编号
                    svdto.setUnitName(mcpdto.getUnitName());//单位
                    svdto.setChronicName(mcpdto.getChronicName());//病种名称
                    svdto.setAttributeSpecification(mcpdto.getAttributeSpecification());//规格
                    if(svdto.getStandardLibraryId() != null && svdto.getStandardLibraryId() > 0){
                        AddDrugListDto drugListDtoParams = new AddDrugListDto();
                        drugListDtoParams.setProductId(String.valueOf(svdto.getStandardLibraryId()));
                        drugListDtoParams.setPage(1);
                        drugListDtoParams.setRows(10);
                        drugListDtoParams.setOrganSign(organSign);
                        result = saasPharmacyApi.addDrugList(drugListDtoParams);
                        PageInfo<StandardLibaryProductVoDto> libaryProductVoDtoPage = (PageInfo<StandardLibaryProductVoDto>) result.getResult();
                        if(!CollectionUtils.isEmpty(libaryProductVoDtoPage.getList())){
                            StandardLibaryProductVoDto dto1 = libaryProductVoDtoPage.getList().get(0);
                            svdto.setStandardCode(dto1.getStandardCode());//本位码
                            svdto.setIndication(dto1.getIndication());//适应症
                            svdto.setTaboos(dto1.getTaboos());//禁忌症
                            svdto.setStorageConsiderations("");//存储注意事项
                            svdto.setProductValidity("");//保质期
                            svdto.setStorageCondition(dto1.getStorageCondition());//存储条件
                            svdto.setIngredient(dto1.getIngredient());//药品成分
                            svdto.setUsageDosage(dto1.getUsageDosage());//用法用量
                            svdto.setAdverseReaction(dto1.getAdverseReaction());//不良反应
                            svdto.setPrecautions(dto1.getPrecautions());//注意事项
                        }
                    }
                    chronicProductsResult.add(svdto);
                }
                chronicProductsPageinfo.setList(chronicProductsResult);
            }
            System.out.println(JSON.toJSONString(chronicProducts));
            result.setResult(chronicProductsPageinfo);
        }
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> consultList(HttpServletRequest request, @RequestBody ConsultListVo vo) {
        logger.info("咨询记录列表：传入参数为===>>>" + JSONObject.toJSONString(vo));
        ConsultListDto dto = new ConsultListDto();
        BeanUtils.copyProperties(vo, dto);

        String organSign = request.getHeader("organSign");
        dto.setOrganSign(organSign);
        ResultVO result = saasPharmacyApi.consultList(dto);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> consultDrugDetails(HttpServletRequest request, @RequestBody ConsultDrugDetailsVo vo) {
        logger.info("咨询记录药品明细：传入参数为===>>>" + JSONObject.toJSONString(vo));
        if (vo == null || StringUtils.isBlank(vo.getGuid())) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.SUCCESS), HttpStatus.OK);
        }

        ConsultDrugDetailsDto dto = new ConsultDrugDetailsDto();
        BeanUtils.copyProperties(vo, dto);

        ResultVO result = saasPharmacyApi.consultDetails(dto);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> consultUserDetails(HttpServletRequest request, @RequestBody ConsultUserDetailsVo vo) {
        logger.info("咨询记录患者信息明细：传入参数为===>>>" + JSONObject.toJSONString(vo));
        if (vo == null || StringUtils.isBlank(vo.getGuid())) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.PRODUCT_REQUIRED_PARAMS_NULL), HttpStatus.SERVICE_UNAVAILABLE);
        }

        ConsultUserDetailsDto dto = new ConsultUserDetailsDto();
        BeanUtils.copyProperties(vo, dto);

        ResultVO result = saasPharmacyApi.consultUserDetails(dto);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }


    @Override
    public ResponseEntity<ResultVO> queryAllEmployeeByOrganSign(HttpServletRequest request) {
        List<EmployeeDto> employeeDtos = employeeApi.queryAllEmployeeByOrganSign(request.getHeader("organSign"));

        return new ResponseEntity<ResultVO>(new ResultVO(employeeDtos), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> listAllEmployeeByOrganSign(HttpServletRequest request) {

        List<EmployeeListDto> employeeDtos = saasPharmacyApi.listAllEmployeeByOrganSign(request.getHeader("organSign"));


        return new ResponseEntity<ResultVO>(new ResultVO(employeeDtos), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> guidance(HttpServletRequest request, @RequestBody DirectionAndGuidanceVo vo) {
        DirectionAndGuidanceDto vo1 = saasPharmacyApi.guidance(vo.getStandardLibraryId());
        return new ResponseEntity<ResultVO>(new ResultVO(vo1), HttpStatus.OK);
    }

}
