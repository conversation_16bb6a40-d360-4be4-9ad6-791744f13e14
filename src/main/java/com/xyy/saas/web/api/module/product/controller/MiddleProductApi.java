package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.dto.ProductDto;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T18:13:39.358+08:00")
@RequestMapping("/product/middle")
@Api(value = "YbmlProduct", description = "中台商品相关API接口")
public interface MiddleProductApi {

    @ApiOperation(value = "纠错引用的中台商品", notes = "纠错引用的中台商品", response = ResultVO.class, tags={ "纠错引用的中台商品", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/addProductCorrection")
    ResponseEntity<ResultVO> addProductCorrection(HttpServletRequest request, @ApiParam(value = "纠错引用的中台商品请求参数", required = true) @RequestBody ProductDto productDto);

}
