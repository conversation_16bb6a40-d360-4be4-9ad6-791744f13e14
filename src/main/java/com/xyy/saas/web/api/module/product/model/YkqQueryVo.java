package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-07T14:34:49.141+08:00")

@ApiModel(description = "宜块钱查询参数类")
public class YkqQueryVo {

    @JsonProperty("prefs")
    private String prefs;

    @JsonProperty("organSign")
    private String organSign;

    @ApiModelProperty(value = "商品编号集合，之间以逗号（,）分隔")
    public String getPrefs() {
        return prefs;
    }

    public void setPrefs(String prefs) {
        this.prefs = prefs;
    }

    @ApiModelProperty(value = "药店机构唯一标识（机构号）")
    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }
}
