package com.xyy.saas.web.api.common.config.aop;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Event;
import com.google.common.base.Joiner;
import com.google.common.base.Stopwatch;
import com.xyy.saas.common.util.MD5Util;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.web.api.common.config.annotition.RepeatSubmitValidation;
import com.xyy.saas.web.api.common.constants.UserOperationConstans;
import com.xyy.saas.web.api.common.context.ActionContext;
import com.xyy.saas.web.api.common.context.ActionContextSupport;
import com.xyy.saas.web.api.module.utils.JedisUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * @desc 防止重复提交切面
 * <AUTHOR>
 * @date    20191120
 */
@Aspect
@Component
public class RepeatSubmitAspect {
    private static final Logger logger = LoggerFactory.getLogger(RepeatSubmitAspect.class);

    private  static final  String   REQUIRE_ID = "product:repeatSubmit:";
    private  static final   String   CAT_TYPE  = "RepeatSubmit";
    private  static final   String   CAT_ERROR_TYPE  = "RepeatSubmitError";

    @Autowired
    private JedisUtils jedisUtils;

    @Around("@annotation(repeatSubmitValidation)")
    public Object around(ProceedingJoinPoint pjp, RepeatSubmitValidation repeatSubmitValidation) throws Throwable {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        Object   object = null;
        Integer  userId =0;
        String   md5Key = "";
        try{
            ActionContext currentContext = null;
            try{
                //处理swagger访问接口
                currentContext = ActionContextSupport.getCurrent();
            }catch(Exception e){
                logger.warn("RepeatSubmitAspect currentContext is null,{}", request.getServletPath());
                object = pjp.proceed();
                Cat.logEvent(CAT_TYPE,this.getUrl(pjp), Event.SUCCESS,"");
                return object;
            }
            int  resultType = repeatSubmitValidation.resultType();
            md5Key = getKey(currentContext,repeatSubmitValidation);
//             默认2秒内同一用户同一个地址同一个参数，视为重复提交
            int flag = jedisUtils.setKeyAndExpireTime(md5Key,REQUIRE_ID,repeatSubmitValidation.pexpire());
            if(flag==0){
                logger.info("用户id:{}，url:{},操作频繁，请稍后再试",userId,currentContext.getUrl());
                Cat.logEvent(CAT_TYPE,this.getUrl(pjp), "1","");
               if(resultType == UserOperationConstans.RESULT_TYPE_INTEGER){
                    return UserOperationConstans.REPEAT_SUBMIT_CODE;
               }else if(resultType == UserOperationConstans.RESULT_TYPE_STRIGN){
                   return UserOperationConstans.REPEAT_SUBMIT_CODE_STRING;
               }else if(resultType == UserOperationConstans.RESULT_TYPE_RESULTVO){
                   return new ResultVO(UserOperationConstans.REPEAT_SUBMIT_CODE, "操作频繁，请稍后再试", "");
                } else {
                   return new ResponseEntity<ResultVO>(new ResultVO(UserOperationConstans.RESULT_TYPE_ResponseEntity_RESULTVO, "操作频繁，请稍后再试", ""), HttpStatus.OK);
               }
            }else{
                object = pjp.proceed();
                Cat.logEvent(CAT_TYPE,this.getUrl(pjp), Event.SUCCESS,"");
                return object;
            }
        }catch (Exception e){
            logger.error("校验重复提交请求异常",e);
            Cat.logEvent(CAT_ERROR_TYPE,this.getUrl(pjp));
            if(!StringUtils.isEmpty(md5Key)){
                boolean flag = jedisUtils.releaseDistributedLock(md5Key,REQUIRE_ID);
                if(flag){
                    logger.info("成功释放分布式锁");
                }
            }
        }
        return object;
    }

    private  String  getUrl(ProceedingJoinPoint pjp){
        Signature sig = null;
        MethodSignature msig = null;
        try {
            sig = pjp.getSignature();
            if (sig instanceof MethodSignature) {
                msig = (MethodSignature)sig;
                Object target = pjp.getTarget();
                Method currentMethod = target.getClass().getMethod(msig.getName(), msig.getParameterTypes());
                String className = currentMethod.getDeclaringClass().getName();
                String methodName = currentMethod.getName();
                return  className + "." + methodName;
            }
        } catch (Exception e) {
            logger.error("aop 获取类名异常",e);
        }
        return "FailRepeatSubmit";
    }

    /**
     * 生成分布式锁缓存key  由md5(系统唯一前缀，员工id,url,业务参数data,client,ip)字段组成key
     * @param currentContext
     * @param repeatSubmitValidation
     * @return
     */
    private String getKey(ActionContext currentContext,RepeatSubmitValidation repeatSubmitValidation){
        //员工id可能为空
        Object employeeIdObj = currentContext.getContext(ActionContextSupport.CURRENT_USER_Id_KEY);
        Integer userId = Integer.valueOf(employeeIdObj!=null?employeeIdObj.toString():"0");
        List<Object> list = new ArrayList<>();
        list.add(userId);
        list.add(currentContext.getUrl());
        list.add(JSONObject.toJSONString(currentContext.getData()));
        list.add(currentContext.getClient());
        list.add(currentContext.getIp());
        String sourceKey = Joiner.on(":").useForNull("").join(list);
        Stopwatch stopwatch = Stopwatch.createStarted();
        String md5Key = REQUIRE_ID+MD5Util.getMD5(sourceKey);
        String times = stopwatch.stop().toString();
        logger.info("RepeatSubmitAspect create md5 key time:{},md5Key:{},sourceKey:{}",times,md5Key,sourceKey);
        return md5Key;
    }
}
