package com.xyy.saas.web.api.common.interceptor;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.base.Strings;
import com.xyy.cat.util.CatUtil;
import com.xyy.common.handler.GrayUserCheckHandler;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.web.api.common.base.GatewayException;
import com.xyy.saas.web.api.common.constants.CacheKeyPreConstant;
import com.xyy.saas.web.api.common.constants.ResultCodeMessage;
import com.xyy.saas.web.api.common.context.ActionContext;
import com.xyy.saas.web.api.common.context.ActionContextSupport;
import com.xyy.saas.web.api.common.enums.ClientEnum;
import com.xyy.saas.web.api.common.filter.ParameterCheckServletRequestWrapper;
import com.xyy.saas.web.api.common.swagger2.SwaggerUrlFilterHelper;
import com.xyy.saas.web.api.module.utils.JedisUtils;
import com.xyy.saas.web.api.module.utils.JwtUtil;
import com.xyy.saas.web.api.module.utils.StringUtils;
import com.xyy.user.module.enums.DrugstoreTypeEnum;
import io.jsonwebtoken.Claims;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 登录校验拦截器
 * <AUTHOR>
 * @date 2019-08-06
 * @mondify
 * @copyright
 */
@Component
public class LoginInterceptor implements HandlerInterceptor {
    public static Logger logger = LoggerFactory.getLogger(LoginInterceptor.class);
    @Autowired
    private JedisUtils jedisUtil;
    /**
     * 无需认证的url请求
     * */
    @Value("#{'${gateway.no-need.url}'.split(',')}")
    private final List NO_NEED_AUTH_URL = null;
    @Autowired
    private GrayUserCheckHandler grayUserCheckHandler;

    /**
     * 无需过滤的url请求
     * */
    @Value("#{'${gateway.no-need-filter.url}'.split(',')}")
    private List NO_NEED_FILTER_URL = null;

    @Override
    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o) throws Exception {
        ResultVO result = new ResultVO();
        String url=httpServletRequest.getServletPath();
        //允许swagger页面或者通过swagger请求的接口
        if(SwaggerUrlFilterHelper.containsSwaggerUrl(httpServletRequest)){
            return true;
        }
        ActionContext current = ActionContextSupport.getCurrent();

        // 无需过滤的URL请求，暂时这样处理
        if (NO_NEED_FILTER_URL.contains(url)) {
            return true;
        }
        Transaction transaction = null;
        try{
            transaction = CatUtil.initTransaction("login.interce.url", url);
            // 当前登录是否携带用户ID
            CommonRequestModel model = new CommonRequestModel();
            Map<String, String> headerMap = new HashMap<>();
            if (ClientEnum.getExistsFilterToken(current.getClient())) {
                if (!NO_NEED_AUTH_URL.contains(url)) {

                    Claims claims = JwtUtil.parseJWT(current.getToken());
                    String key = (String) claims.get("key");
                    boolean exists = jedisUtil.exists(key);
                    transaction.addData("user_cache_key",key);
                    transaction.addData("user_token",current.getToken());
                    if (exists) {
                        // 为token续命
                        jedisUtil.set(key, ClientEnum.getClientTokenExpireSecond(current.getClient()));
                        //disabled 是否禁用，1是 0否 quitDrugstore 是否解绑 1是 0否
                        String[] fieldArray = {"employeeId","organSign","employeeName","identity","loginName","disabled","quitDrugstore","organSignType","bizModel","headquartersOrganSign"};
                        List<String> fieldList = jedisUtil.hmget(key, fieldArray);

                        String employeeId = fieldList.get(0);
                        String organSign = fieldList.get(1);
                        String employeeName = fieldList.get(2);
                        String identity = fieldList.get(3);
                        String loginName = fieldList.get(4);
                        String disabled = fieldList.get(5);
                        String quitDrugstore = fieldList.get(6);
                        String organSignType = fieldList.get(7);
                        String bizModel = fieldList.get(8);
                        String headquartersOrganSign = fieldList.get(9);

//                        过滤登录员工是否被禁用，是否解除绑定 user未定义枚举类，暂时使用数值
                        if((ClientEnum.IOS_CLIENT.getClient().equals(current.getClient()) || ClientEnum.ANDROID_CLIENT.getClient().equals(current.getClient()))
                                && ("1".equals(disabled) || "1".equals(quitDrugstore))){
                            result.setCode(ResultCodeMessage.USER_ACCOUNT_DISABLED_CODE);
                            result.setMsg(ResultCodeMessage.USER_ACCOUNT_DISABLED_MESSAGE);
                            current.setResponseResult(String.format("employee is disabled or quitDrugstore,employeeId:%s",employeeId));
                            httpServletResponse.getWriter().write(JSONUtils.obj2JSON(result));
                            return false;
                        }

                        model.setEmployeeId(employeeId != null ? employeeId : "");
                        model.setOrganSign(organSign != null ? organSign : "");
                        model.setIdentity(Strings.isNullOrEmpty(identity) ? 0 : Byte.valueOf(identity));
                        model.setEmployeeName(employeeName != null ? model.encodeContent(employeeName) : "");
                        model.setLoginName(loginName != null ? model.encodeContent(loginName) : "");
                        model.setBizModel(StringUtils.isNullOrEmpty(bizModel)?0:Byte.valueOf(bizModel));
                        model.setOrganSignType(StringUtils.isNullOrEmpty(organSignType)?0:Byte.valueOf(organSignType));
                        model.setHeadquartersOrganSign(DrugstoreTypeEnum.HEADQUARTERS.toEquals(model.getOrganSignType())?organSign:headquartersOrganSign);
                        headerMap.put("employeeId", employeeId);
                        headerMap.put("organSign", organSign);
                        current.setContext(ActionContextSupport.CURRENT_USER_Id_KEY, employeeId);
                        current.setContext(ActionContextSupport.CURRENT_ORGANSIGN_KEY, organSign);
                        transaction.addData("common_header", JSONUtils.obj2JSON(model));
                        //灰度检查
                        grayUserCheckHandler.setGrayState(httpServletRequest,httpServletResponse,organSign);
                    } else {
                        Cat.logMetricForCount(CacheKeyPreConstant.TOKEN_EXPIRE_COUNT);
                        CatUtil.errorCat(transaction, new GatewayException(ResultCodeMessage.TOKEN_EXPIRE_COUNT_MESSAGE));
                        logger.error(String.format("token expire cachekey:%s,url:%s,token:%s,params:%s",key,url,current.getToken(),current.getClientParams()));
                        current.setResponseResult(ResultCodeMessage.TOKEN_EXPIRE_COUNT_MESSAGE);
                        result.setCode(ResultCodeMessage.TOKEN_EXPIRE_COUNT_CODE);
                        result.setMsg(ResultCodeMessage.TOKEN_EXPIRE_COUNT_MESSAGE);
                        httpServletResponse.getWriter().write(JSONUtils.obj2JSON(result));
                        return false;
                    }
                }
            }
            model.setClient(current.getClient());
            model.setVersion(current.getVersion());
            model.setMac(current.getAppContext()!=null?current.getAppContext().getAppMac():"");
            model.setIp(current.getIp());
            current.setModel(model);
            headerMap.put("commonRequestModel", JSONUtils.obj2JSON(model));
            current.getHeaders().putAll(headerMap);
            modifyHeaders(headerMap, httpServletRequest);
        }catch(Exception e){
            CatUtil.errorCat(transaction, e);
            logger.error("LoginInterceptor error",e);
            result.setCode(ResultCodeMessage.USER_SECURITY_FAIL_CODE);
            result.setMsg(ResultCodeMessage.USER_SECURITY_FAIL_MESSAGE);
            current.setResponseResult("LoginInterceptor error"+e.getMessage());
            httpServletResponse.getWriter().write(JSONUtils.obj2JSON(result));
            return false;
        }
        if (transaction != null){
            CatUtil.successCat(transaction);
        }
        return true;
    }

    /**
     * 修改请求头信息
     * @param headerses
     * @param request
     */
    private void modifyHeaders(Map<String, String> headerses, HttpServletRequest request) {
        if (headerses == null || headerses.isEmpty()) {
            return;
        }
        Class<? extends HttpServletRequest> requestClass = request.getClass();
        try {
            ParameterCheckServletRequestWrapper requestWrapper = (ParameterCheckServletRequestWrapper)request;
            requestWrapper.addHeaders(headerses);
        } catch (Exception e) {
            logger.error("LoginInterceptor modifyHeaders error",e);
        }
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {
    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) throws Exception {
    }
}
