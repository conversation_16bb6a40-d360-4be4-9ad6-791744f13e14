package com.xyy.saas.web.api.module.product.controller;

//import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.inventory.core.api.InventoryLotNumberApi;
import com.xyy.saas.inventory.core.dto.InventoryLotNumberVo;
import com.xyy.saas.inventory.core.dto.InventoryOrganSignVo;
import com.xyy.saas.inventory.core.dto.InventoryOrganSignVoList;
import com.xyy.saas.product.core.dto.ResultCodeEnum;
import com.xyy.saas.rabbitmq.core.api.MessagePushApi;
import com.xyy.saas.web.api.module.product.model.InventoryVo;
import com.xyy.saas.web.api.module.product.util.CommonConsumerFallback;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-01-28T15:28:18.933+08:00")
@Controller
public class InventoryApiController implements InventoryApi {

    private static final Logger logger = LoggerFactory.getLogger(InventoryApiController.class);

    @Reference(version = "0.0.1")
    private com.xyy.saas.inventory.core.api.InventoryApi inventoryApi;
    @Reference(version = "0.0.1")
    private InventoryLotNumberApi lotNumberApi;
    @Reference(version = "0.0.1")
    private MessagePushApi messagePushApi;

    @Override
    public ResponseEntity<ResultVO> findAbsentInventory(@ApiParam(value = "商户编号", required = true) @RequestParam String organSign) {
        Integer resSum = 0;
        try {
            resSum = inventoryApi.findAbsentInventory(organSign, 1);
        } catch (Exception e) {
            logger.error("查询缺货数量遇到问题！", e);
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR), HttpStatus.SERVICE_UNAVAILABLE);
        }
        ResultVO<Integer> resultVO = new ResultVO();
        resultVO.setCode(ResultCodeEnum.SUCCESS.getCode());
        resultVO.setMsg(ResultCodeEnum.SUCCESS.getMsg());
        resultVO.setResult(resSum);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    @Override
    public String adjustInventory(InventoryVo inventoryVo) {
        com.xyy.saas.inventory.core.dto.InventoryVo  ivo = new com.xyy.saas.inventory.core.dto.InventoryVo();
        BeanUtils.copyProperties(inventoryVo,ivo);
        String back = inventoryApi.updateInventory(ivo);
        return back;
    }

    @Override
    public String adjustInventoryLotNumber(String pref, String organSign, BigDecimal stockNumbers) {
        InventoryLotNumberVo lotNumberVo = new InventoryLotNumberVo();
        lotNumberVo.setPref(pref);
        lotNumberVo.setOrgansign(organSign);
        lotNumberVo.setStockNumber(stockNumbers);
        String back = lotNumberApi.updateLotNumberSingle(lotNumberVo);
        return back;
    }

    @Override
    public String updateBatchInventoryAndLotNumbers(String stockPref, String organSign) {
        InventoryLotNumberVo lotNumberVo = new InventoryLotNumberVo();
        lotNumberVo.setStockPref(stockPref);
        lotNumberVo.setOrgansign(organSign);
        String back = lotNumberApi.updateByStockAndOrganSign(lotNumberVo);
        List<String> table = new ArrayList<>();
        table.add("saas_inventory");
        table.add("saas_inventory_lot_number");
        sendMessage(table,organSign);
        return back;
    }
//    @SentinelResource(fallbackClass = CommonConsumerFallback.class,fallback = "fallback",
//            blockHandlerClass = CommonConsumerFallback.class, blockHandler = "blockHandler")
    @Override
    public ResponseEntity<ResultVO> findAndCompareStockNumber(@ApiParam(value = "商品信息实体" ,required=true )@RequestBody String jsonVos) {
        logger.info("健康笔记入参:"+jsonVos);
        ResultVO<List<InventoryOrganSignVo>> resultVO = new ResultVO();
        if(StringUtils.isEmpty(jsonVos)){
            resultVO.setCode(ResultCodeEnum.ERROR.getCode());
            resultVO.setMsg("参数不能为空");
            resultVO.setResult(null);
            return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
        }
        InventoryOrganSignVoList dtoList = JSON.parseObject(jsonVos, new TypeReference<InventoryOrganSignVoList>() {
        });
        List<InventoryOrganSignVo> list = dtoList.getInventoryOrganSignDtos();
       /* List<InventoryOrganSignVo> paramVos = new ArrayList<>();
        for(InventoryOrganSignDto inventoryOrganSignDto : list){
            InventoryOrganSignVo inventoryOrganSignVo = new InventoryOrganSignVo();
            List<InventoryBaseDto> inventoryBaseDtos = inventoryOrganSignDto.getInventoryDetails();
            List<InventoryBaseVo> inventoryBaseVos = new ArrayList<>();
            for(InventoryBaseDto inventoryBaseDto : inventoryBaseDtos){
                InventoryBaseVo ibVo = new InventoryBaseVo();
                BeanUtils.copyProperties(inventoryBaseDto,ibVo);
                inventoryBaseVos.add(ibVo);
            }
            BeanUtils.copyProperties(inventoryOrganSignDto,inventoryOrganSignVo);
            paramVos.add(inventoryOrganSignVo);
        }*/
        List<InventoryOrganSignVo> inventoryOrganSignVos = inventoryApi.compareStockNumber(list);

        resultVO.setCode(ResultCodeEnum.SUCCESS.getCode());
        resultVO.setMsg(ResultCodeEnum.SUCCESS.getMsg());
        resultVO.setResult(inventoryOrganSignVos);
        logger.info("健康笔记调用出参,"+JSON.toJSONString(resultVO));
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    /**推送C#数据修改标识*/
    public void sendMessage(List<String> tableList, String organSign){
        JSONObject json = new JSONObject();
        String[] tables = new String[tableList.size()];
        for(int i=0;i<tableList.size();i++){
            tables[i] = tableList.get(i);
        }
        json.put("code","sync");
        json.put("tables",tables);
        try {
            messagePushApi.sendMsgByOrganSign(organSign,json.toJSONString());
        }catch (Exception e){
            logger.info("推送C#，表"+tables+"推送失败");
            e.printStackTrace();
        }

    }
}
