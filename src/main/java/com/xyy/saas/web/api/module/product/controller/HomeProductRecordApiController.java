package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xyy.ec.external.api.ProductdExternalApi;
import com.xyy.ec.external.dto.ProductInfoForSaaSDTO;
import com.xyy.ec.external.dto.ResultData;
import com.xyy.ec.external.enums.SaaSSkuTypeEnum;
import com.xyy.ec.external.params.SaasQueryParams;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.api.ProductToolApi;
import com.xyy.saas.web.api.module.product.constant.DictConstant;
import com.xyy.saas.web.api.module.product.model.HomeEcProductVo;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.dto.result.YbmDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName HomeProductRecordApiController
 * @Description 首页商品推荐处理接口
 * <AUTHOR>
 * @Date 2019/12/18 15:11
 * @Version 1.0
 **/
@Controller
public class HomeProductRecordApiController  implements HomeProductRecordApi {

    private static final Logger logger = LoggerFactory.getLogger(HomeProductRecordApiController.class);

    @Reference(version = "1.0.0", registry = "common")
    private ProductdExternalApi productdExternalApi;

    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;

    @Reference(version = "0.0.1")
    private ProductToolApi productToolApi;

    @Override
    public ResponseEntity<ResultVO> toShowEcPoroductsRecord(HttpServletRequest request) {
        String organSign = request.getHeader("organSign");
        ResultVO<YbmDto> resultVO = drugstoreApi.queryYbmInfoByOrganSign(organSign);
        Long merchantId = 0L;
        if(resultVO.getCode() == 0){
            String bindCode = resultVO.getResult().getBindCode();
            merchantId = Long.valueOf(bindCode.substring(4));
        }else{
            return new ResponseEntity<ResultVO>(new ResultVO(resultVO.getMsg()), HttpStatus.OK);
        }
        List<SaasQueryParams> params = Lists.newArrayList();
        List<Long> stids = productToolApi.getProductidByOrganSignFromRedis(DictConstant.REDIS_PRICE_ADVANTAGE_PREFIX + organSign);
        formartSaasQueryParams(SaaSSkuTypeEnum.PRICE_ADVANTAGE.getValue(),stids,params);
        stids.clear();
        stids = productToolApi.getProductidByOrganSignFromRedis(DictConstant.REDIS_INVENTORY_RATIOS_PREFIX + organSign);
        formartSaasQueryParams(SaaSSkuTypeEnum.SHORTAGE.getValue(),stids,params);

        List<HomeEcProductVo> vos = new ArrayList<>();
        ResultData<List<ProductInfoForSaaSDTO>> resultData = null;
        try{
            logger.info("organSign:{},skuParams：{}，merchantId：{}",organSign,JSONObject.toJSON(params),merchantId);
            resultData = productdExternalApi.filterSkuVisibleAndBuy(params, merchantId);
            logger.info("返回值：{}",JSONObject.toJSON(resultData));
        }catch (Exception exception){
            exception.printStackTrace();
            logger.info("productdExternalApi.filterSkuVisibleAndBuy(params, merchantId) occur error:" + exception);
        }
        if(resultData.getCode().equals("200")){
            List<ProductInfoForSaaSDTO> resultDataData = resultData.getData();
            for(ProductInfoForSaaSDTO saaSDTO:resultDataData){
                HomeEcProductVo dto = new HomeEcProductVo();
                dto.setSkuId(saaSDTO.getSkuId());
                dto.setImageUrl(saaSDTO.getImageUrl());
                dto.setWhetherCoupon(saaSDTO.getWhetherCoupon());
                dto.setProductName(saaSDTO.getProductName());
                dto.setCommonName(saaSDTO.getCommonName());
                dto.setSpec(saaSDTO.getSpec());
                dto.setManufacturer(saaSDTO.getManufacturer());
                dto.setPrice(saaSDTO.getPrice());
                dto.setMarketPrice(saaSDTO.getMarketPrice());
                dto.setSkuType(saaSDTO.getSkuType());
                vos.add(dto);
            }
        }
        return new ResponseEntity<ResultVO>(new ResultVO(vos), HttpStatus.OK);
    }

    /**
     * 功能描述: <br>封装参数
     * 〈〉
     * @Param: [skuType, stids, params]
     * @Return: void
     */
    private void formartSaasQueryParams(Integer skuType,List<Long> stids,List<SaasQueryParams> params){
        for (Long stid : stids) {
            SaasQueryParams param = new SaasQueryParams();
            param.setStandardProductId(stid);
            param.setSkuType(skuType);
            params.add(param);
        }
    }
}
