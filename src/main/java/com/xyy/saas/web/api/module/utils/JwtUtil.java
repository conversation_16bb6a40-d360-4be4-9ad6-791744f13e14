package com.xyy.saas.web.api.module.utils;

import io.jsonwebtoken.*;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: uzdz
 * @date: 2018/8/29 15:14
 * @description: JJWT token加密解密工具类
 */
public class JwtUtil {

    /**
     *  token秘钥，请勿泄露，请勿随便修改 backups:JKKLJOoasdlfj
     */
    private static final String SECRET = "JKKLJOoasdlfj";

    /**
     * 密钥加密算法
     */
    private static final String generalKeyAlgorithm = "AES";

    /**
     * JJWT签名算法
     */
    private static final SignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.HS256;

    /**
     * 创建jwt
     * @param claims 创建payload的私有声明（根据特定的业务需要添加，如果要拿这个做验证，一般是需要和jwt的接收方提前沟通好验证方式的）
     * @return
     * @throws Exception
     */
    public static String createJWT(Map<String,Object> claims) throws Exception {
        // 指定签名的时候使用的签名算法，也就是header那部分，jjwt已经将这部分内容封装好了。

        /**
         * setClaims 如果有私有声明，一定要先设置这个自己创建的私有的声明，这个是给builder的claim赋值，一旦写在标准的声明赋值之后，就是覆盖了那些标准的声明的
         * signWith  设置签名算法以及密钥
         */
        JwtBuilder builder = Jwts.builder()
                .setClaims(claims)
                .setIssuedAt(new Date(System.currentTimeMillis()))
                .signWith(signatureAlgorithm, generalKey());

        return builder.compact();
    }

    /**
     * 解密jwt
     * @param jwt 加密字符串
     * @return
     * @throws SignatureException
     */
    public static Claims parseJWT(String jwt) throws SignatureException{
        // 签名秘钥，和生成的签名的秘钥一模一样
        SecretKey key = generalKey();
        Claims claims = Jwts.parser()
                .setSigningKey(key)
                .parseClaimsJws(jwt).getBody();
        return claims;
    }

    /**
     * 由字符串生成加密key
     * @return
     */
    public static SecretKey generalKey(){
        byte[] encodedKey = Base64.decodeBase64(SECRET);
        // 根据给定的字节数组使用AES加密算法构造一个密钥，使用 encodedKey中的始于且包含 0 到前 leng 个字节这是当然是所有。
        SecretKey key = new SecretKeySpec(encodedKey, 0, encodedKey.length, generalKeyAlgorithm);
        return key;
    }

    public static void main(String[] args) throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("key", "user:123");
        String jwt = createJWT(map);
        System.out.println(jwt);
    }
}
