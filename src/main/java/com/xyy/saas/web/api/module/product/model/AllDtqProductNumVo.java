package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName AllDtqProductNumVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/29 23:06
 * @Version 1.0
 **/
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

@ApiModel(description = "商品提报列表结果信息类")
public class AllDtqProductNumVo {

    @ApiModelProperty(value = "待提取数量")
    public String getDtqProductNum() {
        return dtqProductNum;
    }

    public void setDtqProductNum(String dtqProductNum) {
        this.dtqProductNum = dtqProductNum;
    }

    @JsonProperty("dtqProductNum")
    private String dtqProductNum;
}
