package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName ConfigurationSchemesQueryVo
 * @Description 配置调价方案列表查询类
 * <AUTHOR>
 * @Date 2020/8/19 18:11
 * @Version 1.0
 **/
@ApiModel(description = "配置调价方案列表查询类")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")
public class ConfigurationSchemesQueryVo {

    @JsonProperty("orSignMixQuuery")
    private String orSignMixQuuery;//门店信息：混合查询(支持门店名称，机构id)
    @JsonProperty("auditState")
    private Byte auditState;//审批状态：-1:全部,1:审核中, 2:同意, 3:拒绝
    @JsonProperty("adjustName")
    private String adjustName;//方案名称
    @JsonProperty("configurBeginTime")
    private String configurBeginTime;//配置开始时间
    @JsonProperty("configurEndTime")
    private String configurEndTime;//配置结束时间
    @JsonProperty("auditBeginTime")
    private String auditBeginTime;//审批开始时间
    @JsonProperty("auditEndTime")
    private String auditEndTime;//审批结束时间
    @JsonProperty("isConfigured")
    private Byte isConfigured;//是否配置调价方案：-1全部，0否，1是
    @JsonProperty("isExpiration")
    private Byte isExpiration;//是否过期：-1全部，0否，1是
    @JsonProperty("page")
    public Integer page;
    @JsonProperty("rows")
    public Integer rows;
    @JsonProperty("address")
    private String address;//区域信息
    /**购货单位类型
     * 1:门店  2.销售单位
     * @see com.xyy.saas.product.control.enums.AdjustEnums.PurchaserTypeEnum
     **/
    @JsonProperty("purchaserType")
    private Byte purchaserType ;
    @ApiModelProperty(value = "购货单位类型")
    public Byte getPurchaserType() {
        return purchaserType;
    }

    public void setPurchaserType(Byte purchaserType) {
        this.purchaserType = purchaserType;
    }

    @ApiModelProperty(value = "门店名称")
    public String getInputDrugName() {
        return inputDrugName;
    }

    public void setInputDrugName(String inputDrugName) {
        this.inputDrugName = inputDrugName;
    }

    @ApiModelProperty(value = "门店id")
    public String getInputDrugOrganSign() {
        return inputDrugOrganSign;
    }

    public void setInputDrugOrganSign(String inputDrugOrganSign) {
        this.inputDrugOrganSign = inputDrugOrganSign;
    }

    @JsonProperty("inputDrugName")
    private String inputDrugName;//门店名称

    @JsonProperty("inputDrugOrganSign")
    private String inputDrugOrganSign;//门店id

    @ApiModelProperty(value = "当前页码")
    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    @ApiModelProperty(value = "每页信息数")
    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    @ApiModelProperty(value = "门店信息：混合查询(支持门店名称，机构id)")
    public String getOrSignMixQuuery() {
        return orSignMixQuuery;
    }

    public void setOrSignMixQuuery(String orSignMixQuuery) {
        this.orSignMixQuuery = orSignMixQuuery;
    }

    @ApiModelProperty(value = "审批状态：-1:全部,1:审核中, 2:同意, 3:拒绝")
    public Byte getAuditState() {
        return auditState;
    }

    public void setAuditState(Byte auditState) {
        this.auditState = auditState;
    }

    @ApiModelProperty(value = "方案名称")
    public String getAdjustName() {
        return adjustName;
    }

    public void setAdjustName(String adjustName) {
        this.adjustName = adjustName;
    }

    @ApiModelProperty(value = "配置时间：开始时间框")
    public String getConfigurBeginTime() {
        return configurBeginTime;
    }

    public void setConfigurBeginTime(String configurBeginTime) {
        this.configurBeginTime = configurBeginTime;
    }

    @ApiModelProperty(value = "配置时间：结束时间框")
    public String getConfigurEndTime() {
        return configurEndTime;
    }

    public void setConfigurEndTime(String configurEndTime) {
        this.configurEndTime = configurEndTime;
    }

    @ApiModelProperty(value = "审批时间：开始时间框")
    public String getAuditBeginTime() {
        return auditBeginTime;
    }

    public void setAuditBeginTime(String auditBeginTime) {
        this.auditBeginTime = auditBeginTime;
    }

    @ApiModelProperty(value = "审批时间：结束时间框")
    public String getAuditEndTime() {
        return auditEndTime;
    }

    public void setAuditEndTime(String auditEndTime) {
        this.auditEndTime = auditEndTime;
    }

    @ApiModelProperty(value = "是否配置调价方案：-1全部，0否，1是")
    public Byte getIsConfigured() {
        return isConfigured;
    }

    public void setIsConfigured(Byte isConfigured) {
        this.isConfigured = isConfigured;
    }

    @ApiModelProperty(value = "是否过期：-1全部，0否，1是")
    public Byte getIsExpiration() {
        return isExpiration;
    }

    public void setIsExpiration(Byte isExpiration) {
        this.isExpiration = isExpiration;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
}
