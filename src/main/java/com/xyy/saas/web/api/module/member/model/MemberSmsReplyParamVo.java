package com.xyy.saas.web.api.module.member.model;

import com.xyy.user.module.dto.sms.SaasSmsReplyDto;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2021/12/22
 **/
public class MemberSmsReplyParamVo implements Serializable {
    /**
     * 回复记录Id列表
     */
    private SaasSmsReplyDto smsReply;

    /**
     * 1：加入短信黑名单，2：移除短信黑名单
     */
    private Integer optType;

    public SaasSmsReplyDto getSmsReply() {
        return smsReply;
    }

    public void setSmsReply(SaasSmsReplyDto smsReply) {
        this.smsReply = smsReply;
    }

    public Integer getOptType() {
        return optType;
    }

    public void setOptType(Integer optType) {
        this.optType = optType;
    }
}
