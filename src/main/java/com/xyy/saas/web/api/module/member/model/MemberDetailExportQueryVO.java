package com.xyy.saas.web.api.module.member.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class MemberDetailExportQueryVO implements Serializable {

    /**
     * 数量
     */
    private Integer rows;

    /**
     * 页码
     */
    private Integer page;

    /**
     * 混合查询
     */
    private String mixedQuery;

    /**
     * 手机号
     */
    private String telephone;

    /**
     * 卡号
     */
    private String cartNo;

    /**
     * 开始积分
     */
    private String startPoint;

    /**
     * 结束积分
     */
    private String endPoint;

    /**
     * 排序字段
     */
    private String sidx;

    /**
     * 排序方式 asc desc
     */
    private String sord;

    /**
     * 白名单
     */
    private Integer whiteList;

    /**
     * 会员Guid
     */
    private String memberGuid;

    /**
     * 会员等级id
     */
    private Long vipLevelId;

    /**
     * 开始时间
     */
    private String startCreateDate;

    /**
     * 结束时间
     */
    private String endCreateDate;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 发卡人
     */
    private String createUserName;

    /**
     * 机构号
     */
    private String organSign;

    /**
     * 开始余额
     */
    private BigDecimal minTotalAmount;

    /**
     * 结束余额
     */
    private BigDecimal maxTotalAmount;

    /**
     *  门店端：是否查询全部
     */
    private Boolean  flag;

    /**
     * 关心病种
     */
    private String chronicPref;

    private Integer startAge;

    private Integer endAge;

    private List<Integer> startBirthdayRange;

    private List<Integer> endBirthdayRange;

    private Byte isDrugstoreHidden;

    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public String getMixedQuery() {
        return mixedQuery;
    }

    public void setMixedQuery(String mixedQuery) {
        this.mixedQuery = mixedQuery;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getCartNo() {
        return cartNo;
    }

    public void setCartNo(String cartNo) {
        this.cartNo = cartNo;
    }

    public String getStartPoint() {
        return startPoint;
    }

    public void setStartPoint(String startPoint) {
        this.startPoint = startPoint;
    }

    public String getEndPoint() {
        return endPoint;
    }

    public void setEndPoint(String endPoint) {
        this.endPoint = endPoint;
    }

    public String getSidx() {
        return sidx;
    }

    public void setSidx(String sidx) {
        this.sidx = sidx;
    }

    public String getSord() {
        return sord;
    }

    public void setSord(String sord) {
        this.sord = sord;
    }

    public Integer getWhiteList() {
        return whiteList;
    }

    public void setWhiteList(Integer whiteList) {
        this.whiteList = whiteList;
    }

    public String getMemberGuid() {
        return memberGuid;
    }

    public void setMemberGuid(String memberGuid) {
        this.memberGuid = memberGuid;
    }

    public Long getVipLevelId() {
        return vipLevelId;
    }

    public void setVipLevelId(Long vipLevelId) {
        this.vipLevelId = vipLevelId;
    }

    public String getStartCreateDate() {
        return startCreateDate;
    }

    public void setStartCreateDate(String startCreateDate) {
        this.startCreateDate = startCreateDate;
    }

    public String getEndCreateDate() {
        return endCreateDate;
    }

    public void setEndCreateDate(String endCreateDate) {
        this.endCreateDate = endCreateDate;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public BigDecimal getMinTotalAmount() {
        return minTotalAmount;
    }

    public void setMinTotalAmount(BigDecimal minTotalAmount) {
        this.minTotalAmount = minTotalAmount;
    }

    public BigDecimal getMaxTotalAmount() {
        return maxTotalAmount;
    }

    public void setMaxTotalAmount(BigDecimal maxTotalAmount) {
        this.maxTotalAmount = maxTotalAmount;
    }

    public Boolean getFlag() {
        return flag;
    }

    public void setFlag(Boolean flag) {
        this.flag = flag;
    }

    public String getChronicPref() {
        return chronicPref;
    }

    public void setChronicPref(String chronicPref) {
        this.chronicPref = chronicPref;
    }

    public Integer getStartAge() {
        return startAge;
    }

    public void setStartAge(Integer startAge) {
        this.startAge = startAge;
    }

    public Integer getEndAge() {
        return endAge;
    }

    public void setEndAge(Integer endAge) {
        this.endAge = endAge;
    }

    public List<Integer> getStartBirthdayRange() {
        return startBirthdayRange;
    }

    public void setStartBirthdayRange(List<Integer> startBirthdayRange) {
        this.startBirthdayRange = startBirthdayRange;
    }

    public List<Integer> getEndBirthdayRange() {
        return endBirthdayRange;
    }

    public void setEndBirthdayRange(List<Integer> endBirthdayRange) {
        this.endBirthdayRange = endBirthdayRange;
    }

    public Byte getIsDrugstoreHidden() {
        return isDrugstoreHidden;
    }

    public void setIsDrugstoreHidden(Byte isDrugstoreHidden) {
        this.isDrugstoreHidden = isDrugstoreHidden;
    }
}
