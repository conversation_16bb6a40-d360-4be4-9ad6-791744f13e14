package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * 商品售价调整信息实体
 */
@ApiModel(description = "商品售价调整明细信息导出信息实体")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")
public class ProPriceAdjustVo {

    @JsonProperty("id")
    private Long id = null;

    @JsonProperty("productId")
    private Long productId = null;

    @JsonProperty("productPref")
    private String productPref = null;

    @JsonProperty("productName")
    private String productName = null;

    @JsonProperty("attributeSpecification")
    private String attributeSpecification = null; // 规格

    @JsonProperty("unitId")
    private Integer unitId = null; // 单位

    @JsonProperty("retailPrice")
    private BigDecimal retailPrice = null; // 零售价

    @JsonProperty("vipPrice")
    private BigDecimal vipPrice = null; // 会员价

    @JsonProperty("manufacturer")
    private String manufacturer = null; // 生产厂家

    @JsonProperty("approvalNumber")
    private String approvalNumber = null; // 批准文号

    @JsonProperty("producingArea")
    private String producingArea = null; // 产地

    @JsonProperty("newPrice")
    private BigDecimal newPrice = null; // 调整后单价

    @JsonProperty("newMemberPrice")
    private BigDecimal newMemberPrice = null; // 调整后会员价格

    @JsonProperty("status")
    private Byte status = null; // 当前状态

    @JsonProperty("unitName")
    private String unitName = null; // 单位名称

    // 单据编号
    @JsonProperty("pref")
    private String pref = null;

    @JsonProperty("createTime")
    private Date createTime = null; // 生效时间

    @JsonProperty("remark")
    private String remark = null;

    @JsonProperty("createUser")
    private String createUser = null; // 申请人

    @JsonProperty("commonName")
    private String commonName = null;

    public ProPriceAdjustVo id(Long id) {
        this.id = id;
        return this;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public ProPriceAdjustVo productId(Long productId) {
        this.productId = productId;
        return this;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public ProPriceAdjustVo productPref(String productPref) {
        this.productPref = productPref;
        return this;
    }

    public String getProductPref() {
        return productPref;
    }

    public void setProductPref(String productPref) {
        this.productPref = productPref;
    }

    public ProPriceAdjustVo productName(String productName) {
        this.productName = productName;
        return this;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public ProPriceAdjustVo attributeSpecification(String attributeSpecification) {
        this.attributeSpecification = attributeSpecification;
        return this;
    }

    public String getAttributeSpecification() {
        return attributeSpecification;
    }

    public void setAttributeSpecification(String attributeSpecification) {
        this.attributeSpecification = attributeSpecification;
    }

    public ProPriceAdjustVo unitId(Integer unitId) {
        this.unitId = unitId;
        return this;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public ProPriceAdjustVo retailPrice(BigDecimal retailPrice) {
        this.retailPrice = retailPrice;
        return this;
    }

    public BigDecimal getRetailPrice() {
        return retailPrice;
    }

    public void setRetailPrice(BigDecimal retailPrice) {
        this.retailPrice = retailPrice;
    }

    public ProPriceAdjustVo vipPrice(BigDecimal vipPrice) {
        this.vipPrice = vipPrice;
        return this;
    }

    public BigDecimal getVipPrice() {
        return vipPrice;
    }

    public void setVipPrice(BigDecimal vipPrice) {
        this.vipPrice = vipPrice;
    }

    public ProPriceAdjustVo manufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
        return this;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public ProPriceAdjustVo approvalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
        return this;
    }

    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    public ProPriceAdjustVo producingArea(String producingArea) {
        this.producingArea = producingArea;
        return this;
    }

    public String getProducingArea() {
        return producingArea;
    }

    public void setProducingArea(String producingArea) {
        this.producingArea = producingArea;
    }

    public ProPriceAdjustVo newPrice(BigDecimal newPrice) {
        this.newPrice = newPrice;
        return this;
    }

    public BigDecimal getNewPrice() {
        return newPrice;
    }

    public void setNewPrice(BigDecimal newPrice) {
        this.newPrice = newPrice;
    }

    public ProPriceAdjustVo newMemberPrice(BigDecimal newMemberPrice) {
        this.newMemberPrice = newMemberPrice;
        return this;
    }

    public BigDecimal getNewMemberPrice() {
        return newMemberPrice;
    }

    public void setNewMemberPrice(BigDecimal newMemberPrice) {
        this.newMemberPrice = newMemberPrice;
    }

    public ProPriceAdjustVo status(Byte status) {
        this.status = status;
        return this;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public ProPriceAdjustVo unitName(String unitName) {
        this.unitName = unitName;
        return this;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public ProPriceAdjustVo pref(String pref) {
        this.pref = pref;
        return this;
    }

    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    public ProPriceAdjustVo createTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public ProPriceAdjustVo remark(String remark) {
        this.remark = remark;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public ProPriceAdjustVo createUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public ProPriceAdjustVo commonName(String commonName) {
        this.commonName = commonName;
        return this;
    }

    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ProPriceAdjustVo that = (ProPriceAdjustVo) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(productId, that.productId) &&
                Objects.equals(productPref, that.productPref) &&
                Objects.equals(productName, that.productName) &&
                Objects.equals(attributeSpecification, that.attributeSpecification) &&
                Objects.equals(unitId, that.unitId) &&
                Objects.equals(retailPrice, that.retailPrice) &&
                Objects.equals(vipPrice, that.vipPrice) &&
                Objects.equals(manufacturer, that.manufacturer) &&
                Objects.equals(approvalNumber, that.approvalNumber) &&
                Objects.equals(producingArea, that.producingArea) &&
                Objects.equals(newPrice, that.newPrice) &&
                Objects.equals(newMemberPrice, that.newMemberPrice) &&
                Objects.equals(status, that.status) &&
                Objects.equals(unitName, that.unitName) &&
                Objects.equals(pref, that.pref) &&
                Objects.equals(createTime, that.createTime) &&
                Objects.equals(remark, that.remark) &&
                Objects.equals(createUser, that.createUser) &&
                Objects.equals(commonName, that.commonName);
    }

    @Override
    public int hashCode() {

        return Objects.hash(id, productId, productPref, productName, attributeSpecification, unitId, retailPrice, vipPrice, manufacturer, approvalNumber, producingArea, newPrice, newMemberPrice, status, unitName, pref, createTime, remark, createUser, commonName);
    }

    @Override
    public String toString() {
        return "ProPriceAdjustVo{" +
                "id=" + id +
                ", productId=" + productId +
                ", productPref='" + productPref + '\'' +
                ", productName='" + productName + '\'' +
                ", attributeSpecification='" + attributeSpecification + '\'' +
                ", unitId=" + unitId +
                ", retailPrice=" + retailPrice +
                ", vipPrice=" + vipPrice +
                ", manufacturer='" + manufacturer + '\'' +
                ", approvalNumber='" + approvalNumber + '\'' +
                ", producingArea='" + producingArea + '\'' +
                ", newPrice=" + newPrice +
                ", newMemberPrice=" + newMemberPrice +
                ", status=" + status +
                ", unitName='" + unitName + '\'' +
                ", pref='" + pref + '\'' +
                ", createTime=" + createTime +
                ", remark='" + remark + '\'' +
                ", createUser='" + createUser + '\'' +
                ", commonName='" + commonName + '\'' +
                '}';
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}
