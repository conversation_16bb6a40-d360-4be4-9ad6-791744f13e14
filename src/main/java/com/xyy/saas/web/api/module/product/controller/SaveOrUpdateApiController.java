package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.inventory.core.api.InventoryLotNumberAdjustApi;
import com.xyy.saas.web.api.module.product.model.InventoryLotNumberAdjustVo;
import com.xyy.saas.web.api.module.product.model.InventoryLotnumAdjustDetailVo;
import com.xyy.saas.web.api.module.product.model.ResultCodeEnum;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-29T17:03:47.479+08:00")

@Controller
public class SaveOrUpdateApiController implements SaveOrUpdateApi {

    @Reference(version = "1.0.0")
    private InventoryLotNumberAdjustApi inventoryLotNumberAdjustApi;

    public ResponseEntity<ResultVO> saveOrUpdate(@ApiParam(value = "商品批号库存调整Vo" ,required=true ) @RequestHeader("userName") String userName, @RequestHeader("organSign") String organSign, @Valid @RequestBody InventoryLotNumberAdjustVo inventoryLotNumberAdjustVo) {

        if(inventoryLotNumberAdjustVo==null){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.INVENTORY_PARAM_NULL), HttpStatus.INTERNAL_SERVER_ERROR);
        }
        com.xyy.saas.inventory.core.dto.InventoryLotNumberAdjustVo vo = new com.xyy.saas.inventory.core.dto.InventoryLotNumberAdjustVo();
        List<com.xyy.saas.inventory.core.dto.InventoryLotnumAdjustDetailVo> iadvos = new ArrayList<>();
        if(inventoryLotNumberAdjustVo!=null&&inventoryLotNumberAdjustVo.getDetailVos()!=null){
            for(InventoryLotnumAdjustDetailVo iavo : inventoryLotNumberAdjustVo.getDetailVos()){
                com.xyy.saas.inventory.core.dto.InventoryLotnumAdjustDetailVo iladvo = new com.xyy.saas.inventory.core.dto.InventoryLotnumAdjustDetailVo();
                BeanUtils.copyProperties(iavo,iladvo);
                iadvos.add(iladvo);
            }
        }

        BeanUtils.copyProperties(inventoryLotNumberAdjustVo,vo);
        vo.setDetailVos(iadvos);
        vo.setOrgansign(organSign);
        vo.setCreateUser(userName);
        com.xyy.saas.inventory.core.dto.InventoryLotNumberAdjustVo back = inventoryLotNumberAdjustApi.saveOrUpdate(vo);
        if(back.getBack()>=1){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.SUCCESS), HttpStatus.OK);
        }else{
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.INVENTORY_RESULT_FAIL), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

}
