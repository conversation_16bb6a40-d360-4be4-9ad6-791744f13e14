package com.xyy.saas.web.api.module.product.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName TjCheckResultVo
 * @Description 天津静海返回结果参数类
 * <AUTHOR>
 * @Date 2019/10/25 13:03
 * @Version 1.0
 **/
@ApiModel(description = "天津静海返回结果参数类")
public class TjCheckResultVo {

    private Byte isOpen;

    @ApiModelProperty(value = "0为不显示，1为显示")
    public Byte getIsOpen() {
        return isOpen;
    }

    public void setIsOpen(Byte isOpen) {
        this.isOpen = isOpen;
    }
}
