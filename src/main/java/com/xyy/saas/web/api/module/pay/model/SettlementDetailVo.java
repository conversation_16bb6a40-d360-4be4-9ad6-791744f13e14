package com.xyy.saas.web.api.module.pay.model;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@ApiModel(description = "结算记录查询结果Vo")
@Data
public class SettlementDetailVo {

    @ApiModelProperty(value = "打款单创建日期")
    private String createTime;

    @ApiModelProperty(value = "打款金额")
    private String realAmount;

    @ApiModelProperty(value = "交易金额")
    private String tradeAmount;

    @ApiModelProperty(value = "手续费")
    private String feeAmount;


    @ApiModelProperty(value = "账单日期")
    private String settleDate;


    @ApiModelProperty(value = "银行卡号")
    private String bankAccount;


    @ApiModelProperty(value = "收款人姓名")
    private String bankHolder;

    @ApiModelProperty(value = "打款状态： -1：打款失败； 0：打款中； 1：打款成功； 2：打款退票； 3：冻结出款；")
    private int state;


    @ApiModelProperty(value = "渠道类型 1 银联 2 乐刷")
    private Integer channelType;


    @ApiModelProperty(value = "打款单完成日期")
    private String bankretTime;




}