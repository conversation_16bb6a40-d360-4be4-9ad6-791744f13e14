package com.xyy.saas.web.api.module.member.model;

import lombok.Data;

import java.util.List;

/**
 * 慢病商品列表查询参数--参考促销明细查询参数
 */
@Data
public class MemberChronicProductSelectListParamVo{
    //商品信息、商品分类、自定义分类、处方分类、生产厂家。
    /**
     * 混合查询字段(通用名称,商品名称,助记码)
     */
    private String mixedQuery;
    /**
     * 商品7大类,填写分类id
     */
    private Integer systemTypeId;
    /**
     * 自定义分类,填写分类id
     */
    private Integer customTypeId;
    /**
     * abc分类,填写分类id   (应该就是处方分类)
     */
    private Integer abcTypeId;
    /**
     * 生产厂家
     */
    private String manufacturer;



    /**
     * 排除商品列表
     */
    private List<String> exclusivePrefs;

    /**
     * 包含商品列表
     */
    private List<String> includePrefs;

    /**
     * 机构唯一标识
     */
    private String organSign;

    /**
     * 每页多少条
     */
    private Integer pageSize;

    /**
     * 当前多少页
     */
    private Integer pageNum;

    /**
     * 1：不展示库存为零的商品， 0：展示库存为零的商品
     */
    private Byte stockNumberYn;

    //1：不展示禁用商品， 0：展示禁用商品
    private Byte usedYn;

    /**
     * 经营模式 1、单体 2、连锁 3、联营
     */
    private Byte bizModel;

    /**
     * 总部机构号
     */
    private String headquartersOrganSign;

    /**
     * 机构类型 1、门店  3、总部
     */
    private Byte organSignType;

    //是否排除商品编号(赠品查询不需要排除)
    private boolean isCheckPref;

    /**
     * 慢病编号
     */
    private String chronicPref;

    //处方分类
    private Integer prescriptionClassification;
}
