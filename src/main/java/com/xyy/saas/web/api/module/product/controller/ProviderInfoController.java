package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.api.ExportExcelApi;
import com.xyy.common.module.api.SysConfigApi;
import com.xyy.common.module.dto.SystemConfigDto;
import com.xyy.saas.common.api.SystemDictApi;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.dto.ExportExcelDto;
import com.xyy.saas.common.dto.SystemDictDto;
import com.xyy.saas.common.dto.SystemDictQueryDto;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.rabbitmq.core.api.MessagePushApi;
import com.xyy.saas.supplier.api.SupplierBasicInfoApi;
import com.xyy.saas.supplier.dto.*;
import com.xyy.saas.supplier.enums.QualificationTypeEnum;
import com.xyy.saas.web.api.module.product.constant.DictConstant;
import com.xyy.saas.web.api.module.product.model.*;
import com.xyy.saas.web.api.module.product.util.ExecServiceTask;
import com.xyy.saas.web.api.module.product.util.ExportExcelUtil;
import com.xyy.user.module.api.CertificateBusinessScopeApi;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.api.RoleApi;
import com.xyy.user.module.dto.restructure.*;
import com.xyy.user.module.dto.result.EmployeeDto;
import com.xyy.user.module.enums.DrugstoreBizModelEnum;
import com.xyy.user.module.enums.DrugstoreTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import com.xyy.common.module.api.SysConfigApi;
import com.xyy.common.module.dto.SystemConfigDto;

@Api(value = "4.0供应商基本信息API接口", description = "4.0供应商基本信息API接口", tags={ "4.0供应商基本信息API接口", })
@Controller
public class ProviderInfoController implements ProviderInfoApi {

    private static final Logger logger = LoggerFactory.getLogger(ProductApiController.class);

    @Reference(version = "0.0.1")
    private SupplierBasicInfoApi supplierBasicInfoApi;

    @Reference(version = "0.0.1")
    private SystemDictApi systemDictApi;

    @Reference(version = "0.0.1")
    private MessagePushApi messagePushApi;

    @Reference(version = "0.0.1")
    private EmployeeApi employeeApi;

    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;

    @Value("${ftp.serverName}")
    public String serverName;

    @Value("${ftp.port}")
    public String port;

    @Value("${ftp.username}")
    public String username;

    @Value("${ftp.password}")
    public String password;

    @Value("${ftp.remotePath}")
    public String remotePath;

    @Value("${ftp.downLoadUrl}")
    public String downLoadUrl;

    @Reference(version = "0.0.1")
    public RoleApi roleUserApi;

    @Reference(version = "0.0.3")
    public ExportExcelApi exportExcelApi;

    @Reference(version = "0.0.1")
    private SysConfigApi sysConfigApi;

    @Reference(version = "0.0.1")
    private CertificateBusinessScopeApi certificateBusinessScopeApi;

    @Override
    public ResponseEntity<ResultVO> toAddProvider(HttpServletRequest request, Model model) {
        String organSign = request.getHeader("organSign");
        getProviderDict(model,organSign);
        String name = "" + organSign;
        model.addAttribute("date",new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
        // 经营范围
        List<SystemDictDto> scopeDict = getSystemDict(DictConstant.BUSINESS_SCOPE, organSign);
        model.addAttribute("scopeDict",scopeDict);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(model), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> addOrUpdate(HttpServletRequest request, @ApiParam(value = "供应商查询对象" ,required=true )  @Valid @RequestBody ProviderVoDto provider) {
        String organSign = request.getHeader("organSign");
        String employeeId = request.getHeader("employeeId");
        CommonRequestModel model =  JSONObject.parseObject(request.getHeader("commonRequestModel"), CommonRequestModel.class);
        logger.info("addOrUpdate providerInfo organSign:{},commonRequestModel:{},employeeId:{},provider id:{}", organSign,JSONObject.toJSON(model),employeeId,provider.getId());

        ArrayList<String> BusinessScopeList = new ArrayList<>();
        if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel())){
            if (CollectionUtils.isEmpty(provider.getQualificationInfos()) ){
                logger.error("==========================参数不合法");
                return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, "noQualificationInfos"),HttpStatus.OK);
            }else {
                List<QualificationInfoDto> qualificationInfos = provider.getQualificationInfos();
                List<String> collect=new ArrayList<>();
                StringBuilder stringBuilder = new StringBuilder();
                qualificationInfos.stream().forEach(item -> {
                    if(!StringUtils.isEmpty(item.getBusinessScopes())){
                        List<String> stringList = Arrays.asList(item.getBusinessScopes().split(","));
                        BusinessScopeList.addAll(stringList);
                    }
                });
                 collect = BusinessScopeList.stream().distinct().collect(Collectors.toList());
                    for(int i=0; i<collect.size();i++){
                        if(i<collect.size()-1){
                            stringBuilder.append(collect.get(i)).append(",");
                        }else{
                            stringBuilder.append(collect.get(i));
                        }
                    }
                provider.setBusinessScope(stringBuilder.toString());
            }

            List<ProviderSalesDto> providerSalesPos = provider.getProviderSalesDtos();
            if (!CollectionUtils.isEmpty(providerSalesPos)) {
                for (ProviderSalesDto providerSalesPo : providerSalesPos) {
                    if (StringUtils.isEmpty(providerSalesPo.getAuthorizationNumExpirationDate())) {
                        logger.error("==========================【授权书号有效期】未填");
                        return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, "noAuthorizationNumExpirationDate"),HttpStatus.OK);
                    }
                }
            }
        }

        if (StringUtils.isEmpty(provider) ){
            logger.error("==========================参数不合法");
            return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, null),HttpStatus.OK);
        }

        if (StringUtils.isEmpty(provider.getProviderName())){
            logger.error("==========================供应商名称未填写");
            return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, "noProviderName"),HttpStatus.OK);
        }else if (StringUtils.isEmpty(provider.getProviderType())){
            logger.error("==========================供应商类型未填写");
            return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, "noProviderType"),HttpStatus.OK);
        }else if (StringUtils.isEmpty(provider.getRegisteredAddress())){
            logger.error("==========================供应商注册地址未填写");
            return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, "noRegisterAddr"),HttpStatus.OK);
        }else if (StringUtils.isEmpty(provider.getBusinessScope())){
            logger.error("==========================供应商经营范围未填写");
            return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, "noBussinessScope"),HttpStatus.OK);
        }else if (StringUtils.isEmpty(provider.getBusinessLicense())){
            logger.error("==========================供应商营业执照未填写");
            return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, "noBussinessLicense"),HttpStatus.OK);
        }
        else if (StringUtils.isEmpty(provider.getRegisteredDateStr())){
            logger.error("==========================供应商注册日期未填写");
            return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, "noRegisterDate"),HttpStatus.OK);
        }
        else if (StringUtils.isEmpty(provider.getExpirationDateType())){
            logger.error("==========================供应商有效期方式未填写");
            return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, "noExpirationDateType"),HttpStatus.OK);
        }else if ("2".equals(provider.getExpirationDateType().toString()) && StringUtils.isEmpty(provider.getYouxianqiDate())){
            logger.error("==========================供应商有效期至未填写");
            return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, "noYouXianQiDate"),HttpStatus.OK);
        }

        if (DrugstoreTypeEnum.HEADQUARTERS.toEquals(model.getOrganSignType())&&DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel())) {
//            if (StringUtils.isEmpty(provider.getProviderExtVo().getProxyName())) {
//                logger.error("==========================委托人未填写");
//                return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, "noProxyName"), HttpStatus.OK);
//            } elseif (StringUtils.isEmpty(provider.getProviderExtVo().getProxyCode())) {
//                logger.error("==========================委托书编号未填写");
//                return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, "noProxyCode"), HttpStatus.OK);
//            } else
            if (StringUtils.isEmpty(provider.getProviderExtVo().getProxyExpiredDate())) {
                logger.error("==========================委托书有效期未填写");
                return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, "noProxyExpiredDate"), HttpStatus.OK);
            }

            if (provider.getProviderType() == 446) {
                if (StringUtils.isEmpty(provider.getProviderExtVo().getDrugProducePermit()) || StringUtils.isEmpty(provider.getProviderExtVo().getDrugProducePermitExpiredDate())) {
                    logger.error("==========================药品生产许可证或有效期未填写");
                    return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, "noDrugProducePermit"), HttpStatus.OK);
                } else if (StringUtils.isEmpty(provider.getProviderExtVo().getQualityAgreement()) || StringUtils.isEmpty(provider.getProviderExtVo().getQualityAgreementExpiredDate())) {
                    logger.error("==========================质量保证协议或日期未填写");
                    return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, "noQualityAgreement"), HttpStatus.OK);

                }
            }
            if (provider.getProviderType() == 447) {
                if (StringUtils.isEmpty(provider.getProviderExtVo().getDrugBusinessPermit()) || StringUtils.isEmpty(provider.getProviderExtVo().getDrugBusinessExpiredDate())) {
                    logger.error("==========================药品经营许可证或有效期未填写");
                    return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, "noDrugBusinessPermit"), HttpStatus.OK);
                } else if (StringUtils.isEmpty(provider.getProviderExtVo().getQualityAgreement()) || StringUtils.isEmpty(provider.getProviderExtVo().getQualityAgreementExpiredDate())) {
                    logger.error("==========================质量保证协议或日期未填写");
                    return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, "noQualityAgreement"), HttpStatus.OK);
                }
            }
            if (provider.getProviderType() == 448 || provider.getProviderType() == 449) {
                if (StringUtils.isEmpty(provider.getProviderExtVo().getQualityAgreement()) || StringUtils.isEmpty(provider.getProviderExtVo().getQualityAgreementExpiredDate())) {
                    logger.error("==========================质量保证协议或日期未填写");
                    return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, "noQualityAgreement"), HttpStatus.OK);
                }
            }
            if (provider.getProviderType() == 450) {
                if (StringUtils.isEmpty(provider.getProviderExtVo().getFoodCirculationPermit()) || StringUtils.isEmpty(provider.getProviderExtVo().getFoodCirculationPermitExpiredDate())) {
                    logger.error("==========================食品生产许可证或日期未填写");
                    return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, "noFoodCirculationPermit"), HttpStatus.OK);
                }
            }
            if (provider.getProviderType() == 451) {
                if (StringUtils.isEmpty(provider.getProviderExtVo().getFoodBusinessPermit()) || StringUtils.isEmpty(provider.getProviderExtVo().getFoodBusinessPermitExpiredDate())) {
                    logger.error("==========================食品经营许可证或日期未填写");
                    return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, "noFoodBusinessPermit"), HttpStatus.OK);
                }
            }
        }

        if(provider.getExpirationDateType() != null && provider.getExpirationDateType() == 1){
            provider.setExpirationDate(null);
            provider.setYouxianqiDate(null);
        }
        String username = employeeId;
        provider.setCreateUser(username);
        provider.setOrganSign(organSign);
        provider.setOrganSignType(model.getOrganSignType());
        //兼容连锁门店
        ProviderPrefDto providerPrefDto = this.supplierBasicInfoApi.providerSaveBaseInfoChain(provider,model.getBizModel());
        if ( providerPrefDto.getStatus() > 0 ){
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel())){
                List<SaaSDrugstoreDto> drugs = drugstoreApi.getDrugstoreByHeadquartersOrganSign(organSign);//获取门店列表
                if(!CollectionUtils.isEmpty(drugs)){
                    for(SaaSDrugstoreDto dto : drugs){
                        pushProviderMessToMQ(dto.getOrganSign());
                    }
                }
            }else{
                pushProviderMessToMQ(provider.getOrganSign());
            }
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(providerPrefDto.getPref()),HttpStatus.OK);
        }
        if ( -2 ==providerPrefDto.getStatus() ){
            String msg = "供应商编号重复，请核对后重新提交！";
            return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, msg), HttpStatus.OK);
        }
        if ( -4 ==providerPrefDto.getStatus() ){
            String msg = "供应商已存在（供应商编号：" + providerPrefDto.getPharmacyPref() + "），请核对后重新提交！";
            return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, msg), HttpStatus.OK);
        }
        if ( -5 ==providerPrefDto.getStatus() ){
            String msg = "提交失败,审批流生成异常,请重新提交！";
            return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, msg), HttpStatus.OK);
        }

        if ( -6 ==providerPrefDto.getStatus() ){
            String msg = "销售人员身份证号重复，请核对后重新提交！！";
            return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, msg), HttpStatus.OK);
        }

        return new ResponseEntity<ResultVO>(new ResultVO(com.xyy.saas.common.util.ResultCodeEnum.ERROR, providerPrefDto.getStatus()),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getProviderInfoById(HttpServletRequest request,@ApiParam(value = "供应商查询对象" ,required=true )  @Valid @RequestBody ProviderQueryVo providerQueryVo) {
        logger.info("getProviderInfoById id:{}",providerQueryVo.getId());
        if ( StringUtils.isEmpty(providerQueryVo.getId()) )
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(""), HttpStatus.OK);
        String organSign = request.getHeader("organSign");
        String modelJson = request.getHeader("commonRequestModel");
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                organSign = model.getHeadquartersOrganSign();
            }
        }
        logger.info("getProviderInfoById organSign:{}, id:{}",organSign,providerQueryVo.getId());
        ProviderVoDto provider = this.supplierBasicInfoApi.getProviderInfoById(providerQueryVo.getId(), organSign);
        provider.setRegisteredAddressCode(provider == null ? "" : provider.getRegisteredAddressCode() == null ? "" : provider.getRegisteredAddressCode());
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(provider),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> deleteProviderInfoById(HttpServletRequest request, @ApiParam(value = "供应商查询对象" ,required=true )  @Valid @RequestBody ProviderQueryVo providerQueryVo) {
        String organSign = request.getHeader("organSign");
        logger.info("deleteProviderInfoById organSign:" + organSign+",providerQueryVo id:"+providerQueryVo.getId());

        if ( StringUtils.isEmpty(providerQueryVo.getId()) )
            return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, null),HttpStatus.OK);

        int status = this.supplierBasicInfoApi.deleteProviderInfoById(providerQueryVo.getId(), organSign);
        if ( status > 0 )
            return new ResponseEntity<ResultVO>(new ResultVO(com.xyy.saas.common.util.ResultCodeEnum.SUCCESS, null),HttpStatus.OK);
        else
            return new ResponseEntity<ResultVO>(new ResultVO(com.xyy.saas.common.util.ResultCodeEnum.ERROR, null),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> toList(HttpServletRequest request, Model model) {
        String organSign = request.getHeader("organSign");
        List<SystemDictDto> providers = getSystemDict(DictConstant.providerCompanyType, organSign);
        JSONArray array = new JSONArray();
        if (null != providers && providers.size() > 0){
            providers.forEach(item -> {
                JSONObject json = new JSONObject();
                json.put("name", item.getName());
                json.put("id", item.getId());

                array.add(json);
            });
        }
        model.addAttribute("providers", array.toString());
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(model),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> queryProvider(HttpServletRequest request,@ApiParam(value = "商品详情信息对象" ,required=true )  @Valid @RequestBody ProviderQueryVo providerQueryVo) {
        Integer page = providerQueryVo.getPage();
        Integer rows = providerQueryVo.getRows();
        if(page==null){
            page=1;
        }
        if(rows==null){
            rows=50;
        }
        String organSign = request.getHeader("organSign");
        String currentOrganSign = organSign;//作为当前登录机构号
        String modelJson = request.getHeader("commonRequestModel");
        boolean isHeadQuarters = false;//默认否
        logger.info("门店本身机构号queryProvider organSign:{}",organSign);
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            providerQueryVo.setModel(model.getBizModel());
            providerQueryVo.setOrganSignType(model.getOrganSignType());
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel())){//连锁
                if(DrugstoreTypeEnum.HEADQUARTERS.toEquals(model.getOrganSignType())){//总部
                    isHeadQuarters = true;
                }else{//连锁门店
                    organSign = model.getHeadquartersOrganSign();
                }
            }
        }
        logger.info("传入service机构号 organSign:{},providerQueryVo:{}",organSign,JSON.toJSONString(providerQueryVo));
        logger.info("传入service机构号 organSign:{}, page:{}, rows:{}, name:{}, used:{}",organSign,providerQueryVo.getPage(),providerQueryVo.getRows(),providerQueryVo.getName(),providerQueryVo.getUsed());
        PageInfo pageInfo=new PageInfo();
        pageInfo.setPageSize(rows);
        pageInfo.setPageNum(page);
        if(!StringUtils.isEmpty(providerQueryVo.getStartTime())){
            providerQueryVo.setStartTime(providerQueryVo.getStartTime()+" 00:00:00");
        }
        if(!StringUtils.isEmpty(providerQueryVo.getEndTime())){
            providerQueryVo.setEndTime(providerQueryVo.getEndTime()+" 23:59:59");
        }
        ProviderQueryDto dto = new ProviderQueryDto();
        BeanUtils.copyProperties(providerQueryVo,dto);

        PageInfo info = this.supplierBasicInfoApi.queryProviderSort(pageInfo, dto, organSign);
        //createrUser从id转name
        List<ProviderDto> providerDtos = info.getList();
        if(!CollectionUtils.isEmpty(providerDtos)){
            Map<String,String> idToNameMap = new HashMap<>();
            if(isHeadQuarters){//查询总部机构下所有的员工
                SaaSEmployeeExtendDto saasEmployeeExtendDto = new SaaSEmployeeExtendDto();
                saasEmployeeExtendDto.setHeadquartersOrganSign(organSign);
                List<SaaSEmployeeDto> employeeList = employeeApi.getHeadquartersAndDepartmentAndBranchStoreEmployeeList(saasEmployeeExtendDto);
                if(employeeList != null && employeeList.size() > 0){
                    employeeList.stream().forEach(employeeDto -> idToNameMap.put(String.valueOf(employeeDto.getId()),employeeDto.getName()));
                }
            }else{//查询特定机构下的所有员工
                List<EmployeeDto> employeeList = employeeApi.queryAllEmployeeByOrganSign(currentOrganSign);
                if(employeeList != null && employeeList.size() > 0){
                    employeeList.stream().forEach(employeeDto -> idToNameMap.put(String.valueOf(employeeDto.getId()),employeeDto.getName()));
                }
            }
            List<SystemDictDto> systemDictList = getSystemDict(DictConstant.PROVIDER_TYPE, organSign);
            if (!CollectionUtils.isEmpty(systemDictList)) {
                Map<Integer,String> typeMap = systemDictList.stream().collect(Collectors.toMap(SystemDictDto::getId,SystemDictDto::getName));
                providerDtos.stream().forEach(pdto->{
                    pdto.setCreateUserName(idToNameMap.get(pdto.getCreateUser()));
                    pdto.setProviderTypeStr(typeMap.get(pdto.getProviderType()));
                });
            }else {
                providerDtos.stream().forEach(pdto->{
                    pdto.setCreateUserName(idToNameMap.get(pdto.getCreateUser()));
                });
            }
        }

        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(info),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> verifyProvider(HttpServletRequest request, @Valid ProviderQueryVo providerQueryVo) {
        String organSign = request.getHeader("organSign");
        logger.info("ProviderInfoController -> verifyProvider() param = {}", JSONObject.toJSONString(providerQueryVo));
        if ( StringUtils.isEmpty(providerQueryVo.getName()) )
            return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, "noProviderName"),HttpStatus.OK);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(
                supplierBasicInfoApi.verifyProvider(providerQueryVo.getName() ,providerQueryVo.getUsed() ,organSign)) ,HttpStatus.OK);
    }

    @Override
    public void exportExcelProvider(HttpServletRequest request, HttpServletResponse response, String name, String ids, Byte used) {
        String organSign = request.getHeader("organSign");
        List<ProviderVoDto> providers =  this.supplierBasicInfoApi.queryProviderByParams(name, ids, used, organSign);
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
        String extfilename="供应商基本信息"+df.format(new Date())+".xls";
        String sheetName = "供应商基本信息";
        String headers[] = new String[]{"状态", "供应商编号", "供应商名称",  "供应商类别", "地址", "是否启用"};
        String fieldNames[] = new String[]{"statusName", "pharmacyPref", "providerName", "providerTypeName", "registeredAddress", "usedName"};
        try {
            ExportExcelUtil.createExcel(response,request,extfilename,sheetName, headers, fieldNames, providers,true);
        } catch (Exception e) {
           logger.error("",e);
        }
    }

    @Override
    public void exportExcelOneProduct(HttpServletRequest request, HttpServletResponse response, ProviderVoDto providerBaseinfoVo) {
        String organSign = request.getHeader("organSign");
        List<ProviderVoDto> list = new ArrayList<>();
        ProviderVoDto provider = supplierBasicInfoApi.excelProviderInfoById(providerBaseinfoVo.getId(), organSign);
        provider.setBillNo(providerBaseinfoVo.getBillNo());
        provider.setBillCreateDate(providerBaseinfoVo.getBillCreateDate());
        provider.setBillCreateUser(providerBaseinfoVo.getBillCreateUser());
        //查询经营范围
        List<SystemDictDto> providers = getSystemDict(DictConstant.BUSINESS_SCOPE, organSign);
        Map<Integer,String> map = providers.stream().collect(Collectors.toMap(SystemDictDto::getId, s->s.getName()));

        if(!StringUtils.isEmpty(provider.getBusinessScope())){
            String[] strings = provider.getBusinessScope().split(",");
            String result = "";
            for(String s : strings){
                if(!StringUtils.isEmpty(result)){
                    result = result+","+ map.get(Integer.parseInt(s));
                }
                else {
                    result = map.get(Integer.parseInt(s));
                }
            }
            provider.setBusinessScopeStr(result);
        }

        list.add(provider);

        try {
            response.setHeader("Content-type", "text/html;charset=utf-8");
            PrintWriter out = response.getWriter();
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            JSONObject json = new JSONObject();
            json.put("prodata", JSONObject.toJSON(list));
            json.put("datatime", df.format(new Date()));
            out.print(json);
            out.flush();
            out.close();
        } catch (IOException e) {
            logger.error("",e);
        }
    }

    @Override
    public ResponseEntity<ResultVO> checkselfMotion(HttpServletRequest request) {
        String organSign = request.getHeader("organSign");
        logger.info("provider checkselfMotion organSign:"+organSign);
        JSONObject result = supplierBasicInfoApi.checkselfMotion(organSign);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(result),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> initXYYProvider() {
        supplierBasicInfoApi.initXYYProviderApprove();
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(null),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> resertIsUsed(HttpServletRequest request,  @RequestBody ProviderQueryVo providerQueryVo) {
        String organSign = request.getHeader("organSign");
        logger.info("resertIsUsed organSign:" + organSign+",providerQueryVo id:"+providerQueryVo.getId()+",used:"+providerQueryVo.getUsed());

        if ( providerQueryVo.getId() == null )
            return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, "id is null"),HttpStatus.OK);
        if ( providerQueryVo.getFlag() == null )
            return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, "used is null"),HttpStatus.OK);

        if(providerQueryVo.getFlag() == 0){
            providerQueryVo.setUsed((byte) 0);
        }
        if(providerQueryVo.getFlag() == 1){
            providerQueryVo.setUsed((byte) 1);
        }
        int status = this.supplierBasicInfoApi.resetProviderIsUsed(providerQueryVo.getId(), organSign,providerQueryVo.getUsed());
        if ( status > 0 )
            return new ResponseEntity<ResultVO>(new ResultVO(com.xyy.saas.common.util.ResultCodeEnum.SUCCESS, null),HttpStatus.OK);
        else
            return new ResponseEntity<ResultVO>(new ResultVO(com.xyy.saas.common.util.ResultCodeEnum.ERROR, null),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> refreshApproveDetailByOrganSigns(HttpServletRequest request, @ApiParam(value = "请求信息对象" ,required=true )  @Valid @RequestBody RefreshDataVo refreshDataVo) {
        logger.info("refreshApproveDetailByOrganSigns parmas organSigns:"+refreshDataVo.getOrganSigns());
        String organSigns = refreshDataVo.getOrganSigns();
        if(StringUtils.isEmpty(organSigns)){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR,"请填写正确的机构号，注意格式，多个机构号以逗号分隔"),HttpStatus.OK);
        }
        String[] organSignArray = refreshDataVo.getOrganSigns().split(",");
        List<String> organSignList = new ArrayList<>();
        for(String organSign:organSignArray){
            organSignList.add(organSign);
        }
        if(organSignList.size() < 1){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR,"请至少填写正确的机构号"),HttpStatus.OK);
        }
        String result = "";
        try{
            result = supplierBasicInfoApi.refreshApproveDetailByOrganSigns(organSignList);
        }catch (Exception e){
            logger.error("refreshApproveDetailByOrganSigns occur error:"+e);
            result = "exception";
        }
//        if(result.equals("success")){
//            //数据刷新之后推送mq
//            for(String organSign:organSignList){
//                refreshDataSuccToMQ(organSign);
//            }
//        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(result),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> exportExcelProviderNew(HttpServletRequest request, HttpServletResponse response, @Valid @RequestBody ProviderQueryExportVo providerQueryExportVo) {
        String organSign = request.getHeader("organSign");
        String mendianOrganSign=organSign;
        String employee = request.getHeader("employeeId");
        String modelJson = request.getHeader("commonRequestModel");
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                organSign = model.getHeadquartersOrganSign();
            }
        }
        //获取登录名称
        String userName = employeeApi.queryEmployeeById(Integer.valueOf(employee)).getResult().getName();
        ProviderQueryDto providerQueryDto = new ProviderQueryDto();
        BeanUtils.copyProperties(providerQueryExportVo,providerQueryDto);
        List<ProviderDto> providers =  this.supplierBasicInfoApi.queryProviderByParams(providerQueryDto,organSign);
        /*============================传ftp begin wht==============================*/
        String excelName=providerQueryExportVo.getExcelName(); //文件名称
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
        String extfilename="";
        String uuid= ExportExcelUtil.getUUID();
        if(org.apache.commons.lang3.StringUtils.isNotBlank(excelName)){
            extfilename=excelName+".xls";
        }else{
            excelName="供应商基本信息报表"+df.format(new Date());
            extfilename="供应商基本信息报表"+df.format(new Date())+".xls";
        }
        String localPath = remotePath + "/" + uuid;
        String jsonStr = toJsonFromObj(providerQueryExportVo);
        String extfilenames = extfilename;
        ExportExcelDto exportExcelDto=new ExportExcelDto();
        exportExcelDto.setSearchParamer(jsonStr);
        exportExcelDto.setExcelName(excelName);
        exportExcelDto.setFileName(uuid+"/"+extfilename);
        exportExcelDto.setCreateTime(new Date());
        exportExcelDto.setName(userName);
        exportExcelDto.setStatus((byte)0); //处理中
        exportExcelDto.setUserId(Integer.valueOf(employee));
        exportExcelDto.setMatchUrl("/baseinfo/provider/exportExcelNew");
        exportExcelDto.setModuleId(10025); //父模块id
        exportExcelDto.setOperateModule("基础信息"); //父模块名称
        exportExcelDto.setSubModule("供应商基本信息"); //子模块名称
        exportExcelDto.setSubmoduleId(10033); //子模块id
        exportExcelDto.setUrl(downLoadUrl+localPath+"/"+extfilename);
        ResultVO<List<Integer>> listResultVO = roleUserApi.queryRoleIdsByEmployeeId(Integer.valueOf(employee));
        List<Integer> idList = listResultVO.getResult();
        String str = org.apache.commons.lang3.StringUtils.join(idList, ",");
        exportExcelDto.setRoleIdstr(str); //角色id字符串
        String headers[] = providerQueryExportVo.getColNameDesc();
        String fieldNames[] = providerQueryExportVo.getColName();
        String colNameDescs = org.apache.commons.lang3.StringUtils.join(providerQueryExportVo.getColNameDesc(), ","); // 数组转字符串(逗号分隔)(推荐)
        String colNames = org.apache.commons.lang3.StringUtils.join(providerQueryExportVo.getColName(), ","); // 数组转字符串(逗号分隔)(推荐)
        exportExcelDto.setColName(colNames);
        exportExcelDto.setColNameDesc(colNameDescs);
        exportExcelDto.setOrganSign(mendianOrganSign);
        ExportExcelDto resultDto=exportExcelApi.saveExportExcel(exportExcelDto);
        /*============================传ftp end wht==============================*/
        String sheetName = "供应商基本信息报表";
        ExecServiceTask.execute(new Runnable(){
            @Override
            public void run() {
                try {
                    ExportExcelUtil.createExcelUpFtp(extfilenames,sheetName, headers, fieldNames, providers,true,serverName,port,username,password,localPath);
                    resultDto.setEndTime(new Date());
                    resultDto.setStatus((byte)1);
                    exportExcelApi.updateExportExcel(resultDto); //更新导出完成时间
                } catch (IOException e) {
                    logger.error("",e);
                }
            }
        });
        return new ResponseEntity(new ResultVO<>(resultDto) ,HttpStatus.OK);
    }

    private void refreshDataSuccToMQ(String organSign) {
        JSONObject json = new JSONObject();
        String[] tables = {"saas_approve_history_detail"};
        json.put("code", "sync");
        json.put("tables", tables);

        messagePushApi.sendMsgByOrganSign(organSign, json.toJSONString());
    }

    private void pushProviderMessToMQ(String organSign) {
        JSONObject json = new JSONObject();
        String[] tables = {"saas_provider_baseinfo"};
        json.put("code", "sync");
        json.put("tables", tables);

        messagePushApi.sendMsgByOrganSign(organSign, json.toJSONString());
    }

    private void getProviderDict(Model model,String organSign) {
        List<SystemDictDto> providers = getSystemDict(DictConstant.providerCompanyType, organSign);
        model.addAttribute("providers", providers);
    }

    private  String toJsonFromObj(Object obj) {
        String outJson ="";
        try {
            ObjectMapper mapper = new ObjectMapper();
            mapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY );
            outJson = mapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            logger.error("",e);
        }
        return outJson;
    }


    @Override
    public ResponseEntity<ResultVO> rereshProviderChongfu(HttpServletRequest request, @Valid @RequestBody ProductRefreshVo refreshVo) {
        if(refreshVo == null || StringUtils.isEmpty(refreshVo.getOrganSigns())){
            return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.ERROR,"参数列表不能为空"),HttpStatus.OK);
        }
        String[] organSigns = refreshVo.getOrganSigns().split(",");
        if(organSigns.length > 0){
            for(String organSign:organSigns){
                List<ProviderDto> deleteOrganSigns = new ArrayList();//待确认禁用或者删除供应商列表
                //重复处理单个机构下的重复商品数据,调用dubbo方法
                List<ProviderDto> providers =  this.supplierBasicInfoApi.queryProviderByOrganSign(organSign,refreshVo.getStartTime(),refreshVo.getEndTime());
                //判断禁用开关是否打开
                if(refreshVo.getIsExcute() == 1){
                    if(providers != null && providers.size() > 0){
                        for(ProviderDto dto :providers){
                            supplierBasicInfoApi.resetProviderIsUsed(dto.getId(),organSign,(byte)0);
                        }
                    }
                }

            }
        }
        return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.SUCCESS,"执行成功"),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> queryNoPage(HttpServletRequest request, @ApiParam(value = "供应商查询对象" ,required=false )  @Valid @RequestBody ProviderQueryVo providerQueryVo) {
        String organSign = request.getHeader("organSign");
        String modelJson = request.getHeader("commonRequestModel");
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                organSign = model.getHeadquartersOrganSign();
            }
        }
        //获取登录名称
        ProviderQueryDto providerQueryDto = new ProviderQueryDto();
        if(providerQueryVo != null){
            BeanUtils.copyProperties(providerQueryVo,providerQueryDto);
        }
        List<ProviderDto> providers =  this.supplierBasicInfoApi.queryProviderByParams(providerQueryDto,organSign);
        return new ResponseEntity(new ResultVO<>(providers) ,HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> queryEffectiveAndExpireCount(HttpServletRequest request) {
        SupplierCount supplierCount = new SupplierCount();
        supplierCount.setEffectiveCount(0);
        supplierCount.setExpireCount(0);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(10000);
        pageInfo.setPageNum(1);
        String organSign = request.getHeader("organSign");
        String modelJson = request.getHeader("commonRequestModel");
        boolean isHeadQuarters = false;//默认否
        logger.info("门店本身机构号queryProvider organSign:{}",organSign);
        ProviderQueryDto providerQueryDto = new ProviderQueryDto();
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            providerQueryDto.setModel(model.getBizModel());
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel())){//连锁
                if(DrugstoreTypeEnum.HEADQUARTERS.toEquals(model.getOrganSignType())){//总部
                    isHeadQuarters = true;
                }else{//连锁门店
                    organSign = model.getHeadquartersOrganSign();
                }
            }
        }
        PageInfo<ProviderDto> info = supplierBasicInfoApi.queryProviderSort(pageInfo, providerQueryDto, organSign);
        if (info == null) {
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(supplierCount),HttpStatus.OK);
        }
        if (CollectionUtils.isEmpty(info.getList())) {
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(supplierCount),HttpStatus.OK);
        }
        AtomicInteger expireCount= new AtomicInteger(0);
        AtomicInteger effectiveCount= new AtomicInteger(0);
        info.getList().parallelStream().forEach(providerDto -> {
            if (providerDto.getExpireYn().intValue()==1) {
               expireCount.getAndIncrement();
            }
            if (providerDto.getAboutToExpire().intValue()==1) {
               effectiveCount.getAndIncrement();
            }
        });
        supplierCount.setEffectiveCount(effectiveCount.get());
        supplierCount.setExpireCount(expireCount.get());
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(supplierCount),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> deleteProviderSalesById(HttpServletRequest request, Long id) {
        int i = supplierBasicInfoApi.deleteProviderSales(id);
        return new ResponseEntity(new ResultVO<>(i) ,HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getProviderSales(@RequestHeader(value = "organSign", required = true) String organSign, @ApiParam(value = "供应商查询对象" ,required=true )  @Valid @RequestBody ProviderQueryVo providerQueryVo) {
        providerQueryVo.setOrganSign(organSign);
        if(org.springframework.util.StringUtils.isEmpty(providerQueryVo.getPref())){
            logger.error("======参数不合法");
            return  new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, null),HttpStatus.OK);
        }
        ResultVO result= supplierBasicInfoApi.getProviderSales(providerQueryVo.getPref(),providerQueryVo.getOrganSign());
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> refreshProviderSales( Integer beginId, Integer endId) {
            Integer newEndId=endId+1;
            for (int i = beginId; i < newEndId; i++) {
                long id = Long.parseLong(Integer.toString(i));
                ProviderVoDto voDto= supplierBasicInfoApi.getProviderById(id);
                if(voDto!=null){
                    ProviderExtVoDto providerExtVo = voDto.getProviderExtVo();
                   if(providerExtVo!=null&&(!StringUtils.isEmpty(providerExtVo.getIdentityCard())||!StringUtils.isEmpty(providerExtVo.getSalesPersonnel()))){
                       ProviderSalesDto providerSalesDto = new ProviderSalesDto();
                       if(!StringUtils.isEmpty(providerExtVo.getIdentityCard())){
                           providerSalesDto.setIdCard(providerExtVo.getIdentityCard());
                       }else{
                           providerSalesDto.setIdCard("");
                       }
                       if(!StringUtils.isEmpty(providerExtVo.getSalesPersonnel())){
                           providerSalesDto.setSalesName(providerExtVo.getSalesPersonnel());
                       }else{
                           providerSalesDto.setSalesName("");
                       }
                       providerSalesDto.setProviderPref(voDto.getPref());
                       providerSalesDto.setAuthorizedScope("");
                       providerSalesDto.setAuthorizedArea("");
                       providerSalesDto.setOrganSign(voDto.getOrganSign());
                       try{
                           supplierBasicInfoApi.saveSale(providerSalesDto);
                           logger.info("第 i:{}条 销售人员插入成功  providerSalesDto:{}",i,JSON.toJSONString(providerSalesDto));
                       }catch (Exception e){
                           logger.error("第 i:{}条 销售人员插入失败  e:{}",i,e);

                       }


                   }
                }
            }

        return new ResponseEntity<>(ResultVO.createSuccess(),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> refreshQualificationInfos(Integer beginId, Integer endId) {

        Integer newEndId=endId+1;
        int a=0;
        for (int i = beginId; i < newEndId; i++) {
            long id = Long.parseLong(Integer.toString(i));
            ProviderVoDto voDto= supplierBasicInfoApi.getProviderById(id);
            if(voDto!=null) {
                String organSign = voDto.getOrganSign();
                SaaSDrugstoreDto drugstore = drugstoreApi.getDrugstoreByOrganSign(organSign);
                if (drugstore != null && drugstore.getBizModel() == DrugstoreBizModelEnum.CHAIN_STORE.getKey() && drugstore.getOrganSignType() == DrugstoreTypeEnum.HEADQUARTERS.getKey()) {
                    //多次调用用户接口查询经营范围对应的证书
                    String businessScope = voDto.getBusinessScope();
                    if (!StringUtils.isEmpty(businessScope)) {
                        List<Integer> integgerList=new ArrayList<>();
                        List<String> stringList = Arrays.asList(businessScope.split(","));
                        stringList.stream().forEach(item->{
                            integgerList.add(Integer.parseInt(item));
                        });

                        List<Integer> bussIds = Arrays.asList(
                                DictConstant.BUSINESS_SCOPE);
                        List<SystemDictDto> dictDtos= new ArrayList<>();
                        SystemDictQueryDto queryDto = new SystemDictQueryDto();
                        queryDto.setBusinessIdList(bussIds);
                        queryDto.setPageNo(1);
                        queryDto.setPageSize(1000);
                        queryDto.setYn((byte)1);
                        PageInfo<SystemDictDto> pageByCondition = systemDictApi.findPageByCondition(queryDto, organSign);
                        if(pageByCondition!=null&&!org.springframework.util.CollectionUtils.isEmpty(pageByCondition.getList())){
                            dictDtos.addAll(pageByCondition.getList());
                            int pages = pageByCondition.getPages();
                            for (int j=1;j<pages;j++){
                                queryDto.setPageNo(j+1);
                                PageInfo<SystemDictDto> pageInfo = systemDictApi.findPageByCondition(queryDto, organSign);
                                if(pageInfo!=null&&!org.springframework.util.CollectionUtils.isEmpty(pageInfo.getList())) {
                                    dictDtos.addAll(pageInfo.getList());
                                }
                            }
                        }
                    Map<Integer, SystemDictDto> collect1 = dictDtos.stream().collect(Collectors.toMap(SystemDictDto::getId, vo -> vo));


                    ArrayList<CertificateBusinessScopeDto> certificateList = new ArrayList<>();
                    QueryCertificateTypeParam queryCertificateTypeParam = new QueryCertificateTypeParam();
                    queryCertificateTypeParam.setIds(integgerList);
                    queryCertificateTypeParam.setPageNum(1);
                    queryCertificateTypeParam.setPageSize(1000);
                    ResultVO<PageInfo<CertificateBusinessScopeDto>> certificateType = certificateBusinessScopeApi.getCertificateType(queryCertificateTypeParam);
                    PageInfo<CertificateBusinessScopeDto> result = certificateType.getResult();
                    if (result != null && !CollectionUtils.isEmpty(result.getList())) {
                        certificateList.addAll(result.getList());
                        int pages = result.getPages();
                        for (int k = 1; k < pages; k++) {
                            queryCertificateTypeParam.setPageNum(k + 1);
                            ResultVO<PageInfo<CertificateBusinessScopeDto>> certificateType2 = certificateBusinessScopeApi.getCertificateType(queryCertificateTypeParam);
                            PageInfo<CertificateBusinessScopeDto> result2 = certificateType2.getResult();
                            if (result2 != null && !CollectionUtils.isEmpty(result2.getList())) {
                                certificateList.addAll(result2.getList());
                            }
                        }
                    }
                        ArrayList<Integer> typeList = new ArrayList<>();
                        for(CertificateBusinessScopeDto dto: certificateList){
                            typeList.add(dto.getType());
                        }
                        List<Integer> collect = typeList.stream().distinct().collect(Collectors.toList());
                        List<QualificationInfoDto> qualificationInfos = new ArrayList<>();
                            //营业执照
                        String businessLicense = voDto.getBusinessLicense();
                        Date expirationDate = voDto.getExpirationDate();
                        Byte expirationDateType = voDto.getExpirationDateType();
                        if (!StringUtils.isEmpty(businessLicense)) {
                            QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                            qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_Business_License.getValue());
                            qualificationInfoDto.setNumber(businessLicense);
                            qualificationInfoDto.setExpireType(expirationDateType);
                            StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_Business_License.getValue(), certificateList);
                            qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                            if(expirationDate!=null){
                                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                                qualificationInfoDto.setEndDate(simpleDateFormat.format(expirationDate));

                            }
                            qualificationInfos.add(qualificationInfoDto);
                        }else{
                            if(collect.contains((int)QualificationTypeEnum.Supplier_Business_License.getValue())){
                                QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_Business_License.getValue());
                                StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_Business_License.getValue(), certificateList);
                                qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                qualificationInfos.add(qualificationInfoDto);
                            }
                        }

                        ProviderExtVoDto providerExtVo = voDto.getProviderExtVo();
                        if(providerExtVo!=null){
                            //药品经营许可证
                            String drugBusinessPermit = providerExtVo.getDrugBusinessPermit();
                            String drugBusinessExpiredStart = providerExtVo.getDrugBusinessExpiredStart();
                            String drugBusinessExpiredDate = providerExtVo.getDrugBusinessExpiredDate();
                            if(!StringUtils.isEmpty(drugBusinessPermit)||!StringUtils.isEmpty(drugBusinessExpiredStart)||!StringUtils.isEmpty(drugBusinessExpiredDate)){
                                QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_Drug_Business_Permit.getValue());
                                qualificationInfoDto.setNumber(drugBusinessPermit);
                                StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_Drug_Business_Permit.getValue(), certificateList);
                                qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                qualificationInfoDto.setStartDate(drugBusinessExpiredStart);
                                qualificationInfoDto.setEndDate(drugBusinessExpiredDate);
                                qualificationInfos.add(qualificationInfoDto);
                            }else {
                                if(collect.contains((int)QualificationTypeEnum.Supplier_Drug_Business_Permit.getValue())){
                                    QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                    qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_Drug_Business_Permit.getValue());
                                    StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_Drug_Business_Permit.getValue(), certificateList);
                                    qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                    qualificationInfos.add(qualificationInfoDto);
                                }
                            }
                             //GSP证书
                            String GSP = providerExtVo.getgSPCertificate();
                            String gSPCertificateExpiredStart = providerExtVo.getgSPCertificateExpiredStart();
                            String gSPCertificateExpiredDate = providerExtVo.getgSPCertificateExpiredDate();
                            if(!StringUtils.isEmpty(GSP)||!StringUtils.isEmpty(gSPCertificateExpiredStart)||!StringUtils.isEmpty(gSPCertificateExpiredDate)){
                                QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_GSP.getValue());
                                qualificationInfoDto.setNumber(GSP);
                                StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_GSP.getValue(), certificateList);
                                qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                qualificationInfoDto.setStartDate(gSPCertificateExpiredStart);
                                qualificationInfoDto.setEndDate(gSPCertificateExpiredDate);
                                qualificationInfos.add(qualificationInfoDto);
                            }else {
                                if(collect.contains((int)QualificationTypeEnum.Supplier_GSP.getValue())){
                                    QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                    qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_GSP.getValue());
                                    StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_GSP.getValue(), certificateList);
                                    qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                    qualificationInfos.add(qualificationInfoDto);
                                }
                            }
                            //质量保证协议
                            String qualityAgreement = providerExtVo.getQualityAgreement();
                            String qualityAgreementExpiredDate = providerExtVo.getQualityAgreementExpiredDate();
                            if(!StringUtils.isEmpty(qualityAgreement)||!StringUtils.isEmpty(qualityAgreementExpiredDate)){
                                QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_Quality_Agreement.getValue());
                                qualificationInfoDto.setNumber(qualityAgreement);
                                StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_Quality_Agreement.getValue(), certificateList);
                                qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                qualificationInfoDto.setEndDate(qualityAgreementExpiredDate);
                                qualificationInfos.add(qualificationInfoDto);
                            }else{
                                if(collect.contains((int)QualificationTypeEnum.Supplier_Quality_Agreement.getValue())){
                                    QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                    qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_Quality_Agreement.getValue());
                                    StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_Quality_Agreement.getValue(), certificateList);
                                    qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                    qualificationInfos.add(qualificationInfoDto);
                                }

                            }
                            //GMP证书
                            String GMP = providerExtVo.getgMPPCertificate();
                            String gMPPCertificateExpiredDate = providerExtVo.getgMPPCertificateExpiredDate();
                            if(!StringUtils.isEmpty(GMP)||!StringUtils.isEmpty(gMPPCertificateExpiredDate)){
                                QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_GMP.getValue());
                                qualificationInfoDto.setNumber(GMP);
                                StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_GMP.getValue(), certificateList);
                                qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                qualificationInfoDto.setEndDate(gMPPCertificateExpiredDate);
                                qualificationInfos.add(qualificationInfoDto);
                            }else{
                                if(collect.contains((int)QualificationTypeEnum.Supplier_GMP.getValue())){
                                    QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                    qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_GMP.getValue());
                                    StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_GMP.getValue(), certificateList);
                                    qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                    qualificationInfos.add(qualificationInfoDto);
                                }

                            }
                            //药品生产许可证
                            String drugProducePermit = providerExtVo.getDrugProducePermit();
                            String drugProducePermitExpiredDate = providerExtVo.getDrugProducePermitExpiredDate();
                            if(!StringUtils.isEmpty(drugProducePermit)||!StringUtils.isEmpty(drugProducePermitExpiredDate)){
                                QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_Drug_Produce_Permit.getValue());
                                qualificationInfoDto.setNumber(drugProducePermit);
                                StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_Drug_Produce_Permit.getValue(), certificateList);
                                qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                qualificationInfoDto.setEndDate(drugProducePermitExpiredDate);
                                qualificationInfos.add(qualificationInfoDto);
                            }else{
                                if(collect.contains((int)QualificationTypeEnum.Supplier_Drug_Produce_Permit.getValue())){
                                    QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                    qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_Drug_Produce_Permit.getValue());
                                    StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_Drug_Produce_Permit.getValue(), certificateList);
                                    qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                    qualificationInfos.add(qualificationInfoDto);
                                }

                            }
                            //食品经营许可证
                            String foodBusinessPermit = providerExtVo.getFoodBusinessPermit();
                            String foodBusinessPermitExpiredDate = providerExtVo.getFoodBusinessPermitExpiredDate();
                            if(!StringUtils.isEmpty(foodBusinessPermit)||!StringUtils.isEmpty(foodBusinessPermitExpiredDate)){
                                QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_Food_Business_Permit.getValue());
                                qualificationInfoDto.setNumber(foodBusinessPermit);
                                StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_Food_Business_Permit.getValue(), certificateList);
                                qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                qualificationInfoDto.setEndDate(foodBusinessPermitExpiredDate);
                                qualificationInfos.add(qualificationInfoDto);
                            }else{
                                if(collect.contains((int)QualificationTypeEnum.Supplier_Food_Business_Permit.getValue())){
                                    QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                    qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_Food_Business_Permit.getValue());
                                    StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_Food_Business_Permit.getValue(), certificateList);
                                    qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                    qualificationInfos.add(qualificationInfoDto);
                                }

                            }
                            //卫生许可证（化妆品生产许可证）
                            String hygienicPermit = providerExtVo.getHygienicPermit();
                            String hygienicLicenseExpiredDate = providerExtVo.getHygienicLicenseExpiredDate();
                            if(!StringUtils.isEmpty(hygienicPermit)||!StringUtils.isEmpty(hygienicLicenseExpiredDate)){
                                QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_Hygienic_Permit.getValue());
                                qualificationInfoDto.setNumber(hygienicPermit);
                                StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_Hygienic_Permit.getValue(), certificateList);
                                qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                qualificationInfoDto.setEndDate(hygienicLicenseExpiredDate);
                                qualificationInfos.add(qualificationInfoDto);
                            }else{
                                if(collect.contains((int)QualificationTypeEnum.Supplier_Hygienic_Permit.getValue())){
                                    QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                    qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_Hygienic_Permit.getValue());
                                    StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_Hygienic_Permit.getValue(), certificateList);
                                    qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                    qualificationInfos.add(qualificationInfoDto);
                                }

                            }
                            //食品流通许可证（食品生产许可证）
                            String foodCirculationPermit = providerExtVo.getFoodCirculationPermit();
                            String foodCirculationPermitExpiredDate = providerExtVo.getFoodCirculationPermitExpiredDate();
                            if(!StringUtils.isEmpty(foodCirculationPermit)||!StringUtils.isEmpty(foodCirculationPermitExpiredDate)){
                                QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_Food_Circulation_Permit.getValue());
                                qualificationInfoDto.setNumber(foodCirculationPermit);
                                StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_Food_Circulation_Permit.getValue(), certificateList);
                                qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                qualificationInfoDto.setEndDate(foodCirculationPermitExpiredDate);
                                qualificationInfos.add(qualificationInfoDto);
                            }else{
                                if(collect.contains((int)QualificationTypeEnum.Supplier_Food_Circulation_Permit.getValue())){
                                    QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                    qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_Food_Circulation_Permit.getValue());
                                    StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_Food_Circulation_Permit.getValue(), certificateList);
                                    qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                    qualificationInfos.add(qualificationInfoDto);
                                }

                            }
                            //医疗器械经营许可证
                            String medicalDeviceBusinessPermit = providerExtVo.getMedicalDeviceBusinessPermit();
                            String medicalDeviceBusinessPermitExpiredDate = providerExtVo.getMedicalDeviceBusinessPermitExpiredDate();
                            if(!StringUtils.isEmpty(medicalDeviceBusinessPermit)||!StringUtils.isEmpty(medicalDeviceBusinessPermitExpiredDate)){
                                QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_Medical_Device_Business_Permit.getValue());
                                qualificationInfoDto.setNumber(medicalDeviceBusinessPermit);
                                StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_Medical_Device_Business_Permit.getValue(), certificateList);
                                qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                qualificationInfoDto.setEndDate(medicalDeviceBusinessPermitExpiredDate);
                                qualificationInfos.add(qualificationInfoDto);
                            }else{
                                if(collect.contains((int)QualificationTypeEnum.Supplier_Medical_Device_Business_Permit.getValue())){
                                    QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                    qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_Medical_Device_Business_Permit.getValue());
                                    StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_Medical_Device_Business_Permit.getValue(), certificateList);
                                    qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                    qualificationInfos.add(qualificationInfoDto);
                                }

                            }
                            //二类器械备案凭证 (第二类医疗器械经营备案凭证)
                            String secondDeviceRecord = providerExtVo.getSecondDeviceRecord();
                            String secondDeviceRecordExpiredDate = providerExtVo.getSecondDeviceRecordExpiredDate();
                            if(!StringUtils.isEmpty(secondDeviceRecord)||!StringUtils.isEmpty(secondDeviceRecordExpiredDate)){
                                QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_Second_Device_Record.getValue());
                                qualificationInfoDto.setNumber(secondDeviceRecord);
                                StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_Second_Device_Record.getValue(), certificateList);
                                qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                qualificationInfoDto.setEndDate(secondDeviceRecordExpiredDate);
                                qualificationInfos.add(qualificationInfoDto);
                            }else{
                                if(collect.contains((int)QualificationTypeEnum.Supplier_Second_Device_Record.getValue())){
                                    QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                    qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_Second_Device_Record.getValue());
                                    StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_Second_Device_Record.getValue(), certificateList);
                                    qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                    qualificationInfos.add(qualificationInfoDto);
                                }

                            }


                        }else{
                            if(collect.contains((int)QualificationTypeEnum.Supplier_Drug_Business_Permit.getValue())){
                                QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_Drug_Business_Permit.getValue());
                                StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_Drug_Business_Permit.getValue(), certificateList);
                                qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                qualificationInfos.add(qualificationInfoDto);
                            }
                            if(collect.contains((int)QualificationTypeEnum.Supplier_Quality_Agreement.getValue())){
                                QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_Quality_Agreement.getValue());
                                StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_Quality_Agreement.getValue(), certificateList);
                                qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                qualificationInfos.add(qualificationInfoDto);
                            }
                            if(collect.contains((int)QualificationTypeEnum.Supplier_GMP.getValue())){
                                QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_GMP.getValue());
                                StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_GMP.getValue(), certificateList);
                                qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                qualificationInfos.add(qualificationInfoDto);
                            }
                            if(collect.contains((int)QualificationTypeEnum.Supplier_GMP.getValue())){
                                QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_GMP.getValue());
                                StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_GMP.getValue(), certificateList);
                                qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                qualificationInfos.add(qualificationInfoDto);
                            }
                            if(collect.contains((int)QualificationTypeEnum.Supplier_Drug_Produce_Permit.getValue())){
                                QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_Drug_Produce_Permit.getValue());
                                StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_Drug_Produce_Permit.getValue(), certificateList);
                                qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                qualificationInfos.add(qualificationInfoDto);
                            }
                            if(collect.contains((int)QualificationTypeEnum.Supplier_Food_Business_Permit.getValue())){
                                QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_Food_Business_Permit.getValue());
                                StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_Food_Business_Permit.getValue(), certificateList);
                                qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                qualificationInfos.add(qualificationInfoDto);
                            }
                            if(collect.contains((int)QualificationTypeEnum.Supplier_Hygienic_Permit.getValue())){
                                QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_Hygienic_Permit.getValue());
                                StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_Hygienic_Permit.getValue(), certificateList);
                                qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                qualificationInfos.add(qualificationInfoDto);
                            }
                            if(collect.contains((int)QualificationTypeEnum.Supplier_Food_Circulation_Permit.getValue())){
                                QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_Food_Circulation_Permit.getValue());
                                StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_Food_Circulation_Permit.getValue(), certificateList);
                                qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                qualificationInfos.add(qualificationInfoDto);
                            }
                            if(collect.contains((int)QualificationTypeEnum.Supplier_Medical_Device_Business_Permit.getValue())){
                                QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_Medical_Device_Business_Permit.getValue());
                                StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_Medical_Device_Business_Permit.getValue(), certificateList);
                                qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                qualificationInfos.add(qualificationInfoDto);
                            }
                            if(collect.contains((int)QualificationTypeEnum.Supplier_Second_Device_Record.getValue())){
                                QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                                qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_Second_Device_Record.getValue());
                                StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_Second_Device_Record.getValue(), certificateList);
                                qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                                qualificationInfos.add(qualificationInfoDto);
                            }

                        }
                        if(collect.contains((int)QualificationTypeEnum.Supplier_Others.getValue())){
                            QualificationInfoDto qualificationInfoDto = new QualificationInfoDto();
                            qualificationInfoDto.setQualificationType(QualificationTypeEnum.Supplier_Others.getValue());
                            StringBuilder businessScopes = getBusinessScopes((int) QualificationTypeEnum.Supplier_Others.getValue(), certificateList);
                            for(  Integer inte:integgerList){
                                SystemDictDto systemDictDto = collect1.get(inte);
                                if(collect1.containsKey(inte)&&!StringUtils.isEmpty(systemDictDto.getOrganSign())){
                                    businessScopes.append(",").append(inte);

                                }
                            }
                            qualificationInfoDto.setBusinessScopes(businessScopes.toString());
                            qualificationInfos.add(qualificationInfoDto);
                        }
                        try{
                            int status=supplierBasicInfoApi.updateQualificationInfos(voDto.getPref(),qualificationInfos,organSign);
                            //同步数据到POS
                            if(status>0) {
                                List<SaaSDrugstoreDto> drugs = drugstoreApi.getDrugstoreByHeadquartersOrganSign(organSign);
                                if (!CollectionUtils.isEmpty(drugs)) {
                                    for (SaaSDrugstoreDto dto : drugs) {
                                        pushProviderMessToMQ(dto.getOrganSign());
                                    }
                                    logger.info("第 i:{}条供应商 经营范围刷数据同步POS成功",i);
                                }
                            }
                            logger.info("第 i:{}条供应商 经营范围刷数据成功  qualificationInfos:{}",i,JSON.toJSONString(qualificationInfos));
                        }catch (Exception e){
                            logger.error("第 i:{}条 供应商经营范围刷数据失败  e:{}",i,e);

                        }
                    }

                }else {
                    a++;
                    logger.info("第 i:{}条 供应商经营范围不需要刷,共a:{}条不需要刷",i,a);

                }

            }
        }
        return new ResponseEntity<>(ResultVO.createSuccess(),HttpStatus.OK);
    }

    /**
     * 如果是门店机构号，则获取对应的总部机构号
     *
     * @param organSign
     * @return
     */
    private String getHeadquartersOrg(String organSign) {
        String headquartersOrg = "";
        SaaSDrugstoreDto drugstore = drugstoreApi.getDrugstoreByOrganSign(organSign);
        // 兼容连锁门店，根据连锁门店获取总部的机构号
        if (drugstore != null && drugstore.getBizModel() == DrugstoreBizModelEnum.CHAIN_STORE.getKey()
                && drugstore.getOrganSignType() == DrugstoreTypeEnum.DRUGSTORE.getKey()) {
            headquartersOrg = drugstore.getHeadquartersOrganSign();
            logger.info("当前机构为连锁门店organSign:{},总部机构headquartersOrg:{}",organSign,headquartersOrg);
        }
        return headquartersOrg;
    }

    /**
     * 根据businessId查询对应字典数据
     *
     * @param businessId
     * @return
     */
    private List<SystemDictDto> getSystemDict(Integer businessId,String organSign) {

        String headquartersOrg = getHeadquartersOrg(organSign);
        if (!StringUtils.isEmpty(headquartersOrg)) {
            organSign = headquartersOrg;
        }
        List<SystemDictDto> dictDtos= new ArrayList<>();
        SystemDictQueryDto queryDto = new SystemDictQueryDto();
        queryDto.setBusinessId(businessId);
        queryDto.setStatus((byte)1);
        queryDto.setPageNo(1);
        queryDto.setPageSize(1000);
        queryDto.setYn((byte)1);
        PageInfo<SystemDictDto> pageByCondition = systemDictApi.findPageByCondition(queryDto, organSign);
        if(pageByCondition!=null&&!CollectionUtils.isEmpty(pageByCondition.getList())){
            dictDtos.addAll(pageByCondition.getList());
            int pages = pageByCondition.getPages();
            for (int i=1;i<pages;i++){
                queryDto.setPageNo(i+1);
                PageInfo<SystemDictDto> pageInfo = systemDictApi.findPageByCondition(queryDto, organSign);
                if(pageInfo!=null&&!CollectionUtils.isEmpty(pageInfo.getList())) {
                    dictDtos.addAll(pageInfo.getList());
                }
            }
        }
        return  dictDtos;
    }

    /**
     * 根据证书类型拼接对应的BusinessScope字符串（逗号分隔）
     * @param certificateType
     * @param certificateList
     * @return
     */
    private StringBuilder getBusinessScopes(int certificateType,ArrayList<CertificateBusinessScopeDto> certificateList){
        StringBuilder businessScopes = new StringBuilder();
        if(!CollectionUtils.isEmpty(certificateList)){
           for(int j=0; j<certificateList.size();j++ ){
               if(certificateType==(certificateList.get(j).getType())){
                   businessScopes.append(certificateList.get(j).getId()).append(",");
               }
           }
           if(businessScopes.length()>0){
               businessScopes.deleteCharAt(businessScopes.length()-1);
           }
       }
       return  businessScopes;
    }

	////<editor-fold desc="码上放心平台">
    /**
     * 码上放心-获取开关标识(查询当前药店机构)
     * @param organSign
     * @return
     */
    @Override
    public ResponseEntity<ResultVO> getEnableMsfx(@RequestHeader(value = "organSign", required = true) String organSign) {
        SystemConfigDto systemConfigDto = sysConfigApi.querySystemConfig(organSign);
        SaaSDrugstoreDto drugstore = drugstoreApi.getDrugstoreByOrganSign(organSign);
        Byte productTraceUploadYn = 0;//0未启用  1启用
        Byte productTraceUploadNonMsfxYn = 0;//0未启用  1启用
        Byte traceCodeSaleEntryYn = 0;//0未启用  1启用
        if(systemConfigDto != null) {
            //待对接获取码上放心开关标识接口
            productTraceUploadYn = systemConfigDto.getProductTraceUploadYn(); //0未启用  1启用
            //待对接获取非码上放心开关标识接口
            //todo 此处单体和连锁的盘点、拆零、报损报溢共用的同一页面，连锁目前没有做这些功能，暂时不开启追溯码录入
            if(!drugstore.getBizModel().equals((byte)2)){
                productTraceUploadNonMsfxYn = systemConfigDto.getProductTraceUploadNonMsfxYn();
            }
            //0未启用  1启用
            //追溯码销售录入开关标识接口
            traceCodeSaleEntryYn = systemConfigDto.getTraceCodeSaleEntryYn();//0未启用  1启用
        }else{
            logger.info("systemConfigDto为空,productTraceUploadYn默认未开启");
        }
        logger.info("querySystemConfig organSign={}, productTraceUploadYn={}", organSign, productTraceUploadYn);
        Map result = new HashMap<String, Object>();
        result.put("productTraceUploadYn", productTraceUploadYn);
        result.put("productTraceUploadNonMsfxYn",productTraceUploadNonMsfxYn);
        result.put("traceCodeSaleEntryYn", traceCodeSaleEntryYn);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(result), HttpStatus.OK);
    }

    /**
     * 码上放心-码上放心机构标识(策略-> 新增是查询/名称变更时查询)
     * @param organSign
     * @param queryReqVo
     * @return
     */
    @Override
    public ResponseEntity<ResultVO> getMsfxOrganSignInfo(@RequestHeader(value = "organSign", required = true) String organSign, @ApiParam(value = "码上放心机构标识-查询VO" ,required=true ) @Valid @RequestBody MsfxOrganSignInfoQueryReqVo queryReqVo) {
        //待对接获取码上放心标识接口
        PUserEntInfoDto pUserEntInfoDto = supplierBasicInfoApi.getMsfxOrganSignInfo(organSign, queryReqVo.getPref(), queryReqVo.getProviderName());
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(pUserEntInfoDto), HttpStatus.OK);
    }

    /**
     * 码上放心-配合Apollo刷所有指定的机构
     * @return
     */
    @Override
    public ResponseEntity<ResultVO> refreshProviderMsfxInfoByApollo() {
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(supplierBasicInfoApi.refreshProviderMsfxInfoByApollo()), HttpStatus.OK);
    }
    //</editor-folder>
}
