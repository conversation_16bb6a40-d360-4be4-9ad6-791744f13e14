package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.dto.*;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T18:13:39.358+08:00")
@RequestMapping("/product/abandons")
@Api(value = "YbmlProduct", description = "废弃商品相关API接口")
public interface AbandonApi {

    @ApiOperation(value = "废弃商品接口", notes = "废弃商品接口", response = ResultVO.class, tags={ "废弃商品接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/abandon")
    ResponseEntity<ResultVO> abandon(HttpServletRequest request, @ApiParam(value = "废弃商品接口", required = true) @RequestBody AbandonProductUpdateDto dto);


    @ApiOperation(value = "批量废弃商品接口", notes = "批量废弃商品接口", response = ResultVO.class, tags={ "废弃商品接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/batchAbandon")
    ResponseEntity<ResultVO> batchAbandon(HttpServletRequest request, @ApiParam(value = "废弃商品接口", required = true) @RequestBody ProductDto product);


    @ApiOperation(value = "废弃商品查询接口", notes = "废弃商品查询接口", response = ResultVO.class, tags={ "废弃商品查询接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/pageSearch")
    ResponseEntity<ResultVO> pageSearch(HttpServletRequest request, @ApiParam(value = "废弃商品查询接口", required = true) @RequestBody AbandonProductQueryDto queryDto);


    @ApiOperation(value = "恢复废弃商品接口", notes = "恢复废弃商品接口", response = ResultVO.class, tags={ "恢复废弃商品接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/recover")
    ResponseEntity<ResultVO> recover(HttpServletRequest request, @ApiParam(value = "废弃商品接口", required = true) @RequestBody AbandonProductUpdateDto dto);


    @ApiOperation(value = "恢复废弃商品接口（待审批）", notes = "恢复废弃商品接口（待审批）", response = ResultVO.class, tags={ "恢复废弃商品接口（待审批）", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/recoverApply")
    ResponseEntity<ResultVO> recoverApply(HttpServletRequest request, @ApiParam(value = "废弃商品接口（待审批）", required = true) @RequestBody RecoverProductApplyDto dto);


    @ApiOperation(value = "恢复废弃商品接口（审批）", notes = "恢复废弃商品接口（审批）", response = ResultVO.class, tags={ "恢复废弃商品接口（审批）", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/recoverAudit")
    ResponseEntity<ResultVO> recoverAudit(HttpServletRequest request, @ApiParam(value = "废弃商品接口（审批）", required = true) @RequestBody RecoverProductAuditDto dto);
}
