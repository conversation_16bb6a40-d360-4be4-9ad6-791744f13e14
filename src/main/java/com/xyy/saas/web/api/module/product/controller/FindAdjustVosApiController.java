package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.web.api.module.product.model.InventoryLotNumberAdjustVo;
import io.swagger.annotations.ApiParam;
import org.springframework.http.HttpRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-29T17:03:47.479+08:00")

@Controller
public class FindAdjustVosApiController implements FindAdjustVosApi {

    public ResponseEntity<ResultVO> findAdjustVos(@ApiParam(value = "批号库存调整Vo" ,required=true ) HttpRequest request, @Valid @RequestBody InventoryLotNumberAdjustVo inventoryLotNumberAdjustVo) {

        return new ResponseEntity<ResultVO>(HttpStatus.OK);
    }

}
