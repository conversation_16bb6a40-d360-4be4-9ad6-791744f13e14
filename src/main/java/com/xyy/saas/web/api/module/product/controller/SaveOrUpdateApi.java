/**
 * NOTE: This class is auto generated by the swagger code generator program (2.2.3).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.web.api.module.product.model.InventoryLotNumberAdjustVo;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-29T17:03:47.479+08:00")
@RequestMapping("/product")
@Api(value = "saveOrUpdate", description = "the saveOrUpdate API")
public interface SaveOrUpdateApi {

    @ApiOperation(value = "商品批号库存调整保存", notes = "商品批号库存调整保存", response = ResultVO.class, tags={ "saveOrUpdateLotNumberAdjust", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "Invalid input", response = ResultVO.class) })
    
    @RequestMapping(value = "/saveOrUpdate",
        produces = { "application/xml", "application/json" }, 
        consumes = { "application/json", "application/xml" },
        method = RequestMethod.POST)
    ResponseEntity<ResultVO> saveOrUpdate(@ApiParam(value = "商品批号库存调整Vo", required = true) @RequestHeader("userName") String userName, @RequestHeader("organSign") String organSign, @Valid @RequestBody InventoryLotNumberAdjustVo inventoryLotNumberAdjustVo);

}
