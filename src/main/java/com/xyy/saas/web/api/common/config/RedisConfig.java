package com.xyy.saas.web.api.common.config;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

/**
 *
 */
@Configuration
//@EnableRedisHttpSession(maxInactiveIntervalInSeconds= 3600, redisNamespace = "web:session")
public class RedisConfig {
    private static Logger logger = LoggerFactory.getLogger(RedisConfig.class);

    @Value("${spring.redis.host}")
    private String host;

    @Value("${spring.redis.port}")
    private int port;

    @Value("${spring.redis.timeout}")
    private int timeout;

    @Value("${spring.redis.pool.max-idle}")
    private int maxIdle;

    @Value("${spring.redis.pool.max-wait}")
    private long maxWaitMillis;

    @Value("${spring.redis.password}")
    private String password;

    @Bean
    public JedisPool jedisPool() {
        logger.info("###【redis注入成功】[host/prot] => {}, {}", host, port);
        JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
        jedisPoolConfig.setMaxIdle(maxIdle);
        jedisPoolConfig.setMaxWaitMillis(maxWaitMillis);
        JedisPool jedisPool = StringUtils.isEmpty(password) ?
                new JedisPool(jedisPoolConfig, host, port, timeout):
                new JedisPool(jedisPoolConfig, host, port, timeout, password);
        return jedisPool;
    }

    /*@Bean
    public RedisPageUtil redisCommonUtil(JedisPageUtil jedisPageUtil) {
        RedisPageUtil redisPageUtil = new RedisPageUtil();

        redisPageUtil.setJedisUtil(jedisPageUtil);
        redisPageUtil.setCacheSeconds(86400);

        return redisPageUtil;
    }*/
}