package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * 商品售价调整明细信息实体
 */
@ApiModel(description = "商品售价调整明细信息实体")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

public class PriceAdjustDetailPo implements Serializable {
  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("productPref")
  private String productPref = null;

  @JsonProperty("oldPrice")
  private BigDecimal oldPrice = null;

  @JsonProperty("newPrice")
  private BigDecimal newPrice = null;

  @JsonProperty("oldMemberPrice")
  private BigDecimal oldMemberPrice = null;

  @JsonProperty("newMemberPrice")
  private BigDecimal newMemberPrice = null;

  @JsonProperty("createUser")
  private String createUser = null;

  @JsonProperty("createTime")
  private Date createTime = null;

  @JsonProperty("updateUser")
  private String updateUser = null;

  @JsonProperty("updateTime")
  private Date updateTime = null;

  @JsonProperty("remark")
  private String remark = null;

  @JsonProperty("yn")
  private Byte yn = null;

  @JsonProperty("adjustPref")
  private String adjustPref = null;

  @JsonProperty("status")
  private Byte status = null;

  public PriceAdjustDetailPo id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * 售价调整单明细表ID
   * @return id
  **/
  @ApiModelProperty(value = "售价调整单明细表ID")


  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public PriceAdjustDetailPo productPref(String productPref) {
    this.productPref = productPref;
    return this;
  }

   /**
   * 商品编号
   * @return productPref
  **/
  @ApiModelProperty(value = "商品编号")


  public String getProductPref() {
    return productPref;
  }

  public void setProductPref(String productPref) {
    this.productPref = productPref;
  }

  public PriceAdjustDetailPo oldPrice(BigDecimal oldPrice) {
    this.oldPrice = oldPrice;
    return this;
  }

   /**
   * 旧价格
   * @return oldPrice
  **/
  @ApiModelProperty(value = "旧价格")


  public BigDecimal getOldPrice() {
    return oldPrice;
  }

  public void setOldPrice(BigDecimal oldPrice) {
    this.oldPrice = oldPrice;
  }

  public PriceAdjustDetailPo newPrice(BigDecimal newPrice) {
    this.newPrice = newPrice;
    return this;
  }

   /**
   * 新价格
   * @return newPrice
  **/
  @ApiModelProperty(value = "新价格")


  public BigDecimal getNewPrice() {
    return newPrice;
  }

  public void setNewPrice(BigDecimal newPrice) {
    this.newPrice = newPrice;
  }

  public PriceAdjustDetailPo oldMemberPrice(BigDecimal oldMemberPrice) {
    this.oldMemberPrice = oldMemberPrice;
    return this;
  }

   /**
   * 旧会员价
   * @return oldMemberPrice
  **/
  @ApiModelProperty(value = "旧会员价")


  public BigDecimal getOldMemberPrice() {
    return oldMemberPrice;
  }

  public void setOldMemberPrice(BigDecimal oldMemberPrice) {
    this.oldMemberPrice = oldMemberPrice;
  }

  public PriceAdjustDetailPo newMemberPrice(BigDecimal newMemberPrice) {
    this.newMemberPrice = newMemberPrice;
    return this;
  }

   /**
   * 新会员价
   * @return newMemberPrice
  **/
  @ApiModelProperty(value = "新会员价")


  public BigDecimal getNewMemberPrice() {
    return newMemberPrice;
  }

  public void setNewMemberPrice(BigDecimal newMemberPrice) {
    this.newMemberPrice = newMemberPrice;
  }

  public PriceAdjustDetailPo createUser(String createUser) {
    this.createUser = createUser;
    return this;
  }

   /**
   * 创建人
   * @return createUser
  **/
  @ApiModelProperty(value = "创建人")


  public String getCreateUser() {
    return createUser;
  }

  public void setCreateUser(String createUser) {
    this.createUser = createUser;
  }

  public PriceAdjustDetailPo createTime(Date createTime) {
    this.createTime = createTime;
    return this;
  }

   /**
   * 创建时间
   * @return createTime
  **/
  @ApiModelProperty(value = "创建时间")


  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public PriceAdjustDetailPo updateUser(String updateUser) {
    this.updateUser = updateUser;
    return this;
  }

   /**
   * 更新人
   * @return updateUser
  **/
  @ApiModelProperty(value = "更新人")


  public String getUpdateUser() {
    return updateUser;
  }

  public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
  }

  public PriceAdjustDetailPo updateTime(Date updateTime) {
    this.updateTime = updateTime;
    return this;
  }

   /**
   * 修改时间
   * @return updateTime
  **/
  @ApiModelProperty(value = "修改时间")


  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public PriceAdjustDetailPo remark(String remark) {
    this.remark = remark;
    return this;
  }

   /**
   * 备注
   * @return remark
  **/
  @ApiModelProperty(value = "备注")


  public String getRemark() {
    return remark;
  }

  public void setRemark(String remark) {
    this.remark = remark;
  }

  public PriceAdjustDetailPo yn(Byte yn) {
    this.yn = yn;
    return this;
  }

   /**
   * 逻辑删除 1 有效 0 删除
   * @return yn
  **/
  @ApiModelProperty(value = "逻辑删除 1 有效 0 删除")


  public Byte getYn() {
    return yn;
  }

  public void setYn(Byte yn) {
    this.yn = yn;
  }

  public PriceAdjustDetailPo adjustPref(String adjustPref) {
    this.adjustPref = adjustPref;
    return this;
  }

   /**
   * 售价调整单主表编号
   * @return adjustPref
  **/
  @ApiModelProperty(value = "售价调整单主表编号")


  public String getAdjustPref() {
    return adjustPref;
  }

  public void setAdjustPref(String adjustPref) {
    this.adjustPref = adjustPref;
  }

  public PriceAdjustDetailPo status(Byte status) {
    this.status = status;
    return this;
  }

   /**
   * 审批状态:1--审批通过，2--审批驳回
   * @return status
  **/
  @ApiModelProperty(value = "审批状态:1--审批通过，2--审批驳回")


  public Byte getStatus() {
    return status;
  }

  public void setStatus(Byte status) {
    this.status = status;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PriceAdjustDetailPo priceAdjustDetailPo = (PriceAdjustDetailPo) o;
    return Objects.equals(this.id, priceAdjustDetailPo.id) &&
        Objects.equals(this.productPref, priceAdjustDetailPo.productPref) &&
        Objects.equals(this.oldPrice, priceAdjustDetailPo.oldPrice) &&
        Objects.equals(this.newPrice, priceAdjustDetailPo.newPrice) &&
        Objects.equals(this.oldMemberPrice, priceAdjustDetailPo.oldMemberPrice) &&
        Objects.equals(this.newMemberPrice, priceAdjustDetailPo.newMemberPrice) &&
        Objects.equals(this.createUser, priceAdjustDetailPo.createUser) &&
        Objects.equals(this.createTime, priceAdjustDetailPo.createTime) &&
        Objects.equals(this.updateUser, priceAdjustDetailPo.updateUser) &&
        Objects.equals(this.updateTime, priceAdjustDetailPo.updateTime) &&
        Objects.equals(this.remark, priceAdjustDetailPo.remark) &&
        Objects.equals(this.yn, priceAdjustDetailPo.yn) &&
        Objects.equals(this.adjustPref, priceAdjustDetailPo.adjustPref) &&
        Objects.equals(this.status, priceAdjustDetailPo.status);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, productPref, oldPrice, newPrice, oldMemberPrice, newMemberPrice, createUser, createTime, updateUser, updateTime, remark, yn, adjustPref, status);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PriceAdjustDetailPo {\n");

    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    productPref: ").append(toIndentedString(productPref)).append("\n");
    sb.append("    oldPrice: ").append(toIndentedString(oldPrice)).append("\n");
    sb.append("    newPrice: ").append(toIndentedString(newPrice)).append("\n");
    sb.append("    oldMemberPrice: ").append(toIndentedString(oldMemberPrice)).append("\n");
    sb.append("    newMemberPrice: ").append(toIndentedString(newMemberPrice)).append("\n");
    sb.append("    createUser: ").append(toIndentedString(createUser)).append("\n");
    sb.append("    createTime: ").append(toIndentedString(createTime)).append("\n");
    sb.append("    updateUser: ").append(toIndentedString(updateUser)).append("\n");
    sb.append("    updateTime: ").append(toIndentedString(updateTime)).append("\n");
    sb.append("    remark: ").append(toIndentedString(remark)).append("\n");
    sb.append("    yn: ").append(toIndentedString(yn)).append("\n");
    sb.append("    adjustPref: ").append(toIndentedString(adjustPref)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

