package com.xyy.saas.web.api.module.supplier.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.supplier.dto.ProviderDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Classname FirstCheckSupplier
 * @Description TODO
 * @Date 2020/6/4 12:02
 */
@Data
@ToString
public class FirstCheckSupplier extends Page implements Serializable {

    private Long id;
    private String guid;
    private String pref;
    private String providerPref;
    private Byte status;
    private String approvalUser;
    private Date approvalTime;
    private String createUser;
    private Date createTime;
    private String updateUser;
    private Date updateTime;
    private Byte yn;
    private Long baseVersion;
    private String organsign;
    private String realName;
    private ProviderDto providerinfo;
    private Integer providerType;
    private String startTimeStr;
    private String endTimeStr;
    private Long approveUserRoleId;
    private Byte approveOrganSign;
    private String approveUserRoleName;
    private String billNo;
    private String pharmacyPref;
    private String providerName;
    private String providerTypeStr;
    private String expirationDateStr;
    private String usedStr;
    private String createProviderUser;
    private String createTimeStr;
    private String statusStr;
    private String[] guids;

}
