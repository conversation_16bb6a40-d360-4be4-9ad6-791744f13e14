package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description 会员慢病患者回访信息VO-结果返回
 * <AUTHOR>
 * @Create 2020-10-13 9:52
 */
@Data
public class MemberChronicVisitInfoVo {
    //"手机号、姓名、性别、年龄、所属地区、详细地址、关心病种、来源门店；"
    //"回访门店、回访人、回访日期、回访记录；"
    @ApiModelProperty(value = "会员手机号")
    private String telephone;
    @ApiModelProperty(value = "姓名")
    private String name;
    @ApiModelProperty(value = "性别 1男 2女")
    private Integer sex;
    @ApiModelProperty(value = "性别 男 女")
    private String sexName;
    @ApiModelProperty(value = "会员卡号")
    private String cartNo;
    @ApiModelProperty(value = "年龄")
    private Integer age;
    @ApiModelProperty(value = "身份证号")
    private String idCard;
    @ApiModelProperty(value = "详细地址")
    private String address;
    @ApiModelProperty(value = "所属地区：省市区")
    private String provinceCityAreaName;
    @ApiModelProperty(value = "关心病种(高血压、糖尿病)")
    private String chronicNames;
    @ApiModelProperty(value = "慢病编号")
    private String chronicPrefs;
    @ApiModelProperty(value = "来源门店")
    private String sourceDrugstore;
    @ApiModelProperty(value = "来源门店名称")
    private String sourceDrugstoreName;
    @ApiModelProperty(value = "回访门店")
    private String reVisitDrugstore;
    @ApiModelProperty(value = "回访门店名称")
    private String reVisitDrugstoreName;
    @ApiModelProperty(value = "回访人")
    private String revisiter;
    @ApiModelProperty(value = "回访人名字")
    private String revisiterName;
    @ApiModelProperty(value = "回访时间")
    private Long visitTime;
    @ApiModelProperty(value = "回访时间")
    private String visitTimeStr;
    @ApiModelProperty(value = "回访内容")
    private String visitContent;
    @ApiModelProperty(value = "创建日期")
    private Long createTime;
    @ApiModelProperty(value = "创建日期")
    private String createTimeStr;

    @ApiModelProperty(value = "会员guid")
    private String guid;
}