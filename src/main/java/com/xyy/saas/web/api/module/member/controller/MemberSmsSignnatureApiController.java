package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.member.core.api.MemberSmsSignnatureApi;
import com.xyy.saas.member.core.dto.MemberSmsSignnatureDto;
import com.xyy.saas.web.api.module.member.model.*;
import com.xyy.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2020-01-08T11:16:06.377+08:00")
@Controller
@RequestMapping("/member/memberSmsSignnature")
@Api(value = "memberSmsSignnature", description = "the memberSmsSignnature API")
public class MemberSmsSignnatureApiController {

    @Reference(version = "0.0.1")
    private MemberSmsSignnatureApi memberSmsSignnatureApi;

    @ApiOperation(value = "新增短信签名", notes = "新增短信签名", response = JSONObject.class, tags={ "memberSmsSignnature", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = JSONObject.class) })
    @RequestMapping(value = "/insertSignnature", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    public ResponseEntity<ResultVO> insertSignnature(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                     @RequestBody MemberSmsSignnatureInsertVo insertVo){
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String organSign = commonRequestModel.getOrganSign();
        String employeeId = commonRequestModel.getEmployeeId();
        MemberSmsSignnatureDto dto = new MemberSmsSignnatureDto();
        dto.setOrganSign(organSign);
        dto.setCreateUser(employeeId);
        dto.setUpdateUser(employeeId);
        BeanUtils.copyProperties(insertVo, dto);
        int result = memberSmsSignnatureApi.insert(dto);
        return new ResponseEntity(ResultVO.createSuccess(result), HttpStatus.OK);
    }

    @ApiOperation(value = "通过id查找短信签名", notes = "通过id查找短信签名", response = JSONObject.class, tags={ "memberSmsSignnature", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = JSONObject.class) })
    @RequestMapping(value = "/selectById", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    public ResponseEntity<ResultVO> selectById(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                               @RequestBody MemberSmsSignnatureSelectByIdVo selectByIdVo){
        MemberSmsSignnatureDto result = memberSmsSignnatureApi.selectById(selectByIdVo.getId());
        return new ResponseEntity(ResultVO.createSuccess(result), HttpStatus.OK);
    }

    @ApiOperation(value = "编辑短信签名", notes = "编辑短信签名", response = JSONObject.class, tags={ "memberSmsSignnature", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = JSONObject.class) })
    @RequestMapping(value = "/updateSignnature", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    public ResponseEntity<ResultVO> updateSignnature(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                     @RequestBody MemberSmsSignnatureUpdateVo updateVo){
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String organSign = commonRequestModel.getOrganSign();
        String employeeId = commonRequestModel.getEmployeeId();
        MemberSmsSignnatureDto dto = new MemberSmsSignnatureDto();
        dto.setOrganSign(organSign);
        dto.setUpdateUser(employeeId);
        BeanUtils.copyProperties(updateVo, dto);
        int result = memberSmsSignnatureApi.update(dto);
        return new ResponseEntity(ResultVO.createSuccess(result), HttpStatus.OK);
    }

    @ApiOperation(value = "设置通知短信签名", notes = "设置通知短信签名", response = JSONObject.class, tags={ "memberSmsSignnature", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = JSONObject.class) })
    @RequestMapping(value = "/updateSignnatureRemind", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    public ResponseEntity<ResultVO> updateSignnatureRemind(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                     @RequestBody MemberSmsSignnatureUpdateRemindVo updateVo){
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String organSign = commonRequestModel.getOrganSign();
        String employeeId = commonRequestModel.getEmployeeId();
        MemberSmsSignnatureDto dto = new MemberSmsSignnatureDto();
        dto.setOrganSign(organSign);
        dto.setUpdateUser(employeeId);
        BeanUtils.copyProperties(updateVo, dto);
        int result = memberSmsSignnatureApi.update(dto);
        return new ResponseEntity(ResultVO.createSuccess(result), HttpStatus.OK);
    }

    @ApiOperation(value = "删除短信签名", notes = "删除短信签名", response = JSONObject.class, tags={ "memberSmsSignnature", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = JSONObject.class) })
    @RequestMapping(value = "/deleteSignnature", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    public ResponseEntity<ResultVO> deleteSignnature(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                     @RequestBody MemberSmsSignnatureDeleteVo deleteVo){
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String organSign = commonRequestModel.getOrganSign();
        String employeeId = commonRequestModel.getEmployeeId();
        MemberSmsSignnatureDto dto = new MemberSmsSignnatureDto();
        dto.setOrganSign(organSign);
        dto.setUpdateUser(employeeId);
        BeanUtils.copyProperties(deleteVo, dto);
        int result = memberSmsSignnatureApi.deleteYn(dto);
        return new ResponseEntity(ResultVO.createSuccess(result), HttpStatus.OK);
    }

    @ApiOperation(value = "短信签名列表(固定查询当前门店)", notes = "短信签名列表", response = JSONObject.class, tags={ "memberSmsSignnature", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = JSONObject.class) })
    @RequestMapping(value = "/selectSignnatureList", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    public ResponseEntity<ResultVO> selectSignnatureList(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                         @RequestBody SelectSignnatureListVo listVo){
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String organSign = commonRequestModel.getOrganSign();
        MemberSmsSignnatureDto dto = new MemberSmsSignnatureDto();
        dto.setOrganSign(organSign);
        PageInfo<MemberSmsSignnatureDto> pageInfo = memberSmsSignnatureApi.selectList(dto, listVo.getPageSize(), listVo.getPageNum());
        return new ResponseEntity(ResultVO.createSuccess(pageInfo), HttpStatus.OK);
    }

    @ApiOperation(value = "短信签名列表(固定查询总部的)", notes = "短信签名列表", response = JSONObject.class, tags={ "memberSmsSignnature", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = JSONObject.class) })
    @RequestMapping(value = "/selectSignnatureListZb", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    public ResponseEntity<ResultVO> selectSignnatureListZb(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                             @RequestBody SelectSignnatureListVo listVo){
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String organSign = commonRequestModel.getHeadquartersOrganSign();
        PageInfo<MemberSmsSignnatureDto> pageInfo = null;
        if(!StringUtil.isEmpty(organSign)) {
            MemberSmsSignnatureDto dto = new MemberSmsSignnatureDto();
            dto.setOrganSign(organSign);
            pageInfo = memberSmsSignnatureApi.selectList(dto, listVo.getPageSize(), listVo.getPageNum());
        }else{
            pageInfo = new PageInfo<>();
        }
        return new ResponseEntity(ResultVO.createSuccess(pageInfo), HttpStatus.OK);
    }


}
