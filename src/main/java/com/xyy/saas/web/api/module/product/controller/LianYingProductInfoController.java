package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.common.module.api.SysConfigApi;
import com.xyy.saas.common.api.SaasAreaApi;
import com.xyy.saas.common.api.SystemDictApi;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.dto.SaasRegionBusinessDto;
import com.xyy.saas.common.dto.SystemDictDto;
import com.xyy.saas.common.dto.XyySaasRegionParamsDto;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.drugremind.core.api.SystemDrugRemindApi;
import com.xyy.saas.drugremind.core.dto.SystemDrugRemindDTO;
import com.xyy.saas.inventory.cores.api.InventoryForPromotionApi;
import com.xyy.saas.inventory.cores.api.SaasPoSitionApi;
import com.xyy.saas.inventory.cores.common.IRemoteBean;
import com.xyy.saas.inventory.cores.dto.SaasPositionVo;
import com.xyy.saas.match.api.MatchStandardLibraryApi;
import com.xyy.saas.product.core.api.LianYingProductApi;
import com.xyy.saas.product.core.api.ProductDictionaryApi;
import com.xyy.saas.product.core.api.ProductToolApi;
import com.xyy.saas.product.core.api.StandardLibaryApi;
import com.xyy.saas.product.core.dto.*;
import com.xyy.saas.rabbitmq.core.api.MessagePushApi;
import com.xyy.saas.web.api.module.product.constant.DictConstant;
import com.xyy.saas.web.api.module.product.model.CheckTaskStatusQueryVo;
import com.xyy.saas.web.api.module.product.model.LianYingSystemDictQueryVo;
import com.xyy.saas.web.api.module.product.model.ProductCommonQueryVo;
import com.xyy.saas.web.api.module.product.model.ProductSystemDictVo;
import com.xyy.saas.web.api.module.utils.JedisUtils;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.api.PermissionApi;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import com.xyy.user.module.enums.DrugstoreBizModelEnum;
import com.xyy.user.module.enums.DrugstoreTypeEnum;
import com.xyy.util.StringUtil;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName LianYingProductInfoController
 * @Description 联营商品网关接口实现类
 * <AUTHOR>
 * @Date 2020/9/16 11:19
 * @Version 1.0
 **/
@Controller
public class LianYingProductInfoController implements LianYingProductInfoApi{
    private static final Logger logger = LoggerFactory.getLogger(LianYingProductInfoController.class);
    @Reference(version = "0.0.1")
    private com.xyy.saas.product.core.api.ProductApi productApi;

    @Reference(version = "0.0.1")
    private EmployeeApi employeeApi;

    @Reference(version = "0.0.1")
    private MessagePushApi messagePushApi;

    @Reference(version = "0.0.2")
    private SaasPoSitionApi poSitionApi;

    @Reference(version = "0.0.1")
    private ProductDictionaryApi productDictionaryApi;

    @Reference(version = "0.0.1")
    private PermissionApi permissionApi;

    @Reference(version = "0.0.2")
    private com.xyy.saas.inventory.cores.api.InventoryApi inventoryApi;

    @Reference(version = "0.0.1")
    private StandardLibaryApi standardLibaryApi;

    @Reference(version = "0.0.1")
    private SystemDrugRemindApi systemDrugRemindApi;

    @Reference(version = "0.0.1")
    private com.xyy.saas.product.core.api.AdjustPriceApi adjustPriceApi;

    @Reference(version = "0.0.2")
    private InventoryForPromotionApi inventoryForPromotionApi;

    @Reference(version = "0.0.1")
    private SysConfigApi sysConfigApi;

    @Reference(version = "0.0.1")
    private ProductToolApi productToolApi;

    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;

    @Reference(version = "0.0.1")
    private SaasAreaApi saasAreaApi;


    @Reference(version = "0.0.1")
    private MatchStandardLibraryApi matchStandardLibraryApi;

    @Value("${fengcheng.organsign.areacodes}")
    private String fengchengAreacodes;

    @Autowired
    private JedisUtils jedisUtils;

    @Reference(version = "0.0.1")
    private SystemDictApi systemDictApi;

    @Reference(version = "0.0.1")
    LianYingProductApi lianYingProductApi;

    /**
     * 智鹿 医保相关字段置灰配置 (指定门店或总部机构号)
     */
    @Value("${product.medical.info.readonly.organsigns:}")
    private String productMedicalInfoReadonlyOrgansigns;

    private boolean isMedicalInfoReadonly(String organSign) {
        if (StringUtils.isEmpty(productMedicalInfoReadonlyOrgansigns)) {
            return false;
        }
        if ("all".equalsIgnoreCase(productMedicalInfoReadonlyOrgansigns)) {
            return true;
        }
        String[] split = productMedicalInfoReadonlyOrgansigns.split(",");
        boolean matchOrganSign = Arrays.stream(split).anyMatch(s -> s.equalsIgnoreCase(organSign));
        if (matchOrganSign) {
            return true;
        }
        // 判断店铺总部机构号
        SaaSDrugstoreDto drugstore = drugstoreApi.getDrugstoreByOrganSign(organSign);
        if (drugstore != null && org.apache.commons.lang3.StringUtils.isNotBlank(drugstore.getHeadquartersOrganSign())) {
            return Arrays.stream(split).anyMatch(s -> s.equalsIgnoreCase(drugstore.getHeadquartersOrganSign()));
        }
        return false;
    }


    @Override
    public ResponseEntity<ResultVO> saveProduct(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody LianYingProductDto product) {
        logger.info("lianying addOrUpdate productbaseinfo:" + product.getOrganSign() + "--->" + JSONObject.toJSON(product));
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        if (StringUtils.isEmpty(product)) {
            logger.error("==============参数不合法");
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, null), HttpStatus.OK);
        }
        ResultVO<Boolean> paramResult = productParamLengthCheck(product);
        if (!paramResult.getResult()){
            logger.error("==============参数不合法 "+paramResult.getMsg());
            return new ResponseEntity<ResultVO>(paramResult, HttpStatus.OK);
        }
        if (StringUtils.isEmpty(product.getPref())) {
            product.setScatteredYn((byte) 0);
        }
        product.setOrganSign(organSign);
        product.setScatteredYn(null);//此字段属性和当前业务无关
        String modelJson = request.getHeader("commonRequestModel");
        Byte bizModel = null;
        String headquartersOrganSign = "";
        if (!StringUtils.isEmpty(modelJson)) {
            CommonRequestModel model = JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            bizModel = model.getBizModel();
            headquartersOrganSign = model.getHeadquartersOrganSign();
        }
        if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(bizModel)){//处理产地相关的逻辑
            if(!StringUtils.isEmpty(product.getAreaCode())){
                //产地
                Map<String,String> areCodeMap = new HashMap<>();
                Set<Integer> areCodes = new HashSet<>();
                String[] areCodestrs = product.getAreaCode().split(",");
                for(String str : areCodestrs){
                    if(!StringUtils.isEmpty(str)){
                        areCodes.add(Integer.valueOf(str));
                    }
                }
                if (!CollectionUtils.isEmpty(areCodes)) {
                    XyySaasRegionParamsDto paramsDto = new XyySaasRegionParamsDto();
                    paramsDto.setAreaCodes(new ArrayList(areCodes));
                    List<SaasRegionBusinessDto> xyyRegionBusinessDtos = saasAreaApi.queryRegionByAreaCodeList(paramsDto);
                    areCodeMap = xyyRegionBusinessDtos.stream().collect((Collectors.toMap(p -> p.getAreaCode() + "", p -> p.getAreaName())));
                }
                StringBuffer sb = new StringBuffer("");
                for(String str : areCodestrs){
                    if(!StringUtils.isEmpty(str)){
                        sb.append(areCodeMap.get(str));
                    }
                }
                product.setProducingArea(sb.toString());
            }
        }
        product.setProductType(product.getProductType() == null ? 0 : product.getProductType()); //自定义分类
        product.setProductFunctionCatagory (product.getProductFunctionCatagory() == null ? 0 : product.getProductFunctionCatagory());// 功能分类
        ResultVO resultVO = lianYingProductApi.saveProduct(product,Integer.parseInt(employee),organSign);
        logger.info("lianying addOrUpdate productbaseinfo result code:{}", resultVO.getCode());
        if (resultVO.getCode() == 0) {
            // 将数据推送到mq和es  连锁门店 需要推送总部下所有的机构
         /* todo lvw 是否推送消息
           if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(bizModel)){
                List<SaaSDrugstoreDto> drugs = drugstoreApi.getDrugstoreByHeadquartersOrganSign(organSign);//获取门店列表
                if(!CollectionUtils.isEmpty(drugs)){
                    for(SaaSDrugstoreDto dto : drugs){
                        pushProductMessToMQ(dto.getOrganSign());
                    }
                }
            }else{
                pushProductMessToMQ(organSign);
            }*/
           // return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
        }else{

        }
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);

        /*else if (-1 == productPrefDto.getStatus()) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "新增  标准库ID已存在",-1),HttpStatus.OK);
        }else if (-2 == productPrefDto.getStatus()) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "新增，根据药店编码查询该编码在数据库中该药店里存在",-2),HttpStatus.OK);
        }else if (-3 == productPrefDto.getStatus()) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "提交异常，请重新提交",-3),HttpStatus.OK);
        }else if (-4 == productPrefDto.getStatus()) {
            String msg = "商品库里已存在该商品（商品编号："+productPrefDto.getPharmacyPref()+"），请核对商品信息后重新提交！";
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, msg,-4),HttpStatus.OK);
        }  else if (-5 == productPrefDto.getStatus()) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "提交失败,审批流生成异常,请重新提交！", -5), HttpStatus.OK);
        } else if (-6 == productPrefDto.getStatus()) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, productPrefDto.getResultMsg(), -6), HttpStatus.OK);
        } else if (-7 == productPrefDto.getStatus()) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "商品名称、通用名、条形码过长！", -7), HttpStatus.OK);
        }else {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "发生未知错误，需要进行排查"),HttpStatus.OK);
        }*/




    }

    @Override
    public ResponseEntity<ResultVO<PageInfo>> proQueryList(HttpServletRequest request, @Valid @RequestBody ProductCommonQueryVo product) {
        logger.info("LianYingProductInfoController proQueryList para={}",JSON.toJSONString(product));
//        String identity = request.getHeader("identity");
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        String identity = employeeApi.queryEmployeeById(Integer.valueOf(employee)).getResult().getIdentity()+"";
        logger.info("organSign:" + organSign + ",employee:" + employee + ",identity:" + identity);
        Byte isProductHidden = Byte.valueOf(identity);
        Integer page = null;
        Integer rows = null;
        if ( null == product.getPage() ){
            page = 1;
        }else{
            page = product.getPage();
        }

        if ( null == product.getRows() ){
            rows = 10;
        }else{
            rows = product.getRows();
        }
        product.setPage(page);
        product.setRows(rows);

        product.setOrganSign(organSign);

        long start_time = System.currentTimeMillis();
        ResultVO result = lianYingProductApi.productList(product);
        logger.info("LianYingProductInfoController proQueryList time:"+(System.currentTimeMillis() - start_time)+"ms");
        return new ResponseEntity(result,HttpStatus.OK);

    }

    @Override
    public ResponseEntity<ResultVO> getProductById(HttpServletRequest request, @Valid @RequestBody ProductDto productDto) {
        String headerOrganSign = request.getHeader("organSign");
        String organSign =productDto.getOrganSign();
       // List<d> drugstoreApi.getDrugstoreByHeadquartersOrganSign(headerOrganSign);
        String employee = request.getHeader("employeeId");
        String identity = employeeApi.queryEmployeeById(Integer.valueOf(employee)).getResult().getIdentity()+"";
        logger.info("organSign:" + organSign + ",employee:" + employee + ",identity:" + identity+",id:"+productDto.getId());
//        String identity = request.getHeader("identity");

//        ModelClassVo model = new ModelClassVo();
//        if (null == productQueryVo.getId())
//            model.addAttribute("result", new ResultVO<>(ResultCodeEnum.ERROR, null));
        String priceQueryOrganSign = "";
        //如果是连锁门店，需要获取总部的机构号
        String modelJson = request.getHeader("commonRequestModel");
        Byte bizModel = null;
        if(!StringUtils.isEmpty(modelJson)) {
            CommonRequestModel model = JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            bizModel = model.getBizModel();
            if (DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())) {
                priceQueryOrganSign = organSign;
                organSign = model.getHeadquartersOrganSign();
            }
        }
        ProductDto product = this.productApi.getProductByIdAndOrganSignChain(productDto.getId(), organSign, Byte.valueOf(identity),priceQueryOrganSign,bizModel);
        product.setPrescriptionClassification((product.getPrescriptionClassification() == null || product.getPrescriptionClassification() == -1 )?null:product.getPrescriptionClassification());
        product.setDosageFormId((product.getDosageFormId() == null || product.getDosageFormId() == 0 )? null:product.getDosageFormId());
        product.setAbcDividing((product.getAbcDividing() == null || product.getAbcDividing() == -1 )? null:product.getAbcDividing());
        product.setStorageCondition((product.getStorageCondition() == null || product.getStorageCondition() == -1 )? null:product.getStorageCondition());
//        model.addAttribute("result", product);
//        logger.info("getProductById productId:" + product.getId() +", drugRemindId:"+ product.getDrugRemindId() +", remind:"+ product.getReminder());
      //  if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(bizModel) || DrugstoreBizModelEnum.JOINT_OPERATION.toEquals(bizModel)){
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(product),HttpStatus.OK);
       // }
    }

    @Override
    public ResponseEntity<ResultVO> updateProduct(HttpServletRequest request, @RequestBody LianYingProductUpdateDto saveDto) {

        logger.info("联营变更商品信息入参+" + JSONObject.toJSONString(saveDto));
        //验参
        if (saveDto.getList() == null || saveDto.getList().isEmpty()
                || saveDto.getUpdateContent() == null) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "参数错误"), HttpStatus.OK);
        }
        String headerOrganSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        Object o = lianYingProductApi.updateProduct(saveDto,Integer.valueOf(employee),headerOrganSign);
        logger.info("联营变更商品信息结果{}",JSON.toJSONString(o));
       // ResultVO resultVO = lianYingProductApi.updateProduct(saveDto,Integer.valueOf(employee), headerOrganSign);

        return new ResponseEntity<ResultVO>((ResultVO)o,HttpStatus.OK);


    }

    @Override
    public  ResponseEntity<ResultVO>  shareProduct(HttpServletRequest request, @RequestBody LianYingProductDto product) {

        logger.info("lianying  shareProduct para:"  + JSONObject.toJSON(product));
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        ResultVO<Boolean> checkResult = checkSharePara(product);
        if (checkResult.getResult()){
            logger.error("====lianying  shareProduct para 不合法 ");
            return new ResponseEntity<ResultVO>(checkResult, HttpStatus.OK);
        }

        ResultVO resultVO = lianYingProductApi.sharePoroduct(product,Integer.valueOf(employee),organSign);
        logger.info("lianying shareProduct result code:{},oper{}", resultVO.getCode(),employee);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);

    }

    @Override
    public ResponseEntity<ResultVO> selectShareOrgans(HttpServletRequest request, @Valid @RequestBody LianYingProductDto product) {
        logger.info("lianying  selectShareOrgans para:{}" ,product.getId());
        String organSign = request.getHeader("organSign");
        ResultVO resultVO  = lianYingProductApi.selectShareOrgans(product,organSign);

        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> selectHadShareOrgans(HttpServletRequest request, @Valid @RequestBody LianYingProductDto product) {
        logger.info("lianying  selectHadShareOrgans para:{}" ,product.getId());
        String organSign = request.getHeader("organSign");
        ResultVO resultVO  = lianYingProductApi.selectHadShareOrgans(product,organSign);

        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    public ResultVO<Boolean> productParamLengthCheck(ProductDto product){
        if (product.getCommonName()!=null&&product.getCommonName().length()>100){
            return new ResultVO(ResultCodeEnum.ERROR, "通用名称长度不可超过100个字符", false);
        }
        if (product.getProductName()!=null&&product.getProductName().length()>100){
            return new ResultVO(ResultCodeEnum.ERROR, "商品名称长度不可超过100个字符", false);
        }
        if (product.getMnemonicCode()!=null&&product.getMnemonicCode().length()>201){
            return new ResultVO(ResultCodeEnum.ERROR, "助记码长度不可超过201个字符", false);
        }
        if (product.getAttributeSpecification()!=null&&product.getAttributeSpecification().length()>100){
            return new ResultVO(ResultCodeEnum.ERROR, "规格/型号不可超过100个字符", false);
        }
        if (product.getUsageAndDosage()!=null&&product.getUsageAndDosage().length()>15000){
            return new ResultVO(ResultCodeEnum.ERROR, "用法用量不可超过15000个字符", false);
        }
        if (product.getPref()!=null&&product.getPref().length()>32){
            return new ResultVO(ResultCodeEnum.ERROR, "商品编号不可超过32个字符", false);
        }
        // 新增校验  修改不校验
        if (product.getId() == null && product.getPharmacyPref()!=null&&product.getPharmacyPref().length()>20){
            return new ResultVO(ResultCodeEnum.ERROR, "商品编号不可超过20个字符", false);
        }
        if (product.getManufacturer()!=null&&product.getManufacturer().length()>100){
            return new ResultVO(ResultCodeEnum.ERROR, "生产厂家不可超过100个字符", false);
        }
        if (product.getProducingArea()!=null&&product.getProducingArea().length()>100){
            return new ResultVO(ResultCodeEnum.ERROR, "产地不可超过100个字符", false);
        }
        if (product.getBarCode()!=null&&product.getBarCode().length()>100){
            return new ResultVO(ResultCodeEnum.ERROR, "条形码不可超过100个字符", false);
        }
        if (product.getApprovalNumber()!=null&&product.getApprovalNumber().length()>100){
            return new ResultVO(ResultCodeEnum.ERROR, "批准文号/备案/注册证号不可超过100个字符", false);
        }
        if (product.getDrugPermissionPerson()!=null&&product.getDrugPermissionPerson().length()>64){
            return new ResultVO(ResultCodeEnum.ERROR, "上市许可持有人不可超过64字符", false);
        }
        return new ResultVO(ResultCodeEnum.SUCCESS, "", true);
    }

    private  ResultVO<Boolean> checkSharePara(ProductDto product) {
        if (StringUtil.isEmpty(product.getPref())){
            return new ResultVO(ResultCodeEnum.ERROR, "被分享的商品编码不能为空", false);
        }
        if (StringUtil.isEmpty(product.getSelectOrganSign())){
            return new ResultVO(ResultCodeEnum.ERROR, "目标门店不能为空", false);
        }
        if (StringUtil.isEmpty(product.getOrganSign())){
            return new ResultVO(ResultCodeEnum.ERROR, "被分享的商品所属门店不能为空", false);
        }
        return new ResultVO(ResultCodeEnum.SUCCESS, "", true);
    }


    @Override
    public ResponseEntity<ResultVO>  checkTaskStatus(HttpServletRequest request, @ApiParam(value = "查询任务执行结果", required = true) @Valid @RequestBody CheckTaskStatusQueryVo checkTaskStatusQueryVo){
        logger.info("lianying  checkTaskStatus para:{}" ,checkTaskStatusQueryVo.getTaskNo());
        TaskQueryDto taskQueryDto = new TaskQueryDto();
        BeanUtils.copyProperties(checkTaskStatusQueryVo,taskQueryDto);
        ResultVO resultVO = lianYingProductApi.checkTaskStatus( taskQueryDto );
        logger.info("lianying  checkTaskStatus result:{}" ,JSON.toJSONString(resultVO));
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }


    @Override
    public ResponseEntity<ResultVO> getAdjustPriceListByPref(HttpServletRequest request,@ApiParam(value = "商品售价调整信息实体" ,required=true )  @Valid @RequestBody ProPriceAdjustDto proPriceAdjustDto) {


        String organSign = request.getHeader("organSign");
        //String organSign="ZHL0f2ce7b9";
        if(StringUtils.isEmpty(proPriceAdjustDto.getProductPref())){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR,"商品编号不能为空"),HttpStatus.OK);
        }
        String queryOrganSign = proPriceAdjustDto.getOrganSign();
        //String organSign="ZHL0f2ce7b9";
        if(StringUtils.isEmpty(queryOrganSign)){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR,"机构号不能为空"),HttpStatus.OK);
        }
        //权限判断
        List<String> allSubOrganSigns = lianYingProductApi.getSubOrganSigns(organSign);

        if(!allSubOrganSigns.contains(queryOrganSign)){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR,"非法查询"),HttpStatus.OK);
        }


        if (proPriceAdjustDto.getPage() == null) {
            proPriceAdjustDto.setPage(1);
        }
        if (proPriceAdjustDto.getRows() == null) {
            proPriceAdjustDto.setRows(50);
        }
        proPriceAdjustDto.setOrganSign(queryOrganSign);
        ResultVO result =adjustPriceApi.getAdjustPriceListByPref(proPriceAdjustDto );

        return new ResponseEntity<ResultVO>(result,HttpStatus.OK);
    }


    @Override
    public ResponseEntity<ResultVO> getProductTypeList(HttpServletRequest request,@ApiParam(value = "当前登录机构码" ,required=true) @RequestHeader(value="organSign", required=true) String organSign,
                                                       @ApiParam(value = "当前登录员工的ID" ,required=true) @RequestHeader(value="employeeId", required=true) String employeeId,
                                                       @ApiParam(value = "请求字典类型" ,required=true )  @Valid @RequestBody LianYingSystemDictQueryVo systemDictQueryVo) {
        List<String> dictIds = new ArrayList<>();

        logger.info("getProductTypeList-lianying param:organSign:"+organSign);
        long start_time = System.currentTimeMillis();
        List<Integer> ids = Arrays.asList(DictConstant.unitBussinessId, DictConstant.agentBussinessId, DictConstant.commodtyTypeBussinessId, DictConstant.STORE_CONDITION_BUSINESS_ID,
                DictConstant.ABC_BUSSINESS_ID, DictConstant.prescriptionBussinessId, DictConstant.scopeOfOperation, DictConstant.maintenanceType, DictConstant.functionBussinessId,
                                          DictConstant.productSystemType,DictConstant.providerCompanyType,DictConstant.medicalInsuranceLevelBussinessId,DictConstant.CosmeticsCategoryBussinessId);
        long start_time1 = System.currentTimeMillis();
//        //必须用传入的机构号，不然自定义的值查不到
//        if(StringUtil.isEmpty(systemDictQueryVo.getOrganSign())){
//            return new ResponseEntity<ResultVO>(ResultVO.createError("机构号不能为空"),HttpStatus.OK);
//        }
        String queryOrganSign = StringUtil.isEmpty(systemDictQueryVo.getOrganSign())?organSign:systemDictQueryVo.getOrganSign();
        List<SystemDictDto> dictDtos = productDictionaryApi.findSystemDictDtoBybussinessIds(ids,queryOrganSign);
        List<ProductSystemDictVo> unitDict = new ArrayList<>();
        List<ProductSystemDictVo> proTypeDict = new ArrayList<>();
        List<ProductSystemDictVo> agentDict = new ArrayList<>();
        List<ProductSystemDictVo> cfDict = new ArrayList<>();
        List<ProductSystemDictVo> ptypeDict = new ArrayList<>();
        List<ProductSystemDictVo> yhDict = new ArrayList<>();
        List<ProductSystemDictVo> scopeDict = new ArrayList<>();
        List<ProductSystemDictVo> providers = new ArrayList<>();
        List<ProductSystemDictVo> abcDict = new ArrayList<>();
        List<ProductSystemDictVo> storeDict = new ArrayList<>();
        List<ProductSystemDictVo> comproDict = new ArrayList<>();
        List<ProductSystemDictVo> cflbDict = new ArrayList<>();
        List<ProductSystemDictVo> spuCategoryDict = new ArrayList<>();
        List<ProductSystemDictVo> shadingAttrDict = DictConstant.shadingAttrDictVos;
        List<ProductSystemDictVo> specialAttributesDict = new ArrayList<>();
        List<ProductSystemDictVo> manageAttrDict = new ArrayList<>();
        List<ProductSystemDictVo> managementAttrDict = new ArrayList<>();
        List<ProductSystemDictVo> cosmeticsCategoryDict = new ArrayList<>();

        List<ProductSystemDictVo> medicalInsuranceLevelDict = new ArrayList<>();

        if(dictDtos != null && dictDtos.size() > 0){
            for(SystemDictDto dto:dictDtos){
                if(dto.getBussinessId().equals(DictConstant.unitBussinessId)){
                    ProductSystemDictVo vo = new ProductSystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    if(dto.getStatus() == 0){
                        vo.setDisabled(true);
                    }else{
                        vo.setDisabled(false);
                    }
                    unitDict.add(vo);
                }
                if(dto.getBussinessId().equals(DictConstant.commodtyTypeBussinessId)){
                    ProductSystemDictVo vo = new ProductSystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    if(dto.getStatus() == 0){
                        vo.setDisabled(true);
                    }else{
                        vo.setDisabled(false);
                    }
                    proTypeDict.add(vo);
                }
                if(dto.getBussinessId().equals(DictConstant.agentBussinessId)){
                    ProductSystemDictVo vo = new ProductSystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    if(dto.getStatus() == 0){
                        vo.setDisabled(true);
                    }else{
                        vo.setDisabled(false);
                    }
                    agentDict.add(vo);
                }
                if(dto.getBussinessId().equals(DictConstant.prescriptionBussinessId)){
                    ProductSystemDictVo vo = new ProductSystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    if(dto.getStatus() == 0){
                        vo.setDisabled(true);
                    }else{
                        vo.setDisabled(false);
                    }
                    cfDict.add(vo);
                }
                if(dto.getBussinessId().equals(DictConstant.functionBussinessId)){
                    ProductSystemDictVo vo = new ProductSystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    if(dto.getStatus() == 0){
                        vo.setDisabled(true);
                    }else{
                        vo.setDisabled(false);
                    }
                    ptypeDict.add(vo);
                }
                if(dto.getBussinessId().equals(DictConstant.maintenanceType)){
                    ProductSystemDictVo vo = new ProductSystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    if(dto.getStatus() == 0){
                        vo.setDisabled(true);
                    }else{
                        vo.setDisabled(false);
                    }
                    yhDict.add(vo);
                }
                if(dto.getBussinessId().equals(DictConstant.scopeOfOperation)){
                    ProductSystemDictVo vo = new ProductSystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    if(dto.getStatus() == 0){
                        vo.setDisabled(true);
                    }else{
                        vo.setDisabled(false);
                    }
                    if (!CollectionUtils.isEmpty(dictIds)) {
                        if (!dictIds.contains(String.valueOf(dto.getId()))) {
                            scopeDict.add(vo);
                        }
                    }else {
                        scopeDict.add(vo);
                    }
                }
                if(dto.getBussinessId().equals(DictConstant.providerCompanyType)){
                    ProductSystemDictVo vo = new ProductSystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    if(dto.getStatus() == 0){
                        vo.setDisabled(true);
                    }else{
                        vo.setDisabled(false);
                    }
                    providers.add(vo);
                }
                if(dto.getBussinessId().equals(DictConstant.ABC_BUSSINESS_ID)){
                    ProductSystemDictVo vo = new ProductSystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    if(dto.getStatus() == 0){
                        vo.setDisabled(true);
                    }else{
                        vo.setDisabled(false);
                    }
                    abcDict.add(vo);
                }
                if(dto.getBussinessId().equals(DictConstant.STORE_CONDITION_BUSINESS_ID)){
                    ProductSystemDictVo vo = new ProductSystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    if(dto.getStatus() == 0){
                        vo.setDisabled(true);
                    }else{
                        vo.setDisabled(false);
                    }
                    storeDict.add(vo);
                }
                if(dto.getBussinessId().equals(DictConstant.productSystemType)){
                    ProductSystemDictVo vo = new ProductSystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    if(dto.getStatus() == 0){
                        vo.setDisabled(true);
                    }else{
                        vo.setDisabled(false);
                    }
                    comproDict.add(vo);
                }
                if(dto.getBussinessId().equals(DictConstant.medicalInsuranceLevelBussinessId)){
                    ProductSystemDictVo vo = new ProductSystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    if(dto.getStatus() == 0){
                        vo.setDisabled(true);
                    }else{
                        vo.setDisabled(false);
                    }
                    medicalInsuranceLevelDict.add(vo);
                }
                if(dto.getBussinessId().equals(DictConstant.CosmeticsCategoryBussinessId)){
                    ProductSystemDictVo vo = new ProductSystemDictVo();
                    BeanUtils.copyProperties(dto,vo);
                    if(dto.getStatus() == 0){
                        vo.setDisabled(true);
                    }else{
                        vo.setDisabled(false);
                    }
                    cosmeticsCategoryDict.add(vo);
                }
            }
        }
        logger.info("getProductTypeList systemDictApi.findSystemDictDtoBybussinessIds need time:"+(System.currentTimeMillis() - start_time1)+" ms");
        //手动写处方类别 单轨和双轨
        ProductSystemDictVo singleTrack = new ProductSystemDictVo();
        singleTrack.setId(0);
        singleTrack.setName("双轨处方药");
        ProductSystemDictVo doubleTrack = new ProductSystemDictVo();
        doubleTrack.setId(1);
        doubleTrack.setName("单轨处方药");
        cflbDict.add(singleTrack);
        cflbDict.add(doubleTrack);

        //商品大类和存储属性
        ProductSystemDictVo spuCategory1 = new ProductSystemDictVo();
        spuCategory1.setId(1);
        spuCategory1.setName("普通药品");
        ProductSystemDictVo spuCategory2 = new ProductSystemDictVo();
        spuCategory2.setId(2);
        spuCategory2.setName("中药");
        ProductSystemDictVo spuCategory3 = new ProductSystemDictVo();
        spuCategory3.setId(3);
        spuCategory3.setName("医疗器械");
        ProductSystemDictVo spuCategory4 = new ProductSystemDictVo();
        spuCategory4.setId(4);
        spuCategory4.setName("其他");
        ProductSystemDictVo spuCategory5 = new ProductSystemDictVo();
        spuCategory4.setId(5);
        spuCategory4.setName("非药");
        ProductSystemDictVo spuCategory6 = new ProductSystemDictVo();
        spuCategory4.setId(6);
        spuCategory4.setName("赠品");
        spuCategoryDict.add(spuCategory1);
        spuCategoryDict.add(spuCategory2);
        spuCategoryDict.add(spuCategory3);
        spuCategoryDict.add(spuCategory4);
        spuCategoryDict.add(spuCategory5);
        spuCategoryDict.add(spuCategory6);



        //特殊属性
        ProductSystemDictVo specialAttributes1 = new ProductSystemDictVo();
        specialAttributes1.setId(1);
        specialAttributes1.setName("抗肿瘤治疗药");
        ProductSystemDictVo specialAttributes2 = new ProductSystemDictVo();
        specialAttributes2.setId(2);
        specialAttributes2.setName("限制使用抗菌药 ");
        ProductSystemDictVo specialAttributes3 = new ProductSystemDictVo();
        specialAttributes3.setId(3);
        specialAttributes3.setName("特殊使用抗菌药");
        ProductSystemDictVo specialAttributes4 = new ProductSystemDictVo();
        specialAttributes4.setId(4);
        specialAttributes4.setName("含特殊药品复方制剂");
        ProductSystemDictVo specialAttributes5 = new ProductSystemDictVo();
        specialAttributes5.setId(5);
        specialAttributes5.setName("β-内酰胺类注射剂");
        specialAttributesDict.add(specialAttributes1);
        specialAttributesDict.add(specialAttributes2);
        specialAttributesDict.add(specialAttributes3);
        specialAttributesDict.add(specialAttributes4);
        specialAttributesDict.add(specialAttributes5);
        //管理属性
        ProductSystemDictVo manageAttr1 = new ProductSystemDictVo();
        manageAttr1.setId(1);
        manageAttr1.setName("自有品牌");
        ProductSystemDictVo manageAttr2 = new ProductSystemDictVo();
        manageAttr2.setId(2);
        manageAttr2.setName("战略品种");
        ProductSystemDictVo manageAttr3 = new ProductSystemDictVo();
        manageAttr3.setId(3);
        manageAttr3.setName("统采毛利");
        ProductSystemDictVo manageAttr4 = new ProductSystemDictVo();
        manageAttr4.setId(4);
        manageAttr4.setName("统采品牌");
        ProductSystemDictVo manageAttr5 = new ProductSystemDictVo();
        manageAttr5.setId(5);
        manageAttr5.setName("地采补充");
        ProductSystemDictVo manageAttr6 = new ProductSystemDictVo();
        manageAttr6.setId(6);
        manageAttr6.setName("地采品牌");
        ProductSystemDictVo manageAttr7 = new ProductSystemDictVo();
        manageAttr7.setId(7);
        manageAttr7.setName("地采毛利");
        manageAttrDict.add(manageAttr1);
        manageAttrDict.add(manageAttr2);
        manageAttrDict.add(manageAttr3);
        manageAttrDict.add(manageAttr4);
        manageAttrDict.add(manageAttr5);
        manageAttrDict.add(manageAttr6);
        manageAttrDict.add(manageAttr7);
        //经营属性
        ProductSystemDictVo managementAttr1 = new ProductSystemDictVo();
        managementAttr1.setId(1);
        managementAttr1.setName("S");
        ProductSystemDictVo managementAttr2 = new ProductSystemDictVo();
        managementAttr2.setId(2);
        managementAttr2.setName("A");
        ProductSystemDictVo managementAttr3 = new ProductSystemDictVo();
        managementAttr3.setId(3);
        managementAttr3.setName("B");
        ProductSystemDictVo managementAttr4 = new ProductSystemDictVo();
        managementAttr4.setId(4);
        managementAttr4.setName("C");
        ProductSystemDictVo managementAttr5 = new ProductSystemDictVo();
        managementAttr5.setId(5);
        managementAttr5.setName("D");
        managementAttrDict.add(managementAttr1);
        managementAttrDict.add(managementAttr2);
        managementAttrDict.add(managementAttr3);
        managementAttrDict.add(managementAttr4);
        managementAttrDict.add(managementAttr5);

        // 查询字典表
        Map<String, Object> map = new HashMap<>();
        // 针对智鹿 - 返回标识(置灰医保相关字段)
        map.put("medicalInfoReadonly", isMedicalInfoReadonly(organSign));

        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 10004){
            // 单位
//            List<SystemDictDto> unitDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.unitBussinessId)).collect(Collectors.toList());
            map.put("unitDict", unitDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 10002){
            // 商品自定义分类
//            List<Integer> ads = Arrays.asList(DictConstant.commodtyTypeBussinessId);
//            List<SystemDictDto> proTypeDict = systemDictApi.findSystemDictDtoBybussinessIds(ads,organSign);
//            List<SystemDictDto> proTypeDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.commodtyTypeBussinessId)).collect(Collectors.toList());
            map.put("proTypeDict", proTypeDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 10003){
            // 剂型
//            List<SystemDictDto> agentDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.agentBussinessId)).collect(Collectors.toList());
            map.put("agentDict", agentDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 20013){
            // 处方分类
//            List<SystemDictDto> cfDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.prescriptionBussinessId)).collect(Collectors.toList());
            map.put("cfDict", cfDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 20020){
            // 商品功能分类
//            List<SystemDictDto> ptypeDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.functionBussinessId)).collect(Collectors.toList());
            map.put("ptypeDict", ptypeDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 20082){
            // 养护类型
//            List<SystemDictDto> yhDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.maintenanceType)).collect(Collectors.toList());
            map.put("yhDict", yhDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 10005){
            // 经营范围
//            List<SystemDictDto> scopeDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.scopeOfOperation)).collect(Collectors.toList());
            map.put("scopeDict", scopeDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 20048){//供应商类别
//            List<SystemDictDto> providers = this.systemDictApi.findSystemDictDto(DictConstant.providerCompanyType,organSign);
//            List<SystemDictDto> providers = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.providerCompanyType)).collect(Collectors.toList());
            map.put("providers", providers);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 20001){
            // ABC分类
//            List<SystemDictDto> abcDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.ABC_BUSSINESS_ID)).collect(Collectors.toList());
            map.put("abcDict", abcDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 20042){//存储条件
            // 存储条件
//            List<SystemDictDto> storeDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.STORE_CONDITION_BUSINESS_ID)).collect(Collectors.toList());
            map.put("storeDict", storeDict);
        }
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 30000 || systemDictQueryVo.getType() == 10101){
            //商品系统类型
//            List<SystemDictDto> comproDict = dictDtos.parallelStream().filter(item -> item.getBussinessId().equals(DictConstant.productSystemType)).collect(Collectors.toList());
            map.put("comproDict",comproDict);
        }
        map.put("cflbDict",cflbDict);
        map.put("spuCategoryDict", spuCategoryDict);
        map.put("shadingAttrDict", shadingAttrDict);
        map.put("specialAttributesDict", specialAttributesDict);
        map.put("manageAttrDict",manageAttrDict);
        map.put("managementAttrDict",managementAttrDict);
        map.put("medicalInsuranceLevelDict", medicalInsuranceLevelDict);
        map.put("cosmeticsCategoryDict", cosmeticsCategoryDict);

        logger.info("getProductTypeList common api need time:"+(System.currentTimeMillis() - start_time)+"ms");
        long start_time2 = System.currentTimeMillis();
        if(systemDictQueryVo.getType() == 0 || systemDictQueryVo.getType() == 10000){
            //获取商品架位：
            com.xyy.saas.inventory.cores.dto.SaasPositionVo sp = new com.xyy.saas.inventory.cores.dto.SaasPositionVo();
            sp.setPositionStatus(1);


            if( queryOrganSign.equals(organSign)){
                sp.setIfDefault(1);
            }else {
                sp.setOrgansign(queryOrganSign);
            }
            IRemoteBean<List<com.xyy.saas.inventory.cores.dto.SaasPositionVo>> iRemoteBean = poSitionApi.getPositionList(sp);
            List<com.xyy.saas.inventory.cores.dto.SaasPositionVo> positionList = new ArrayList<>();
            if (iRemoteBean.isSuccess()) {
                positionList = iRemoteBean.getContent();
            }
            List<ProductSystemDictVo> positionJonList = new ArrayList<>();
            List<ProductSystemDictVo> zYpositionJonList = new ArrayList<>();
            if(positionList != null && positionList.size() > 0){
                for(com.xyy.saas.inventory.cores.dto.SaasPositionVo vo:positionList){
                    if(vo.getPositionType() == 2){
                        ProductSystemDictVo systemDictDto = new ProductSystemDictVo();
                        systemDictDto.setId(vo.getId());
                        systemDictDto.setName(vo.getName());
                        systemDictDto.setDisabled(false);
                        zYpositionJonList.add(systemDictDto);
                    }
                    ProductSystemDictVo systemDictDto = new ProductSystemDictVo();
                    systemDictDto.setId(vo.getId());
                    systemDictDto.setName(vo.getName());
                    systemDictDto.setDisabled(false);
                    positionJonList.add(systemDictDto);
                }
            }
            map.put("positionList", positionJonList);//架位id，架位名称
            map.put("zYpositionJonList", zYpositionJonList);//架位id，架位名称
        }else if(systemDictQueryVo.getType() == 30000){
            //获取商品架位：
            com.xyy.saas.inventory.cores.dto.SaasPositionVo sp = new com.xyy.saas.inventory.cores.dto.SaasPositionVo();
            sp.setOrgansign(queryOrganSign);
            sp.setPositionType(2);
            sp.setPositionStatus(1);
            IRemoteBean<List<com.xyy.saas.inventory.cores.dto.SaasPositionVo>> iRemoteBean = poSitionApi.getPositionList(sp);
            List<com.xyy.saas.inventory.cores.dto.SaasPositionVo> positionList = new ArrayList<>();
            if (iRemoteBean.isSuccess()) {
                positionList = iRemoteBean.getContent();
            }
            List<ProductSystemDictVo> positionJonList = new ArrayList<>();
            if(positionList != null && positionList.size() > 0){
                for(SaasPositionVo vo:positionList){
                    ProductSystemDictVo systemDictDto = new ProductSystemDictVo();
                    systemDictDto.setId(vo.getId());
                    systemDictDto.setName(vo.getName());
                    systemDictDto.setDisabled(false);
                    positionJonList.add(systemDictDto);
                }
            }
            map.put("positionList", positionJonList);//架位id，架位名称
        }
        logger.info("getProductTypeList position api need time:"+(System.currentTimeMillis() - start_time2)+"ms");
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(map), HttpStatus.OK);
    }


    @Override
    public ResponseEntity<ResultVO> listAllDrugRemind(HttpServletRequest request, @RequestBody LianYingSystemDictQueryVo systemDictQueryVo) {
        String organSign = systemDictQueryVo.getOrganSign();
        if(StringUtil.isEmpty(organSign)){
            organSign = request.getHeader("organSign");
        }
        List<SystemDrugRemindDTO> list = systemDrugRemindApi.listAllByOrganSign(organSign);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(list),HttpStatus.OK);
    }
}
