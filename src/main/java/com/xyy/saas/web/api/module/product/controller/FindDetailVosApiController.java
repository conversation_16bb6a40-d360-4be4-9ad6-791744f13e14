package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.supplier.api.SupplierCurrentAccountApi;
import com.xyy.saas.web.api.module.product.model.InventoryLotnumAdjustDetailVo;
import com.xyy.saas.web.api.module.product.model.InventoryLotnumAdjustDetailVoList;
import io.swagger.annotations.ApiParam;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-29T17:04:23.698+08:00")

@Controller
public class FindDetailVosApiController implements FindDetailVosApi {

    @Reference(version = "0.0.1")
    private SupplierCurrentAccountApi supplierCurrentAccountApi;

    @Override
    public ResponseEntity<InventoryLotnumAdjustDetailVoList> findDetailVos(@ApiParam(value = "批号库存明细调整Vo" ,required=true )  @Valid @RequestBody InventoryLotnumAdjustDetailVo inventoryLotnumAdjustDetailVo) {
        return new ResponseEntity<InventoryLotnumAdjustDetailVoList>(HttpStatus.OK);
    }

    @Override
    public String updateBusinessDealAccount(String organSign) {
        return supplierCurrentAccountApi.updatePurchaseRetrieveBusinessDealAccount(organSign);
    }

}
