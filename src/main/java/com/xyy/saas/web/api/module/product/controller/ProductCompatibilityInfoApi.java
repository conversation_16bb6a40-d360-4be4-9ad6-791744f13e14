package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.dto.SystemDictDto;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.web.api.module.product.model.*;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-01-28T15:28:18.933+08:00")
@RequestMapping("/product")
@Api(value = "4.0商品配伍禁忌接口API", description = "0商品配伍禁忌接口API")
public interface ProductCompatibilityInfoApi {

    @ApiOperation(value = "配伍禁忌管理数据回显接口", notes = "配伍禁忌管理数据回显接口", response = List.class, tags={ "4.0商品配伍禁忌接口API", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = List.class) })
    @RequestMapping(value = "/baseinfo/compatibity/toList", method = RequestMethod.POST)
    ResponseEntity<ResultVO> toList(@ApiParam(value = "当前登录机构码", required = true) @RequestHeader(value = "organSign", required = true) String organSign,
                                    @ApiParam(value = "当前登录员工身份", required = true) @RequestHeader(value = "identity", required = true) String identity,
                                    @ApiParam(value = "当前登录员工的ID", required = true) @RequestHeader(value = "employee", required = true) String employee);

    @ApiOperation(value = "配伍禁忌管理 查询列表分页接口", notes = "配伍禁忌管理 查询列表分页", response = ProductCompatibilityTabooListVo.class, tags={ "4.0商品配伍禁忌接口API", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ProductCompatibilityTabooListVo.class) })
    @RequestMapping(value = "/baseinfo/compatibity/query", method = RequestMethod.POST)
    ResponseEntity<ResultVO> query(HttpServletRequest request,
                                   @ApiParam(value = "请求参数体", required = true) @Valid @RequestBody ProductCompatibilityQueryVo productCompatibilityQueryVo);

    @ApiOperation(value = "配伍禁忌管理 删除", notes = "配伍禁忌管理 删除", response = ResultVO.class, tags={ "4.0商品配伍禁忌接口API", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/baseinfo/compatibity/delete", method = RequestMethod.POST)
    ResponseEntity<ResultVO> delete(HttpServletRequest request,
                                    @ApiParam(value = "请求参数体", required = true) @Valid @RequestBody ProductCompatibilityQueryVo productCompatibilityQueryVo);

    @ApiOperation(value = "配伍禁忌管理 新增 选择主商品", notes = "配伍禁忌管理 新增 选择主商品", response = ProductCompatibilityVo.class, tags={ "4.0商品配伍禁忌接口API", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ProductCompatibilityVo.class) })
    @RequestMapping(value = "/baseinfo/compatibity/selectOutPW", method = RequestMethod.POST)
    ResponseEntity<ResultVO> selectOutPW(HttpServletRequest request,
                                         @ApiParam(value = "请求参数体", required = true) @Valid @RequestBody ProductCompatibilityQueryVo productCompatibilityQueryVo);

    @ApiOperation(value = "配伍禁忌管理 新增 选择配伍子商品", notes = "配伍禁忌管理 新增 选择配伍子商品", response = ProductCompatibilityVo.class, tags={ "4.0商品配伍禁忌接口API", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ProductCompatibilityVo.class) })
    @RequestMapping(value = "/baseinfo/compatibity/select", method = RequestMethod.POST)
    ResponseEntity<ResultVO> select(HttpServletRequest request,
                                    @ApiParam(value = "请求参数体", required = true) @Valid @RequestBody ProductCompatibilityQueryVo productCompatibilityQueryVo);

    @ApiOperation(value = "配伍禁忌管理 新增 保存", notes = "配伍禁忌管理 新增 选择商品", response = ResultVO.class, tags={ "4.0商品配伍禁忌接口API", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/baseinfo/compatibity/addSaveCommit", method = RequestMethod.POST)
    ResponseEntity<ResultVO> addSaveCommit(HttpServletRequest request,
                                           @ApiParam(value = "请求参数体", required = true) @Valid @RequestBody ProductCompatiSaveUpdateVo saveVo);

    @ApiOperation(value = "配伍禁忌管理 编辑 保存", notes = "配伍禁忌管理 新增 选择商品", response = ResultVO.class, tags={ "4.0商品配伍禁忌接口API", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/baseinfo/compatibity/updateCommit", method = RequestMethod.POST)
    ResponseEntity<ResultVO> updateCommit(HttpServletRequest request,
                                          @ApiParam(value = "请求参数体", required = true) @Valid @RequestBody ProductCompatiSaveUpdateVo saveVo);

    @ApiOperation(value = "配伍禁忌管理 编辑", notes = "配伍禁忌管理 编辑", response = ProductCompatibilityTabooListVo.class, tags={ "4.0商品配伍禁忌接口API", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ProductCompatibilityTabooListVo.class) })
    @RequestMapping(value = "/baseinfo/compatibity/updateDataCallback", method = RequestMethod.POST)
    ResponseEntity<ResultVO> updateDataCallback(HttpServletRequest request,
                                                @ApiParam(value = "请求参数体", required = true) @Valid @RequestBody ProductCompatibilityQueryVo productCompatibilityQueryVo);

    @ApiOperation(value = "配伍禁忌导出excel接口", notes = "配伍禁忌导出excel接口", response = ResultVO.class, tags={ "4.0商品配伍禁忌接口API", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/compatibity/exportListExcel", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> compatibityExportListExcel(HttpServletRequest request, @ApiParam(value = "配伍禁忌导出查询对象", required = true) @Valid @RequestBody ExportProductCompatibilityQueryVo exportVo) throws InterruptedException;





    @ApiOperation(value = "获取所有字典信息类接口，状态是未删除并且启用", notes = "获取所有字典信息类接口，状态是未删除并且启用", response = SystemDictDto.class, tags={ "product", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = SystemDictDto.class) })
    @RequestMapping(value = "/product/typeList", method = RequestMethod.POST)
    ResponseEntity<ResultVO> getProductTypeList(HttpServletRequest request,@ApiParam(value = "当前登录机构码", required = true) @RequestHeader(value = "organSign", required = true) String organSign,
                                                @ApiParam(value = "当前登录员工的ID", required = true) @RequestHeader(value = "employeeId", required = true) String employeeId,
                                                @ApiParam(value = "请求字典类型", required = true) @Valid @RequestBody SystemDictQueryVo systemDictQueryVo);

    @ApiOperation(value = "获取所有字典信息类接口，状态是未删除", notes = "获取所有字典信息类接口，状态是未删除", response = SystemDictDto.class, tags={ "product", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = SystemDictDto.class) })
    @RequestMapping(value = "/product/allTypeList", method = RequestMethod.POST)
    ResponseEntity<ResultVO> getProductAllTypeList(@ApiParam(value = "当前登录机构码", required = true) @RequestHeader(value = "organSign", required = true) String organSign,
                                                   @ApiParam(value = "当前登录员工的ID", required = true) @RequestHeader(value = "employeeId", required = true) String employeeId,
                                                   @ApiParam(value = "请求字典类型", required = true) @Valid @RequestBody SystemDictQueryVo systemDictQueryVo);
}
