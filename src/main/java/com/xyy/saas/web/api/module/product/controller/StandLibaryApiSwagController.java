package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.match.api.MatchStandardLibraryApi;
import com.xyy.saas.match.dto.MatchProductVoDto;
import com.xyy.saas.match.dto.MatchedProductDto;
import com.xyy.saas.product.core.api.StandardLibaryApi;
import com.xyy.saas.product.core.dto.MatchedSLProductDto;
import com.xyy.saas.product.core.dto.ProductVoDto;
import com.xyy.saas.product.core.dto.StandardLibaryProductVoDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @title: StandLibaryApiSwagController
 * @date 2019/12/19  19:24
 * @description:
 */
@Controller
public class StandLibaryApiSwagController implements StandLibaryApiSwag {
    private static final Logger logger = LoggerFactory.getLogger(StandLibaryApiSwagController.class);

    @Reference(version = "0.0.1")
    private MatchStandardLibraryApi matchStandardLibraryApi;

    @Override
    public ResultVO matchStandLibrary(HttpServletRequest request, @Valid @RequestBody StandardLibaryProductVoDto voDto) {
        String organSign = request.getHeader("organSign");
        String barCode = voDto.getBarCode();
        if(StringUtils.isEmpty(barCode)){
            return new ResultVO(ResultCodeEnum.SUCCESS,"条形码为空",null);
        }
        StandardLibaryProductVoDto standardLibaryProductVoDto = matchStandardLibraryApi.findStandardLibaryProductVoByBarCode(barCode,organSign);
        if(standardLibaryProductVoDto==null){
            return new ResultVO(ResultCodeEnum.SUCCESS,"未匹配到标准库信息",null);
        }
        return ResultVO.createSuccess(standardLibaryProductVoDto);
    }

    @Override
    public ResultVO banchMatchStandLibrary(HttpServletRequest request, @Valid @RequestBody MatchProductVoDto matchProductVoDto) {
        String organSign = request.getHeader("organSign");
        matchProductVoDto.setOrganSign(organSign);
        boolean flag = matchStandardLibraryApi.banchMatchStandLibrary(matchProductVoDto);
        if(!flag){
            return new ResultVO(ResultCodeEnum.ERROR,false);
        }
        return ResultVO.createSuccess(flag);
    }

    @Override
    public ResultVO queryMatchedSuccessPage(HttpServletRequest request, Integer rows, Integer page) {
        logger.info("queryMatchedSuccessPage  productbaseinfo 方法开始执行,page:{},rows:{}",rows,page);
        String organSign = request.getHeader("organSign");
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(page==null?1:page);
        pageInfo.setPageSize(rows==null?10:rows);
        PageInfo<MatchedProductDto> matchedSuccessPage= matchStandardLibraryApi.queryMatchedSuccessPage(organSign,pageInfo);
        if(matchedSuccessPage==null){
            return ResultVO.createError(ResultCodeEnum.ERROR,"未查询到标准库信息");
        }
        return ResultVO.createSuccess(matchedSuccessPage);
    }


    @Override
    public ResultVO deleteMatchedSuccess(HttpServletRequest request, Long id) {
        boolean result = true;
        String organSign = request.getHeader("organSign");
        result = matchStandardLibraryApi.deleteMatchedSuccess(id,organSign);
        if(!result){
            return ResultVO.createError(ResultCodeEnum.ERROR,"删除失败");
        }
        return ResultVO.createSuccess(result);
    }

    @Override
    public ResultVO saveMatchedSuccess(HttpServletRequest request) {
        boolean result = true;
        String organSign = request.getHeader("organSign");
        result = matchStandardLibraryApi.saveMatchedSuccess(organSign);
        if(!result){
            return ResultVO.createError(ResultCodeEnum.ERROR,"保存失败");
        }
        return ResultVO.createSuccess(result);
    }

}
