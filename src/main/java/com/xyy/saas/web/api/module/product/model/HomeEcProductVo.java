package com.xyy.saas.web.api.module.product.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * @ClassName HomeEcProductVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/12/18 15:22
 * @Version 1.0
 **/
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

@ApiModel(description = "首页EC商品展示信息实体类")
public class HomeEcProductVo {

    /**
     * ec商品id
     */
    private Long skuId;

    /** 商品主图 */
    private String imageUrl;

    /**
     * 是否有券标识
     */
    private Boolean whetherCoupon;

    /**
     * 商品名称
     */
    private String productName;
    /**
     * 通用名
     */
    private String commonName;
    /**
     * 规格
     */
    private String spec;
    /**
     * 生产厂家
     */
    private String manufacturer;
    /**
     * 采购价（EC售价）
     */
    private BigDecimal price;
    /**
     * 市场价（EC建议零售价）
     */
    private BigDecimal marketPrice;
    /**
     * 商品类型
     */
    private Integer skuType;

    @ApiModelProperty(value = "ec商品id")
    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    @ApiModelProperty(value = "商品主图")
    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    @ApiModelProperty(value = "是否有券标识")
    public Boolean getWhetherCoupon() {
        return whetherCoupon;
    }

    public void setWhetherCoupon(Boolean whetherCoupon) {
        this.whetherCoupon = whetherCoupon;
    }

    @ApiModelProperty(value = "商品名称")
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    @ApiModelProperty(value = "通用名")
    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    @ApiModelProperty(value = "规格")
    public String getSpec() {
        return spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    @ApiModelProperty(value = "生产厂家")
    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    @ApiModelProperty(value = "采购价（EC售价）")
    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @ApiModelProperty(value = "市场价（EC建议零售价）")
    public BigDecimal getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(BigDecimal marketPrice) {
        this.marketPrice = marketPrice;
    }

    @ApiModelProperty(value = "商品类型 1：药帮忙价格优势商品 2：药店缺货 3：高毛商品")
    public Integer getSkuType() {
        return skuType;
    }

    public void setSkuType(Integer skuType) {
        this.skuType = skuType;
    }
}
