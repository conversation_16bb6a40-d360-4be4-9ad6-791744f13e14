package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * @ClassName ConfigureAdjustsVo
 * @Description 配置调价方案提交信息类
 * <AUTHOR>
 * @Date 2020/8/19 18:00
 * @Version 1.0
 **/
@ApiModel(description = "配置调价方案提交信息类")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")
public class ConfigureAdjustsVo {


    @JsonProperty("adjustPref")
    private String adjustPref;//方案编号
    @JsonProperty("organSigns")
    private List<String> organSigns;//；配置的门店机构列表

    @ApiModelProperty(value = "备注")
    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    @JsonProperty("remarks")
    private String remarks;//备注

    @ApiModelProperty(value = "方案编号")
    public String getAdjustPref() {
        return adjustPref;
    }

    public void setAdjustPref(String adjustPref) {
        this.adjustPref = adjustPref;
    }

    @ApiModelProperty(value = "配置的门店机构列表")
    public List<String> getOrganSigns() {
        return organSigns;
    }

    public void setOrganSigns(List<String> organSigns) {
        this.organSigns = organSigns;
    }
}
