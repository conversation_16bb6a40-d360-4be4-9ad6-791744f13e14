package com.xyy.saas.web.api.module.member.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.common.api.SystemDictApi;
import com.xyy.saas.common.dto.SystemDictDto;
import com.xyy.saas.inventory.cores.api.InventoryApi;
import com.xyy.saas.inventory.cores.api.InventoryLotNumberApi;
import com.xyy.saas.inventory.cores.common.IRemoteBean;
import com.xyy.saas.inventory.cores.dto.InventoryTransmissDto;
import com.xyy.saas.inventory.cores.dto.InventoryVo;
import com.xyy.saas.member.core.api.MemberBaseApi;
import com.xyy.saas.member.core.api.MemberExchangeInfoApi;
import com.xyy.saas.member.core.dto.MemberBaseDto;
import com.xyy.saas.member.core.dto.MemberConvertV2Dto;
import com.xyy.saas.member.core.dto.MemberExchangeInfoV2Dto;
import com.xyy.saas.web.api.module.member.service.MemberExchangeInfoService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 积分兑换流水记录服务
 * 从会员模块上移到网关层，为了使会员不依赖库存
 * 此方案可能也只是临时方案
 * <AUTHOR>
 * @since 2020-05-30
 */
@Service
public class MemberExchangeInfoServiceImpl implements MemberExchangeInfoService {

    private static final Logger logger = LogManager.getLogger(MemberExchangeInfoService.class);

    /**
     * 会员积分兑换
     */
    public final static Integer MEMBER_DICT_CONSTANT = 8 ;

    public final static Integer MEMBER_DICT_BUSSINESS_CONSTANT = 20096 ;

    @Reference(version = "0.0.1")
    private SystemDictApi systemDictApi;

    @Reference(version = "0.0.2")
    private InventoryApi inventoryApi;

    @Reference(version = "0.0.2")
    private InventoryLotNumberApi inventoryLotNumberApi;

    @Reference(version = "0.0.1")
    private MemberBaseApi memberBaseApi;

    @Reference(version = "0.0.1")
    private MemberExchangeInfoApi memberExchangeInfoApi;

    /**
     * pos端 会员积分兑换
     * @param convertDto
     * @return Map
     */
    @Override
    public Map<String, Object> saveMemberExchangeInfo(MemberConvertV2Dto convertDto) {
        Map<String, Object> map = new HashMap<>();
        String memberGuid = convertDto.getGuid();
        String organSign = convertDto.getOrgansign();
        MemberBaseDto memberBase = memberBaseApi.getMemberBaseByGuid(memberGuid, null);
        if (memberBase == null) {
            map.put("code", 1);
            map.put("msg", "兑换失败,没有找到该会员信息");
            return map;
        }
        if (convertDto.getSalesman() == null) {
            map.put("code", 1);
            map.put("msg", "兑换失败,销售员不能为空");
            return map;
        }
        if (!org.apache.commons.lang.StringUtils.isNumeric(convertDto.getSalesman())) {
            map.put("code", 1);
            map.put("msg", "兑换失败,请传入正确的员工Id");
            return map;
        }
        //消费积分
        BigDecimal countPoint = convertDto.getCountPoint();
        //可用积分
        BigDecimal availablePoint = memberBase.getPoint();
        if (availablePoint.compareTo(countPoint) < 0) {
            map.put("code", 1);
            map.put("msg", "兑换失败,可用积分不足");
            return map;
        }
        String bussinessNo = memberExchangeInfoApi.getBusinessNo(organSign);
        SystemDictDto systemDto = systemDictApi.findBybussinessIdAndValue(MEMBER_DICT_BUSSINESS_CONSTANT, MEMBER_DICT_CONSTANT, null);
        //会员积分兑换摘要
        Integer residuePointSummary = systemDto != null ? systemDto.getId() : 0;

        List<InventoryTransmissDto> inventoryProDtoLst = new ArrayList<>();
        for (MemberExchangeInfoV2Dto exchangeInfo : convertDto.getProductVoList()) {
            exchangeInfo.setBusinessNo(bussinessNo);
            InventoryVo inventoryVo = inventoryApi.findByproductPref(exchangeInfo.getPref(), organSign);
            BigDecimal stockNumber = inventoryVo.getStockNumber();
            exchangeInfo.setInventoryNum(stockNumber.subtract(exchangeInfo.getNumber()));

            InventoryTransmissDto inventoryTransmissDto = new InventoryTransmissDto();
            inventoryTransmissDto.setLotNumber(exchangeInfo.getBatchNo());
            inventoryTransmissDto.setOrganSign(organSign);
            inventoryTransmissDto.setPositionId(exchangeInfo.getPositionId());
            inventoryTransmissDto.setProductPref(exchangeInfo.getPref());
            inventoryTransmissDto.setSaleNum(exchangeInfo.getNumber());
            inventoryTransmissDto.setStockDetailPref(bussinessNo);
            inventoryTransmissDto.setTraceCode(exchangeInfo.getTraceCode());
            inventoryProDtoLst.add(inventoryTransmissDto);
        }
        IRemoteBean<Map<String, Object>> saveInventoryResult = inventoryLotNumberApi.addOrSubLotNumInventory(inventoryProDtoLst, convertDto.getSalesman().toString(), residuePointSummary);
        if (saveInventoryResult == null) {
            logger.error("积分兑换扣减库存接口返回结果为空 param: {}", JSON.toJSONString(convertDto));
            map.put("code", 1);
            map.put("msg", "扣减库存失败");
            return map;
        }
        if (!saveInventoryResult.isSuccess()) {
            logger.error("积分兑换扣减库存异常 param: {}, result: {}", JSON.toJSONString(convertDto), JSON.toJSONString(saveInventoryResult));
            map.put("code", 1);
            map.put("msg", saveInventoryResult.getResultMsg());
            return map;
        }
        logger.error("积分兑换扣减库存 param: {}, result: {}", JSON.toJSONString(convertDto), JSON.toJSONString(saveInventoryResult));
        try {
            return memberExchangeInfoApi.saveMemberExchangeInfoV2(convertDto);
        } catch (Exception e) {
            logger.error("积分兑换操作会员数据异常 param: {}, result: {}",JSON.toJSONString(convertDto), JSON.toJSONString(saveInventoryResult));
            map.put("code", 1);
            map.put("msg", "扣减库存异常");
            return map;
        }
    }
}
