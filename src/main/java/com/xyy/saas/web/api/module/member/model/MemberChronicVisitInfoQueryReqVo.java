package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Description 会员慢病患者回访信息VO-查询
 * <AUTHOR>
 * @Create 2020-10-13 9:52
 */
@Data
public class MemberChronicVisitInfoQueryReqVo {
    //搜索条件：患者信息	支持患者手机号、姓名、卡号的模糊搜索；
    //搜索条件：关心病种	连锁门店取值连锁总部维护的病种列表（联营门店取值联营总部维护的病种列表），支持先模糊搜索再选择，支持直接选择；默认为：全部；
    //搜索条件：回访门店	枚举值：全部门店、本店；默认：全部门店；
    //搜索条件：来源门店	枚举值：全部门店、本店；默认：全部门店；
    //搜索条件：回访日期	默认为空；
    //搜索条件：创建日期	默认为空；
    @ApiModelProperty(value = "患者信息(会员信息)-混合查询")
    private String memberMixedQuery;
    @ApiModelProperty(value = "关心病种")
    private String chronicPref;
    @ApiModelProperty(value = "回访门店")
    private String reVisitDrugstore;
    @ApiModelProperty(value = "来源门店-会员来源")
    private String sourceDrugstore;
    @ApiModelProperty(value = "回访日期-开始日期")
    private String reVisitStart;
    @ApiModelProperty(value = "回访日期-结束日期")
    private String reVisitEnd;
    @ApiModelProperty(value = "创建日期-开始日期")
    private String createStart;
    @ApiModelProperty(value = "创建日期-结束日期")
    private String createEnd;
    @ApiModelProperty(value = "开始的页码")
    private Integer pageNum;
    @ApiModelProperty(value = "每页的数量")
    private Integer pageSize;
}