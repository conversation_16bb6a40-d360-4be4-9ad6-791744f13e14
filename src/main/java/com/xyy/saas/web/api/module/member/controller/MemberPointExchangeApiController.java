package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.api.SystemDictApi;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.dto.SystemDictDto;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.member.core.api.MemberLevelApi;
import com.xyy.saas.member.core.dto.MemberExchangeProductDto;
import com.xyy.saas.member.core.dto.MemberLevelDto;
import com.xyy.saas.member.core.dto.MemberPointExchangeDto;
import com.xyy.saas.member.core.dto.SpecialProductDto;
import com.xyy.saas.product.core.api.ProductApi;
import com.xyy.saas.product.core.dto.MemberDayMixResultDto;
import com.xyy.saas.product.core.dto.MemberDayQueryDto;
import com.xyy.saas.product.core.dto.ProductDto;
import com.xyy.saas.rabbitmq.core.api.MessagePushApi;
import com.xyy.saas.web.api.module.member.model.*;
import com.xyy.saas.web.api.module.member.utils.ResultCodeEnum;
import com.xyy.saas.web.api.module.utils.ExportExcelUtil;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.aspectj.apache.bcel.Constants;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/member")
public class MemberPointExchangeApiController {

	private static final Logger logger = LogManager.getLogger(MemberPointExchangeApiController.class);

    @Reference(version = "0.0.1")
    private com.xyy.saas.member.core.api.MemberPointExchangeApi memberPointExchangeApi;
    
    @Reference(version = "0.0.1")
    private MemberLevelApi memberLevelApi;

    @Reference(version = "0.0.1")
    private SystemDictApi systemDictApi;

    @Reference(version = "0.0.1")
    private MessagePushApi messagePushApi;

    @Reference(version = "0.0.1")
    private ProductApi productApi;
    
    @ApiOperation(value = "查询积分规则", notes = "查询积分规则", response = MemberPointExchange.class, tags={ "memberPointExchange", })
    @ApiResponses(value = { @ApiResponse(code = 200, message = "操作成功", response = MemberPointExchange.class) })
    @RequestMapping(value = "/memberPointExchange/query", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> query(@RequestBody MemberLevelVo memberLevelVo, @RequestHeader("organSign") String organSign){
    	MemberLevelDto memberLevelDto = new MemberLevelDto();
        BeanUtils.copyProperties(memberLevelVo, memberLevelDto);
        memberLevelDto.setOrgansign(organSign);
        MemberPointExchangeDto baseDto = new MemberPointExchangeDto();
        baseDto.setOrgansign(organSign);
        baseDto.setYn(1);
        
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(memberLevelDto.getPage() == null ? 1:memberLevelDto.getPage());
        pageInfo.setPageSize(memberLevelDto.getRows() == null ? 50:memberLevelDto.getRows());
        PageInfo<MemberPointExchangeDto> resultPage = memberPointExchangeApi.getMemberPointExchangeListPager(pageInfo, baseDto);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(resultPage), HttpStatus.OK);
    }

    @ApiOperation(value = "新增积分规则", notes = "新增积分规则", response = MemberPointExchange.class, tags={ "memberPointExchange", })
    @ApiResponses(value = { @ApiResponse(code = 200, message = "操作成功", response = MemberPointExchange.class) })
    @RequestMapping(value = "/memberPointExchange/toAdd", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> toAdd(MemberLevelVo memberLevelVo, @RequestHeader("organSign") String organSign){
    	MemberLevelDto memberLevelDto = new MemberLevelDto();
        BeanUtils.copyProperties(memberLevelVo, memberLevelDto);
        memberLevelDto.setOrgansign(organSign);
        List<SystemDictDto> systemDictList= systemDictApi.findSystemDictDto(20023, organSign);
        MemberPointExchangeDto exchangeDto = new MemberPointExchangeDto();
        if(!systemDictList.isEmpty() && systemDictList.size() > 0 ){
            SystemDictDto systemDictDto = systemDictList.get(0);
            exchangeDto.setExchangeType(systemDictDto.getId());
        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(exchangeDto), HttpStatus.OK);
    }
    
    @ApiOperation(value = "会员等级下拉框", notes = "会员等级下拉框", response = MemberLevel.class, tags={ "memberLevel", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberLevel.class) })
    @RequestMapping(value = "/memberPointExchange/memberLevelList", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> memberLevelList(MemberLevelVo memberLevelVo, @RequestHeader("organSign") String organSign) {
    	MemberLevelDto memberLevelDto = new MemberLevelDto();
        BeanUtils.copyProperties(memberLevelVo, memberLevelDto);
    	memberLevelDto.setOrgansign(organSign);
        List<MemberLevelDto> memberLevelList = memberLevelApi.getSyncMemberLevelListWithoutRelation(memberLevelDto);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(memberLevelList), HttpStatus.OK);
    }
    
    @ApiOperation(value = "规则类型下拉框", notes = "规则类型下拉框", response = MemberLevel.class, tags={ "memberLevel", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberLevel.class) })
    @RequestMapping(value = "/memberPointExchange/systemDictList", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> systemDictList(@RequestHeader("organSign") String organSign) {
        List<SystemDictDto> systemDictList = systemDictApi.findSystemDictDto(20023, organSign);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(systemDictList), HttpStatus.OK);
    }
    
    @ApiOperation(value = "更新积分规则", notes = "更新积分规则", response = MemberPointExchange.class, tags={ "memberPointExchange", })
    @ApiResponses(value = { @ApiResponse(code = 200, message = "操作成功", response = MemberPointExchange.class) })
    @RequestMapping(value = "/memberPointExchange/toUpdate", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> toUpdate(Long id, @RequestBody MemberLevelVo memberLevelVo, @RequestHeader("organSign") String organSign){
    	MemberLevelDto memberLevelDto = new MemberLevelDto();
        BeanUtils.copyProperties(memberLevelVo, memberLevelDto);
    	MemberPointExchangeDto pointExchangeDto = memberPointExchangeApi.getMemberExchangeById(memberLevelDto.getId());
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(pointExchangeDto), HttpStatus.OK);
    }
    
    @ApiOperation(value = "显示积分规则", notes = "显示积分规则", response = MemberPointExchange.class, tags={ "memberPointExchange", })
    @ApiResponses(value = { @ApiResponse(code = 200, message = "操作成功", response = MemberPointExchange.class) })
    @RequestMapping(value = "/memberPointExchange/toView", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> toView(Long id, @RequestBody MemberLevelVo memberLevelVo, @RequestHeader("organSign") String organSign){
    	MemberLevelDto memberLevelDto = new MemberLevelDto();
        BeanUtils.copyProperties(memberLevelVo, memberLevelDto);
    	MemberPointExchangeDto pointExchangeDto = memberPointExchangeApi.getMemberExchangeById(memberLevelDto.getId());
        if(pointExchangeDto.getExchangeType() == 1){
            pointExchangeDto.setExchangePrice(null);
        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(pointExchangeDto), HttpStatus.OK);
    }
    
    @ApiOperation(value = "删除积分规则", notes = "删除积分规则", response = MemberPointExchange.class, tags={ "memberPointExchange", })
    @ApiResponses(value = { @ApiResponse(code = 200, message = "操作成功", response = MemberPointExchange.class) })
    @RequestMapping(value = "/memberPointExchange/toDelete", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> toDelete(Long id, @RequestBody MemberLevelVo memberLevelVo, @RequestHeader("organSign") String organSign){
    	MemberLevelDto memberLevelDto = new MemberLevelDto();
        BeanUtils.copyProperties(memberLevelVo, memberLevelDto);
        MemberPointExchangeDto pointExchangeDto = new MemberPointExchangeDto();
        pointExchangeDto.setOrgansign(organSign);
        pointExchangeDto.setId(memberLevelDto.getId());
        pointExchangeDto.setMemberLevelId(0L);
        pointExchangeDto.setYn(0);
        memberPointExchangeApi.updateMemberPointExchange(pointExchangeDto);
        syncMqPush(organSign);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess("删除积分规则成功"), HttpStatus.OK);
    }
    
    @ApiOperation(value = "同步积分规则", notes = "同步积分规则", response = MemberPointExchange.class, tags={ "memberPointExchange", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberPointExchange.class) })
    @RequestMapping(value = "/memberPointExchange/getSyncMemberPointExchangeList",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getSyncMemberPointExchangeList(@RequestHeader(name = "organSign", required = true) String organSign, @ApiParam(value = "积分兑换规则", required = true) @RequestBody MemberPointExchange memberPointExchange){
        MemberPointExchangeDto memberPointExchangeDto = new MemberPointExchangeDto();
        BeanUtils.copyProperties(memberPointExchange,memberPointExchangeDto);
        memberPointExchangeDto.setOrgansign(organSign);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(memberPointExchangeApi.getSyncMemberPointExchangeList(memberPointExchangeDto)), HttpStatus.OK);
    }

    @ApiOperation(value = "保存或修改会员积分兑换规则", notes = "保存或修改会员积分兑换规则", response = MemberPointExchange.class, tags={ "memberPointExchange", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberPointExchange.class) })
    @RequestMapping(value = "/memberPointExchange/saveMemberPointExchange",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> saveMemberPointExchange(@RequestHeader("commonRequestModel") String commonRequestModel, @RequestHeader(name = "employeeId", required = true) Integer employeeId, @RequestHeader(name = "organSign", required = true) String organSign, @ApiParam(value = "积分兑换规则", required = true) @RequestBody MemberPointExchange memberPointExchange){
        if(StringUtils.isEmpty(memberPointExchange.getExchangeType())){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.NO_EXCHANGE_TYPE.getCode(), ResultCodeEnum.NO_EXCHANGE_TYPE.getMsg(), null), HttpStatus.OK);
        }
        if(memberPointExchange.getPoint() == null){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.NO_POINT.getCode(), ResultCodeEnum.NO_POINT.getMsg(), null), HttpStatus.OK);
        }
        if(memberPointExchange.getMemberLevelIds() == null){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.NO_MEMBER_LEVEL_ID.getCode(), ResultCodeEnum.NO_MEMBER_LEVEL_ID.getMsg(), null), HttpStatus.OK);
        }
        if(StringUtils.isEmpty(memberPointExchange.getConsumePrice())){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.NO_CONSUME_PRICE.getCode(), ResultCodeEnum.NO_CONSUME_PRICE.getMsg(), null), HttpStatus.OK);
        }
        MemberPointExchangeDto memberPointExchangeDto = new MemberPointExchangeDto();
        BeanUtils.copyProperties(memberPointExchange,memberPointExchangeDto);
        memberPointExchangeDto.setOrgansign(organSign);
        memberPointExchangeDto.setMemberLevelId(0L);
        boolean flag = false;
        if (memberPointExchangeDto.getId() != null && memberPointExchangeDto.getId() != 0) {
            memberPointExchangeDto.setUpdateUser(employeeId.toString());
            flag = memberPointExchangeApi.updateMemberPointExchange(memberPointExchangeDto);
        } else {
        	CommonRequestModel model = JSONObject.parseObject(commonRequestModel, CommonRequestModel.class);
        	String employeeName = model.decodeContent(model.getEmployeeName());
            memberPointExchangeDto.setCreateUser(employeeId+"");
            memberPointExchangeDto.setCreateUserName(employeeName);
            flag = memberPointExchangeApi.saveMemberPointExchange(memberPointExchangeDto);
        }
        if (flag) {
            return new ResponseEntity(new ResultVO(0, "操作成功", flag), HttpStatus.OK);
        } else {
            return new ResponseEntity(new ResultVO(-1, "操作失败,请填写正确信息", flag), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "门店端:商品积分规则列表", notes = "门店端:商品积分规则列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberExchangeProductDto.class) })
    @RequestMapping(value = "/memberPointExchange/getPointProductPager", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<com.xyy.saas.common.util.ResultVO> getPointProductPager(@RequestHeader("commonRequestModel") String commonRequestModel, @RequestBody MemberExchangeProductVo vo){
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        MemberExchangeProductDto exchangeProductDto = new MemberExchangeProductDto();
        BeanUtils.copyProperties(vo, exchangeProductDto);
        exchangeProductDto.setOrganSign(model.getOrganSign());
        exchangeProductDto.setHeadquartersOrganSign(model.getHeadquartersOrganSign());
        exchangeProductDto.setBizModel(model.getBizModel());
        exchangeProductDto.setOrganSignType(model.getOrganSignType());
        exchangeProductDto.setProductName(vo.getCommonName());
        PageInfo pageInfo = memberPointExchangeApi.getPointProductListPager(exchangeProductDto);
        return new ResponseEntity<com.xyy.saas.common.util.ResultVO>(com.xyy.saas.common.util.ResultVO.createSuccess(pageInfo), HttpStatus.OK);
    }

    @ApiOperation(value = "门店端:商品积分规则导出", notes = "门店端:商品积分规则导出")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功") })
    @RequestMapping(value = "/memberPointExchange/exportProductIntegral",  method = RequestMethod.POST)
    public void exportProductIntegral(@RequestHeader("commonRequestModel") String commonRequestModel, HttpServletRequest request, HttpServletResponse response,
                                      @RequestBody MemberExchangeProductVo vo){
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        MemberExchangeProductDto exchangeProductDto = new MemberExchangeProductDto();
        BeanUtils.copyProperties(vo, exchangeProductDto);
        exchangeProductDto.setOrganSign(model.getOrganSign());
        exchangeProductDto.setHeadquartersOrganSign(model.getHeadquartersOrganSign());
        exchangeProductDto.setBizModel(model.getBizModel());
        exchangeProductDto.setOrganSignType(model.getOrganSignType());
        exchangeProductDto.setPageNum(1);
        exchangeProductDto.setPageSize(1000000);
        PageInfo pageInfo = memberPointExchangeApi.getPointProductListPager(exchangeProductDto);
        List<ProductDto> list = pageInfo.getList();
        if (list != null && !list.isEmpty()) {
            list.stream().forEach(dto -> {
                if (dto.getScoreProductYn() == Constants.ATTR_CONSTANT_VALUE) {
                    dto.setBarCode("是");
                }else {
                    dto.setBarCode("否");
                }
                //保留一位小数
                dto.setScoreRate(dto.getScoreRate().setScale(1, BigDecimal.ROUND_HALF_UP));
            });
        }
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
        String extfilename="商品积分规则"+df.format(new Date())+".xls";
        String sheetName = "商品积分规则";
        String headers[] = new String[]{"通用名", "规格","单位","商品名称", "商品编号","标准库ID", "生产厂家","商品分类","是否为积分商品","积分倍数"};
        String fieldNames[] = new String[]{"commonName", "attributeSpecification", "unitName", "productName","pharmacyPref", "standardLibraryId", "manufacturer", "systemTypeName", "barCode", "scoreRate"};
        try {
            ExportExcelUtil.createExcel(response,request,extfilename,sheetName, headers, fieldNames, list,true);
        } catch (Exception e) {
            logger.error("exportProductIntegral is error.", e);
        }
    }

    @ApiOperation(value = "门店端:保存商品积分规则", notes = "门店端:保存商品积分规则")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberExchangeProductDto.class) })
    @RequestMapping(value = "/memberPointExchange/savePointProduct", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<com.xyy.saas.common.util.ResultVO> updateSpecialProduct(@RequestHeader("commonRequestModel") String commonRequestModel, @RequestBody MemberExchangeProductVo vo){
//        if(vo.getProductUpdateDtos() == null){
//            return new ResponseEntity<com.xyy.saas.common.util.ResultVO>(com.xyy.saas.common.util.ResultVO.createError(com.xyy.saas.common.util.ResultCodeEnum.ERROR,"商品编号列表为空"),HttpStatus.OK);
//        }
        int result = memberPointExchangeApi.batchUpdateSpecialProductInfo(vo.getProductUpdateDtos());
        if(result<=0){
            return new ResponseEntity<com.xyy.saas.common.util.ResultVO>(com.xyy.saas.common.util.ResultVO.createError(com.xyy.saas.common.util.ResultCodeEnum.ERROR,"没发生异常，但是更新失败，需要排查原因"),HttpStatus.OK);
        }
        return new ResponseEntity<com.xyy.saas.common.util.ResultVO>(com.xyy.saas.common.util.ResultVO.createSuccess(com.xyy.saas.common.util.ResultCodeEnum.SUCCESS),HttpStatus.OK);
    }

    public void syncMqPush(String organsign) {
        JSONObject msg = new JSONObject();
        String [] table = {"saas_member_point_exchange", "saas_member_level_pointexchange_relation"};
        msg.put("code", "sync");
        msg.put("tables", table);
        messagePushApi.sendMsgByOrganSign(organsign,msg.toString());
    }

}
