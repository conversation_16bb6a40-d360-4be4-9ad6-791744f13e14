package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import com.xyy.saas.common.api.SystemDictApi;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.dto.SystemDictDto;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.member.core.api.MemberPrepayCardConfigApi;
import com.xyy.saas.member.core.api.MemberShareApi;
import com.xyy.saas.member.core.dto.*;
import com.xyy.saas.member.core.enums.MemberPreferentialDayEnum;
import com.xyy.saas.member.core.enums.MemberShareStatusEnum;
import com.xyy.saas.rabbitmq.core.api.MessagePushApi;
import com.xyy.saas.web.api.module.member.model.*;
import com.xyy.saas.web.api.module.member.utils.ResultCodeEnum;
import com.xyy.user.module.api.EmployeefileApi;
import com.xyy.user.module.dto.EmployeefileRequestModel;
import com.xyy.user.module.dto.result.EmployeeDto;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/member")
public class MemberLevelApiController {

    private static final Logger logger = LogManager.getLogger(MemberLevelApiController.class);

    @Reference(version = "0.0.1")
    private com.xyy.saas.member.core.api.MemberLevelApi memberLevelApi;

    @Reference(version = "0.0.1")
    private com.xyy.saas.member.core.api.MemberBaseApi memberBaseApi;

    @Reference(version = "0.0.1")
    private MessagePushApi messagePushApi;

    @Reference(version = "0.0.1")
    private SystemDictApi systemDictApi;

    @Reference(version = "0.0.1")
    private EmployeefileApi employeefileApi;

    @Reference(version = "0.0.1")
    private MemberPrepayCardConfigApi memberPrepayCardConfigApi;

    @Reference(version = "0.0.1")
    private MemberShareApi memberShareApi;

    @ApiOperation(value = "更新或保存会员级别", notes = "更新或保存会员级别", response = MemberLevel.class, tags={ "memberLevel", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberLevel.class) })
    @RequestMapping(value = "/memberLevel/save", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> save(@RequestBody MemberLevelVo memberLevelVo, @RequestHeader("commonRequestModel") String commonRequestModel, @RequestHeader("employeeId") Integer employeeId, @RequestHeader("organSign") String organSign) {
        CommonRequestModel model = JSONObject.parseObject(commonRequestModel, CommonRequestModel.class);
        if (StringUtils.isEmpty(memberLevelVo.getName())) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.NO_LEVEL_NAME.getCode(),ResultCodeEnum.NO_LEVEL_NAME.getMsg(), null), HttpStatus.OK);
        }
        if (StringUtils.isEmpty(memberLevelVo.getDiscount())) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.NO_DISCOUNT.getCode(),ResultCodeEnum.NO_DISCOUNT.getMsg(), null), HttpStatus.OK);
        }
        if (StringUtils.isEmpty(memberLevelVo.getUpPoint())) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.NO_UP_POINT.getCode(),ResultCodeEnum.NO_UP_POINT.getMsg(), null), HttpStatus.OK);
        }
        if (StringUtils.isEmpty(memberLevelVo.getPriceStrategy())) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.NOPRICE_STRATEGY.getCode(),ResultCodeEnum.NOPRICE_STRATEGY.getMsg(), null), HttpStatus.OK);
        }
        MemberLevelDto memberLevelDto = new MemberLevelDto();
        BeanUtils.copyProperties(memberLevelVo, memberLevelDto);
        memberLevelDto.setNeedPoint(memberLevelVo.getUpPoint());
        boolean flag = false;
        try {
            //会员上级
            if (memberLevelVo.getUpLevelId() != null && memberLevelVo.getUpLevelId() != 0) {
                MemberLevelDto bean = memberLevelApi.getMemberLevelById(memberLevelVo.getUpLevelId());
                if (bean.getYn() == 1 && memberLevelVo.getUpPoint().compareTo(bean.getUpPoint()) > -1 && bean.getUpPoint().compareTo(new BigDecimal(0)) == 1) {
                    return new ResponseEntity(new ResultVO(-1, "当前等级升级所需积分不能比上级等级对应的升级所需积分高", flag), HttpStatus.OK);
                }
            }
            //会员下级
            if (memberLevelVo.getId() != null && memberLevelVo.getId() != 0 ) {
                List<MemberLevelDto> listDto = memberLevelApi.getMemberLevelByUpLevelId(memberLevelVo.getId());
                if (listDto != null && listDto.size() > 0) {
                    MemberLevelDto levelDto = listDto.get(0);
                    if (memberLevelVo.getUpPoint().compareTo(levelDto.getUpPoint()) < 1 && memberLevelVo.getUpPoint().compareTo(new BigDecimal(0)) == 1) {
                        return new ResponseEntity(new ResultVO(-1, "当前等级升级所需积分不能比下级等级对应的升级所需积分低", flag), HttpStatus.OK);
                    }
                }
            }
            //会员储值集合Vo
            MemberPrepayCardConfigSaveVo configVo = memberLevelVo.getMemberPrepayCardConfigSaveVo();
            if (configVo != null) {
                List<MemberPrepayCardConfigVo> list = configVo.getList();
                List<MemberPrepayCardConfigDto> copyList = new ArrayList<>();
                if (list != null && list.size() > 0) {
                    MemberPrepayCardConfigDto dto;
                    for (MemberPrepayCardConfigVo item : list) {
                        dto = new MemberPrepayCardConfigDto();
                        BeanUtils.copyProperties(item, dto);
                        dto.setOrgansign(organSign);
                        if (dto.getId() == null) {
                            dto.setCreateUser(employeeId + "");
                        } else {
                            dto.setUpdateUser(employeeId + "");
                        }
                        copyList.add(dto);

                    }
                    com.xyy.saas.member.core.response.ResultVO<Boolean> resultVO = memberPrepayCardConfigApi.checkConfigData(copyList);
                    if (!resultVO.getResult()) {
                        return new ResponseEntity(new ResultVO(-1, "会员储值设置区间不能重叠，下限应该小于上限", false), HttpStatus.OK);
                    }
                }
                MemberPrepayCardConfigSaveDto saveDto = new MemberPrepayCardConfigSaveDto();
                saveDto.setList(copyList);
                saveDto.setDeleteIds(configVo.getDeleteIds());
                saveDto.setLevelIds(configVo.getLevelIds());
                saveDto.setEmployeeId(employeeId + "");
                memberLevelDto.setMemberPrepayCardConfigSaveDto(saveDto);
            }
            //会员日集合Vo
            MemberPreferentialDaySaveVo dayVo = memberLevelVo.getMemberPreferentialDaySaveVo();
            List<MemberPreferentialProductDto> proDtoList;
            if (dayVo != null) {
                MemberPreferentialDayDto dayDto = new MemberPreferentialDayDto();
                BeanUtils.copyProperties(dayVo, dayDto);
                dayDto.setOrgansign(organSign);
                Byte isDelete = dayVo.getIsDelete();
                if (isDelete == null) {
                    isDelete = (byte) 1;
                    dayDto.setIsDelete(isDelete);
                }
                if (isDelete.equals((byte) 1)) {
                    if (dayVo.getMeanwhileTemplete() != null && dayVo.getMeanwhileTemplete().equals(MemberPreferentialDayEnum.YES.getKey())) {
                        if (StringUtil.isEmpty(dayVo.getTempleteName())) {
                            logger.info("会员等级 -- 模板名称空！");
                            return new ResponseEntity<ResultVO>(new ResultVO(-1, "模板名称不能为空", flag), HttpStatus.OK);
                        }
                    }
                    if (StringUtils.isEmpty(dayVo.getProductChoiceModel())) {
                        return new ResponseEntity(new ResultVO(-1, "参数productChoiceModel不能为空", false), HttpStatus.OK);
                    }
                    List<MemberPreferentialProductSaveVo> productList = dayVo.getMemberPreferentialProductVoList();
                    if (productList == null || productList.isEmpty()) {
                        if (!dayVo.getProductChoiceModel().equals(11)) {
                            logger.info("会员等级 -- 会员日对应商品列表为空！");
                            return new ResponseEntity<ResultVO>(new ResultVO(-1, "会员日对应商品列表不能为空", flag), HttpStatus.OK);
                        }
                    }
                    proDtoList = Lists.newArrayList();
                    List<MemberPreferentialProductSaveVo> proVoList = dayVo.getMemberPreferentialProductVoList();
                    if (proVoList != null && proVoList.size() > 0) {
                        for (MemberPreferentialProductSaveVo proVo : proVoList) {
                            MemberPreferentialProductDto proDto = new MemberPreferentialProductDto();
                            BeanUtils.copyProperties(proVo, proDto);
                            proDto.setOrgansign(organSign);
                            proDtoList.add(proDto);
                        }
                    }
                    dayDto.setMemberPreferentialProductDtoList(proDtoList);
                }
                memberLevelDto.setMemberPreferentialDay(dayDto);
            }
            //添加积分规则
            MemberPointExchange memberPointExchange = memberLevelVo.getMemberPointExchange();
            if (memberPointExchange != null && memberPointExchange.getConsumePrice()!=null && memberPointExchange.getPoint()!=null) {
                if(memberPointExchange.getYn()==null || memberPointExchange.getYn()==1) {
                    if (StringUtils.isEmpty(memberPointExchange.getExchangeType())) {
                        return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.NO_EXCHANGE_TYPE.getCode(), ResultCodeEnum.NO_EXCHANGE_TYPE.getMsg(), null), HttpStatus.OK);
                    }
                    if (memberPointExchange.getPoint() == null) {
                        return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.NO_POINT.getCode(), ResultCodeEnum.NO_POINT.getMsg(), null), HttpStatus.OK);
                    }
                    if (StringUtils.isEmpty(memberPointExchange.getConsumePrice())) {
                        return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.NO_CONSUME_PRICE.getCode(), ResultCodeEnum.NO_CONSUME_PRICE.getMsg(), null), HttpStatus.OK);
                    }
                }
                MemberPointExchangeDto memberPointExchangeDto = new MemberPointExchangeDto();
                BeanUtils.copyProperties(memberPointExchange,memberPointExchangeDto);
                memberPointExchangeDto.setOrgansign(organSign);
                if (memberPointExchangeDto.getId() != null && memberPointExchangeDto.getId() != 0) {
                    memberPointExchangeDto.setUpdateUser(employeeId + "");
                } else {
                    memberPointExchangeDto.setCreateUser(employeeId + "");
                }
                memberLevelDto.setMemberPointExchangeDto(memberPointExchangeDto);
            }
            boolean level;
            if (memberLevelDto.getId() != null) {
                memberLevelDto.setOrgansign(organSign);
                memberLevelDto.setUpdateUser(employeeId.toString());
                level = memberLevelApi.updateMemberLevel(memberLevelDto);
            } else {
                memberLevelDto.setOrgansign(organSign);
                memberLevelDto.setCreateUser(employeeId + "");
                memberLevelDto.setStatus(1);
                memberLevelDto.setYn(1);
                level = memberLevelApi.saveMemberLevel(memberLevelDto);

            }
            if(level){
                return new ResponseEntity<ResultVO>(new ResultVO(0, "操作成功", true), HttpStatus.OK);
            }else{
                return new ResponseEntity<ResultVO>(new ResultVO(-1, "操作失败,请填写正确信息", flag), HttpStatus.OK);
            }
        } catch (Exception e) {
            logger.info("新增或修改会员级别失败" + e.getMessage());
            e.printStackTrace();
            return new ResponseEntity<ResultVO>(new ResultVO(-1, "操作失败,请填写正确信息", flag), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "新增会员等级", notes = "新增会员等级", response = MemberLevel.class, tags={ "memberLevel", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberLevel.class) })
    @RequestMapping(value = "/memberLevel/toAdd", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> toAdd(@RequestBody MemberLevelVo memberLevelVo, @RequestHeader("employeeId") Integer employeeId, @RequestHeader("organSign") String organSign) {
        MemberLevelDto memberLevelDto = new MemberLevelDto();
        BeanUtils.copyProperties(memberLevelVo, memberLevelDto);
        memberLevelDto.setOrgansign(organSign);
        memberLevelDto.setUpPoint(new BigDecimal(0.00));
        memberLevelDto.setDiscount(100.00);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(memberLevelDto),HttpStatus.OK);
    }

    @ApiOperation(value = "会员等级下拉框", notes = "会员等级下拉框", response = MemberLevel.class, tags={ "memberLevel", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberLevel.class) })
    @RequestMapping(value = "/memberLevel/memberLevelList", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> memberLevelList(@RequestHeader("commonRequestModel") String commonRequestModel,@RequestBody MemberLevelVo memberLevelVo) {
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        String organSign = null;
        if (model.getBizModel() == 1) {
            organSign = model.getOrganSign();
        }else{
//            organSign = model.getHeadquartersOrganSign();
            List<MemberShareDto> shareRel = memberShareApi.selectByOrganSign(model.getOrganSign()).getResult();
            if ((CollectionUtils.isEmpty(shareRel) && model.getBizModel() != 2)
                    || (!CollectionUtils.isEmpty(shareRel) && shareRel.get(0).getShareStatus() == MemberShareStatusEnum.SHARE_NO.getCode())){
                //不共享会员的联营/连锁门店拿门店自己的会员等级
                organSign = model.getOrganSign();
            }else {
                organSign = model.getHeadquartersOrganSign();
            }
        }
        MemberLevelDto memberLevelDto = new MemberLevelDto();
        BeanUtils.copyProperties(memberLevelVo, memberLevelDto);
        memberLevelDto.setOrgansign(organSign);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(memberLevelDto.getPage() == null ? 1 : memberLevelDto.getPage());
        pageInfo.setPageSize(memberLevelDto.getRows() == null ? 100 : memberLevelDto.getRows());
        PageInfo<MemberLevelDto> resultList = memberLevelApi.getMemberLevelListPager(pageInfo, memberLevelDto);
        List<MemberLevelDto> list = Lists.newArrayList();
        if (resultList != null) {
            list = resultList.getList();
        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(list), HttpStatus.OK);
    }


    @ApiOperation(value = "新增页面会员等级下拉框", notes = "新增页面会员等级下拉框", response = MemberLevel.class, tags={ "memberLevel", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberLevel.class) })
    @RequestMapping(value = "/memberLevel/memberLevelListForAdd", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> memberLevelListForAdd(@RequestBody MemberLevelVo memberLevelVo, @RequestHeader("organSign") String organSign) {
        MemberLevelDto memberLevelDto = new MemberLevelDto();
        BeanUtils.copyProperties(memberLevelVo, memberLevelDto);
        memberLevelDto.setOrgansign(organSign);
        List<MemberLevelDto> memberLevelList = memberLevelApi.getSyncMemberLevelList(memberLevelDto);

        List<MemberLevelDto> finalMemberLevelList = new ArrayList<MemberLevelDto>();
        Map<Long,MemberLevelDto> memberMap =new HashMap<Long,MemberLevelDto>();
        for(MemberLevelDto dto :memberLevelList){
            if(dto.getUpLevelId()!=null&&dto.getUpLevelId()!=0){
                memberMap.put(dto.getUpLevelId(), dto);
            }
        }

        for(MemberLevelDto dto : memberLevelList) {
            if(memberMap.get(dto.getId()) == null) {
                finalMemberLevelList.add(dto);
            }
        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(finalMemberLevelList),HttpStatus.OK);
    }

    @ApiOperation(value = "修改页面会员等级下拉框", notes = "修改页面会员等级下拉框", response = MemberLevel.class, tags={ "memberLevel", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberLevel.class) })
    @RequestMapping(value = "/memberLevel/memberLevelListForUpdate", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> memberLevelListForUpdate(@RequestBody MemberLevelVo memberLevelVo, @RequestHeader("organSign") String organSign) {
        logger.info("xxxxxxxxxxxxxxx: " + memberLevelVo.getId());
        MemberLevelDto  memberLevelDto1 = memberLevelApi.getMemberLevelById(memberLevelVo.getId());
        MemberLevelDto memberLevelDto = new MemberLevelDto();
//        BeanUtils.copyProperties(memberLevelVo, memberLevelDto);
        memberLevelDto.setOrgansign(organSign);
        List<MemberLevelDto> memberLevelList = memberLevelApi.getSyncMemberLevelList(memberLevelDto);
//        List<MemberLevelDto> finalMemberLevelList = new ArrayList<MemberLevelDto>();
//        Map<Long,MemberLevelDto> memberMap =new HashMap<Long,MemberLevelDto>();
//        for(MemberLevelDto dto :memberLevelList){
//            if(dto.getUpLevelId()!=null&&dto.getUpLevelId()!=0){
//            	memberMap.put(dto.getUpLevelId(), dto);
//            }
//        }
//
//        for(MemberLevelDto dto : memberLevelList) {
//        	if(memberLevelVo.getUpLevelId() != dto.getId() && memberMap.get(dto.getId()) == null) {
//        		finalMemberLevelList.add(dto);
//        	}
//        }

        Iterator<MemberLevelDto> it = memberLevelList.iterator();
        Map<Long,MemberLevelDto> tmpMap =new HashMap<Long,MemberLevelDto>();
        for(MemberLevelDto dto :memberLevelList){
            if(dto.getUpLevelId()!=null&&dto.getUpLevelId()!=0){
                tmpMap.put(dto.getUpLevelId(), dto);
            }
        }
        try {
            int tmpsize = tmpMap.size();
            while(it.hasNext()){
                MemberLevelDto m = it.next();
                //排除同名的
                if(m.getName().equals(memberLevelDto1.getName())){
                    it.remove();///                continue;
                }
                //排除自己选择上级等级之外的等级
                if(!m.getId().equals(memberLevelDto1.getUpLevelId())  && tmpMap.get(m.getId())!=null){
                    it.remove();
                    continue;
                }
                Long lId = memberLevelDto1.getId();
                for(int i=0;i<tmpsize;i++){
                    MemberLevelDto downLevel = tmpMap.get(lId);
                    if(downLevel == null){
                        break;
                    }
                    if(m.getId().equals(downLevel.getId())){
                        it.remove();
                        break;
                    }
                    lId = downLevel.getId();
                }
            }
        }catch(Exception e) {
            e.printStackTrace();
        }


        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(memberLevelList),HttpStatus.OK);
    }


    @ApiOperation(value = "价格策略下拉框", notes = "价格策略下拉框", response = MemberLevel.class, tags={ "memberLevel", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberLevel.class) })
    @RequestMapping(value = "/memberLevel/systemDictList", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> systemDictList(@RequestHeader("organSign") String organSign) {
        List<SystemDictDto> systemDictList = systemDictApi.findSystemDictDto(20031, organSign);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(systemDictList),HttpStatus.OK);
    }

    @ApiOperation(value = "创建人下拉框", notes = "创建人下拉框", response = MemberLevel.class, tags={ "memberLevel", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberLevel.class) })
    @RequestMapping(value = "/memberLevel/memberCreateUserList", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> memberCreateUserList(@RequestHeader("organSign") String organSign) {
        EmployeefileRequestModel model = new EmployeefileRequestModel();
        model.setPageSize(500);
        model.setOrganSign(organSign);
//        List<SystemDictDto> systemDictList = employeeApi.queryEmployeeByCondition(model, organSign);
        com.xyy.saas.common.util.ResultVO v = employeefileApi.getEmployeefileByCondition(model);
        PageInfo<EmployeeDto> page = (PageInfo<EmployeeDto>) v.getResult();
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(page.getList()),HttpStatus.OK);
    }

    @ApiOperation(value = "查看会员等级", notes = "查看会员等级", response = MemberLevel.class, tags={ "memberLevel", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberLevel.class) })
    @RequestMapping(value = "/memberLevel/toView", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> toView(@RequestBody MemberLevelVo memberLevelVo, @RequestHeader("organSign") String organSign) {
        MemberLevelDto memberLevelDto = memberLevelApi.getMemberLevelById(memberLevelVo.getId());
//        memberLevelDto.setOrgansign(memberLevelDto.getOrgansign());
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(memberLevelDto),HttpStatus.OK);
    }

    @ApiOperation(value = "通过id获取会员等级名称", notes = "通过id获取会员等级名称", response = MemberLevel.class, tags={ "memberLevel", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberLevel.class) })
    @RequestMapping(value = "/memberLevel/findById", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getLevelById(@RequestBody MemberLevelVo memberLevelVo) {
        MemberLevelDto MemberLevelDto = memberLevelApi.getMemberLevelById(memberLevelVo.getId());
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(MemberLevelDto),HttpStatus.OK);
    }

    @ApiOperation(value = "会员等级", notes = "会员等级", response = MemberLevel.class, tags={ "memberLevel", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberLevel.class) })
    @RequestMapping(value = "/memberLevel/toUpdate", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> toUpdate(@RequestBody MemberLevelVo memberLevelVo, @RequestHeader("organSign") String organSign){
        MemberLevelDto  dto = memberLevelApi.getMemberLevelById(memberLevelVo.getId());
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(dto),HttpStatus.OK);
    }

    @ApiOperation(value = "验证会员级别名称", notes = "验证会员级别名称", response = MemberLevel.class, tags={ "memberLevel", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberLevel.class) })
    @RequestMapping(value = "/memberLevel/validMemberLevelName", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> validMemberLevelName(@RequestBody MemberLevelVo memberLevelVo, @RequestHeader("organSign") String organSign){
        MemberLevelDto  memberLevelDto = memberLevelApi.getMemberLevelById(memberLevelVo.getId());
        if(memberLevelDto == null) {
            memberLevelDto = new MemberLevelDto();
        }
        BeanUtils.copyProperties(memberLevelVo, memberLevelDto);
        memberLevelDto.setOrgansign(organSign);
        int result = memberLevelApi.validLevelNameExists(memberLevelDto);
        if(result > 0){
            return new ResponseEntity<ResultVO>(new ResultVO(-1,"等级名称已存在，请重新输入等级名称！",null) ,HttpStatus.OK);
        }
        return new ResponseEntity<ResultVO>(new ResultVO(0,"操作成功",null) ,HttpStatus.OK);
    }


    @ApiOperation(value = "获取同步会员级别集合", notes = "获取同步会员级别集合", response = MemberLevel.class, tags={ "memberLevel", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberLevel.class) })

    @RequestMapping(value = "/memberLevel/getSyncMemberLevelList",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getSyncMemberLevelList(@RequestHeader(name = "organSign", required = true) String organSign, @ApiParam(value = "会员级别", required = true) @RequestBody MemberLevel memberLevel){
        MemberLevelDto mlt = new MemberLevelDto();
        BeanUtils.copyProperties(memberLevel,mlt);
        mlt.setOrgansign(organSign);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(memberLevelApi.getSyncMemberLevelList(mlt)),HttpStatus.OK);
    }

    @ApiOperation(value = "保存或修改会员级别", notes = "保存或修改会员级别", response = MemberLevel.class, tags={ "memberLevel", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberLevel.class) })

    @RequestMapping(value = "/memberLevel/saveMemberLevel",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> saveMemberLevel(@RequestHeader(name = "employeeId", required = true) Integer employeeId,@RequestHeader(name = "organSign", required = true)String organSign, @ApiParam(value = "会员级别" ,required=true )  @RequestBody MemberLevel data){
        if(StringUtils.isEmpty(data.getName())){
            return new ResponseEntity<ResultVO>(new ResultVO( ResultCodeEnum.NO_LEVEL_NAME.getCode() ,ResultCodeEnum.NO_LEVEL_NAME.getMsg() ,null),HttpStatus.OK);
        }
        if(StringUtils.isEmpty(data.getUpLevelId())){
            return new ResponseEntity<ResultVO>(new ResultVO( ResultCodeEnum.NO_UP_LEVEL_ID.getCode() ,ResultCodeEnum.NO_UP_LEVEL_ID.getMsg(),null),HttpStatus.OK);
        }
        if(StringUtils.isEmpty(data.getDiscount())){
            return new ResponseEntity<ResultVO>(new ResultVO( ResultCodeEnum.NO_DISCOUNT.getCode(),ResultCodeEnum.NO_DISCOUNT.getMsg(),null ),HttpStatus.OK);
        }
        if(StringUtils.isEmpty(data.getUpPoint())){
            return new ResponseEntity<ResultVO>(new ResultVO( ResultCodeEnum.NO_UP_POINT.getCode() , ResultCodeEnum.NO_UP_POINT.getMsg() ,null),HttpStatus.OK);
        }
        if(StringUtils.isEmpty(data.getPriceStrategy())){
            return new ResponseEntity<ResultVO>(new ResultVO( ResultCodeEnum.NOPRICE_STRATEGY.getCode() , ResultCodeEnum.NOPRICE_STRATEGY.getMsg() ,null),HttpStatus.OK);
        }
        if(StringUtils.isEmpty(data.getStatus())){
            return new ResponseEntity<ResultVO>(new ResultVO( ResultCodeEnum.NO_STATUS.getCode(),ResultCodeEnum.NO_STATUS.getMsg(),null ),HttpStatus.OK);
        }
        if(StringUtils.isEmpty(data.getYn())){
            return new ResponseEntity<ResultVO>(new ResultVO( ResultCodeEnum.NO_YN.getCode() ,ResultCodeEnum.NO_YN.getMsg() ,null),HttpStatus.OK);
        }
        MemberLevelDto mlt = new MemberLevelDto();
        BeanUtils.copyProperties(data,mlt);
        mlt.setOrgansign(organSign);
        boolean flag = false;
        if (mlt.getId() != null && mlt.getId() != 0) {
            mlt.setUpdateUser(employeeId.toString());
            flag = memberLevelApi.updateMemberLevel(mlt);
        } else {
            mlt.setCreateUser(employeeId.toString());
            flag = memberLevelApi.saveMemberLevel(mlt);
        }
        if (flag) {
            return new ResponseEntity(new ResultVO(0, "操作成功", flag), HttpStatus.OK);
        } else {
            return new ResponseEntity(new ResultVO(-1, "操作失败,请填写正确信息", flag), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "是否能删除会员级别", notes = "是否能删除会员级别", response = MemberLevel.class, tags = {"memberLevel",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberLevel.class)})
    @RequestMapping(value = "/memberLevel/isLogicDel", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> isLogicDelMemberLevel(@RequestHeader(name = "organSign", required = true) String organSign,@RequestBody MemberLevelVo memberLevelVo) {
        if (memberLevelVo.getId() == null || memberLevelVo.getId() == 0) {
            return new ResponseEntity<ResultVO>(new ResultVO(-1, "会员id不能为空", null), HttpStatus.OK);
        }
        MemberLevelDto bean = memberLevelApi.getMemberLevelById(memberLevelVo.getId());
        int count = 0;
        if (bean == null) {
            return new ResponseEntity<ResultVO>(new ResultVO(-1, "该会员等级不存在", null), HttpStatus.OK);
        } else {
            count = memberBaseApi.getLevelTakeCount(memberLevelVo.getId(), organSign);
        }
        if (count == 0) {
            return new ResponseEntity(new ResultVO(0, "操作成功", true), HttpStatus.OK);
        } else {
            return new ResponseEntity(new ResultVO(-1, "该会员等级已被" + count + "个会员使用，无法删除", false), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "逻辑删除会员级别", notes = "逻辑删除会员级别", response = MemberLevel.class, tags = {"memberLevel",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberLevel.class)})
    @RequestMapping(value = "/memberLevel/logicDel", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> logicDelMemberLevel(@RequestHeader(name = "employeeId", required = true)Integer employeeId,@RequestHeader(name = "organSign", required = true) String organSign,@RequestBody MemberLevelVo memberLevelVo) {
        if (memberLevelVo.getId() == null || memberLevelVo.getId() == 0) {
            return new ResponseEntity<ResultVO>(new ResultVO(-1, "会员id不能为空", null), HttpStatus.OK);
        }
        MemberLevelDto bean = memberLevelApi.getMemberLevelById(memberLevelVo.getId());
        if (bean == null) {
            return new ResponseEntity<ResultVO>(new ResultVO(-1, "该会员等级不存在", null), HttpStatus.OK);
        } else {
            //逻辑删除会员等级，包括会员日设置和会员储值设置
            bean.setOrgansign(organSign);
            bean.setUpdateTime(new Date());
            bean.setUpdateUser(employeeId.toString());
            bean.setId(memberLevelVo.getId());
            bean.setYn(0);
        }
        Boolean flag = false;
        flag = memberLevelApi.logicDelMemberLevel(bean);
        if (flag) {
            return new ResponseEntity(new ResultVO(0, "操作成功", true), HttpStatus.OK);
        } else {
            return new ResponseEntity(new ResultVO(-1, "操作失败，无法删除", false), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "同步其他会员等级查询列表", notes = "不分页查询会员等级列表", response = MemberLevel.class, tags={ "memberLevel", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberLevel.class) })
    @RequestMapping(value = "/memberLevel/levelList", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getLevelList(@RequestBody MemberLevelVo memberLevelVo, @RequestHeader(name = "organSign", required = true) String organSign) {

        MemberLevelDto memberLevelDto = new MemberLevelDto();
        BeanUtils.copyProperties(memberLevelVo, memberLevelDto);
        memberLevelDto.setOrgansign(organSign);
        memberLevelDto.setId(null);
        List<MemberLevelDto> memberLevelList = memberLevelApi.getSyncMemberLevelList(memberLevelDto);
        List<MemberLevelDto> list = Lists.newArrayList();
        if (memberLevelVo.getId() != null && memberLevelVo.getId() > 0) {
            for (MemberLevelDto levelDto : memberLevelList) {
                if (!levelDto.getId().equals(memberLevelVo.getId())) {
                    list.add(levelDto);
                }
            }
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(list), HttpStatus.OK);
        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(memberLevelList), HttpStatus.OK);
    }

//-----------------------------------------------------改造会员等级--------------------------------------------------------------------------------------

    @ApiOperation(value = "门店端:查询等级列表(获取同步会员级别集合)", notes = "查询等级列表(获取同步会员级别集合)", response = MemberLevel.class, tags={ "memberLevel", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberLevel.class) })
    @RequestMapping(value = "/memberLevel/query")
    public ResponseEntity<ResultVO> query(@RequestHeader("commonRequestModel") String commonRequestModel, @RequestBody MemberLevelVo memberLevelVo) {
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        logger.info("门店端:查询等级列表,药店机构号:{},入参：{}",model.getOrganSign(), JSON.toJSONString(memberLevelVo));
        String organSign = null;
        if (model.getBizModel() == 1) {
            organSign = model.getOrganSign();
        }else{
            List<MemberShareDto> shareRel = memberShareApi.selectByOrganSign(model.getOrganSign()).getResult();
            if ((CollectionUtils.isEmpty(shareRel) && model.getBizModel() != 2)
                    || (!CollectionUtils.isEmpty(shareRel) && shareRel.get(0).getShareStatus() == MemberShareStatusEnum.SHARE_NO.getCode())){
                organSign = model.getOrganSign();
            }else {
                organSign = model.getHeadquartersOrganSign();
            }
        }
        MemberLevelDto memberLevelDto = new MemberLevelDto();
        BeanUtils.copyProperties(memberLevelVo, memberLevelDto);
        memberLevelDto.setOrgansign(organSign);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(memberLevelDto.getPage() == null ? 1 : memberLevelDto.getPage());
        pageInfo.setPageSize(memberLevelDto.getRows() == null ? 100 : memberLevelDto.getRows());
        logger.info("门店端:查询等级列表,入参：{}",JSON.toJSONString(memberLevelDto));
        PageInfo<MemberLevelDto> resultList = memberLevelApi.getMemberLevelListPager(pageInfo, memberLevelDto);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(resultList),HttpStatus.OK);
    }


    @ApiOperation(value = "门店端:获取各个等级的人数", notes = "获取各个等级的人数", response = MemberLevel.class, tags={ "memberLevel", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberLevel.class) })
    @RequestMapping(value = "/memberLevel/num", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> queryMemberLevelNum(@RequestHeader("commonRequestModel") String commonRequestModel) {
        CommonRequestModel model = JSONObject.parseObject(commonRequestModel, CommonRequestModel.class);
//        String organSign = model.getHeadquartersOrganSign();
//        if (organSign == null || StringUtils.isEmpty(organSign)) {
//            organSign = model.getOrganSign();
//        }
        MemberLevelDto memberLevelDto = new MemberLevelDto();
        memberLevelDto.setOrgansign(model.getOrganSign());
        List<MemberLevelDto> list = memberLevelApi.queryMemberLevelNum(memberLevelDto);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(list), HttpStatus.OK);
    }



}
