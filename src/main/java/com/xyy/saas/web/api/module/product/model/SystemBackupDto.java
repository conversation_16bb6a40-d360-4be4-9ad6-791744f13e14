package com.xyy.saas.web.api.module.product.model;

import com.xyy.saas.inventory.core.dto.ProductExtendInventoryVo;
import com.xyy.saas.product.core.dto.ProductExtMessDto;
import com.xyy.saas.upload.core.dto.ImgUrlVo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class SystemBackupDto implements Serializable {

    private String pref;
    private String commonName;
    private String productName;
    private String attributeSpecification;
    private String barCode;
    private String approvalNumber;
    private String manufacturer;
    private String producingArea;

    private String prescriptionClassificationName;
    private Integer businessScope;
    private Integer maintenanceType;
    private Integer productFunctionCatagory;
    private BigDecimal incomeTaxRate;
    private BigDecimal ouputTaxRate;

    private BigDecimal costPrice;
    private Byte scoreProductYn;
    private BigDecimal scoreRate;
    private BigDecimal storeMaxLimit;
    private BigDecimal storeMinLimit;
    private Long parentProductId;
    private Byte status;
    private Byte scatteredYn;
    private Byte nodeType;
    private Byte collectType;
    private Byte freezeType;
    private Byte createType;
    private String createUser;
    private Date createTime;
    private String updateUser;
    private Date updateTime;
    private String remark;
    private Byte used;
    private Byte special;
    private String imgUrl;
    private List<ImgUrlVo> productImgVolist;
    private String lastPurchase;
    private String baseVersion;
    private String organSign;
    private String mixedQuery;
    private Byte yn;
    private Byte isHidden;
    private String pharmacyPref;
    private Byte printStatus;
    private Byte scatteredUseYn;
    private String unmarketableDays;
    private Byte expireYn;
    private ProductExtMessDto productExtMess;
    private String parentPref;
    private String extMess;
    private Byte quickYn;
    private ProductExtendInventoryVo extProperty;
    private Integer systemType;
    private Byte madeType;
    private Byte medicalInsurance;
    private BigDecimal stockNumber;
    private Date expirationDate;
    private String productValidity;
    private String dosageFormName;
    private String lotGuid;
    private String lotNumber;
    private Integer positionId;
    private String positionName;
    private String subPref;
    private Integer lotStatus;
    private Integer positionType;
    private Integer drugRemindId;
    private String reminder;
    private String remindContent;
    private String usageAndDosage;
    private String unitName;
    private String sidx;
    private String sord;
    private Integer productNumber;
    private Integer ykqPrice;
    private List<String> productPharmacyPrefs;
    private String medicalUnique;
    private Integer medicalType;
    private Integer medicalStatus;
    private Byte fromControllerOrOther;


    public String getPref() {
        return pref == null ? "" : pref.getBytes().toString();
    }

    public void setPref(String pref) {
        this.pref = pref;
    }


    public String getCommonName() {
        return commonName == null ? "" :commonName.getBytes().toString();
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    public String getProductName() {
        return productName == null ? "":productName.getBytes().toString();
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getAttributeSpecification() {
        return attributeSpecification == null ? "":attributeSpecification.getBytes().toString();
    }

    public void setAttributeSpecification(String attributeSpecification) {
        this.attributeSpecification = attributeSpecification;
    }

    public String getBarCode() {
        return barCode == null ? "":barCode.getBytes().toString();
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getApprovalNumber() {
        return approvalNumber == null ? "":approvalNumber.getBytes().toString();
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    public String getManufacturer() {
        return manufacturer == null ? "":manufacturer.getBytes().toString();
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getProducingArea() {
        return producingArea == null ? "":producingArea.getBytes().toString();
    }

    public void setProducingArea(String producingArea) {
        this.producingArea = producingArea;
    }


    public String getPrescriptionClassificationName() {
        return prescriptionClassificationName == null ? "":prescriptionClassificationName.getBytes().toString();
    }

    public void setPrescriptionClassificationName(String prescriptionClassificationName) {
        this.prescriptionClassificationName = prescriptionClassificationName;
    }

    public Integer getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(Integer businessScope) {
        this.businessScope = businessScope;
    }

    public Integer getMaintenanceType() {
        return maintenanceType;
    }

    public void setMaintenanceType(Integer maintenanceType) {
        this.maintenanceType = maintenanceType;
    }

    public Integer getProductFunctionCatagory() {
        return productFunctionCatagory;
    }

    public void setProductFunctionCatagory(Integer productFunctionCatagory) {
        this.productFunctionCatagory = productFunctionCatagory;
    }

    public BigDecimal getIncomeTaxRate() {
        return incomeTaxRate;
    }

    public void setIncomeTaxRate(BigDecimal incomeTaxRate) {
        this.incomeTaxRate = incomeTaxRate;
    }

    public BigDecimal getOuputTaxRate() {
        return ouputTaxRate;
    }

    public void setOuputTaxRate(BigDecimal ouputTaxRate) {
        this.ouputTaxRate = ouputTaxRate;
    }

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public Byte getScoreProductYn() {
        return scoreProductYn;
    }

    public void setScoreProductYn(Byte scoreProductYn) {
        this.scoreProductYn = scoreProductYn;
    }

    public BigDecimal getScoreRate() {
        return scoreRate;
    }

    public void setScoreRate(BigDecimal scoreRate) {
        this.scoreRate = scoreRate;
    }

    public BigDecimal getStoreMaxLimit() {
        return storeMaxLimit;
    }

    public void setStoreMaxLimit(BigDecimal storeMaxLimit) {
        this.storeMaxLimit = storeMaxLimit;
    }

    public BigDecimal getStoreMinLimit() {
        return storeMinLimit;
    }

    public void setStoreMinLimit(BigDecimal storeMinLimit) {
        this.storeMinLimit = storeMinLimit;
    }

    public Long getParentProductId() {
        return parentProductId;
    }

    public void setParentProductId(Long parentProductId) {
        this.parentProductId = parentProductId;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Byte getScatteredYn() {
        return scatteredYn;
    }

    public void setScatteredYn(Byte scatteredYn) {
        this.scatteredYn = scatteredYn;
    }

    public Byte getNodeType() {
        return nodeType;
    }

    public void setNodeType(Byte nodeType) {
        this.nodeType = nodeType;
    }

    public Byte getCollectType() {
        return collectType;
    }

    public void setCollectType(Byte collectType) {
        this.collectType = collectType;
    }

    public Byte getFreezeType() {
        return freezeType;
    }

    public void setFreezeType(Byte freezeType) {
        this.freezeType = freezeType;
    }

    public Byte getCreateType() {
        return createType;
    }

    public void setCreateType(Byte createType) {
        this.createType = createType;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Byte getUsed() {
        return used;
    }

    public void setUsed(Byte used) {
        this.used = used;
    }

    public Byte getSpecial() {
        return special;
    }

    public void setSpecial(Byte special) {
        this.special = special;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public List<ImgUrlVo> getProductImgVolist() {
        return productImgVolist;
    }

    public void setProductImgVolist(List<ImgUrlVo> productImgVolist) {
        this.productImgVolist = productImgVolist;
    }

    public String getLastPurchase() {
        return lastPurchase;
    }

    public void setLastPurchase(String lastPurchase) {
        this.lastPurchase = lastPurchase;
    }

    public String getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(String baseVersion) {
        this.baseVersion = baseVersion;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public String getMixedQuery() {
        return mixedQuery == null ? "":mixedQuery.getBytes().toString();
    }

    public void setMixedQuery(String mixedQuery) {
        this.mixedQuery = mixedQuery;
    }

    public Byte getYn() {
        return yn;
    }

    public void setYn(Byte yn) {
        this.yn = yn;
    }

    public Byte getIsHidden() {
        return isHidden;
    }

    public void setIsHidden(Byte isHidden) {
        this.isHidden = isHidden;
    }

    public String getPharmacyPref() {
        return pharmacyPref == null ? "":pharmacyPref.getBytes().toString();
    }

    public void setPharmacyPref(String pharmacyPref) {
        this.pharmacyPref = pharmacyPref;
    }

    public Byte getPrintStatus() {
        return printStatus;
    }

    public void setPrintStatus(Byte printStatus) {
        this.printStatus = printStatus;
    }

    public Byte getScatteredUseYn() {
        return scatteredUseYn;
    }

    public void setScatteredUseYn(Byte scatteredUseYn) {
        this.scatteredUseYn = scatteredUseYn;
    }

    public String getUnmarketableDays() {
        return unmarketableDays;
    }

    public void setUnmarketableDays(String unmarketableDays) {
        this.unmarketableDays = unmarketableDays;
    }

    public Byte getExpireYn() {
        return expireYn;
    }

    public void setExpireYn(Byte expireYn) {
        this.expireYn = expireYn;
    }

    public ProductExtMessDto getProductExtMess() {
        return productExtMess;
    }

    public void setProductExtMess(ProductExtMessDto productExtMess) {
        this.productExtMess = productExtMess;
    }

    public String getParentPref() {
        return parentPref;
    }

    public void setParentPref(String parentPref) {
        this.parentPref = parentPref;
    }

    public String getExtMess() {
        return extMess;
    }

    public void setExtMess(String extMess) {
        this.extMess = extMess;
    }

    public Byte getQuickYn() {
        return quickYn;
    }

    public void setQuickYn(Byte quickYn) {
        this.quickYn = quickYn;
    }

    public ProductExtendInventoryVo getExtProperty() {
        return extProperty;
    }

    public void setExtProperty(ProductExtendInventoryVo extProperty) {
        this.extProperty = extProperty;
    }

    public Integer getSystemType() {
        return systemType;
    }

    public void setSystemType(Integer systemType) {
        this.systemType = systemType;
    }

    public Byte getMadeType() {
        return madeType;
    }

    public void setMadeType(Byte madeType) {
        this.madeType = madeType;
    }

    public Byte getMedicalInsurance() {
        return medicalInsurance;
    }

    public void setMedicalInsurance(Byte medicalInsurance) {
        this.medicalInsurance = medicalInsurance;
    }

    public BigDecimal getStockNumber() {
        return stockNumber;
    }

    public void setStockNumber(BigDecimal stockNumber) {
        this.stockNumber = stockNumber;
    }

    public Date getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }

    public String getProductValidity() {
        return productValidity;
    }

    public void setProductValidity(String productValidity) {
        this.productValidity = productValidity;
    }

    public String getDosageFormName() {
        return dosageFormName;
    }

    public void setDosageFormName(String dosageFormName) {
        this.dosageFormName = dosageFormName;
    }

    public String getLotGuid() {
        return lotGuid;
    }

    public void setLotGuid(String lotGuid) {
        this.lotGuid = lotGuid;
    }

    public String getLotNumber() {
        return lotNumber;
    }

    public void setLotNumber(String lotNumber) {
        this.lotNumber = lotNumber;
    }

    public Integer getPositionId() {
        return positionId;
    }

    public void setPositionId(Integer positionId) {
        this.positionId = positionId;
    }

    public String getPositionName() {
        return positionName;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public String getSubPref() {
        return subPref;
    }

    public void setSubPref(String subPref) {
        this.subPref = subPref;
    }

    public Integer getLotStatus() {
        return lotStatus;
    }

    public void setLotStatus(Integer lotStatus) {
        this.lotStatus = lotStatus;
    }

    public Integer getPositionType() {
        return positionType;
    }

    public void setPositionType(Integer positionType) {
        this.positionType = positionType;
    }

    public Integer getDrugRemindId() {
        return drugRemindId;
    }

    public void setDrugRemindId(Integer drugRemindId) {
        this.drugRemindId = drugRemindId;
    }

    public String getReminder() {
        return reminder;
    }

    public void setReminder(String reminder) {
        this.reminder = reminder;
    }

    public String getRemindContent() {
        return remindContent;
    }

    public void setRemindContent(String remindContent) {
        this.remindContent = remindContent;
    }

    public String getUsageAndDosage() {
        return usageAndDosage;
    }

    public void setUsageAndDosage(String usageAndDosage) {
        this.usageAndDosage = usageAndDosage;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getSidx() {
        return sidx;
    }

    public void setSidx(String sidx) {
        this.sidx = sidx;
    }

    public String getSord() {
        return sord;
    }

    public void setSord(String sord) {
        this.sord = sord;
    }

    public Integer getProductNumber() {
        return productNumber;
    }

    public void setProductNumber(Integer productNumber) {
        this.productNumber = productNumber;
    }

    public Integer getYkqPrice() {
        return ykqPrice;
    }

    public void setYkqPrice(Integer ykqPrice) {
        this.ykqPrice = ykqPrice;
    }

    public List<String> getProductPharmacyPrefs() {
        return productPharmacyPrefs;
    }

    public void setProductPharmacyPrefs(List<String> productPharmacyPrefs) {
        this.productPharmacyPrefs = productPharmacyPrefs;
    }

    public String getMedicalUnique() {
        return medicalUnique;
    }

    public void setMedicalUnique(String medicalUnique) {
        this.medicalUnique = medicalUnique;
    }

    public Integer getMedicalType() {
        return medicalType;
    }

    public void setMedicalType(Integer medicalType) {
        this.medicalType = medicalType;
    }

    public Integer getMedicalStatus() {
        return medicalStatus;
    }

    public void setMedicalStatus(Integer medicalStatus) {
        this.medicalStatus = medicalStatus;
    }

    public Byte getFromControllerOrOther() {
        return fromControllerOrOther;
    }

    public void setFromControllerOrOther(Byte fromControllerOrOther) {
        this.fromControllerOrOther = fromControllerOrOther;
    }
}
