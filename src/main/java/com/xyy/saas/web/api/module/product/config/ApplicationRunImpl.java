//package com.xyy.saas.web.api.module.product.config;
//
//import com.xyy.saas.sentinel.base.application.ApplicationRunnerUtil;
//import com.xyy.saas.sentinel.base.core.util.ClassUtil;
//import com.xyy.saas.web.api.module.product.util.CommonConsumerFallback;
//import org.springframework.boot.ApplicationArguments;
//import org.springframework.boot.ApplicationRunner;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
//@Component
//public class ApplicationRunImpl extends ApplicationRunnerUtil implements ApplicationRunner {
//    @Override
//    public void run(ApplicationArguments args) throws Exception {
//        List<Class<?>> classList = ClassUtil.getAllClassByPackageName("com.xyy.saas.web.api");
//        super.onCommonOper(classList,new Class[]{CommonConsumerFallback.class});
//    }
//}