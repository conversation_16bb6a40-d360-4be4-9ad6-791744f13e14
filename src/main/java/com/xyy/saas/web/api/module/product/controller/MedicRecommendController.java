package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.api.MedicRecommendApi;
import com.xyy.saas.product.core.dto.MedicalRecommendDto;
import com.xyy.saas.product.core.dto.QueryRecommendDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date 2020/11/9 19:01
 * @Version 1.0
 */
@Controller
@RequestMapping("/product/medicRecommend")
@Slf4j
public class MedicRecommendController {

    @Reference(version = "0.0.1")
    private MedicRecommendApi medicRecommendApi;


    @ResponseBody
    @PostMapping("/queryByPage")
    public ResultVO query(@RequestBody String json,@RequestHeader(name = "organSign", required = true) String organSign){
        log.info("查询用药服务配置界面参数:{}",json);
        ResultVO result = null;
        try {
            if (StringUtils.isNotEmpty(json)) {
                MedicalRecommendDto recommendDto = JSONObject.parseObject(json,MedicalRecommendDto.class);
                if (null == recommendDto.getPageNum() || null == recommendDto.getPageSize()){
                    result = ResultVO.createError("分页参数必填");
                }else {
                    result = medicRecommendApi.queryInfo(recommendDto,organSign);
                }
            }else {
                result = ResultVO.createError("参数为空");
            }
        }catch (Exception e){
            log.error("查询用药服务配置界面异常:{}",e);
            result = ResultVO.createError("查询异常,请联系管理员");
        }
        return result;
    }

    @ResponseBody
    @PostMapping("/used")
    public ResultVO update(@RequestBody String json){
        log.info("更新用药服务启用配置参数:{}",json);
        ResultVO result = null;
        try {
            if (StringUtils.isNotEmpty(json)) {
                QueryRecommendDto query = JSONObject.parseObject(json,QueryRecommendDto.class);
                result = medicRecommendApi.updateUsed(query);
            }else {
                result = ResultVO.createError("参数为空");
            }
        }catch (Exception e){
            log.error("更新用药服务启用配置异常:{}",e);
            result = ResultVO.createError("更新异常,请联系管理员");
        }
        return result;
    }
}
