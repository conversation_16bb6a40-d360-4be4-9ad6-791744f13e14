package com.xyy.saas.web.api.module.consult.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 远程问诊记录Vo，关联患者信息
 *
 * <AUTHOR>
 */
@ApiModel(description = "远程问诊记录，关联患者信息")
public class ConsultRecordDetailVo implements Serializable {

    private static final long serialVersionUID = -728049501947331477L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 患者id
     */
    @ApiModelProperty(value = "患者id", required = true)
    private Long patientId;

    /**
     * 既往病史
     */
    @ApiModelProperty(value = "既往病史")
    private String history;

    /**
     * 病情描述
     */
    @ApiModelProperty(value = "病情描述", required = true)
    private String patientCondition;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer baseVersion;

    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识", required = true)
    private String organSign;

    /**
     * 问诊状态 0-待问诊 1-问诊完成
     */
    @ApiModelProperty(value = "问诊状态 0-待问诊 1-问诊完成", example = "0")
    private Byte isEnd;

    /**
     * 逻辑删除 1-有效 0-删除
     */
    @ApiModelProperty(value = "逻辑删除 1-有效 0-删除", hidden = true)
    private Byte yn;

    /**
     * 远程问诊记录guid
     */
    @ApiModelProperty(value = "远程问诊记录guid")
    private String guid;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 性别 1-男 2-女
     */
    @ApiModelProperty(value = "性别 1-男 2-女", required = true, example = "1")
    private Byte sex;

    /**
     * 生日
     */
    @ApiModelProperty(value = "生日", required = true)
    private Date birthday;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码", required = true)
    private String telephone;

    /**
     * 婚否 0-未婚 1-已婚
     */
    @ApiModelProperty(value = "婚否 0-未婚 1-已婚", required = true, example = "0")
    private Byte married;

    /**
     * 过敏史 0-无 1-有
     */
    @ApiModelProperty(value = "过敏史 0-无 1-有", required = true, example = "0")
    private Byte allergy;

    /**
     * 患者guid
     */
    @ApiModelProperty(value = "患者guid")
    private String patientGuid;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPatientId() {
        return patientId;
    }

    public void setPatientId(Long patientId) {
        this.patientId = patientId;
    }

    public String getHistory() {
        return history;
    }

    public void setHistory(String history) {
        this.history = history;
    }

    public String getPatientCondition() {
        return patientCondition;
    }

    public void setPatientCondition(String patientCondition) {
        this.patientCondition = patientCondition;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(Integer baseVersion) {
        this.baseVersion = baseVersion;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public Byte getIsEnd() {
        return isEnd;
    }

    public void setIsEnd(Byte isEnd) {
        this.isEnd = isEnd;
    }

    public Byte getYn() {
        return yn;
    }

    public void setYn(Byte yn) {
        this.yn = yn;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Byte getSex() {
        return sex;
    }

    public void setSex(Byte sex) {
        this.sex = sex;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public Byte getMarried() {
        return married;
    }

    public void setMarried(Byte married) {
        this.married = married;
    }

    public Byte getAllergy() {
        return allergy;
    }

    public void setAllergy(Byte allergy) {
        this.allergy = allergy;
    }

    public String getPatientGuid() {
        return patientGuid;
    }

    public void setPatientGuid(String patientGuid) {
        this.patientGuid = patientGuid;
    }

    @Override
    public String toString() {
        return "ConsultRecordDetailVo{" +
                "id=" + id +
                ", patientId=" + patientId +
                ", history='" + history + '\'' +
                ", patientCondition='" + patientCondition + '\'' +
                ", createUser='" + createUser + '\'' +
                ", createTime=" + createTime +
                ", updateUser='" + updateUser + '\'' +
                ", updateTime=" + updateTime +
                ", baseVersion=" + baseVersion +
                ", organSign='" + organSign + '\'' +
                ", isEnd=" + isEnd +
                ", yn=" + yn +
                ", guid='" + guid + '\'' +
                ", name='" + name + '\'' +
                ", sex=" + sex +
                ", birthday=" + birthday +
                ", idCard='" + idCard + '\'' +
                ", telephone='" + telephone + '\'' +
                ", married=" + married +
                ", allergy=" + allergy +
                ", patientGuid='" + patientGuid + '\'' +
                '}';
    }
}