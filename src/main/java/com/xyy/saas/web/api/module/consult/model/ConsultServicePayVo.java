package com.xyy.saas.web.api.module.consult.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 远程问诊服务支付Vo
 * <AUTHOR>
 */
@ApiModel(value = "远程问诊服务支付")
public class ConsultServicePayVo implements Serializable {

    private static final long serialVersionUID = -542654210285693893L;

    /**
     * 购买服务id
     */
    @ApiModelProperty(value = "购买服务id", required = true, example = "1")
    private Long serviceItemId;

    /**
     * 购买数量
     */
    @ApiModelProperty(value = "购买数量", required = true, example = "1")
    private Integer num;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", required = true)
    private String createUser;

    public Long getServiceItemId() {
        return serviceItemId;
    }

    public void setServiceItemId(Long serviceItemId) {
        this.serviceItemId = serviceItemId;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    @Override
    public String toString() {
        return "ConsultServicePayVo{" +
                "serviceItemId=" + serviceItemId +
                ", num=" + num +
                ", createUser='" + createUser + '\'' +
                '}';
    }
}
