package com.xyy.saas.web.api.module.product.controller;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.dto.ProductBatchUpdatemsgRecordDto;
import com.xyy.saas.product.core.dto.ProductUpdatemsgRecordCondition;
import com.xyy.saas.product.core.dto.SaasProductUpdatemsgRecordDto;
import com.xyy.saas.web.api.module.product.model.*;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-07T16:41:52.348+08:00")

@Api(value = "qualityModify", description = "the putfights API")
@RequestMapping(value = "/product/qualityModify")
public interface QualityModifyApi {

    @ApiOperation(value = "记录列表", notes = "记录列表", response = SaasProductUpdatemsgListVo.class, tags = {"质量信息变更",})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = SaasProductUpdatemsgListVo.class)})

    @RequestMapping(value = "/list",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> list(@RequestHeader(value = "organSign", required = true) String organSign, @RequestHeader(name = "employeeId", required = true) Integer employeeId, @ApiParam(value = "记录列表", required = true) @RequestBody SaasProductUpdatemsgListDto listDto);

    @ApiOperation(value = "修改记录回显", notes = "修改记录回显", tags = {"质量信息变更",})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功")})

    @RequestMapping(value = "/echo",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> echo(HttpServletRequest request, @RequestHeader(value = "organSign", required = true) String organSign, @RequestBody SaasProductUpdatemsgEchoDto echoDto);


    @ApiOperation(value = "记录新增", notes = "记录新增", tags = {"质量信息变更",})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功")})

    @RequestMapping(value = "/save",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> save(@RequestHeader(value = "organSign", required = true) String organSign, @RequestHeader(name = "employeeId", required = true) Integer employeeId, @ApiParam(value = "记录新增", required = true) @RequestBody SaasProductUpdatemsgSaveDto saveDto);

    @ApiOperation(value = "记录审核", notes = "记录审核", tags = {"质量信息变更",})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功")})

    @RequestMapping(value = "/audit",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> audit(HttpServletRequest request, @RequestHeader(value = "organSign", required = true) String organSign, @RequestHeader(name = "employeeId", required = true) Integer employeeId, @ApiParam(value = "记录审核", required = true) @RequestBody SaasProductUpdatemsgAuditDto auditDto);


    @ApiOperation(value = "验证密码", notes = "验证密码", tags = {"质量信息变更",})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功")})

    @RequestMapping(value = "/verifyPassword",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> verifyPassword(@RequestHeader(value = "organSign", required = true) String organSign, @ApiParam(value = "验证密码", required = true) @RequestBody SaasProductUpdatemsgVerifyPasswordDto providerListDto);


    @ApiOperation(value = "审核人列表", notes = "审核人列表", tags = {"质量信息变更",})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功")})

    @RequestMapping(value = "/qualityUser",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> qualityUser(@RequestHeader(value = "organSign", required = true) String organSign);

    @ApiOperation(value = "申请人列表", notes = "申请人列表", tags = {"质量信息变更",})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功")})

    @RequestMapping(value = "/applyUser",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> applyUser(@RequestHeader(value = "organSign", required = true) String organSign);

    @ApiOperation(value = "导出", notes = "导出", tags = {"质量信息变更",})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功")})

    @RequestMapping(value = "/exportExcel",
            method = RequestMethod.POST)
    void export(HttpServletRequest request, HttpServletResponse response, @RequestHeader(value = "organSign", required = true) String organSign, @RequestHeader(name = "employeeId", required = true) Integer employeeId, @RequestBody SaasProductUpdatemsgExportDto listDto);

    @RequestMapping(value = "/batchModifyRecordlist",
            method = RequestMethod.POST)
    ResponseEntity<ResultVO> batchModifyRecordlist(@RequestHeader(value = "organSign", required = true) String organSign, @ApiParam(value = "商品批量编辑记录", required = true) @RequestBody ProductBatchUpdatemsgRecordDto recordDto);

    /**
     * 分页查询质量信息变更记录
     * @param organSign
     * @param condition
     * @return
     */
    @RequestMapping(value = "/recordList", method = RequestMethod.POST)
    ResponseEntity<ResultVO<PageInfo<SaasProductUpdatemsgRecordDto>>> recordList(@RequestHeader(value = "organSign", required = true) String organSign, @RequestBody ProductUpdatemsgRecordCondition condition);


}
