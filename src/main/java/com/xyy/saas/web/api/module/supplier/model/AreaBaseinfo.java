package com.xyy.saas.web.api.module.supplier.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * saas_area_baseinfo
 * <AUTHOR>
@ApiModel(description = "区域信息对象")
public class AreaBaseinfo implements Serializable {

    private static final long serialVersionUID = -1645428664171310906L;
    @ApiModelProperty(value = "id")
    @JsonProperty("id")
    private Integer id;

    /**
     * 区域编码
     */
    @ApiModelProperty(value = "区域编码")
    @JsonProperty("areaCode")
    private String areaCode;

    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称")
    @JsonProperty("areaName")
    private String areaName;

    /**
     * 父级编码
     */
    @ApiModelProperty(value = "父级编码")
    @JsonProperty("parentAreaCode")
    private String parentAreaCode;

    /**
     * 级别
     */
    @ApiModelProperty(value = "级别")
    @JsonProperty("level")
    private String level;

    /**
     * 区域全称
     */
    @ApiModelProperty(value = "区域全称")
    @JsonProperty("areaAllName")
    private String areaAllName;

    /**
     * 1  有效  0 无效
     */
    @ApiModelProperty(value = "有效")
    @JsonProperty("yn")
    private Byte yn;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonProperty("createTime")
    private String createTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @JsonProperty("createUser")
    private String createUser;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonProperty("updateTime")
    private String updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @JsonProperty("updateUser")
    private String updateUser;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @JsonProperty("remark")
    private String remark;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    @JsonProperty("page")
    private Integer page;

    /**
     * 每页行数
     */
    @ApiModelProperty(value = "每页行数")
    @JsonProperty("rows")
    private Integer rows;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getParentAreaCode() {
        return parentAreaCode;
    }

    public void setParentAreaCode(String parentAreaCode) {
        this.parentAreaCode = parentAreaCode;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getAreaAllName() {
        return areaAllName;
    }

    public void setAreaAllName(String areaAllName) {
        this.areaAllName = areaAllName;
    }

    public Byte getYn() {
        return yn;
    }

    public void setYn(Byte yn) {
        this.yn = yn;
    }



    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }


    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "AreaBaseinfoDto{" +
                "id=" + id +
                ", areaCode='" + areaCode + '\'' +
                ", areaName='" + areaName + '\'' +
                ", parentAreaCode='" + parentAreaCode + '\'' +
                ", level='" + level + '\'' +
                ", areaAllName='" + areaAllName + '\'' +
                ", yn=" + yn +
                ", createTime=" + createTime +
                ", createUser='" + createUser + '\'' +
                ", updateTime=" + updateTime +
                ", updateUser='" + updateUser + '\'' +
                ", remark='" + remark + '\'' +
                ", page=" + page +
                ", rows=" + rows +
                '}';
    }
}