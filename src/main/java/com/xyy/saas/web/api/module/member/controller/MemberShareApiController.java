package com.xyy.saas.web.api.module.member.controller;


import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.member.core.api.MemberShareApi;
import com.xyy.saas.member.core.dto.*;
import com.xyy.saas.member.core.response.ResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;


/**
 * 会员共享 门店端API
 * <AUTHOR>
@Controller
@RequestMapping(value = "/member/memberShare")
@Api(value = "memberPrepayCardToken", description = "会员共享门店端API")
public class MemberShareApiController {

    private static final Logger logger = Logger.getLogger(MemberShareApiController.class);

    @Reference(version = "0.0.1")
    private MemberShareApi memberShareApi;

    @ApiOperation(value = "查询门店共享状态", notes = "查询门店共享状态", response = MemberShareDto.class, tags = {"会员共享门店端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberShareDto.class)})
    @RequestMapping(value = "/getMemberShareStatus", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> getAll(@RequestHeader("commonRequestModel") String commonRequestModelStr) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String organSign = commonRequestModel.getOrganSign();
        return new ResponseEntity(memberShareApi.getMemberShareStatus(organSign), HttpStatus.OK);
    }
}
