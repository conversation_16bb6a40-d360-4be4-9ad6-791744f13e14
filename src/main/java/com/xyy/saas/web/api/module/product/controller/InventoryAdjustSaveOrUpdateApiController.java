package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.inventory.core.api.InventoryAdjustApi;
import com.xyy.saas.web.api.module.product.model.InventoryAdjustVo;
import com.xyy.saas.web.api.module.product.model.ResultCodeEnum;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-30T12:28:35.123+08:00")

@Controller
public class InventoryAdjustSaveOrUpdateApiController implements InventoryAdjustSaveOrUpdateApi {

    @Reference(version = "1.0.0")
    private InventoryAdjustApi inventoryAdjustApi;
    @ResponseBody
    public ResponseEntity<ResultVO> inventoryAdjustSaveOrUpdate(@ApiParam(value = "批号效期调整Vo" ,required=true ) @RequestHeader("userId") Integer userId, @RequestHeader("organSign") String organSign, @Valid @RequestBody InventoryAdjustVo inventoryAdjustVo) {


            if(inventoryAdjustVo==null){
               return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.INVENTORY_PARAM_NULL), HttpStatus.OK);
            }
            com.xyy.saas.inventory.core.dto.InventoryAdjustVo vo = new com.xyy.saas.inventory.core.dto.InventoryAdjustVo();
        BeanUtils.copyProperties(inventoryAdjustVo,vo);
        vo.setOrgansign(organSign);
        vo.setUpdateUserId(userId);
            Integer integer = 0;//inventoryAdjustApi.saveOrUpdate(vo);
            if(integer>=1){
                return  new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.SUCCESS), HttpStatus.OK);
            }else{
                return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.INVENTORY_RESULT_FAIL), HttpStatus.OK);
            }
    }

}
