package com.xyy.saas.web.api.module.product.controller;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.dto.SystemDictDto;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.dto.LianYingProductDto;
import com.xyy.saas.product.core.dto.LianYingProductUpdateDto;
import com.xyy.saas.product.core.dto.ProPriceAdjustDto;
import com.xyy.saas.product.core.dto.ProductDto;
import com.xyy.saas.web.api.module.product.model.*;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RequestMapping("/product")
@Api(value = "联营总部商品统一接口", description = "联营总部商品统一接口")
public interface LianYingProductInfoApi {

    /**
     * 联营商品新增接口
     * @param request
     * @param productDto
     * @return
     */
    @ApiOperation(value = "联营商品新增接口", notes = "联营商品新增接口", response = ResultVO.class, tags={ "联营总部商品统一接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ComputeCostPriceVo.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/lianying/saveProduct", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> saveProduct(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody LianYingProductDto productDto);


    @ApiOperation(value = "查询联营商品列表接口", notes = "查询联营商品列表接口", response = ResultVO.class)
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/lianying/queryProduct",method = RequestMethod.POST)
    public ResponseEntity<ResultVO<PageInfo>> proQueryList(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody ProductCommonQueryVo productCommonQueryVo);

    @ApiOperation(value = "联营商品更新数据回显接口", notes = "联营商品更新数据回显接口", response = ProductDto.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ProductDto.class) })
    @RequestMapping(value = "/baseinfo/lianying/toUpdate",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getProductById(HttpServletRequest request, @ApiParam(value = "商品详情信息对象", required = true) @Valid @RequestBody ProductDto productDto);

    /**
     * 编辑联营商品，与
     * @param saveDto
     * @return
     */
    @ApiOperation(value = "联营商品编辑数据接口", notes = "联营商品编辑数据接口", response = ProductDto.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ProductDto.class) })
    @RequestMapping(value = "/baseinfo/lianying/updateProduct",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> updateProduct(HttpServletRequest request, @RequestBody LianYingProductUpdateDto saveDto) ;

    @ApiOperation(value = "联营分享商品接口", notes = "联营分享商品接口", response = ResultVO.class)
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/lianying/shareProduct",method = RequestMethod.POST)
    public ResponseEntity<ResultVO>  shareProduct(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody LianYingProductDto product);

    @ApiOperation(value = "查询未包含分享商品门店接口", notes = "查询未包含分享商品门店接口", response = ResultVO.class)
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/lianying/selectShareOrgans",method = RequestMethod.POST)
    public ResponseEntity<ResultVO>  selectShareOrgans(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody LianYingProductDto product);

    @ApiOperation(value = "查询包含分享商品门店接口", notes = "查询包含分享商品门店接口", response = ResultVO.class)
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/lianying/selectHadShareOrgans",method = RequestMethod.POST)
    public ResponseEntity<ResultVO>  selectHadShareOrgans(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody LianYingProductDto product);


    @ApiOperation(value = "查询包含分享商品门店接口", notes = "查询包含分享商品门店接口", response = ResultVO.class)
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/lianying/checkTaskStatus",method = RequestMethod.POST)
    public ResponseEntity<ResultVO>  checkTaskStatus(HttpServletRequest request, @ApiParam(value = "查询任务执行结果", required = true) @Valid @RequestBody CheckTaskStatusQueryVo checkTaskStatusQueryVo);

    /**
     * 查询商品价格修改明细，分页展示
     * @param proPriceAdjustDto
     * @return
     */
    @ApiOperation(value = "查询商品价格修改明细接口", notes = "查询商品价格修改明细接口", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/baseinfo/lianying/getAdjustPriceListByPref", method = RequestMethod.POST)
    ResponseEntity<ResultVO> getAdjustPriceListByPref(HttpServletRequest request, @ApiParam(value = "商品售价调整信息实体", required = true) @Valid @RequestBody ProPriceAdjustDto proPriceAdjustDto);

    @ApiOperation(value = "获取所有字典信息类接口，状态是未删除并且启用", notes = "获取所有字典信息类接口，状态是未删除并且启用", response = SystemDictDto.class, tags={ "product", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = SystemDictDto.class) })
    @RequestMapping(value = "/baseinfo/lianying/typeList", method = RequestMethod.POST)
    ResponseEntity<ResultVO> getProductTypeList(HttpServletRequest request,@ApiParam(value = "当前登录机构码", required = true) @RequestHeader(value = "organSign", required = true) String organSign,
                                                @ApiParam(value = "当前登录员工的ID", required = true) @RequestHeader(value = "employeeId", required = true) String employeeId,
                                                @ApiParam(value = "请求字典类型", required = true) @Valid @RequestBody LianYingSystemDictQueryVo systemDictQueryVo);


    /**
     * 刷新数据需求脚本，用药提醒
     * @param request
     * @param systemDictQueryVo ,只使用organSign
     * @return
     */
    @ApiOperation(value = "用药提醒列表接口查询", notes = "用药提醒列表接口查询", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/lianying/listAllDrugRemind",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> listAllDrugRemind(HttpServletRequest request , @RequestBody LianYingSystemDictQueryVo systemDictQueryVo);








}
