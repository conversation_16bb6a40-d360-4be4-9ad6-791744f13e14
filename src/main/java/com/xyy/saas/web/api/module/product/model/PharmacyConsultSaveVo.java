package com.xyy.saas.web.api.module.product.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * InventoryVo
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-10-21T10:11:31.623+08:00")
@ApiModel(description = "新增咨询记录")
public class PharmacyConsultSaveVo {

    /**
     * 会员卡号
     */
    @ApiModelProperty(value = "会员卡号")
    private String memberCard;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String userName;
    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄")
    private String age;
    /**
     * 性别 1:男 0:女
     */
    @ApiModelProperty(value = "性别 1:男 0:女")
    private Integer sex;
    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String moblie;
    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    private String idCard;
    /**
     * 身高
     */
    @ApiModelProperty(value = "身高")
    private String userHeight;
    /**
     * 体重
     */
    @ApiModelProperty(value = "体重")
    private String userWeight;
    /**
     * 肝功能
     */
    @ApiModelProperty(value = "肝功能")
    private String liverFunction;
    /**
     * 肾功能
     */
    @ApiModelProperty(value = "肾功能")
    private String renalFunction;
    /**
     * 怀孕 1:是 0:否
     */
    @ApiModelProperty(value = "怀孕 1:是 0:否")
    private Integer isPregnancy;
    /**
     * 病史
     */
    @ApiModelProperty(value = "病史")
    private String illnessHistory;
    /**
     * 过敏史
     */
    @ApiModelProperty(value = "过敏史")
    private String allergyHistory;
    /**
     * 药师id
     */
    @ApiModelProperty(value = "药师id")
    private Integer pharmacistId;
    /**
     * 药师名称
     */
    @ApiModelProperty(value = "药师名称")
    private String pharmacistName;
    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    private String address;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String details;
    /**
     * 解答
     */
    @ApiModelProperty(value = "解答")
    private String answer;
    /**
     * 咨询时间
     */
    @ApiModelProperty(value = "咨询时间")
    private Date consultTime;

    /**
     * 咨询药品
     */
    @ApiModelProperty(value = "咨询药品")
    private List<Integer> ids;

    public String getAllergyHistory() {
        return allergyHistory;
    }

    public void setAllergyHistory(String allergyHistory) {
        this.allergyHistory = allergyHistory;
    }

    public String getMemberCard() {
        return memberCard;
    }

    public void setMemberCard(String memberCard) {
        this.memberCard = memberCard;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getAge() {
        return age;
    }

    public void setAge(String age) {
        this.age = age;
    }

    public Integer getSex() {
        return sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public String getMoblie() {
        return moblie;
    }

    public void setMoblie(String moblie) {
        this.moblie = moblie;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getUserHeight() {
        return userHeight;
    }

    public void setUserHeight(String userHeight) {
        this.userHeight = userHeight;
    }

    public String getUserWeight() {
        return userWeight;
    }

    public void setUserWeight(String userWeight) {
        this.userWeight = userWeight;
    }

    public String getLiverFunction() {
        return liverFunction;
    }

    public void setLiverFunction(String liverFunction) {
        this.liverFunction = liverFunction;
    }

    public String getRenalFunction() {
        return renalFunction;
    }

    public void setRenalFunction(String renalFunction) {
        this.renalFunction = renalFunction;
    }

    public Integer getIsPregnancy() {
        return isPregnancy;
    }

    public void setIsPregnancy(Integer isPregnancy) {
        this.isPregnancy = isPregnancy;
    }

    public String getIllnessHistory() {
        return illnessHistory;
    }

    public void setIllnessHistory(String illnessHistory) {
        this.illnessHistory = illnessHistory;
    }

    public Integer getPharmacistId() {
        return pharmacistId;
    }

    public void setPharmacistId(Integer pharmacistId) {
        this.pharmacistId = pharmacistId;
    }

    public String getPharmacistName() {
        return pharmacistName;
    }

    public void setPharmacistName(String pharmacistName) {
        this.pharmacistName = pharmacistName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public Date getConsultTime() {
        return consultTime;
    }

    public void setConsultTime(Date consultTime) {
        this.consultTime = consultTime;
    }

    public List<Integer> getIds() {
        return ids;
    }

    public void setIds(List<Integer> ids) {
        this.ids = ids;
    }
}

