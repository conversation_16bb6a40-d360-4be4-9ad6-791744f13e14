package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * @ClassName heartbeatApi
 * @User: ouyang
 * @Date: 2019/4/16 23:04
 * @Description: 心跳请求
 */
@RequestMapping("/product")
@Controller
public class heartbeatApi {

    /**
     * 心跳
     * @return
     */
    @RequestMapping(value = "/saasheartbeat")
    @ResponseBody
    public String heartbeat() {
        return "ok";
    }
}
