package com.xyy.saas.web.api.module.member.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.web.api.module.member.service.DrugstoreService;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 封装药店机构Api服务
 * <AUTHOR>
 */
@Service
public class DrugstoreServiceImpl implements DrugstoreService {

    private static final Logger logger = LogManager.getLogger(DrugstoreService.class);

    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;

    @Reference(version = "0.0.1")
    private EmployeeApi employeeApi;

    /**
     * 查询机构详情
     * @param organSign 机构标识
     * @return
     */
    @Override
    public SaaSDrugstoreDto getDrugstoreByOrganSign(String organSign) {
        long startTime = System.currentTimeMillis();
        logger.info("查询机构详情，organSign: {}", organSign);
        try {
            return drugstoreApi.getDrugstoreByOrganSign(organSign);
        } catch (Exception e) {
            logger.error("查询机构详情异常，organSign: {}", organSign, e);
            return null;
        } finally {
            long endTime = System.currentTimeMillis();
            logger.info("查询机构详情，organSign: {}, 耗时: {}ms", organSign, (endTime - startTime));
        }
    }

    /**
     * 根据机构标识列表查询机构列表
     * @param organSigns 机构标识列表
     * @return
     */
    @Override
    public List<SaaSDrugstoreDto> getListByOrganSignList(List<String> organSigns) {
        long startTime = System.currentTimeMillis();
        logger.info("查询机构列表，organSign: {}", JSONObject.toJSONString(organSigns));
        if (organSigns == null || organSigns.isEmpty()) {
            return Collections.EMPTY_LIST;
        }
        try {
            return drugstoreApi.getDrugstoreByMultipleOrganSign(organSigns);
        } catch (Exception e) {
            logger.error("查询机构列表异常，organSign: {}", JSONObject.toJSONString(organSigns), e);
            return Collections.EMPTY_LIST;
        } finally {
            long endTime = System.currentTimeMillis();
            logger.info("查询机构列表，organSign: {}, 耗时: {}ms", JSONObject.toJSONString(organSigns), (endTime - startTime));
        }
    }

    /**
     * 根据机构标识列表查询机构map
     * @param organSigns 机构标识列表
     * @return
     */
    @Override
    public Map<String, SaaSDrugstoreDto> getMapByOrganSignList(List<String> organSigns) {
        List<SaaSDrugstoreDto> list = getListByOrganSignList(organSigns);
        if (list == null || list.isEmpty()) {
            return Collections.EMPTY_MAP;
        }
        Map<String, SaaSDrugstoreDto> map = list.stream()
                .collect(Collectors.toMap(SaaSDrugstoreDto::getOrganSign, Function.identity(), (key1, key2) -> key2));
        return map;
    }

    /**
     * 根据总部机构标识查询所有门店
     * @param headquartersOrganSign 总部机构标识
     * @return
     */
    @Override
    public List<SaaSDrugstoreDto> getDrugstoreByHeadquartersOrganSign(String headquartersOrganSign) {
        long startTime = System.currentTimeMillis();
        logger.info("根据总部机构标识查询所有门店，headquartersOrganSign: {}", headquartersOrganSign);
        if (StringUtils.isEmpty(headquartersOrganSign)) {
            return Collections.EMPTY_LIST;
        }
        try {
            return drugstoreApi.getDrugstoreByHeadquartersOrganSign(headquartersOrganSign);
        } catch (Exception e) {
            logger.error("根据总部机构标识查询所有门店异常，headquartersOrganSign: {}", headquartersOrganSign, e);
            return Collections.EMPTY_LIST;
        } finally {
            long endTime = System.currentTimeMillis();
            logger.info("根据总部机构标识查询所有门店，headquartersOrganSign: {}, 耗时: {}ms", headquartersOrganSign, (endTime - startTime));
        }
    }

    /**
     * 根据总部机构标识查询所有门店
     * @param headquartersOrganSign 总部机构标识
     * @return
     */
    @Override
    public List<SaaSDrugstoreDto> getDrugstoreByHeadquartersOrganSign(Byte isDrugstoreHidden, String headquartersOrganSign) {
        long startTime = System.currentTimeMillis();
        logger.info("根据总部机构标识查询所有门店，headquartersOrganSign: {}", headquartersOrganSign);
        if (StringUtils.isEmpty(headquartersOrganSign)) {
            return Collections.EMPTY_LIST;
        }
        try {
            SaaSDrugstoreDto dto = new SaaSDrugstoreDto();
            dto.setIsDrugstoreHidden(isDrugstoreHidden);
            return drugstoreApi.getDrugstoreByHeadquartersOrganSign(dto, headquartersOrganSign);
        } catch (Exception e) {
            logger.error("根据总部机构标识查询所有门店异常，headquartersOrganSign: {}", headquartersOrganSign, e);
            return Collections.EMPTY_LIST;
        } finally {
            long endTime = System.currentTimeMillis();
            logger.info("根据总部机构标识查询所有门店，headquartersOrganSign: {}, 耗时: {}ms", headquartersOrganSign, (endTime - startTime));
        }
    }

    /**
     * 根据总部机构标识查询所有门店map
     * @param headquartersOrganSign 总部机构标识
     * @return
     */
    @Override
    public Map<String, SaaSDrugstoreDto> getDrugstoreMapByHeadquartersOrganSign(String headquartersOrganSign) {
        List<SaaSDrugstoreDto> list = this.getDrugstoreByHeadquartersOrganSign(headquartersOrganSign);
        if (list == null || list.isEmpty()) {
            return Collections.EMPTY_MAP;
        }
        Map<String, SaaSDrugstoreDto> map = list.stream()
                .collect(Collectors.toMap(SaaSDrugstoreDto::getOrganSign, Function.identity(), (key1, key2) -> key2));
        return map;
    }

    /**
     * 根据门店机构标识查询总部机构
     * @param organSign 门店机构标识
     * @return
     */
    @Override
    public SaaSDrugstoreDto getHeadquartersDrugstoreByOrganSign(String organSign) {
        long startTime = System.currentTimeMillis();
        logger.info("根据门店机构标识查询总部机构，organSign: {}", organSign);
        if (StringUtils.isEmpty(organSign)) {
            return null;
        }
        try {
            return drugstoreApi.getHeadquartersDrugstoreByOrganSign(organSign);
        } catch (Exception e) {
            logger.error("根据门店机构标识查询总部机构异常，organSign: {}", organSign, e);
            return null;
        } finally {
            long endTime = System.currentTimeMillis();
            logger.info("根据门店机构标识查询总部机构，organSign: {}, 耗时: {}ms", organSign, (endTime - startTime));
        }
    }

//    @Override
//    public Boolean getDataAuthByEmployeeId(Integer operateId) {
//        boolean flag = false;
//        QueryDataAuthDto queryDataAuthDto = new QueryDataAuthDto();
//        queryDataAuthDto.setEmployeeId(operateId);
//        ResultVO<Boolean> dataAuth = employeeApi.getDataAuth(queryDataAuthDto);
//        if (dataAuth != null && dataAuth.getResult() != null) {
//            flag = dataAuth.getResult();
//        }
//        return flag;
//    }

}