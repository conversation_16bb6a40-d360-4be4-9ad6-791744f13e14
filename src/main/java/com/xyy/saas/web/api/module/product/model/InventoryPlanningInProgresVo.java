package com.xyy.saas.web.api.module.product.model;

import java.io.Serializable;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-29T18:35:09.839+08:00")
public class InventoryPlanningInProgresVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String guid;

    private String organSign;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }
}
