package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.api.SystemDictApi;
import com.xyy.saas.product.core.api.ProScatteredruleApi;
import com.xyy.saas.product.core.api.ProductApi;
import com.xyy.saas.rabbitmq.core.api.MessagePushApi;
import com.xyy.user.module.api.EmployeeApi;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;

@Controller
public class ProductScatteredController implements ProductScatteredApi {

    private static final Logger logger = LoggerFactory.getLogger(ProductScatteredController.class);

    @Reference(version = "0.0.1")
    private SystemDictApi systemDictApi;

    @Reference(version = "0.0.1")
    private ProScatteredruleApi ruleApi;

    @Reference(version = "0.0.1")
    private MessagePushApi messagePushApi;

    @Reference(version = "0.0.1")
    private ProductApi productApi;

    @Reference(version = "0.0.1")
    private EmployeeApi employeeApi;



//    @Override
//    public ResponseEntity<ResultVO> toAdd(HttpServletRequest request, Model model) {
//        String organSign = request.getHeader("organSign");
//        getProductUnitDict(model,organSign);
//        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(model), HttpStatus.OK);
//    }
//
//    @Override
//    public ResponseEntity<ResultVO> getProScatteredById(HttpServletRequest request,@ApiParam(value = "商品拆零维护查询信息对象" ,required=true )  @Valid @RequestBody ProviderQueryVo providerQueryVo) {
//        String organSign = request.getHeader("organSign");
//        logger.info("getProScatteredById organSign:"+organSign);
//        if (null == providerQueryVo.getId())
//            return new ResponseEntity<ResultVO>(new ResultVO(com.xyy.saas.common.util.ResultCodeEnum.ERROR, "noParam"),HttpStatus.OK);
//
//        ProductScatteredRuleVoDto result = this.ruleApi.getProScatteredById(providerQueryVo.getId(), organSign);
////        model.addAttribute("result", result);
////        getProductUnitDict(model,organSign);
//        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(result),HttpStatus.OK);
//    }
//
//    @Override
//    public ResponseEntity<ResultVO> addOrUpdate(HttpServletRequest request,@ApiParam(value = "拆零商品信息对象" ,required=true )  @Valid @RequestBody ProductScatteredRuleDto scatteredRulePo) throws InterruptedException {
//        String organSign = request.getHeader("organSign");
//        String employee = request.getHeader("employeeId");
//        logger.info("addOrUpdate productScatteredRule organSign:" + organSign+",employeeid:" + employee);
//        if(StringUtils.isEmpty(scatteredRulePo) ){
//            logger.error("==============参数不合法");
//            return new ResponseEntity<ResultVO>(new ResultVO<>(com.xyy.saas.common.util.ResultCodeEnum.ERROR, null),HttpStatus.OK);
//        }
//
//        int status = 0;
//        scatteredRulePo.setYn((byte) 1);
//        // 新增一条商品规则的时候需要将原商品信息的状态设置为拆零状态、在Service中已做处理
//        if ( StringUtils.isEmpty(scatteredRulePo.getId()) ){
//            scatteredRulePo.setOrganSign(organSign);
//            scatteredRulePo.setCreateUser(employee);
//            scatteredRulePo.setCreateTime(new Date());
//        } else{
//            scatteredRulePo.setOrganSign(organSign);
//            scatteredRulePo.setUpdateUser(employee);
//            scatteredRulePo.setUpdateTime(new Date());
//        }
//
//        ResultVO result = ruleApi.save(scatteredRulePo);
//        if (result.getCode() == ResultCodeEnum.SUCCESS.getCode()){
//            Thread.sleep(1000);
//            pushProScatteredRuleMessToMQ(scatteredRulePo.getOrganSign());
//        }
//
//        return new ResponseEntity<ResultVO>(result,HttpStatus.OK);
//    }
//
//    @Override
//    public ResponseEntity<ResultVO> deleteScatteredById(HttpServletRequest request, @ApiParam(value = "商品拆零查询对象" ,required=true )  @Valid @RequestBody ProviderQueryVo providerQueryVo) {
//        String organSign = request.getHeader("organSign");
//        logger.info("deleteScatteredById organSign:"+organSign+",id:"+providerQueryVo.getId());
//        this.ruleApi.deleteScatteredById(providerQueryVo.getId(), organSign);
//        return new ResponseEntity<ResultVO>(ResultVO.createSuccess("redirect:toList"),HttpStatus.OK);
//    }
//
//    @Override
//    public ResponseEntity<ResultVO> toList(HttpServletRequest request, Model model) {
//        String organSign = request.getHeader("organSign");
//        List<SystemDictDto> unitDict = this.systemDictApi.findSystemDictDto(DictConstant.unitBussinessId,organSign);
//        model.addAttribute("unitDict", JSONObject.toJSON(unitDict).toString());
//        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(model),HttpStatus.OK);
//    }
//
//    @Override
//    public ResponseEntity<ResultVO<PageInfo>> query(HttpServletRequest request, @ApiParam(value = "商品拆零查询对象" ,required=true )  @Valid @RequestBody ProviderQueryVo providerQueryVo) {
//        String organSign = request.getHeader("organSign");
//        String employeeId = request.getHeader("employeeId");
//        String identity = employeeApi.queryEmployeeById(Integer.valueOf(employeeId)).getResult().getIdentity()+"";
//        logger.info("proScattered query ,organSign:"+organSign+",employeeId:"+employeeId+",identity:"+identity);
//        Byte isProductHidden = Byte.valueOf(identity);
//        Integer page = providerQueryVo.getPage();
//        Integer rows = providerQueryVo.getRows();
//        if ( null == page )
//            page = 1;
//
//        if ( null == rows )
//            rows = 50;
//
//        PageInfo info = new PageInfo();
//        info.setPageNum(page);
//        info.setPageSize(rows);
//        PageInfo<ProductScatteredRuleVoDto> result = this.ruleApi.selectProScatteredListByParams(info, providerQueryVo.getName(),isProductHidden, organSign);
//        return new ResponseEntity<ResultVO<PageInfo>>(ResultVO.createSuccess(result),HttpStatus.OK);
//    }
//
//    @Override
//    public ResponseEntity<ResultVO> toNoRuleProductList(HttpServletRequest request, Model model) {
//        String organSign = request.getHeader("organSign");
//        List<SystemDictDto> unitDict = this.systemDictApi.findSystemDictDto(DictConstant.unitBussinessId,organSign);
//        model.addAttribute("unitDict", JSONObject.toJSON(unitDict).toString());
//        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(model),HttpStatus.OK);
//    }
//
//    @Override
//    public ResponseEntity<ResultVO<PageInfo>> productQuery(HttpServletRequest request, @ApiParam(value = "商品拆零查询对象" ,required=true )  @Valid @RequestBody ProviderQueryVo providerQueryVo) {
//        String organSign = request.getHeader("organSign");
//        String employeeId = request.getHeader("employeeId");
//        String identity = employeeApi.queryEmployeeById(Integer.valueOf(employeeId)).getResult().getIdentity()+"";
//        logger.info("proScattered productQuery organSign:"+organSign+",employeeId:"+employeeId+",identity:"+identity);
//        Byte isProductHidden = Byte.valueOf(identity);
//        Integer page = providerQueryVo.getPage();
//        Integer rows = providerQueryVo.getRows();
//        if(page==null){
//            page=1;
//        }
//        if(rows==null){
//            rows=50;
//        }
//
//        PageInfo info = new PageInfo();
//        info.setPageNum(page);
//        info.setPageSize(rows);
//        PageInfo<ProductDto> dto = this.productApi.ruleProductQuery(info, providerQueryVo.getName(), isProductHidden, 0, 0, organSign);
//        return new ResponseEntity<ResultVO<PageInfo>>(ResultVO.createSuccess(dto),HttpStatus.OK);
//    }
//
//    @Override
//    public ResponseEntity<ResultVO> fightproductQuery(HttpServletRequest request, @ApiParam(value = "商品拆零查询对象" ,required=true )  @Valid @RequestBody ProviderQueryVo providerQueryVo) {
//        String organSign = request.getHeader("organSign");
//        String employeeId = request.getHeader("employeeId");
//        String identity = employeeApi.queryEmployeeById(Integer.valueOf(employeeId)).getResult().getIdentity()+"";
//        logger.info("proScattered fightproductQuery organSign:"+organSign+",employeeId:"+employeeId+",identity:"+identity);
//        Byte isProductHidden = Byte.valueOf(identity);
//        Integer page = providerQueryVo.getPage();
//        Integer rows = providerQueryVo.getRows();
//        if(page==null){
//            page=1;
//        }
//        if(rows==null){
//            rows=50;
//        }
//
//        PageInfo info = new PageInfo();
//        info.setPageNum(page);
//        info.setPageSize(rows);
//        Integer productType = 0;
//        List<SystemDictDto> systemDicts = systemDictApi.findSystemDictDto(DictConstant.commodtyTypeBussinessId,organSign);
//        for(SystemDictDto systemDict:systemDicts){
//            if("中药饮片".equals(systemDict.getName())){
//                productType = systemDict.getId();
//            }
//        }
//        PageInfo<ProductDto> products = this.productApi.ruleProductQuery(info, providerQueryVo.getName(), isProductHidden,null,productType, organSign);
//        List<SystemDictDto> unitList = systemDictApi.findSystemDictDto(DictConstant.unitBussinessId,organSign);
//        Map<Integer, String> unitMap = unitList.stream().collect(Collectors.toMap(SystemDictDto::getId, (p) -> p.getName()));
//        PageInfo pros = new PageInfo<ProductExportDto>();
//        BeanUtils.copyProperties(products, pros);
//        if (null!=products.getList()&&products.getList().size()>0){
//            List<ProductExportDto> proList = new ArrayList<>();
//            products.getList().stream().forEach(s->{
//                ProductExportDto productExportDto = new ProductExportDto();
//                BeanUtils.copyProperties( s ,productExportDto);
//                if(s.getUnitId()!=null && unitMap.containsKey(s.getUnitId())){
//                    productExportDto.setUnitName(unitMap.get(s.getUnitId()));
//                }
//                proList.add(productExportDto);
//            });
//            pros.setList(proList);
//        }
//        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(pros),HttpStatus.OK);
//    }
//
//    private void getProductUnitDict(Model model,String organSign) {
//        List<SystemDictDto> unitDict = this.systemDictApi.findSystemDictDto(DictConstant.unitBussinessId,organSign);
//        model.addAttribute("unitDict", unitDict);
//    }
//
//    private void pushProScatteredRuleMessToMQ(String organSign) {
//        JSONObject json = new JSONObject();
//        String[] tables = {"saas_product_scattered_rule","saas_inventory","saas_inventory_lot_number","saas_product_baseinfo","saas_product_scattered_record", "saas_product_scattered_record_detail"};
//        json.put("code", "sync");
//        json.put("tables", tables);
//        messagePushApi.sendMsgByOrganSign(organSign, json.toJSONString());
//    }
}
