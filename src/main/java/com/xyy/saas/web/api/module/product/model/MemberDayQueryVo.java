package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName MemberDayQueryVo
 * @Description 会员日商品查询信息类
 * <AUTHOR>
 * @Date 2019/8/26 17:13
 * @Version 1.0
 **/
@ApiModel(description = "会员日商品查询信息类")
public class MemberDayQueryVo {
    //当前页
    @JsonProperty("page")
    private Integer page;
    //每页显示数量
    @JsonProperty("rows")
    private Integer rows;
    //混合查询字段（通用名称，商品名称，助记码）
    @JsonProperty("mixedQuery")
    private String mixedQuery;
    //商品7大类,填写分类id
    @JsonProperty("systemTypeId")
    private Integer systemTypeId;
    //自定义分类，填写分类id
    @JsonProperty("customTypeId")
    private Integer customTypeId;
    //生产厂家
    @JsonProperty("manufacturer")
    private String manufacturer;
    //最小毛利率
    @JsonProperty("minGrossMargin")
    private BigDecimal minGrossMargin;
    //最大毛利率
    @JsonProperty("maxGrossMargin")
    private BigDecimal maxGrossMargin;
    //排除的商品列表,商品
    @JsonProperty("exclusivePrefs")
    private List<String> exclusivePrefs;
    //包含的商品列表,商品
    @JsonProperty("includePrefs")
    private List<String> includePrefs;
    @JsonProperty("scoreProductYn")
    private Byte scoreProductYn;
    //最小积分倍率
    @JsonProperty("minScoreRate")
    private BigDecimal minScoreRate;
    //最大积分倍率
    @JsonProperty("maxScoreRate")
    private BigDecimal maxScoreRate;

    @ApiModelProperty(value = "最小积分倍率")
    public BigDecimal getMinScoreRate() {
        return minScoreRate;
    }

    public void setMinScoreRate(BigDecimal minScoreRate) {
        this.minScoreRate = minScoreRate;
    }

    @ApiModelProperty(value = "最大积分倍率")
    public BigDecimal getMaxScoreRate() {
        return maxScoreRate;
    }

    public void setMaxScoreRate(BigDecimal maxScoreRate) {
        this.maxScoreRate = maxScoreRate;
    }

    @ApiModelProperty(value = "包含的商品列表,商品内码集合")
    public List<String> getIncludePrefs() {
        return includePrefs;
    }

    public void setIncludePrefs(List<String> includePrefs) {
        this.includePrefs = includePrefs;
    }

    @ApiModelProperty(value = "排除的商品列表,商品内码集合")
    public List<String> getExclusivePrefs() {
        return exclusivePrefs;
    }

    public void setExclusivePrefs(List<String> exclusivePrefs) {
        this.exclusivePrefs = exclusivePrefs;
    }

    @ApiModelProperty(value = "混合查询字段（通用名称，商品名称，助记码）")
    public String getMixedQuery() {
        return mixedQuery;
    }

    public void setMixedQuery(String mixedQuery) {
        this.mixedQuery = mixedQuery;
    }

    @ApiModelProperty(value = "商品7大类,填写分类id")
    public Integer getSystemTypeId() {
        return systemTypeId;
    }

    public void setSystemTypeId(Integer systemTypeId) {
        this.systemTypeId = systemTypeId;
    }

    @ApiModelProperty(value = "自定义分类，填写分类id")
    public Integer getCustomTypeId() {
        return customTypeId;
    }

    public void setCustomTypeId(Integer customTypeId) {
        this.customTypeId = customTypeId;
    }

    @ApiModelProperty(value = "生产厂家")
    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    @ApiModelProperty(value = "最小毛利率")
    public BigDecimal getMinGrossMargin() {
        return minGrossMargin;
    }

    public void setMinGrossMargin(BigDecimal minGrossMargin) {
        this.minGrossMargin = minGrossMargin;
    }

    @ApiModelProperty(value = "最大毛利率")
    public BigDecimal getMaxGrossMargin() {
        return maxGrossMargin;
    }

    public void setMaxGrossMargin(BigDecimal maxGrossMargin) {
        this.maxGrossMargin = maxGrossMargin;
    }

    @ApiModelProperty(value = "当前页")
    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    @ApiModelProperty(value = "每页显示数量")
    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    @ApiModelProperty(value = "是否积分商品，1是，0否")
    public Byte getScoreProductYn() {
        return scoreProductYn;
    }

    public void setScoreProductYn(Byte scoreProductYn) {
        this.scoreProductYn = scoreProductYn;
    }
}
