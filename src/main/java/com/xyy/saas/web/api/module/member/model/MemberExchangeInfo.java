package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 积分兑换记录
 * <AUTHOR>
 */
@ApiModel(description = "积分兑换记录")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-06T17:16:06.377+08:00")
public class MemberExchangeInfo   {

  private Long id = null;

  private Long memberId = null;

  private Long productId = null;

  private String productPref = null;

  private String productName = null;

  private BigDecimal productPrice = null;

  private BigDecimal receivableAmount = null;

  private BigDecimal actualAmount = null;

  private String batchNo = null;

  private BigDecimal number = null;

  private String createUser = null;

  private String createTime = null;

  private String updateTime = null;

  private String updateUser = null;

  private Integer yn = null;

  private Integer type = null;

  private BigDecimal integral = null;

  private String businessNo = null;

  private String organsign = null;

  private Integer baseVersion = null;

  public MemberExchangeInfo id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * id
   * @return id
  **/
  @ApiModelProperty(value = "id")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public MemberExchangeInfo memberId(Long memberId) {
    this.memberId = memberId;
    return this;
  }

   /**
   * 会员id
   * @return memberId
  **/
  @ApiModelProperty(value = "会员id")


  public Long getMemberId() {
    return memberId;
  }

  public void setMemberId(Long memberId) {
    this.memberId = memberId;
  }

  public MemberExchangeInfo productId(Long productId) {
    this.productId = productId;
    return this;
  }

   /**
   * 商品id
   * @return productId
  **/
  @ApiModelProperty(value = "商品id(必传)")


  public Long getProductId() {
    return productId;
  }

  public void setProductId(Long productId) {
    this.productId = productId;
  }

  public MemberExchangeInfo productPref(String productPref) {
    this.productPref = productPref;
    return this;
  }

   /**
   * 商品编号
   * @return productPref
  **/
  @ApiModelProperty(value = "商品编号(必传)")


  public String getProductPref() {
    return productPref;
  }

  public void setProductPref(String productPref) {
    this.productPref = productPref;
  }

  public MemberExchangeInfo productName(String productName) {
    this.productName = productName;
    return this;
  }

   /**
   * 商品名称 
   * @return productName
  **/
  @ApiModelProperty(value = "商品名称(必传)")
  public String getProductName() {
    return productName;
  }

  public void setProductName(String productName) {
    this.productName = productName;
  }

  public MemberExchangeInfo productPrice(BigDecimal productPrice) {
    this.productPrice = productPrice;
    return this;
  }

   /**
   * 商品单价
   * @return productPrice
  **/
  @ApiModelProperty(value = "商品单价(必传)")
  public BigDecimal getProductPrice() {
    return productPrice;
  }

  public void setProductPrice(BigDecimal productPrice) {
    this.productPrice = productPrice;
  }

  public MemberExchangeInfo receivableAmount(BigDecimal receivableAmount) {
    this.receivableAmount = receivableAmount;
    return this;
  }

   /**
   * 应收金额
   * @return receivableAmount
  **/
  @ApiModelProperty(value = "应收金额（非必传（POS零售时必传）")
  public BigDecimal getReceivableAmount() {
    return receivableAmount;
  }

  public void setReceivableAmount(BigDecimal receivableAmount) {
    this.receivableAmount = receivableAmount;
  }

  public MemberExchangeInfo actualAmount(BigDecimal actualAmount) {
    this.actualAmount = actualAmount;
    return this;
  }

   /**
   * 实收金额
   * @return actualAmount
  **/
  @ApiModelProperty(value = "实收金额 （非必传（POS零售时必传）")
  public BigDecimal getActualAmount() {
    return actualAmount;
  }

  public void setActualAmount(BigDecimal actualAmount) {
    this.actualAmount = actualAmount;
  }

  public MemberExchangeInfo batchNo(String batchNo) {
    this.batchNo = batchNo;
    return this;
  }

   /**
   * 批号
   * @return batchNo
  **/
  @ApiModelProperty(value = "批号  必传")
  public String getBatchNo() {
    return batchNo;
  }

  public void setBatchNo(String batchNo) {
    this.batchNo = batchNo;
  }

  public MemberExchangeInfo number(BigDecimal number) {
    this.number = number;
    return this;
  }

   /**
   * 兑换数量
   * @return number
  **/
  @ApiModelProperty(value = "兑换数量  必传")
  public BigDecimal getNumber() {
    return number;
  }

  public void setNumber(BigDecimal number) {
    this.number = number;
  }

  public MemberExchangeInfo createUser(String createUser) {
    this.createUser = createUser;
    return this;
  }

   /**
   * 创建时间
   * @return createUser
  **/
  @ApiModelProperty(value = "创建时间  非必传")
  public String getCreateUser() {
    return createUser;
  }

  public void setCreateUser(String createUser) {
    this.createUser = createUser;
  }

  public MemberExchangeInfo createTime(String createTime) {
    this.createTime = createTime;
    return this;
  }

   /**
   * 创建人
   * @return createTime
  **/
  @ApiModelProperty(value = "创建人  非必传")
  public String getCreateTime() {
    return createTime;
  }

  public void setCreateTime(String createTime) {
    this.createTime = createTime;
  }

  public MemberExchangeInfo updateTime(String updateTime) {
    this.updateTime = updateTime;
    return this;
  }

   /**
   * 更新时间
   * @return updateTime
  **/
  @ApiModelProperty(value = "更新时间 非必传")
  public String getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(String updateTime) {
    this.updateTime = updateTime;
  }

  public MemberExchangeInfo updateUser(String updateUser) {
    this.updateUser = updateUser;
    return this;
  }

   /**
   * 更新人
   * @return updateUser
  **/
  @ApiModelProperty(value = "更新人 非必传")
  public String getUpdateUser() {
    return updateUser;
  }

  public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
  }

  public MemberExchangeInfo yn(Integer yn) {
    this.yn = yn;
    return this;
  }

   /**
   * 是否可用
   * @return yn
  **/
  @ApiModelProperty(value = "是否可用  必传  1有效 0删除")
  public Integer getYn() {
    return yn;
  }

  public void setYn(Integer yn) {
    this.yn = yn;
  }

  public MemberExchangeInfo type(Integer type) {
    this.type = type;
    return this;
  }

   /**
   * 0:兑换  1:零售
   * @return type
  **/
  @ApiModelProperty(value = "0:兑换  1:零售   必传")


  public Integer getType() {
    return type;
  }

  public void setType(Integer type) {
    this.type = type;
  }

  public MemberExchangeInfo integral(BigDecimal integral) {
    this.integral = integral;
    return this;
  }

   /**
   * 积分
   * @return integral
  **/
  @ApiModelProperty(value = "积分  必传")


  public BigDecimal getIntegral() {
    return integral;
  }

  public void setIntegral(BigDecimal integral) {
    this.integral = integral;
  }

  public MemberExchangeInfo businessNo(String businessNo) {
    this.businessNo = businessNo;
    return this;
  }

   /**
   * 业务号
   * @return businessNo
  **/
  @ApiModelProperty(value = "业务号 非必传")


  public String getBusinessNo() {
    return businessNo;
  }

  public void setBusinessNo(String businessNo) {
    this.businessNo = businessNo;
  }

  public MemberExchangeInfo organsign(String organsign) {
    this.organsign = organsign;
    return this;
  }

   /**
   * 机构标识
   * @return organsign
  **/
  @ApiModelProperty(value = "机构标识 非必传")


  public String getOrgansign() {
    return organsign;
  }

  public void setOrgansign(String organsign) {
    this.organsign = organsign;
  }

  public MemberExchangeInfo baseVersion(Integer baseVersion) {
    this.baseVersion = baseVersion;
    return this;
  }

   /**
   * 版本号
   * @return baseVersion
  **/
  @ApiModelProperty(value = "版本号 非必传")


  public Integer getBaseVersion() {
    return baseVersion;
  }

  public void setBaseVersion(Integer baseVersion) {
    this.baseVersion = baseVersion;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MemberExchangeInfo memberExchangeInfo = (MemberExchangeInfo) o;
    return Objects.equals(this.id, memberExchangeInfo.id) &&
        Objects.equals(this.memberId, memberExchangeInfo.memberId) &&
        Objects.equals(this.productId, memberExchangeInfo.productId) &&
        Objects.equals(this.productPref, memberExchangeInfo.productPref) &&
        Objects.equals(this.productName, memberExchangeInfo.productName) &&
        Objects.equals(this.productPrice, memberExchangeInfo.productPrice) &&
        Objects.equals(this.receivableAmount, memberExchangeInfo.receivableAmount) &&
        Objects.equals(this.actualAmount, memberExchangeInfo.actualAmount) &&
        Objects.equals(this.batchNo, memberExchangeInfo.batchNo) &&
        Objects.equals(this.number, memberExchangeInfo.number) &&
        Objects.equals(this.createUser, memberExchangeInfo.createUser) &&
        Objects.equals(this.createTime, memberExchangeInfo.createTime) &&
        Objects.equals(this.updateTime, memberExchangeInfo.updateTime) &&
        Objects.equals(this.updateUser, memberExchangeInfo.updateUser) &&
        Objects.equals(this.yn, memberExchangeInfo.yn) &&
        Objects.equals(this.type, memberExchangeInfo.type) &&
        Objects.equals(this.integral, memberExchangeInfo.integral) &&
        Objects.equals(this.businessNo, memberExchangeInfo.businessNo) &&
        Objects.equals(this.organsign, memberExchangeInfo.organsign) &&
        Objects.equals(this.baseVersion, memberExchangeInfo.baseVersion);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, memberId, productId, productPref, productName, productPrice, receivableAmount, actualAmount, batchNo, number, createUser, createTime, updateTime, updateUser, yn, type, integral, businessNo, organsign, baseVersion);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MemberExchangeInfo {\n");

    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    memberId: ").append(toIndentedString(memberId)).append("\n");
    sb.append("    productId: ").append(toIndentedString(productId)).append("\n");
    sb.append("    productPref: ").append(toIndentedString(productPref)).append("\n");
    sb.append("    productName: ").append(toIndentedString(productName)).append("\n");
    sb.append("    productPrice: ").append(toIndentedString(productPrice)).append("\n");
    sb.append("    receivableAmount: ").append(toIndentedString(receivableAmount)).append("\n");
    sb.append("    actualAmount: ").append(toIndentedString(actualAmount)).append("\n");
    sb.append("    batchNo: ").append(toIndentedString(batchNo)).append("\n");
    sb.append("    number: ").append(toIndentedString(number)).append("\n");
    sb.append("    createUser: ").append(toIndentedString(createUser)).append("\n");
    sb.append("    createTime: ").append(toIndentedString(createTime)).append("\n");
    sb.append("    updateTime: ").append(toIndentedString(updateTime)).append("\n");
    sb.append("    updateUser: ").append(toIndentedString(updateUser)).append("\n");
    sb.append("    yn: ").append(toIndentedString(yn)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    integral: ").append(toIndentedString(integral)).append("\n");
    sb.append("    businessNo: ").append(toIndentedString(businessNo)).append("\n");
    sb.append("    organsign: ").append(toIndentedString(organsign)).append("\n");
    sb.append("    baseVersion: ").append(toIndentedString(baseVersion)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

