package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 会员储值卡退款Vo
 * <AUTHOR>
 */
@ApiModel(description = "会员储值卡退款")
public class MemberPrepayCardRefundVo extends MemberPrepayCardTokenVo implements Serializable {

    private static final long serialVersionUID = -8362753263078650963L;

    /**
     * 会员guid
     */
    @ApiModelProperty(value = "会员guid", required = true, example = "584a4460-2144-47c6-b7f7-d85b33d3322c")
    private String memberGuid;

    /**
     * 退款总额
     */
    @ApiModelProperty(value = "退款总额", required = true, example = "20.00")
    private BigDecimal totalAmount;

    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识", hidden = true)
    private String organsign;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", hidden = true, example = "张三")
    private String createUser;

    /**
     * 退单号
     */
    @ApiModelProperty(value = "退单号", required = true, example = "ZHL00000271155118564")
    private String ticketNo;

    /**
     * 原小票号
     */
    @ApiModelProperty(value = "原小票号", required = true, example = "ZHL000002711551180141")
    private String oldTicketNo;

    public String getMemberGuid() {
        return memberGuid;
    }

    public void setMemberGuid(String memberGuid) {
        this.memberGuid = memberGuid;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getOrgansign() {
        return organsign;
    }

    public void setOrgansign(String organsign) {
        this.organsign = organsign;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getTicketNo() {
        return ticketNo;
    }

    public void setTicketNo(String ticketNo) {
        this.ticketNo = ticketNo;
    }

    public String getOldTicketNo() {
        return oldTicketNo;
    }

    public void setOldTicketNo(String oldTicketNo) {
        this.oldTicketNo = oldTicketNo;
    }

    @Override
    public String toString() {
        return "MemberPrepayCardRefundVo{" +
                "memberGuid='" + memberGuid + '\'' +
                ", totalAmount=" + totalAmount +
                ", organsign='" + organsign + '\'' +
                ", createUser='" + createUser + '\'' +
                ", ticketNo='" + ticketNo + '\'' +
                ", oldTicketNo='" + oldTicketNo + '\'' +
                '}';
    }
}