package com.xyy.saas.web.api.module.member.model;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 会员储值卡导出ExcelVo
 * <AUTHOR>
 */
public class MemberPrepayCardExcelVo implements Serializable {

    private static final long serialVersionUID = -2172969172573796262L;

    /**
     * 会员姓名
     */
    private String memberName;

    /**
     * 会员卡号
     */
    private String cartNo;

    /**
     * 会员等级
     */
    private String vipLevelName;

    /**
     * 开卡时间
     */
    private String createTime;

    /**
     * 开卡人
     */
    private String createUser;

    /**
     * 卡内余额
     */
    private BigDecimal totalAmount;

    /**
     * 最后充值时间
     */
    private String lastDepositTime;

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getCartNo() {
        return cartNo;
    }

    public void setCartNo(String cartNo) {
        this.cartNo = cartNo;
    }

    public String getVipLevelName() {
        return vipLevelName;
    }

    public void setVipLevelName(String vipLevelName) {
        this.vipLevelName = vipLevelName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getLastDepositTime() {
        return lastDepositTime;
    }

    public void setLastDepositTime(String lastDepositTime) {
        this.lastDepositTime = lastDepositTime;
    }

    @Override
    public String toString() {
        return "MemberPrepayCardExcelVo{" +
                "memberName='" + memberName + '\'' +
                ", cartNo='" + cartNo + '\'' +
                ", vipLevelName='" + vipLevelName + '\'' +
                ", createTime='" + createTime + '\'' +
                ", createUser='" + createUser + '\'' +
                ", totalAmount=" + totalAmount +
                ", lastDepositTime='" + lastDepositTime + '\'' +
                '}';
    }
}