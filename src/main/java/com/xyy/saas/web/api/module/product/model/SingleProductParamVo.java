package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName SingleProductParamVo
 * @Description 单个商品查询类
 * <AUTHOR>
 * @Date 2019/8/30 19:15
 * @Version 1.0
 **/
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

@ApiModel(description = "单个商品查询类")
public class SingleProductParamVo {

    @JsonProperty("pref")
    private String pref;

    @ApiModelProperty(value = "商品内码")
    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

}
