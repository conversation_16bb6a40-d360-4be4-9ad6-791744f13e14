package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.inventory.core.api.InventoryIncomeStatementApi;
import com.xyy.saas.web.api.module.product.model.InventoryIncomeStatementDetailVo;
import com.xyy.saas.web.api.module.product.model.InventoryIncomeStatementVo;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-30T11:13:55.467+08:00")

@Controller
public class IncomeStatementSaveOrUpdateApiController implements IncomeStatementSaveOrUpdateApi {


    @Reference(version = "1.0.0")
    private InventoryIncomeStatementApi incomeStatementApi;
    @ResponseBody
    public ResponseEntity<ResultVO> incomeStatementSaveOrUpdate(@ApiParam(value = "报损报溢Vo" ,required=true ) @RequestHeader("userName") String userName, @RequestHeader("organSign") String organSign, @Valid @RequestBody InventoryIncomeStatementVo inventoryIncomeStatementVo) {
        if(inventoryIncomeStatementVo==null){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR,""), HttpStatus.INTERNAL_SERVER_ERROR);
        }
        com.xyy.saas.inventory.core.dto.InventoryIncomeStatementVo vo = new com.xyy.saas.inventory.core.dto.InventoryIncomeStatementVo();
        List<InventoryIncomeStatementDetailVo> idvos = inventoryIncomeStatementVo.getInventoryIncomeStatementDetailVos();
        List<com.xyy.saas.inventory.core.dto.InventoryIncomeStatementDetailVo> isdvos = new ArrayList<>();
        for(InventoryIncomeStatementDetailVo ivo : idvos){
            com.xyy.saas.inventory.core.dto.InventoryIncomeStatementDetailVo isvo = new com.xyy.saas.inventory.core.dto.InventoryIncomeStatementDetailVo();
            BeanUtils.copyProperties(ivo,isvo);
            isdvos.add(isvo);
        }
        BeanUtils.copyProperties(inventoryIncomeStatementVo,vo);
        vo.setInventoryIncomeStatementDetailVos(isdvos);
        vo.setOrgansign(organSign);
        vo.setCreateUser(userName);
        Integer integer = 0;//incomeStatementApi.saveOrUpdate(vo);
        if(integer>=1){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.SUCCESS,""), HttpStatus.OK);
        }else{
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR,""), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

}
