package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class ExchangeProductVo {
    /**
     * 商品id
     */
    private Long productId;

    /**
     * 商品编号
     */
    private String productPref;

    /**
     * 商品名称
     */
    private String productName;


    /**
     * 批号
     */
    private String batchNo;

    /**
     * 兑换数量
     */
    private BigDecimal number;


    /**
     * 总计积分
     */
    private BigDecimal integral;

    /**
     * 商品内码
     */
    private String pref;
    /**
     * 货架id
     */
    private Integer positionId;

    /**
     * 追溯码
     */
    private String traceCode;

    @ApiModelProperty(value = "总计积分")
    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    @ApiModelProperty(value = "商品编码")
    public String getProductPref() {
        return productPref;
    }

    public void setProductPref(String productPref) {
        this.productPref = productPref;
    }

    @ApiModelProperty(value = "商品名称")
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }


    @ApiModelProperty(value = "批号")
    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    @ApiModelProperty(value = "购买数量")
    public BigDecimal getNumber() {
        return number;
    }

    public void setNumber(BigDecimal number) {
        this.number = number;
    }

    @ApiModelProperty(value = "总计积分")
    public BigDecimal getIntegral() {
        return integral;
    }

    public void setIntegral(BigDecimal integral) {
        this.integral = integral;
    }




    @ApiModelProperty(value = "商品内码")
    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }
    @ApiModelProperty(value = "货架id")
	public Integer getPositionId() {
		return positionId;
	}

	public void setPositionId(Integer positionId) {
		this.positionId = positionId;
	}

    public String getTraceCode() {
        return traceCode;
    }

    public void setTraceCode(String traceCode) {
        this.traceCode = traceCode;
    }
}
