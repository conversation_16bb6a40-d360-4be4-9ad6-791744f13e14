package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 会员等级类
 * <AUTHOR>
 */
public class MemberLevelVo  implements Serializable {

    private static final long serialVersionUID = 4887144192562708387L;

    /**
     * 主键Id
     */
    @ApiModelProperty(value = "会员等级id", required = true, example = "584a4460-2144-47c6-b7f7-d85b33d3322c")
    private Long id;

    /**
     * 级别名称
     */
    @ApiModelProperty(value = "会员等级名称", required = true, example = "黄金会员")
    private String name;

    /**
     * 上级等级id 0 为最高级
     */
    @ApiModelProperty(value = "会员等级上级id, 0为最高级", required = true, example = "12")
    private Long upLevelId;

    /**
     * 升级积分
     */
    @ApiModelProperty(value = "升级积分", required = true, example = "50")
    private BigDecimal upPoint;

    /**
     * 折扣率
     */
    @ApiModelProperty(value = "折扣率", required = true, example = "100")
    private Double discount;

    /**
     * 价格策略 1 零售价 2 会员价
     */
    @ApiModelProperty(value = "价格策略 目前只有零售价", required = true, example = "零售价")
    private Integer priceStrategy;

    /**
     * 状态 启用 禁用 0 禁用 1 启用
     */
    @ApiModelProperty(value = "会员等级状态 0-禁用 1-启用", required = true, example = "1")
    private Integer status;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", required = true, example = "张三")
    private String createUser;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", required = true, example = "2019-08-05 22:22:22")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人", required = true, example = "李四")
    private String updateUser;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", required = true, example = "2019-08-05 22:22:22")
    private Date updateTime;

    /**
     * 是否删除 1 有效 0 删除
     */
    @ApiModelProperty(value = "是否删除 1-有效 0-删除", required = true, example = "1")
    private Integer yn;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "baseVersion", required = true, example = "1111")
    private Integer baseVersion;

    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识", required = true, example = "ZHL00006788")
    private String organsign;

    /**
     * 页码
     */
//    @ApiModelProperty(value = "当前页码", required = true, example = "1")
    private Integer pageNum;


    /**
     * 页码大小
     */
//    @ApiModelProperty(value = "每页数量", required = true, example = "10")
    private Integer pageSize;
    
    /**
     * 页码
     */
    @ApiModelProperty(value = "当前页码", required = true, example = "1")
    private Integer page;
    
    
    /**
     * 页码大小
     */
    @ApiModelProperty(value = "每页数量", required = true, example = "10")
    private Integer rows;

    /**
     * 上级等级名称
     */
    @ApiModelProperty(value = "上级等级名称", required = true, example = "黄金会员")
    private String  upLevelName;

    /**
     * 是否开启会员特价
     */
    @ApiModelProperty(value = "是否开启会员特价 0-否 1-是'", required = true, example = "0")
    private Integer isSpecial;

    /**
     * 会员优惠设置
     */
    @ApiModelProperty(value = "会员优惠设置集合", required = true, example = "会员优惠设置集合")
    private MemberPrepayCardConfigSaveVo memberPrepayCardConfigSaveVo;

    /**
     * 会员日设置
     */
    @ApiModelProperty(value = "会员日设置", required = true, example = "会员日设置")
    private MemberPreferentialDaySaveVo memberPreferentialDaySaveVo;

    /**
     * 积分规则
     */
    @ApiModelProperty(value = "积分规则设置", required = true, example = "积分规则设置")
    private MemberPointExchange memberPointExchange;

    /**
     * 所需积分
     */
    @ApiModelProperty(value = "所需积分", required = true, example = "所需积分")
    private BigDecimal needPoint;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public Long getUpLevelId() {
        return upLevelId;
    }

    public void setUpLevelId(Long upLevelId) {
        this.upLevelId = upLevelId;
    }

    public BigDecimal getUpPoint() {
        return upPoint;
    }

    public void setUpPoint(BigDecimal upPoint) {
        this.upPoint = upPoint;
    }

    public Double getDiscount() {
        return discount;
    }

    public void setDiscount(Double discount) {
        this.discount = discount;
    }

    public Integer getPriceStrategy() {
        return priceStrategy;
    }

    public void setPriceStrategy(Integer priceStrategy) {
        this.priceStrategy = priceStrategy;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getYn() {
        return yn;
    }

    public void setYn(Integer yn) {
        this.yn = yn;
    }


    public Integer getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(Integer baseVersion) {
        this.baseVersion = baseVersion;
    }

    public String getOrgansign() {
        return organsign;
    }

    public void setOrgansign(String organsign) {
        this.organsign = organsign;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getUpLevelName() {
        return upLevelName;
    }

    public void setUpLevelName(String upLevelName) {
        this.upLevelName = upLevelName;
    }

    public Integer getIsSpecial() {
        return isSpecial;
    }

    public void setIsSpecial(Integer isSpecial) {
        this.isSpecial = isSpecial;
    }

	public Integer getPage() {
		return page;
	}

	public void setPage(Integer page) {
		this.page = page;
	}

	public Integer getRows() {
		return rows;
	}

	public void setRows(Integer rows) {
		this.rows = rows;
	}

    public MemberPrepayCardConfigSaveVo getMemberPrepayCardConfigSaveVo() {
        return memberPrepayCardConfigSaveVo;
    }

    public void setMemberPrepayCardConfigSaveVo(MemberPrepayCardConfigSaveVo memberPrepayCardConfigSaveVo) {
        this.memberPrepayCardConfigSaveVo = memberPrepayCardConfigSaveVo;
    }

    public MemberPreferentialDaySaveVo getMemberPreferentialDaySaveVo() {
        return memberPreferentialDaySaveVo;
    }

    public void setMemberPreferentialDaySaveVo(MemberPreferentialDaySaveVo memberPreferentialDaySaveVo) {
        this.memberPreferentialDaySaveVo = memberPreferentialDaySaveVo;
    }

    public MemberPointExchange getMemberPointExchange() {
        return memberPointExchange;
    }

    public void setMemberPointExchange(MemberPointExchange memberPointExchange) {
        this.memberPointExchange = memberPointExchange;
    }

    public BigDecimal getNeedPoint() {
        return needPoint;
    }

    public void setNeedPoint(BigDecimal needPoint) {
        this.needPoint = needPoint;
    }

    @Override
    public String toString() {
        return "MemberLevelVo{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", upLevelId=" + upLevelId +
                ", upPoint=" + upPoint +
                ", discount=" + discount +
                ", priceStrategy=" + priceStrategy +
                ", status=" + status +
                ", createUser='" + createUser + '\'' +
                ", createTime=" + createTime +
                ", updateUser='" + updateUser + '\'' +
                ", updateTime=" + updateTime +
                ", yn=" + yn +
                ", baseVersion=" + baseVersion +
                ", organsign='" + organsign + '\'' +
                ", pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                ", page=" + page +
                ", rows=" + rows +
                ", upLevelName='" + upLevelName + '\'' +
                ", isSpecial=" + isSpecial +
                ", memberPrepayCardConfigSaveVo=" + memberPrepayCardConfigSaveVo +
                ", memberPreferentialDaySaveVo=" + memberPreferentialDaySaveVo +
                ", memberPointExchange=" + memberPointExchange +
                ", needPoint=" + needPoint +
                '}';
    }
}