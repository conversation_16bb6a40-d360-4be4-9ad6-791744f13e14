package com.xyy.saas.web.api.common.config;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @Author:chenxiaoyi
 * @Date:2023/02/02 19:38
 */
@Component
public class PaymentChangeGrayConfig {

    @Value("${pay.channle.change.organSigns:''}")
    private String payChannleChangeOranSigns;

    /**
     * 支付切换开关
     * @param organSign
     * @return
     */
    public boolean payChannleChange(String organSign){
        // 如果没有配置任何机构号,就全部切换
        if(StringUtils.isBlank(payChannleChangeOranSigns)){
            return true;
        }
        // 机构为空 默认不走支付切换
        if(StringUtils.isBlank(organSign)){
            return false;
        }

        return StringUtils.contains(payChannleChangeOranSigns,organSign);
    }


}
