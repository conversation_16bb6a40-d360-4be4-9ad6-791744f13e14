package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

@ApiModel(description = "商品拆零信息维护查询对象")
public class ScatteredRuleQueryVo {

    @JsonProperty("id")
    private Long id;//数据库主键id
    @JsonProperty("rows")
    private Integer rows;//每页显示多少
    @JsonProperty("page")
    private Integer page;//当前页码
    @JsonProperty("name")
    private String name;//通用名称
    @JsonProperty("manufacturer")
    private String manufacturer;//成产厂家
    @JsonProperty("productPref")
    private String productPref;//商品编号，要传商品外码
    @JsonProperty("createUser")
    private String createUser;//创建人，要传用户id
    @JsonProperty("createTimeStart")
    private String createTimeStart;//查询开始时间，格式是1999-08-28
    @JsonProperty("createTimeEnd")
    private String createTimeEnd;//查询截止-时间，格式是1999-08-28
    @JsonProperty("status")
    private Byte status;
    @JsonProperty("approvalNumber")
    private String approvalNumber;
    @JsonProperty("systemType")
    private Integer systemType;
    @JsonProperty("productName")
    private String productName;//查询截止-时间，格式是1999-08-28

    @ApiModelProperty(value = "批准文号")
    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    @ApiModelProperty(value = "商品类型，传id")
    public Integer getSystemType() {
        return systemType;
    }

    public void setSystemType(Integer systemType) {
        this.systemType = systemType;
    }

    @ApiModelProperty(value = "商品名称")
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    @ApiModelProperty(value = "主键id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @ApiModelProperty(value = "每页显示多少")
    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    @ApiModelProperty(value = "当前页码")
    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    @ApiModelProperty(value = "通用名称")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @ApiModelProperty(value = "生产厂家")
    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    @ApiModelProperty(value = "商品编号，要传商品外码")
    public String getProductPref() {
        return productPref;
    }

    public void setProductPref(String productPref) {
        this.productPref = productPref;
    }

    @ApiModelProperty(value = "创建人，要传用户id")
    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    @ApiModelProperty(value = "查询开始时间，格式是1999-08-28")
    public String getCreateTimeStart() {
        return createTimeStart;
    }

    public void setCreateTimeStart(String createTimeStart) {
        this.createTimeStart = createTimeStart;
    }

    @ApiModelProperty(value = "查询截止-时间，格式是1999-08-28")
    public String getCreateTimeEnd() {
        return createTimeEnd;
    }

    public void setCreateTimeEnd(String createTimeEnd) {
        this.createTimeEnd = createTimeEnd;
    }

    @ApiModelProperty(value = "是否启用，1--启用，0--不启用,启用禁用的接口，需要传id，status两个参数")
    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

}
