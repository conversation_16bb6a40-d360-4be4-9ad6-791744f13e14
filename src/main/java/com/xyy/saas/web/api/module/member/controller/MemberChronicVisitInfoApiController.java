package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.member.core.api.MemberChronicVisitInfoApi;
import com.xyy.saas.member.core.dto.MemberChronicVisitInfoDto;
import com.xyy.saas.member.core.dto.MemberChronicVisitInfoQueryReqDto;
import com.xyy.saas.member.core.dto.MemberChronicVisitInfoReqDto;
import com.xyy.saas.member.core.response.ResultVO;
import com.xyy.saas.web.api.module.member.model.MemberChronicVisitInfoQueryReqVo;
import com.xyy.saas.web.api.module.member.model.MemberChronicVisitInfoReqVo;
import com.xyy.saas.web.api.module.member.model.MemberChronicVisitInfoVo;
import com.xyy.saas.web.api.module.member.service.DrugstoreService;
import com.xyy.saas.web.api.module.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import static com.xyy.saas.web.api.module.utils.OptUtil.dateToLong;

/**
 * @Description 患者回访API-患者即会员
 * <AUTHOR>
 * @Create 2020-09-24 16:15
 * @menu 患者回访API
 */
@Controller
@RequestMapping(value = "/member/memberChronicVisitInfo")
@Api(value = "MemberChronicVisitInfoAPI", description = "患者回访API")
public class MemberChronicVisitInfoApiController{
    private static final Logger logger = LoggerFactory.getLogger(MemberChronicVisitInfoApiController.class);

    @Reference(version = "0.0.1")
    private MemberChronicVisitInfoApi memberChronicVisitInfoApi;
    @Autowired
    private DrugstoreService drugstoreService;


    /*
     * @param commonRequestModelStr 机构信息
     * @param reqVo 实体VO信息
     */
    @ApiOperation(value = "新增或编辑患者回访", notes = "新增或编辑患者回访", response = Boolean.class, tags={ "患者回访", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = Boolean.class) })
    @RequestMapping(value = "/saveOrUpdateChronicVisitInfo",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> saveOrUpdateChronicVisitInfo(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                                 @RequestBody MemberChronicVisitInfoReqVo reqVo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        MemberChronicVisitInfoReqDto reqDto = new MemberChronicVisitInfoReqDto();
        BeanUtils.copyProperties(reqVo, reqDto);
        reqDto.setVisitTime(dateToLong(reqVo.getVisitTime())); //date -> long
        reqDto.setOrganSign(commonRequestModel.getOrganSign());
        boolean ChronicVisitInfo = memberChronicVisitInfoApi.saveOrUpdateChronicVisitInfo(reqDto);
        if(ChronicVisitInfo){
            return new ResponseEntity(ResultVO.createSuccess("操作成功"),HttpStatus.OK);
        }
        return new ResponseEntity(ResultVO.createError("操作失败"),HttpStatus.OK);
    }

    /**
     * @param commonRequestModelStr 机构信息
     * @param queryReqVo            查询条件
     */
    @ApiOperation(value = "门店分页查询患者回访", notes = "门店分页查询患者回访", response = MemberChronicVisitInfoVo.class, tags = {"患者回访",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberChronicVisitInfoVo.class)})
    @RequestMapping(value = "/queryChronicVisitInfoPage", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> queryChronicVisitInfoPage(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                              @RequestBody MemberChronicVisitInfoQueryReqVo queryReqVo) {
        CommonRequestModel model = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
//   organ_sign_type` tinyint(4) DEFAULT '1' COMMENT '机构类型 1、门店  3、总部',
//  `biz_model` tinyint(4) DEFAULT '1' COMMENT '经营模式 1、单体 2、连锁 3、联营',
        Byte bizModel = model.getBizModel();
        Byte organSignType = model.getOrganSignType();
        if (organSignType == 3) {
            return new ResponseEntity(new ResultVO(-1, "机构不匹配", false), HttpStatus.OK);
        }
        MemberChronicVisitInfoQueryReqDto queryReqDto = new MemberChronicVisitInfoQueryReqDto();
        if (bizModel == 1) {
            queryReqDto.setReVisitDrugstore(model.getOrganSign());
            queryReqDto.setSourceDrugstore(model.getOrganSign());
        } else {
            String sourceDrugstore = queryReqVo.getSourceDrugstore();
            String reVisitDrugstore = queryReqVo.getReVisitDrugstore();
            if (StringUtils.isEmpty(sourceDrugstore)) {
                queryReqDto.setSourceHeadquartersOrganSign(model.getHeadquartersOrganSign());
            } else {
                queryReqDto.setSourceDrugstore(sourceDrugstore);
            }
            if (StringUtils.isEmpty(reVisitDrugstore)) {
                queryReqDto.setReVisitHeadquartersOrganSign(model.getHeadquartersOrganSign());
            } else {
                queryReqDto.setReVisitDrugstore(reVisitDrugstore);
            }
        }
        queryReqDto.setMemberMixedQuery(queryReqVo.getMemberMixedQuery());
        queryReqDto.setChronicPref(queryReqVo.getChronicPref());
        queryReqDto.setReVisitStart(queryReqVo.getReVisitStart());
        queryReqDto.setReVisitEnd(queryReqVo.getReVisitEnd());
        queryReqDto.setCreateStart(queryReqVo.getCreateStart());
        queryReqDto.setCreateEnd(queryReqVo.getCreateEnd());
        queryReqDto.setPageSize(queryReqVo.getPageSize());
        queryReqDto.setPageNum(queryReqVo.getPageNum());
        return new ResponseEntity(ResultVO.createSuccess(memberChronicVisitInfoApi.queryChronicVisitInfoPage(queryReqDto)), HttpStatus.OK);
    }


    /**
     * @param commonRequestModelStr 机构信息
     * @param queryReqVo            查询条件
     */
    @ApiOperation(value = "总部分页查询患者回访", notes = "总部分页查询患者回访", response = MemberChronicVisitInfoVo.class, tags = {"患者回访",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberChronicVisitInfoVo.class)})
    @RequestMapping(value = "/zb/queryChronicVisitInfoPage", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> queryChronicVisitInfoPageForZB(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                                   @RequestBody MemberChronicVisitInfoQueryReqVo queryReqVo) {
        CommonRequestModel model = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        //   organ_sign_type机构类型 1、门店  3、总部
        //  biz_model经营模式 1、单体 2、连锁 3、联营
        Byte bizModel = model.getBizModel();
        Byte organSignType = model.getOrganSignType();
        if (organSignType == 1) {
            return new ResponseEntity(new ResultVO(-1, "机构不匹配", false), HttpStatus.OK);
        }
        MemberChronicVisitInfoQueryReqDto queryReqDto = new MemberChronicVisitInfoQueryReqDto();
        String sourceDrugstore = queryReqVo.getSourceDrugstore();
        String reVisitDrugstore = queryReqVo.getReVisitDrugstore();
        if (StringUtils.isEmpty(sourceDrugstore)) {
            queryReqDto.setSourceHeadquartersOrganSign(model.getOrganSign());
        } else {
            queryReqDto.setSourceDrugstore(sourceDrugstore);
        }
        if (StringUtils.isEmpty(reVisitDrugstore)) {
            queryReqDto.setReVisitHeadquartersOrganSign(model.getOrganSign());
        } else {
            queryReqDto.setReVisitDrugstore(reVisitDrugstore);
        }
        queryReqDto.setMemberMixedQuery(queryReqVo.getMemberMixedQuery());
        queryReqDto.setChronicPref(queryReqVo.getChronicPref());
        queryReqDto.setReVisitStart(queryReqVo.getReVisitStart());
        queryReqDto.setReVisitEnd(queryReqVo.getReVisitEnd());
        queryReqDto.setCreateStart(queryReqVo.getCreateStart());
        queryReqDto.setCreateEnd(queryReqVo.getCreateEnd());
        queryReqDto.setPageSize(queryReqVo.getPageSize());
        queryReqDto.setPageNum(queryReqVo.getPageNum());
        return new ResponseEntity(ResultVO.createSuccess(memberChronicVisitInfoApi.queryChronicVisitInfoPage(queryReqDto)), HttpStatus.OK);
    }

}