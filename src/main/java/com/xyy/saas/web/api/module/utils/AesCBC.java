package com.xyy.saas.web.api.module.utils;

import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * AES 是一种可逆加密算法，对用户的敏感信息加密处理
 * 对原始数据进行AES加密后，在进行Base64编码转化；
 * 正确
 */
public class AesCBC {
    /**
     * 加密用的Key 可以用26个字母和数字组成
     * 此处使用AES-128-CBC加密模式，key需要为16位。
     */
    private static AesCBC instance = null;

    //private static
    private AesCBC() {

    }

    public static AesCBC getInstance() {
        if (instance == null)
            instance = new AesCBC();
        return instance;
    }

    // 加密
    public String encrypt(String sSrc, String encodingFormat, String sKey, String ivParameter) {
        System.out.println(sSrc);
        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            byte[] raw = sKey.getBytes();
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            IvParameterSpec iv = new IvParameterSpec(ivParameter.getBytes());//使用CBC模式，需要一个向量iv，可增加加密算法的强度
            cipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);
            byte[] encrypted = cipher.doFinal(sSrc.getBytes(encodingFormat));
            String encodeStr = new BASE64Encoder().encode(encrypted);
            return encodeStr.replaceAll("\r|\n","");//此处使用BASE64做转码。
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    // 解密
    public String decrypt(String sSrc, String encodingFormat, String sKey, String ivParameter) {
        try {
            byte[] raw = sKey.getBytes("ASCII");
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            IvParameterSpec iv = new IvParameterSpec(ivParameter.getBytes());
            cipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);
            byte[] encrypted1 = new BASE64Decoder().decodeBuffer(sSrc);//先用base64解密
            byte[] original = cipher.doFinal(encrypted1);
            String originalString = new String(original,encodingFormat);
            return originalString;
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

//    public static void main(String[] args) {
//        String d = AesCBC.getInstance().decrypt("O3FF5IsHxd77OnPHzPqBCwf3wpVxGvriGWtvEN1SrSucHNrnlWSSqf5SQIR046wbzPHVnACnAOvOC0fmyB3A5DVLpss/Tjx6F9qBLZcSUbjeZodpqe6Wk5EKOOMGL++Uthg29xytzsEM7bBi25GMqg7qzRnNFWl9B1PVSb4+NutbnQNehH9UyzdiJ9TRkC9o/SGYIXeGb9/2fA+u8jLIIfd6gV3RXcRb9bT8Cjr/Vdw=\"",
//                "utf-8",
//                ConsultConstants.PUB_KEY,
//                ConsultConstants.IV_PARAMETER);
//        System.out.println(d);
//        // marriage=1&name=李刚&sex=1&birth_day=2010-06-17&phone=18512345678&cos_code=xyy001&cos_key=edf1af948e33a542f4ecaf9590caa5b9&request_time=1560751860185&third_require_code=18
//    }

}