package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.member.core.dto.MemberConvertV2Dto;
import com.xyy.saas.member.core.dto.MemberExchangeInfoV2Dto;
import com.xyy.saas.rabbitmq.core.api.MessagePushApi;
import com.xyy.saas.web.api.module.member.model.ExchangeProductVo;
import com.xyy.saas.web.api.module.member.model.MemberPointConvertVo;
import com.xyy.saas.web.api.module.member.service.MemberExchangeInfoService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/member")
public class MemberConvertApiController {
	private static final Logger logger = Logger.getLogger(MemberConvertApiController.class);

    @Autowired
    private MemberExchangeInfoService memberExchangeInfoService;

    @Reference( version = "0.0.1")
    private MessagePushApi messagePushApi;


    @ApiOperation(value = "会员积分兑换", notes = "会员积分兑换", response = MemberPointConvertVo.class, tags={ "会员积分兑换", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberPointConvertVo.class) })
    @RequestMapping(value = "/memberConvert/saveMemberConvert",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> saveMemberConvert(@RequestHeader(name = "organSign", required = true) String organSign, @ApiParam(value = "会员积分兑换", required = true) @RequestBody MemberPointConvertVo convertVo) throws Exception {
    	convertVo.setOrgansign(organSign);
        MemberExchangeInfoV2Dto v2Dto= null;
        MemberConvertV2Dto v2Vo = new MemberConvertV2Dto();
        List<MemberExchangeInfoV2Dto> list = new ArrayList<>();
        List<ExchangeProductVo> exchangeInfos = convertVo.getProductVoList();
        for (ExchangeProductVo infoDto : exchangeInfos) {
            v2Dto = new MemberExchangeInfoV2Dto();
            BeanUtils.copyProperties(infoDto, v2Dto);
            list.add(v2Dto);
        }
        BeanUtils.copyProperties(convertVo,v2Vo);
        v2Vo.setProductVoList(list);
        Map<String,Object> map = memberExchangeInfoService.saveMemberExchangeInfo(v2Vo);
        if(map!=null && map.get("code")!=null && Integer.parseInt(map.get("code").toString()) == 0){
            syncMqPush(organSign);
        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(map), HttpStatus.OK);
    }

    public void syncMqPush(String organsign) {
        JSONObject msg = new JSONObject();
        String [] table = {"saas_member_base","saas_member_exchange_info","saas_member_point_history","saas_inventory","saas_inventory_lot_number"};
        msg.put("code", "sync");
        msg.put("tables", table);
        messagePushApi.sendMsgByOrganSign(organsign,msg.toString());
    }

}
