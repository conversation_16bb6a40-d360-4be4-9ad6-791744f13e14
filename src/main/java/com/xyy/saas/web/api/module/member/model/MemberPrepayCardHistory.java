package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 会员储值卡流水
 * <AUTHOR>
 */
@ApiModel(description = "会员储值卡流水")
public class MemberPrepayCardHistory {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 会员guid
     */
    @ApiModelProperty(value = "会员guid")
    private String memberGuid;

    /**
     * 会员卡号
     */
    @ApiModelProperty(value = "会员卡号")
    private String cartNo;

    /**
     * 会员姓名
     */
    @ApiModelProperty(value = "会员姓名")
    private String memberName;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String telephone;

    /**
     * 会员等级id
     */
    @ApiModelProperty(value = "会员等级id")
    private Long vipLevelId;

    /**
     * 会员等级名称
     */
    @ApiModelProperty(value = "会员等级名称")
    private String vipLevelName;

    /**
     * 操作类型 操作类型 1-储值 2-消费 3-退款 4-退货
     * @see com.xyy.saas.member.core.enums.PrepayCardOperateTypeEnum
     */
    @ApiModelProperty(value = "操作类型 操作类型 1-储值 2-消费 3-退款 4-退货")
    private Byte operateType;

    /**
     * 实充金额变动
     */
    @ApiModelProperty(value = "实充金额变动")
    private BigDecimal changeAmount;

    /**
     * 赠送金额变动
     */
    @ApiModelProperty(value = "赠送金额变动")
    private BigDecimal changeBonus;

    /**
     * 变动总额
     */
    @ApiModelProperty(value = "变动总额")
    private BigDecimal changeTotalAmount;

    /**
     * 变动前的实充金额
     */
    @ApiModelProperty(value = "变动前的实充金额")
    private BigDecimal originAmount;

    /**
     * 变动后的实充金额
     */
    @ApiModelProperty(value = "变动后的实充金额")
    private BigDecimal originBonus;

    /**
     * 变动后的实充金额
     */
    @ApiModelProperty(value = "变动后的实充金额")
    private BigDecimal currentAmount;

    /**
     * 变动后的赠送金额
     */
    @ApiModelProperty(value = "变动后的赠送金额")
    private BigDecimal currentBonus;

    /**
     * 账户余额
     */
    @ApiModelProperty(value = "账户余额")
    private BigDecimal currentTotalAmount;

    /**
     * 订单id
     */
    @ApiModelProperty(value = "订单id")
    private Integer orderId;

    /**
     * 小票号
     */
    @ApiModelProperty(value = "小票号")
    private String ticketNo;

    /**
     * 支付类型 1-现金 2-微信 3-支付宝 4-银联 9-其他
     * @see com.xyy.saas.member.core.enums.PayTypeEnum
     */
    @ApiModelProperty(value = "支付类型 1-现金 2-微信 3-支付宝 4-银联 9-其他")
    private Byte payType;

    /**
     * 退款金额
     */
    @ApiModelProperty(value = "退款金额")
    private BigDecimal refund;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识")
    private String organsign;

    /**
     * 原因
     */
    @ApiModelProperty(value = "原因")
    private String reason;

    /**
     * 审核人
     */
    @ApiModelProperty(value = "审核人")
    private String checkUser;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMemberGuid() {
        return memberGuid;
    }

    public void setMemberGuid(String memberGuid) {
        this.memberGuid = memberGuid;
    }

    public String getCartNo() {
        return cartNo;
    }

    public void setCartNo(String cartNo) {
        this.cartNo = cartNo;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public Long getVipLevelId() {
        return vipLevelId;
    }

    public void setVipLevelId(Long vipLevelId) {
        this.vipLevelId = vipLevelId;
    }

    public String getVipLevelName() {
        return vipLevelName;
    }

    public void setVipLevelName(String vipLevelName) {
        this.vipLevelName = vipLevelName;
    }

    public Byte getOperateType() {
        return operateType;
    }

    public void setOperateType(Byte operateType) {
        this.operateType = operateType;
    }

    public BigDecimal getChangeAmount() {
        return changeAmount;
    }

    public void setChangeAmount(BigDecimal changeAmount) {
        this.changeAmount = changeAmount;
    }

    public BigDecimal getChangeBonus() {
        return changeBonus;
    }

    public void setChangeBonus(BigDecimal changeBonus) {
        this.changeBonus = changeBonus;
    }

    public BigDecimal getChangeTotalAmount() {
        return changeTotalAmount;
    }

    public void setChangeTotalAmount(BigDecimal changeTotalAmount) {
        this.changeTotalAmount = changeTotalAmount;
    }

    public BigDecimal getOriginAmount() {
        return originAmount;
    }

    public void setOriginAmount(BigDecimal originAmount) {
        this.originAmount = originAmount;
    }

    public BigDecimal getOriginBonus() {
        return originBonus;
    }

    public void setOriginBonus(BigDecimal originBonus) {
        this.originBonus = originBonus;
    }

    public BigDecimal getCurrentAmount() {
        return currentAmount;
    }

    public void setCurrentAmount(BigDecimal currentAmount) {
        this.currentAmount = currentAmount;
    }

    public BigDecimal getCurrentBonus() {
        return currentBonus;
    }

    public void setCurrentBonus(BigDecimal currentBonus) {
        this.currentBonus = currentBonus;
    }

    public BigDecimal getCurrentTotalAmount() {
        return currentTotalAmount;
    }

    public void setCurrentTotalAmount(BigDecimal currentTotalAmount) {
        this.currentTotalAmount = currentTotalAmount;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getTicketNo() {
        return ticketNo;
    }

    public void setTicketNo(String ticketNo) {
        this.ticketNo = ticketNo;
    }

    public Byte getPayType() {
        return payType;
    }

    public void setPayType(Byte payType) {
        this.payType = payType;
    }

    public BigDecimal getRefund() {
        return refund;
    }

    public void setRefund(BigDecimal refund) {
        this.refund = refund;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getOrgansign() {
        return organsign;
    }

    public void setOrgansign(String organsign) {
        this.organsign = organsign;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getCheckUser() {
        return checkUser;
    }

    public void setCheckUser(String checkUser) {
        this.checkUser = checkUser;
    }

    @Override
    public String toString() {
        return "MemberPrepayCardHistoryDto{" +
                "id=" + id +
                ", memberGuid='" + memberGuid + '\'' +
                ", cartNo='" + cartNo + '\'' +
                ", memberName='" + memberName + '\'' +
                ", telephone='" + telephone + '\'' +
                ", vipLevelId=" + vipLevelId +
                ", vipLevelName='" + vipLevelName + '\'' +
                ", operateType=" + operateType +
                ", changeAmount=" + changeAmount +
                ", changeBonus=" + changeBonus +
                ", changeTotalAmount=" + changeTotalAmount +
                ", originAmount=" + originAmount +
                ", originBonus=" + originBonus +
                ", currentAmount=" + currentAmount +
                ", currentBonus=" + currentBonus +
                ", currentTotalAmount=" + currentTotalAmount +
                ", orderId=" + orderId +
                ", ticketNo='" + ticketNo + '\'' +
                ", payType=" + payType +
                ", refund=" + refund +
                ", createTime=" + createTime +
                ", createUser='" + createUser + '\'' +
                ", organsign='" + organsign + '\'' +
                ", reason='" + reason + '\'' +
                ", checkUser='" + checkUser + '\'' +
                '}';
    }
}