package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * InventoryLotNumberVo
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-28T11:40:30.801+08:00")

public class InventoryLotNumberVo   {
  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("pref")
  private String pref = null;

  @JsonProperty("productPref")
  private String productPref = null;

  @JsonProperty("stockNumber")
  private BigDecimal stockNumber = null;

  @JsonProperty("stockAmount")
  private BigDecimal stockAmount = null;

  @JsonProperty("costPrice")
  private BigDecimal costPrice = null;

  @JsonProperty("lastCostPrice")
  private BigDecimal lastCostPrice = null;

  @JsonProperty("lastProvidePref")
  private String lastProvidePref = null;

  @JsonProperty("lastInTime")
  private Date lastInTime = null;

  @JsonProperty("lotNumber")
  private String lotNumber = null;

  @JsonProperty("status")
  private Integer status = null;

  @JsonProperty("expirationDate")
  private Date expirationDate = null;

  @JsonProperty("producedDate")
  private Date producedDate = null;

  @JsonProperty("createUser")
  private String createUser = null;

  @JsonProperty("createTime")
  private Date createTime = null;

  @JsonProperty("updateUser")
  private String updateUser = null;

  @JsonProperty("updateTime")
  private Date updateTime = null;

  @JsonProperty("remark")
  private String remark = null;

  @JsonProperty("version")
  private Integer version = null;

  @JsonProperty("yn")
  private byte yn = 1;

  @JsonProperty("baseVersion")
  private String baseVersion = null;

  @JsonProperty("organsign")
  private String organsign = null;

  @JsonProperty("productName")
  private String productName = null;

  @JsonProperty("attributeSpecification")
  private String attributeSpecification = null;

  @JsonProperty("packgeUnit")
  private String packgeUnit = null;

  @JsonProperty("manufacturer")
  private String manufacturer = null;

  @JsonProperty("positionname")
  private String positionname = null;

  @JsonProperty("positionid")
  private Integer positionid = null;

  @JsonProperty("productStartDate")
  private String productStartDate = null;

  @JsonProperty("productEndDate")
  private String productEndDate = null;

  @JsonProperty("providerName")
  private String providerName = null;

  public InventoryLotNumberVo id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * 主键id
   * @return id
  **/
  @ApiModelProperty(value = "主键id")


  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public InventoryLotNumberVo pref(String pref) {
    this.pref = pref;
    return this;
  }

   /**
   * 编号
   * @return pref
  **/
  @ApiModelProperty(value = "编号")


  public String getPref() {
    return pref;
  }

  public void setPref(String pref) {
    this.pref = pref;
  }

  public InventoryLotNumberVo productPref(String productPref) {
    this.productPref = productPref;
    return this;
  }

   /**
   * 商品编号
   * @return productPref
  **/
  @ApiModelProperty(value = "商品编号")


  public String getProductPref() {
    return productPref;
  }

  public void setProductPref(String productPref) {
    this.productPref = productPref;
  }

  public InventoryLotNumberVo stockNumber(BigDecimal stockNumber) {
    this.stockNumber = stockNumber;
    return this;
  }

   /**
   * 库存数量
   * @return stockNumber
  **/
  @ApiModelProperty(value = "库存数量")

  @Valid

  public BigDecimal getStockNumber() {
    return stockNumber;
  }

  public void setStockNumber(BigDecimal stockNumber) {
    this.stockNumber = stockNumber;
  }

  public InventoryLotNumberVo stockAmount(BigDecimal stockAmount) {
    this.stockAmount = stockAmount;
    return this;
  }

   /**
   * 库存金额
   * @return stockAmount
  **/
  @ApiModelProperty(value = "库存金额")

  @Valid

  public BigDecimal getStockAmount() {
    return stockAmount;
  }

  public void setStockAmount(BigDecimal stockAmount) {
    this.stockAmount = stockAmount;
  }

  public InventoryLotNumberVo costPrice(BigDecimal costPrice) {
    this.costPrice = costPrice;
    return this;
  }

   /**
   * 成本价
   * @return costPrice
  **/
  @ApiModelProperty(value = "成本价")

  @Valid

  public BigDecimal getCostPrice() {
    return costPrice;
  }

  public void setCostPrice(BigDecimal costPrice) {
    this.costPrice = costPrice;
  }

  public InventoryLotNumberVo lastCostPrice(BigDecimal lastCostPrice) {
    this.lastCostPrice = lastCostPrice;
    return this;
  }

   /**
   * 最后一次成本价
   * @return lastCostPrice
  **/
  @ApiModelProperty(value = "最后一次成本价")

  @Valid

  public BigDecimal getLastCostPrice() {
    return lastCostPrice;
  }

  public void setLastCostPrice(BigDecimal lastCostPrice) {
    this.lastCostPrice = lastCostPrice;
  }

  public InventoryLotNumberVo lastProvidePref(String lastProvidePref) {
    this.lastProvidePref = lastProvidePref;
    return this;
  }

   /**
   * 最后供应商
   * @return lastProvidePref
  **/
  @ApiModelProperty(value = "最后供应商")


  public String getLastProvidePref() {
    return lastProvidePref;
  }

  public void setLastProvidePref(String lastProvidePref) {
    this.lastProvidePref = lastProvidePref;
  }

  public InventoryLotNumberVo lastInTime(Date lastInTime) {
    this.lastInTime = lastInTime;
    return this;
  }

   /**
   * 最后一次入库时间
   * @return lastInTime
  **/
  @ApiModelProperty(value = "最后一次入库时间")

  @Valid

  public Date getLastInTime() {
    return lastInTime;
  }

  public void setLastInTime(Date lastInTime) {
    this.lastInTime = lastInTime;
  }

  public InventoryLotNumberVo lotNumber(String lotNumber) {
    this.lotNumber = lotNumber;
    return this;
  }

   /**
   * 批号
   * @return lotNumber
  **/
  @ApiModelProperty(value = "批号")


  public String getLotNumber() {
    return lotNumber;
  }

  public void setLotNumber(String lotNumber) {
    this.lotNumber = lotNumber;
  }

  public InventoryLotNumberVo status(Integer status) {
    this.status = status;
    return this;
  }

   /**
   * 状态，1：合格; 0:不合格
   * @return status
  **/
  @ApiModelProperty(value = "状态，1：合格; 0:不合格")


  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public InventoryLotNumberVo expirationDate(Date expirationDate) {
    this.expirationDate = expirationDate;
    return this;
  }

   /**
   * 到期日期
   * @return expirationDate
  **/
  @ApiModelProperty(value = "到期日期")

  @Valid

  public Date getExpirationDate() {
    return expirationDate;
  }

  public void setExpirationDate(Date expirationDate) {
    this.expirationDate = expirationDate;
  }

  public InventoryLotNumberVo producedDate(Date producedDate) {
    this.producedDate = producedDate;
    return this;
  }

   /**
   * 生产日期
   * @return producedDate
  **/
  @ApiModelProperty(value = "生产日期")

  @Valid

  public Date getProducedDate() {
    return producedDate;
  }

  public void setProducedDate(Date producedDate) {
    this.producedDate = producedDate;
  }

  public InventoryLotNumberVo createUser(String createUser) {
    this.createUser = createUser;
    return this;
  }

   /**
   * 创建人
   * @return createUser
  **/
  @ApiModelProperty(value = "创建人")


  public String getCreateUser() {
    return createUser;
  }

  public void setCreateUser(String createUser) {
    this.createUser = createUser;
  }

  public InventoryLotNumberVo createTime(Date createTime) {
    this.createTime = createTime;
    return this;
  }

   /**
   * 创建时间
   * @return createTime
  **/
  @ApiModelProperty(value = "创建时间")

  @Valid

  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public InventoryLotNumberVo updateUser(String updateUser) {
    this.updateUser = updateUser;
    return this;
  }

   /**
   * 更新人
   * @return updateUser
  **/
  @ApiModelProperty(value = "更新人")


  public String getUpdateUser() {
    return updateUser;
  }

  public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
  }

  public InventoryLotNumberVo updateTime(Date updateTime) {
    this.updateTime = updateTime;
    return this;
  }

   /**
   * 更新时间
   * @return updateTime
  **/
  @ApiModelProperty(value = "更新时间")

  @Valid

  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public InventoryLotNumberVo remark(String remark) {
    this.remark = remark;
    return this;
  }

   /**
   * 更新人
   * @return remark
  **/
  @ApiModelProperty(value = "更新人")


  public String getRemark() {
    return remark;
  }

  public void setRemark(String remark) {
    this.remark = remark;
  }

  public InventoryLotNumberVo version(Integer version) {
    this.version = version;
    return this;
  }

   /**
   * 更新人
   * @return version
  **/
  @ApiModelProperty(value = "更新人")


  public Integer getVersion() {
    return version;
  }

  public void setVersion(Integer version) {
    this.version = version;
  }

  public InventoryLotNumberVo yn(byte yn) {
    this.yn = yn;
    return this;
  }

   /**
   * 更新人
   * @return yn
  **/
  @ApiModelProperty(value = "更新人")

  public byte getYn() {
    return yn;
  }

  public void setYn(byte yn) {
    this.yn = yn;
  }

  public InventoryLotNumberVo baseVersion(String baseVersion) {
    this.baseVersion = baseVersion;
    return this;
  }

   /**
   * 版本号
   * @return baseVersion
  **/
  @ApiModelProperty(value = "版本号")


  public String getBaseVersion() {
    return baseVersion;
  }

  public void setBaseVersion(String baseVersion) {
    this.baseVersion = baseVersion;
  }

  public InventoryLotNumberVo organsign(String organsign) {
    this.organsign = organsign;
    return this;
  }

   /**
   * 机构代码
   * @return organsign
  **/
  @ApiModelProperty(value = "机构代码")


  public String getOrgansign() {
    return organsign;
  }

  public void setOrgansign(String organsign) {
    this.organsign = organsign;
  }

  public InventoryLotNumberVo productName(String productName) {
    this.productName = productName;
    return this;
  }

   /**
   * 商品名称
   * @return productName
  **/
  @ApiModelProperty(value = "商品名称")


  public String getProductName() {
    return productName;
  }

  public void setProductName(String productName) {
    this.productName = productName;
  }

  public InventoryLotNumberVo attributeSpecification(String attributeSpecification) {
    this.attributeSpecification = attributeSpecification;
    return this;
  }

   /**
   * 商品规格
   * @return attributeSpecification
  **/
  @ApiModelProperty(value = "商品规格")


  public String getAttributeSpecification() {
    return attributeSpecification;
  }

  public void setAttributeSpecification(String attributeSpecification) {
    this.attributeSpecification = attributeSpecification;
  }

  public InventoryLotNumberVo packgeUnit(String packgeUnit) {
    this.packgeUnit = packgeUnit;
    return this;
  }

   /**
   * 包装单位
   * @return packgeUnit
  **/
  @ApiModelProperty(value = "包装单位")


  public String getPackgeUnit() {
    return packgeUnit;
  }

  public void setPackgeUnit(String packgeUnit) {
    this.packgeUnit = packgeUnit;
  }

  public InventoryLotNumberVo manufacturer(String manufacturer) {
    this.manufacturer = manufacturer;
    return this;
  }

   /**
   * 生产厂家
   * @return manufacturer
  **/
  @ApiModelProperty(value = "生产厂家")


  public String getManufacturer() {
    return manufacturer;
  }

  public void setManufacturer(String manufacturer) {
    this.manufacturer = manufacturer;
  }

  public InventoryLotNumberVo positionname(String positionname) {
    this.positionname = positionname;
    return this;
  }

   /**
   * 架位名称
   * @return positionname
  **/
  @ApiModelProperty(value = "架位名称")


  public String getPositionname() {
    return positionname;
  }

  public void setPositionname(String positionname) {
    this.positionname = positionname;
  }

  public InventoryLotNumberVo positionid(Integer positionid) {
    this.positionid = positionid;
    return this;
  }

   /**
   * 架位ID
   * @return positionid
  **/
  @ApiModelProperty(value = "架位ID")


  public Integer getPositionid() {
    return positionid;
  }

  public void setPositionid(Integer positionid) {
    this.positionid = positionid;
  }

  public InventoryLotNumberVo productStartDate(String productStartDate) {
    this.productStartDate = productStartDate;
    return this;
  }

   /**
   * 生产日期(冗余字段，用于双击批号查询页面赋值)
   * @return productStartDate
  **/
  @ApiModelProperty(value = "生产日期(冗余字段，用于双击批号查询页面赋值)")


  public String getProductStartDate() {
    return productStartDate;
  }

  public void setProductStartDate(String productStartDate) {
    this.productStartDate = productStartDate;
  }

  public InventoryLotNumberVo productEndDate(String productEndDate) {
    this.productEndDate = productEndDate;
    return this;
  }

   /**
   * 有效期至(冗余字段，用于双击批号查询页面赋值)
   * @return productEndDate
  **/
  @ApiModelProperty(value = "有效期至(冗余字段，用于双击批号查询页面赋值)")


  public String getProductEndDate() {
    return productEndDate;
  }

  public void setProductEndDate(String productEndDate) {
    this.productEndDate = productEndDate;
  }

  public InventoryLotNumberVo providerName(String providerName) {
    this.providerName = providerName;
    return this;
  }

   /**
   * 供应商名称
   * @return providerName
  **/
  @ApiModelProperty(value = "供应商名称")


  public String getProviderName() {
    return providerName;
  }

  public void setProviderName(String providerName) {
    this.providerName = providerName;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InventoryLotNumberVo inventoryLotNumberVo = (InventoryLotNumberVo) o;
    return Objects.equals(this.id, inventoryLotNumberVo.id) &&
        Objects.equals(this.pref, inventoryLotNumberVo.pref) &&
        Objects.equals(this.productPref, inventoryLotNumberVo.productPref) &&
        Objects.equals(this.stockNumber, inventoryLotNumberVo.stockNumber) &&
        Objects.equals(this.stockAmount, inventoryLotNumberVo.stockAmount) &&
        Objects.equals(this.costPrice, inventoryLotNumberVo.costPrice) &&
        Objects.equals(this.lastCostPrice, inventoryLotNumberVo.lastCostPrice) &&
        Objects.equals(this.lastProvidePref, inventoryLotNumberVo.lastProvidePref) &&
        Objects.equals(this.lastInTime, inventoryLotNumberVo.lastInTime) &&
        Objects.equals(this.lotNumber, inventoryLotNumberVo.lotNumber) &&
        Objects.equals(this.status, inventoryLotNumberVo.status) &&
        Objects.equals(this.expirationDate, inventoryLotNumberVo.expirationDate) &&
        Objects.equals(this.producedDate, inventoryLotNumberVo.producedDate) &&
        Objects.equals(this.createUser, inventoryLotNumberVo.createUser) &&
        Objects.equals(this.createTime, inventoryLotNumberVo.createTime) &&
        Objects.equals(this.updateUser, inventoryLotNumberVo.updateUser) &&
        Objects.equals(this.updateTime, inventoryLotNumberVo.updateTime) &&
        Objects.equals(this.remark, inventoryLotNumberVo.remark) &&
        Objects.equals(this.version, inventoryLotNumberVo.version) &&
        Objects.equals(this.yn, inventoryLotNumberVo.yn) &&
        Objects.equals(this.baseVersion, inventoryLotNumberVo.baseVersion) &&
        Objects.equals(this.organsign, inventoryLotNumberVo.organsign) &&
        Objects.equals(this.productName, inventoryLotNumberVo.productName) &&
        Objects.equals(this.attributeSpecification, inventoryLotNumberVo.attributeSpecification) &&
        Objects.equals(this.packgeUnit, inventoryLotNumberVo.packgeUnit) &&
        Objects.equals(this.manufacturer, inventoryLotNumberVo.manufacturer) &&
        Objects.equals(this.positionname, inventoryLotNumberVo.positionname) &&
        Objects.equals(this.positionid, inventoryLotNumberVo.positionid) &&
        Objects.equals(this.productStartDate, inventoryLotNumberVo.productStartDate) &&
        Objects.equals(this.productEndDate, inventoryLotNumberVo.productEndDate) &&
        Objects.equals(this.providerName, inventoryLotNumberVo.providerName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, pref, productPref, stockNumber, stockAmount, costPrice, lastCostPrice, lastProvidePref, lastInTime, lotNumber, status, expirationDate, producedDate, createUser, createTime, updateUser, updateTime, remark, version, yn, baseVersion, organsign, productName, attributeSpecification, packgeUnit, manufacturer, positionname, positionid, productStartDate, productEndDate, providerName);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InventoryLotNumberVo {\n");

    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    pref: ").append(toIndentedString(pref)).append("\n");
    sb.append("    productPref: ").append(toIndentedString(productPref)).append("\n");
    sb.append("    stockNumber: ").append(toIndentedString(stockNumber)).append("\n");
    sb.append("    stockAmount: ").append(toIndentedString(stockAmount)).append("\n");
    sb.append("    costPrice: ").append(toIndentedString(costPrice)).append("\n");
    sb.append("    lastCostPrice: ").append(toIndentedString(lastCostPrice)).append("\n");
    sb.append("    lastProvidePref: ").append(toIndentedString(lastProvidePref)).append("\n");
    sb.append("    lastInTime: ").append(toIndentedString(lastInTime)).append("\n");
    sb.append("    lotNumber: ").append(toIndentedString(lotNumber)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    expirationDate: ").append(toIndentedString(expirationDate)).append("\n");
    sb.append("    producedDate: ").append(toIndentedString(producedDate)).append("\n");
    sb.append("    createUser: ").append(toIndentedString(createUser)).append("\n");
    sb.append("    createTime: ").append(toIndentedString(createTime)).append("\n");
    sb.append("    updateUser: ").append(toIndentedString(updateUser)).append("\n");
    sb.append("    updateTime: ").append(toIndentedString(updateTime)).append("\n");
    sb.append("    remark: ").append(toIndentedString(remark)).append("\n");
    sb.append("    version: ").append(toIndentedString(version)).append("\n");
    sb.append("    yn: ").append(toIndentedString(yn)).append("\n");
    sb.append("    baseVersion: ").append(toIndentedString(baseVersion)).append("\n");
    sb.append("    organsign: ").append(toIndentedString(organsign)).append("\n");
    sb.append("    productName: ").append(toIndentedString(productName)).append("\n");
    sb.append("    attributeSpecification: ").append(toIndentedString(attributeSpecification)).append("\n");
    sb.append("    packgeUnit: ").append(toIndentedString(packgeUnit)).append("\n");
    sb.append("    manufacturer: ").append(toIndentedString(manufacturer)).append("\n");
    sb.append("    positionname: ").append(toIndentedString(positionname)).append("\n");
    sb.append("    positionid: ").append(toIndentedString(positionid)).append("\n");
    sb.append("    productStartDate: ").append(toIndentedString(productStartDate)).append("\n");
    sb.append("    productEndDate: ").append(toIndentedString(productEndDate)).append("\n");
    sb.append("    providerName: ").append(toIndentedString(providerName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

