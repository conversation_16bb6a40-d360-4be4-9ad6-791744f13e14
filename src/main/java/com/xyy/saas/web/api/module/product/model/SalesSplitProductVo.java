package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 销售拆零商品信息实体
 */
@ApiModel(description = "销售拆零商品信息实体")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T15:59:22.655+08:00")

public class SalesSplitProductVo   {
  @JsonProperty("cleckTime")
  private String cleckTime = null;

  @JsonProperty("cleckUser")
  private String cleckUser = null;

  @JsonProperty("cleckUserId")
  private Integer cleckUserId = null;

  @JsonProperty("organSign")
  private String organSign = null;

  @JsonProperty("salesData")
  private List<ProSalesScatteredVo> salesData = null;

  public SalesSplitProductVo cleckTime(String cleckTime) {
    this.cleckTime = cleckTime;
    return this;
  }

   /**
   * 开票时间
   * @return cleckTime
  **/
  @ApiModelProperty(value = "开票时间")


  public String getCleckTime() {
    return cleckTime;
  }

  public void setCleckTime(String cleckTime) {
    this.cleckTime = cleckTime;
  }

  public SalesSplitProductVo cleckUser(String cleckUser) {
    this.cleckUser = cleckUser;
    return this;
  }

   /**
   * 开票员
   * @return cleckUser
  **/
  @ApiModelProperty(value = "开票员")


  public String getCleckUser() {
    return cleckUser;
  }

  public void setCleckUser(String cleckUser) {
    this.cleckUser = cleckUser;
  }

  public SalesSplitProductVo cleckUserId(Integer cleckUserId) {
    this.cleckUserId = cleckUserId;
    return this;
  }

   /**
   * 开票员ID
   * @return cleckUserId
  **/
  @ApiModelProperty(value = "开票员ID")


  public Integer getCleckUserId() {
    return cleckUserId;
  }

  public void setCleckUserId(Integer cleckUserId) {
    this.cleckUserId = cleckUserId;
  }

  public SalesSplitProductVo organSign(String organSign) {
    this.organSign = organSign;
    return this;
  }

   /**
   * 药店标识
   * @return organSign
  **/
  @ApiModelProperty(value = "药店标识")


  public String getOrganSign() {
    return organSign;
  }

  public void setOrganSign(String organSign) {
    this.organSign = organSign;
  }

  public SalesSplitProductVo salesData(List<ProSalesScatteredVo> salesData) {
    this.salesData = salesData;
    return this;
  }

  public SalesSplitProductVo addSalesDataItem(ProSalesScatteredVo salesDataItem) {
    if (this.salesData == null) {
      this.salesData = new ArrayList<ProSalesScatteredVo>();
    }
    this.salesData.add(salesDataItem);
    return this;
  }

   /**
   * Get salesData
   * @return salesData
  **/
  @ApiModelProperty(value = "")

  @Valid

  public List<ProSalesScatteredVo> getSalesData() {
    return salesData;
  }

  public void setSalesData(List<ProSalesScatteredVo> salesData) {
    this.salesData = salesData;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SalesSplitProductVo salesSplitProductVo = (SalesSplitProductVo) o;
    return Objects.equals(this.cleckTime, salesSplitProductVo.cleckTime) &&
        Objects.equals(this.cleckUser, salesSplitProductVo.cleckUser) &&
        Objects.equals(this.cleckUserId, salesSplitProductVo.cleckUserId) &&
        Objects.equals(this.organSign, salesSplitProductVo.organSign) &&
        Objects.equals(this.salesData, salesSplitProductVo.salesData);
  }

  @Override
  public int hashCode() {
    return Objects.hash(cleckTime, cleckUser, cleckUserId, organSign, salesData);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SalesSplitProductVo {\n");

    sb.append("    cleckTime: ").append(toIndentedString(cleckTime)).append("\n");
    sb.append("    cleckUser: ").append(toIndentedString(cleckUser)).append("\n");
    sb.append("    cleckUserId: ").append(toIndentedString(cleckUserId)).append("\n");
    sb.append("    organSign: ").append(toIndentedString(organSign)).append("\n");
    sb.append("    salesData: ").append(toIndentedString(salesData)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

