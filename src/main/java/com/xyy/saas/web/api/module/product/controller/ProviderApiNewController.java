package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.util.ResultVO;

import com.xyy.saas.supplier.api.SupplierBasicInfoApi;
import com.xyy.saas.supplier.dto.ProviderDto;
import com.xyy.saas.supplier.dto.ProviderVoDto;
import com.xyy.saas.web.api.module.product.model.SaasProviderBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;


@Controller
public class ProviderApiNewController implements ProviderNewApi {

	@Reference(version = "0.0.1")
	private SupplierBasicInfoApi supplierBasicInfoApi;

	private static final Logger logger = LoggerFactory.getLogger(ProviderApiNewController.class);

	@Override
	public ResultVO<Object> save(@ApiParam(value="供应商信息",required=true) @RequestBody SaasProviderBaseInfo provider) {
		ProviderVoDto providerDto = new ProviderVoDto();
		BeanUtils.copyProperties(provider, providerDto);
		supplierBasicInfoApi.saveBaseInfo(providerDto);
		return new ResultVO<Object>(null);
	}

	@Override
	public ResultVO<ProviderDto> syncDataForXYY(HttpServletRequest request) {
		logger.info("请求小药药供应商的同步接口starting");
        String organSign = request.getHeader("organSign");
        Map<String, Object> map = new HashMap<>();
	    map.put("organSign", organSign);
        ProviderDto providerDto = supplierBasicInfoApi.syncDataForXYY(map);
		return new ResultVO<>(providerDto);
	}

	@Override
	public ResultVO initXYYApprove() {
		supplierBasicInfoApi.initXYYProviderApprove();
		return ResultVO.createSuccess(null);
	}

	@Override
	public ResultVO updateXYYApproveExtMess() {
		supplierBasicInfoApi.updateXYYApproveExtMess();
		return ResultVO.createSuccess(null);
	}
}
