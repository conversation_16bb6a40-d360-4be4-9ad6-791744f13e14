package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * @ClassName CostAdjustsByPrefResultDetailVo
 * @Description 成本调价获取详情detail查询结果类
 * <AUTHOR>
 * @Date 2020/8/24 14:37
 * @Version 1.0
 **/
@ApiModel(description = "成本调价获取详情detail查询结果类")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")
public class CostAdjustsByPrefResultDetailVo {

    @JsonProperty("productPref")
    private String productPref;//商品编号:内码，用于系统交互，不用于展示
    @JsonProperty("pharmacyPref")
    private String pharmacyPref;//商品编号：外码，用于展示，不用于系统交互
    @JsonProperty("commonName")
    private String commonName;//通用名
    @JsonProperty("productName")
    private String productName;//商品名称
    @JsonProperty("attributeSpecification")
    private String attributeSpecification;//规格型号
    @JsonProperty("standardLibraryId")
    private Long standardLibraryId;//标准库id
    @JsonProperty("approvalNumber")
    private String approvalNumber;//批准文号
    @JsonProperty("manufacturer")
    private String manufacturer;//生产厂商
    @JsonProperty("producingArea")
    private String producingArea;//产地
    @JsonProperty("dosageFormName")
    private String dosageFormName;//剂型
    @JsonProperty("unitName")
    private String unitName;//单位
    @JsonProperty("newCostPrice")
    private String newCostPrice;//新成本价(来源于神农接口)
    @JsonProperty("oldCostPrice")
    private String oldCostPrice;//原成本价
    @JsonProperty("adjustmentRatio")
    private String adjustmentRatio;//调价比例
    @JsonProperty("priceUpper")
    private String priceUpper;//价格上限
    @JsonProperty("priceLower")
    private String priceLower;//价格下限
    @JsonProperty("mixedQuery")
    private String mixedQuery;//商品信息
    @JsonProperty("sixLevels")
    private String sixLevels;//六级分类
    @JsonProperty("isEnquiryId")
    private Byte isEnquiryId;//是否可要货id
    @JsonProperty("isEnquiryName")
    private String isEnquiryName;//是否可要货name
    @JsonProperty("systemType")
    private Integer systemType;
    @JsonProperty("lastPurchaseSupplier")
    private String lastPurchaseSupplier;

    public Integer getSid() {
        return sid;
    }

    public void setSid(Integer sid) {
        this.sid = sid;
    }

    @JsonProperty("sid")
    private Integer sid;//前端渲染页面唯一id

    public Byte getControlSalesYn() {
        return controlSalesYn;
    }

    public void setControlSalesYn(Byte controlSalesYn) {
        this.controlSalesYn = controlSalesYn;
    }

    public String getControlSalesYnName() {
        return controlSalesYnName;
    }

    public void setControlSalesYnName(String controlSalesYnName) {
        this.controlSalesYnName = controlSalesYnName;
    }

    private Byte controlSalesYn;

    public Integer getSystemType() {
        return systemType;
    }

    public void setSystemType(Integer systemType) {
        this.systemType = systemType;
    }

    public String getSystemTypeName() {
        return systemTypeName;
    }

    public void setSystemTypeName(String systemTypeName) {
        this.systemTypeName = systemTypeName;
    }

    private String controlSalesYnName;
    private String systemTypeName;

    @ApiModelProperty(value = "商品取值范围：1：按比例，2按出库价取值")
    public Byte getValueType() {
        return valueType;
    }

    public void setValueType(Byte valueType) {
        this.valueType = valueType;
    }

    @JsonProperty("valueType")
    private Byte valueType;//商品取值范围：1：按比例，2按出库价取值

    @ApiModelProperty(value = "商品取值范围：1：按比例，2按出库价取值")
    public String getValueTypeName() {
        return valueTypeName;
    }

    public void setValueTypeName(String valueTypeName) {
        this.valueTypeName = valueTypeName;
    }

    @JsonProperty("valueTypeName")
    private String valueTypeName;//商品取值范围：1：按比例，2按出库价取值

    public String getPriceUpper() {
        return priceUpper;
    }

    public void setPriceUpper(String priceUpper) {
        this.priceUpper = priceUpper;
    }

    public String getPriceLower() {
        return priceLower;
    }

    public void setPriceLower(String priceLower) {
        this.priceLower = priceLower;
    }

    @ApiModelProperty(value = "采购含税价")
    public String getTaxCost() {
        return taxCost;
    }

    public void setTaxCost(String taxCost) {
        this.taxCost = taxCost;
    }

    @JsonProperty("taxCost")
    private String taxCost;//采购含税价

    @ApiModelProperty(value = "商品编号:内码，用于系统交互，不用于展示")
    public String getProductPref() {
        return productPref;
    }

    public void setProductPref(String productPref) {
        this.productPref = productPref;
    }

    @ApiModelProperty(value = "商品编号：外码，用于展示，不用于系统交互")
    public String getPharmacyPref() {
        return pharmacyPref;
    }

    public void setPharmacyPref(String pharmacyPref) {
        this.pharmacyPref = pharmacyPref;
    }

    @ApiModelProperty(value = "通用名")
    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    @ApiModelProperty(value = "商品名称")
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    @ApiModelProperty(value = "规格型号")
    public String getAttributeSpecification() {
        return attributeSpecification;
    }

    public void setAttributeSpecification(String attributeSpecification) {
        this.attributeSpecification = attributeSpecification;
    }

    @ApiModelProperty(value = "标准库id")
    public Long getStandardLibraryId() {
        return standardLibraryId;
    }

    public void setStandardLibraryId(Long standardLibraryId) {
        this.standardLibraryId = standardLibraryId;
    }

    @ApiModelProperty(value = "批准文号")
    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    @ApiModelProperty(value = "生产厂商")
    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    @ApiModelProperty(value = "产地")
    public String getProducingArea() {
        return producingArea;
    }

    public void setProducingArea(String producingArea) {
        this.producingArea = producingArea;
    }

    @ApiModelProperty(value = "剂型")
    public String getDosageFormName() {
        return dosageFormName;
    }

    public void setDosageFormName(String dosageFormName) {
        this.dosageFormName = dosageFormName;
    }

    @ApiModelProperty(value = "单位")
    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    @ApiModelProperty(value = "新成本价")
    public String getNewCostPrice() {
        return newCostPrice;
    }

    public void setNewCostPrice(String newCostPrice) {
        this.newCostPrice = newCostPrice;
    }

    @ApiModelProperty(value = "原成本价")
    public String getOldCostPrice() {
        return oldCostPrice;
    }

    public void setOldCostPrice(String oldCostPrice) {
        this.oldCostPrice = oldCostPrice;
    }

    @ApiModelProperty(value = "调价比例")
    public String getAdjustmentRatio() {
        return adjustmentRatio;
    }

    public void setAdjustmentRatio(String adjustmentRatio) {
        this.adjustmentRatio = adjustmentRatio;
    }

    public String getMixedQuery() {
        return mixedQuery;
    }

    public void setMixedQuery(String mixedQuery) {
        this.mixedQuery = mixedQuery;
    }

    public String getSixLevels() {
        return sixLevels;
    }

    public void setSixLevels(String sixLevels) {
        this.sixLevels = sixLevels;
    }

    public Byte getIsEnquiryId() {
        return isEnquiryId;
    }

    public void setIsEnquiryId(Byte isEnquiryId) {
        this.isEnquiryId = isEnquiryId;
    }

    public String getIsEnquiryName() {
        return isEnquiryName;
    }

    public void setIsEnquiryName(String isEnquiryName) {
        this.isEnquiryName = isEnquiryName;
    }

    public String getLastPurchaseSupplier() {
        return lastPurchaseSupplier;
    }

    public void setLastPurchaseSupplier(String lastPurchaseSupplier) {
        this.lastPurchaseSupplier = lastPurchaseSupplier;
    }
}
