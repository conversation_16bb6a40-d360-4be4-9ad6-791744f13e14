package com.xyy.saas.web.api.module.external.controller.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.external.core.api.ProductExternalApi;
import com.xyy.saas.external.core.dto.ProductExternalDto;
import com.xyy.saas.product.core.dto.ProductDto;
import com.xyy.saas.web.api.module.external.controller.ProductExternalApiSwag;
import com.xyy.saas.web.api.module.external.model.ProductExternal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Classname ProductExternalApiController
 * @Description 商品对外提供API
 * @Date 2020/5/14 10:00
 */
@Controller
@Slf4j
public class ProductExternalApiController implements ProductExternalApiSwag {

    @Reference(version = "0.0.1")
    private ProductExternalApi productExternalApi;

    @Override
    public ResponseEntity<Integer> updateProduct(@Valid @NotNull @RequestBody ProductExternal productExternal) {
        ProductExternalDto productExternalDto = new ProductExternalDto();
        BeanUtils.copyProperties(productExternal,productExternalDto);
        Integer result = productExternalApi.updateProduct(productExternalDto);
        return new ResponseEntity<Integer>(result, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> findStandLibrarysByOrganSign(@Valid @NotNull @RequestBody ProductDto productDto) {
        ResultVO<PageInfo<ProductDto>> standLibrarysByOrganSign = productExternalApi.findStandLibrarysByOrganSign(productDto);
        return new ResponseEntity<ResultVO>(standLibrarysByOrganSign, HttpStatus.OK);
    }
}
