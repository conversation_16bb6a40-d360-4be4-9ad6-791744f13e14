package com.xyy.saas.web.api.module.supplier.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(description = "质量变更列表传参")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-07T16:41:52.348+08:00")
public class SupplierUpdatemsg implements Serializable {
    @ApiModelProperty(value = "字段名称")
    private String fieldName;// 字段名称
    @ApiModelProperty(value = "字段对应中文名称")
    private String fieldValue; // 字段对应中文名称
    @ApiModelProperty(value = "变更后图片地址")
    private String changeImageurl; // 变更后图片地址
    @ApiModelProperty(value = "类型 0 文字 1 图片")
    private Integer type;// 类型 0 文字 1 图片
    @ApiModelProperty(value = "变更前id  例如 规格/型号 、单位")
    private String updateSrc; // 变更前id  例如 规格/型号 、单位
    @ApiModelProperty(value = "变更前名称")
    private String updateSrcName; // 变更前名称
    @ApiModelProperty(value = "变更后id 例如 规格/型号 、单位")
    private String updateDest; // 变更后id 例如 规格/型号 、单位
    @ApiModelProperty(value = "变更后名称")
    private String updateDestName; // 变更后名称

    private String addStr;
    private String desStr;
    private String text;
    private String salePersonIndex;
    private Integer addPicNum;
    private Integer desPicNum;
    public String getAddStr() {
        return addStr;
    }

    public void setAddStr(String addStr) {
        this.addStr = addStr;
    }

    public String getDesStr() {
        return desStr;
    }

    public void setDesStr(String desStr) {
        this.desStr = desStr;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getSalePersonIndex() {
        return salePersonIndex;
    }

    public void setSalePersonIndex(String salePersonIndex) {
        this.salePersonIndex = salePersonIndex;
    }

    public Integer getAddPicNum() {
        return addPicNum;
    }

    public void setAddPicNum(Integer addPicNum) {
        this.addPicNum = addPicNum;
    }

    public Integer getDesPicNum() {
        return desPicNum;
    }

    public void setDesPicNum(Integer desPicNum) {
        this.desPicNum = desPicNum;
    }


    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getFieldValue() {
        return fieldValue;
    }

    public void setFieldValue(String fieldValue) {
        this.fieldValue = fieldValue;
    }

    public String getUpdateSrc() {
        return updateSrc;
    }

    public void setUpdateSrc(String updateSrc) {
        this.updateSrc = updateSrc;
    }

    public String getUpdateSrcName() {
        return updateSrcName;
    }

    public void setUpdateSrcName(String updateSrcName) {
        this.updateSrcName = updateSrcName;
    }

    public String getUpdateDest() {
        return updateDest;
    }

    public void setUpdateDest(String updateDest) {
        this.updateDest = updateDest;
    }

    public String getUpdateDestName() {
        return updateDestName;
    }

    public void setUpdateDestName(String updateDestName) {
        this.updateDestName = updateDestName;
    }

    public String getChangeImageurl() {
        return changeImageurl;
    }

    public void setChangeImageurl(String changeImageurl) {
        this.changeImageurl = changeImageurl;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
