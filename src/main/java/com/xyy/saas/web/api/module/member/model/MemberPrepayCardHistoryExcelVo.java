package com.xyy.saas.web.api.module.member.model;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 会员储值卡流水导出ExcelVo
 * <AUTHOR>
 */
public class MemberPrepayCardHistoryExcelVo implements Serializable {


    private static final long serialVersionUID = 5858665791460127266L;

    /**
     * 会员卡号
     */
    private String cartNo;

    /**
     * 会员姓名
     */
    private String memberName;

    /**
     * 手机号
     */
    private String telephone;

    /**
     * 会员等级名称
     */
    private String vipLevelName;

    /**
     * 操作类型 1-储值 2-消费 3-退款 4-退货
     * @see com.xyy.saas.member.core.enums.PrepayCardOperateTypeEnum
     */
    private String operateType;

    /**
     * 实充金额变动
     */
    private BigDecimal changeAmount;

    /**
     * 赠送金额变动
     */
    private BigDecimal changeBonus;

    /**
     * 变动金额
     */
    private BigDecimal changeTotalAmount;

    /**
     * 储值卡实充余额
     */
    private BigDecimal currentAmount;

    /**
     * 储值卡赠送余额
     */
    private BigDecimal currentBonus;

    /**
     * 账户余额
     */
    private BigDecimal totalAmount;

    /**
     * 小票号
     */
    private String ticketNo;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 审核人
     */
    private String checkUser;

    /**
     * 原因
     */
    private String reason;

    public String getCartNo() {
        return cartNo;
    }

    public void setCartNo(String cartNo) {
        this.cartNo = cartNo;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getVipLevelName() {
        return vipLevelName;
    }

    public void setVipLevelName(String vipLevelName) {
        this.vipLevelName = vipLevelName;
    }

    public String getOperateType() {
        return operateType;
    }

    public void setOperateType(String operateType) {
        this.operateType = operateType;
    }

    public BigDecimal getChangeAmount() {
        return changeAmount;
    }

    public void setChangeAmount(BigDecimal changeAmount) {
        this.changeAmount = changeAmount;
    }

    public BigDecimal getChangeBonus() {
        return changeBonus;
    }

    public void setChangeBonus(BigDecimal changeBonus) {
        this.changeBonus = changeBonus;
    }

    public BigDecimal getChangeTotalAmount() {
        return changeTotalAmount;
    }

    public void setChangeTotalAmount(BigDecimal changeTotalAmount) {
        this.changeTotalAmount = changeTotalAmount;
    }

    public BigDecimal getCurrentAmount() {
        return currentAmount;
    }

    public void setCurrentAmount(BigDecimal currentAmount) {
        this.currentAmount = currentAmount;
    }

    public BigDecimal getCurrentBonus() {
        return currentBonus;
    }

    public void setCurrentBonus(BigDecimal currentBonus) {
        this.currentBonus = currentBonus;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getTicketNo() {
        return ticketNo;
    }

    public void setTicketNo(String ticketNo) {
        this.ticketNo = ticketNo;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCheckUser() {
        return checkUser;
    }

    public void setCheckUser(String checkUser) {
        this.checkUser = checkUser;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    @Override
    public String toString() {
        return "MemberPrepayCardHistoryExcelVo{" +
                "cartNo='" + cartNo + '\'' +
                ", memberName='" + memberName + '\'' +
                ", telephone='" + telephone + '\'' +
                ", vipLevelName='" + vipLevelName + '\'' +
                ", operateType='" + operateType + '\'' +
                ", changeAmount=" + changeAmount +
                ", changeBonus=" + changeBonus +
                ", changeTotalAmount=" + changeTotalAmount +
                ", currentAmount=" + currentAmount +
                ", currentBonus=" + currentBonus +
                ", totalAmount=" + totalAmount +
                ", ticketNo='" + ticketNo + '\'' +
                ", createTime='" + createTime + '\'' +
                ", createUser='" + createUser + '\'' +
                ", checkUser='" + checkUser + '\'' +
                ", reason='" + reason + '\'' +
                '}';
    }
}