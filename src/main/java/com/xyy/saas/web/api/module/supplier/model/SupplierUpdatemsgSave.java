package com.xyy.saas.web.api.module.supplier.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

@ApiModel(description = "质量变更列表传参")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-07T16:41:52.348+08:00")
public class SupplierUpdatemsgSave implements Serializable {
    @ApiModelProperty(value = "商品编号")
    private String pref;
    @ApiModelProperty(value = "变更类型 1:供应商 0：商品")
    private Integer updateType;
    @ApiModelProperty(value = "申请时间")
    private String applyTime;
    private String commonName;

    private Object updateContent;

    @ApiModelProperty(value = "变更信息")
    @JsonProperty("list")
    List<SupplierUpdatemsg> list;

    public Object getUpdateContent() {
        return updateContent;
    }

    public void setUpdateContent(Object updateContent) {
        this.updateContent = updateContent;
    }

    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    public Integer getUpdateType() {
        return updateType;
    }

    public void setUpdateType(Integer updateType) {
        this.updateType = updateType;
    }

    public String getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(String applyTime) {
        this.applyTime = applyTime;
    }

    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    public List<SupplierUpdatemsg> getList() {

        return list;
    }

    public void setList(List<SupplierUpdatemsg> list) {
        this.list = list;
    }
}
