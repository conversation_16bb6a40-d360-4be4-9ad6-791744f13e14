package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName UpdateRedisVo
 * @Description 刷新redis的key值接口
 * <AUTHOR>
 * @Date 2019/9/11 20:32
 * @Version 1.0
 **/
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T17:19:18.933+08:00")

@ApiModel(description = "刷新商品redis的key值接口请求信息对象")
public class UpdateRedisVo {

    @JsonProperty("organSign")
    private String organSign;

    @JsonProperty("keyValue")
    private String keyValue;

    @ApiModelProperty(value = "机构号")
    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    @ApiModelProperty(value = "商品表外码设置的最大key值,需要设置到redis里")
    public String getKeyValue() {
        return keyValue;
    }

    public void setKeyValue(String keyValue) {
        this.keyValue = keyValue;
    }
}
