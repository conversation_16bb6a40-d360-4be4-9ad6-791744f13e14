package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.web.api.module.product.model.SaasPositionVo;
import io.swagger.annotations.ApiParam;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-30T11:33:52.138+08:00")

@Controller
public class FindPositionVosApiController implements FindPositionVosApi {



    public ResponseEntity<SaasPositionVo> findPositionVos(@ApiParam(value = "架位Vo" ,required=true )  @Valid @RequestBody SaasPositionVo saasPositionVo) {
        // do some magic!
        return new ResponseEntity<SaasPositionVo>(HttpStatus.OK);
    }

}
