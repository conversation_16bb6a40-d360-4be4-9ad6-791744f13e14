package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName ComputeCostPriceBatchDetailVo
 * @Description 后端计算器批量单条信息封装对象
 * <AUTHOR>
 * @Date 2020/8/19 17:22
 * @Version 1.0
 **/
@ApiModel(description = "后端计算器批量单条信息封装对象")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")
public class ComputeCostPriceBatchDetailVo extends ComputeCostPriceVo{

    @JsonProperty("productPref")
    private String productPref;//商品编号:商品内码

    @ApiModelProperty(value = "前端页面渲染唯一id")
    public Integer getSid() {
        return sid;
    }

    public void setSid(Integer sid) {
        this.sid = sid;
    }

    @JsonProperty("sid")
    private Integer sid;//前端页面渲染唯一id

    @ApiModelProperty(value = "商品编号:商品内码")
    public String getProductPref() {
        return productPref;
    }

    public void setProductPref(String productPref) {
        this.productPref = productPref;
    }
}
