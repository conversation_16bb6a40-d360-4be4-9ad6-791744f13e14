package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.jm.common.result.ResultVO;
import com.xyy.saas.web.api.module.product.model.ProductStockModel;
import io.swagger.annotations.*;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 联营库存查询接口迁移
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-01-28T15:28:18.933+08:00")
@Api(tags = {"商品库存服务-调拨单API"}, description = "JoinOperationApi", produces = MediaType.APPLICATION_JSON_VALUE)
@RestController
@RequestMapping("/product/allocate")
public interface JoinOperationApi {

    @ApiOperation(value = "商品库存信息查询", notes = "商品库存信息查询", response = ResultVO.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping(value = "/queryProductInventory")
    public ResultVO queryProductInventory(HttpServletRequest request,
                                          @ApiParam(required = true, value = "商品库存信息") @RequestBody ProductStockModel productStockModel);

    @ApiOperation(value = "商品批号库存信息查询", notes = "商品批号库存信息查询", response = ResultVO.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping(value = "/queryProductLotInventory")
    public ResultVO queryProductLotInventory(HttpServletRequest request,
                                             @ApiParam(required = true, value = "商品库存信息") @RequestBody ProductStockModel productStockModel);


    @ApiOperation(value = "联营体商品品类和商品总货值", notes = "联营体商品品类和商品总货值", response = ResultVO.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping(value = "/allStoresProductKindsAndValue")
    public ResultVO allStoresProductKindsAndValue(@RequestHeader("commonRequestModel") String commonRequestModel,
                                                  @ApiParam(required = true, value = "商品库存信息") @RequestBody ProductStockModel productStockModel);


    @ApiOperation(value = "架位查询", notes = "架位查询", response = ResultVO.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping(value = "/queryPositions")
    public ResultVO queryPositions(HttpServletRequest request,
                                   @ApiParam(required = true, value = "商品库存信息") @RequestBody ProductStockModel productStockModel);

}