package com.xyy.saas.web.api.module.member.controller;


import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.member.core.api.MemberPreferentialDayForZBApi;
import com.xyy.saas.member.core.dto.MemberPreferentialDayDto;
import com.xyy.saas.member.core.enums.LogicallyDeletedEnum;
import io.swagger.annotations.*;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/member/memberPreferentialDay/zb")
@Api(value = "memberPreferentialDay（总部端）API", description = "会员日（总部端）API")
public class MemberPreferentialDayForZBApiController {


    //@Reference(version = "123", url="dubbo://localhost:20848")
    @Reference(version = "0.0.1")
    private MemberPreferentialDayForZBApi memberPreferentialDayForZBApi;

    @ApiOperation(value = "总部端:会员日列表", notes = "总部端:会员日列表  \n" +
            "错误码:  \n" +
            "code: -99 参数不正确", response = Boolean.class, tags = {"memberPreferentialDay",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/query", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> query(@RequestHeader("commonRequestModel") String commonRequestModel,
                                   @RequestBody MemberPreferentialDayDto memberPreferentialDayDto) {
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        //是否查询全部门店
        if (StringUtils.isEmpty(memberPreferentialDayDto.getOrgansign())) {
            memberPreferentialDayDto.setOrgansign(model.getOrganSign());
        }
        memberPreferentialDayDto.setYn((byte) LogicallyDeletedEnum.EFFECTIVE.getKey());
        //是否是模板 1是 0否
        memberPreferentialDayDto.setIsTemplete((byte) 0);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(memberPreferentialDayDto.getPage() == null ? 1 : memberPreferentialDayDto.getPage());
        pageInfo.setPageSize(memberPreferentialDayDto.getRows() == null ? 50 : memberPreferentialDayDto.getRows());
        PageInfo<MemberPreferentialDayDto> pageList = memberPreferentialDayForZBApi.query(pageInfo, memberPreferentialDayDto);
        return new ResponseEntity(ResultVO.createSuccess(pageList), HttpStatus.OK);
    }

    @ApiOperation(value = "总部端:获取会员日设置", notes = "总部端:获取会员日设置  \n" +
            "错误码:  \n" +
            "code: -99 参数不正确", response = MemberPreferentialDayDto.class, tags = {"memberPreferentialDay",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberPreferentialDayDto.class)})
    @RequestMapping(value = "/get", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> get(@RequestBody MemberPreferentialDayDto memberPreferentialDayDto) {
        if ((StringUtils.isEmpty(memberPreferentialDayDto.getId()) || memberPreferentialDayDto.getId() < 0)
                && (StringUtils.isEmpty(memberPreferentialDayDto.getMemberLevelId()) || memberPreferentialDayDto.getMemberLevelId() < 0)) {
            return new ResponseEntity(new ResultVO(-1, "参数id和memberLevelId不能同时为空", false), HttpStatus.OK);
        }
        if (StringUtils.isEmpty(memberPreferentialDayDto.getOrgansign())) {
            return new ResponseEntity(new ResultVO(-1, "参数机构号不能为空", false), HttpStatus.OK);
        }
        MemberPreferentialDayDto dto = memberPreferentialDayForZBApi.get(memberPreferentialDayDto);
        return new ResponseEntity(ResultVO.createSuccess(dto), HttpStatus.OK);
    }

}
