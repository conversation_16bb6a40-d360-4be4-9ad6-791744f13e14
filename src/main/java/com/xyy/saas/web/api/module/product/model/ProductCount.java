package com.xyy.saas.web.api.module.product.model;

import io.swagger.annotations.ApiModelProperty;

/**
 * app 首页商品统计
 * <AUTHOR>
 */
public class ProductCount {

    private Integer adequateCount;

    private Integer shortageCount;

    private Integer nearlyCount;

    @ApiModelProperty(value = "在库商品数")
    public Integer getAdequateCount() {
        return adequateCount;
    }

    public void setAdequateCount(Integer adequateCount) {
        this.adequateCount = adequateCount;
    }

    @ApiModelProperty(value = "缺货商品数")
    public Integer getShortageCount() {
        return shortageCount;
    }

    public void setShortageCount(Integer shortageCount) {
        this.shortageCount = shortageCount;
    }


    @ApiModelProperty(value = "效期商品数")
    public Integer getNearlyCount() {
        return nearlyCount;
    }

    public void setNearlyCount(Integer nearlyCount) {
        this.nearlyCount = nearlyCount;
    }
}
