package com.xyy.saas.web.api.module.finance.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.api.SystemDictApi;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.inventory.search.inventory.search.api.PageResult;
import com.xyy.saas.inventory.search.inventory.search.api.common.param.ProductParam;
import com.xyy.saas.inventory.search.inventory.search.api.common.result.Product;
import com.xyy.saas.inventory.search.inventory.search.api.productstock.ProductStockQueryApi;
import com.xyy.saas.inventory.search.inventory.search.api.productstock.ProductStockQueryRequest;
import com.xyy.saas.inventory.search.inventory.search.api.productstock.ProductStockQueryResult;
import com.xyy.saas.product.core.api.AdjustPriceApi;
import com.xyy.saas.product.core.dto.PriceAdjustDto;
import com.xyy.saas.product.core.dto.ProPriceAdjustDto;
import com.xyy.saas.product.core.dto.ProductVoDto;
import com.xyy.saas.web.api.common.base.AbstractCommonController;
import com.xyy.saas.web.api.module.finance.model.ProPriceAdjustQueryVo;
import com.xyy.saas.web.api.module.finance.model.ProPriceAdjustVo;
import com.xyy.saas.web.api.module.finance.model.ProductQueryCustomVo;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.dto.result.EmployeeDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 扫码调整价格
 * copy form saas-gateway-web ProductSalesPriceAdjustController.java
 * <AUTHOR>
 * @date 2021/6/22 15:42
 */
@Controller
@RequestMapping("/product/finance/adjust")
@Slf4j
public class ProductSalesPriceAdjustController extends AbstractCommonController {
    @Reference(version = "0.0.1")
    private AdjustPriceApi adjustPriceApi;
    @Reference(version = "0.0.1")
    private EmployeeApi employeeApi;
    @Reference(version = "0.0.1")
    private SystemDictApi systemDictApi;
    @Reference(version = "1.0.0")
    private ProductStockQueryApi productStockQueryApi;

    /**
     * 提交售价修改
     * 示例:{"token":"eyJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2MjQ0MTczMzcsImtleSI6InVzZXI6bG9naW46MTE4MzYyOlpITDAwMDA2NjE1OjE2MjQ0MTczMzc3NjUifQ.lRkbfiMPe67TDEO-URHM1WMHMSyx_SzDZDI8oxYkUWc","sign":"2DA6F932FA9BB501D3063E417D0F1070","data":"{\"adjustId\":\"\",\"pref\":\"\",\"remark\":\"\",\"userId\":\"118362\",\"starttime\":\"2021-06-23\",\"details\":[{\"id\":\"-1\",\"productId\":\"95359156\",\"productPref\":\"ZHL20346778\",\"oldPrice\":\"24\",\"newPrice\":\"23\",\"oldMemberPrice\":\"18\",\"newMemberPrice\":\"16\",\"remark\":\"\",\"pharmacyPref\":\"ZHL2\",\"ykqPrice\":0}]}","client":"web"}
     * userId 当前用户id, starttime 申请日期，
     * details里面的字段productId 商品id,productPref 商品编码(内码), "pharmacyPref":商品外码, oldPrice 原零售价， newprice新零售价，oldMemberPrice旧会员价， newMemberPrice新会员价，"remark"备注, "ykqPrice":0这个就填写0吧
     * <AUTHOR>
     * @date 2021/6/22 15:41
     * @param priceAdjustDto
     */
    @ResponseBody
    @RequestMapping("/submit")
    public ResultVO toSubmit(@RequestBody PriceAdjustDto priceAdjustDto){
        log.info("toSubmit 入参param={}", JSONUtils.obj2JSON(priceAdjustDto));
        priceAdjustDto.setOrganSign(getCurrentOrganSign());
        priceAdjustDto.setUsername(String.valueOf(priceAdjustDto.getUserId()));
        // 售价变动接口
        ResultVO resultVO = this.adjustPriceApi.submit(priceAdjustDto);
        return resultVO;
    }

    //@ResponseBody
    //@RequestMapping("/query")
    //public ResultVO<PageInfo<PriceAdjustDto>> query(Integer rows, Integer page, PriceAdjustDto priceAdjustDto){
    //    priceAdjustDto.setRows(rows==null?10:rows);
    //    priceAdjustDto.setPage(page==null?1:page);
    //    priceAdjustDto.setOrganSign(getCurrentOrganSign());
    //    List<String> organSigns = new ArrayList<>();
    //    organSigns.add(getCurrentOrganSign());
    //    priceAdjustDto.setOrganSigns(organSigns);
    //    ResultVO<PageInfo<PriceAdjustDto>> resutVO = this.adjustPriceApi.list(priceAdjustDto);
    //    return resutVO;
    //}

    @Value("${onoff.queryProductListIsUseOld:false}")
    private boolean queryProductListIsUseOld;// TODO-lichunpeng 开关是否使用旧接口 2021/4/8-17:17

    /**
     * 查询商品列表，支持按名称过滤(支持barCode查询)
     * 示例:{pageNum:1,pageSize:50,name:""}
     * <AUTHOR>
     * @date 2021/6/22 16:26
     * @param productQueryCustomVo
     */
    @ResponseBody
    @RequestMapping("/queryProducts")
    public ResultVO<PageInfo<ProductVoDto>> queryProductList(@RequestBody ProductQueryCustomVo productQueryCustomVo) {
        log.info("queryProductList 入参param={}", JSONUtils.obj2JSON(productQueryCustomVo));
        Integer pageNum = productQueryCustomVo.getPageNum();
        Integer pageSize = productQueryCustomVo.getPageSize();
        String name = productQueryCustomVo.getName();

        Byte isProductHidden = Byte.valueOf(getCurrentEmployeeIdentity());
        String currentOrganSign = getCurrentOrganSign();

        PageInfo info = new PageInfo();
        info.setPageNum(pageNum == null ? 1 : pageNum);
        info.setPageSize(pageSize == null ? 10 : pageSize);
        // 售价变动接口
        PageInfo<ProductVoDto> result;
        if (queryProductListIsUseOld) {
            result = this.adjustPriceApi.queryProductList(name, info, isProductHidden, currentOrganSign);
        } else {
            ProductParam productParam = new ProductParam();
            productParam.setProductQuery(name);
            productParam.setIsHidden(isProductHidden.intValue());
            productParam.setOrganSign(currentOrganSign);

            ProductStockQueryRequest productStockQueryRequest = new ProductStockQueryRequest();
            productStockQueryRequest.setOrganSign(currentOrganSign);
            productStockQueryRequest.setProductParam(productParam);
            productStockQueryRequest.setPage(info.getPageNum());
            productStockQueryRequest.setSize(info.getPageSize());

            PageResult<ProductStockQueryResult> productStockQueryResultPageResult = productStockQueryApi.queryByDb(productStockQueryRequest);
            List<ProductVoDto> orpvs = productStockQueryResultPageResult.getList().stream()
                .filter(Objects::nonNull).map(productStockQueryResult -> {
                    Product product = productStockQueryResult.getProduct();
                    ProductVoDto productVoDto = new ProductVoDto();
                    if (product != null) {
                        BeanUtils.copyProperties(product, productVoDto);
                        productVoDto.setStockNum(productStockQueryResult.getStockNumber());
                        productVoDto.setStockAmount(productStockQueryResult.getStockAmount());
                        productVoDto.setProductId(product.getId());
                    }
                    return productVoDto;
                }).collect(Collectors.toList());

            result = new PageInfo<>();
            result.setPageNum(productStockQueryResultPageResult.getPageNum());
            result.setPageSize(productStockQueryResultPageResult.getPageSize());
            result.setList(orpvs);
            result.setTotal(productStockQueryResultPageResult.getTotal());
            result.setPages(productStockQueryResultPageResult.getPages());
        }
        return ResultVO.createSuccess(result);
    }

    /**
     * 售价变动明细(调价记录列表)
     * 示例:{pageNum:1,pageSize:50,starttime:2021-06-01,endtime:2021-06-16,pref:,username:,proname:}
     * 单据编号perf  商品proname  申请人username  开始日期starttime    结束日期endtime  下面查询字段放入json中
     * {"token":"eyJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2MjQ0MTczMzcsImtleSI6InVzZXI6bG9naW46MTE4MzYyOlpITDAwMDA2NjE1OjE2MjQ0MTczMzc3NjUifQ.lRkbfiMPe67TDEO-URHM1WMHMSyx_SzDZDI8oxYkUWc","sign":"3F399F24883D8B3DCC3A5601609D2AE6","data":"{\"pref\":\"\",\"pageSize\":50,\"endtime\":\"2021-06-23\",\"proname\":\"\",\"starttime\":\"2021-06-01\",\"pageNum\":1,\"username\":\"\"}","client":"web"}
     * 返回值列表中实体示例:{"id":19104,"productId":95359156,"productPref":"ZHL20346778","productName":"阿胶糕","attributeSpecification":"60ml","unitId":212,"retailPrice":24,"vipPrice":18,"manufacturer":"山东东阿鲁润阿胶制品有限公司","approvalNumber":"QS3715 2801 1302","producingArea":"","newPrice":23,"newMemberPrice":16,"pref":"STZ7927","createTime":"2021-06-23","remark":"","createUser":"yzl","pharmacyPref":"ZHL2"}"
     * 单据编号pref,生效日期createTime,商品编号pharmacyPref，通用名productName，包装单位unitId212(需要翻译)[取已新增的unitName],规格attributeSpecification，生产厂家manufacturer，
     * 原零售价retailPrice，新零售价newPrice，原会员价vipPrice，新会员价newMemberPrice，createUser申请人，remark备注
     * <AUTHOR>
     * @date 2021/6/22 16:27
     * @param proPriceAdjustQueryVo
     */
    @ResponseBody
    @RequestMapping("/queryAdjustDetailsList")
    public ResultVO<PageInfo> salesPriceAdjustDetails(@RequestBody ProPriceAdjustQueryVo proPriceAdjustQueryVo){
        log.info("salesPriceAdjustDetails 入参param={}", JSONUtils.obj2JSON(proPriceAdjustQueryVo));
        Integer pageNum = proPriceAdjustQueryVo.getPageNum();
        Integer pageSize = proPriceAdjustQueryVo.getPageSize();
        String currentEmployeeIdentity = getCurrentEmployeeIdentity();
        Byte isProductHidden = Byte.valueOf(StringUtils.isBlank(currentEmployeeIdentity)?"0":currentEmployeeIdentity);
        PriceAdjustDto priceAdjustDto = new PriceAdjustDto();
        BeanUtils.copyProperties(proPriceAdjustQueryVo, priceAdjustDto);
        if(isProductHidden!=null){
            priceAdjustDto.setIsProductHidden(isProductHidden);
        }
        priceAdjustDto.setPage(pageNum == null ? 1 : pageNum);
        priceAdjustDto.setRows(pageSize == null ? 10 : pageSize);
        priceAdjustDto.setOrganSign(getCurrentOrganSign());
        // 售价变动接口
        ResultVO<PageInfo> result = this.adjustPriceApi.queryAdjustDetailsList(priceAdjustDto);
        if(result != null && result.getResult() != null && result.getResult().getList() != null) {
            List<ProPriceAdjustDto> dtoList = result.getResult().getList();
            List<ProPriceAdjustVo> voList = new ArrayList<ProPriceAdjustVo>();
            for (ProPriceAdjustDto proPriceAdjustDto : dtoList) {
                ProPriceAdjustVo proPriceAdjustVo = new ProPriceAdjustVo();
                BeanUtils.copyProperties(proPriceAdjustDto, proPriceAdjustVo);
                voList.add(proPriceAdjustVo);
            }
            result.getResult().setList(voList);
        }
        return result;
    }

    private String getCurrentEmployeeIdentity() {
        String identity = null;
        EmployeeDto employeeDto = employeeApi.queryEmployeeById(getCurrentEmployeeId()).getResult();
        if(employeeDto != null) {
            identity = employeeDto.getIdentity()+"";
        }
        return identity;
    }

}
