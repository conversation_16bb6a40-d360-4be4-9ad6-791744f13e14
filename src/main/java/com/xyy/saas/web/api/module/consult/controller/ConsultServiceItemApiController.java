package com.xyy.saas.web.api.module.consult.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.consult.cores.api.ConsultServiceItemApi;
import com.xyy.saas.consult.cores.dto.ConsultServiceItemDto;
import com.xyy.saas.web.api.module.consult.model.ConsultServiceItemVo;
import io.swagger.annotations.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/member/consultServiceItem")
@Api(value = "consultServiceItem", description = "远程问诊服务购买项目API")
public class ConsultServiceItemApiController {

	private static final Logger logger = LogManager.getLogger(ConsultServiceItemApiController.class);
    @Reference( version = "0.0.2")
    private ConsultServiceItemApi consultServiceItemApi;


    @ApiOperation(value = "添加远程问诊服务购买项目", notes = "添加远程问诊服务购买项目", response = Boolean.class, tags = {"consultServiceItem",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/insert", method = RequestMethod.POST,produces =  "application/json",consumes = "application/json")
    ResponseEntity<ResultVO> insert(@ApiParam(value = "远程问诊服务购买项目", required = true) @RequestBody ConsultServiceItemVo consultServiceItemVo) {
        if (StringUtils.isEmpty(consultServiceItemVo.getName())) {
            return new ResponseEntity(new ResultVO(-1, "参数name不能为空", false), HttpStatus.OK);
        }
        if (consultServiceItemVo.getType() == null || consultServiceItemVo.getType() <= 0) {
            return new ResponseEntity(new ResultVO(-1, "参数type不能为空", false), HttpStatus.OK);
        }
        if (consultServiceItemVo.getType() == 1 && consultServiceItemVo.getCount() == null) {
            return new ResponseEntity(new ResultVO(-1, "[type = 1]参数count不能为空", false), HttpStatus.OK);
        }
        if (consultServiceItemVo.getType() == 2 && consultServiceItemVo.getDays() == null) {
            return new ResponseEntity(new ResultVO(-1, "[type = 2]参数days不能为空", false), HttpStatus.OK);
        }
        if (consultServiceItemVo.getPrice() == null || consultServiceItemVo.getPrice().doubleValue() <= 0) {
            return new ResponseEntity(new ResultVO(-1, "参数price必须为正数", false), HttpStatus.OK);
        }
        ConsultServiceItemDto dto = new ConsultServiceItemDto();
        BeanUtils.copyProperties(consultServiceItemVo, dto);
        return new ResponseEntity(ResultVO.createSuccess(consultServiceItemApi.insert(dto)), HttpStatus.OK);
    }

    @ApiOperation(value = "根据主键查询远程问诊服务购买项目", notes = "根据主键查询远程问诊服务购买项目", response = ConsultServiceItemDto.class, tags = {"consultServiceItem",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ConsultServiceItemDto.class)})
    @RequestMapping(value = "/selectById", method = RequestMethod.POST,produces =  "application/json",consumes = "application/json")
    ResponseEntity<ResultVO> selectById(@ApiParam(value = "主键", required = true) @RequestBody ConsultServiceItemVo consultServiceItemVo) {
        Long id = consultServiceItemVo.getId();
        if (id == null || id <= 0) {
            return new ResponseEntity(new ResultVO(-1, "参数id不能为空", false), HttpStatus.OK);
        }
        ConsultServiceItemDto dto = consultServiceItemApi.selectById(id);
        return new ResponseEntity(ResultVO.createSuccess(dto), HttpStatus.OK);
    }

    @ApiOperation(value = "禁用远程问诊服务购买项目", notes = "禁用远程问诊服务购买项目", response = Boolean.class, tags = {"consultServiceItem",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/disableById", method = RequestMethod.POST,produces =  "application/json",consumes = "application/json")
    ResponseEntity<ResultVO> disableById(@ApiParam(value = "主键", required = true) @RequestBody ConsultServiceItemVo consultServiceItemVo) {
        Long id = consultServiceItemVo.getId();
        if (id == null || id <= 0) {
            return new ResponseEntity(new ResultVO(-1, "参数id不能为空", false), HttpStatus.OK);
        }
        return new ResponseEntity(ResultVO.createSuccess(consultServiceItemApi.disableById(id)), HttpStatus.OK);
    }

    @ApiOperation(value = "启用远程问诊服务购买项目", notes = "启用远程问诊服务购买项目", response = Boolean.class, tags = {"consultServiceItem",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/enableById", method = RequestMethod.POST,produces =  "application/json",consumes = "application/json")
    ResponseEntity<ResultVO> enableById(@ApiParam(value = "主键", required = true) @RequestBody ConsultServiceItemVo consultServiceItemVo) {
        Long id = consultServiceItemVo.getId();
        if (id == null || id <= 0) {
            return new ResponseEntity(new ResultVO(-1, "参数id不能为空", false), HttpStatus.OK);
        }
        return new ResponseEntity(ResultVO.createSuccess(consultServiceItemApi.enableById(id)), HttpStatus.OK);
    }

    @ApiOperation(value = "根据查询条件查询远程问诊服务购买项目列表", notes = "根据查询条件查询远程问诊服务购买项目列表", response = ConsultServiceItemDto.class, tags = {"consultServiceItem",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ConsultServiceItemDto.class)})
    @RequestMapping(value = "/getEnableList", method = RequestMethod.POST,produces =  "application/json",consumes = "application/json")
    ResponseEntity<ResultVO> getEnableList() {
        return new ResponseEntity(ResultVO.createSuccess(consultServiceItemApi.getEnableList()), HttpStatus.OK);
    }

}
