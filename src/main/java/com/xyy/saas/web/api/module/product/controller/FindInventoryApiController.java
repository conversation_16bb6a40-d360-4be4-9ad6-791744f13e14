package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.inventory.core.api.InventoryLotNumberApi;
import com.xyy.saas.web.api.module.product.model.InventoryVo;
import io.swagger.annotations.ApiParam;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T18:13:39.358+08:00")

@Controller
public class FindInventoryApiController implements FindInventoryApi {
    @Reference(version = "0.0.1")
    private InventoryLotNumberApi inventoryLotNumberApi;


    public ResponseEntity<ResultVO> findInventory(@ApiParam(value = "库存Vo" ,required=true )  @Valid @RequestBody InventoryVo inventory) {
        // do some magic!
        return new ResponseEntity<ResultVO>(HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> updateInventoryNew() {

        String[] productPrefs = /*new String[]{"ZHL915437CL"};*/new String[]{"ZHL3924375","ZHL3924375","ZHL3924381","ZHL2947286","ZHL2952384","ZHL2951788","ZHL2948248","ZHL2952003","ZHL2949053","ZHL2948688","ZHL2952870",
        "ZHL2952870","ZHL2947170","ZHL2951816","ZHL2949132","ZHL2946946","ZHL2952113","ZHL2952024","ZHL2947268","ZHL2951390","ZHL2949104","ZHL2946834","ZHL3924393","ZHL2951932",
                "ZHL3924402","ZHL2952578","ZHL3924404","ZHL2947899","ZHL2951844"};
        String[] stockNumbers =/*new String[]{"100"};*/ new String[]{"150","50","15","2","3","5","2","10","20","10","14","36","5","10","2",
        "3","10","5","10","5","5","20","10","5","8","2","5","20","5"};
        String[] lotNumbers = /*new String[]{"1"};*/new String[]{"20180604","20180606","180609","20180903","80166161","180901","1704087","1180607",
        "180907","1809053","324170501","3241803134","180953","180701","8hg0264","gscyk010","2013994","180702","181211","20181001",
                "20181016","18080125","181122","ra18025a","18304025","20180505","1800118","181002","1803002"};
        String[] organSigns= /*new String[]{"ZHL00000265"};*/new String[]{"ZHL00000548","ZHL00000548","ZHL00000548","ZHL00000548",
                "ZHL00000548","ZHL00000548","ZHL00000548","ZHL00000548","ZHL00000548","ZHL00000548","ZHL00000548","ZHL00000548","ZHL00000548",
                "ZHL00000548","ZHL00000548","ZHL00000548","ZHL00000548","ZHL00000548","ZHL00000548","ZHL00000548",
                "ZHL00000548","ZHL00000548","ZHL00000548","ZHL00000548","ZHL00000548","ZHL00000548","ZHL00000548","ZHL00000548","ZHL00000548"};
        List<Map<String,Object>> params = new ArrayList<>();
        for(int i=0;i<productPrefs.length;i++){
            Map<String,Object> param = new HashMap<>();
            param.put("stockNumber", new BigDecimal(stockNumbers[i]));
            param.put("productPref",productPrefs[i]);
            param.put("lotNumber",lotNumbers[i]);
            param.put("organSign",organSigns[i]);
            params.add(param);
        }
        inventoryLotNumberApi.updateInventoryLotNumberNew(params);
        return new ResponseEntity<ResultVO>(HttpStatus.OK);
    }

}
