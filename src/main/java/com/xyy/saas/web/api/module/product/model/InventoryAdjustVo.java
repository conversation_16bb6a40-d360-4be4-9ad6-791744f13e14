package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import java.util.Date;
import java.util.Objects;

/**
 * InventoryAdjustVo
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-30T12:28:35.123+08:00")

public class InventoryAdjustVo   {
  @JsonProperty("id")
  private Integer id = null;

  @JsonProperty("pref")
  private String pref = null;

  @JsonProperty("oldProducedDate")
  private Date oldProducedDate = null;

  @JsonProperty("oldExpirationDate")
  private Date oldExpirationDate = null;

  @JsonProperty("updateUser")
  private String updateUser = null;

  @JsonProperty("updateTime")
  private Date updateTime = null;

  @JsonProperty("newProducedDate")
  private Date newProducedDate = null;

  @JsonProperty("newExpirationDate")
  private Date newExpirationDate = null;

  @JsonProperty("baseVersion")
  private String baseVersion = null;

  @JsonProperty("organsign")
  private String organsign = null;

  @JsonProperty("productPref")
  private String productPref = null;

  @JsonProperty("oldLotNumber")
  private String oldLotNumber = null;

  @JsonProperty("newLotNumber")
  private String newLotNumber = null;

  public InventoryAdjustVo id(Integer id) {
    this.id = id;
    return this;
  }

   /**
   * 主键id
   * @return id
  **/
  @ApiModelProperty(value = "主键id")


  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public InventoryAdjustVo pref(String pref) {
    this.pref = pref;
    return this;
  }

   /**
   * 编号
   * @return pref
  **/
  @ApiModelProperty(value = "编号")


  public String getPref() {
    return pref;
  }

  public void setPref(String pref) {
    this.pref = pref;
  }

  public InventoryAdjustVo oldProducedDate(Date oldProducedDate) {
    this.oldProducedDate = oldProducedDate;
    return this;
  }

   /**
   * 旧生产日期
   * @return oldProducedDate
  **/
  @ApiModelProperty(value = "旧生产日期")

  @Valid

  public Date getOldProducedDate() {
    return oldProducedDate;
  }

  public void setOldProducedDate(Date oldProducedDate) {
    this.oldProducedDate = oldProducedDate;
  }

  public InventoryAdjustVo oldExpirationDate(Date oldExpirationDate) {
    this.oldExpirationDate = oldExpirationDate;
    return this;
  }

   /**
   * 旧有效期至
   * @return oldExpirationDate
  **/
  @ApiModelProperty(value = "旧有效期至")

  @Valid

  public Date getOldExpirationDate() {
    return oldExpirationDate;
  }

  public void setOldExpirationDate(Date oldExpirationDate) {
    this.oldExpirationDate = oldExpirationDate;
  }

  public InventoryAdjustVo updateUser(String updateUser) {
    this.updateUser = updateUser;
    return this;
  }

   /**
   * 更新人
   * @return updateUser
  **/
  @ApiModelProperty(value = "更新人")


  public String getUpdateUser() {
    return updateUser;
  }

  public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
  }

  public InventoryAdjustVo updateTime(Date updateTime) {
    this.updateTime = updateTime;
    return this;
  }

   /**
   * 更新时间
   * @return updateTime
  **/
  @ApiModelProperty(value = "更新时间")

  @Valid

  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public InventoryAdjustVo newProducedDate(Date newProducedDate) {
    this.newProducedDate = newProducedDate;
    return this;
  }

   /**
   * 新生产日期
   * @return newProducedDate
  **/
  @ApiModelProperty(value = "新生产日期")

  @Valid

  public Date getNewProducedDate() {
    return newProducedDate;
  }

  public void setNewProducedDate(Date newProducedDate) {
    this.newProducedDate = newProducedDate;
  }

  public InventoryAdjustVo newExpirationDate(Date newExpirationDate) {
    this.newExpirationDate = newExpirationDate;
    return this;
  }

   /**
   * 新有效期至
   * @return newExpirationDate
  **/
  @ApiModelProperty(value = "新有效期至")

  @Valid

  public Date getNewExpirationDate() {
    return newExpirationDate;
  }

  public void setNewExpirationDate(Date newExpirationDate) {
    this.newExpirationDate = newExpirationDate;
  }

  public InventoryAdjustVo baseVersion(String baseVersion) {
    this.baseVersion = baseVersion;
    return this;
  }

   /**
   * 版本号
   * @return baseVersion
  **/
  @ApiModelProperty(value = "版本号")


  public String getBaseVersion() {
    return baseVersion;
  }

  public void setBaseVersion(String baseVersion) {
    this.baseVersion = baseVersion;
  }

  public InventoryAdjustVo organsign(String organsign) {
    this.organsign = organsign;
    return this;
  }

   /**
   * 机构代码
   * @return organsign
  **/
  @ApiModelProperty(value = "机构代码")


  public String getOrgansign() {
    return organsign;
  }

  public void setOrgansign(String organsign) {
    this.organsign = organsign;
  }

  public InventoryAdjustVo productPref(String productPref) {
    this.productPref = productPref;
    return this;
  }

   /**
   * 商品编号
   * @return productPref
  **/
  @ApiModelProperty(value = "商品编号")


  public String getProductPref() {
    return productPref;
  }

  public void setProductPref(String productPref) {
    this.productPref = productPref;
  }

  public InventoryAdjustVo oldLotNumber(String oldLotNumber) {
    this.oldLotNumber = oldLotNumber;
    return this;
  }

   /**
   * 旧批号
   * @return oldLotNumber
  **/
  @ApiModelProperty(value = "旧批号")


  public String getOldLotNumber() {
    return oldLotNumber;
  }

  public void setOldLotNumber(String oldLotNumber) {
    this.oldLotNumber = oldLotNumber;
  }

  public InventoryAdjustVo newLotNumber(String newLotNumber) {
    this.newLotNumber = newLotNumber;
    return this;
  }

   /**
   * 新批号
   * @return newLotNumber
  **/
  @ApiModelProperty(value = "新批号")


  public String getNewLotNumber() {
    return newLotNumber;
  }

  public void setNewLotNumber(String newLotNumber) {
    this.newLotNumber = newLotNumber;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InventoryAdjustVo inventoryAdjustVo = (InventoryAdjustVo) o;
    return Objects.equals(this.id, inventoryAdjustVo.id) &&
        Objects.equals(this.pref, inventoryAdjustVo.pref) &&
        Objects.equals(this.oldProducedDate, inventoryAdjustVo.oldProducedDate) &&
        Objects.equals(this.oldExpirationDate, inventoryAdjustVo.oldExpirationDate) &&
        Objects.equals(this.updateUser, inventoryAdjustVo.updateUser) &&
        Objects.equals(this.updateTime, inventoryAdjustVo.updateTime) &&
        Objects.equals(this.newProducedDate, inventoryAdjustVo.newProducedDate) &&
        Objects.equals(this.newExpirationDate, inventoryAdjustVo.newExpirationDate) &&
        Objects.equals(this.baseVersion, inventoryAdjustVo.baseVersion) &&
        Objects.equals(this.organsign, inventoryAdjustVo.organsign) &&
        Objects.equals(this.productPref, inventoryAdjustVo.productPref) &&
        Objects.equals(this.oldLotNumber, inventoryAdjustVo.oldLotNumber) &&
        Objects.equals(this.newLotNumber, inventoryAdjustVo.newLotNumber);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, pref, oldProducedDate, oldExpirationDate, updateUser, updateTime, newProducedDate, newExpirationDate, baseVersion, organsign, productPref, oldLotNumber, newLotNumber);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InventoryAdjustVo {\n");

    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    pref: ").append(toIndentedString(pref)).append("\n");
    sb.append("    oldProducedDate: ").append(toIndentedString(oldProducedDate)).append("\n");
    sb.append("    oldExpirationDate: ").append(toIndentedString(oldExpirationDate)).append("\n");
    sb.append("    updateUser: ").append(toIndentedString(updateUser)).append("\n");
    sb.append("    updateTime: ").append(toIndentedString(updateTime)).append("\n");
    sb.append("    newProducedDate: ").append(toIndentedString(newProducedDate)).append("\n");
    sb.append("    newExpirationDate: ").append(toIndentedString(newExpirationDate)).append("\n");
    sb.append("    baseVersion: ").append(toIndentedString(baseVersion)).append("\n");
    sb.append("    organsign: ").append(toIndentedString(organsign)).append("\n");
    sb.append("    productPref: ").append(toIndentedString(productPref)).append("\n");
    sb.append("    oldLotNumber: ").append(toIndentedString(oldLotNumber)).append("\n");
    sb.append("    newLotNumber: ").append(toIndentedString(newLotNumber)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

