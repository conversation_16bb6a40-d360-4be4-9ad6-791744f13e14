package com.xyy.saas.web.api.module.member.model;

import java.io.Serializable;
import java.math.BigDecimal;

public class MemberDetailExportExcelVO implements Serializable {

    /**
     * ids
     */
    private  String ids;
    /**
     * 混合查询
     */
    private String mixedQuery;

    /**
     * 手机号
     */
    private String telephone;

    /**
     * 卡号
     */
    private String cartNo;

    /**
     * 开始积分
     */
    private String startPoint;

    /**
     * 结束积分
     */
    private String endPoint;
    /**
     * 排序字段
     */
    private String sidx;

    /**
     * 排序方式 asc desc
     */
    private String sord;

    /**
     * 白名单
     */
    private Integer whiteList;

    /**
     * 会员Guid
     */
    private String memberGuid;

    /**
     * 会员等级id
     */
    private Long vipLevelId;

    /**
     * 开始时间
     */
    private String startCreateDate;

    /**
     * 结束时间
     */
    private String endCreateDate;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 发卡人
     */
    private String createUserName;

    /**
     * 机构号
     */
    private String organSign;

    /**
     * 开始余额
     */
    private BigDecimal minTotalAmount;

    /**
     * 结束余额
     */
    private BigDecimal maxTotalAmount;

    /**
     * 导出头
     */
    private String headers;

    /**
     * 导出头对应字段
     */
    private String  fieldNames;

    /**
     *  门店端：是否查询全部
     */
    private Boolean  flag;

    /**
     * 关心病种
     */
    private String chronicPref;

    public String getIds() {
        return ids;
    }

    public void setIds(String ids) {
        this.ids = ids;
    }

    public String getMixedQuery() {
        return mixedQuery;
    }

    public void setMixedQuery(String mixedQuery) {
        this.mixedQuery = mixedQuery;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getCartNo() {
        return cartNo;
    }

    public void setCartNo(String cartNo) {
        this.cartNo = cartNo;
    }

    public String getStartPoint() {
        return startPoint;
    }

    public void setStartPoint(String startPoint) {
        this.startPoint = startPoint;
    }

    public String getEndPoint() {
        return endPoint;
    }

    public void setEndPoint(String endPoint) {
        this.endPoint = endPoint;
    }

    public String getSidx() {
        return sidx;
    }

    public void setSidx(String sidx) {
        this.sidx = sidx;
    }

    public String getSord() {
        return sord;
    }

    public void setSord(String sord) {
        this.sord = sord;
    }

    public Integer getWhiteList() {
        return whiteList;
    }

    public void setWhiteList(Integer whiteList) {
        this.whiteList = whiteList;
    }

    public String getMemberGuid() {
        return memberGuid;
    }

    public void setMemberGuid(String memberGuid) {
        this.memberGuid = memberGuid;
    }

    public Long getVipLevelId() {
        return vipLevelId;
    }

    public void setVipLevelId(Long vipLevelId) {
        this.vipLevelId = vipLevelId;
    }

    public String getStartCreateDate() {
        return startCreateDate;
    }

    public void setStartCreateDate(String startCreateDate) {
        this.startCreateDate = startCreateDate;
    }

    public String getEndCreateDate() {
        return endCreateDate;
    }

    public void setEndCreateDate(String endCreateDate) {
        this.endCreateDate = endCreateDate;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public BigDecimal getMinTotalAmount() {
        return minTotalAmount;
    }

    public void setMinTotalAmount(BigDecimal minTotalAmount) {
        this.minTotalAmount = minTotalAmount;
    }

    public BigDecimal getMaxTotalAmount() {
        return maxTotalAmount;
    }

    public void setMaxTotalAmount(BigDecimal maxTotalAmount) {
        this.maxTotalAmount = maxTotalAmount;
    }

    public String getHeaders() {
        return headers;
    }

    public void setHeaders(String headers) {
        this.headers = headers;
    }

    public String getFieldNames() {
        return fieldNames;
    }

    public void setFieldNames(String fieldNames) {
        this.fieldNames = fieldNames;
    }

    public Boolean getFlag() {
        return flag;
    }

    public void setFlag(Boolean flag) {
        this.flag = flag;
    }

    public String getChronicPref() {
        return chronicPref;
    }

    public void setChronicPref(String chronicPref) {
        this.chronicPref = chronicPref;
    }
}
