package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.inventory.core.dto.InventoryVerifyVo;
import com.xyy.saas.product.core.dto.ResultCodeEnum;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-01-28T15:28:18.933+08:00")
@Controller
public class InventoryVerifyApiController implements InventoryVerifyApi {

    private static final Logger logger = LoggerFactory.getLogger(InventoryVerifyApiController.class);

    @Reference(version ="0.0.1")
    private com.xyy.saas.inventory.core.api.InventoryVerifyApi inventoryVerifyApi;

    @Override
    public ResponseEntity<ResultVO> saveInventoryVerify(@ApiParam(value = "盘点确认单信息" ,required=true )  @Valid @RequestBody InventoryVerifyVo vo) {

    	return new ResponseEntity<ResultVO>(new ResultVO(1,"APP版本过低，请在智慧脸官网下载最新版本",false), HttpStatus.OK);

//        if (StringUtils.isEmpty(vo.getInventoryType())){
//            logger.info("方法InventoryVerifyApiController->saveInventoryVerify错误：关键入参inventoryType不存在");
//            return new ResponseEntity<ResultVO>(new ResultVO(1,"关键入参inventoryType不存在",false), HttpStatus.OK);
//        }
//        if (StringUtils.isEmpty(vo.getOrgansign())){
//            logger.info("方法InventoryVerifyApiController->saveInventoryVerify错误：关键入参organsign不存在");
//            return new ResponseEntity<ResultVO>(new ResultVO(1,"关键入参organsign不存在",false), HttpStatus.OK);
//        }
//        if (StringUtils.isEmpty(vo.getInventoryAllYn())){
//            logger.info("方法InventoryVerifyApiController->saveInventoryVerify错误：关键入参inventoryAllYn不存在");
//            return new ResponseEntity<ResultVO>(new ResultVO(1,"关键入参inventoryAllYn不存在",false), HttpStatus.OK);
//        }
//        logger.info("移动端保存盘点结果：入参《"+ JSON.toJSONString(vo)+"》");
//        try{
//            vo=inventoryVerifyApi.mobileSaveyVerify(vo);
//            logger.info("移动端保存盘点，返回结果《"+JSON.toJSONString(vo)+"》");
//        }catch (Exception e){
//            logger.error("保存确认单信息失败",e);
//            ResultVO<Boolean> resultVO = new ResultVO();
//            resultVO.setCode(ResultCodeEnum.ERROR.getCode());
//            resultVO.setMsg("保存确认单信息失败");
//            resultVO.setResult(false);
//            return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
//        }
//        if (vo.getBack()!=null&&vo.getBack().equals(1)){
//            ResultVO<Boolean> resultVO = new ResultVO();
//            resultVO.setCode(ResultCodeEnum.SUCCESS.getCode());
//            resultVO.setMsg(ResultCodeEnum.SUCCESS.getMsg());
//            resultVO.setResult(true);
//            return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
//        }else{
//            ResultVO<Boolean> resultVO = new ResultVO();
//            resultVO.setCode(ResultCodeEnum.ERROR.getCode());
//            if (!StringUtils.isEmpty(vo.getErrorMsg())){
//                resultVO.setMsg(vo.getErrorMsg());
//            }else{
//                resultVO.setMsg("未知原因，保存失败");
//            }
//            resultVO.setResult(false);
//            return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
//        }
    }
}
