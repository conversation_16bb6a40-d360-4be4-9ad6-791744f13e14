package com.xyy.saas.web.api.module.member.controller;


import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.member.core.api.MemberShareForZBApi;
import com.xyy.saas.member.core.dto.*;
import com.xyy.saas.member.core.response.ResultVO;
import com.xyy.saas.web.api.module.member.model.MemberShareVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.ArrayList;
import java.util.List;

/**
 * 会员共享总部端API
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/member/memberShare/zb")
@Api(value = "memberPrepayCardToken", description = "会员共享总部端API")
public class MemberShareForZBApiController {

    private static final Logger logger = Logger.getLogger(MemberShareForZBApiController.class);

    @Reference(version = "0.0.1")
    private MemberShareForZBApi memberShareForZBApi;

    @ApiOperation(value = "获取会员共享状态列表", notes = "获取会员共享状态列表", response = MemberShareDto.class, tags = {"会员共享总部端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberShareDto.class)})
    @RequestMapping(value = "/getAll", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> getAll(@RequestHeader("commonRequestModel") String commonRequestModelStr) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String organSign = commonRequestModel.getOrganSign();
        return new ResponseEntity(memberShareForZBApi.selectByHeadquartersOrganSignForZB(organSign), HttpStatus.OK);
    }

    @ApiOperation(value = "分页获取会员共享状态列表", notes = "分页获取会员共享状态列表", response = MemberShareDto.class, tags = {"会员共享总部端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = PageInfo.class)})
    @RequestMapping(value = "/getPage", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> getPage(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                     @RequestBody MemberShareQueryDto queryDto) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String organSign = commonRequestModel.getOrganSign();
        queryDto.setHeadquartersOrganSign(organSign);
        return new ResponseEntity(memberShareForZBApi.selectPageByHeadquartersOrganSignForZB(queryDto), HttpStatus.OK);
    }

    @ApiOperation(value = "保存积分折算比例", notes = "保存积分折算比例，会员共享第一步【统一积分价值】", response = Boolean.class, tags = {"会员共享总部端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> save(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                  @RequestBody List<MemberShareVo> voList) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String organSign = commonRequestModel.getOrganSign();
        String employeeId = commonRequestModel.getEmployeeId();
        List<MemberShareDto> list = new ArrayList<>();
        voList.stream().forEach(item -> {
            MemberShareDto dto = new MemberShareDto();
            BeanUtils.copyProperties(item, dto);
            if (dto.getId() == null) {
                dto.setCreateUser(employeeId);
            } else {
                dto.setUpdateUser(employeeId);
            }
            dto.setHeadquartersOrganSign(organSign);
            list.add(dto);
        });
        return new ResponseEntity(memberShareForZBApi.saveForZB(list), HttpStatus.OK);
    }

    @ApiOperation(value = "获取门店会员等级和总部会员等级的匹配关系", notes = "获取门店会员等级和总部会员等级的匹配关系", response = MemberShareLevelRelationDto.class, tags = {"会员共享总部端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberShareLevelRelationDto.class)})
    @RequestMapping(value = "/levelRelation", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> levelRelation(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                           @RequestBody MemberShareExecuteDto shareExecuteDto) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String organSign = commonRequestModel.getOrganSign();
        shareExecuteDto.setHeadquartersOrganSign(organSign);
        return new ResponseEntity(memberShareForZBApi.getLevelRelationForZB(shareExecuteDto), HttpStatus.OK);
    }

    @ApiOperation(value = "执行会员共享", notes = "执行会员共享", response = MemberShareLevelRelationDto.class, tags = {"会员共享总部端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberShareLevelRelationDto.class)})
    @RequestMapping(value = "/execute", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> execute(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                     @RequestBody MemberShareExecuteDto shareExecuteDto) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        if (shareExecuteDto == null){
            shareExecuteDto = new MemberShareExecuteDto();
        }
        shareExecuteDto.setCreateUser(commonRequestModel.getEmployeeId());
        shareExecuteDto.setHeadquartersOrganSign(commonRequestModel.getOrganSign());
        return new ResponseEntity(memberShareForZBApi.executeForZB(shareExecuteDto), HttpStatus.OK);
    }


    @ApiOperation(value = "查询会员共享执行结果", notes = "查询会员共享执行结果", response = MemberShareLevelRelationDto.class, tags = {"会员共享总部端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberShareLevelRelationDto.class)})
    @RequestMapping(value = "/checkExecuteResult", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> checkExecuteResult(@RequestHeader("commonRequestModel") String commonRequestModelStr) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        return new ResponseEntity(memberShareForZBApi.checkExecuteResult(commonRequestModel.getOrganSign()), HttpStatus.OK);
    }

    @ApiOperation(value = "执行取消会员共享", notes = "执行取消会员共享", response = MemberShareResultDto.class, tags = {"取消会员共享总部端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberShareResultDto.class)})
    @RequestMapping(value = "/executeSplitShare", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> executeSplitShare(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                               @RequestBody MemberShareSplitDto shareSplitDto) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        shareSplitDto.setCreateUser(commonRequestModel.getEmployeeId());
        shareSplitDto.setHeadquartersOrganSign(commonRequestModel.getOrganSign());
        return new ResponseEntity(memberShareForZBApi.splitShareMember(shareSplitDto), HttpStatus.OK);
    }


    @ApiOperation(value = "来源门店列表", notes = "来源门店列表", response = MemberShareDto.class, tags = {"来源门店列表",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberShareDto.class)})
    @RequestMapping(value = "/getMemberStore", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> getMemberStore(@RequestHeader("commonRequestModel") String commonRequestModelStr) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        String organSign = commonRequestModel.getOrganSign();
        return new ResponseEntity(memberShareForZBApi.selectMemberStore(organSign), HttpStatus.OK);
    }

}
