package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.supplier.dto.ProviderDto;
import com.xyy.saas.web.api.common.config.annotition.RepeatSubmitValidation;
import com.xyy.saas.web.api.module.product.model.SaasProviderBaseInfo;
import io.swagger.annotations.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

/**
 * @ClassName ProviderNewApi
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/1/3 1:26
 * @Version 1.0
 **/
@RequestMapping("/product")
@Api(value = "供应商小药药", description = "供应商小药药")
public interface ProviderNewApi {

    @ApiOperation(value = "供应商新增数据信息回显接口", notes = "供应商新增数据信息回显接口", response = ResultVO.class, tags={ "4.0供应商基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/provider/save",method = RequestMethod.POST)
    @RepeatSubmitValidation(resultType = 3)
    public ResultVO<Object> save(@ApiParam(value="供应商信息",required=true) @RequestBody SaasProviderBaseInfo provider);

    @ApiOperation(value = "供应商保存和编辑提交接口", notes = "供应商保存和编辑提交接口", response = ResultVO.class, tags={ "4.0供应商基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/provider/syncDataForXYY", method = RequestMethod.POST)
    public ResultVO<ProviderDto> syncDataForXYY(HttpServletRequest request);

    @ApiOperation(value = "供应商保存和编辑提交接口", notes = "供应商保存和编辑提交接口", response = ResultVO.class, tags={ "4.0供应商基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/provider/initXYYApprove", method = RequestMethod.POST)
    public ResultVO initXYYApprove();

    @ApiOperation(value = "供应商保存和编辑提交接口", notes = "供应商保存和编辑提交接口", response = ResultVO.class, tags={ "4.0供应商基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/provider/updateXYYApproveExt", method = RequestMethod.POST)
    public ResultVO updateXYYApproveExtMess();
}
