package com.xyy.saas.web.api.module.product.constant;

import com.xyy.saas.web.api.module.product.model.ProductSystemDictVo;

import java.util.ArrayList;
import java.util.List;

public class DictConstant {

    public static final Integer unitBussinessId = 10004;  //单位业务Id

    public static final Integer commodtyTypeBussinessId = 10002; //商品类别(自定义分类)

    public static final Integer  agentBussinessId = 10003; //剂型id

    public static final Integer productSystemType = 10012; //商品系统分类，7大类

    public static final Integer prescriptionBussinessId = 20013;//处方Id

    public static final Integer functionBussinessId = 20020; //功能Id

    public static final Integer maintenanceType = 20082; // 养护类型

    public static final Integer scopeOfOperation = 10005; // 经营范围

    public static final Integer providerCompanyType = 20048; // 供应商类别

    public static final Integer productPriceLabelType = 30001; // 价签模板类型

    public static final Integer productPriceLabelContentType = 30002; // 价签模板标签内容类别

    public static final Integer medicalInsuranceLevelBussinessId = 50007; // 医保等级

    public static final Integer incomeAndOuputTaxRateBussinessId = 50012; //进销项税率
    public static final Integer CosmeticsCategoryBussinessId = 50013; //化妆品分类

    // 商品(供应商)审核状态
    public static final Byte CHECKED_PRODUCT_NO = 1; // 审核通过(审批通过)

    public static final Byte CHECKING_PRODUCT_NO = 2; // 审核中(审批驳回)

    public static final Byte UNCHECK_PRODUCT_NO = 3; // 审核未通过

    // 首营商品(供应商)状态（审批状态）
    public static final Byte PRODUCT_ENTERING_SUCCESS = 1; // 录入成功(待审批)

    public static final Byte PRODUCT_QUALITY_LEADER_CHECKED = 2; // 质量负责人审批成功(一级审批通过)

    public static final Byte PRODUCT_QUALITY_LEADER_REJECT = 3; // 质量负责人审批驳回(一级审批驳回)

    public static final Byte PRODUCT_ENTERPRISE_LEADER_CHECKED = 4; // 企业负责人审批成功(二级审批通过)

    public static final Byte PRODUCT_ENTERPRISE_LEADER_REJECT = 5; // 企业负责人审批驳回(二级审批驳回)

    // 审批类型
    public static final Integer APPROVE_PRODUCT_NO = 1; // 首营商品

    public static final Integer APPROVE_PROVIDER_NO = 2; // 首营供应商

    // 审批节点类型
    public static final Integer QUALITY_LEADER_NODECHECK = 1; // 质量负责人审批

    public static final Integer ENTERPRISE_LEADER_NODECHECK = 2; // 企业负责人审批




    /**
     * ABC分类业务ID
     */
    public static final Integer ABC_BUSSINESS_ID = 20001;

    /**
     * 自定义一级分类业务ID
     */
    public static final Integer PRODUCT_FIRST_BUSSINESS_ID = 20097;
    /**
     * 自定义二级分类业务ID
     */
    public static final Integer PRODUCT_SECOND_BUSSINESS_ID = 20098;
    /**
     * 自定义三级分类业务ID
     */
    public static final Integer PRODUCT_THIRD_BUSSINESS_ID = 20099;
    /**
     * 库房条件
     */
    public static final Integer STORE_CONDITION_BUSINESS_ID = 20042;

    /**
     * 供应商类别
     */
    public static final Integer PROVIDER_TYPE = 20048;

    /**
     * 供应商经营范围
     */
    public static final Integer BUSINESS_SCOPE = 10005;

    /**
     * 批量执行插入操作的数量
     */
    public static final int BATCH_SIZE=1000;


    public static final Integer CONSERVER_STATUS_NO =  20083; //养护状态

    public static final Integer CONSERVER_TYPE_NO = 20082; //养护类型


    public static final String SPLIT_CHAR = "|";

    public static final Integer DISPLAY_STATUS_NO =  20030; //养护状态



    public static final String REDIS_INVENTORY_RATIOS_PREFIX = "inventory:ratio:";

    public static final String REDIS_PRICE_ADVANTAGE_PREFIX = "price:advantage:";

    public static final List<ProductSystemDictVo> shadingAttrDictVos = new ArrayList<>();
    static{
        ProductSystemDictVo shadingAttr1 = new ProductSystemDictVo();
        shadingAttr1.setId(1);
        shadingAttr1.setName("外用");
        ProductSystemDictVo shadingAttr2 = new ProductSystemDictVo();
        shadingAttr2.setId(2);
        shadingAttr2.setName("内服");
        ProductSystemDictVo shadingAttr3 = new ProductSystemDictVo();
        shadingAttr3.setId(3);
        shadingAttr3.setName("注射剂");
        ProductSystemDictVo shadingAttr4 = new ProductSystemDictVo();
        shadingAttr4.setId(4);
        shadingAttr4.setName("食品");
        ProductSystemDictVo shadingAttr5 = new ProductSystemDictVo();
        shadingAttr5.setId(5);
        shadingAttr5.setName("保健食品");
        ProductSystemDictVo shadingAttr6 = new ProductSystemDictVo();
        shadingAttr6.setId(9);
        shadingAttr6.setName("其他");
        shadingAttrDictVos.add(shadingAttr1);
        shadingAttrDictVos.add(shadingAttr2);
        shadingAttrDictVos.add(shadingAttr3);
        shadingAttrDictVos.add(shadingAttr4);
        shadingAttrDictVos.add(shadingAttr5);
        shadingAttrDictVos.add(shadingAttr6);
    }
}
