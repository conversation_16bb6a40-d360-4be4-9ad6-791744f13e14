package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.api.SaasCustomTypeApi;
import com.xyy.saas.common.dto.SaasCustomTypeAddDto;
import com.xyy.saas.common.dto.SaasCustomTypeDto;
import com.xyy.saas.common.dto.SaasCustomTypeQueryDto;
import com.xyy.saas.common.dto.SaasCustomTypeUpdateDto;
import com.xyy.saas.common.enums.CustomTypeBusinessTypeEnum;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T19:34:49.364+08:00")

@Slf4j
@Controller
public class CustomTypeApiController implements CustomTypeApi {

    @Reference(version = "0.0.1")
    private SaasCustomTypeApi saasCustomTypeApi;

    @Override
    public ResponseEntity<ResultVO> pageSearch(HttpServletRequest request, @RequestBody SaasCustomTypeQueryDto queryDto) {
        queryDto.setOrganSign(request.getHeader("organSign"));
        queryDto.setBusinessType(CustomTypeBusinessTypeEnum.PRODUCT_TYPE.getValue());
        log.info("CustomTypeApiController.pageSearch, params:{}", JSON.toJSONString(queryDto));
        if (queryDto.getPage() == null || queryDto.getPage() <= 0
                || queryDto.getRows() == null || queryDto.getRows() <= 0) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "分页参数不合法"), HttpStatus.OK);
        }
        PageInfo<SaasCustomTypeDto> dtoPageInfo = saasCustomTypeApi.pageSearch(queryDto);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(dtoPageInfo), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getDirectChildrens(HttpServletRequest request, @RequestBody SaasCustomTypeQueryDto queryDto) {
        Long parentId = queryDto.getId();
        if (parentId == null) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "参数不合法"), HttpStatus.OK);
        }
        List<SaasCustomTypeDto> directChildrens = saasCustomTypeApi.getDirectChildrens(parentId);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(directChildrens), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getAll(HttpServletRequest request) {
        String organSign = request.getHeader("organSign");
        log.info("CustomTypeApiController.getAll, organSign:{}", organSign);
        List<SaasCustomTypeDto> dtoList = saasCustomTypeApi.getAll(organSign, CustomTypeBusinessTypeEnum.PRODUCT_TYPE.getValue());
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(dtoList), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> update(HttpServletRequest request, @RequestBody SaasCustomTypeUpdateDto updateDto) {
        String organSign = request.getHeader("organSign");
        log.info("CustomTypeApiController.update, organSign:{}, updateDto:{}", organSign, JSON.toJSONString(updateDto));
        ResultVO resultVO = saasCustomTypeApi.updateById(updateDto);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> delete(HttpServletRequest request, @RequestBody SaasCustomTypeUpdateDto updateDto) {
        String organSign = request.getHeader("organSign");
        log.info("CustomTypeApiController.delete, organSign:{}, id:{}", organSign, updateDto.getId());
        ResultVO<String> stringResultVO = saasCustomTypeApi.deleteById(updateDto.getId());
        return new ResponseEntity<ResultVO>(stringResultVO, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> addType(HttpServletRequest request,@RequestBody SaasCustomTypeAddDto addDto) {
        String organSign = request.getHeader("organSign");
        addDto.setOrganSign(organSign);
        ResultVO resultVO = saasCustomTypeApi.addType(addDto);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

}
