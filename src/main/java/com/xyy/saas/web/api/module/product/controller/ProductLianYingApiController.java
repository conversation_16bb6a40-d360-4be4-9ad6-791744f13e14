package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.dynamic.config.api.client.DynamicConfigApi;
import com.xyy.saas.dynamic.config.api.pojo.Result;
import com.xyy.saas.dynamic.config.api.pojo.param.BaseConfigListParam;
import com.xyy.saas.dynamic.config.api.pojo.param.BaseConfigParam;
import com.xyy.saas.dynamic.config.api.pojo.result.BaseConfigListResult;
import com.xyy.saas.dynamic.config.api.pojo.result.BaseConfigResult;
import com.xyy.saas.product.core.api.LianYingProductApi;
import com.xyy.saas.product.core.api.ProductApi;
import com.xyy.saas.product.core.api.ProductDrugStandardApi;
import com.xyy.saas.product.core.dto.*;
import com.xyy.saas.product.core.vo.SimilarCountVo;
import com.xyy.saas.web.api.module.product.model.DistributeQueryVo;
import com.xyy.saas.web.api.module.product.model.TaskDescVo;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api(tags = {"商品服务-联营总部接口"}, description = "联营总部接口 Controller", produces = MediaType.APPLICATION_JSON_VALUE)
@RestController
@RequestMapping("/product/lianying")
@Slf4j
public class ProductLianYingApiController {

    @Reference(version = "0.0.1")
    public DynamicConfigApi dynamicConfigApi;

    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;

    @Reference(version = "0.0.1")
    private ProductDrugStandardApi drugStandardApi;

    @Reference(version = "0.0.1")
    private LianYingProductApi lianYingProductApi;

    @Reference(version = "0.0.1")
    private ProductApi productApi;

    @ApiOperation(value="商品联营总部首营设置查询", notes="商品联营总部首营设置查询")
    @ApiResponses(value = {@ApiResponse(code = 0, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/approvalSettings",method = RequestMethod.POST)
    public ResultVO<List<Map<String, String>>> queryApprovalSettings(HttpServletRequest request, @ApiParam(value = "查询类型 17 商品 18 供应商" ,required=true )@RequestBody BaseConfigParam baseConfig) {
        List<Map<String, String>> resultList = new ArrayList<>();
        Map<String, String> nameMap = new HashMap<>();
        String organSign = request.getHeader("organSign");
        SaaSDrugstoreDto saaSDrugstoreDto = new SaaSDrugstoreDto();
        saaSDrugstoreDto.setHeadquartersOrganSign(organSign);
        List<SaaSDrugstoreDto> allDrugStores = drugstoreApi.getSaaSDrugstoreByCondition(saaSDrugstoreDto);
        int pageSize = 10;
        int num = 0;
        int total = 0;
        do{
            List<BaseConfigParam> list = new ArrayList<>();
            BaseConfigListParam baseConfigListParam = new BaseConfigListParam();
            for(int i = num*pageSize; i<(num+1)*pageSize&&i<allDrugStores.size(); i++){
                SaaSDrugstoreDto dto = allDrugStores.get(i);
                BaseConfigParam baseConfigParam = new BaseConfigParam();
                baseConfigParam.setBizSceneTypes(baseConfig.getBizSceneTypes());
                baseConfigParam.setBizType(1);
                baseConfigParam.setBizUniqueKey("system");
                baseConfigParam.setOrganSign(dto.getOrganSign());
                list.add(baseConfigParam);
                nameMap.put(dto.getOrganSign(), dto.getDrugstoreName());
            }
            baseConfigListParam.setList(list);
            log.info("queryApprovalSettings baseConfigListParam:{}", JSONUtils.obj2JSON(baseConfigListParam));
            Result<BaseConfigListResult> configList = dynamicConfigApi.getConfigList(baseConfigListParam);
            for (BaseConfigResult baseConfigResult: configList.getResult().getList()){
                Map<String, String>  map = new HashMap<>();
                map.put("organSign", baseConfigResult.getOrganSign());
                map.put("name", nameMap.get(baseConfigResult.getOrganSign()));
                map.put("isSet", "false");
                if (baseConfig.getBizSceneTypes().get(0)==17){
                    //sponsor 发起人
                    if (baseConfigResult.getData().containsKey("storeFirstProductSetting")&& null!=baseConfigResult.getData().get("storeFirstProductSetting")){
                        Map map1 = JSONUtils.json2Obj(baseConfigResult.getData().get("storeFirstProductSetting").toString(), Map.class);
                        if (map1.containsKey("sponsor")&&map1.get("sponsor")!=null){
                            map.put("isSet", "true");
                        }
                    }
                }
                if (baseConfig.getBizSceneTypes().get(0)==18){
                    if (baseConfigResult.getData().containsKey("storeFirstSupplierSetting")&& null!=baseConfigResult.getData().get("storeFirstSupplierSetting")){
                        Map map1 = JSONUtils.json2Obj(baseConfigResult.getData().get("storeFirstSupplierSetting").toString(), Map.class);
                        if (map1.containsKey("sponsor")&&map1.get("sponsor")!=null){
                            map.put("isSet", "true");
                        }
                    }
                }
                resultList.add(map);
            }
            num+=1;
            total = num*pageSize;
        }while (total<=allDrugStores.size());
        //因为某些原因这里不想生成对象比较排序，用笨方法遍历两遍遍吧
        List<Map<String, String>> resultListFinal = new ArrayList<>();
        for (Map<String, String> map: resultList){
            if ("true".equals(map.get("isSet"))){
                resultListFinal.add(map);
            }
        }
        for(Map<String, String> map: resultList){
            if ("false".equals(map.get("isSet"))){
                resultListFinal.add(map);
            }
        }
        return ResultVO.createSuccess(resultListFinal);
    }

    /**
     * 查询联营总部商品分发进度 任务进度接口 0数据准备中 1数据分发中 2完成
     */
    @ApiOperation(value="查询联营总部商品分发进度", notes="查询联营总部商品分发进度")
    @ApiResponses(value = {@ApiResponse(code = 0, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/distribute/progress",method = RequestMethod.POST)
    public ResultVO<TaskDescVo> queryDistributeProgress(HttpServletRequest request, @RequestBody TaskDto taskDto) {
        ProductJobDto productJobDto = new ProductJobDto();
        productJobDto.setJobId(taskDto.getTaskId());
        ResultVO<String> resultVO = drugStandardApi.queryJobStatus(productJobDto);
        TaskDescVo taskDescVo = new TaskDescVo();
        if("waiting".equals(resultVO.getResult())){
            taskDescVo.setBusinessCode(1);
            taskDescVo.setMsg1("数据准备中...");
            taskDescVo.setMsg2("这个过程可能需要几分钟，请勿进行任何操作");
        }else if("executing".equals(resultVO.getResult())){
            taskDescVo.setBusinessCode(2);
            taskDescVo.setMsg1("数据准备完成，正在分发中，请稍后...");
            taskDescVo.setMsg2("这个过程可能需要几分钟，请勿进行任何操作");
        }else if("complete".equals(resultVO.getResult())){
            taskDescVo.setBusinessCode(3);//处理完成
        }else {
            taskDescVo.setBusinessCode(4);//未知错误
        }
        return ResultVO.createSuccess(taskDescVo);
    }

    /**
     * 查询相似品个数和机构个数
     */
    @ApiOperation(value="查询相似品个数和机构个数", notes="查询相似品个数和机构个数")
    @ApiResponses(value = {@ApiResponse(code = 0, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/similar/total",method = RequestMethod.POST)
    public ResultVO<SimilarCountVo> querySimilarTotal(HttpServletRequest request, @RequestBody TaskDto taskDto){
        return lianYingProductApi.querySimilarTotal(taskDto);
    }

    /**
     * 查询单个相似品信息
     */
    @ApiOperation(value="查询单个相似品信息", notes="查询单个相似品信息")
    @ApiResponses(value = {@ApiResponse(code = 0, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/similar/info",method = RequestMethod.POST)
    public ResultVO<List<ProductSimilarInfoVo>> querySimilarInfo(HttpServletRequest request, @RequestBody TaskDto taskDto){
        return lianYingProductApi.querySimilarInfo(taskDto);
    }

    /**
     * 商品分发
     */
    @ApiOperation(value="商品分发", notes="商品分发")
    @ApiResponses(value = {@ApiResponse(code = 0, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/distribute",method = RequestMethod.POST)
    public ResultVO<TaskDescVo> distribute(HttpServletRequest request, @RequestBody DistributeQueryVo distributeVo){
        String headerOrganSign = request.getHeader("organSign");
        DistributeQueryDto distributeQueryDto = new DistributeQueryDto();
        List<String> departOrganSigns = distributeVo.getDrugs();
        distributeQueryDto.setDepartOrganSigns(departOrganSigns);
        distributeQueryDto.setHeaderOrganSign(headerOrganSign);
        distributeQueryDto.setBusinessScope(distributeVo.getBusinessScope());
        distributeQueryDto.setManufacturer(distributeVo.getManufacturer());
        distributeQueryDto.setMedicalInsurance(distributeVo.getMedicalInsurance());
        distributeQueryDto.setMixedQuery(distributeVo.getMixedQuery());
        distributeQueryDto.setPrefs(distributeVo.getPrefs());
        distributeQueryDto.setType(distributeVo.getType());
        distributeQueryDto.setPrescriptionClassification(distributeVo.getPrescriptionClassification());
        distributeQueryDto.setSystemType(distributeVo.getSystemType());
        distributeQueryDto.setUsed(distributeVo.getUsed());
        ResultVO<DistributeTaskInfoDto> resultVO = productApi.productDistribute(distributeQueryDto);
        if(resultVO.getCode() != 0){
            return ResultVO.createError(ResultCodeEnum.ERROR, resultVO.getMsg());
        }
        TaskDescVo finalVo = new TaskDescVo();
        finalVo.setBusinessCode(resultVO.getResult().getBusinessCode());
        finalVo.setTaskId(resultVO.getResult().getTaskId());
        if(finalVo.getBusinessCode() == 1){//任务创建成功
            finalVo.setMsg1("数据准备中...");
            finalVo.setMsg2("这个过程可能需要几分钟，请勿进行任务操作");
            return ResultVO.createSuccess(finalVo);
        }else{
            finalVo.setMsg1("当前有正在执行的任务，不可再创建");
            return new ResultVO(ResultCodeEnum.ERROR, "当前有正在执行的任务，不可再创建", finalVo);
        }
    }

    /**
     * 相似品操作  批量/单个
     */
    @ApiOperation(value="相似品操作 批量/单个", notes="相似品操作 批量/单个")
    @ApiResponses(value = {@ApiResponse(code = 0, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/similar/operation",method = RequestMethod.POST)
    public ResultVO similarOperation(HttpServletRequest request, @RequestBody DistributeDto distributeDto){
        return lianYingProductApi.similarOperation(distributeDto);
    }

    /**
     * 相似品操作所有
     */
    @ApiOperation(value="相似品操作 批量", notes="相似品操作 批量")
    @ApiResponses(value = {@ApiResponse(code = 0, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/similar/operation/all",method = RequestMethod.POST)
    public ResultVO similarOperationAll(HttpServletRequest request, @RequestBody SimilarOperationAllDto similarOperationDto){
        return lianYingProductApi.similarOperationAll(similarOperationDto);
    }
}
