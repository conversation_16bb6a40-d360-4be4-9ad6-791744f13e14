/**
 * NOTE: This class is auto generated by the swagger code generator program (2.2.3).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.web.api.module.product.model.SaasPositionVo;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-30T11:33:52.138+08:00")
@RequestMapping("/product")
@Api(value = "findPositionVos", description = "the findPositionVos API")
public interface FindPositionVosApi {

    @ApiOperation(value = "查询所有架位信息", notes = "查询所有架位信息", response = SaasPositionVo.class, tags={ "SaasPositionVo", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "操作成功", response = SaasPositionVo.class) })
    
    @RequestMapping(value = "/findPositionVos",
        produces = { "application/xml", "application/json" }, 
        consumes = { "application/json", "application/xml" },
        method = RequestMethod.POST)
    ResponseEntity<SaasPositionVo> findPositionVos(@ApiParam(value = "架位Vo", required = true) @Valid @RequestBody SaasPositionVo saasPositionVo);

}
