package com.xyy.saas.web.api.module.member.controller;


import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.member.core.api.MemberSmsPackageForZBApi;
import com.xyy.saas.member.core.dto.MemberSmsPackageDto;
import com.xyy.saas.web.api.module.member.model.MemberSmsPackageVo;
import com.xyy.user.module.api.EmployeeApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.regex.Pattern;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-08-29T21:37:06.377+08:00")

@Controller
@RequestMapping("/member/memberSmsPackage/zb")
@Api(value = "memberSmsPackage", description = "the memberSmsPackage API")
public class MemberSmsPackageForZBApiController {

    @Reference(version = "0.0.1")
    private MemberSmsPackageForZBApi smsPackageApi;
    @Reference(version = "0.0.1")
    private EmployeeApi employeeApi;

    private Pattern pattern = Pattern.compile("^[0-9]+(\\.[0-9]{1,2})?$");

    @ApiOperation(value = "短信套餐", notes = "短信套餐", response = MemberSmsPackageDto.class, tags={ "memberSmsPackage", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberSmsPackageDto.class) })
    @RequestMapping(value = "/getMemberSmsPackageList",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getMemberSmsPackageList(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                            @RequestBody MemberSmsPackageVo smsPackageDto){
        MemberSmsPackageDto dto = new MemberSmsPackageDto();
        BeanUtils.copyProperties(smsPackageDto, dto);
//        dto.setPage(dto.getPage() == null ? 0 : dto.getPage());
//        dto.setRows(dto.getRows() == null ? 10 : dto.getRows());
        dto.setStatus((byte)1);
        //按照短信条数大小从小到大排序
        dto.setOrderParams(1);
        return new ResponseEntity(ResultVO.createSuccess(smsPackageApi.getMemberSmsPackage(dto)), HttpStatus.OK);
    }

}
