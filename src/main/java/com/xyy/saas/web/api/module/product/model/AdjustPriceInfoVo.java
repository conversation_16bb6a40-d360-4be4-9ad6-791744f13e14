package com.xyy.saas.web.api.module.product.model;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName AdjustPriceInfoVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/1/14 11:23
 * @Version 1.0
 **/
public class AdjustPriceInfoVo {
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    private Long id;
    private String pref;//"单据编号",
    private String effectType;//"生效类型",
    private String effectTime;//"生效时间",
    private String applicableStores;//适用门店",
    private String pharmacyPref;//药店商品编号",
    private String adjustReason;//调价原因",
    private String commonName;//商品通用名称",
    private String productName;//商品名称",
    private String attributeSpecification;//规格",
    private String jixingName;//剂型名称",
    private String unitName;//单位名称",
    private String manufacturer;//生产厂家",
    private String producingArea;//产地",
    private BigDecimal retailPrice;//原零售价",
    private BigDecimal vipPrice;//原会员价",
    private BigDecimal newPrice;//新零售价",
    private BigDecimal newMemberPrice;//新会员价"
    private String adjustOrganSignName;//调价部门
    private String adjustUserName;//调价员

    private Date createTime;//创建时间
    private Date updateTime;//更新时间

    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    public String getEffectType() {
        return effectType;
    }

    public void setEffectType(String effectType) {
        this.effectType = effectType;
    }

    public String getEffectTime() {
        return effectTime;
    }

    public void setEffectTime(String effectTime) {
        this.effectTime = effectTime;
    }

    public String getApplicableStores() {
        return applicableStores;
    }

    public void setApplicableStores(String applicableStores) {
        this.applicableStores = applicableStores;
    }

    public String getPharmacyPref() {
        return pharmacyPref;
    }

    public void setPharmacyPref(String pharmacyPref) {
        this.pharmacyPref = pharmacyPref;
    }

    public String getAdjustReason() {
        return adjustReason;
    }

    public void setAdjustReason(String adjustReason) {
        this.adjustReason = adjustReason;
    }

    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getAttributeSpecification() {
        return attributeSpecification;
    }

    public void setAttributeSpecification(String attributeSpecification) {
        this.attributeSpecification = attributeSpecification;
    }

    public String getJixingName() {
        return jixingName;
    }

    public void setJixingName(String jixingName) {
        this.jixingName = jixingName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getProducingArea() {
        return producingArea;
    }

    public void setProducingArea(String producingArea) {
        this.producingArea = producingArea;
    }

    public BigDecimal getRetailPrice() {
        return retailPrice;
    }

    public void setRetailPrice(BigDecimal retailPrice) {
        this.retailPrice = retailPrice;
    }

    public BigDecimal getVipPrice() {
        return vipPrice;
    }

    public void setVipPrice(BigDecimal vipPrice) {
        this.vipPrice = vipPrice;
    }

    public BigDecimal getNewPrice() {
        return newPrice;
    }

    public void setNewPrice(BigDecimal newPrice) {
        this.newPrice = newPrice;
    }

    public BigDecimal getNewMemberPrice() {
        return newMemberPrice;
    }

    public void setNewMemberPrice(BigDecimal newMemberPrice) {
        this.newMemberPrice = newMemberPrice;
    }

    public String getAdjustOrganSignName() {
        return adjustOrganSignName;
    }

    public void setAdjustOrganSignName(String adjustOrganSignName) {
        this.adjustOrganSignName = adjustOrganSignName;
    }

    public String getAdjustUserName() {
        return adjustUserName;
    }

    public void setAdjustUserName(String adjustUserName) {
        this.adjustUserName = adjustUserName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
