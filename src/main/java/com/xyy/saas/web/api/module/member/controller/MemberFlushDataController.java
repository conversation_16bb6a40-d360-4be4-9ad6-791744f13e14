package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.member.core.api.DataTokenFlushApi;
import com.xyy.saas.member.core.api.MemberLevelApi;
import com.xyy.saas.member.core.dto.DataTokenFlushDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@Controller
@RequestMapping("/member/flush/data")
@Api(value = "flushData", description = "脱敏刷新数据")
public class MemberFlushDataController {


    @Reference(version = "0.0.1")
    DataTokenFlushApi dataTokenFlushApi;

    @Reference(version = "0.0.1")
    MemberLevelApi memberLevelApi;

    @ApiOperation(value = "刷新会员数据", notes = "刷新会员数据")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功") })
    @RequestMapping(value = "/flushMember", method = RequestMethod.POST)
    @ResponseBody
    public String flushMember(@RequestBody DataTokenFlushDto dto){
        return dataTokenFlushApi.flushMemberData(dto);
    }

    @ApiOperation(value = "刷新会员等级数据", notes = "刷新会员等级数据")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功") })
    @RequestMapping(value = "/flushMemberLevel", method = RequestMethod.POST)
    @ResponseBody
    public int flushMemberLevel(){
         return memberLevelApi.initMemberLevel();
    }


//    @ApiOperation(value = "获取手机号token", notes = "获取手机号token")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功") })
//    @RequestMapping(value = "/getPhoneToken", method = RequestMethod.POST)
//    public ResultVO getPhoneToken(@RequestBody List<String> phoneList){
//        Map<String, String> map = dataTokenFlushApi.getPhoneToken(phoneList);
//        return ResultVO.ofSuccess(map);
//    }
//
    @ApiOperation(value = "刷新会员历史数据", notes = "刷新会员历史数据")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功") })
    @RequestMapping(value = "/flushHistory", method = RequestMethod.POST)
    @ResponseBody
    public String flushHistory(@RequestBody DataTokenFlushDto dto){
        return dataTokenFlushApi.flushHistoryData(dto);
    }
//
//    @ApiOperation(value = "刷新患者数据", notes = "刷新患者数据")
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
//    @RequestMapping(value = "/flushPatient", method = RequestMethod.POST)
//    @ResponseBody
//    public String flushPatient(@RequestBody DataTokenFlushDto dto){
//        return dataTokenFlushApi.flushPatientData(dto);
//    }

}
