/**
 * NOTE: This class is auto generated by the swagger code generator program (2.2.3).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.web.api.common.config.annotition.RepeatSubmitValidation;
import com.xyy.saas.web.api.module.product.model.SalesSplitProductVo;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T15:59:22.655+08:00")
@RequestMapping("/product")
@Api(value = "proSalesScattered", description = "the proSalesScattered API")
public interface ProSalesScatteredApi {

    @ApiOperation(value = "添加销售拆零商品信息", notes = "添加销售拆零商品信息", response = SalesSplitProductVo.class, tags={ "proSalesScattered", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "操作成功", response = SalesSplitProductVo.class) })
    
    @RequestMapping(value = "/proSalesScattered/addPro",
        method = RequestMethod.POST)
    @RepeatSubmitValidation(resultType = 4)
    ResponseEntity<ResultVO> addPro(HttpServletRequest request, @ApiParam(value = "销售拆零商品信息", required = true) @Valid @RequestBody SalesSplitProductVo salesSplitProductVo);


    @ApiOperation(value = "修改销售拆零商品信息", notes = "修改销售拆零商品信息", response = SalesSplitProductVo.class, tags={ "proSalesScattered", })
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "操作成功", response = SalesSplitProductVo.class) })

    @RequestMapping(value = "/proSalesScattered/updatePro/",
        method = RequestMethod.POST)
    ResponseEntity<ResultVO> updatePro(HttpServletRequest request, @ApiParam(value = "销售拆零商品信息", required = true) @Valid @RequestBody SalesSplitProductVo salesSplitProductVo);

}
