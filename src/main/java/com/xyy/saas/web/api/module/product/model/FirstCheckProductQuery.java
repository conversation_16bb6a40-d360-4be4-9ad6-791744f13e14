package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

/**
 * 首营商品列表查询条件实体
 */
@ApiModel(description = "首营商品列表查询条件实体")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T18:00:37.938+08:00")

public class FirstCheckProductQuery extends Page{
  @JsonProperty("pref")
  private String pref = null;

  @JsonProperty("productParams")
  private String productParams = null;

  @JsonProperty("status")
  private Byte status = null;

  @JsonProperty("isProductIsHidden")
  private Byte isProductIsHidden = null;

  public FirstCheckProductQuery pref(String pref) {
    this.pref = pref;
    return this;
  }

   /**
   * 首营品种单据编号
   * @return pref
  **/
  @ApiModelProperty(value = "首营品种单据编号")


  public String getPref() {
    return pref;
  }

  public void setPref(String pref) {
    this.pref = pref;
  }

  public FirstCheckProductQuery productParams(String productParams) {
    this.productParams = productParams;
    return this;
  }

   /**
   * 商品查询条件
   * @return productParams
  **/
  @ApiModelProperty(value = "商品查询条件")


  public String getProductParams() {
    return productParams;
  }

  public void setProductParams(String productParams) {
    this.productParams = productParams;
  }

  public FirstCheckProductQuery status(Byte status) {
    this.status = status;
    return this;
  }

   /**
   * 首营品种状态
   * @return status
  **/
  @ApiModelProperty(value = "首营品种状态")


  public Byte getStatus() {
    return status;
  }

  public void setStatus(Byte status) {
    this.status = status;
  }


  public FirstCheckProductQuery isProductIsHidden(Byte isProductIsHidden) {
      this.isProductIsHidden = isProductIsHidden;
      return this;
  }

  /**
   * 首营品种状态
   * @return status
   **/
  @ApiModelProperty(value = "商品状态状态")

  public Byte getIsProductIsHidden() {
        return isProductIsHidden;
    }

  public void setIsProductIsHidden(Byte isProductIsHidden) {
        this.isProductIsHidden = isProductIsHidden;
    }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    FirstCheckProductQuery firstCheckProductQuery = (FirstCheckProductQuery) o;
    return Objects.equals(this.pref, firstCheckProductQuery.pref) &&
        Objects.equals(this.productParams, firstCheckProductQuery.productParams) &&
        Objects.equals(this.status, firstCheckProductQuery.status) &&
        Objects.equals(this.isProductIsHidden, firstCheckProductQuery.isProductIsHidden);
  }

  @Override
  public int hashCode() {
    return Objects.hash(pref, productParams, status, isProductIsHidden);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class FirstCheckProductQuery {\n");

    sb.append("    pref: ").append(toIndentedString(pref)).append("\n");
    sb.append("    productParams: ").append(toIndentedString(productParams)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    isProductIsHidden: ").append(toIndentedString(isProductIsHidden)).append("\n");
      sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

