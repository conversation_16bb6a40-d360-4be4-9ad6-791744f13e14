package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName ProductApplyQueryVo
 * @Description 商品提报查询
 * <AUTHOR>
 * @Date 2020/10/29 10:41
 * @Version 1.0
 **/
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

@ApiModel(description = "商品提报查询信息类")
public class ProductApplyQueryVo {

    @JsonProperty("mixQuery")
    private String mixQuery;//商品信息：支持商品编号、批准文号、条形码、助记码、商品名称、通用名称进行查询，和总部商品查询保持一致
    @JsonProperty("manufacturer")
    private String manufacturer;//生产厂家
    @JsonProperty("auditState")
    private String auditState;//提报审批状态 1:审批中 2:已通过 3:已驳回，不填：默认全部
    @JsonProperty("productAuditState")
    private String productAuditState;//首映审批状态 1:审批中 2:已通过 3:已驳回，不填：默认全部

    @ApiModelProperty(value = "当前页码")
    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    @ApiModelProperty(value = "行数")
    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    @JsonProperty("page")
    private Integer page;
    @JsonProperty("rows")
    private Integer rows;

    @ApiModelProperty(value = "商品信息：支持商品编号、批准文号、条形码、助记码、商品名称、通用名称进行查询，和总部商品查询保持一致")
    public String getMixQuery() {
        return mixQuery;
    }

    public void setMixQuery(String mixQuery) {
        this.mixQuery = mixQuery;
    }

    @ApiModelProperty(value = "生产厂家")
    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    @ApiModelProperty(value = "提报审批状态 1:审批中 2:已通过 3:已驳回，不填：默认全部")
    public String getAuditState() {
        return auditState;
    }

    public void setAuditState(String auditState) {
        this.auditState = auditState;
    }

    @ApiModelProperty(value = "首映审批状态 1:审批中 2:已通过 3:已驳回，不填：默认全部")
    public String getProductAuditState() {
        return productAuditState;
    }

    public void setProductAuditState(String productAuditState) {
        this.productAuditState = productAuditState;
    }
}
