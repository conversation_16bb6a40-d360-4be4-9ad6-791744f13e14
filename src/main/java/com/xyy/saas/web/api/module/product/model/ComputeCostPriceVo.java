package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * @ClassName ComputeCostPriceVo
 * @Description 单条后端计算器信息封装类
 * <AUTHOR>
 * @Date 2020/8/19 17:14
 * @Version 1.0
 **/
@ApiModel(description = "单条后端计算器信息封装类")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")
public class ComputeCostPriceVo {

    @JsonProperty("newCostPrice")
    private String newCostPrice;//新成本价
    @JsonProperty("oldCostPrice")
    private String oldCostPrice;//原成本价
    @JsonProperty("adjustmentRatio")
    private String adjustmentRatio;//调整比例
    @JsonProperty("flag")
    private Byte flag;//1:通过调价比例和原成本价计算新成本价，2:通过新成本价和原成本价计算调价比例

    @ApiModelProperty(value = "1:通过调价比例和原成本价计算新成本价，2:通过新成本价和原成本价计算调价比例")
    public Byte getFlag() {
        return flag;
    }

    public void setFlag(Byte flag) {
        this.flag = flag;
    }

    @ApiModelProperty(value = "新成本价")
    public String getNewCostPrice() {
        return newCostPrice;
    }

    public void setNewCostPrice(String newCostPrice) {
        this.newCostPrice = newCostPrice;
    }

    @ApiModelProperty(value = "原成本价")
    public String getOldCostPrice() {
        return oldCostPrice;
    }

    public void setOldCostPrice(String oldCostPrice) {
        this.oldCostPrice = oldCostPrice;
    }

    @ApiModelProperty(value = "调整比例")
    public String getAdjustmentRatio() {
        return adjustmentRatio;
    }

    public void setAdjustmentRatio(String adjustmentRatio) {
        this.adjustmentRatio = adjustmentRatio;
    }
}
