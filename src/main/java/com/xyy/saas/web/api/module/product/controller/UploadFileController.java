package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.web.api.module.product.model.FileUrlVo;
import io.swagger.annotations.ApiParam;
import org.jboss.netty.handler.codec.http.HttpRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.net.URL;

/**
 * @ClassName UploadFileController
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/4/23 13:19
 * @Version 1.0
 **/
@Controller
@RequestMapping("/product/upload/file")
public class UploadFileController {

    private static final Logger logger = LoggerFactory.getLogger(UploadFileController.class);
    /**
     * 功能描述: <br> 图片下载
     * 〈〉
     * @Param: [response, fileUrl]
     * @Return: void
     */
    @RequestMapping("/downLoad")
    public void downLoad(HttpServletRequest request, HttpServletResponse response, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody FileUrlVo fileUrlVo) throws IOException {
        BufferedInputStream dis = null;
        BufferedOutputStream fos = null;
        String fileUrl = fileUrlVo.getFileUrl();
        try {
            String filename = fileUrl.substring(fileUrl.lastIndexOf("/") + 1,fileUrl.length());
            URL url = new URL(fileUrl);
            response.reset();//避免空行
            response.setContentType("application/x-msdownload;");
            response.setHeader("Content-disposition", "attachment; filename=" + filename);
            response.setHeader("Content-Length", String.valueOf(url.openConnection().getContentLength()));
            response.setHeader("Access-Control-Allow-Origin", "*");

            dis = new BufferedInputStream(url.openStream());
            fos = new BufferedOutputStream(response.getOutputStream());
            byte[] buff = new byte[2048];
            int bytesRead;
            while (-1 != (bytesRead = dis.read(buff, 0, buff.length))) {
                fos.write(buff, 0, bytesRead);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("UploadFileController ",e);
        } finally {
            if (dis != null){
                dis.close();
            }
            if (fos != null){
                fos.close();
            }
        }
    }
}
