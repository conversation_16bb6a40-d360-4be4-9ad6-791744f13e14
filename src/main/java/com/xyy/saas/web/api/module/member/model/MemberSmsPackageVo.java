package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "会员短信套餐")
public class MemberSmsPackageVo implements Serializable {

    private static final long serialVersionUID = -8174528858614869087L;
    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long id;
    /**
     * 套餐类型
     */
    @ApiModelProperty(value="套餐类型")
    private String smsPackage;
    /**
     * 价格
     */
    @ApiModelProperty(value="价格")
    private BigDecimal price;
    /**
     * 原价
     */
    @ApiModelProperty(value="原价")
    private BigDecimal costPrice;
    /**
     * 条数
     */
    @ApiModelProperty(value="条数")
    private Integer count;
    /**
     * 创建人
     */
    @ApiModelProperty(value="创建人")
    private String createUser;
    /**
     * 是否删除  0、否  1、是
     */
    @ApiModelProperty(value="是否删除  0、否  1、是", example = "0")
    private Byte yn;
    /**
     * 促销  0、否  1、是
     */
    @ApiModelProperty(value="促销  0、否  1、是", example = "0")
    private Byte isSale;
    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    private Integer page;
    /**
     * 页码大小
     */
    @ApiModelProperty(value = "页码大小")
    private Integer rows;
    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private Date startDate;
    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private Date endDate;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSmsPackage() {
        return smsPackage;
    }

    public void setSmsPackage(String smsPackage) {
        this.smsPackage = smsPackage;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Byte getYn() {
        return yn;
    }

    public void setYn(Byte yn) {
        this.yn = yn;
    }

    public Byte getIsSale() {
        return isSale;
    }

    public void setIsSale(Byte isSale) {
        this.isSale = isSale;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    @Override
    public String toString() {
        return "MemberSmsPackageVo{" +
                "id=" + id +
                ", smsPackage='" + smsPackage + '\'' +
                ", price=" + price +
                ", costPrice=" + costPrice +
                ", count=" + count +
                ", createUser='" + createUser + '\'' +
                ", yn=" + yn +
                ", isSale=" + isSale +
                ", page=" + page +
                ", rows=" + rows +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                '}';
    }
}