package com.xyy.saas.web.api.module.product.model;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;


@Data
public class OperationGuideReadRecordVo implements Serializable {

    /**
     * 操作指引类型
     */
    @JsonProperty("guideType")
    private Integer guideType;

    /**
     * 机构号
     */
    @JsonProperty("organSign")
    private String organSign;

    /**
     * 员工ID
     */
    @JsonProperty("employeeId")
    private String employeeId;
}