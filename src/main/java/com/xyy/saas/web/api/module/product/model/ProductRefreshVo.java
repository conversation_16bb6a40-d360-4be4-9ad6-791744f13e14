package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName ProductRefreshVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/10/29 13:59
 * @Version 1.0
 **/
@ApiModel(description = "刷新实体类")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")
public class ProductRefreshVo {

    @JsonProperty("organSigns")
    private String organSigns;
    @JsonProperty("startTime")
    private String startTime;
    @JsonProperty("endTime")
    private String endTime;

    @ApiModelProperty(value = "isExcute 为1执行禁用，不传的话，不执行禁用操作")
    public int getIsExcute() {
        return isExcute;
    }

    public void setIsExcute(int isExcute) {
        this.isExcute = isExcute;
    }

    @JsonProperty("isExcute")
    private int isExcute;

    @ApiModelProperty(value = "开始时间，2019-10-28 19:00:00")
    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    @ApiModelProperty(value = "截止时间，2019-10-28 10:00:00")
    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
    @ApiModelProperty(value = "机构编码列表，以逗号分隔")
    public String getOrganSigns() {
        return organSigns;
    }

    public void setOrganSigns(String organSigns) {
        this.organSigns = organSigns;
    }

}
