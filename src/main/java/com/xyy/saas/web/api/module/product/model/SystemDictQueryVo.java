package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-07T14:34:49.141+08:00")

@ApiModel(description = "商品字典类查询对象")
public class SystemDictQueryVo {

    @JsonProperty("type")
    private Integer type;

    @ApiModelProperty(value = "请求字典类型：" +
            "    0:全部类型\n" +
            "    10002:商品自定义分类\n" +
            "    20001:ABC分类业务ID\n" +
            "    10101:商品系统类型\n"+
            "    10000:货位字典")
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

}
