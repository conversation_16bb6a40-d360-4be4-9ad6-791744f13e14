package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @annotation:封装库存导入VO
 * @create 2018-09-18 20:56
 **/
public class InventoryLotNumberPageVo {

    @JsonProperty("lotNumberVos")
    private List<InventoryLotNumberVo> lotNumberVos=null;

    public InventoryLotNumberPageVo lotNumberVos(List<InventoryLotNumberVo> lotNumberVos) {
        this.lotNumberVos = lotNumberVos;
        return this;
    }

    public InventoryLotNumberPageVo addLotNumberVosItem(InventoryLotNumberVo lotNumberVosItem) {
        if (this.lotNumberVos == null) {
            this.lotNumberVos = new ArrayList<InventoryLotNumberVo>();
        }
        this.lotNumberVos.add(lotNumberVosItem);
        return this;
    }


    @ApiModelProperty(value = "封装VO")

    public List<InventoryLotNumberVo> getLotNumberVos() {
        return lotNumberVos;
    }

    public void setLotNumberVos(List<InventoryLotNumberVo> lotNumberVos) {
        this.lotNumberVos = lotNumberVos;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        InventoryLotNumberPageVo that = (InventoryLotNumberPageVo) o;

        return lotNumberVos != null ? lotNumberVos.equals(that.lotNumberVos) : that.lotNumberVos == null;
    }

    @Override
    public int hashCode() {
        return lotNumberVos != null ? lotNumberVos.hashCode() : 0;
    }

    @Override
    public String toString() {
        return "InventoryLotNumberPageVo{" +
                "lotNumberVos=" + lotNumberVos +
                '}';
    }
}

