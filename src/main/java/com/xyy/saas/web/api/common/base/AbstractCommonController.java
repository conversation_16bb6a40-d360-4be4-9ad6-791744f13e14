package com.xyy.saas.web.api.common.base;


import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.web.api.common.context.ActionContext;
import com.xyy.saas.web.api.common.context.ActionContextSupport;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 *
 */
public abstract class AbstractCommonController {
    /**
     * 获取当前登录员工的ID
     * @return
     */
    public Integer getCurrentEmployeeId(){
        ActionContext current = ActionContextSupport.getCurrent();
        Object obj = current.getContext(ActionContextSupport.CURRENT_USER_Id_KEY);
        return obj !=null?Integer.parseInt(obj.toString()):0;
    }

    /**
     * 获取当前登录机构码
     * @return
     */
    public String getCurrentOrganSign(){
       ActionContext current = ActionContextSupport.getCurrent();
        Object obj = current.getContext(ActionContextSupport.CURRENT_ORGANSIGN_KEY);
        return obj !=null?obj.toString():"";
    }

    /**
     * 获取当前登录员工姓名
     * @return
     */
    public String getCurrentLoginName(){
        ActionContext current = ActionContextSupport.getCurrent();
        CommonRequestModel model = current.getModel();
        return model.decodeContent(model.getEmployeeName());
    }

    /**
     * 获取当前登录员工身份
     * 员工身份 0普通身份 1防检查身份
     * @return
     *//*
    public String getCurrentEmployeeIdentity(){
        RequestAttributes ra = RequestContextHolder.getRequestAttributes();
        ServletRequestAttributes sra = (ServletRequestAttributes) ra;
        HttpServletRequest request = sra.getRequest();
        return String.valueOf(request.getSession().getAttribute(LoginConstans.LOGIN_EMPLOYEE_IDENTITY).toString());
    }*/
}
