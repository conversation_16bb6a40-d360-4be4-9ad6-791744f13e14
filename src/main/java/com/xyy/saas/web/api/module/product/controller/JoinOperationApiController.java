package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.jm.common.result.ResultVO;
import com.xyy.saas.jm.productstock.core.api.JmInventoryApi;
import com.xyy.saas.jm.productstock.core.dto.*;
import com.xyy.saas.web.api.common.base.AbstractCommonController;
import com.xyy.saas.web.api.module.product.model.ProductStockModel;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 联营库存接口迁移
 */
@Slf4j
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-01-28T15:28:18.933+08:00")
@RestController
public class JoinOperationApiController extends AbstractCommonController implements JoinOperationApi {

    @Reference(version = "0.0.1")
//    @Reference(version = "0.0.1", url="dubbo://192.168.122.112:20248", timeout=3000000)
    private JmInventoryApi jmInventoryApi;

    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;

    /**
     * 查看商品库存
     *
     * @param request
     * @param productStockModel
     * @return
     */
    public ResultVO queryProductInventory(HttpServletRequest request, @RequestBody @NotNull ProductStockModel productStockModel) {
        JmInventoryDto jmInventoryDto = new JmInventoryDto();
        if (productStockModel.getPage() != null) {
            jmInventoryDto.setPageNum(productStockModel.getPage());
        }
        if (productStockModel.getRows() != null) {
            jmInventoryDto.setPageSize(productStockModel.getRows());
        }
        if (productStockModel.getOrganSign() != null) {
            jmInventoryDto.setOrgansign(productStockModel.getOrganSign());
        }
        if (productStockModel.getStockNumber() != null) {
            jmInventoryDto.setStockNumber(new BigDecimal(productStockModel.getStockNumber().intValue()));
        } else {
            //按条件查询1: 大于0    2:全部   3:等于0
            jmInventoryDto.setStockNumber(new BigDecimal(2));
        }
        jmInventoryDto.setProductName(productStockModel.getProductName());

        ResultVO<PageInfo<JmInventoryDto>> pageInfoResultVO = jmInventoryApi.queryInventoryInfo(jmInventoryDto);
        log.info("queryProductInventory|zss|pageInfoResultVO->{}", JSON.toJSONString(pageInfoResultVO));
        return pageInfoResultVO;
    }

    /**
     * 商品批号库存信息查询
     *
     * @param request
     * @param productStockModel
     * @return
     */
    public ResultVO queryProductLotInventory(HttpServletRequest request, @RequestBody @NotNull ProductStockModel productStockModel) {
        JmInventoryLotNumberDto jmInventoryLotNumberDto = new JmInventoryLotNumberDto();
        jmInventoryLotNumberDto.setPageNum(productStockModel.getPage());
        jmInventoryLotNumberDto.setPageSize(productStockModel.getRows());
        jmInventoryLotNumberDto.setOrgansign(productStockModel.getOrganSign());
        jmInventoryLotNumberDto.setPositionid(productStockModel.getPositionid());
        jmInventoryLotNumberDto.setStatus(productStockModel.getStatus());
        jmInventoryLotNumberDto.setProductName(productStockModel.getProductName());
        jmInventoryLotNumberDto.setStockNumber(new BigDecimal(productStockModel.getStockNumber().intValue()));
        ResultVO<PageInfo<JmInventoryLotNumberDto>> pageInfoResultVO = jmInventoryApi.queryLotInventoryInfo(jmInventoryLotNumberDto);
        return pageInfoResultVO;
    }

    /**
     * 联营体商品品类和商品总货值
     *
     * @param commonRequestModel
     * @param productStockModel
     * @return
     */
    public ResultVO allStoresProductKindsAndValue(@RequestHeader("commonRequestModel") String commonRequestModel, @RequestBody @NotNull ProductStockModel productStockModel) {

        CommonRequestModel model = JSONObject.parseObject(commonRequestModel, CommonRequestModel.class);
        String mainStoreGuid = model == null ? null : model.getHeadquartersOrganSign();
        log.info("mainStoreGuid:", mainStoreGuid);
        /**
         * 获取总部门店信息
         */
        List<SaaSDrugstoreDto> drugstores = null;
        if (StringUtils.isNotBlank(mainStoreGuid)) {
            drugstores = drugstoreApi.getDrugstoreByHeadquartersOrganSign(mainStoreGuid);
        }
        List<String> paramsList = new ArrayList<>();
        HashMap<String, String> organSignAndNameMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(drugstores)) {
            for (SaaSDrugstoreDto drugstoreDto : drugstores) {
                String organSign = drugstoreDto.getOrganSign();
                paramsList.add(organSign);
                organSignAndNameMap.put(drugstoreDto.getOrganSign(), drugstoreDto.getDrugstoreName());
            }
        }
        /**
         * 查看门店下商品品类和商品总货值
         */
        ResultVO<JmProductKindsAndValuesDto> jmProductKindsAndValuesDtoResultVO = new ResultVO<>();
        if (paramsList.size() > 0) {
            jmProductKindsAndValuesDtoResultVO = jmInventoryApi.queryKindsAndValues(paramsList);
            if (jmProductKindsAndValuesDtoResultVO.getCode() == 0) {
                JmProductKindsAndValuesDto result = jmProductKindsAndValuesDtoResultVO.getResult();
                List<SingleStoreKindAndValueDto> singleStores = result.getSingleStores();
                if (!CollectionUtils.isEmpty(singleStores)) {
                    for (SingleStoreKindAndValueDto singleStoreKindAndValueDto : singleStores) {
                        String storeName = organSignAndNameMap.get(singleStoreKindAndValueDto.getOrganSign());
                        singleStoreKindAndValueDto.setStoreName(storeName);
                    }
                }
            }
        } else {
            jmProductKindsAndValuesDtoResultVO.setCode(0);
        }
        return jmProductKindsAndValuesDtoResultVO;
    }

    /**
     * 获取货位
     *
     * @param request
     * @param productStockModel
     * @return
     */
    public ResultVO queryPositions(HttpServletRequest request, @RequestBody @NotNull ProductStockModel productStockModel) {
        String organSign = productStockModel.getOrganSign();
        ResultVO<List<JmSaasPositionDto>> resultPositions = jmInventoryApi.queryPositions(organSign);
        return resultPositions;
    }
}
