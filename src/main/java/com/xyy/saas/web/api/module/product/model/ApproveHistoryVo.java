package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.Objects;

/**
 * 商品审核列表实体以及查询条件实体
 */
@ApiModel(description = "商品审核列表实体以及查询条件实体")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T19:34:49.364+08:00")

public class ApproveHistoryVo extends Page{
  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("gspBusinessNo")
  private String gspBusinessNo = null;

  @JsonProperty("businessNo")
  private String businessNo = null;

  @JsonProperty("billNo")
  private String billNo = null;

  @JsonProperty("type")
  private Integer type = null;

  @JsonProperty("msg")
  private String msg = null;

  @JsonProperty("username")
  private String username = null;

  @JsonProperty("status")
  private Integer status = null;

  @JsonProperty("initiatorName")
  private String initiatorName = null;

  @JsonProperty("createTime")
  private Date createTime = null;

  @JsonProperty("createUser")
  private String createUser = null;

  @JsonProperty("updateTime")
  private Date updateTime = null;

  @JsonProperty("updateUser")
  private String updateUser = null;

  @JsonProperty("yn")
  private Byte yn = null;

  @JsonProperty("startDate")
  private Date startDate = null;

  @JsonProperty("endDate")
  private Date endDate = null;

  @JsonProperty("startDateStr")
  private String startDateStr = null;

  @JsonProperty("endDateStr")
  private String endDateStr = null;

  @JsonProperty("ids")
  private String ids = null;

  @JsonProperty("isProductHidden")
  private Byte isProductHidden = null;

  @JsonProperty("guid")
  private String guid;

  @JsonProperty("guids")
  private String guids;

  public ApproveHistoryVo id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * 审核记录主表ID
   * @return id
  **/
  @ApiModelProperty(value = "审核记录主表ID")


  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public ApproveHistoryVo gspBusinessNo(String gspBusinessNo) {
    this.gspBusinessNo = gspBusinessNo;
    return this;
  }

   /**
   * 关联的首营商品、首营企业的编号
   * @return gspBusinessNo
  **/
  @ApiModelProperty(value = "关联的首营商品、首营企业的编号")


  public String getGspBusinessNo() {
    return gspBusinessNo;
  }

  public void setGspBusinessNo(String gspBusinessNo) {
    this.gspBusinessNo = gspBusinessNo;
  }

  public ApproveHistoryVo businessNo(String businessNo) {
    this.businessNo = businessNo;
    return this;
  }

   /**
   * 关联的业务编号：供应商编号，商品编号，处方编号
   * @return businessNo
  **/
  @ApiModelProperty(value = "关联的业务编号：供应商编号，商品编号，处方编号")


  public String getBusinessNo() {
    return businessNo;
  }

  public void setBusinessNo(String businessNo) {
    this.businessNo = businessNo;
  }

  public ApproveHistoryVo billNo(String billNo) {
    this.billNo = billNo;
    return this;
  }

   /**
   * 单据编号
   * @return billNo
  **/
  @ApiModelProperty(value = "单据编号")


  public String getBillNo() {
    return billNo;
  }

  public void setBillNo(String billNo) {
    this.billNo = billNo;
  }

  public ApproveHistoryVo type(Integer type) {
    this.type = type;
    return this;
  }

   /**
   * 审批类型：1--首营商品，2--首营供应商，3--处方登记
   * @return type
  **/
  @ApiModelProperty(value = "审批类型：1--首营商品，2--首营供应商，3--处方登记")


  public Integer getType() {
    return type;
  }

  public void setType(Integer type) {
    this.type = type;
  }

  public ApproveHistoryVo msg(String msg) {
    this.msg = msg;
    return this;
  }

   /**
   * 回复内容
   * @return msg
  **/
  @ApiModelProperty(value = "回复内容")


  public String getMsg() {
    return msg;
  }

  public void setMsg(String msg) {
    this.msg = msg;
  }

  public ApproveHistoryVo username(String username) {
    this.username = username;
    return this;
  }

   /**
   * 审批人
   * @return username
  **/
  @ApiModelProperty(value = "审批人")


  public String getUsername() {
    return username;
  }

  public void setUsername(String username) {
    this.username = username;
  }

  public ApproveHistoryVo status(Integer status) {
    this.status = status;
    return this;
  }

   /**
   * 审批状态，1：待审批；2：一级审批通过；3：一级审批驳回，4--二级审批通过，5--二级审批驳回
   * @return status
  **/
  @ApiModelProperty(value = "审批状态，1：待审批；2：一级审批通过；3：一级审批驳回，4--二级审批通过，5--二级审批驳回")


  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public ApproveHistoryVo initiatorName(String initiatorName) {
    this.initiatorName = initiatorName;
    return this;
  }

   /**
   * 审批发起人
   * @return initiatorName
  **/
  @ApiModelProperty(value = "审批发起人")


  public String getInitiatorName() {
    return initiatorName;
  }

  public void setInitiatorName(String initiatorName) {
    this.initiatorName = initiatorName;
  }

  public ApproveHistoryVo createTime(Date createTime) {
    this.createTime = createTime;
    return this;
  }

   /**
   * 创建时间
   * @return createTime
  **/
  @ApiModelProperty(value = "创建时间")


  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public ApproveHistoryVo createUser(String createUser) {
    this.createUser = createUser;
    return this;
  }

   /**
   * 创建人
   * @return createUser
  **/
  @ApiModelProperty(value = "创建人")


  public String getCreateUser() {
    return createUser;
  }

  public void setCreateUser(String createUser) {
    this.createUser = createUser;
  }

  public ApproveHistoryVo updateTime(Date updateTime) {
    this.updateTime = updateTime;
    return this;
  }

   /**
   * 更新时间
   * @return updateTime
  **/
  @ApiModelProperty(value = "更新时间")


  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public ApproveHistoryVo updateUser(String updateUser) {
    this.updateUser = updateUser;
    return this;
  }

   /**
   * 更新人
   * @return updateUser
  **/
  @ApiModelProperty(value = "更新人")


  public String getUpdateUser() {
    return updateUser;
  }

  public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
  }

  public ApproveHistoryVo yn(Byte yn) {
    this.yn = yn;
    return this;
  }

   /**
   * 逻辑删除 1 有效 0 删除
   * @return yn
  **/
  @ApiModelProperty(value = "逻辑删除 1 有效 0 删除")


  public Byte getYn() {
    return yn;
  }

  public void setYn(Byte yn) {
    this.yn = yn;
  }

  public ApproveHistoryVo startDate(Date startDate) {
    this.startDate = startDate;
    return this;
  }

   /**
   * 查询开始时间
   * @return startDate
  **/
  @ApiModelProperty(value = "查询开始时间")


  public Date getStartDate() {
    return startDate;
  }

  public void setStartDate(Date startDate) {
    this.startDate = startDate;
  }

  public ApproveHistoryVo endDate(Date endDate) {
    this.endDate = endDate;
    return this;
  }

   /**
   * 查询结束时间
   * @return endDate
  **/
  @ApiModelProperty(value = "查询结束时间")


  public Date getEndDate() {
    return endDate;
  }

  public void setEndDate(Date endDate) {
    this.endDate = endDate;
  }

  public ApproveHistoryVo startDateStr(String startDateStr) {
    this.startDateStr = startDateStr;
    return this;
  }

   /**
   * 查询开始时间字符串格式
   * @return startDateStr
  **/
  @ApiModelProperty(value = "查询开始时间字符串格式")


  public String getStartDateStr() {
    return startDateStr;
  }

  public void setStartDateStr(String startDateStr) {
    this.startDateStr = startDateStr;
  }

  public ApproveHistoryVo endDateStr(String endDateStr) {
    this.endDateStr = endDateStr;
    return this;
  }

   /**
   * 查询结束时间字符串格式
   * @return endDateStr
  **/
  @ApiModelProperty(value = "查询结束时间字符串格式")


  public String getEndDateStr() {
    return endDateStr;
  }

  public void setEndDateStr(String endDateStr) {
    this.endDateStr = endDateStr;
  }

  public ApproveHistoryVo ids(String ids) {
    this.ids = ids;
    return this;
  }

   /**
   * 需要审核的id串
   * @return ids
  **/
  @ApiModelProperty(value = "需要审核的id串")


  public String getIds() {
    return ids;
  }

  public void setIds(String ids) {
    this.ids = ids;
  }

  public ApproveHistoryVo isProductHidden(Byte isProductHidden) {
    this.isProductHidden = isProductHidden;
    return this;
  }

   /**
   * 分组商品标志
   * @return isProductHidden
  **/
  @ApiModelProperty(value = "分组商品标志")


  public Byte getIsProductHidden() {
    return isProductHidden;
  }

  public void setIsProductHidden(Byte isProductHidden) {
    this.isProductHidden = isProductHidden;
  }

  @ApiModelProperty(value = "主键标识")
  public String getGuid() {
    return guid;
  }

  public void setGuid(String guid) {
    this.guid = guid;
  }

  @ApiModelProperty(value = "主键标识guid串")
  public String getGuids() {
    return guids;
  }

  public void setGuids(String guids) {
    this.guids = guids;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ApproveHistoryVo approveHistoryVo = (ApproveHistoryVo) o;
    return Objects.equals(this.id, approveHistoryVo.id) &&
        Objects.equals(this.gspBusinessNo, approveHistoryVo.gspBusinessNo) &&
        Objects.equals(this.businessNo, approveHistoryVo.businessNo) &&
        Objects.equals(this.billNo, approveHistoryVo.billNo) &&
        Objects.equals(this.type, approveHistoryVo.type) &&
        Objects.equals(this.msg, approveHistoryVo.msg) &&
        Objects.equals(this.username, approveHistoryVo.username) &&
        Objects.equals(this.status, approveHistoryVo.status) &&
        Objects.equals(this.initiatorName, approveHistoryVo.initiatorName) &&
        Objects.equals(this.createTime, approveHistoryVo.createTime) &&
        Objects.equals(this.createUser, approveHistoryVo.createUser) &&
        Objects.equals(this.updateTime, approveHistoryVo.updateTime) &&
        Objects.equals(this.updateUser, approveHistoryVo.updateUser) &&
        Objects.equals(this.yn, approveHistoryVo.yn) &&
        Objects.equals(this.startDate, approveHistoryVo.startDate) &&
        Objects.equals(this.endDate, approveHistoryVo.endDate) &&
        Objects.equals(this.startDateStr, approveHistoryVo.startDateStr) &&
        Objects.equals(this.endDateStr, approveHistoryVo.endDateStr) &&
        Objects.equals(this.ids, approveHistoryVo.ids) &&
        Objects.equals(this.isProductHidden, approveHistoryVo.isProductHidden);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, gspBusinessNo, businessNo, billNo, type, msg, username, status, initiatorName, createTime, createUser, updateTime, updateUser, yn, startDate, endDate, startDateStr, endDateStr, ids, isProductHidden);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ApproveHistoryVo {\n");

    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    gspBusinessNo: ").append(toIndentedString(gspBusinessNo)).append("\n");
    sb.append("    businessNo: ").append(toIndentedString(businessNo)).append("\n");
    sb.append("    billNo: ").append(toIndentedString(billNo)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    msg: ").append(toIndentedString(msg)).append("\n");
    sb.append("    username: ").append(toIndentedString(username)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    initiatorName: ").append(toIndentedString(initiatorName)).append("\n");
    sb.append("    createTime: ").append(toIndentedString(createTime)).append("\n");
    sb.append("    createUser: ").append(toIndentedString(createUser)).append("\n");
    sb.append("    updateTime: ").append(toIndentedString(updateTime)).append("\n");
    sb.append("    updateUser: ").append(toIndentedString(updateUser)).append("\n");
    sb.append("    yn: ").append(toIndentedString(yn)).append("\n");
    sb.append("    startDate: ").append(toIndentedString(startDate)).append("\n");
    sb.append("    endDate: ").append(toIndentedString(endDate)).append("\n");
    sb.append("    startDateStr: ").append(toIndentedString(startDateStr)).append("\n");
    sb.append("    endDateStr: ").append(toIndentedString(endDateStr)).append("\n");
    sb.append("    ids: ").append(toIndentedString(ids)).append("\n");
    sb.append("    isProductHidden: ").append(toIndentedString(isProductHidden)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

