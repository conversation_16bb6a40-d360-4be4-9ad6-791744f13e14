package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.web.api.module.product.model.InventoryVerifyVo;
import com.xyy.saas.web.api.module.product.model.InventoryVerifyVoList;
import io.swagger.annotations.ApiParam;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T17:19:18.933+08:00")

@Controller
public class FindVerifyVosApiController implements FindVerifyVosApi {



    public ResponseEntity<InventoryVerifyVoList> findVerifyVos(@ApiParam(value = "盘点计划单Vo" ,required=true )  @Valid @RequestBody InventoryVerifyVo inventoryVerifyVo) {
        // do some magic!
        return new ResponseEntity<InventoryVerifyVoList>(HttpStatus.OK);
    }

}
