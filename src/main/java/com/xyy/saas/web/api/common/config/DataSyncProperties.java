package com.xyy.saas.web.api.common.config;

import com.xyy.saas.common.datasync.api.DataSyncApi;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * 数据同步 Api properties
 * 
 * <AUTHOR> on 2018年9月14日
 */
@Configuration(value = "data-sync")
//@ConfigurationProperties(value = "data-sync")
public class DataSyncProperties {

	/**
	 * api map
	 * 
	 * 
	 */
	private Map<String, Class<? extends DataSyncApi<?>>> api;

	public Map<String, Class<? extends DataSyncApi<?>>> getApi() {
		return api;
	}

	public void setApi(Map<String, Class<? extends DataSyncApi<?>>> api) {
		this.api = api;
	}


	
}
