package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.national.core.api.SaasNationalMenuApi;
import com.xyy.saas.national.core.dto.*;
import com.xyy.saas.web.api.module.product.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T19:34:49.364+08:00")

@Slf4j
@Controller
public class YbmlProductApiController implements YbmlProductApi {

    @Reference(version = "0.0.1")
    private SaasNationalMenuApi saasNationalMenuApi;


    @Value("#{'${ybml.product.update.ignore.organSigns:}'.split(',')}")
    public List<String> ybmlProductUpdateIgnoreOrganSigns;

    @Override
    public ResponseEntity<ResultVO> matchClear(HttpServletRequest request, @RequestBody YbmlUpdateVo updateVo) {
        String organSign = request.getHeader("organSign");
        log.info("一键清除匹配关系, organSign:{}, params:{}", organSign, JSON.toJSONString(updateVo));
        SaasNationalMenuMatchClearOrSyncDto respDto;
        try {
            if (updateVo == null || 1 == updateVo.getAllFlag()) {
                // 全部
                respDto = saasNationalMenuApi.clearMatch(organSign, null);
            } else {
                if (CollectionUtils.isEmpty(updateVo.getPrefs())){
                    return new ResponseEntity(ResultVO.createError("未选择商品"),HttpStatus.OK);
                }
                // 指定商品
                respDto = saasNationalMenuApi.clearMatch(organSign, updateVo.getPrefs());
            }
        }catch (Exception e){
            log.error("一键清除匹配关系, organSign:{} ,error:{}", organSign, e.getMessage(), e);
            return new ResponseEntity(ResultVO.createError("系统异常"),HttpStatus.OK);
        }

        ResultVO resultVO = ResultVO.createSuccess();
        if (!StringUtils.isEmpty(respDto.getErrorMsg())) {
            resultVO.setCode(ResultCodeEnum.ERROR.getCode());
            resultVO.setMsg(respDto.getErrorMsg());
        }
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> matchSyncMedicare(HttpServletRequest request, @RequestBody YbmlUpdateVo updateVo) {
        String organSign = request.getHeader("organSign");
        log.info("一键同步POS匹配关系, organSign:{}, params:{}", organSign, JSON.toJSONString(updateVo));
        SaasNationalMenuMatchClearOrSyncDto respDto;
        try {
            if (updateVo == null || 1 == updateVo.getAllFlag()) {
                // 全部
                respDto = saasNationalMenuApi.syncMatchFromMedicare(organSign, null);
            } else {
                if (CollectionUtils.isEmpty(updateVo.getPrefs())){
                    return new ResponseEntity(ResultVO.createError("未选择商品"),HttpStatus.OK);
                }
                // 指定商品
                respDto = saasNationalMenuApi.syncMatchFromMedicare(organSign, updateVo.getPrefs());
            }
        }catch (Exception e){
            log.error("一键清除匹配关系 organSign:{} ,error:{}", organSign, e.getMessage(), e);
            return new ResponseEntity(ResultVO.createError("系统异常"),HttpStatus.OK);
        }

        ResultVO resultVO = ResultVO.createSuccess();
        if (!StringUtils.isEmpty(respDto.getErrorMsg())) {
            resultVO.setCode(ResultCodeEnum.ERROR.getCode());
            resultVO.setMsg(respDto.getErrorMsg());
        }
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> productYbmlMatchSubmit(HttpServletRequest request) {
        String organSign = request.getHeader("organSign");
        log.info("productYbmlMatchSubmit, organSign:{}", organSign);
        SaasNationalMenuQuickMatchResultDto respDto = saasNationalMenuApi.autoMatchSubmit(organSign);
        ResultVO resultVO = ResultVO.createSuccess();
        Map<String, Object> respMap = new HashMap<>();
        if (StringUtils.isEmpty(respDto.getErrorMsg())) {
            respMap.put("jobId", respDto.getJobId());
        } else {
            resultVO.setCode(ResultCodeEnum.ERROR.getCode());
            resultVO.setMsg(respDto.getErrorMsg());
        }
        resultVO.setResult(respMap);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> productYbmlMatchCancel(HttpServletRequest request, @RequestBody YbmlQuickMatchParamVo ybmlQuickMatchParamVo) {
        String organSign = request.getHeader("organSign");
        String jobId = ybmlQuickMatchParamVo.getJobId();
        log.info("productYbmlMatchCancel, organSign:{}, jobId:{}", organSign, jobId);
        saasNationalMenuApi.autoMatchCancel(organSign, jobId);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> productYbmlMatchResultQuery(HttpServletRequest request, @RequestBody YbmlQuickMatchParamVo ybmlQuickMatchParamVo) {
        String organSign = request.getHeader("organSign");
        String jobId = ybmlQuickMatchParamVo.getJobId();
        log.info("productYbmlMatchResultQuery, organSign:{}, jobId:{}", organSign, jobId);
        SaasNationalMenuQuickMatchResultDto resultDto = saasNationalMenuApi.autoMatchResultQuery(organSign, jobId);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(resultDto), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> ybmlList(HttpServletRequest request, @RequestBody YbmlListQueryVo ybmlListQueryVo) {
        String organSign = request.getHeader("organSign");
        log.info("ybmlList, organSign:{}, params:{}", organSign, JSON.toJSONString(ybmlListQueryVo));
        SaasNationalMenuQueryDto queryDto = new SaasNationalMenuQueryDto();
        queryDto.setOrganSign(organSign);
        queryDto.setRegisterName(ybmlListQueryVo.getRegisterName());
        queryDto.setManufacturer(ybmlListQueryVo.getManufacturer());
        queryDto.setSpecification(ybmlListQueryVo.getSpecification());
        queryDto.setPageNum(ybmlListQueryVo.getPage());
        queryDto.setPageSize(ybmlListQueryVo.getRows());
        queryDto.setApprovalNumber(ybmlListQueryVo.getApprovalNumber());
        PageInfo<SaasNationalCloudMenuDto> pageInfo = saasNationalMenuApi.findNationalMenuByParams(queryDto);
        return new ResponseEntity<ResultVO>(new ResultVO(pageInfo), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> productYbmlManualMatch(HttpServletRequest request, @RequestBody YbmlManualMatchParamVo ybmlManualMatchParamVo) {
        log.info("productYbmlManualMatch, organSign:{}, params:{}", request.getHeader("organSign"), JSON.toJSONString(ybmlManualMatchParamVo));
        if (StringUtils.isEmpty(ybmlManualMatchParamVo) || StringUtils.isEmpty(ybmlManualMatchParamVo.getProductPref())
                || ybmlManualMatchParamVo.getNationalId() == null) {
            log.error("==============参数不合法");
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, null),HttpStatus.OK);
        }
        SaasNationalMenuMatchParamDto nationalMenuMatchParamDto = new SaasNationalMenuMatchParamDto();
        nationalMenuMatchParamDto.setProductPref(ybmlManualMatchParamVo.getProductPref());
        nationalMenuMatchParamDto.setNationalId(ybmlManualMatchParamVo.getNationalId());
        saasNationalMenuApi.matchNationalMenu(nationalMenuMatchParamDto);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> querySummary(HttpServletRequest request, @RequestBody SaasNationalMenuSummaryQueryDto summaryQueryDto) {
        summaryQueryDto.setOrganSign(request.getHeader("organSign"));
        log.info("querySummary param:{}", JSON.toJSONString(summaryQueryDto));
        SaasNationalMenuSummaryDto summaryDto = saasNationalMenuApi.summary(summaryQueryDto);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(summaryDto), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> queryList(HttpServletRequest request, @RequestBody YbnlQueryListVo ybnlQueryListVo) {
        String organSign = request.getHeader("organSign");
        log.info("YbmlProductApiController#queryList vo:{}",ybnlQueryListVo);
        if (StringUtils.isEmpty(organSign)){
            log.error("YbmlProductApiController#queryList 参数不合法 organSign:{},YbnlQueryListVo:{}",organSign,ybnlQueryListVo);
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, null),HttpStatus.OK);
        }
        NationalQueryDto queryDto = new NationalQueryDto();
        BeanUtils.copyProperties(ybnlQueryListVo,queryDto);
        queryDto.setPage(ybnlQueryListVo.getPageNum());
        queryDto.setRows(ybnlQueryListVo.getPageSize());
        PageInfo<ProductNatioanlMenuDto> pageInfo = saasNationalMenuApi.queryList(queryDto, organSign);
        return new ResponseEntity<ResultVO>(new ResultVO(pageInfo), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> queryTaskId(HttpServletRequest request, String param) {
        String organSign = request.getHeader("organSign");
        if (StringUtils.isEmpty(organSign)){
            log.error("YbmlProductApiController#queryTaskId 参数不合法 organSign:{}",organSign);
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, null),HttpStatus.OK);
        }

        Long taskId = saasNationalMenuApi.getImportTaskId(organSign);
        return new ResponseEntity<ResultVO>(new ResultVO(taskId), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> updateProduct(HttpServletRequest request) {
        String organSign = request.getHeader("organSign");
        log.info("updateProduct, organSign:{}", organSign);
        ResultVO resultVO = ResultVO.createSuccess();
        if (CollectionUtils.isEmpty(ybmlProductUpdateIgnoreOrganSigns)
                || !ybmlProductUpdateIgnoreOrganSigns.contains(organSign)) {
            SaasNationalMenuMedicSyncDto respDto = saasNationalMenuApi.batchUpdateProductNationalDrugCodeAndStandardCode(organSign);
            if (!StringUtils.isEmpty(respDto.getErrorMsg())) {
                resultVO.setCode(ResultCodeEnum.ERROR.getCode());
                resultVO.setMsg(respDto.getErrorMsg());
            }
        }
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> batchSetYbType(HttpServletRequest request, @RequestBody YbmlUpdateVo updateVo) {
        String organSign = request.getHeader("organSign");
        log.info("YbmlProductApiController batchSetYbType, organSign:{}, params:{}", organSign, JSON.toJSONString(updateVo));
        if (updateVo.getMedicalInsurance() == null) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "没有指定是否医保"),HttpStatus.OK);
        }
        if (1 == updateVo.getAllFlag()) {
            String paramStr = updateVo.getParam();
            YbnlQueryListVo param = JSON.parseObject(paramStr,YbnlQueryListVo.class);
            NationalQueryDto queryDto = new NationalQueryDto();
            BeanUtils.copyProperties(param, queryDto);
            queryDto.setPage(1);
            queryDto.setRows(500);
            PageInfo<String> pageInfo = saasNationalMenuApi.queryMatchedPrefs(queryDto, organSign);
            Integer total = pageInfo.getPages();
            if (null == total || 0 >= total){
                return new ResponseEntity(ResultVO.createSuccess("成功"),HttpStatus.OK);
            }

            Integer pageSize = pageInfo.getPageSize();
            for (int i = 1; i <= total; i++) {
                NationalQueryDto pageParam = new NationalQueryDto();
                BeanUtils.copyProperties(param, pageParam);
                pageParam.setPage(i);
                pageParam.setRows(pageSize);
                PageInfo<String> subPage = saasNationalMenuApi.queryMatchedPrefs(queryDto, organSign);
                List<String> prefs = subPage.getList();;
                if (!CollectionUtils.isEmpty(prefs)) {
                    saasNationalMenuApi.batchUpdateMedicalInsurance(organSign, updateVo.getMedicalInsurance(), prefs);
                }
            }
        }else {
            if (CollectionUtils.isEmpty(updateVo.getPrefs())) {
                return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "未选择商品"),HttpStatus.OK);
            }
            saasNationalMenuApi.batchUpdateMedicalInsurance(organSign, updateVo.getMedicalInsurance(), updateVo.getPrefs());
        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> deleteMatch(HttpServletRequest request, @RequestBody YbmlUpdateVo updateVo) {
        String organSign = request.getHeader("organSign");
        try {
            if (1 == updateVo.getAllFlag()) {
                String paramStr = updateVo.getParam();
                YbnlQueryListVo param = JSON.parseObject(paramStr,YbnlQueryListVo.class);
                NationalQueryDto queryDto = new NationalQueryDto();
                BeanUtils.copyProperties(param, queryDto);
                queryDto.setPage(1);
                queryDto.setRows(500);
                List<Integer> status = new ArrayList<>();
                status.add(1);
                status.add(2);
                queryDto.setMatchStatusList(status);
                PageInfo<String> pageInfo = saasNationalMenuApi.queryMatchedPrefs(queryDto, organSign);
                Integer total = pageInfo.getPages();
                if (null == total || 0 >= total){
                    return new ResponseEntity(ResultVO.createSuccess("成功"),HttpStatus.OK);
                }

                Integer pageSize = pageInfo.getPageSize();
                for (int i = 1; i <= total; i++) {
                    NationalQueryDto pageParam = new NationalQueryDto();
                    BeanUtils.copyProperties(param, pageParam);
                    pageParam.setPage(i);
                    pageParam.setRows(pageSize);
                    PageInfo<String> subPage = saasNationalMenuApi.queryMatchedPrefs(queryDto, organSign);
                    List<String> prefs = subPage.getList();
                    if (!CollectionUtils.isEmpty(prefs)) {
                        saasNationalMenuApi.clearRelation(prefs);
                    }
                }
            }else {
                if (CollectionUtils.isEmpty(updateVo.getPrefs())){
                    return new ResponseEntity(ResultVO.createError("未选择商品"),HttpStatus.OK);
                }
                saasNationalMenuApi.clearRelation(updateVo.getPrefs());
            }
        }catch (Exception e){
            log.error("YbmlProductApiController#deleteMatch organSign:{} ,error:{}",organSign,e);
            return new ResponseEntity(ResultVO.createError("系统异常"),HttpStatus.OK);
        }
        return new ResponseEntity(ResultVO.createSuccess("成功"),HttpStatus.OK);
    }

}

