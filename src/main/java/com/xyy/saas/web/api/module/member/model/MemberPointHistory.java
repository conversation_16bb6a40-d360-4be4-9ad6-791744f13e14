package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * 会员积分变动历史
 * <AUTHOR>
 */
@ApiModel(description = "会员积分变动历史")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-06T17:16:06.377+08:00")
public class MemberPointHistory   {
  private Long id = null;

  private Long memberId = null;

  private Integer operatorType = null;

  private BigDecimal operatorPoint = null;

  private String businessNo = null;

  private BigDecimal currentPoint = null;

  private String createTime = null;

  private String updateUser = null;

  private String updateTime = null;

  private Integer yn = null;

  private Integer vipLevelId = null;

  private BigDecimal priorPoint = null;

  private String editReason = null;

  private Integer baseVersion = null;

  private String organsign = null;

  private Integer pageNum ;

  private Integer pageSize;

  private String guid;

  private String mixedQuery; // 会员信息

  private String startDate;

  private String endDate;

  public MemberPointHistory id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * id
   * @return id
  **/
  @ApiModelProperty(value = "id")


  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public MemberPointHistory memberId(Long memberId) {
    this.memberId = memberId;
    return this;
  }

   /**
   * 会员id
   * @return memberId
  **/
  @ApiModelProperty(value = "会员id")


  public Long getMemberId() {
    return memberId;
  }

  public void setMemberId(Long memberId) {
    this.memberId = memberId;
  }

  public MemberPointHistory operatorType(Integer operatorType) {
    this.operatorType = operatorType;
    return this;
  }

   /**
   * 操作类型 1 增加积分 2 兑换 3 管理员手动加 4 管理员手动减
   * @return operatorType
  **/
  @ApiModelProperty(value = "操作类型 1 增加积分 2 兑换 3 管理员手动加 4 管理员手动减")


  public Integer getOperatorType() {
    return operatorType;
  }

  public void setOperatorType(Integer operatorType) {
    this.operatorType = operatorType;
  }

  public MemberPointHistory operatorPoint(BigDecimal operatorPoint) {
    this.operatorPoint = operatorPoint;
    return this;
  }

   /**
   * 操作积分数
   * @return operatorPoint
  **/
  @ApiModelProperty(value = "操作积分数")


  public BigDecimal getOperatorPoint() {
    return operatorPoint;
  }

  public void setOperatorPoint(BigDecimal operatorPoint) {
    this.operatorPoint = operatorPoint;
  }

  public MemberPointHistory businessNo(String businessNo) {
    this.businessNo = businessNo;
    return this;
  }

   /**
   * 业务号 
   * @return businessNo
  **/
  @ApiModelProperty(value = "业务号 ")


  public String getBusinessNo() {
    return businessNo;
  }

  public void setBusinessNo(String businessNo) {
    this.businessNo = businessNo;
  }

  public MemberPointHistory currentPoint(BigDecimal currentPoint) {
    this.currentPoint = currentPoint;
    return this;
  }

   /**
   * 操作完后的积分
   * @return currentPoint
  **/
  @ApiModelProperty(value = "操作完后的积分")


  public BigDecimal getCurrentPoint() {
    return currentPoint;
  }

  public void setCurrentPoint(BigDecimal currentPoint) {
    this.currentPoint = currentPoint;
  }

  public MemberPointHistory createTime(String createTime) {
    this.createTime = createTime;
    return this;
  }

   /**
   * 创建时间
   * @return createTime
  **/
  @ApiModelProperty(value = "创建时间")


  public String getCreateTime() {
    return createTime;
  }

  public void setCreateTime(String createTime) {
    this.createTime = createTime;
  }

  public MemberPointHistory updateUser(String updateUser) {
    this.updateUser = updateUser;
    return this;
  }

   /**
   * 更新人
   * @return updateUser
  **/
  @ApiModelProperty(value = "更新人")


  public String getUpdateUser() {
    return updateUser;
  }

  public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
  }

  public MemberPointHistory updateTime(String updateTime) {
    this.updateTime = updateTime;
    return this;
  }

   /**
   * 更新时间
   * @return updateTime
  **/
  @ApiModelProperty(value = "更新时间")


  public String getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(String updateTime) {
    this.updateTime = updateTime;
  }

  public MemberPointHistory yn(Integer yn) {
    this.yn = yn;
    return this;
  }

   /**
   * 是否删除 1 有效 0 删除
   * @return yn
  **/
  @ApiModelProperty(value = "是否删除 1 有效 0 删除")


  public Integer getYn() {
    return yn;
  }

  public void setYn(Integer yn) {
    this.yn = yn;
  }

  public MemberPointHistory vipLevelId(Integer vipLevelId) {
    this.vipLevelId = vipLevelId;
    return this;
  }

   /**
   * 机构标识
   * @return vipLevelId
  **/
  @ApiModelProperty(value = "机构标识")


  public Integer getVipLevelId() {
    return vipLevelId;
  }

  public void setVipLevelId(Integer vipLevelId) {
    this.vipLevelId = vipLevelId;
  }

  public MemberPointHistory priorPoint(BigDecimal priorPoint) {
    this.priorPoint = priorPoint;
    return this;
  }

   /**
   * 上期结余积分
   * @return priorPoint
  **/
  @ApiModelProperty(value = "上期结余积分")


  public BigDecimal getPriorPoint() {
    return priorPoint;
  }

  public void setPriorPoint(BigDecimal priorPoint) {
    this.priorPoint = priorPoint;
  }

  public MemberPointHistory editReason(String editReason) {
    this.editReason = editReason;
    return this;
  }

   /**
   * 积分变动原因
   * @return editReason
  **/
  @ApiModelProperty(value = "积分变动原因")
  public String getEditReason() {
    return editReason;
  }

  public void setEditReason(String editReason) {
    this.editReason = editReason;
  }

  public MemberPointHistory baseVersion(Integer baseVersion) {
    this.baseVersion = baseVersion;
    return this;
  }

   /**
   * 版本号
   * @return baseVersion
  **/
  @ApiModelProperty(value = "版本号")


  public Integer getBaseVersion() {
    return baseVersion;
  }

  public void setBaseVersion(Integer baseVersion) {
    this.baseVersion = baseVersion;
  }

  public MemberPointHistory organsign(String organsign) {
    this.organsign = organsign;
    return this;
  }

   /**
   * 机构标识
   * @return organsign
  **/
  @ApiModelProperty(value = "机构标识")
  public String getOrgansign() {
    return organsign;
  }

  public void setOrgansign(String organsign) {
    this.organsign = organsign;
  }

  public String getGuid() {
    return guid;
  }

  public void setGuid(String guid) {
    this.guid = guid;
  }

  @ApiModelProperty(value = "当前页")
  public Integer getPageNum() {
    return pageNum;
  }

  public void setPageNum(Integer pageNum) {
    this.pageNum = pageNum;
  }

  public String getMixedQuery() {
    return mixedQuery;
  }

  public void setMixedQuery(String mixedQuery) {
    this.mixedQuery = mixedQuery;
  }

  @ApiModelProperty(value = "每页大小")
  public Integer getPageSize() {
    return pageSize;
  }

  public void setPageSize(Integer pageSize) {
    this.pageSize = pageSize;
  }

  public String getStartDate() {
    return startDate;
  }

  public void setStartDate(String startDate) {
    this.startDate = startDate;
  }

  public String getEndDate() {
    return endDate;
  }

  public void setEndDate(String endDate) {
    this.endDate = endDate;
  }
}

