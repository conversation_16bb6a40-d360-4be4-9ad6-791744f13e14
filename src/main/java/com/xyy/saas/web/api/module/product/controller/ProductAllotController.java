package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.xyy.saas.common.util.MD5Util;
import com.xyy.saas.jm.common.enums.ResultCodeEnum;
import com.xyy.saas.jm.common.result.ResultVO;
import com.xyy.saas.jm.foundation.core.api.StoreApi;
import com.xyy.saas.jm.foundation.core.vo.BranchStoreListVo;
import com.xyy.saas.jm.productstock.core.api.AllocateApi;
import com.xyy.saas.jm.productstock.core.api.JmInventoryApi;
import com.xyy.saas.jm.productstock.core.api.OutProductApi;
import com.xyy.saas.jm.productstock.core.dto.*;
import com.xyy.saas.jm.productstock.core.dto.requ.JmAllocateInventoryDto;
import com.xyy.saas.jm.productstock.core.dto.requ.JmAllocateInventoryItemDto;
import com.xyy.saas.jm.productstock.core.enums.JmAllocateInventoryTypeEnum;
import com.xyy.saas.jm.productstock.core.vo.*;
import com.xyy.saas.product.core.dto.ProductDto;
import com.xyy.saas.product.core.dto.ProductInventoryAllDto;
import com.xyy.saas.web.api.module.product.model.ProductStockModel;
import com.xyy.saas.web.api.module.utils.JedisUtils;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import com.xyy.user.module.dto.result.EmployeeDto;
import com.xyy.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Api(tags = {"商品库存服务-调拨单API"}, description = "AllocatController", produces = MediaType.APPLICATION_JSON_VALUE)
@RestController
@RequestMapping("/product/productstock/allocate")
public class ProductAllotController {


    private static final Logger logger = LoggerFactory.getLogger(ProductAllotController.class);

    private static final int EXPIRE_TIME = 30000;

    @Reference(version = "0.0.1")
    private AllocateApi allocateApi;

    @Reference(version = "0.0.1")
    private JmInventoryApi jmInventoryApi;

    @Reference(version = "0.0.1")
    private OutProductApi outProductApi;

    @Reference(version = "0.0.1")
    private StoreApi storeApi;

    @Reference(version = "0.0.1")
    private EmployeeApi employeeApi;

    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;
    @Autowired
    private JedisUtils jedisUtil;

    @ApiOperation(value = "根据GUID获取调拨单信息", notes = "根据GUID获取调拨单信息", tags = {"商品库存服务-调拨单API",})
    @PostMapping("/getAllocateByGuid")
    public ResultVO<AllocateVo> getAllocateByGuid(HttpServletRequest request, @RequestBody GuidDto paramDto) {
        // paramDto.setGuid("");
        logger.info("paramDto = ", JSONObject.toJSONString(paramDto));

        return allocateApi.getAllocateByGuid(paramDto);
    }

    @ApiOperation(value = "出库审核", notes = "出库审核", response = AllocateVo.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping("/outBillAudit")
    public ResultVO<AllocateVo> outBillAudit(@RequestHeader(name = "organSign", required = true) String organSign, @RequestHeader(name = "employeeId", required = true) Integer employeeId, @RequestBody JmAllocateAuditDto dto) {
        logger.info("出库审核入参 = " + JSONObject.toJSONString(dto));
        String allocateBillGuid = dto.getAllocateBillGuid();
        if (StringUtils.isEmpty(allocateBillGuid)) {
            ResultVO.error(ResultCodeEnum.PARAM_ERROR);
        }

        //验证密码
        com.xyy.saas.common.util.ResultVO<EmployeeDto> resultVO = employeeApi.queryEmployeeById(employeeId);
        if (resultVO == null || resultVO.getCode() != 0) {
            logger.info("调用用户模块出错用户id:{}", employeeId);
        }
        logger.info("出库审核密码 = " + JSONObject.toJSONString(resultVO));
        if (!resultVO.getResult().getPassword().equals(MD5Util.getMD5(dto.getLoginPassword()))) {
            return ResultVO.error(ResultCodeEnum.PASSWORD_ERROR);
        }
        dto.setName(resultVO.getResult().getName());
        dto.setUserId(employeeId);
        return outProductApi.outBillAudit(dto);
    }

    @ApiOperation(value = "出库获取商品下面的【批号库存】信息", notes = "出库获取商品下面的【批号库存】信息", response = AllocateVo.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping("/queryLotNumList")
    public ResultVO<AllocateVo> queryLotNumList(HttpServletRequest request, @RequestBody QueryLotNumListDto dto) {
        logger.info("出库获取商品下面的【批号库存】信息 入参= " + JSONObject.toJSONString(dto));

        return outProductApi.queryLotNumList(dto);
    }

    @ApiOperation(value = "出库调拨单详情页", notes = "出库调拨单详情页", response = JmAuditBillItemVo.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping("/outBillList")
    public ResultVO outBillList(@RequestBody JmAllocateAuditListDto dto) {
        logger.info("出库调拨单详情页入参 = " + JSONObject.toJSONString(dto));

        return outProductApi.outBillList(dto);
    }

    @ApiOperation(value = "出库同步", notes = "出库同步", response = ResultVO.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping("/outBillSync")
    public ResultVO outBillSync(@RequestHeader(name = "organSign", required = true) String organSign, @RequestHeader(name = "employeeId", required = true) Integer employeeId, @RequestBody JmAllocateSyncDto dto) {
        logger.info("出库同步入参 = " + JSONObject.toJSONString(dto) + "当前登录药店机构:" + organSign + "当前登录账号" + employeeId);
        //根据employeeApi查询此机构下的所有员工
        String guid = dto.getGuid();
        dto.setOperatorUser(employeeId.toString());
        if (StringUtils.isEmpty(guid)) {
            ResultVO.error(ResultCodeEnum.PARAM_ERROR);
        }

        return outProductApi.outBillSync(dto);
    }

    @ApiOperation(value = "出库标准库列表", notes = "出库标准库列表", response = StandLibraryVo.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping("/findStandLibraryPage")
    public ResultVO findStandLibraryPage(@RequestBody QueryStandardDto dto) {
        logger.info("出库标准库列表入参 = " + JSONObject.toJSONString(dto));
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(dto.getPage());
        pageInfo.setPageSize(dto.getRows());
        PageInfo standLibraryPage = outProductApi.findStandLibraryPage(pageInfo, dto);
        List standLibraryPageList = standLibraryPage.getList();
        return ResultVO.success(ResultCodeEnum.QUERY_SUCCESS.getMsg(), standLibraryPage);
    }

    @ApiOperation(value = "出库标准库详情", notes = "出库标准库详情", response = StandLibraryVo.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping("/findStandLibraryById")
    public ResultVO findStandLibraryById(@RequestBody StandLibraryByIdDto dto) {
        logger.info("出库标准库详情入参 = " + JSONObject.toJSONString(dto));

        return outProductApi.findStandLibraryById(dto.getStandardLibraryId());
    }

    @ApiOperation(value = "出库匹配标准库商品列表", notes = "出库匹配标准库商品列表", response = StandLibraryProductListVo.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping("/findStandLibraryProductList")
    public ResultVO findStandLibraryProductList(@RequestBody StandLibraryProductListDto dto) {
        logger.info("出库匹配标准库商品列表入参 = " + JSONObject.toJSONString(dto));
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(dto.getPage());
        pageInfo.setPageSize(dto.getRows());
        PageInfo standLibraryProductList = outProductApi.findStandLibraryProductList(pageInfo, dto);
        return ResultVO.success(ResultCodeEnum.QUERY_SUCCESS.getMsg(), standLibraryProductList);
    }

    @ApiOperation(value = "出库标准库商品匹配", notes = "出库标准库商品匹配", response = ResultVO.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping("/findStandLibraryProductMate")
    public ResultVO findStandLibraryProductMate(@RequestBody StandLibraryProductMateDto dto) {
        logger.info("出库标准库商品匹配入参 = " + JSONObject.toJSONString(dto));

        return outProductApi.findStandLibraryProductMate(dto);
    }

    @ApiOperation(value = "标准库匹配-查询当前机构商品列表信息", notes = "标准库匹配-查询当前机构商品列表信息", response = ResultVO.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping(value = "/getOwnerProductList")
    public ResultVO getOwnerProductList(@RequestHeader(name = "organSign", required = true) String organSign, @ApiParam(required = true, value = "请求参数") @RequestBody ProductDto productDto) {
        productDto.setOrganSign(organSign);
        logger.info("解析token得到的organSign:" + productDto.getOrganSign());
        PageInfo productPage = allocateApi.getOwnerProductList(productDto);
        logger.info("getOwnerProductList - 出参：" + new Gson().toJson(productPage));
        return ResultVO.success(ResultCodeEnum.QUERY_SUCCESS.getMsg(), productPage);
    }

    @ApiOperation(value = "查询调拨单信息", notes = "查询调拨单信息", response = ResultVO.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping(value = "/getAllocate")
    public ResultVO getAllocate(@RequestHeader(name = "organSign", required = true) String organSign, @RequestHeader(name = "employeeId", required = true) Integer employeeId, @ApiParam(required = true, value = "请求参数") @RequestBody JmAllocateBillDto billDto) {
        logger.info("getAllocate入参：" + new Gson().toJson(billDto));
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(billDto.getPage());
        pageInfo.setPageSize(billDto.getRows());
        billDto.setOrganSign(organSign);// 登录的单店的机构唯一标识
        billDto.setCreateUser(String.valueOf(employeeId));
        logger.info("billDto：" + JSONObject.toJSONString(billDto));
        PageInfo productPage = allocateApi.getAllocate(pageInfo, billDto);
        logger.info("getAllocate出参：" + new Gson().toJson(productPage));
        return ResultVO.success(ResultCodeEnum.QUERY_SUCCESS.getMsg(), productPage);
    }

    @ApiOperation(value = "创建调拨单", notes = "创建调拨单", response = ResultVO.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping(value = "/saveAllocate")
    public ResultVO saveAllocate(@RequestHeader(name = "organSign", required = true) String organSign, @RequestHeader(name = "employeeId", required = true) Integer employeeId, @ApiParam(required = true, value = "调拨单信息") @RequestBody String data) {
        logger.info("保存调拨单信息{}", data);
        if (StringUtils.isEmpty(data)) {
            return ResultVO.error(ResultCodeEnum.PARAM_ERROR);
        }
        Gson gson = new Gson();
        Map<String, Object> jsonMap = gson.fromJson(data, Map.class);
        if (!jsonMap.containsKey("data")) {
            return ResultVO.error(ResultCodeEnum.PARAM_ERROR);
        } else {
            Map<String, Object> dataMap = (Map<String, Object>) jsonMap.get("data");
            if (!dataMap.containsKey("baseInfo") || !dataMap.containsKey("productInfo")) {
                return ResultVO.error(ResultCodeEnum.PARAM_ERROR);
            }
        }
        //查询当前登录机构
        SaaSDrugstoreDto drugstoreByOrganSign = drugstoreApi.getDrugstoreByOrganSign(organSign);
        Integer result = allocateApi.saveAllocate(data, drugstoreByOrganSign.getDrugstoreName(), organSign, String.valueOf(employeeId));
        return ResultVO.success(ResultCodeEnum.SAVE_SUCCESS.getMsg(), result);
    }

    @ApiOperation(value = "调拨单预览", notes = "调拨单预览", response = ResultVO.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping(value = "/allocateDetailInfo")
    public ResultVO allocateDetailInfo(HttpServletRequest request,
                                       @ApiParam(required = true, value = "单据编号") @RequestBody JmAllocateBillDto jmAllocateBillDto) {
        logger.info("allocateDetailInfo入参:" + new Gson().toJson(jmAllocateBillDto));
        String billNo = jmAllocateBillDto.getBillNo();
        Integer page = jmAllocateBillDto.getPage();
        Integer rows = jmAllocateBillDto.getRows();
        Map<String, Object> resultMap = Maps.newHashMap();
        Map<String, Object> billMap = Maps.newHashMap();
        if (StringUtils.isEmpty(billNo) || page == null || rows == null || jmAllocateBillDto.getType() == null) {
            return ResultVO.error(ResultCodeEnum.PARAM_ERROR);
        }
        JmAllocateBillDto billDto = new JmAllocateBillDto();
        billDto.setBillNo(billNo);
        // 基本信息
        PageInfo allocatePageInfo = allocateApi.getAllocate(null, billDto);
        List<AllocateVo> allocateVo = allocatePageInfo.getList();
        if (!CollectionUtils.isEmpty(allocateVo) && allocateVo.size() > 0) {

            billMap.put("baseInfo", allocateVo.get(0));
        }
        // 调拨商品信息
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(rows);
        pageInfo.setPageNum(page);
        JmAllocateBillItemDto billItemDto = new JmAllocateBillItemDto();
        billItemDto.setAllocateBillNo(billNo);
        // Integer type = jmAllocateBillDto.getType();
        Integer type = 1;
        PageInfo itemVosPageInfo = allocateApi.getAllocateItemBatch(pageInfo, billItemDto, type);
        List<AllocateItemVo> itemVos = itemVosPageInfo.getList();
        BigDecimal pageSum = BigDecimal.ZERO;
        for (AllocateItemVo itemVo : itemVos) {
            pageSum = pageSum.add(itemVo.getProductPriceSum() == null ? BigDecimal.ZERO : itemVo.getProductPriceSum());
        }
        billMap.put("pageSum", pageSum);
        billMap.put("productInfo", itemVosPageInfo);
        resultMap.put("data", billMap);
        logger.info("allocateDetailInfo出参:" + new Gson().toJson(resultMap));
        return ResultVO.success(ResultCodeEnum.QUERY_SUCCESS.getMsg(), resultMap);
    }

    @ApiOperation(value = "查询商品详情信息[下拉框内容]", notes = "查询商品详情信息[下拉框内容", response = ResultVO.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping(value = "/getProductDetailSelect")
    public ResultVO getProductDetailSelect(@RequestHeader(name = "organSign", required = true) String organSign) {
        logger.info("查询商品详情信息[下拉框内容]，organSign：{}", organSign);
        ResultVO resultVO = allocateApi.getProductDetailSelect(0, organSign);
        return resultVO;
    }

    @ApiOperation(value = "查询商品详情信息", notes = "查询商品详情信息", response = ResultVO.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping(value = "/getProductDetailInfo")
    public ResultVO getProductDetailInfo(@RequestHeader(name = "organSign", required = true) String organSign,
                                         @ApiParam(value = "请求参数封装", required = true) @RequestBody ProductStockModel productStockModel) {
        logger.info("getProductDetailInfo入参:" + new Gson().toJson(productStockModel));
        if (StringUtils.isEmpty(productStockModel.getOrganSign())) {
            productStockModel.setOrganSign(organSign);
        }
        if (productStockModel.getProductId() == null) {
            return ResultVO.error(ResultCodeEnum.PARAM_ERROR);
        }
        /*if (productStockModel.getType() == null) {
            productStockModel.setType(0);
        }*/
        logger.info("查询商品详情信息，organSign：{}, 商品id:{}", organSign, productStockModel.getProductId());
        // 获取登录用户信息
        // BaseController baseController = new BaseController();
        // UserVo userVo = getCurrentUser();
        ProductDto productDto = allocateApi.getProductDetailInfo(productStockModel.getProductId(), productStockModel.getOrganSign());
        logger.info("getProductDetailInfo出参:" + new Gson().toJson(productDto));
        return ResultVO.success(ResultCodeEnum.QUERY_SUCCESS.getMsg(), productDto);
    }

    @ApiOperation(value = "查询需要匹配标准库的商品信息", notes = "查询需要匹配标准库的商品信息", response = ResultVO.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping(value = "/getNeedStandardLibraryIdProduct")
    public ResultVO getNeedStandardLibraryIdProduct(@RequestHeader(name = "organSign", required = true) String organSign,
                                                    @ApiParam(required = true, value = "请求参数信息") @RequestBody JmAllocateBillItemDto billItemDto) {
        logger.info("getNeedStandardLibraryIdProduct入参:" + new Gson().toJson(billItemDto));
        if (StringUtils.isEmpty(organSign) || StringUtils.isEmpty(billItemDto.getAllocateBillNo()) || null == billItemDto.getPage() || null == billItemDto.getRows()) {
            return ResultVO.error(ResultCodeEnum.PARAM_ERROR);
        }
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(billItemDto.getPage());
        pageInfo.setPageSize(billItemDto.getRows());
        // 查询此单据编号下的所有调拨商品
        PageInfo itemVosPageInfo = allocateApi.getAllocateItem(null, billItemDto);
        List<AllocateItemVo> allocateItemVos = itemVosPageInfo.getList();
        List<AllocateItemVo> pageAllocateItemVos = Lists.newArrayList();
        // 如果不为空需要，需要查询入库门店的商品信息
        ProductDto productDto = new ProductDto();
        productDto.setPage(billItemDto.getPage());
        productDto.setRows(billItemDto.getRows());
        productDto.setOrganSign(organSign);
        PageInfo productPage = allocateApi.getOwnerProductList(productDto);
        // 遍历调拨商品
        for (AllocateItemVo item : allocateItemVos) {
            // 如果此商品标准库ID为空或为0，那肯定在需要匹配范围内
            if ((item.getStandardLibraryId() == null || item.getStandardLibraryId() == 0) && StringUtils.isEmpty(item.getInStoreProductPref())) {
                pageAllocateItemVos.add(item);
            } else {
                List<ProductVo> productVos = productPage.getList();
                // 如果productVos中含有相同标准库id商品，则不在匹配列表中
                List<ProductVo> filterList = productVos.stream().filter(productVo -> productVo.getStandardLibraryId() == item.getStandardLibraryId()).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(filterList)) {
                    if (StringUtils.isEmpty(item.getInStoreProductPref())) {
                        pageAllocateItemVos.add(item);
                    }
                } else {
                    // 找到匹配的商品，更新调拨商品信息的in_store_product_pref字段
                    ProductVo productVo = filterList.get(0);
                    String pref = productVo.getPref();
                    // 更新in_store_product_pref字段
                    JmAllocateBillItemDto billItemDtoUpdate = new JmAllocateBillItemDto();
                    billItemDtoUpdate.setGuid(item.getGuid());
                    billItemDtoUpdate.setAllocateBillNo(billItemDto.getAllocateBillNo());
                    billItemDtoUpdate.setInStoreProductPref(pref);
                    Integer result = allocateApi.updateAllocateBillItemByBillNo(billItemDtoUpdate);
                    if (result != 0) {
                        return ResultVO.error("guid：" + item.getGuid() + "商品更新商品外码异常");
                    }
                }
            }
        }
        // 手工分页
        int page = billItemDto.getPage();
        int rows = billItemDto.getRows();
        int size = pageAllocateItemVos.size();
        PageInfo resultPage = new PageInfo();
        resultPage.setTotal(size);
        Integer total = size;
        int pages = total / rows;
        if (total % rows == 0) {
            resultPage.setPages(pages);
        } else {
            resultPage.setPages(pages + 1);
        }
        resultPage.setList(pageAllocateItemVos.subList(page < 0 ? 0 : rows * (page - 1), rows * page > size ? size : rows * page));
        logger.info("getNeedStandardLibraryIdProduct出参:" + new Gson().toJson(resultPage));
        return ResultVO.success(ResultCodeEnum.QUERY_SUCCESS.getMsg(), resultPage);
    }

    @ApiOperation(value = "查询可调拨商品列表信息", notes = "查询可调拨商品列表信息", response = ResultVO.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping(value = "/getAllocateProductInfo")
    public ResultVO getAllocateProductInfo(HttpServletRequest request,
                                           @ApiParam(required = true, value = "请求参数信息") @RequestBody ProductDto paramDto) {
        logger.info("getAllocateProductInfo入参:" + new Gson().toJson(paramDto));
        if (StringUtils.isEmpty(paramDto.getOrganSign()) || paramDto.getPage() == null || paramDto.getRows() == null) {
            return ResultVO.error(ResultCodeEnum.PARAM_ERROR);
        }
        ProductInventoryAllDto productInventoryAllDto = new ProductInventoryAllDto();
        productInventoryAllDto.setPageNum(paramDto.getPage());
        productInventoryAllDto.setPageSige(paramDto.getRows());
        productInventoryAllDto.setManufacturer(paramDto.getManufacturer());
        productInventoryAllDto.setBarCode(paramDto.getBarCode());
        productInventoryAllDto.setOrganSign(paramDto.getOrganSign());
        productInventoryAllDto.setCommonName(paramDto.getCommonName());
        PageInfo<ProductInventoryAllDto> pageInfo = allocateApi.getAllocateProductInfo(productInventoryAllDto);
        logger.info("getAllocateProductInfo出参:" + new Gson().toJson(pageInfo));
        return ResultVO.success(ResultCodeEnum.QUERY_SUCCESS.getMsg(), pageInfo);
    }

    @ApiOperation(value = "标准库匹配", notes = "标准库匹配", response = ResultVO.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping(value = "/productMatching")
    public ResultVO productMatching(HttpServletRequest request,
                                    @ApiParam(required = true, value = "调拨单据编号") @RequestBody JmAllocateBillItemDto itemDto) {
        logger.info("productMatching入参:" + new Gson().toJson(itemDto));
        Integer result = allocateApi.updateAllocateBillItemByBillNo(itemDto);
        return ResultVO.success(ResultCodeEnum.UPDATE_SUCCESS.getMsg(), result);
    }

    @ApiOperation(value = "调拨单撤回", notes = "调拨单撤回", response = ResultVO.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping(value = "/callBack")
    public ResultVO callBack(HttpServletRequest request,
                             @ApiParam(required = true, value = "调拨单据编号") @RequestBody ProductStockModel model) {
        String billNo = model.getBillNo();
        logger.info("callBack入参", billNo);
        JmAllocateBillDto jmAllocateBillDto = new JmAllocateBillDto();
        jmAllocateBillDto.setBillNo(billNo);
        jmAllocateBillDto.setStatus(3);
        jmAllocateBillDto.setYn(0);
        Integer result = allocateApi.updateAllocateByBillNO(jmAllocateBillDto);
        return ResultVO.success(ResultCodeEnum.UPDATE_SUCCESS.getMsg(), result);
    }

    @ApiOperation(value = "入库同步", notes = "入库同步", response = ResultVO.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping(value = "/warehouseingSync")
    public ResultVO warehouseingSync(@RequestHeader(name = "organSign", required = true) String organSign, @RequestHeader(name = "employeeId", required = true) Integer employeeId,
                                     @ApiParam(required = true, value = "调拨单据信息") @RequestBody JmAllocateBillDto jmAllocateBillDto) {
        logger.info("warehouseingSync入参:" + new Gson().toJson(jmAllocateBillDto));
        String billNo = jmAllocateBillDto.getBillNo();
        // 判断必填参数是否已填
        if (StringUtils.isEmpty(billNo)) {
            return ResultVO.error(ResultCodeEnum.PARAM_ERROR);
        }

        JmAllocateInventoryDto jmAllocateInventoryDto = new JmAllocateInventoryDto();
        //根据employeeApi查询此机构下的所有员工
        jmAllocateInventoryDto.setOperatorUser(String.valueOf(employeeId));

        // 查询调拨单据商品信息
        JmAllocateBillItemDto billItemDto = new JmAllocateBillItemDto();
        billItemDto.setAllocateBillNo(jmAllocateBillDto.getBillNo());
        PageInfo itemVosPageInfo = allocateApi.getAllocateItemBatch(null, billItemDto, 1);
        List<AllocateItemVo> itemVos = itemVosPageInfo.getList();
        // 商品库存入库同步
        jmAllocateInventoryDto.setBillNo(jmAllocateBillDto.getBillNo());
        jmAllocateInventoryDto.setJmAllocateInventoryTypeEnum(JmAllocateInventoryTypeEnum.in);
        List<JmAllocateInventoryItemDto> jmAllocateInventoryItemDtoList = Lists.newArrayList();
        for (AllocateItemVo item : itemVos) {
            JmAllocateInventoryItemDto inventoryItemDto = new JmAllocateInventoryItemDto();
            // 入库药店
            inventoryItemDto.setOrgansign(organSign);
            // 调拨数量
            inventoryItemDto.setProductAmount(item.getProductAmount());
            // 批号
            inventoryItemDto.setProductBatchNo(item.getProductBatchNo());
            // guid
            inventoryItemDto.setBillItemBatchId(item.getGuid());
            // 最后一次供应商
            inventoryItemDto.setLastProvidePref(item.getLastProvidePref());
            if (StringUtils.isEmpty(item.getInStoreProductPref())) {
                return ResultVO.error("调拨单中有商品未进行标准库匹配，请先匹配");
            }
            // 商品编号
            inventoryItemDto.setProductCode(StringUtils.isEmpty(item.getInStoreProductPref()) ? "" : item.getInStoreProductPref());
            BigDecimal productPrice = item.getProductPrice();
            inventoryItemDto.setOutPcostPrice(productPrice);
            inventoryItemDto.setLastProvidePref(item.getLastProvidePref());
            //根据架位-出库机构
            inventoryItemDto.setProductDate(item.getProductDate());
            inventoryItemDto.setProductExpiryDate(item.getProductExpiryDate());
            jmAllocateInventoryItemDtoList.add(inventoryItemDto);
        }
        jmAllocateInventoryDto.setJmAllocateInventoryItemDtoList(jmAllocateInventoryItemDtoList);
        ResultVO resultVO = jmInventoryApi.syncInInventory(jmAllocateInventoryDto);
        logger.info("warehouseingSync出参:" + new Gson().toJson(resultVO));
        return resultVO;
    }

    @ApiOperation(value = "开票人/审核人列表", notes = "开票人/审核人列表", response = ResultVO.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping(value = "/getEmployees")
    public ResultVO getEmployees(@RequestHeader(name = "organSign", required = true) String organSign, @RequestHeader(name = "employeeId", required = true) Integer employeeId) {
        //获取全部采购员
        com.xyy.saas.common.util.ResultVO<EmployeeDto> employeeDtoResultVO = employeeApi.queryEmployeeById(employeeId);
        List<Map<String, Object>> employees = allocateApi.getEmployees(organSign, employeeDtoResultVO.getResult().getName());
        return ResultVO.success(ResultCodeEnum.QUERY_SUCCESS.getMsg(), employees);
    }

    @ApiOperation(value = "联营体商品品类和商品总货值", notes = "联营体商品品类和商品总货值", response = ResultVO.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping(value = "/allStoresProductKindsAndValue")
    public ResultVO allStoresProductKindsAndValue(HttpServletRequest request,
                                                  @ApiParam(required = true, value = "商品库存信息") @RequestBody ProductStockModel productStockModel) {

        String mainStoreGuid = productStockModel.getMainStoreGuid();
        ResultVO<List<BranchStoreListVo>> branchStoreList = storeApi.getBranchStoreList(mainStoreGuid);
        List<String> paramsList = new ArrayList<>();
        //
        HashMap<String, String> organSignAndNameMap = new HashMap<>();
        if (branchStoreList.getResult() != null && branchStoreList.getResult().size() > 0) {
            for (BranchStoreListVo branchStoreListVo : branchStoreList.getResult()) {
                String organSign = branchStoreListVo.getOrganSign();
                paramsList.add(organSign);
                organSignAndNameMap.put(branchStoreListVo.getOrganSign(), branchStoreListVo.getDrugstoreName());
            }
        }
        ResultVO<JmProductKindsAndValuesDto> jmProductKindsAndValuesDtoResultVO = new ResultVO<>();
        if (paramsList.size() > 0) {
            jmProductKindsAndValuesDtoResultVO = jmInventoryApi.queryKindsAndValues(paramsList);
            if (jmProductKindsAndValuesDtoResultVO.getCode() == 0) {
                JmProductKindsAndValuesDto result = jmProductKindsAndValuesDtoResultVO.getResult();
                List<SingleStoreKindAndValueDto> singleStores = result.getSingleStores();
                if (singleStores != null && singleStores.size() > 0) {
                    for (SingleStoreKindAndValueDto singleStoreKindAndValueDto : singleStores) {
                        String storeName = organSignAndNameMap.get(singleStoreKindAndValueDto.getOrganSign());
                        singleStoreKindAndValueDto.setStoreName(storeName);
                    }
                }
            }
        } else {
            jmProductKindsAndValuesDtoResultVO.setCode(0);
        }
        return jmProductKindsAndValuesDtoResultVO;
    }


    @ApiOperation(value = "商品库存信息查询", notes = "商品库存信息查询", response = ResultVO.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping(value = "/queryProductInventory")
    public ResultVO queryProductInventory(HttpServletRequest request,
                                          @ApiParam(required = true, value = "商品库存信息") @RequestBody ProductStockModel productStockModel) {
        JmInventoryDto jmInventoryDto = new JmInventoryDto();
        if (productStockModel.getPage() != null) {
            jmInventoryDto.setPageNum(productStockModel.getPage());
        }
        if (productStockModel.getRows() != null) {
            jmInventoryDto.setPageSize(productStockModel.getRows());
        }
        if (productStockModel.getOrganSign() != null) {
            jmInventoryDto.setOrgansign(productStockModel.getOrganSign());
        }
        if (productStockModel.getStockNumber() != null) {
            jmInventoryDto.setStockNumber(new BigDecimal(productStockModel.getStockNumber().intValue()));
        } else {
            //按条件查询1: 大于0    2:全部   3:等于0
            jmInventoryDto.setStockNumber(new BigDecimal(2));
        }
        jmInventoryDto.setProductName(productStockModel.getProductName());

        ResultVO<PageInfo<JmInventoryDto>> pageInfoResultVO = jmInventoryApi.queryInventoryInfo(jmInventoryDto);
        return pageInfoResultVO;
    }

    @ApiOperation(value = "商品批号库存信息查询", notes = "商品批号库存信息查询", response = ResultVO.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping(value = "/queryProductLotInventory")
    public ResultVO queryProductLotInventory(HttpServletRequest request,
                                             @ApiParam(required = true, value = "商品库存信息") @RequestBody ProductStockModel productStockModel) {
        JmInventoryLotNumberDto jmInventoryLotNumberDto = new JmInventoryLotNumberDto();
        jmInventoryLotNumberDto.setPageNum(productStockModel.getPage());
        jmInventoryLotNumberDto.setPageSize(productStockModel.getRows());
        jmInventoryLotNumberDto.setOrgansign(productStockModel.getOrganSign());
        jmInventoryLotNumberDto.setPositionid(productStockModel.getPositionid());
        jmInventoryLotNumberDto.setStatus(productStockModel.getStatus());
        jmInventoryLotNumberDto.setProductName(productStockModel.getProductName());
        jmInventoryLotNumberDto.setStockNumber(new BigDecimal(productStockModel.getStockNumber().intValue()));
        ResultVO<PageInfo<JmInventoryLotNumberDto>> pageInfoResultVO = jmInventoryApi.queryLotInventoryInfo(jmInventoryLotNumberDto);
        return pageInfoResultVO;
    }

    @ApiOperation(value = "架位查询", notes = "架位查询", response = ResultVO.class, tags = {"商品库存服务-调拨单API",})
    @PostMapping(value = "/queryPositions")
    public ResultVO queryPositions(HttpServletRequest request,
                                   @ApiParam(required = true, value = "商品库存信息") @RequestBody ProductStockModel productStockModel) {
        String organSign = productStockModel.getOrganSign();
        ResultVO<List<JmSaasPositionDto>> resultPositions = jmInventoryApi.queryPositions(organSign);
        return resultPositions;
    }

}
