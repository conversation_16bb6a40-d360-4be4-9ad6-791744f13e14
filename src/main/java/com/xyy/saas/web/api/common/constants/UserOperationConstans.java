package com.xyy.saas.web.api.common.constants;

public class UserOperationConstans {


    // 防重复提交状态码
    public static final Integer REPEAT_SUBMIT_CODE = -1001;

    public static final String REPEAT_SUBMIT_CODE_STRING = "-1001";

    public static final Integer RESULT_TYPE_INTEGER = 1;

    public static final Integer RESULT_TYPE_STRIGN = 2;

    public static final Integer RESULT_TYPE_RESULTVO = 3;

    public static final Integer RESULT_TYPE_ResponseEntity_RESULTVO = 4;

    //用户操作key前缀
    public static final String USER_OPERATION_KEY_PREF = "useroperation";
    //分隔符
    public static final String SPLIT = ":";
    //用户操作业务分类
    public static final String LIBRARY_QUERY = "libraryquery";

    public static final String JX_PROVINCE = "江西省";
    public static final String JS_PROVINCE = "江苏省";
    public static final String SR_CITY = "上饶市";
    public static final String JA_CITY = "吉安市";
    public static final String NT_CITY = "南通市";
    public static final String SR_CITY_ORG_TYPE = "1";
    public static final String JA_CITY_ORG_TYPE = "2";
    public static final String NT_CITY_ORG_TYPE = "3";
    public static final String JX_SR_REMARK = "(导出上月销售医保商品的库存信息)";
    public static final String JX_JA_REMARK = "(请输入日期查询条件)";
    public static final String JS_NT_REMARK = "";

}