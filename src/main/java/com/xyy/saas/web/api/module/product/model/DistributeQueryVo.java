package com.xyy.saas.web.api.module.product.model;

import java.util.List;

/**
 * @ClassName DistributeQueryVo
 * @Description 联营总部商品分发
 * <AUTHOR>
 * @Date 2021/2/4 20:10
 * @Version 1.0
 **/
public class DistributeQueryVo {

    private Byte type;//1：全部分发，2：prefs分发
    private List<String> prefs;//商品列表分发
    private String mixedQuery; //混合查询
    private String manufacturer; //生产厂家
    private Integer prescriptionClassification; //处方分类
    private Integer systemType;//系统类型，与自定义分类要区分
    private Byte used; //是否启用：0---未启用   1---启用
    private Integer businessScope; //经营范围
    private Byte medicalInsurance;//是否医保，0非医保，1医保
    private String createTimeStart;//开始时间
    private String createTimeEnd;//结束时间
    private Byte allDrugsYn;//1,全部门店，2部分门店
    private List<String> drugs;//同步门店列表

    public String getCreateTimeStart() {
        return createTimeStart;
    }

    public void setCreateTimeStart(String createTimeStart) {
        this.createTimeStart = createTimeStart;
    }

    public String getCreateTimeEnd() {
        return createTimeEnd;
    }

    public void setCreateTimeEnd(String createTimeEnd) {
        this.createTimeEnd = createTimeEnd;
    }

    public Byte getAllDrugsYn() {
        return allDrugsYn;
    }

    public void setAllDrugsYn(Byte allDrugsYn) {
        this.allDrugsYn = allDrugsYn;
    }

    public List<String> getDrugs() {
        return drugs;
    }

    public void setDrugs(List<String> drugs) {
        this.drugs = drugs;
    }


    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public List<String> getPrefs() {
        return prefs;
    }

    public void setPrefs(List<String> prefs) {
        this.prefs = prefs;
    }

    public String getMixedQuery() {
        return mixedQuery;
    }

    public void setMixedQuery(String mixedQuery) {
        this.mixedQuery = mixedQuery;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public Integer getPrescriptionClassification() {
        return prescriptionClassification;
    }

    public void setPrescriptionClassification(Integer prescriptionClassification) {
        this.prescriptionClassification = prescriptionClassification;
    }

    public Integer getSystemType() {
        return systemType;
    }

    public void setSystemType(Integer systemType) {
        this.systemType = systemType;
    }

    public Byte getUsed() {
        return used;
    }

    public void setUsed(Byte used) {
        this.used = used;
    }

    public Integer getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(Integer businessScope) {
        this.businessScope = businessScope;
    }

    public Byte getMedicalInsurance() {
        return medicalInsurance;
    }

    public void setMedicalInsurance(Byte medicalInsurance) {
        this.medicalInsurance = medicalInsurance;
    }

}
