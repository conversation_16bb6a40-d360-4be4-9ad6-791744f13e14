package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

@ApiModel(description = "数据刷新实体对象")
public class RefreshDataVo {

    @JsonProperty("type")
    private Integer type;

    @JsonProperty("organSigns")
    private String organSigns;

    @ApiModelProperty(value = "操作类型：1，刷新单位，2，刷新商品类型，3，刷新剂型")
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @ApiModelProperty(value = "机构列表：以逗号分隔的机构编号列表")
    public String getOrganSigns() {
        return organSigns;
    }

    public void setOrganSigns(String organSigns) {
        this.organSigns = organSigns;
    }
}
