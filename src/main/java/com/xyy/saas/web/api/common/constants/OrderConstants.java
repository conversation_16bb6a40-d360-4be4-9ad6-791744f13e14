package com.xyy.saas.web.api.common.constants;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/4/13.
 */
public class OrderConstants {

    public static final String CASH_PAY = "现金";

    public static final String WECHAT_PAY = "微信";

    public static final String ALI_PAY = "支付宝";

    public static final String MEDICARE_PAY = "医保";

    public static final String UNION_PAY = "银联";
    
    public static final String STORED_VALUE_PAY = "储值";
    
    public static final String OTHER_PAY = "其他";

    public static final String TOGETHER_PAY = "聚合收款";

    public static final String SUM_PAY = "支付合计";
    //常数100
    public static final Integer ONE_HUNDRED = 100;
    //常数1
    public static final Integer ONE = 1;
    //常数字符串0
    public static final String ZREO_STR = "0";
    //常数字符串1
    public static final String ONE_STR = "1";
    //orderId序列号
    public static final String ORDERID_SEQ = "orderId";
    //orderNo序列号
    public static final String ORDERNO_SEQ = "orderNo";
    //逻辑删除 1 有效
    public static final Boolean YN_TRUE = true;
    //逻辑删除 0 删除
    public static final Boolean YN_FALSE = false;
    //逻辑删除 1 有效
    public static final Byte YN_TRUE_BYTE = 1;
    //逻辑删除 0 删除
    public static final Byte YN_FALSE_BYTE = 0;
    //订单提取状态: 1-已提取
    public static final Byte HAS_EXTRACTED = 1;
    //订单提取状态: 0-未提取
    public static final Byte UN_EXTRACTED = 0;
    //是积分商品
    public static final Byte SCORE_PRODUCT_Y = 1 ;
    //不是否积分商品
    public static final Byte SCORE_PRODUCT_N = 0 ;
    //价格策略 1 零售价
    public static final Byte PRICE_STRATEGY_RETAIL = 1;
    //价格策略 2 会员价
    public static final Byte PRICE_STRATEGY_MEMBER = 2;
    //是
    public static final Integer Y = 1;
    //否
    public static final Integer N = 0;
    //订单状态: 00-正常销售 01-挂帐 02-提取挂账 03-提取处方 04-提取原单 05-直接退货
    public static final String STATUS_RETURN = "05";
    public static final Integer noUpload = 0; // 处方登记上报状态

}
