package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.member.core.api.MemberPointDeductConfigApi;
import com.xyy.saas.member.core.api.MemberShareApi;
import com.xyy.saas.member.core.dto.MemberPointDeductConfigDto;
import com.xyy.saas.member.core.dto.MemberShareDto;
import com.xyy.saas.member.core.enums.MemberShareStatusEnum;
import com.xyy.saas.web.api.module.member.model.MemberBase;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import io.swagger.annotations.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员积分抵扣规则
 */
@Controller
@RequestMapping(value = "/member")
@Api(value = "pointDeductConfig", description = "积分抵扣规则")
public class MemberPointDeductConfigApiController {

    private static final Logger logger = LogManager.getLogger(MemberPointDeductConfigApiController.class);
    @Reference(version = "0.0.1")
    private MemberPointDeductConfigApi memberPointDeductConfigApi;
    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;

    @Reference(version = "0.0.1")
    private MemberShareApi memberShareApi;

    @ApiOperation(value = "保存抵扣规则信息", notes = "保存抵扣规则信息", response = Boolean.class, tags = {"积分抵扣规则",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/pointDeductConfig/save", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> save(@RequestHeader("commonRequestModel") String commonRequestModel,@ApiParam(value = "抵扣规则信息", required = true) @RequestBody MemberPointDeductConfigDto memberPointDeductConfigDto) {
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        String organSign = model.getOrganSign();//总部机构号
        String employeeId = model.getEmployeeId();
        memberPointDeductConfigDto.setOrganSign(organSign);
        memberPointDeductConfigDto.setCreateUser(employeeId);
        memberPointDeductConfigDto.setUpdateUser(employeeId);
        boolean isSuccess = memberPointDeductConfigApi.save(memberPointDeductConfigDto);
        return new ResponseEntity(ResultVO.createSuccess(isSuccess), HttpStatus.OK);
    }


    @ApiOperation(value = "抵现规则查询-门店", notes = "抵现规则查询-门店", response = MemberPointDeductConfigDto.class, tags = {"积分抵扣规则",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberPointDeductConfigDto.class)})
    @RequestMapping(value = "/pointDeductConfig/query",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> query(@RequestHeader(name = "organSign", required = true) String organSign, @ApiParam(value = "抵扣规则信息", required = true) @RequestBody MemberPointDeductConfigDto memberPointDeductConfigDto) {
        SaaSDrugstoreDto model = drugstoreApi.getDrugstoreByOrganSign(organSign);
        // 单体门店
        if (model.getBizModel() == 1) {
            organSign = model.getOrganSign();
        }
        // 联营连锁门店
        else {
            //总部
            if(model.getOrganSignType() == 3){
                organSign = model.getOrganSign();
            }else{
//                organSign = model.getHeadquartersOrganSign();
                List<MemberShareDto> shareRel = memberShareApi.selectSharedList(model.getHeadquartersOrganSign()).getResult();
                List<String> shares = shareRel.stream().map(s -> s.getOrganSign()).collect(Collectors.toList());
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(shares) && shares.contains(organSign)){
                    //共享门店
                    organSign = model.getHeadquartersOrganSign();
                }else {
                    //不共享门店
                    organSign = model.getOrganSign();
                }
//                List<MemberShareDto> shareRel = memberShareApi.selectByOrganSign(model.getOrganSign()).getResult();
//                if (CollectionUtils.isEmpty(shareRel) || shareRel.get(0).getShareStatus() == MemberShareStatusEnum.SHARE_NO.getCode()){
//                    //不共享会员的联营/连锁门店拿门店自己的抵现规则
//                    organSign = model.getOrganSign();
//                }else {
//                    organSign = model.getHeadquartersOrganSign();
//                }
            }
        }
        memberPointDeductConfigDto.setOrganSign(organSign);
        return new ResponseEntity(ResultVO.createSuccess(memberPointDeductConfigApi.getMemberPointDeductConfig(memberPointDeductConfigDto)), HttpStatus.OK);
    }

    @ApiOperation(value = "抵现规则查询-总部", notes = "抵现规则查询-总部", response = MemberPointDeductConfigDto.class, tags = {"积分抵扣规则",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberPointDeductConfigDto.class)})
    @RequestMapping(value = "/pointDeductConfig/zb/query",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> queryByZb(@RequestHeader(name = "organSign", required = true) String organSign, @ApiParam(value = "抵扣规则信息", required = true) @RequestBody MemberPointDeductConfigDto memberPointDeductConfigDto) {
        memberPointDeductConfigDto.setOrganSign(organSign);
        return new ResponseEntity(ResultVO.createSuccess(memberPointDeductConfigApi.getMemberPointDeductConfig(memberPointDeductConfigDto)), HttpStatus.OK);
    }

}
