package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 会员储值卡Vo
 * <AUTHOR>
 */
@ApiModel(description = "会员储值卡")
public class MemberPrepayCardVo extends MemberPrepayCardTokenVo implements Serializable {

    private static final long serialVersionUID = -1368179524986113255L;

    /**
     * 会员guid
     */
    @ApiModelProperty(value = "会员guid", required = true, example = "584a4460-2144-47c6-b7f7-d85b33d3322c")
    private String memberGuid;

    /**
     * 储值卡实充余额
     */
    @ApiModelProperty(value = "储值卡实充余额", example = "50.00")
    private BigDecimal amount;

    /**
     * 储值卡赠送余额
     */
    @ApiModelProperty(value = "储值卡赠送余额", example = "5.00")
    private BigDecimal bonus;

    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识", hidden = true)
    private String organsign;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", example = "12", hidden = true)
    private String createUser;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人", example = "14", hidden = true)
    private String updateUser;

    /**
     * 审核人
     */
    @ApiModelProperty(value = "审核人", example = "90")
    private String checkUser;

    /**
     * 是否退款
     */
    @ApiModelProperty(value = "是否退款", example = "0")
    private Boolean isRefunded;

    /**
     * 支付类型 1-现金 2-微信 3-支付宝 4-银联 9-其他
     * @see com.xyy.saas.member.core.enums.PayTypeEnum
     */
    @ApiModelProperty(value = "支付类型 1-现金 2-微信 3-支付宝 4-银联 9-其他", example = "1")
    private Byte payType;

    /**
     * 原因
     */
    @ApiModelProperty(value = "原因", example = "扣减错误充值")
    private String reason;

    /**
     * 请求来源 web-管理端 pos-pos端
     */
    @ApiModelProperty(value = "请求来源 web-管理端 pos-pos端", example = "web")
    private String from;

    /**
     * 审核人密码（MD5加密）
     */
    @ApiModelProperty(value = "审核人密码（MD5加密）", example = "e10adc3949ba59abbe56e057f20f883e")
    private String checkPassword;

    public String getMemberGuid() {
        return memberGuid;
    }

    public void setMemberGuid(String memberGuid) {
        this.memberGuid = memberGuid;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getBonus() {
        return bonus;
    }

    public void setBonus(BigDecimal bonus) {
        this.bonus = bonus;
    }

    public String getOrgansign() {
        return organsign;
    }

    public void setOrgansign(String organsign) {
        this.organsign = organsign;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getCheckUser() {
        return checkUser;
    }

    public void setCheckUser(String checkUser) {
        this.checkUser = checkUser;
    }

    public Boolean getRefunded() {
        return isRefunded;
    }

    public void setRefunded(Boolean refunded) {
        isRefunded = refunded;
    }

    public Byte getPayType() {
        return payType;
    }

    public void setPayType(Byte payType) {
        this.payType = payType;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public String getCheckPassword() {
        return checkPassword;
    }

    public void setCheckPassword(String checkPassword) {
        this.checkPassword = checkPassword;
    }

    @Override
    public String toString() {
        return "MemberPrepayCardVo{" +
                "memberGuid='" + memberGuid + '\'' +
                ", amount=" + amount +
                ", bonus=" + bonus +
                ", organsign='" + organsign + '\'' +
                ", createUser='" + createUser + '\'' +
                ", updateUser='" + updateUser + '\'' +
                ", checkUser='" + checkUser + '\'' +
                ", isRefunded=" + isRefunded +
                ", payType=" + payType +
                ", reason='" + reason + '\'' +
                ", from='" + from + '\'' +
                ", checkPassword='" + checkPassword + '\'' +
                '}';
    }
}