package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * @ClassName DrugStandardQueryVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/16 12:45
 * @Version 1.0
 **/
@ApiModel(value = "药监匹配查询参数", description = "药监匹配查询参数")
public class DrugStandardQueryVo  implements Serializable {
    @ApiModelProperty(value = "地区编号,必填")
    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }


    @JsonProperty("areaCode")
    private String areaCode;

    @ApiModelProperty(value = "当前页码,必填")
    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    @ApiModelProperty(value = "每页显示多少，必填")
    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    @JsonProperty("page")
    private Integer page;
    @JsonProperty("rows")
    private Integer rows;


}
