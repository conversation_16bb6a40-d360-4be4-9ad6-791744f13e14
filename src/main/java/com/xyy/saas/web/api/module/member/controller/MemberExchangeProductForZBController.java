package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.member.core.api.MemberExchangeProductForZBApi;
import com.xyy.saas.member.core.dto.MemberExchangeProductDto;
import com.xyy.saas.web.api.module.member.model.MemberExchangeProductVo;
import com.xyy.saas.web.api.module.member.service.MemberExchangeProductService;
import com.xyy.saas.web.api.module.member.utils.ResultCodeEnum;
import com.xyy.saas.web.api.module.utils.ExportExcelUtil;
import com.xyy.saas.web.api.module.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Controller
@RequestMapping("/member/memberExchangeProduct/zb")
@Api(value = "exchangeProduct", description = "总部端积分兑换商品接口")
public class MemberExchangeProductForZBController {

    private static final Logger logger = LogManager.getLogger(MemberExchangeProductForZBController.class);

    @Reference( version = "0.0.1")
    private MemberExchangeProductForZBApi memberExchangeProductForZBApi;

    @Autowired
    private MemberExchangeProductService memberExchangeProductService;

    @ApiOperation(value = "总部端:保存商品积分规则", notes = "总部端:保存商品积分规则")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/saveExchangeProduct", method = RequestMethod.POST)
    @ResponseBody
    public ResultVO saveExchangeProduct(@RequestHeader("commonRequestModel") String commonRequestModel, @RequestBody MemberExchangeProductVo vo){
        if (vo == null || vo.getProductPref() == null) {
            return new ResultVO(ResultCodeEnum.NO_EXCHANGE_PRODUCT.getCode(), ResultCodeEnum.NO_EXCHANGE_PRODUCT.getMsg(), null);
        }
        List<MemberExchangeProductDto> exchangeProductDtoList = new ArrayList<>();
        MemberExchangeProductDto exchangeProductDto = new MemberExchangeProductDto();
        BeanUtils.copyProperties(vo, exchangeProductDto);
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        exchangeProductDto.setOrganSign(model.getHeadquartersOrganSign());
        exchangeProductDtoList.add(exchangeProductDto);
        memberExchangeProductForZBApi.saveBatchExchangeProductZB(exchangeProductDtoList);
        return new ResultVO(ResultCodeEnum.SUCCESS);
    }

    @ApiOperation(value = "总部端:批量保存商品积分规则", notes = "总部端:批量保存商品积分规则")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/batchSaveExchangeProduct", method = RequestMethod.POST)
    @ResponseBody
    public ResultVO batchSaveExchangeProduct(@RequestHeader("commonRequestModel") String commonRequestModel, @RequestBody MemberExchangeProductVo vo){
        if (vo == null || vo.getPrefList() == null) {
            return new ResultVO(ResultCodeEnum.NO_EXCHANGE_PRODUCT.getCode(), ResultCodeEnum.NO_EXCHANGE_PRODUCT.getMsg(), null);
        }
        List<MemberExchangeProductDto> exchangeProductDtoList = new ArrayList<>();
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        vo.getPrefList().forEach(pref->{
            MemberExchangeProductDto exchangeProductDto = new MemberExchangeProductDto();
            exchangeProductDto.setProductPref(pref);
            exchangeProductDto.setIntegral(vo.getIntegral());
            exchangeProductDto.setYn(vo.getYn());
            exchangeProductDto.setOrganSign(model.getHeadquartersOrganSign());
            exchangeProductDtoList.add(exchangeProductDto);
        });
        memberExchangeProductForZBApi.saveBatchExchangeProductZB(exchangeProductDtoList);
        return new ResultVO(ResultCodeEnum.SUCCESS);
    }

    @ApiOperation(value = "总部端:积分可兑换商品列表", notes = "总部端:积分可兑换商品列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberExchangeProductDto.class) })
    @RequestMapping(value = "/getExchangeProductPagerZB", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> getExchangeProductPagerZB(@RequestHeader("commonRequestModel") String commonRequestModel, @RequestBody MemberExchangeProductVo vo){
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        MemberExchangeProductDto exchangeProductDto = new MemberExchangeProductDto();
        BeanUtils.copyProperties(vo, exchangeProductDto);
        if(StringUtils.isEmpty(vo.getOrganSign())){
            //查询全部
            exchangeProductDto.setHeadquartersOrganSign(model.getOrganSign());
        }else{
            //查询门店
            exchangeProductDto.setOrganSign(vo.getOrganSign());
        }
        exchangeProductDto.setIshidden(model.getIdentity());
        exchangeProductDto.setPageNum(exchangeProductDto.getPageNum() == null ? 1 : exchangeProductDto.getPageNum());
        exchangeProductDto.setPageSize(exchangeProductDto.getPageSize() == null ? 50 : exchangeProductDto.getPageSize());
        PageInfo pageInfo = memberExchangeProductService.getExchangeProductListPager(exchangeProductDto);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(pageInfo), HttpStatus.OK);
    }

    @ApiOperation(value = "总部端:导出积分可兑换商品列表", notes = "导出积分可兑换商品列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberExchangeProductDto.class) })
    @RequestMapping(value = "/exportExchangeProductZB", method = RequestMethod.POST)
    @ResponseBody
    public void exportExchangeProductZB(@RequestHeader("commonRequestModel") String commonRequestModel, HttpServletRequest request,
                                      HttpServletResponse response, @RequestBody MemberExchangeProductVo vo){
        MemberExchangeProductDto exchangeProductDto = new MemberExchangeProductDto();
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        BeanUtils.copyProperties(vo, exchangeProductDto);
        if(StringUtils.isEmpty(vo.getOrganSign())){
            //查询全部
            exchangeProductDto.setHeadquartersOrganSign(model.getOrganSign());
        }else{
            //查询门店
            exchangeProductDto.setOrganSign(vo.getOrganSign());
        }
        exchangeProductDto.setIshidden(model.getIdentity());
        exchangeProductDto.setPageNum(1);
        exchangeProductDto.setPageSize(100000);
        PageInfo pageInfo = memberExchangeProductService.getExchangeProductListPager(exchangeProductDto);
        List<MemberExchangeProductDto> list = pageInfo.getList();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
        String sheetName = "积分可兑换商品";
        String extFilename="积分可兑换商品"+df.format(new Date())+".xls";
        String headers[] = new String[]{"通用名","规格","单位", "商品编号", "生产厂家","零售价","库存数量","所属门店" ,"门店编码","所需积分"};
        String fieldNames[] = new String[]{ "commonName", "attributeSpecification", "unitName", "pharmacyPref","manufacturer", "retailPrice", "inventoryNum","organSignName","organSign", "integral"};
        try {
            ExportExcelUtil.createExcel(response,request,extFilename,sheetName, headers, fieldNames, list,true);
        } catch (Exception e) {
            logger.error("MemberExchangeProductController exportRecordExcel is error.", e);
        }
    }
}
