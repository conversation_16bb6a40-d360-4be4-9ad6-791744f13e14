package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 会员储值卡详情查询Vo
 * <AUTHOR>
 */
@ApiModel(description = "会员储值卡详情查询")
public class MemberPrepayCardDetailQueryVo implements Serializable {


    private static final long serialVersionUID = 5931812348670880101L;

    /**
     * 会员guid
     */
    @ApiModelProperty(value = "会员guid", example = "584a4460-2144-47c6-b7f7-d85b33d3322c")
    private String memberGuid;

    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识", hidden = true)
    private String organsign;

    /**
     * 总部机构标识
     */
    @ApiModelProperty(value = "总部机构标识", hidden = true)
    private String headquartersOrganSign;

    public String getMemberGuid() {
        return memberGuid;
    }

    public void setMemberGuid(String memberGuid) {
        this.memberGuid = memberGuid;
    }

    public String getOrgansign() {
        return organsign;
    }

    public void setOrgansign(String organsign) {
        this.organsign = organsign;
    }

    public String getHeadquartersOrganSign() {
        return headquartersOrganSign;
    }

    public void setHeadquartersOrganSign(String headquartersOrganSign) {
        this.headquartersOrganSign = headquartersOrganSign;
    }

    @Override
    public String toString() {
        return "MemberPrepayCardDetailQueryVo{" +
                "memberGuid='" + memberGuid + '\'' +
                ", organsign='" + organsign + '\'' +
                ", headquartersOrganSign='" + headquartersOrganSign + '\'' +
                '}';
    }
}