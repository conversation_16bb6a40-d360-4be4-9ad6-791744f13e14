package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.api.SaasCustomTypeApi;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.dto.SystemDictDto;
import com.xyy.saas.common.enums.BusinessFlowEnum;
import com.xyy.saas.common.enums.CustomTypeBusinessTypeEnum;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.manufactor.core.api.ManufactorApi;
import com.xyy.saas.manufactor.core.dto.ManufactorDto;
import com.xyy.saas.product.core.api.ProductDictionaryApi;
import com.xyy.saas.product.core.dto.*;
import com.xyy.saas.web.api.module.product.constant.DictConstant;
import com.xyy.saas.web.api.module.product.model.FirstCheckProductQuery;
import com.xyy.saas.workflow.api.WorkflowAuditTaskService;
import com.xyy.saas.workflow.exception.WorkflowException;
import com.xyy.saas.workflow.model.dto.WorkflowAuditLogQueryDto;
import com.xyy.saas.workflow.model.dto.WorkflowAuditProcessQueryDto;
import com.xyy.saas.workflow.model.dto.WorkflowAuditTaskDto;
import com.xyy.saas.workflow.model.meta.TenantIdEnum;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.api.RoleApi;
import com.xyy.user.module.dto.SaasRoleDto;
import com.xyy.user.module.dto.result.EmployeeDto;
import com.xyy.user.module.enums.DrugstoreBizModelEnum;
import com.xyy.user.module.enums.DrugstoreTypeEnum;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.*;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T18:00:37.938+08:00")

@Controller
public class FirstCheckProductApiController implements FirstCheckProductApi {

    private static final Logger logger = LoggerFactory.getLogger(FirstCheckProductApiController.class);

    @Reference(version = "0.0.1")
    private com.xyy.saas.product.core.api.FirstCheckProductApi firstCheckProductApi;

    @Reference(version = "0.0.1")
    private EmployeeApi employeeApi;

    @Reference(version = "0.0.1")
    private RoleApi roleApi;

    @Reference(version = "0.0.1")
    private com.xyy.saas.product.core.api.ProductApi productApi;

    @Reference(version = "1.0.0")
    private WorkflowAuditTaskService workflowAuditTaskService;

    @Reference(version = "0.0.1")
    private ProductDictionaryApi productDictionaryApi;

    @Reference(version = "0.0.1")
    private ManufactorApi manufactorApi;

    @Reference(version = "0.0.1")
    private SaasCustomTypeApi saasCustomTypeApi;


    @Override
    public ResponseEntity<ResultVO> getProductById(String guid,String organ) {
        ResultVO result = firstCheckProductApi.getProductById(guid,organ);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }
    @Override
    public ResponseEntity<ResultVO> list(@ApiParam(value = "首营商品列表查询条件实体" ,required=true )  @Valid @RequestBody FirstCheckProductQuery firstCheckProductQuery) {
        FirstCheckProductQueryDto queryDto = new FirstCheckProductQueryDto();
        BeanUtils.copyProperties(firstCheckProductQuery, queryDto);
        ResultVO result = firstCheckProductApi.list(queryDto);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> queryFirstProductList(HttpServletRequest request,@ApiParam(value = "首营商品查询对象", required = true) @Valid @RequestBody FirstCheckProductQueryDto firstProduct) {
        String organSign = request.getHeader("organSign");
//        String modelJson = request.getHeader("commonRequestModel");
        String employeeId = request.getHeader("employeeId");
//        Byte bizModel = null;
        //如果是连锁门店，需要获取总部的机构号
        String priceQueryOrganSign = "";
        String modelJson = request.getHeader("commonRequestModel");
        Byte bizModel = null;
        boolean isChainStore = false;
        if(!org.springframework.util.StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            bizModel = model.getBizModel();
            if (DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel())) {
                isChainStore = true;
            }
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                priceQueryOrganSign = organSign;
                organSign = model.getHeadquartersOrganSign();
            }
        }
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            bizModel = model.getBizModel();
        }
        Integer page = firstProduct.getPage();
        Integer rows = firstProduct.getRows();
        if (page == null) {
            page = 1;
        }
        if (rows == null) {
            rows = 10;
        }
        if(!StringUtils.isEmpty(firstProduct.getCreateTimeStart())){
            firstProduct.setCreateTimeStart(firstProduct.getCreateTimeStart()+" 00:00:00");
        }
        if(!StringUtils.isEmpty(firstProduct.getCreateTimeEnd())){
            firstProduct.setCreateTimeEnd(firstProduct.getCreateTimeEnd()+" 23:59:59");
        }
        PageInfo<FirstCheckProductDto> pageInfo = new PageInfo<>();
        pageInfo.setPageSize(rows);
        pageInfo.setPageNum(page);
        firstProduct.setOrganSign(organSign);
        PageInfo<FirstCheckProductDto> pList = firstCheckProductApi.findHeadquartersPageInfo(pageInfo, firstProduct);
        List<FirstCheckProductDto> firstCheckProductList = pList.getList();
        //如果查询结果为空
        if(CollectionUtils.isEmpty(firstCheckProductList)){
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(pList), HttpStatus.OK);
        }
        //通过employeeId查询角色集合
        Set<Integer> roleIds = null;
        if(!StringUtils.isEmpty(employeeId)){
            ResultVO<List<Integer>> listResultVO = roleApi.queryRoleIdsByEmployeeId(Integer.valueOf(employeeId));
            if(null != listResultVO){
                roleIds = new HashSet<>(listResultVO.getResult());
            }
        }
        List<Integer> ids = new ArrayList<>();
        ids.add(DictConstant.productSystemType);   //商品类别
        ids.add(DictConstant.prescriptionBussinessId);  //药品类别
        ids.add(DictConstant.PRODUCT_THIRD_BUSSINESS_ID);  //自定义三级分类业务ID
        ids.add(DictConstant.unitBussinessId);   //单位
        ids.add(DictConstant.agentBussinessId);  //剂型
        ids.add(DictConstant.scopeOfOperation);  //经营范围
        List<SystemDictDto> dictDtos = productDictionaryApi.findSystemDictDtoBybussinessIds(ids,organSign);
        Map<Integer,String> productSystemTypeMap = new HashMap<>();
        Map<Integer,String> prescriptionTypeMap = new HashMap<>();
        Map<Integer,String> productThirdBussinessTypeMap = new HashMap<>();
        Map<Integer,String> unitTypeMap = new HashMap<>();
        Map<Integer,String> agentTypeMap = new HashMap<>();
        Map<Integer,String> businessScopeTypeMap = new HashMap<>();
        for(SystemDictDto sdd:dictDtos){
            if(sdd.getBussinessId().equals(DictConstant.productSystemType) ){
                productSystemTypeMap.put(sdd.getId(),sdd.getName());
            }
            if(sdd.getBussinessId().equals(DictConstant.prescriptionBussinessId) ){
                prescriptionTypeMap.put(sdd.getId(),sdd.getName());
            }
            if(sdd.getBussinessId().equals(DictConstant.PRODUCT_THIRD_BUSSINESS_ID)){
                productThirdBussinessTypeMap.put(sdd.getId(),sdd.getName());
            }
            if(sdd.getBussinessId().equals(DictConstant.unitBussinessId)){
                unitTypeMap.put(sdd.getId(),sdd.getName());
            }
            if(sdd.getBussinessId().equals(DictConstant.agentBussinessId) ){
                agentTypeMap.put(sdd.getId(),sdd.getName());
            }
            if(sdd.getBussinessId().equals(DictConstant.scopeOfOperation) ){
                businessScopeTypeMap.put(sdd.getId(),sdd.getName());
            }
        }
        ManufactorDto manufactor= new ManufactorDto();
        manufactor.setOrganSign(organSign);
        List<ManufactorDto> manufactorDtoList= this.manufactorApi.queryManufactorListNoPage(manufactor);
        HashMap<String, String> map = new HashMap<String, String>();
        if(manufactorDtoList!=null &&manufactorDtoList.size()>0){
            for (ManufactorDto manufactorDto:manufactorDtoList){
                map.put(manufactorDto.getId().toString(),manufactorDto.getName());
            }
        }
        Set<Integer> employeeIds = new HashSet<>();
        Set<Integer> approveUserRoleIds = new HashSet<>();
        firstCheckProductList.stream().forEach( dto -> {
            if(null != dto){
                if(!StringUtils.isEmpty(dto.getCreateUser())){
                    employeeIds.add(Integer.valueOf(dto.getCreateUser()));
                }
                if(dto.getApproveUserRoleId() != null){
                    approveUserRoleIds.add(Integer.valueOf(dto.getApproveUserRoleId().toString()));
                }
            }
        });

        Map<String,String> employeeMaps = new HashMap<>();
        if(!CollectionUtils.isEmpty(employeeIds)){
            List<EmployeeDto> employees = employeeApi.queryEmployeeByIds(new ArrayList<>(employeeIds));
            if(!CollectionUtils.isEmpty(employees)){
                employees.stream().forEach( employee -> {
                    if(null != employee && null != employee.getId()){
                        employeeMaps.put(String.valueOf(employee.getId()),employee.getName());
                    }
                });
            }
        }

        Map<String,String> approveUserRoleMaps = new HashMap<>();
        if(!CollectionUtils.isEmpty(approveUserRoleIds)){
            List<SaasRoleDto> roles = roleApi.getRoleByIdList(new ArrayList<>(approveUserRoleIds));
            if(!CollectionUtils.isEmpty(roles)){
                roles.stream().forEach( roleDto -> {
                    if(null != roleDto && null != roleDto.getId()){
                        approveUserRoleMaps.put(String.valueOf(roleDto.getId()),roleDto.getName());
                    }
                });
            }
        }

        //新的商品自定义分类
        Map<Long, String> customTypeIdNameMap = saasCustomTypeApi.getCustomTypeIdNameMap(organSign, CustomTypeBusinessTypeEnum.PRODUCT_TYPE.getValue());

        for (FirstCheckProductDto vo : firstCheckProductList) {
            ProductDto productinfo = vo.getProductinfo();
            vo.setPharmacyPref(productinfo.getPharmacyPref());
            vo.setCommonName(productinfo.getCommonName());
            vo.setProductName(productinfo.getProductName());
            vo.setBarCode(productinfo.getBarCode());
            vo.setAttributeSpecification(productinfo.getAttributeSpecification());
            if (!StringUtils.isEmpty(productinfo.getManufacturerCode()) && 2 == bizModel) {//连锁
                vo.setManufacturer(map.get(productinfo.getManufacturerCode()));
            }
//            if (!StringUtils.isEmpty(productinfo.getManufacturer())&&3==bizModel) {
//                vo.setManufacturer(productinfo.getManufacturer());
//            }
            vo.setProducingArea(productinfo.getProducingArea());
            vo.setApprovalNumber(productinfo.getApprovalNumber());
            vo.setApprovalNumberValidity(productinfo.getProductExtMess().getApprovalNumberValidity());
            vo.setRetailPrice(productinfo.getRetailPrice());
            vo.setVipPrice(productinfo.getVipPrice());
            vo.setUsed(productinfo.getUsed());
            vo.setExpireYn(productinfo.getExpireYn());
            vo.setCreateTime(productinfo.getCreateTime());
            if(vo.getUsed()!=null &&vo.getUsed()==1){
                vo.setUsedName("否");
            }else if(vo.getUsed()!=null &&vo.getUsed()==0){
                vo.setUsedName("是");
            }
            if(vo.getExpireYn()!=null &&vo.getExpireYn()==1){
                vo.setExpireYnName("是");
            }else if(vo.getExpireYn()!=null &&vo.getExpireYn()==0){
                vo.setExpireYnName("否");
            }
            if(vo.getStatus()!=null &&vo.getStatus()==1){
                vo.setStatusName("审批中");
            }else if(vo.getStatus()!=null &&vo.getStatus()==6){
                vo.setStatusName("已驳回");
            }else if(vo.getStatus()!=null &&vo.getStatus()==7){
                vo.setStatusName("已通过");
            }
            vo.setSystemTypeName(productSystemTypeMap.get(productinfo.getSystemType()));
            vo.setPrescriptionClassificationName(prescriptionTypeMap.get(productinfo.getPrescriptionClassification()));
            if (isChainStore && productinfo.getProductType() != null && customTypeIdNameMap.get(productinfo.getProductType().longValue()) != null) {
                vo.setProductTypeName(customTypeIdNameMap.get(productinfo.getProductType().longValue()));
            } else {
                vo.setProductTypeName(productThirdBussinessTypeMap.get(productinfo.getProductType()));
            }

            vo.setUnitName(unitTypeMap.get(productinfo.getUnitId()));
            vo.setDosageFormName(agentTypeMap.get(Integer.parseInt(productinfo.getDosageFormId().toString())));
            vo.setBusinessScopeName(businessScopeTypeMap.get(productinfo.getBusinessScope()));
//            String createUser = productinfo.getCreateUser();
//            SaaSEmployeeDto employee= employeeApi.getEmployeeById(Integer.parseInt(createUser));
            if(!StringUtils.isEmpty(productinfo.getCreateUser()) && employeeMaps.containsKey(productinfo.getCreateUser())){
                String createUser = employeeMaps.get(productinfo.getCreateUser());
                productinfo.setCreateUser(createUser);
                vo.setCreateUser(createUser);
            }else {
                productinfo.setCreateUser("");
                vo.setCreateUser("");
            }
            vo.setApproveYn((byte) 0);//审批权限
            if(vo.getApproveUserRoleId() != null ){
//                SaasRoleDto role= roleApi.getRoleById(Integer.parseInt(vo.getApproveUserRoleId().toString()));
                if(approveUserRoleMaps.containsKey(vo.getApproveUserRoleId().toString())) {
                    vo.setApproveUserRoleName(approveUserRoleMaps.get(vo.getApproveUserRoleId().toString()));
                }
                if(roleIds.contains(vo.getApproveUserRoleId().intValue())) {
                    vo.setApproveYn((byte) 1);
                }
            }
        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(pList), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getFirstProduct(HttpServletRequest request,@ApiParam(value = "首营商品查询对象", required = true) @Valid @RequestBody FirstCheckProductQueryDto firstProduct) {
        if ( StringUtils.isEmpty(firstProduct.getId()) ||StringUtils.isEmpty(firstProduct.getProductPref()))
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(""), HttpStatus.OK);

        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        ResultVO<List<Integer>> listResultVO = roleApi.queryRoleIdsByEmployeeId(Integer.parseInt(employee));
        //如果是连锁门店，需要获取总部的机构号
        String priceQueryOrganSign = "";
        String modelJson = request.getHeader("commonRequestModel");
        Byte bizModel = null;
        if(!org.springframework.util.StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            bizModel = model.getBizModel();
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                priceQueryOrganSign = organSign;
                organSign = model.getHeadquartersOrganSign();
            }
        }
        ProductDto product = productApi.getProductByPref(firstProduct.getProductPref(), organSign);
        ProductDto productdto = this.productApi.getProductByIdAndOrganSign(product.getId(), organSign, null);
        productdto.setPrescriptionClassification((productdto.getPrescriptionClassification() == null || productdto.getPrescriptionClassification() == -1 )?null:productdto.getPrescriptionClassification());
        productdto.setDosageFormId((productdto.getDosageFormId() == null || productdto.getDosageFormId() == 0 )? null:productdto.getDosageFormId());
        productdto.setAbcDividing((productdto.getAbcDividing() == null || productdto.getAbcDividing() == -1 )? null:productdto.getAbcDividing());
        productdto.setStorageCondition((productdto.getStorageCondition() == null || productdto.getStorageCondition() == -1 )? null:productdto.getStorageCondition());

        ApproveHistoryDto approveHistory=  firstCheckProductApi.getApproveHistoryByProductPrefOrganSign(firstProduct.getProductPref(),organSign);
        FirstCheckProductDto firstCheckProductDto = new FirstCheckProductDto();
        firstCheckProductDto.setId(firstProduct.getId());
        firstCheckProductDto.setProductPref(firstProduct.getProductPref());
        firstCheckProductDto.setProductinfo(productdto);
        List<Integer> result = listResultVO.getResult();
        try {
            WorkflowAuditProcessQueryDto workflowAuditProcessQueryDto = workflowAuditTaskService.queryAuditTask(approveHistory.getBillNo(), BusinessFlowEnum.SAAS_Commodity_FirstSale.getValue(), TenantIdEnum.SAAS.getValue());
            if(workflowAuditProcessQueryDto!=null){
                WorkflowAuditTaskDto activeTask = workflowAuditProcessQueryDto.getActiveTask();
                if(activeTask!=null){
                    Long candidateGroupId = activeTask.getCandidateGroupId();
                    String taskId = activeTask.getTaskId();
                    approveHistory.setApproveUserRoleId(candidateGroupId);
                    firstCheckProductDto.setTaskId(taskId);

                }
            }

        } catch (WorkflowException e) {

            logger.error("Controller首营商品获取当前审批任务失败e:{}",e);

        }
        Long approveUserRoleId = approveHistory.getApproveUserRoleId();
        firstCheckProductDto.setApproveYn((byte)0);
        if(approveUserRoleId!=null){
            Integer roleId = Integer.parseInt(approveUserRoleId.toString());
            if(result.contains(roleId)){
                firstCheckProductDto.setApproveYn((byte)1);

            }
        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(firstCheckProductDto), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> printFirstProduct(HttpServletRequest request,@ApiParam(value = "首营商品查询对象", required = true) @Valid @RequestBody  FirstCheckProductQueryDto firstProduct) {
        String pref = firstProduct.getProductPref();
        if(StringUtils.isEmpty(pref) ){
            return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.ERROR,"参数为空错误"), HttpStatus.BAD_REQUEST);
        }
        String organSign = request.getHeader("organSign");
        String headquartersOrganSign = null;
        boolean isChainStore = false;
        String modelJson = request.getHeader("commonRequestModel");
        if(!org.springframework.util.StringUtils.isEmpty(modelJson)) {
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            Byte bizModel = model.getBizModel();
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel())){
                isChainStore = true;
                if (DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())) {
                    headquartersOrganSign = model.getHeadquartersOrganSign();
                } else {
                    headquartersOrganSign = organSign;
                }
            }
        }

        //新的商品自定义分类
        Map<Long, String> customTypeIdNameMap = saasCustomTypeApi.getCustomTypeIdNameMap(headquartersOrganSign, CustomTypeBusinessTypeEnum.PRODUCT_TYPE.getValue());

        firstProduct.setOrganSign(organSign);
        FirstCheckProductVoDto firstCheckProductVoDto = firstCheckProductApi.getFirstProductChainByPref(pref, organSign);

        List<Integer> ids = new ArrayList<>();
        ids.add(DictConstant.productSystemType);   //商品类别
        ids.add(DictConstant.prescriptionBussinessId);  //药品类别
        ids.add(DictConstant.PRODUCT_THIRD_BUSSINESS_ID);  //自定义三级分类业务ID
        ids.add(DictConstant.unitBussinessId);   //单位
        ids.add(DictConstant.agentBussinessId);  //剂型
        ids.add(DictConstant.scopeOfOperation);  //经营范围
        ids.add(DictConstant.maintenanceType);  //养护类型
        ids.add(DictConstant.STORE_CONDITION_BUSINESS_ID);  //存储条件
        List<SystemDictDto> dictDtos = productDictionaryApi.findSystemDictDtoBybussinessIds(ids,organSign,1,1);
        Map<Integer,String> productSystemTypeMap = new HashMap<>();
        Map<Integer,String> prescriptionTypeMap = new HashMap<>();
        Map<Integer,String> productThirdBussinessTypeMap = new HashMap<>();
        Map<Integer,String> unitTypeMap = new HashMap<>();
        Map<Integer,String> agentTypeMap = new HashMap<>();
        Map<Integer,String> businessScopeTypeMap = new HashMap<>();
        Map<Integer,String> maintenanceTypeMap = new HashMap<>();
        Map<Integer,String> storageConditionMap = new HashMap<>();
        for(SystemDictDto sdd:dictDtos){
            if(sdd.getBussinessId().equals(DictConstant.productSystemType) ){
                productSystemTypeMap.put(sdd.getId(),sdd.getName());
            }
            if(sdd.getBussinessId().equals(DictConstant.prescriptionBussinessId) ){
                prescriptionTypeMap.put(sdd.getId(),sdd.getName());
            }
            if(sdd.getBussinessId().equals(DictConstant.PRODUCT_THIRD_BUSSINESS_ID)){
                productThirdBussinessTypeMap.put(sdd.getId(),sdd.getName());
            }
            if(sdd.getBussinessId().equals(DictConstant.unitBussinessId)){
                unitTypeMap.put(sdd.getId(),sdd.getName());
            }
            if(sdd.getBussinessId().equals(DictConstant.agentBussinessId) ){
                agentTypeMap.put(sdd.getId(),sdd.getName());
            }
            if(sdd.getBussinessId().equals(DictConstant.scopeOfOperation) ){
                businessScopeTypeMap.put(sdd.getId(),sdd.getName());
            }
            if(sdd.getBussinessId().equals(DictConstant.maintenanceType) ){
                maintenanceTypeMap.put(sdd.getId(),sdd.getName());
            }
            if(sdd.getBussinessId().equals(DictConstant.STORE_CONDITION_BUSINESS_ID) ){
                storageConditionMap.put(sdd.getId(),sdd.getName());
            }
        }
        ManufactorDto manufactor= new ManufactorDto();
        manufactor.setOrganSign(organSign);
        List<ManufactorDto> manufactorDtoList= this.manufactorApi.queryManufactorListNoPage(manufactor);
        HashMap<String, String> map = new HashMap<String, String>();
        if(manufactorDtoList!=null &&manufactorDtoList.size()>0){
            for (ManufactorDto manufactorDto:manufactorDtoList){
                map.put(manufactorDto.getId()+"",manufactorDto.getName());
            }
        }
        ProductDto productinfo = firstCheckProductVoDto.getProductinfo();
        firstCheckProductVoDto.setPharmacyPref(productinfo.getPharmacyPref());
        firstCheckProductVoDto.setCommonName(productinfo.getCommonName());
        firstCheckProductVoDto.setProductName(productinfo.getProductName());
        firstCheckProductVoDto.setBarCode(productinfo.getBarCode());
        firstCheckProductVoDto.setAttributeSpecification(productinfo.getAttributeSpecification());
        if (!StringUtils.isEmpty(productinfo.getManufacturerCode())){
            firstCheckProductVoDto.setManufacturer(map.get(productinfo.getManufacturerCode()));
        }
        firstCheckProductVoDto.setApprovalNumber(productinfo.getApprovalNumber());
        if(productinfo.getProductExtMess()!=null){
            firstCheckProductVoDto.setApprovalNumberValidity(productinfo.getProductExtMess().getApprovalNumberValidity());
        }
        firstCheckProductVoDto.setRetailPrice(productinfo.getRetailPrice());
        firstCheckProductVoDto.setVipPrice(productinfo.getVipPrice());

        firstCheckProductVoDto.setSystemTypeName(productSystemTypeMap.get(productinfo.getSystemType()));
        firstCheckProductVoDto.setPrescriptionClassificationName(prescriptionTypeMap.get(productinfo.getPrescriptionClassification()));

        if (isChainStore && productinfo.getProductType() != null && customTypeIdNameMap.get(productinfo.getProductType().longValue()) != null) {
            firstCheckProductVoDto.setProductTypeName(customTypeIdNameMap.get(productinfo.getProductType().longValue()));
        } else {
            firstCheckProductVoDto.setProductTypeName(productThirdBussinessTypeMap.get(productinfo.getProductType()));
        }

        firstCheckProductVoDto.setUnitName(unitTypeMap.get(productinfo.getUnitId()));
        firstCheckProductVoDto.setDosageFormName(agentTypeMap.get(Integer.parseInt(productinfo.getDosageFormId().toString())));
        firstCheckProductVoDto.setBusinessScopeName(businessScopeTypeMap.get(productinfo.getBusinessScope()));
        firstCheckProductVoDto.setMaintenanceType(maintenanceTypeMap.get(productinfo.getMaintenanceType()));
        firstCheckProductVoDto.setStoreConditionName(storageConditionMap.get(productinfo.getStorageCondition()));

        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(firstCheckProductVoDto), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getApproveFlowList(HttpServletRequest request, @ApiParam(value = "首营商品查询对象", required = true) @Valid @RequestBody FirstCheckProductQueryDto firstProduct) {

        String pref = firstProduct.getProductPref();
        if(StringUtils.isEmpty(pref) ){
            return new ResponseEntity<ResultVO>(ResultVO.createError(ResultCodeEnum.ERROR,"参数为空错误"), HttpStatus.BAD_REQUEST);
        }
        String organSign = request.getHeader("organSign");
        //如果是连锁门店，需要获取总部的机构号
        String priceQueryOrganSign = "";
        String modelJson = request.getHeader("commonRequestModel");
        Byte bizModel = null;
        if(!org.springframework.util.StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            bizModel = model.getBizModel();
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                priceQueryOrganSign = organSign;
                organSign = model.getHeadquartersOrganSign();
            }
        }
        List<WorkflowAuditLogQueryDto> workflowLogQueryDtos = firstCheckProductApi.getApproveFlowList(pref,organSign);
        return  new ResponseEntity<ResultVO>(ResultVO.createSuccess(workflowLogQueryDtos), HttpStatus.OK);
    }

}

