package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.member.core.api.MemberBaseApi;
import com.xyy.saas.member.core.api.MemberExchangeProductApi;
import com.xyy.saas.member.core.api.MemberLevelApi;
import com.xyy.saas.member.core.dto.MemberBaseDto;
import com.xyy.saas.member.core.dto.MemberExchangeProductDto;
import com.xyy.saas.member.core.dto.MemberLevelDto;
import com.xyy.saas.member.core.dto.MemberLevelEquitiesDto;
import com.xyy.saas.rabbitmq.core.api.MessagePushApi;
import com.xyy.saas.web.api.module.member.model.MemberOneMoneyVo;
import com.xyy.saas.web.api.module.member.service.MemberExchangeProductService;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.api.RoleApi;
import com.xyy.user.module.dto.EmployeeListRequestModel;
import com.xyy.user.module.dto.result.QueryDrugstoreDto;
import com.xyy.user.module.dto.result.QueryEmployeePrimaryVO;
import com.xyy.user.module.dto.result.RoleDto;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-11-12T17:16:06.377+08:00")
@Controller
@RequestMapping("/member/member/oneMoney")
public class MemberOneMoneyApiController {

    private static final Logger logger = LogManager.getLogger(MemberOneMoneyApiController.class);

    @Reference(version = "0.0.1")
    private MessagePushApi messagePushApi;

    @Reference(version = "0.0.1")
    private MemberBaseApi memberBaseApi;

    @Reference(version = "0.0.1")
    private EmployeeApi employeeApi;

    @Reference(version = "0.0.1")
    private RoleApi roleApi;

    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;

    @Reference(version = "0.0.1")
    private MemberLevelApi memberLevelApi;

    @Autowired
    private MemberExchangeProductService memberExchangeProductService;

    @ApiOperation(value = "壹块钱端查询会员等级列表", notes = "壹块钱端查询会员等级列表", response = String.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = String.class)})
    @RequestMapping(value = "/memberLevel/query", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> queryOneMoneyMemberLevel(@ApiParam(value = "请求信息对象" ,required=true ) @Valid @RequestBody MemberOneMoneyVo memberOneMoneyVo) {
        try {
            if (StringUtils.isEmpty(memberOneMoneyVo.getOrganSign())) {
                return new ResponseEntity(new ResultVO(-1, "参数organSign不能为空。", false), HttpStatus.OK);
            }
            com.xyy.saas.common.util.ResultVO<QueryDrugstoreDto> dtoResultVO = drugstoreApi.queryDrugstoreByOrganSign(memberOneMoneyVo.getOrganSign());
            QueryDrugstoreDto model = dtoResultVO.getResult();
            if (model == null) {
                return new ResponseEntity(new ResultVO(-1, "该" + memberOneMoneyVo.getOrganSign() + "号关联的药店被冻结或不存在，请核对药店机构号在重新添加会员。", false), HttpStatus.OK);
            }
            Byte bizModel = model.getBizModel();
            Byte organSignType = model.getOrganSignType();
            // `organ_sign_type` 机构类型 1、门店  3、总部',
            // `biz_model` '经营模式 1、单体 2、连锁 3、联营',
            MemberLevelDto baseDto = new MemberLevelDto();
            if(bizModel==1){
                baseDto.setOrgansign(memberOneMoneyVo.getOrganSign());
            } // 联营连锁
            else {
                if(organSignType == 3){
                    baseDto.setOrgansign(model.getOrganSign());
                }else{
                    baseDto.setOrgansign(model.getHeadquartersOrganSign());
                }
            }
            logger.info("MemberLevelDto为："+JSONObject.toJSON(baseDto));
            List<MemberLevelDto> memberLevelList = memberLevelApi.getMemberLevelList(baseDto);
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(memberLevelList), HttpStatus.OK);
        } catch (Exception e) {
            logger.error("查询会员等级列表失败,异常为："+e);
            return new ResponseEntity(new ResultVO(-1, "查询会员等级列表失败", false), HttpStatus.OK);
        }
    }


    @ApiOperation(value = "壹块钱端新增会员", notes = "壹块钱端新增会员", response = String.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = String.class)})
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> saveOneMoneyMember(@ApiParam(value = "请求信息对象" ,required=true ) @Valid @RequestBody MemberOneMoneyVo memberOneMoneyVo) {
        try {
            if (StringUtils.isEmpty(memberOneMoneyVo.getName())) {
                return new ResponseEntity(new ResultVO(-1, "参数name不能为空。", false), HttpStatus.OK);
            }
            if (memberOneMoneyVo.getSex() == null) {
                return new ResponseEntity(new ResultVO(-1, "参数sex不能为空。", false), HttpStatus.OK);
            }
            if (memberOneMoneyVo.getVipLevelId() == null) {
                return new ResponseEntity(new ResultVO(-1, "参数vipLevelId不能为空。", false), HttpStatus.OK);
            }
            if (StringUtils.isEmpty(memberOneMoneyVo.getTelephone())) {
                return new ResponseEntity(new ResultVO(-1, "参数telephone不能为空。", false), HttpStatus.OK);
            }
            if (StringUtils.isEmpty(memberOneMoneyVo.getOrganSign())) {
                return new ResponseEntity(new ResultVO(-1, "参数organSign不能为空。", false), HttpStatus.OK);
            }
            com.xyy.saas.common.util.ResultVO<QueryDrugstoreDto> dtoResultVO = drugstoreApi.queryDrugstoreByOrganSign(memberOneMoneyVo.getOrganSign());
            QueryDrugstoreDto model = dtoResultVO.getResult();
            if (model == null) {
                return new ResponseEntity(new ResultVO(-1, "该" + memberOneMoneyVo.getOrganSign() + "号关联的药店被冻结或不存在，请核对药店机构号在重新添加会员。", false), HttpStatus.OK);
            }
            String cardNo = memberBaseApi.getMemberCardNo();
            MemberBaseDto dto = new MemberBaseDto();
            Byte bizModel = model.getBizModel();
            // 经营模式 1、单体 2、连锁 3、联营
            if (bizModel == 1 || bizModel == 3) {
                dto.setOrgansign(model.getOrganSign());
            }
            if (bizModel == 2) {
                dto.setHeadquartersOrganSign(model.getHeadquartersOrganSign());
            }
            dto.setTelephone(memberOneMoneyVo.getTelephone());
            long existTelephone = memberBaseApi.getMemberBaseByCondition(dto);
            if (existTelephone > 0 && memberOneMoneyVo.getTelephone() != null) {
                return new ResponseEntity(new ResultVO(-1, "电话号码已注册，请重新输入电话号码！", false), HttpStatus.OK);
            }
            dto.setTelephone(null);
            dto.setCartNo(cardNo);
            long existCartNo = memberBaseApi.getMemberBaseByCondition(dto);
            if (existCartNo > 0 && cardNo != null) {
                return new ResponseEntity(new ResultVO(-1, "会员卡号已注册，请重新输入会员卡号！", false), HttpStatus.OK);
            }
            MemberBaseDto baseDto = new MemberBaseDto();
            baseDto.setName(memberOneMoneyVo.getName());
            baseDto.setSex(memberOneMoneyVo.getSex());
            baseDto.setVipLevelId(memberOneMoneyVo.getVipLevelId());
            baseDto.setTelephone(memberOneMoneyVo.getTelephone());
            baseDto.setOrgansign(memberOneMoneyVo.getOrganSign());
            baseDto.setCartNo(cardNo);
            baseDto.setPasswd("123456");
            baseDto.setState(1);
            if (bizModel != 1) {
                if(model.getOrganSignType() == 3){
                    baseDto.setHeadquartersOrganSign(model.getOrganSign());
                }else{
                    baseDto.setHeadquartersOrganSign(model.getHeadquartersOrganSign());
                }
            }
            com.xyy.saas.common.util.ResultVO<List<RoleDto>> listResultVO = roleApi.querySystemRoleInfo();
            List<RoleDto> result1 = listResultVO.getResult();
            for (RoleDto roleDto : result1) {
                if ("药店负责人".equals(roleDto.getName()) && "0".equals(roleDto.getOrganSign())) {
                    EmployeeListRequestModel bean = new EmployeeListRequestModel();
                    bean.setRoleId(roleDto.getId());
                    bean.setRoleName("药店负责人");
                    com.xyy.saas.common.util.ResultVO<PageInfo<QueryEmployeePrimaryVO>> result = new com.xyy.saas.common.util.ResultVO();
                    result = employeeApi.queryEmployeeByCondition(bean, memberOneMoneyVo.getOrganSign());
                    List<QueryEmployeePrimaryVO> list = result.getResult().getList();
                    if (list != null && list.size() > 0) {
                        baseDto.setCreateUser(list.get(0).getEmployeeId() + "");
                    } else {
                        return new ResponseEntity(new ResultVO(-1, "该药店没有药店负责人，暂时无法创建会员信息。", false), HttpStatus.OK);
                    }
                }
            }
            boolean result = false;
            logger.info("MemberBaseDto为:"+JSONObject.toJSON(baseDto));
            result = memberBaseApi.saveMemberBase(baseDto);
            if (!result) {
                logger.info("MemberOneMoneyApiController.saveOneMoneyMember: 壹块钱会员新增失败。");
                return new ResponseEntity(new ResultVO(-1, "会员新增失败", false), HttpStatus.OK);
            }
            return new ResponseEntity(new ResultVO(0, "新增会员成功", true), HttpStatus.OK);
        } catch (Exception e) {
            logger.error("新增会员失败,异常为："+e);
            return new ResponseEntity(new ResultVO(-1, "新增会员失败", false), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "壹块钱端查询会员详情", notes = "壹块钱端查询会员详情", response = String.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = String.class)})
    @RequestMapping(value = "/query", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> queryOneMoneyMember(@ApiParam(value = "请求信息对象" ,required=true ) @Valid @RequestBody MemberOneMoneyVo memberOneMoneyVo) {
        MemberLevelEquitiesDto equitiesDto = new MemberLevelEquitiesDto();
        try {
            if (StringUtils.isEmpty(memberOneMoneyVo.getTelephone())) {
                return new ResponseEntity(new ResultVO(-1, "参数telephone不能为空。", false), HttpStatus.OK);
            }
            if (StringUtils.isEmpty(memberOneMoneyVo.getOrganSign())) {
                return new ResponseEntity(new ResultVO(-1, "参数organSign不能为空。", false), HttpStatus.OK);
            }
            com.xyy.saas.common.util.ResultVO<QueryDrugstoreDto> dtoResultVO = drugstoreApi.queryDrugstoreByOrganSign(memberOneMoneyVo.getOrganSign());
            QueryDrugstoreDto model = dtoResultVO.getResult();
            if (model == null) {
                return new ResponseEntity(new ResultVO(-1, "该" + memberOneMoneyVo.getOrganSign() + "号关联的药店被冻结或不存在，请核对药店机构号在重新添加会员。", false), HttpStatus.OK);
            }
            MemberBaseDto baseDto = new MemberBaseDto();
            baseDto.setOrgansign(memberOneMoneyVo.getOrganSign());
            baseDto.setTelephone(memberOneMoneyVo.getTelephone());
            logger.info("MemberBaseDto为:"+JSONObject.toJSON(baseDto));
            List<MemberBaseDto> list = memberBaseApi.getMemberBaseList(baseDto);
            if (list != null && list.size() > 0) {
                MemberBaseDto memberBaseDto = list.get(0);
                //权益中会员等级查询未用到organSign
                equitiesDto = memberLevelApi.getMemberLevelEquities(memberBaseDto.getVipLevelId(), memberOneMoneyVo.getOrganSign());
                equitiesDto.setCartNo(memberBaseDto.getCartNo() == null ? "" : memberBaseDto.getCartNo());
                equitiesDto.setPoint(memberBaseDto.getPoint() == null ? new BigDecimal(0) : memberBaseDto.getPoint());
                equitiesDto.setName(memberBaseDto.getName() == null ? "" : memberBaseDto.getName());
                equitiesDto.setDrugstoreName(model.getDrugstoreName() == null ? "" : model.getDrugstoreName());
                MemberExchangeProductDto exchangeProductDto= new MemberExchangeProductDto();
                exchangeProductDto.setYn(1);
                //1防检查身份
                exchangeProductDto.setIshidden((byte)1);
                exchangeProductDto.setOrganSign(memberOneMoneyVo.getOrganSign());
                List<MemberExchangeProductDto> exchangeProductList = memberExchangeProductService.getYKQExchangeProductList(exchangeProductDto);
                equitiesDto.setMemberExchangeProductDtoList(exchangeProductList);
            } else {
                Byte bizModel = model.getBizModel();
                Byte organSignType = model.getOrganSignType();
                //查询折扣率最低的会员等级
                MemberLevelDto memberLevelDto = new MemberLevelDto();
                if (bizModel == 1) {
                    memberLevelDto.setOrgansign(memberOneMoneyVo.getOrganSign());
                } // 联营连锁
                else {
                    if (organSignType == 3) {
                        memberLevelDto.setOrgansign(model.getOrganSign());
                    }else{
                        memberLevelDto.setOrgansign(model.getHeadquartersOrganSign());
                    }
                }
                List<MemberLevelDto> levelList = memberLevelApi.getMemberLevelList(memberLevelDto);
                if (levelList != null && levelList.size() > 0) {
                    //权益中会员等级查询未用到organSign
                    equitiesDto = memberLevelApi.getMemberLevelEquities(levelList.get(0).getId(), memberOneMoneyVo.getOrganSign());
                    equitiesDto.setDrugstoreName(model.getDrugstoreName() == null ? "" : model.getDrugstoreName());
                    MemberExchangeProductDto exchangeProductDto= new MemberExchangeProductDto();
                    exchangeProductDto.setYn(1);
                    //1防检查身份
                    exchangeProductDto.setOrganSign(memberOneMoneyVo.getOrganSign());
                    exchangeProductDto.setIshidden((byte)1);
                    List<MemberExchangeProductDto> exchangeProductList = memberExchangeProductService.getYKQExchangeProductList(exchangeProductDto);
                    equitiesDto.setMemberExchangeProductDtoList(exchangeProductList);
                } else {
                    return new ResponseEntity(new ResultVO(-1, "未查询到会员信息并没有相关会员等级信息。", false), HttpStatus.OK);
                }
            }
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(equitiesDto), HttpStatus.OK);
        } catch (Exception e) {
            logger.error("MemberBaseDto为:"+e);
            return new ResponseEntity(new ResultVO(-1, "查询会员相关信息异常。", false), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "壹块钱端查询药店会员卡包", notes = "壹块钱端查询药店会员卡包", response = String.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = String.class)})
    @RequestMapping(value = "/memberCardBag/query", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> queryMemberCardBag(@ApiParam(value = "请求信息对象" ,required=true ) @Valid @RequestBody MemberOneMoneyVo memberOneMoneyVo) {
        List<MemberLevelEquitiesDto> list = new ArrayList<>();
        try {
            if (StringUtils.isEmpty(memberOneMoneyVo.getTelephone())) {
                return new ResponseEntity(new ResultVO(-1, "参数telephone不能为空。", false), HttpStatus.OK);
            }
            logger.info("memberOneMoneyVo为:"+JSONObject.toJSON(memberOneMoneyVo));
            List<MemberBaseDto> memberBaseDtos = memberBaseApi.getMemberBaseListByTelephone(memberOneMoneyVo.getTelephone());
            if (memberBaseDtos != null && memberBaseDtos.size() > 0) {
                for (MemberBaseDto memberBaseDto : memberBaseDtos) {
                    com.xyy.saas.common.util.ResultVO<QueryDrugstoreDto> dtoResultVO = drugstoreApi.queryDrugstoreByOrganSign(memberBaseDto.getOrgansign());
                    QueryDrugstoreDto model = dtoResultVO.getResult();
                    if (model != null) {
                        MemberLevelEquitiesDto equitiesDto = new MemberLevelEquitiesDto();
                        //权益中会员等级查询未用到organSign
                        equitiesDto = memberLevelApi.getMemberLevelEquities(memberBaseDto.getVipLevelId(), memberBaseDto.getOrgansign());
                        equitiesDto.setCartNo(memberBaseDto.getCartNo() == null ? "" : memberBaseDto.getCartNo());
                        equitiesDto.setPoint(memberBaseDto.getPoint() == null ? new BigDecimal(0) : memberBaseDto.getPoint());
                        equitiesDto.setName(memberBaseDto.getName() == null ? "" : memberBaseDto.getName());
                        equitiesDto.setDrugstoreName(model.getDrugstoreName() == null ? "" : model.getDrugstoreName());
                        list.add(equitiesDto);
                    }
                }
            }
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(list), HttpStatus.OK);
        } catch (Exception e) {
            logger.error("MemberBaseDto为:"+e);
            return new ResponseEntity(new ResultVO(-1, "查询会员卡包相关信息异常。", false), HttpStatus.OK);
        }
    }

}
