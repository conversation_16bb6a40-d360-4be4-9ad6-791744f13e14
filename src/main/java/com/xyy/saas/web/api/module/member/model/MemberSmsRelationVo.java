package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 短信会员关系
 * @Author: sdb
 */
public class MemberSmsRelationVo implements Serializable {

    private static final long serialVersionUID = -3942101882123256481L;

    private Long id;
    /**
     * 短信记录id
     */
    @ApiModelProperty(value = "短信记录id")
    private Long smsRecordId;
    /**
     * 会员id
     */
    @ApiModelProperty(value = "会员id")
    private Long memberId;
    /**
     * 会员唯一标识
     */
    @ApiModelProperty(value = "会员唯一标识")
    private String memberGuid;
    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识")
    private String organSign;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    private Integer page;
    /**
     * 页码大小
     */
    @ApiModelProperty(value = "页码大小")
    private Integer rows;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSmsRecordId() {
        return smsRecordId;
    }

    public void setSmsRecordId(Long smsRecordId) {
        this.smsRecordId = smsRecordId;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public String getMemberGuid() {
        return memberGuid;
    }

    public void setMemberGuid(String memberGuid) {
        this.memberGuid = memberGuid == null ? null : memberGuid.trim();
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign == null ? null : organSign.trim();
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }
}



