package com.xyy.saas.web.api.module.product.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "说明书指导单", description = "说明书指导单")
public class DirectionAndGuidanceResultVo {

    @ApiModelProperty(
            value = "通用名称",
            name = "productName"
    )
    private String productName;

    @ApiModelProperty(
            value = "适应症",
            name = "indication"
    )
    private String indication;

    @ApiModelProperty(
            value = "不良反应",
            name = "untowardeffect"
    )
    private String untowardeffect;

    @ApiModelProperty(
            value = "储存注意事项",
            name = "considerations"
    )
    private String considerations;

    @ApiModelProperty(
            value = "用法用量",
            name = "usageanddosage"
    )
    private String usageanddosage;

    @ApiModelProperty(
            value = "禁忌症",
            name = "contraindications"
    )
    private String contraindications;

    @ApiModelProperty(
            value = "注意事项",
            name = "announcements"
    )
    private String announcements;

    @ApiModelProperty(
            value = "保质期",
            name = "usedays"
    )
    private Integer usedays;

    @ApiModelProperty(
            value = "存储条件",
            name = "cunchutiaojian"
    )
    private String cunchutiaojian;

    @ApiModelProperty(
            value = "药品成分",
            name = "ingredient"
    )
    private String ingredient;

    public String getProductName() {
        return productName==null?"":productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getIndication() {
        return indication==null?"":indication;
    }

    public void setIndication(String indication) {
        this.indication = indication;
    }

    public String getUntowardeffect() {
        return untowardeffect==null?"":untowardeffect;
    }

    public void setUntowardeffect(String untowardeffect) {
        this.untowardeffect = untowardeffect;
    }

    public String getConsiderations() {
        return considerations==null?"":considerations;
    }

    public void setConsiderations(String considerations) {
        this.considerations = considerations;
    }

    public String getUsageanddosage() {
        return usageanddosage==null?"":usageanddosage;
    }

    public void setUsageanddosage(String usageanddosage) {
        this.usageanddosage = usageanddosage;
    }

    public String getContraindications() {
        return contraindications==null?"":contraindications;
    }

    public void setContraindications(String contraindications) {
        this.contraindications = contraindications;
    }

    public String getAnnouncements() {
        return announcements==null?"":announcements;
    }

    public void setAnnouncements(String announcements) {
        this.announcements = announcements;
    }

    public Integer getUsedays() {
        return usedays;
    }

    public void setUsedays(Integer usedays) {
        this.usedays = usedays==null?0:usedays;
    }

    public String getCunchutiaojian() {
        return cunchutiaojian==null?"":cunchutiaojian;
    }

    public void setCunchutiaojian(String cunchutiaojian) {
        this.cunchutiaojian = cunchutiaojian;
    }

    public String getIngredient() {
        return ingredient==null?"":ingredient;
    }

    public void setIngredient(String ingredient) {
        this.ingredient = ingredient;
    }
}
