package com.xyy.saas.web.api.module.product.model;

import java.math.BigDecimal;
import java.util.Date;

public class PromotionPlanDetailVo  {

	private Long id;
	private String promotionPref;//促销活动编号
	private String productPref;//赠送商品或者特价商品编号
	private BigDecimal giftProductPrice;//赠送商品价格
	private BigDecimal promotionPrice;//促销价
	private BigDecimal promotionStore;//限购库存
	private BigDecimal saleNumber;//已卖数量
	private BigDecimal perLimit;//人均限购量
	private Long version;//版本号
	private String createUser;//创建人
	private Date createTime;//创建时间
	private String updateUser;//更新人
	private Date updateTime;//更新时间
	private Byte yn;//逻辑删除 1 有效 0 删除
	private String baseVersion;//操作版本号
	private String organSign;//药店唯一标识

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getPromotionPref() {
		return promotionPref;
	}

	public void setPromotionPref(String promotionPref) {
		this.promotionPref = promotionPref;
	}

	public String getProductPref() {
		return productPref;
	}

	public void setProductPref(String productPref) {
		this.productPref = productPref;
	}

	public BigDecimal getGiftProductPrice() {
		return giftProductPrice;
	}

	public void setGiftProductPrice(BigDecimal giftProductPrice) {
		this.giftProductPrice = giftProductPrice;
	}

	public BigDecimal getPromotionPrice() {
		return promotionPrice;
	}

	public void setPromotionPrice(BigDecimal promotionPrice) {
		this.promotionPrice = promotionPrice;
	}

	public BigDecimal getPromotionStore() {
		return promotionStore;
	}

	public void setPromotionStore(BigDecimal promotionStore) {
		this.promotionStore = promotionStore;
	}

	public BigDecimal getSaleNumber() {
		return saleNumber;
	}

	public void setSaleNumber(BigDecimal saleNumber) {
		this.saleNumber = saleNumber;
	}

	public BigDecimal getPerLimit() {
		return perLimit;
	}

	public void setPerLimit(BigDecimal perLimit) {
		this.perLimit = perLimit;
	}

	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public String getCreateUser() {
		return createUser;
	}

	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getUpdateUser() {
		return updateUser;
	}

	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Byte getYn() {
		return yn;
	}

	public void setYn(Byte yn) {
		this.yn = yn;
	}

	public String getBaseVersion() {
		return baseVersion;
	}

	public void setBaseVersion(String baseVersion) {
		this.baseVersion = baseVersion;
	}

	public String getOrganSign() {
		return organSign;
	}

	public void setOrganSign(String organSign) {
		this.organSign = organSign;
	}
}
