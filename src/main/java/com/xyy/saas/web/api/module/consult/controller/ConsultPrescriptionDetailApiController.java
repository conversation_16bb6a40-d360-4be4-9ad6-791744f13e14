package com.xyy.saas.web.api.module.consult.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.consult.cores.dto.ConsultPrescriptionDto;
import com.xyy.saas.consult.cores.api.ConsultPrescriptionDetailApi;
import com.xyy.saas.consult.cores.dto.ConsultPrescriptionDetailDto;
import com.xyy.saas.web.api.module.consult.model.ConsultPrescriptionDetailVo;
import com.xyy.saas.web.api.module.consult.model.ConsultPrescriptionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;


@Controller
@RequestMapping(value = "/member/consultPrescriptionDetail")
@Api(value = "consultPrescription", description = "远程问诊第三方处方药明细API")
public class ConsultPrescriptionDetailApiController {

    @Reference(version = "0.0.2")
    private ConsultPrescriptionDetailApi consultPrescriptionDetailApi;

    @ApiOperation(value = "根据远程问诊id查询处方药明细", notes = "根据远程问诊id查询处方药明细", response = ConsultPrescriptionDto.class, tags = {"consultPrescriptionDetail",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ConsultPrescriptionDto.class)})
    @RequestMapping(value = "/selectByConsultRecordId", method = RequestMethod.POST,produces =  "application/json",
            consumes = "application/json")
    ResponseEntity<ResultVO> selectByConsultRecordId(@RequestHeader(name = "organSign", required = true) String organSign,
                                                     @RequestBody ConsultPrescriptionVo consultPrescriptionVo) {
        Long consultRecordId = consultPrescriptionVo.getConsultRecordId();
        if (consultRecordId == null || consultRecordId <= 0) {
            new ResponseEntity(new ResultVO(-1, "问诊id不能为空", false), HttpStatus.OK);
        }
        List<ConsultPrescriptionDetailDto> list = consultPrescriptionDetailApi.selectListByConsultRecordId(consultRecordId, organSign, null);
        return new ResponseEntity(ResultVO.createSuccess(list), HttpStatus.OK);
    }

    @ApiOperation(value = "重新匹配处方药明细", notes = "重新匹配处方药明细", response = ConsultPrescriptionDto.class, tags = {"consultPrescriptionDetail",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ConsultPrescriptionDto.class)})
    @RequestMapping(value = "/rematch", method = RequestMethod.POST,produces =  "application/json",
            consumes = "application/json")
    ResponseEntity<ResultVO> rematch(@RequestHeader(name = "organSign", required = true) String organSign,
                                     @RequestBody ConsultPrescriptionVo consultPrescriptionVo) {
        String prescriptionGuid = consultPrescriptionVo.getPrescriptionGuid();
        if (StringUtils.isEmpty(prescriptionGuid)) {
            return new ResponseEntity(new ResultVO(-1, "gsp处方guid不能为空", false), HttpStatus.OK);
        }
//        List<String> productPrefs = consultPrescriptionVo.getProductPrefs();
//        if (CollectionUtils.isEmpty(productPrefs)) {
//            return new ResponseEntity(new ResultVO(-1, "请选择需要匹配的商品", false), HttpStatus.OK);
//        }
        List<ConsultPrescriptionDetailDto> list = consultPrescriptionDetailApi.selectListByPrescriptionGuid(prescriptionGuid, organSign);
        // 过滤选择的
//        list = list.stream().filter(item -> {
//            return productPrefs.contains(item.getProductPref());
//        }).collect(Collectors.toList());
        return new ResponseEntity(ResultVO.createSuccess(list), HttpStatus.OK);
    }

    @ApiOperation(value = "手工匹配", notes = "手工匹配", response = ConsultPrescriptionDto.class, tags = {"consultPrescriptionDetail",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ConsultPrescriptionDto.class)})
    @RequestMapping(value = "/updateById", method = RequestMethod.POST,produces =  "application/json",
            consumes = "application/json")
    ResponseEntity<ResultVO> rematch(@RequestHeader(name = "organSign", required = true) String organSign,
                                     @RequestBody ConsultPrescriptionDetailVo detailVo) {
        Long id = detailVo.getId();
        if (id == null || id <= 0) {
            return new ResponseEntity(new ResultVO(-1, "id不能为空", false), HttpStatus.OK);
        }
        String productPref = detailVo.getProductPref();
        if (StringUtils.isEmpty(productPref)) {
            return new ResponseEntity(new ResultVO(-1, "商品编码不能为空", false), HttpStatus.OK);
        }
        ConsultPrescriptionDetailDto dto = new ConsultPrescriptionDetailDto();
        dto.setOrganSign(organSign);
        dto.setId(id);
        dto.setProductPref(productPref);
        return new ResponseEntity(ResultVO.createSuccess(consultPrescriptionDetailApi.updateById(dto)), HttpStatus.OK);
    }
}

