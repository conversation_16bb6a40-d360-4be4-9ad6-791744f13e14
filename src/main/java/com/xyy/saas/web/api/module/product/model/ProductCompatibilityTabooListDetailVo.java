package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T17:19:18.933+08:00")

@ApiModel(description = "商品配伍禁忌子表详情信息类")
public class ProductCompatibilityTabooListDetailVo {

    @JsonProperty("id")
    private Long id;//数据库主键id

    @JsonProperty("subPref")
    private String subPref;//对应主表商品的配伍禁忌商品内码

    @JsonProperty("yn")
    private Byte yn;//逻辑删除 1 有效 0 删除

    @JsonProperty("baseVersion")
    private Long baseVersion;//操作版本号

    @JsonProperty("organSign")
    private String organSign;//药店唯一标识

    @JsonProperty("createUser")
    private String createUser;//创建人

    @JsonProperty("createTime")
    private String createTime; //创建时间

    @JsonProperty("updateUser")
    private String updateUser;//更新人

    @JsonProperty("updateTime")
    private Date updateTime;//更新时间

    @JsonProperty("guid")
    private String guid;//全局唯一id

    @JsonProperty("pharmacyPref")
    private String pharmacyPref;//商品编号（页面展示用）

    @JsonProperty("productName")
    private String productName;//商品名称（页面展示用）

    @JsonProperty("commonName")
    private String commonName;//通用名称（页面展示用）

    @JsonProperty("attributeSpecification")
    private String attributeSpecification;//规格（页面展示）

    @JsonProperty("unitId")
    private Integer unitId;//单位id（传值，不显示）

    @JsonProperty("unitName")
    private String unitName;//单位名称（页面展示）

    @JsonProperty("dosageFormId")
    private Long dosageFormId;//剂型
    @JsonProperty("manufacturer")
    private String manufacturer;//生产厂家
    @JsonProperty("approvalNumber")
    private String approvalNumber;//批准文号

    @JsonProperty("prescriptionClassification")
    private Integer prescriptionClassification; //处方分类
    @JsonProperty("medicalInsurance")
    private Byte medicalInsurance;//是否医保，0非医保，1医保

    private String  drugPermissionPerson; //药品上市许可证持有人

    public String getDrugPermissionPerson() {
        return drugPermissionPerson;
    }

    public void setDrugPermissionPerson(String drugPermissionPerson) {
        this.drugPermissionPerson = drugPermissionPerson;
    }

    public Integer getSystemType() {
        return systemType;
    }

    public void setSystemType(Integer systemType) {
        this.systemType = systemType;
    }

    public Integer getProductType() {
        return productType;
    }

    public void setProductType(Integer productType) {
        this.productType = productType;
    }

    private Integer systemType;//商品系统分类
    private Integer productType;//商品功能分类

    @ApiModelProperty(value = "剂型")
    public Long getDosageFormId() {
        return dosageFormId;
    }

    public void setDosageFormId(Long dosageFormId) {
        this.dosageFormId = dosageFormId;
    }

    @ApiModelProperty(value = "生产厂家")
    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    @ApiModelProperty(value = "批准文号")
    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }


    @ApiModelProperty(value = "主键id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @ApiModelProperty(value = "对应主表商品的配伍禁忌商品内码")
    public String getSubPref() {
        return subPref;
    }

    public void setSubPref(String subPref) {
        this.subPref = subPref;
    }

    @ApiModelProperty(value = "逻辑删除 1 有效 0 删除")
    public Byte getYn() {
        return yn;
    }

    public void setYn(Byte yn) {
        this.yn = yn;
    }

    @ApiModelProperty(value = "操作版本号")
    public Long getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(Long baseVersion) {
        this.baseVersion = baseVersion;
    }

    @ApiModelProperty(value = "药店唯一标识")
    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    @ApiModelProperty(value = "创建人")
    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    @ApiModelProperty(value = "创建时间")
    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    @ApiModelProperty(value = "更新人")
    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    @ApiModelProperty(value = "更新时间")
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @ApiModelProperty(value = "全局唯一id")
    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    @ApiModelProperty(value = "商品编号（页面展示用）")
    public String getPharmacyPref() {
        return pharmacyPref;
    }

    public void setPharmacyPref(String pharmacyPref) {
        this.pharmacyPref = pharmacyPref;
    }

    @ApiModelProperty(value = "商品名称（页面展示用）")
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    @ApiModelProperty(value = "通用名称（页面展示用）")
    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    @ApiModelProperty(value = "规格（页面展示）")
    public String getAttributeSpecification() {
        return attributeSpecification;
    }

    public void setAttributeSpecification(String attributeSpecification) {
        this.attributeSpecification = attributeSpecification;
    }

    @ApiModelProperty(value = "单位id（传值，不显示）")
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @ApiModelProperty(value = "单位名称（页面展示）")
    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }


    public Integer getPrescriptionClassification() {
        return prescriptionClassification;
    }

    public void setPrescriptionClassification(Integer prescriptionClassification) {
        this.prescriptionClassification = prescriptionClassification;
    }

    public Byte getMedicalInsurance() {
        return medicalInsurance;
    }

    public void setMedicalInsurance(Byte medicalInsurance) {
        this.medicalInsurance = medicalInsurance;
    }
}
