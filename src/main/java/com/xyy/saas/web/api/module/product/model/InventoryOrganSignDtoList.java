package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * @annotation:
 * <AUTHOR>
 * @create 2019-05-08 20:15
 **/
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-05-08T17:03:47.479+08:00")
public class InventoryOrganSignDtoList implements Serializable {
    private static final long serialVersionUID = 3158011111135407620L;
    @JsonProperty("inventoryOrganSignDtos")
    private List<InventoryOrganSignDto> inventoryOrganSignDtos;
    @ApiModelProperty(value = "参数明细")
    public List<InventoryOrganSignDto> getInventoryOrganSignDtos() {
        return inventoryOrganSignDtos;
    }

    public void setInventoryOrganSignDtos(List<InventoryOrganSignDto> inventoryOrganSignDtos) {
        this.inventoryOrganSignDtos = inventoryOrganSignDtos;
    }
}

