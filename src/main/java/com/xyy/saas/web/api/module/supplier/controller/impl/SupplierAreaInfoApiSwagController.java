package com.xyy.saas.web.api.module.supplier.controller.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.supplier.api.AreaBaseinfoApi;
import com.xyy.saas.supplier.api.SupplierAreaInfoApi;
import com.xyy.saas.supplier.dto.AreaBaseinfoDto;
import com.xyy.saas.supplier.dto.ProviderAreaInfoDto;
import com.xyy.saas.web.api.module.supplier.controller.SupplierAreaInfoApiSwag;
import com.xyy.saas.web.api.module.supplier.model.AreaBaseinfo;
import com.xyy.saas.web.api.module.supplier.model.ProviderAreaInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Classname SupplierAreaInfoApiSwagController
 * @Description TODO
 * @Date 2020/5/15 17:41
 */
@Controller
public class SupplierAreaInfoApiSwagController implements SupplierAreaInfoApiSwag {

    @Reference(version = "0.0.1")
    private AreaBaseinfoApi areaBaseinfoApi;

    @Reference(version = "0.0.1")
    private SupplierAreaInfoApi supplierAreaInfoApi;

    @Override
    public ResponseEntity<ResultVO> findAreaBaseinfoByCondition(HttpServletRequest request, @NotNull @RequestBody AreaBaseinfo areaBaseinfo) {
        AreaBaseinfoDto areaBaseinfoDto = new AreaBaseinfoDto();
        BeanUtils.copyProperties(areaBaseinfo,areaBaseinfoDto);
        ResultVO<PageInfo<AreaBaseinfoDto>> areaBaseinfoByCondition = areaBaseinfoApi.findAreaBaseinfoByCondition(areaBaseinfoDto);
        return new ResponseEntity<>(areaBaseinfoByCondition, HttpStatus.OK);
    }

    /**
     * 分页查询供应商和区域码关系
     *
     * @param request
     * @param providerAreaInfo
     * @return
     */
    @Override
    public ResponseEntity<ResultVO> findSupplierAreaByCondition(HttpServletRequest request, @NotNull @RequestBody ProviderAreaInfo providerAreaInfo) {
        String organSign = request.getHeader("organSign");
        ProviderAreaInfoDto providerAreaInfoDto = new ProviderAreaInfoDto();
        BeanUtils.copyProperties(providerAreaInfo,providerAreaInfoDto);
        providerAreaInfoDto.setOrgansign(organSign);
        ResultVO<PageInfo<ProviderAreaInfoDto>> supplierAreaByCondition = supplierAreaInfoApi.findSupplierAreaByCondition(providerAreaInfoDto);
        return new ResponseEntity<>(supplierAreaByCondition, HttpStatus.OK);
    }

    /**
     * 保存供应商与区域编码关系
     *
     * @param request
     * @param providerAreaInfo
     * @return
     */
    @Override
    public ResponseEntity<ResultVO> saveSupplierAreaByCondition(HttpServletRequest request, @NotNull @RequestBody ProviderAreaInfo providerAreaInfo) {
        String organSign = request.getHeader("organSign");
        ProviderAreaInfoDto providerAreaInfoDto = new ProviderAreaInfoDto();
        BeanUtils.copyProperties(providerAreaInfo,providerAreaInfoDto);
        providerAreaInfoDto.setOrgansign(organSign);
        ResultVO<Integer> integerResultVO = supplierAreaInfoApi.saveSupplierAreaByCondition(providerAreaInfoDto);
        return new ResponseEntity<>(integerResultVO, HttpStatus.OK);
    }
}
