package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName ProductApplyQueryDetailVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/30 16:39
 * @Version 1.0
 **/
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

@ApiModel(description = "商品提报查询信息类")
public class ProductApplyQueryDetailVo {

    @JsonProperty("pref")
    private String pref;//商品提报单编号

    @JsonProperty("standardLibraryId")
    private Long standardLibraryId;

    @ApiModelProperty(value = "标准库ID")
    public Long getStandardLibraryId() {
        return standardLibraryId;
    }

    public void setStandardLibraryId(Long standardLibraryId) {
        this.standardLibraryId = standardLibraryId;
    }

    @ApiModelProperty(value = "商品提报单编号")
    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }



}
