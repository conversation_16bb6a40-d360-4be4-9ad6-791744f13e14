package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName ProductZbApplyQueryVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/29 13:02
 * @Version 1.0
 **/
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")

@ApiModel(description = "商品提报查询信息类")
public class ProductZbApplyQueryVo {

    @JsonProperty("mixQuery")
    private String mixQuery;//商品信息：支持商品编号、批准文号、条形码、助记码、商品名称、通用名称进行查询，和总部商品查询保持一致
    @JsonProperty("systemType")
    private String systemType;//商品分类
    @JsonProperty("chainStoreOrgansign")
    private String chainStoreOrgansign;//来源门店
    @JsonProperty("manufacturer")
    private String manufacturer;//生产厂家
    @JsonProperty("businessScope")
    private String businessScope;//所属范围
    @JsonProperty("auditState")
    private String auditState;//提报审批状态 1:审批中 2:已通过 3:已驳回，不填：默认全部
    @JsonProperty("createTimeBegin")
    private String createTimeBegin;//开始时间
    @JsonProperty("createTimeEnd")
    private String createTimeEnd;//结束时间

    /**
     * 是否有药品标识码 0 无 1 有 为空全部
     */
    @JsonProperty("isDrugIdentCode")
    private Byte isDrugIdentCode;

    @JsonProperty("isDrugstoreHidden")
    private Byte isDrugstoreHidden;

    @ApiModelProperty(value = "当前页码")
    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    @ApiModelProperty(value = "每页显示多少")
    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    @JsonProperty("page")
    private Integer page;
    @JsonProperty("rows")
    private Integer rows;

    @ApiModelProperty(value = "商品信息：支持商品编号、批准文号、条形码、助记码、商品名称、通用名称进行查询，和总部商品查询保持一致")
    public String getMixQuery() {
        return mixQuery;
    }

    public void setMixQuery(String mixQuery) {
        this.mixQuery = mixQuery;
    }

    @ApiModelProperty(value = "生产厂家")
    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    @ApiModelProperty(value = "提报审批状态 1:审批中 2:已通过 3:已驳回，不填：默认全部")
    public String getAuditState() {
        return auditState;
    }

    public void setAuditState(String auditState) {
        this.auditState = auditState;
    }

    @ApiModelProperty(value = "商品分类")
    public String getSystemType() {
        return systemType;
    }

    public void setSystemType(String systemType) {
        this.systemType = systemType;
    }

    @ApiModelProperty(value = "来源门店")
    public String getChainStoreOrgansign() {
        return chainStoreOrgansign;
    }

    public void setChainStoreOrgansign(String chainStoreOrgansign) {
        this.chainStoreOrgansign = chainStoreOrgansign;
    }

    @ApiModelProperty(value = "所属范围")
    public String getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    @ApiModelProperty(value = "创建开始时间")
    public String getCreateTimeBegin() {
        return createTimeBegin;
    }

    public void setCreateTimeBegin(String createTimeBegin) {
        this.createTimeBegin = createTimeBegin;
    }

    @ApiModelProperty(value = "创建结束时间")
    public String getCreateTimeEnd() {
        return createTimeEnd;
    }

    public void setCreateTimeEnd(String createTimeEnd) {
        this.createTimeEnd = createTimeEnd;
    }

    public Byte getIsDrugIdentCode() {
        return isDrugIdentCode;
    }

    public void setIsDrugIdentCode(Byte isDrugIdentCode) {
        this.isDrugIdentCode = isDrugIdentCode;
    }

    public Byte getIsDrugstoreHidden() {
        return isDrugstoreHidden;
    }

    public void setIsDrugstoreHidden(Byte isDrugstoreHidden) {
        this.isDrugstoreHidden = isDrugstoreHidden;
    }
}
