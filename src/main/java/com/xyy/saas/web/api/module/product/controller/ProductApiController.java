package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.me.product.common.beans.Response;
import com.xyy.me.product.common.beans.ResponseInfo;
import com.xyy.me.product.common.exception.api.ApiException;
import com.xyy.me.product.general.api.dto.DictCategoryGeneralDto;
import com.xyy.me.product.general.api.facade.dictionary.DictionaryFacade;
import com.xyy.me.product.general.api.vo.dictionary.DictCategoryGeneralVo;
import com.xyy.me.product.saas.api.dto.SaasCategoryReqDto;
import com.xyy.me.product.saas.api.dto.SaasCategoryRespDto;
import com.xyy.me.product.saas.api.facade.SaasFacade;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.inventory.cores.api.InventoryLotNumberApi;
import com.xyy.saas.inventory.cores.common.IRemoteBean;
import com.xyy.saas.inventory.cores.dto.repo.InventoryStatisResultDto;
import com.xyy.saas.inventory.cores.enums.IsHiddenEnum;
import com.xyy.saas.product.core.api.ProductMatchApi;
import com.xyy.saas.product.core.api.ProductToolApi;
import com.xyy.saas.product.core.dto.ResultCodeEnum;
import com.xyy.saas.product.core.dto.*;
import com.xyy.saas.purchase.core.dto.ProductBaseinfoPoDto;
import com.xyy.saas.web.api.module.product.model.*;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.dto.result.EmployeeDto;
import com.xyy.util.StringUtil;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.*;
import java.util.stream.Collectors;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T15:22:37.401+08:00")

@Controller
public class ProductApiController implements ProductApi {

    private static final Logger logger = LoggerFactory.getLogger(ProductApiController.class);

    @Reference(version = "0.0.1")
    private com.xyy.saas.product.core.api.ProductApi productApi;

    @Reference(version = "0.0.1")
    private EmployeeApi employeeApi;

    @Reference(version = "0.0.1")
    private com.xyy.saas.inventory.core.api.InventoryApi inventoryApi;

    @Reference(version = "0.0.2")
    private InventoryLotNumberApi inventoryLotNumberApi;

    @Reference(version = "0.0.1")
    private ProductToolApi productToolApi;

    @Reference(version = "1.0.0" ,registry = "middlestage", timeout = 3000,retries = -1)
    private DictionaryFacade dictionaryFacade;

    @Reference(version = "1.0.0" ,registry = "middlestage", timeout = 3000,retries = -1)
    private SaasFacade saasFacade;

    @Reference(version = "0.0.1")
    private ProductMatchApi productMatchApi;

    @Override
    public ResponseEntity<ResultVO> addProduct(HttpServletRequest request, @ApiParam(value = "商品信息" ,required=true )  @Valid @RequestBody SaasProductBaseInfo saasProductBaseInfo) {
        logger.info("调用新增商品接口：传入参数为===>>>"+saasProductBaseInfo.toString());
        // 校验数据是否有必填项未填
        if (StringUtils.isEmpty(saasProductBaseInfo.getProductName()) || StringUtils.isEmpty(saasProductBaseInfo.getAttributeSpecification())
                || StringUtils.isEmpty(saasProductBaseInfo.getUnitId()) || StringUtils.isEmpty(saasProductBaseInfo.getDosageFormId())
                || StringUtils.isEmpty(saasProductBaseInfo.getApprovalNumber()) || StringUtils.isEmpty(saasProductBaseInfo.getManufacturer())
                || StringUtils.isEmpty(saasProductBaseInfo.getContainingHempYn()) || StringUtils.isEmpty(saasProductBaseInfo.getIncomeTaxRate())
                || StringUtils.isEmpty(saasProductBaseInfo.getOuputTaxRate()) || StringUtils.isEmpty(saasProductBaseInfo.getRetailPrice())){

            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.PRODUCT_REQUIRED_PARAMS_NULL), HttpStatus.SERVICE_UNAVAILABLE);
        }
        ProductDto productDto = new ProductDto();
        BeanUtils.copyProperties(saasProductBaseInfo, productDto);
        String organSign = request.getHeader("organSign");
        String username = request.getHeader("userName");
        productDto.setOrganSign(organSign);
        productDto.setCreateUser(username);

        Integer userid = Integer.valueOf(request.getHeader("userId"));
        int status = productApi.addOrUpdate(productDto, userid);

        if (status > 0){
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(status), HttpStatus.OK);
        }else if (status == -1){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.PRODUCT_STANDARD_LIBRARY_ID_EXIST), HttpStatus.SERVICE_UNAVAILABLE);
        }else {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR), HttpStatus.SERVICE_UNAVAILABLE);
        }

    }

    @Override
    public ResponseEntity<ResultVO> updateProduct(HttpServletRequest request, @ApiParam(value = "商品信息" ,required=true )  @Valid @RequestBody SaasProductBaseInfo saasProductBaseInfo) {
        logger.info("调用修改商品接口：传入参数为===>>>"+saasProductBaseInfo.toString());
        // 校验数据是否有必填项未填
        if (StringUtils.isEmpty(saasProductBaseInfo.getProductName()) || StringUtils.isEmpty(saasProductBaseInfo.getAttributeSpecification())
                || StringUtils.isEmpty(saasProductBaseInfo.getUnitId()) || StringUtils.isEmpty(saasProductBaseInfo.getDosageFormId())
                || StringUtils.isEmpty(saasProductBaseInfo.getApprovalNumber()) || StringUtils.isEmpty(saasProductBaseInfo.getManufacturer())
                || StringUtils.isEmpty(saasProductBaseInfo.getContainingHempYn()) || StringUtils.isEmpty(saasProductBaseInfo.getIncomeTaxRate())
                || StringUtils.isEmpty(saasProductBaseInfo.getOuputTaxRate()) || StringUtils.isEmpty(saasProductBaseInfo.getRetailPrice())){

            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.PRODUCT_REQUIRED_PARAMS_NULL), HttpStatus.SERVICE_UNAVAILABLE);
        }

        ProductDto productDto = new ProductDto();
        BeanUtils.copyProperties(saasProductBaseInfo, productDto);
        String organSign = request.getHeader("organSign");
        String username = request.getHeader("userName");
        productDto.setOrganSign(organSign);
        productDto.setCreateUser(username);

        Integer userid = Integer.valueOf(request.getHeader("userId"));
        int status = productApi.addOrUpdate(productDto, userid);
        if (status > 0){
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(status), HttpStatus.OK);
        }else {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR), HttpStatus.SERVICE_UNAVAILABLE);
        }
    }

    @Override
    public ResponseEntity<ResultVO> batchAddProduct(HttpServletRequest request, @ApiParam(value = "商品信息" ,required=true )  @Valid @RequestBody Products products) {
        List<SaasProductBaseInfo> saasProductBaseInfos = products.getSaasProductBaseInfos();
        if (null != saasProductBaseInfos && saasProductBaseInfos.size() > 0){
            String organSign = request.getHeader("organSign");
            String username = request.getHeader("userName");

            List<ProductDto> dtos = new ArrayList<>();
            for (SaasProductBaseInfo info : saasProductBaseInfos) {
                ProductDto dto = new ProductDto();
                BeanUtils.copyProperties(info, dto);

                dto.setOrganSign(organSign);
                dto.setCreateUser(username);
                dtos.add(dto);
            }

            int status = productApi.batchAddProduct(dtos);
            if (status > 0){
                return new ResponseEntity<ResultVO>(ResultVO.createSuccess(status), HttpStatus.OK);
            }else {
                return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR), HttpStatus.SERVICE_UNAVAILABLE);
            }
        }
        return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.PRODUCT_PARAM_NULL), HttpStatus.SERVICE_UNAVAILABLE);
    }

    @Override
    public ResponseEntity<ResultVO> batchUpdateProductLibraryId(HttpServletRequest request, @ApiParam(value = "商品和标准库id的对应关系" ,required=true )  @Valid @RequestBody ProductLibraryRelations relations) {
        List<ProductLibraryRelationPo> relationPos = relations.getRelationPos();
        if (null != relationPos && relationPos.size() > 0){
            List<ProductLibraryRelationDto> dtos = new ArrayList<>();
            for (ProductLibraryRelationPo info : relationPos) {
                ProductLibraryRelationDto dto = new ProductLibraryRelationDto();
                BeanUtils.copyProperties(info, dto);

                dtos.add(dto);
            }

            String organSign = request.getHeader("organSign");
            int status = productApi.batchUpdateProductLibraryId(dtos, organSign);
            if (status > 0){
                return new ResponseEntity<ResultVO>(ResultVO.createSuccess(status), HttpStatus.OK);
            }else {
                return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR), HttpStatus.SERVICE_UNAVAILABLE);
            }
        }
        return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.PRODUCT_PARAM_NULL), HttpStatus.SERVICE_UNAVAILABLE);
    }

    @Override
    public ResponseEntity<ResultVO> productList(HttpServletRequest request, @ApiParam(value = "商品信息", required = true) @RequestBody SaasProductBaseInfo saasProductBaseInfo){
        ProductDto dto = new ProductDto();
        BeanUtils.copyProperties(saasProductBaseInfo, dto);
        String organSign = request.getHeader("organSign");
        dto.setOrganSign(organSign);
        ResultVO result = productApi.productList(dto);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getProductById(HttpServletRequest request, Long id) {
        String organSign = request.getHeader("organSign");
        ProductDto productDto = productApi.getProductByIdAndOrganSign(id, organSign, null);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(productDto), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> updateScatteredPharmacy(HttpServletRequest request, String organSigns) {
        int result = productApi.updateScatteredProductPharmacyPref(organSigns);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(result), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getNearlyEffectiveProductCount(HttpServletRequest request) {
        String eId = request.getHeader("employeeId");
        String organSign = request.getHeader("organSign");
        if (StringUtils.isEmpty(eId) || StringUtils.isEmpty(organSign)){
            logger.error("商品近效期总数接口：未获取到当前登录人的员工ID和药店标识");
        }

        Byte isHidden = null;
        ResultVO<EmployeeDto> resultVO = employeeApi.queryEmployeeById(Integer.valueOf(eId));
        EmployeeDto employee = resultVO.getResult();
        if (null != employee){
            isHidden = employee.getIdentity();
        }

        int count = productApi.getNearlyEffectiveProductCount(organSign, isHidden);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(count), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getAppIndexCount(@RequestHeader("commonRequestModel") String commonRequestModel,@RequestHeader(name = "organSign", required = true)String organSign) {
        ProductCount productCount = new ProductCount();
        try {
            //1.获取员工身份信息
            CommonRequestModel model = JSONObject.parseObject(commonRequestModel, CommonRequestModel.class);
            logger.info("getAppIndexCount headers Info：组织机构:{},header请求信息:{}", organSign, JSONObject.toJSONString(model));
            String identiry = "0";
            if (model != null) {
                Byte aByte = model.getIdentity();
                identiry = aByte == null ? "0" : aByte.toString();
                logger.info("getAppIndexCount identiry：组织机构:{},identiry:{}", organSign, identiry);
            }
            IsHiddenEnum isHiddenEnum = IsHiddenEnum.getEnumByCode(Integer.valueOf(identiry));
            //2.调用库存服务接口
            logger.info("getInventoryStatis Method Begin：组织机构:{},身份类型:{}", organSign, isHiddenEnum.getCode());
            IRemoteBean<InventoryStatisResultDto> inventoryStatis = inventoryLotNumberApi.getInventoryStatis(organSign, isHiddenEnum);
            InventoryStatisResultDto inventoryStatisResult = inventoryStatis.getContent();
            if (inventoryStatis.isSuccess() && inventoryStatisResult != null) {
                productCount.setShortageCount(inventoryStatisResult.getShortageCount());
                productCount.setAdequateCount(inventoryStatisResult.getAdequateCount());
            }
            IRemoteBean<Integer> resultStatis = inventoryLotNumberApi.getNearlyEffectLotNumCount(organSign,isHiddenEnum);
            if(resultStatis.isSuccess()){
                productCount.setNearlyCount(resultStatis.getContent());
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("服务端异常");
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR), HttpStatus.INTERNAL_SERVER_ERROR);
        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(productCount), HttpStatus.OK);
    }

    /*@Override
    public ResponseEntity<ResultVO> getProductByOrganSign(String organSign) {
        productApi.getProductListByOrgansign(organSign);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(null), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> batchUpdatePurchase(String organSign) {
        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> map0 = new HashMap<>();
        map0.put("pref", "ZHL915450");
        map0.put("lastPurchase", "测试0");
        list.add(map0);

        Map<String, Object> map1 = new HashMap<>();
        map1.put("pref", "ZHL915451");
        map1.put("lastPurchase", "测试1");
        list.add(map1);

        Map<String, Object> map2 = new HashMap<>();
        map2.put("pref", "ZHL915452");
        map2.put("lastPurchase", "测试2");
        list.add(map2);

        Map<String, Object> map3 = new HashMap<>();
        map3.put("pref", "ZHL915453");
        map3.put("lastPurchase", "测试3");
        list.add(map3);

        Map<String, Object> map4 = new HashMap<>();
        map4.put("pref", "ZHL915454");
        map4.put("lastPurchase", "测试4");
        list.add(map4);

        Map<String, Object> map5 = new HashMap<>();
        map5.put("pref", "ZHL915455");
        map5.put("lastPurchase", "测试5");
        list.add(map5);

        Map<String, Object> map6 = new HashMap<>();
        map6.put("pref", "ZHL915456");
        map6.put("lastPurchase", "测试6");
        list.add(map6);

        Map<String, Object> map7 = new HashMap<>();
        map7.put("pref", "ZHL915457");
        map7.put("lastPurchase", "测试7");
        list.add(map7);

        Map<String, Object> map8 = new HashMap<>();
        map8.put("pref", "ZHL915458");
        map8.put("lastPurchase", "测试8");
        list.add(map8);

        Map<String, Object> map9 = new HashMap<>();
        map9.put("pref", "ZHL915459");
        map9.put("lastPurchase", "测试9");
        list.add(map9);
        productApi.batchUpdateLastPurchase(list, organSign);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(null), HttpStatus.OK);
    }*/
    /**
     * @desc: 批量更新商品版本号
     * @param commonRequestModel
     * @param pageNum
     * @param pageSize
     * @return: com.xyy.saas.common.util.ResultVO<java.lang.Boolean>
     * @author: hbt
     */
    @Override
    public ResponseEntity<ResultVO> batchUpdateBaseVersion(@NotEmpty int pageNum, @NotEmpty int pageSize, @NotEmpty int endPage) {
        if (pageNum > endPage) {
            return new ResponseEntity<ResultVO>(ResultVO.createError("pageNum > endPage"), HttpStatus.OK);
        }
        List<Map<String, Object>> resList = new ArrayList<>();
        for (int i = pageNum; i <= endPage; i++) {
            logger.info("批量更新商品版本号，开始执行第{}页，启始页：{}，每页条数：{}，截止页：{}", i, pageNum, pageSize, endPage);
            Map<String, Object> updateResult = new HashMap<>();
            Boolean res = false;
            try {
                res = productToolApi.batchUpdateBaseVersion(i, pageSize);
            } catch (Exception e) {
                logger.error("批量更新商品版本号-batchUpdateBaseVersion error", e);
            }
            updateResult.put("page" + i, res);
            resList.add(updateResult);
            logger.info("批量更新商品版本号每页执行结果，启始页:{}，第{}页，结果:{}", pageNum, i, res);
        }
        logger.info("批量更新商品版本号批量结果，启始页：{}，截止页：{}，结果:{}", pageNum, endPage, resList);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(resList), HttpStatus.OK);

    }

    @Override
    public ResponseEntity<ResultVO> sixLevelsDict(@RequestBody DictCategoryGeneralVo dictCategoryGeneralVo) {
        Integer spuCategory = dictCategoryGeneralVo.getSpuCategory();
        if(spuCategory != null){
            if(spuCategory == 5){
                dictCategoryGeneralVo.setSpuCategory(4);
            }else if(spuCategory == 6){
                dictCategoryGeneralVo.setSpuCategory(5);
            }
        }
        dictCategoryGeneralVo.setTraceId(UUID.randomUUID().toString());
        ResponseInfo<List<DictCategoryGeneralDto>> responseInfo = dictionaryFacade.pageQueryCategoryByKeyword(dictCategoryGeneralVo);
        if (responseInfo.getRetCode()!=0){
            logger.error("ProductApiController#sixLevelsDict error dictCategoryGeneralVo:{}, msg:{}", JSONUtils.obj2JSON(dictCategoryGeneralVo), responseInfo.getRetMsg());
            return new ResponseEntity<ResultVO>(HttpStatus.OK);
        }
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(responseInfo.getData()), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> sixLevelsDictIdToName(@RequestBody SaasCategoryReqDto saasCategoryReqDto) throws ApiException {
        List<SaasCategoryReqDto> list = new ArrayList<>();
        logger.info("ProductApiController#sixLevelsDictIdToName begin saasCategoryReqDto:{}", JSONUtils.obj2JSON(saasCategoryReqDto));
        try{
            list.add(saasCategoryReqDto);
            ProductDto productDto = new ProductDto();
            Response<SaasCategoryRespDto> res = saasFacade.getNameBySixCategoryIDStr(list);
            logger.info("ProductApiController#sixLevelsDictIdToName end saasCategoryReqDto:{}", JSONUtils.obj2JSON(saasCategoryReqDto));
            Map<String, String> map = new HashMap<>();
            map.put("dictId", saasCategoryReqDto.getSixCategoryIdStr());
            map.put("dictName", res.getData().get(0).getSixCategoryNameStr().replaceAll(","," > "));
            productDto.getSixLevelList().add(map);
            return new ResponseEntity<ResultVO>(ResultVO.createSuccess(productDto), HttpStatus.OK);
        }catch (Exception e){
            logger.error("ProductApiController#sixLevelsDictIdToName error saasCategoryReqDto:{}", JSONUtils.obj2JSON(saasCategoryReqDto), e);
            return new ResponseEntity<ResultVO>(ResultVO.createError(), HttpStatus.OK);
        }
    }

    @Override
    public ResponseEntity<ResultVO> queryUpload(@RequestBody ProductUploadInfoDto productUploadInfoDto, @RequestHeader(name = "organSign", required = true)String organSign) {
        logger.info("ProductApiController#queryUpload begin productUploadInfoDto:{}, organSign:{}", JSONUtils.obj2JSON(productUploadInfoDto), organSign);
        productUploadInfoDto.setOrganSign(organSign);
        PageInfo<ProductUploadInfoVo> pageInfo =  productApi.queryUpload(productUploadInfoDto);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(pageInfo), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> updatePurchaseStatus(HttpServletRequest request, @RequestBody ProductDto productDto,@RequestHeader(name = "organSign", required = true)String organSign) {
        logger.info("updatePurchaseStatus para :{}",JSONObject.toJSONString(productDto));
        if(productDto.getDisablePurchase() == null){
            return new ResponseEntity<ResultVO>(ResultVO.createError("缺少禁采参数"), HttpStatus.OK);
        }else if( productDto.getDisablePurchase() <0 ||  productDto.getDisablePurchase()>1){
            return new ResponseEntity<ResultVO>(ResultVO.createError("非法禁采参数"), HttpStatus.OK);
        }
        productDto.setOrganSign(organSign);
        ResultVO resultVO  = productApi.updateDisablePurchaseStatus(productDto);
        return new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> matchGate2(@RequestBody MatchProductParam matchProductParam,@RequestHeader(name = "organSign", required = true)String organSign) {
        logger.info("matchGate2 para :{}",JSONObject.toJSONString(matchProductParam));
        ResultVO<List<MatchResultDto>> resultVO = productMatchApi.matchGate2(matchProductParam);
        return  new ResponseEntity<ResultVO>(resultVO, HttpStatus.OK);
    }

    public ResultVO<PageInfo<ProductBaseinfoPoDto>> queryManualMatchingPros(@RequestBody ManualProListQueryVo manualProListQueryVo,
                                                                            @RequestHeader(name = "organSign", required = true)String organSign) {

        Integer rows = manualProListQueryVo.getRows();
        Integer page = manualProListQueryVo.getPage();
        String productName = manualProListQueryVo.getProductName();
        String manufacturer = manualProListQueryVo.getManufacturer();
        String standardLibraryIdYn = manualProListQueryVo.getStandardLibraryIdYn();
        String approvalNumber = manualProListQueryVo.getApprovalNumber();

        rows = rows == null ? 10 : rows;
        page = page == null ? 1 : page;
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(page);
        pageInfo.setPageSize(rows);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("productName", productName);
        map.put("manufacturer", manufacturer);
        map.put("approvalNumber", approvalNumber);
        map.put("organSign", organSign);
        // standardLibraryIdYn 有值代表不过滤标准库ID为空的商品，standardLibraryIdYn无值，代表只查询没有标准库ID的商品
        if (!StringUtil.isEmpty(standardLibraryIdYn))
        {
            map.put("standardLibraryIdYn", 1);
        }
        map.put("status", 1);
        PageInfo list = productApi.queryManualMatchingPros(pageInfo, map);
        List<ProductDto> dtos = list.getList();
        // 获取商品信息
        Set<String> prefs = new HashSet<>();
        dtos.stream().filter(s -> s.getPref() != null).forEach(item -> {
            prefs.add(item.getPref());
        });
        if (prefs.size() > 0) {
            List<ProductDto> products = productApi.getProductsByParams(new ArrayList<>(prefs), null, organSign, null);
            Map<String, ProductDto> pmap = products.stream().collect(Collectors.toMap(ProductDto::getPref, p -> p, (value1, value2) -> value2));
            dtos.forEach(item -> {
                item.setUnitName(null == pmap.get(item.getPref()) ? null : pmap.get(item.getPref()).getUnitName());
                item.setPref(null == pmap.get(item.getPref()) ? null : pmap.get(item.getPref()).getPharmacyPref());
            });
            list.setList(dtos);
        }
        return ResultVO.createSuccess(list);
    }

}
