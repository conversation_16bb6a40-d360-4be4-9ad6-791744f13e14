package com.xyy.saas.web.api.module.external.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * @Classname ProductExternalDto
 * @Description 期初工具实体
 * @Date 2020/5/13 16:06
 * @Created by 吕东杰
 */
@ApiModel(value = "商品信息", description = "商品信息")
public class ProductExternal implements Serializable {

    @ApiModelProperty(value = "商品编号")
    @JsonProperty("pref")
    private String pref; //商品编号

    @ApiModelProperty(value = "通用名")
    @JsonProperty("commonName")
    private String commonName; //通用名

    @ApiModelProperty(value = "标准库ID")
    @JsonProperty("standardLibraryId")
    private Long standardLibraryId; //标准库ID

    @ApiModelProperty(value = "规格/型号")
    @JsonProperty("attributeSpecification")
    private String attributeSpecification; //规格/型号

    @ApiModelProperty(value = "条形码")
    @JsonProperty("barCode")
    private String barCode; //条形码

    @ApiModelProperty(value = "批准文号")
    @JsonProperty("approvalNumber")
    private String approvalNumber; //批准文号

    @ApiModelProperty(value = "生产厂家")
    @JsonProperty("manufacturer")
    private String manufacturer; //生产厂家

    @ApiModelProperty(value = "商品类别")
    @JsonProperty("productType")
    private Integer productType; //商品类别ID(自定义分类)

    @ApiModelProperty(value = "机构编码")
    @JsonProperty("organSign")
    private String organSign;

    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    public Long getStandardLibraryId() {
        return standardLibraryId;
    }

    public void setStandardLibraryId(Long standardLibraryId) {
        this.standardLibraryId = standardLibraryId;
    }

    public String getAttributeSpecification() {
        return attributeSpecification;
    }

    public void setAttributeSpecification(String attributeSpecification) {
        this.attributeSpecification = attributeSpecification;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public Integer getProductType() {
        return productType;
    }

    public void setProductType(Integer productType) {
        this.productType = productType;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    @Override
    public String toString() {
        return "ProductExternalDto{" +
                "pref='" + pref + '\'' +
                ", commonName='" + commonName + '\'' +
                ", standardLibraryId=" + standardLibraryId +
                ", attributeSpecification='" + attributeSpecification + '\'' +
                ", barCode='" + barCode + '\'' +
                ", approvalNumber='" + approvalNumber + '\'' +
                ", manufacturer='" + manufacturer + '\'' +
                ", productType=" + productType +
                ", organSign='" + organSign + '\'' +
                '}';
    }
}
