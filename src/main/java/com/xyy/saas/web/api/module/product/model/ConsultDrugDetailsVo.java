package com.xyy.saas.web.api.module.product.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "咨询记录明细", description = "咨询记录明细")
public class ConsultDrugDetailsVo {

    @ApiModelProperty(
            value = "页码",
            name = "page"
    )
    private Integer page;
    @ApiModelProperty(
            value = "每页数据量",
            name = "rows"
    )
    private Integer rows;

    private String guid;

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }
}
