package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * saas_medical_product
 * <AUTHOR>
public class MedicalProductModel implements Serializable {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 机构编码
     */
    @ApiModelProperty(value = "机构编码")
    @JsonProperty("organSign")
    private String organSign;

    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称")
    @JsonProperty("organSignName")
    private String organSignName;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    @JsonProperty("productPref")
    private String productPref;

    /**
     * 标准库id
     */
    @ApiModelProperty(value = "标准库id")
    @JsonProperty("standardLibraryId")
    private Long standardLibraryId;

    /**
     * 医保地区
     */
    @ApiModelProperty(value = "医保地区")
    @JsonProperty("medicalProvince")
    private String medicalProvince;

    /**
     * 医保匹配的唯一编码
     */
    @ApiModelProperty(value = "医保匹配的唯一编码")
    @JsonProperty("medicalUnique")
    private String medicalUnique;

    /**
     * 医保信息存储es索引名称
     */
    @ApiModelProperty(value = "医保信息存储es索引名称")
    @JsonProperty("medicalEsName")
    private String medicalEsName;

    /**
     * 医保目录商品名称
     */
    @ApiModelProperty(value = "医保目录商品名称")
    @JsonProperty("medicalProductName")
    private String medicalProductName;

    /**
     * 医保信息类型，1、医保药品 2、诊疗项目
     */
    @ApiModelProperty(value = "医保信息类型，1、医保药品 2、诊疗项目")
    @JsonProperty("medicalType")
    private Integer medicalType;

    /**
     * 删除状态；1有效，0删除
     */
    @ApiModelProperty(value = "删除状态；1有效，0删除")
    @JsonProperty("yn")
    private Byte yn;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonProperty("createTime")
    private String createTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @JsonProperty("createUser")
    private String createUser;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonProperty("updateTime")
    private String updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @JsonProperty("updateUser")
    private String updateUser;

    /**
     * 状态，1医保 2非医保
     */
    @ApiModelProperty(value = "状态，1医保 2非医保")
    @JsonProperty("status")
    private Integer status;

    /**
     * 同步状态，1已同步，0未同步
     */
    @ApiModelProperty(value = "同步状态，1已同步，0未同步")
    @JsonProperty("sync")
    private Byte sync;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    @JsonProperty("baseVersion")
    private Long baseVersion;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public String getOrganSignName() {
        return organSignName;
    }

    public void setOrganSignName(String organSignName) {
        this.organSignName = organSignName;
    }

    public String getProductPref() {
        return productPref;
    }

    public void setProductPref(String productPref) {
        this.productPref = productPref;
    }

    public Long getStandardLibraryId() {
        return standardLibraryId;
    }

    public void setStandardLibraryId(Long standardLibraryId) {
        this.standardLibraryId = standardLibraryId;
    }

    public String getMedicalProvince() {
        return medicalProvince;
    }

    public void setMedicalProvince(String medicalProvince) {
        this.medicalProvince = medicalProvince;
    }

    public String getMedicalUnique() {
        return medicalUnique;
    }

    public void setMedicalUnique(String medicalUnique) {
        this.medicalUnique = medicalUnique;
    }

    public String getMedicalEsName() {
        return medicalEsName;
    }

    public void setMedicalEsName(String medicalEsName) {
        this.medicalEsName = medicalEsName;
    }

    public String getMedicalProductName() {
        return medicalProductName;
    }

    public void setMedicalProductName(String medicalProductName) {
        this.medicalProductName = medicalProductName;
    }

    public Integer getMedicalType() {
        return medicalType;
    }

    public void setMedicalType(Integer medicalType) {
        this.medicalType = medicalType;
    }

    public Byte getYn() {
        return yn;
    }

    public void setYn(Byte yn) {
        this.yn = yn;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Byte getSync() {
        return sync;
    }

    public void setSync(Byte sync) {
        this.sync = sync;
    }

    public Long getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(Long baseVersion) {
        this.baseVersion = baseVersion;
    }
}