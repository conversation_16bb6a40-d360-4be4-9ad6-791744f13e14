package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-07T14:34:49.141+08:00")

@ApiModel(description = "商品配伍禁忌查询对象")
public class ProductCompatibilityQueryVo {

    @JsonProperty("guid")
    private String guid;//全局唯一id，删除，编辑时必传

    @JsonProperty("pref")
    private String pref;//商品内码编号，删除，编辑时必传

    @JsonProperty("mixQueyStr")
    private String mixQueyStr;//通用名称

    @JsonProperty("productType")
    private Integer productType;//商品类型

    @JsonProperty("systemType")
    private Integer systemType;//商品类型

    @JsonProperty("startTimeStr")
    private String startTimeStr;//开始时间

    @JsonProperty("endTimeStr")
    private String endTimeStr;//结束时间

    @JsonProperty("page")
    private Integer page;//当前页码

    private Integer rows;//每页显示数量，前端传值参数

    @JsonProperty("pageSize")
    private Integer pageSize;//每页显示数量

    @JsonProperty("pwPrefListStr")
    private String pwPrefListStr;//当前商品配伍商品列表，格式(ZHL13998,ZHL13997,ZHL13996)，在选择当前商品的配伍商品查询时必须传

    @JsonProperty("createUser")
    private String createUser;//创建人,需要传用户的id

    @JsonProperty("pharmacyPref")
    private String pharmacyPref;//商品外码，页面查询的时候需要传此参数

    @ApiModelProperty(value = "批准文号，查询字段，非必传字段")
    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    @JsonProperty("approvalNumber")
    private String approvalNumber;//批准文号

    @ApiModelProperty(value = "商品名称，查询字段")
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    @JsonProperty("productName")
    private String productName;//商品名称

    @ApiModelProperty(value = "商品编号（商品外码），查询时需要传递")
    public String getPharmacyPref() {
        return pharmacyPref;
    }

    public void setPharmacyPref(String pharmacyPref) {
        this.pharmacyPref = pharmacyPref;
    }

    @ApiModelProperty(value = "创建人,需要传用户id")
    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    @ApiModelProperty(value = "当前页码")
    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    @ApiModelProperty(value = "每页显示数量")
    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @ApiModelProperty(value = "通用名称")
    public String getMixQueyStr() {
        return mixQueyStr;
    }

    public void setMixQueyStr(String mixQueyStr) {
        this.mixQueyStr = mixQueyStr;
    }

    @ApiModelProperty(value = "商品功能分类")
    public Integer getProductType() {
        return productType;
    }

    public void setProductType(Integer productType) {
        this.productType = productType;
    }

    @ApiModelProperty(value = "开始时间")
    public String getStartTimeStr() {
        return startTimeStr;
    }

    public void setStartTimeStr(String startTimeStr) {
        this.startTimeStr = startTimeStr;
    }

    @ApiModelProperty(value = "结束时间")
    public String getEndTimeStr() {
        return endTimeStr;
    }

    public void setEndTimeStr(String endTimeStr) {
        this.endTimeStr = endTimeStr;
    }

    @ApiModelProperty(value = "（全局唯一id）删除，编辑时必传")
    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    @ApiModelProperty(value = "当前商品配伍商品编码列表，格式“ZHL13998,ZHL13997,ZHL13996”，在选择当前商品的配伍商品查询时必须传")
    public String getPwPrefListStr() {
        return pwPrefListStr;
    }

    public void setPwPrefListStr(String pwPrefListStr) {
        this.pwPrefListStr = pwPrefListStr;
    }

    @ApiModelProperty(value = "商品内码编号，删除，编辑时，给主商品配伍选择商品时必传")
    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    @ApiModelProperty(value = "商品系统分类")
    public Integer getSystemType() {
        return systemType;
    }

    public void setSystemType(Integer systemType) {
        this.systemType = systemType;
    }

    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }
}
