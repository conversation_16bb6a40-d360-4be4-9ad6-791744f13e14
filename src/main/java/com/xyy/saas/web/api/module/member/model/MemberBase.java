package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 会员基本信息
 * <AUTHOR>
 */
@ApiModel(description = "会员基本信息")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-06T17:16:06.377+08:00")
public class MemberBase {
    private Long id = null;

    private String cartNo = null;

    private String mixedQuery = null;

    private String name = null;

    private Integer sex = null;

    private Integer age = null;

    private Long vipLevelId = null;

    private String telephone = null;

    private String idCard = null;

    private String address = null;

    private String wechat = null;

    private String qq = null;

    private BigDecimal point = null;

    private BigDecimal allPoint = null;

    private Integer baseVersion = null;

    private String organsign = null;

    private Date expriedTime = null;

    private String createUser = null;

    private Date createTime = null;

    private String updateUser = null;

    private Date updateTime = null;

    private Integer yn = null;

    private Integer start;

    private Integer pageSize;

    private Integer sortWay;

    /**
     * 生日
     */
    private Date birthday;

    /**
     * 生效时间
     */
    private Date effectTime;


    /**
     * 发卡时间
     */
    private Date sendCardTime;


    /**
     * 发卡状态 0 启用 1停用
     */
    private Integer state;

    /**
     * 密码
     */
    private String passwd;


    /**
     * guid
     */
    private String guid;


    /**
     * 备注
     */
    private String remark;

    /**
     * 白名单
     */
    private Integer whiteList;

    /**
     * 储值卡实充余额
     */
    @ApiModelProperty(value = "储值卡实充余额", example = "50.00")
    private BigDecimal amount;

    /**
     * 储值卡赠送余额
     */
    @ApiModelProperty(value = "储值卡赠送余额", example = "5.00")
    private BigDecimal bonus;

    /**
     * 支付类型 1-现金 2-微信 3-支付宝 4-银联 9-其他
     * @see com.xyy.saas.member.core.enums.PayTypeEnum
     */
    @ApiModelProperty(value = "支付类型 1-现金 2-微信 3-支付宝 4-银联 9-其他", example = "1")
    private Byte payType;

    /**
     * 审核人
     */
    @ApiModelProperty(value = "审核人", example = "12")
    private String checkUser;

    /**
     * 总部机构号
     */
    @ApiModelProperty(value = "总部机构号", example = "ZHL00002558")
    private String headquartersOrganSign;

    /**
     * 省
     */
    @ApiModelProperty(value = "省", example = "北京")
    private String provinceName;

    /**
     * 省编码
     */
    @ApiModelProperty(value = "省编码", example = "123000")
    private Integer provinceCode;

    /**
     * 市
     */
    @ApiModelProperty(value = "市", example = "北京市")
    private String cityName;

    /**
     * 市编码
     */
    @ApiModelProperty(value = "市编码", example = "北京市")
    private Integer cityCode;

    /**
     * 区
     */
    @ApiModelProperty(value = "区", example = "朝阳区")
    private String areaName;

    /**
     * 区编码
     */
    @ApiModelProperty(value = "区编码", example = "朝阳区")
    private Integer areaCode;

    /**
     * 会员来源
     */
    @ApiModelProperty(value = "会员来源", example = "管理端，pos")
    private Byte source;

    /**
     * 慢病集合 chronicPrefList：[{pref:'12',chronicName:'12'},{pref:'22',chronicName:'22'}]
     */
    private List<MemberChronicConfigVo> chronicPrefList;
    /**
     * 慢病名称(逗号分隔)
     */
    private String chronicName;


    public MemberBase id(Long id) {
        this.id = id;
        return this;
    }

    /**
     * 会员id
     *
     * @return id
     **/
    @ApiModelProperty(value = "会员id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public MemberBase cartNo(String cartNo) {
        this.cartNo = cartNo;
        return this;
    }

    /**
     * 会员卡号
     *
     * @return cartNo
     **/
    @ApiModelProperty(value = "会员卡号 (必传)")
    public String getCartNo() {
        return cartNo;
    }

    public void setCartNo(String cartNo) {
        this.cartNo = cartNo;
    }

    public MemberBase mixedQuery(String mixedQuery) {
        this.mixedQuery = mixedQuery;
        return this;
    }

    /**
     * 混合查询
     *
     * @return mixedQuery
     **/
    @ApiModelProperty(value = "混合查询 (非必传)")
    public String getMixedQuery() {
        return mixedQuery;
    }

    public void setMixedQuery(String mixedQuery) {
        this.mixedQuery = mixedQuery;
    }

    public MemberBase name(String name) {
        this.name = name;
        return this;
    }

    /**
     * 姓名
     *
     * @return name
     **/
    @ApiModelProperty(value = "姓名 (必传）")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public MemberBase sex(Integer sex) {
        this.sex = sex;
        return this;
    }

    /**
     * 性别
     *
     * @return sex
     **/
    @ApiModelProperty(value = "性别   必传 1 男 2 女 ")
    public Integer getSex() {
        return sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public MemberBase age(Integer age) {
        this.age = age;
        return this;
    }

    /**
     * 年龄
     *
     * @return age
     **/
    @ApiModelProperty(value = "年龄  非必传")
    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public MemberBase vipLevelId(Long vipLevelId) {
        this.vipLevelId = vipLevelId;
        return this;
    }

    /**
     * 级别Id
     *
     * @return vipLevelId
     **/
    @ApiModelProperty(value = "级别Id  必传")
    public Long getVipLevelId() {
        return vipLevelId;
    }

    public void setVipLevelId(Long vipLevelId) {
        this.vipLevelId = vipLevelId;
    }

    public MemberBase telephone(String telephone) {
        this.telephone = telephone;
        return this;
    }

    /**
     * 电话号
     *
     * @return telephone
     **/
    @ApiModelProperty(value = "电话号  必传")
    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public MemberBase idCard(String idCard) {
        this.idCard = idCard;
        return this;
    }

    /**
     * 身份证号
     *
     * @return idCard
     **/
    @ApiModelProperty(value = "身份证号 (非必传)")
    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public MemberBase address(String address) {
        this.address = address;
        return this;
    }

    /**
     * 地址
     *
     * @return address
     **/
    @ApiModelProperty(value = "地址 (非必传)")
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public MemberBase wechat(String wechat) {
        this.wechat = wechat;
        return this;
    }

    /**
     * 微信号
     *
     * @return wechat
     **/
    @ApiModelProperty(value = "微信号  (非必传)")
    public String getWechat() {
        return wechat;
    }

    public void setWechat(String wechat) {
        this.wechat = wechat;
    }

    public MemberBase qq(String qq) {
        this.qq = qq;
        return this;
    }

    /**
     * QQ号码
     *
     * @return qq
     **/
    @ApiModelProperty(value = "QQ号码 (非必传)")
    public String getQq() {
        return qq;
    }

    public void setQq(String qq) {
        this.qq = qq;
    }

    public MemberBase point(BigDecimal point) {
        this.point = point;
        return this;
    }

    /**
     * 可用积分
     *
     * @return point
     **/
    @ApiModelProperty(value = "可用积分 (非必传)")
    public BigDecimal getPoint() {
        return point;
    }

    public void setPoint(BigDecimal point) {
        this.point = point;
    }

    public MemberBase allPoint(BigDecimal allPoint) {
        this.allPoint = allPoint;
        return this;
    }

    /**
     * 累计积分
     *
     * @return allPoint
     **/
    @ApiModelProperty(value = "累计积分(非必传)")
    public BigDecimal getAllPoint() {
        return allPoint;
    }

    public void setAllPoint(BigDecimal allPoint) {
        this.allPoint = allPoint;
    }

    public MemberBase baseVersion(Integer baseVersion) {
        this.baseVersion = baseVersion;
        return this;
    }

    /**
     * 版本号
     *
     * @return baseVersion
     **/
    @ApiModelProperty(value = "版本号  非必传(同步必传)")
    public Integer getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(Integer baseVersion) {
        this.baseVersion = baseVersion;
    }

    public MemberBase organsign(String organsign) {
        this.organsign = organsign;
        return this;
    }

    /**
     * 机构标识
     *
     * @return organsign
     **/
    @ApiModelProperty(value = "机构标识")


    public String getOrgansign() {
        return organsign;
    }

    public void setOrgansign(String organsign) {
        this.organsign = organsign;
    }

    public MemberBase expriedTime(Date expriedTime) {
        this.expriedTime = expriedTime;
        return this;
    }

    /**
     * 过期时间
     *
     * @return expriedTime
     **/
    @ApiModelProperty(value = "过期时间 非必传")

    public Date getExpriedTime() {
        return expriedTime;
    }

    public void setExpriedTime(Date expriedTime) {
        this.expriedTime = expriedTime;
    }

    public MemberBase createUser(String createUser) {
        this.createUser = createUser;
        return this;
    }

    /**
     * 创建人
     * @return createUser
     **/
    @ApiModelProperty(value = "创建人  非必传")
    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public MemberBase createTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    /**
     * 创建时间
     *
     * @return createTime
     **/
    @ApiModelProperty(value = "创建时间 非必传")
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public MemberBase updateUser(String updateUser) {
        this.updateUser = updateUser;
        return this;
    }

    /**
     * 更新人
     *
     * @return updateUser
     **/
    @ApiModelProperty(value = "更新人 非必传")
    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public MemberBase updateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    /**
     * 更新时间
     *
     * @return updateTime
     **/
    @ApiModelProperty(value = "更新时间  非必传")
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public MemberBase yn(Integer yn) {
        this.yn = yn;
        return this;
    }

    /**
     * 是否可用
     *
     * @return yn
     **/
    @ApiModelProperty(value = "是否有效  （必传 1 有效 0 删除）")
    public Integer getYn() {
        return yn;
    }

    public void setYn(Integer yn) {
        this.yn = yn;
    }


    /**
     * 分页开始的页码
     * @return
     */
    @ApiModelProperty(value = "分页开始的页码")
    public Integer getStart() {
        return start;
    }

    public void setStart(Integer start) {
        this.start = start;
    }

    /**
     * 分页大小
     * @return
     */
    @ApiModelProperty(value = "分页大小")
    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 排序 1:asc 2:desc
     * @return
     */
    @ApiModelProperty(value = "排序 1:asc 2:desc")
    public Integer getSortWay() {
        return sortWay;
    }

    public void setSortWay(Integer sortWay) {
        this.sortWay = sortWay;
    }

    @ApiModelProperty(value = "生日")
    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    @ApiModelProperty(value = "生效日期")
    public Date getEffectTime() {
        return effectTime;
    }

    public void setEffectTime(Date effectTime) {
        this.effectTime = effectTime;
    }

    @ApiModelProperty(value = "发卡时间")
    public Date getSendCardTime() {
        return sendCardTime;
    }

    public void setSendCardTime(Date sendCardTime) {
        this.sendCardTime = sendCardTime;
    }

    @ApiModelProperty(value = "发卡状态 0 启用  1 停用")
    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    @ApiModelProperty(value = "密码")
    public String getPasswd() {
        return passwd;
    }

    public void setPasswd(String passwd) {
        this.passwd = passwd;
    }

    @ApiModelProperty(value = "guid")
    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    @ApiModelProperty(value = "备注")
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @ApiModelProperty(value = "白名单 默认0  0：否 1：是")
    public Integer getWhiteList() {
        return whiteList;
    }

    public void setWhiteList(Integer whiteList) {
        this.whiteList = whiteList;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getBonus() {
        return bonus;
    }

    public void setBonus(BigDecimal bonus) {
        this.bonus = bonus;
    }

    public Byte getPayType() {
        return payType;
    }

    public void setPayType(Byte payType) {
        this.payType = payType;
    }

    public String getCheckUser() {
        return checkUser;
    }

    public void setCheckUser(String checkUser) {
        this.checkUser = checkUser;
    }

    public String getHeadquartersOrganSign() {
        return headquartersOrganSign;
    }

    public void setHeadquartersOrganSign(String headquartersOrganSign) {
        this.headquartersOrganSign = headquartersOrganSign;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public Integer getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(Integer provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Integer getCityCode() {
        return cityCode;
    }

    public void setCityCode(Integer cityCode) {
        this.cityCode = cityCode;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public Integer getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(Integer areaCode) {
        this.areaCode = areaCode;
    }

    public Byte getSource() {
        return source;
    }

    public void setSource(Byte source) {
        this.source = source;
    }

    public List<MemberChronicConfigVo> getChronicPrefList() {
        return chronicPrefList;
    }

    public void setChronicPrefList(List<MemberChronicConfigVo> chronicPrefList) {
        this.chronicPrefList = chronicPrefList;
    }

    public String getChronicName() {
        return chronicName;
    }

    public void setChronicName(String chronicName) {
        this.chronicName = chronicName;
    }
}

