package com.xyy.saas.web.api.module.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import redis.clients.jedis.*;

import java.util.*;

/**
 * redis工具类 后面需要重构
 */
@Component
public class JedisUtils {

	private static final Logger logger = LoggerFactory.getLogger(JedisUtils.class);

	private static final ObjectMapper objectMapper = new ObjectMapper();

	private static final Long RELEASE_SUCCESS = 1L;

	@Autowired
	private JedisPool jedisPool;

//	@Autowired
//	private GuavaCache guavaCache;

	/**
	 * 关闭 Redis
	 */
	public void destory() {
		jedisPool.destroy();
	}

	/**
	 * 获取 master jedis
	 * 
	 * @return
	 */
	public Jedis getResource() {
		return jedisPool.getResource();
	}

	/**
	 * 回收 jedis
	 * 
	 * @param jedis
	 */
	public void returnResource(Jedis jedis) {
		if (jedis == null) {
			return;
		}
		try {
			jedis.close();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}

	}

	/**
	 * 销毁 jedis
	 * 
	 * @param jedis
	 */
	public void returnBrokenResource(Jedis jedis) {
		if (jedis == null) {
			return;
		}
		try {
			jedisPool.close();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}

	}

    /**
     * 保存对象到redis
     *
     * @param key
     * @param jsonStr
     * @return
     */
    public String set(String key, String jsonStr) {
        Jedis jedis = null;
        try {
            jedis=getResource();
            return jedis.set(key, jsonStr);
        } catch (Exception e) {
			logger.error("调用redis异常", e);
        }finally {
        	returnResource(jedis);
		}
        return null;
    }

	/**
	 * 保存对象到redis
	 *
	 * @param key
	 * @param bytes
	 * @return
	 */
	public String set(byte[] key, byte[] bytes, int cacheSeconds) {
		Jedis jedis = null;
		try {
			jedis=getResource();
			String set = jedis.set(key, bytes);
			if (cacheSeconds != 0) {
				jedis.expire(key, cacheSeconds);
			}
			return set;
		} catch (Exception e) {
			logger.error("调用redis异常", e);
		}finally {
			returnResource(jedis);
		}
		return null;
	}

    /**
     * 从redis获取对象
     *
     * @param key
     * @param clazz 获取对象的类型
     * @return
     */
    public <T> T get(String key, Class<T> clazz) {
    	Jedis jedis=null;
		try {
			jedis = getResource();
			String jsonStr = jedis.get(key);
			return objectMapper.readValue(jsonStr, clazz);
		} catch (Exception e) {
			logger.error("调用redis异常", e);
        }finally {
			returnResource(jedis);
		}
        return null;
    }
    
    /**
     * 在字段上对应的值进行整数的增量计算，如果field的value不是整数报错
     *
     * @param key
     * @param field
     * @param increment
     * @return
     */
    public Integer hincrby (String key,String field,long increment) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			Long result = jedis.hincrBy(key, field, increment);
			return result == null ? 0 : result.intValue();
		} catch (Exception e) {
			logger.error("调用redis异常", e);
		} finally {
			returnResource(jedis);
		}
		return null;
    }
    
    /**
     * 在hash类型中保存对象
     * @param key
     * @param map
     * @return
     */
    public String hmset(final String key, Map<String, String> map) {
        Jedis jedis = null;
        try {
        	jedis=getResource();
			return jedis.hmset(key, map);
        } catch (Exception e) {
			logger.error("调用redis异常", e);
        }finally {
			returnResource(jedis);
		}
        return null;
    }

	/**
	 * 获取hash所有的值
	 *
	 * @param key
	 * @return
	 */
	public List<String> hmget(String key, String... fields) {
		Jedis jedis = getResource();
		try {
			List<String> hmgetValues = jedis.hmget(key, fields);
			return hmgetValues;
		} catch (Exception e) {
			logger.error(String.format("hmget key = %s,fields:%s ", key,fields), e);
		} finally {
			returnResource(jedis);
		}
		return null;
	}

	public String setex(String key,long seconds,String value) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			return jedis.setex(key, (int)seconds, value);
		} catch (Exception e) {
			logger.error("调用redis异常", e);
		} finally {
			returnResource(jedis);
		}
		return "";
	}
	
	public Long del(String... keys) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			Long del = jedis.del(keys);
			return del;
		} catch (Exception e) {
			logger.error("调用redis异常", e);
		} finally {
			returnResource(jedis);
		}
		return null;
	}
	
	public List<String> get(String... keys) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			List<String> list = jedis.mget(keys);
			return list;
		} catch (Exception e) {
			logger.error("调用redis异常", e);
		} finally {
			returnResource(jedis);
		}
		return Collections.emptyList();
	}

	/**
	 * 保存对象到redis
	 *
	 * @param key
	 * @return
	 */
	public <T> String setTList(String key,  List<T> list) {
		Jedis jedis = null;
		try {
			jedis=getResource();
			return jedis.set(key.getBytes(), SerializeUtil.serializeList(list));
		} catch (Exception e) {
			logger.error("调用redis异常", e);
		}finally {
			returnResource(jedis);
		}
		return null;
	}

	/**
	 * 从redis获取对象
	 *
	 * @param key
	 * @return
	 */
	public <T> List<T> getTList(String key) {
		Jedis jedis=null;
		try {
			jedis = getResource();
            List<T> list = SerializeUtil.unserializeList(jedis.get(key.getBytes()));
			return list;
		} catch (Exception e) {
			logger.error("调用redis异常", e);
		}finally {
			returnResource(jedis);
		}
		return null;
	}

	/**
	 * 获取byte[]类型Key
	 *
	 * @return
	 */
	public static byte[] getBytesKey(Object object) {
		if (object instanceof String) {
			return SerializeUtil.getBytes((String) object);
		} else {
			return SerializeUtil.serialize(object);
		}
	}

	/**
	 * Object转换byte[]类型
	 *
	 * @return
	 */
	public static byte[] toBytes(Object object) {
		return SerializeUtil.serialize(object);
	}



	/**
	 * 获取缓存
	 *
	 * @param pattern 规则
	 * @return 值
	 */
	public Set<String> keys(String pattern) {
		Set<String> value = Sets.newHashSet();
		Jedis jedis = null;
		try {
			jedis = getResource();
			Set<String> keys = jedis.keys(pattern);
			if (Objects.nonNull(keys)) {
				value = keys;
			}
		} catch (Exception e) {
			logger.warn("keys {} = {}", pattern, value, e);
		} finally {
			returnResource(jedis);
		}
		return value;
	}

	/**
	 * 获取缓存 非阻塞scan
	 *
	 * @param pattern 规则
	 * @return 值
	 */
//	public List<String> scan(String pattern) {
//		String cursor = "0";
//		List<String> value = new ArrayList<>();
//		Jedis jedis = null;
//		try {
//			jedis = getResource();
//			ScanParams scanParams = new ScanParams();
//			scanParams.match(pattern);
//			while (true) {
//				ScanResult<String> scan = jedis.scan(cursor, scanParams);
//				value.addAll(scan.getResult());
//				cursor = scan.getStringCursor();
//				if ("0".equals(cursor)) {
//					return value;
//				}
//			}
//		} catch (Exception e) {
//			logger.warn("keys {} = {} e = {}", pattern, value, e);
//		} finally {
//			returnResource(jedis);
//		}
//		return value;
//	}

	public List<String> scan(String pattern) {
		String cursor = "0";
		List<String> value = new ArrayList<>();
		Jedis jedis = null;
		try {
			jedis = getResource();
			ScanParams scanParams = new ScanParams();
			scanParams.match(pattern);
			while (true) {
				ScanResult<String> scan = jedis.scan(cursor, scanParams);
				value.addAll(scan.getResult());
				cursor = scan.getStringCursor();
				if ("0".equals(cursor)) {
					return value;
				}
			}
		} catch (Exception e) {
			logger.warn("keys {} = {} e = {}", pattern, value, e);
		} finally {
			returnResource(jedis);
		}
		return value;
	}

	/**
	 * 获取缓存 非阻塞scan
	 * 无序集合(set)
	 *
	 * @return 值
	 */
	public List<String> sscan(String key) {
		String cursor = "0";
		List<String> value = new ArrayList<>();
		Jedis jedis = null;
		try {
			jedis = getResource();
			while (true) {
				ScanResult<String> scan = jedis.sscan(key, cursor);
				value.addAll(scan.getResult());
				cursor = scan.getStringCursor();
				if ("0".equals(cursor)) {
					return value;
				}
			}
		} catch (Exception e) {
			logger.warn("keys {} e = {}", value, e);
		} finally {
			returnResource(jedis);
		}
		return value;
	}

	/**
	 * 获取缓存
	 *
	 * @param key 键
	 * @return 值
	 */
	public String get(String key) {
		String value = null;
		Jedis jedis = null;
		try {
			jedis = getResource();
			if (jedis.exists(key)) {
				value = jedis.get(key);
				value = !StringUtils.isEmpty(value) && !"nil".equalsIgnoreCase(value) ? value : null;
			}
		} catch (Exception e) {
			logger.warn("get {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return value;
	}

	/**
	 * 为对应的key的value值 -1
	 *
	 * @param key 键
	 */
	public long decr(String key) {
		long value = -1;
		Jedis jedis = null;
		try {
			jedis = getResource();
			if (jedis.exists(key)) {
				value = jedis.decr(key);
			}
		} catch (Exception e) {
			logger.warn("get {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return value;
	}

	/**
	 * 为对应的key的value值 +1
	 *
	 * @param key 键
	 */
	public long incr(String key) {
		long value = -1;
		Jedis jedis = null;
		try {
			jedis = getResource();
			value = jedis.incr(key);
		} catch (Exception e) {
			logger.warn("get {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return value;
	}

	/**
	 * 获取缓存
	 *
	 * @param key 键
	 * @return 值
	 */
	public Object getObject(String key) {
		Object value = null;
		Jedis jedis = null;
		try {
			jedis = getResource();
			if (jedis.exists(getBytesKey(key))) {
				value = SerializeUtil.unserialize((jedis.get(getBytesKey(key))));
			}
		} catch (Exception e) {
			logger.warn("getObject {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return value;
	}

	/**
	 * 设置缓存
	 *
	 * @param key          键
	 * @param value        值
	 * @param cacheSeconds 超时时间，0为不超时
	 */
	public String set(String key, String value, int cacheSeconds) {
		String result = null;
		Jedis jedis = null;
		try {
			jedis = getResource();
			result = jedis.set(key, value);
			if (cacheSeconds != 0) {
				jedis.expire(key, cacheSeconds);
			}
		} catch (Exception e) {
			logger.warn("set {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 设置缓存
	 *
	 * @param key          键
	 * @param cacheSeconds 超时时间，0为不超时
	 */
	public String set(String key, int cacheSeconds) {
		String result = null;
		Jedis jedis = null;
		try {
			jedis = getResource();
			if (cacheSeconds != 0) {
				jedis.expire(key, cacheSeconds);
			}
		} catch (Exception e) {
			logger.warn("set {} = {}", key, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 设置缓存 -- 在cacheSeconds时间点过期
	 *
	 * @param key          键
	 * @param value        值
	 * @param cacheSeconds 超时时间戳，0为不超时
	 */
	public String set(String key, String value, long cacheSeconds) {
		String result = null;
		Jedis jedis = null;
		try {
			jedis = getResource();
			result = jedis.set(key, value);
			if (cacheSeconds != 0) {
				jedis.expireAt(key, cacheSeconds);
			}
		} catch (Exception e) {
			logger.warn("set {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 设置缓存
	 *
	 * @param key          键
	 * @param value        值
	 * @param cacheSeconds 超时时间，0为不超时
	 */
	public String setObject(String key, Object value, int cacheSeconds) {
		String result = null;
		Jedis jedis = null;
		try {
			jedis = getResource();
			result = jedis.set(getBytesKey(key), toBytes(value));
			if (cacheSeconds != 0) {
				jedis.expire(key, cacheSeconds);
			}
		} catch (Exception e) {
			logger.warn("setObject {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 获取List缓存
	 *
	 * @param key 键
	 * @return 值
	 */
	public List<String> getList(String key) {
		List<String> value = null;
		Jedis jedis = null;
		try {
			jedis = getResource();
			if (jedis.exists(key)) {
				value = jedis.lrange(key, 0, -1);
			}
		} catch (Exception e) {
			logger.warn("getList {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return value;
	}

	/**
	 * 获取List缓存
	 *
	 * @param key 键
	 * @return 值
	 */
	public List<Object> getObjectList(String key) {
		List<Object> value = null;
		Jedis jedis = null;
		try {
			jedis = getResource();
			if (jedis.exists(getBytesKey(key))) {
				List<byte[]> list = jedis.lrange(getBytesKey(key), 0, -1);
				value = Lists.newArrayList();
				for (byte[] bs : list) {
					value.add(SerializeUtil.unserialize((bs)));
				}
			}
		} catch (Exception e) {
			logger.warn("getObjectList {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return value;
	}

	/**
	 * 获取List缓存
	 *
	 * @param key   键
	 * @param start 开始索引
	 * @param end   结束索引
	 */
	public List<Object> getObjectList(String key, long start, long end) {
		List<Object> value = null;
		Jedis jedis = null;
		try {
			jedis = getResource();
			if (jedis.exists(getBytesKey(key))) {
				List<byte[]> list = jedis.lrange(getBytesKey(key), start, end);
				value = Lists.newArrayList();
				for (byte[] bs : list) {
					value.add(SerializeUtil.unserialize(bs));
				}
			}
		} catch (Exception e) {
			logger.warn("getObjectList {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return value;
	}

	/**
	 * 设置List缓存
	 *
	 * @param key          键
	 * @param value        值
	 * @param cacheSeconds 超时时间，0为不超时
	 */
	public long setList(String key, List<String> value, int cacheSeconds) {
		long result = 0;
		Jedis jedis = null;
		try {
			jedis = getResource();
			if (jedis.exists(key)) {
				jedis.del(key);
			}
			result = jedis.rpush(key, value.toArray(new String[value.size()]));
			if (cacheSeconds != 0) {
				jedis.expire(key, cacheSeconds);
			}
		} catch (Exception e) {
			logger.warn("setList {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 设置List缓存
	 *
	 * @param key          键
	 * @param value        值
	 * @param cacheSeconds 超时时间，0为不超时
	 */
	public long setObjectList(String key, List<Object> value, int cacheSeconds) {
		long result = 0;
		Jedis jedis = null;
		try {
			jedis = getResource();
			if (jedis.exists(getBytesKey(key))) {
				jedis.del(key);
			}
			List<byte[]> list = Lists.newArrayList();
			for (Object o : value) {
				list.add(toBytes(o));
			}
			result = jedis.rpush(getBytesKey(key), list.toArray(new byte[][]{}));
			if (cacheSeconds != 0) {
				jedis.expire(key, cacheSeconds);
			}
		} catch (Exception e) {
			logger.warn("setObjectList {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 设置MQList缓存
	 * 存取json字符串，方便lua脚本提取做判断
	 *
	 * @param key          键
	 * @param value        值
	 * @param cacheSeconds 超时时间，0为不超时
	 */
	public long setMQObjectList(String key, List<String> value, int cacheSeconds) {
		long result = 0;
		Jedis jedis = null;
		try {
			jedis = getResource();
			if (jedis.exists(getBytesKey(key))) {
				jedis.del(key);
			}
			result = jedis.rpush(key, value.toArray(new String[value.size()]));
			if (cacheSeconds != 0) {
				jedis.expire(key, cacheSeconds);
			}
		} catch (Exception e) {
			logger.warn("setMQObjectList {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 向List缓存中添加值
	 *
	 * @param key   键
	 * @param value 值
	 */
	public long listAdd(String key, String... value) {
		long result = 0;
		Jedis jedis = null;
		try {
			jedis = getResource();
			result = jedis.rpush(key, value);
		} catch (Exception e) {
			logger.warn("listAdd {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 向List缓存中添加值
	 *
	 * @param key   键
	 * @param value 值
	 */
	public long listObjectAdd(String key, Object... value) {
		long result = 0;
		Jedis jedis = null;
		try {
			jedis = getResource();
			List<byte[]> list = Lists.newArrayList();
			for (Object o : value) {
				list.add(toBytes(o));
			}
			result = jedis.rpush(getBytesKey(key), list.toArray(new byte[][]{}));
		} catch (Exception e) {
			logger.warn("listObjectAdd {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 获取缓存
     * 如果元素过多，需要遍历，可能会造成服务器堵塞，生产环境应避免使用
	 *
	 * @param key 键
	 * @return 值
	 */
	public Set<String> getSet(String key) {
		Set<String> value = null;
		Jedis jedis = null;
		try {
			jedis = getResource();
			if (jedis.exists(key)) {
				value = jedis.smembers(key);
			}
		} catch (Exception e) {
			logger.warn("getSet {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return value;
	}

	/**
	 * 获取缓存
	 *
	 * @param key 键
	 * @return 值
	 */
	public String getPop(String key) {
		String value = null;
		Jedis jedis = null;
		try {
			jedis = getResource();
			if (jedis.exists(key)) {
				value = jedis.spop(key);
			}
		} catch (Exception e) {
			logger.warn("getSet {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return value;
	}

	/**
	 * 设置Set缓存
	 *
	 * @param key          键
	 * @param value        值
	 * @param cacheSeconds 超时时间，0为不超时
	 */
	public long setSet(String key, Set<String> value, int cacheSeconds) {
		long result = 0;
		Jedis jedis = null;
		try {
			jedis = getResource();
			if (jedis.exists(key)) {
				jedis.del(key);
			}
			result = jedis.sadd(key, value.toArray(new String[value.size()]));
			if (cacheSeconds != 0) {
				jedis.expire(key, cacheSeconds);
			}
		} catch (Exception e) {
			logger.warn("setSet {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 设置Set缓存
	 *
	 * @param key          键
	 * @param value        值
	 * @param cacheSeconds 超时时间，0为不超时
	 */
	public long setObjectSet(String key, Set<Object> value, int cacheSeconds) {
		long result = 0;
		Jedis jedis = null;
		try {
			jedis = getResource();
			if (jedis.exists(getBytesKey(key))) {
				jedis.del(key);
			}
			Set<byte[]> set = Sets.newHashSet();
			for (Object o : value) {
				set.add(toBytes(o));
			}
			result = jedis.sadd(getBytesKey(key), (byte[][]) set.toArray());
			if (cacheSeconds != 0) {
				jedis.expire(key, cacheSeconds);
			}
		} catch (Exception e) {
			logger.warn("setObjectSet {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 向Set缓存中添加值
	 *
	 * @param key   键
	 * @param value 值
	 */
	public boolean setSetAdd(String key, String value) {
		long result = 0;
		Jedis jedis = null;
		try {
			jedis = getResource();
			result = jedis.sadd(key, value);
			if (result == 1) {
				return true;
			}
		} catch (Exception e) {
			logger.warn("setSetAdd {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return false;
	}

	/**
	 * 查询某个值是否存在于set集合中
	 *
	 * @param key   键
	 * @param value 值
	 */
	public boolean sismemeberSet(String key, String value) {
		boolean bool = false;
		Jedis jedis = null;
		try {
			jedis = getResource();
			bool = jedis.sismember(key, value);
		} catch (Exception e) {
			logger.warn("sismemeberSet {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return bool;
	}

	/**
	 * 向Set缓存中添加值
	 *
	 * @param key   键
	 * @param value 值
	 * @return
	 */
	public long setSetAdd(String key, String value, int cacheSeconds) {
		long result = 0;
		Jedis jedis = null;
		try {
			jedis = getResource();
			result = jedis.sadd(key, value);
			if (cacheSeconds != 0) {
				jedis.expire(key, cacheSeconds);
			}
		} catch (Exception e) {
			logger.warn("setSetAdd {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 向Set缓存中添加值
	 *
	 * @param key   键
	 * @param value 值
	 */
	public long setSetObjectAdd(String key, Object... value) {
		long result = 0;
		Jedis jedis = null;
		try {
			jedis = getResource();
			Set<byte[]> set = Sets.newHashSet();
			for (Object o : value) {
				set.add(toBytes(o));
			}
			result = jedis.rpush(getBytesKey(key), (byte[][]) set.toArray());
		} catch (Exception e) {
			logger.warn("setSetObjectAdd {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 获取Map缓存
	 *
	 * @param key 键
	 * @return 值
	 */
	public Map<String, String> getMap(String key) {
		Map<String, String> value = null;
		Jedis jedis = null;
		try {
			jedis = getResource();
			if (jedis.exists(key)) {
				value = jedis.hgetAll(key);
			}
		} catch (Exception e) {
			logger.warn("getMap {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return value;
	}

	/**
	 * 获取Map缓存
	 *
	 * @param key 键
	 * @return 值
	 */
	public Map<String, Object> getObjectMap(String key) {
		Map<String, Object> value = null;
		Jedis jedis = null;
		try {
			jedis = getResource();
			if (jedis.exists(getBytesKey(key))) {
				value = Maps.newHashMap();
				Map<byte[], byte[]> map = jedis.hgetAll(getBytesKey(key));
				for (Map.Entry<byte[], byte[]> e : map.entrySet()) {
					value.put(new String(e.getKey(), "utf-8"), SerializeUtil.unserialize(e.getValue()));
				}
			}
		} catch (Exception e) {
			logger.warn("getObjectMap {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return value;
	}

	/**
	 * 设置Map缓存
	 *
	 * @param key          键
	 * @param value        值
	 * @param cacheSeconds 超时时间，0为不超时
	 */
	public String setMap(String key, Map<String, String> value, int cacheSeconds) {
		String result = null;
		Jedis jedis = null;
		try {
			jedis = getResource();
			if (jedis.exists(key)) {
				jedis.del(key);
			}
			result = jedis.hmset(key, value);
			if (cacheSeconds != 0) {
				jedis.expire(key, cacheSeconds);
			}
		} catch (Exception e) {
			logger.warn("setMap {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 设置Map缓存
	 *
	 * @param key          键
	 * @param value        值
	 * @param cacheSeconds 超时时间，0为不超时
	 */
	public String setObjectMap(String key, Map<String, Object> value, int cacheSeconds) {
		String result = null;
		Jedis jedis = null;
		try {
			jedis = getResource();
			if (jedis.exists(getBytesKey(key))) {
				jedis.del(key);
			}
			Map<byte[], byte[]> map = Maps.newHashMap();
			for (Map.Entry<String, Object> e : value.entrySet()) {
				map.put(getBytesKey(e.getKey()), toBytes(e.getValue()));
			}
			result = jedis.hmset(getBytesKey(key), (Map<byte[], byte[]>) map);
			if (cacheSeconds != 0) {
				jedis.expire(key, cacheSeconds);
			}
		} catch (Exception e) {
			logger.warn("setObjectMap {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 向Map缓存中添加值
	 *
	 * @param key   键
	 * @param value 值
	 */
	public String mapPut(String key, Map<String, String> value) {
		String result = null;
		Jedis jedis = null;
		try {
			jedis = getResource();
			if (jedis.exists(getBytesKey(key))) {
				jedis.del(key);
			}
			result = jedis.hmset(key, value);
		} catch (Exception e) {
			logger.warn("mapPut {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 向Map缓存中添加值
	 *
	 * @param key   键
	 * @param value 值
	 */
	public String mapObjectPut(String key, Map<String, Object> value) {
		String result = null;
		Jedis jedis = null;
		try {
			jedis = getResource();
			Map<byte[], byte[]> map = Maps.newHashMap();
			for (Map.Entry<String, Object> e : value.entrySet()) {
				map.put(getBytesKey(e.getKey()), toBytes(e.getValue()));
			}
			result = jedis.hmset(getBytesKey(key), (Map<byte[], byte[]>) map);
		} catch (Exception e) {
			logger.warn("mapObjectPut {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 移除Map缓存中的值
	 *
	 * @param key    键
	 * @param mapKey 值
	 */
	public long mapRemove(String key, String mapKey) {
		long result = 0;
		Jedis jedis = null;
		try {
			jedis = getResource();
			result = jedis.hdel(key, mapKey);
		} catch (Exception e) {
			logger.warn("mapRemove {}  {}", key, mapKey, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 移除Map缓存中的值
	 *
	 * @param key    键
	 * @param mapKey 值
	 */
	public long mapObjectRemove(String key, String mapKey) {
		long result = 0;
		Jedis jedis = null;
		try {
			jedis = getResource();
			result = jedis.hdel(getBytesKey(key), getBytesKey(mapKey));
		} catch (Exception e) {
			logger.warn("mapObjectRemove {}  {}", key, mapKey, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 判断Map缓存中的Key是否存在
	 *
	 * @param key    键
	 * @param mapKey 值
	 */
	public boolean mapExists(String key, String mapKey) {
		boolean result = false;
		Jedis jedis = null;
		try {
			jedis = getResource();
			result = jedis.hexists(key, mapKey);
		} catch (Exception e) {
			logger.warn("mapExists {}  {}", key, mapKey, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 判断Map缓存中的Key是否存在
	 *
	 * @param key    键
	 * @param mapKey 值
	 */
	public boolean mapObjectExists(String key, String mapKey) {
		boolean result = false;
		Jedis jedis = null;
		try {
			jedis = getResource();
			result = jedis.hexists(getBytesKey(key), getBytesKey(mapKey));
		} catch (Exception e) {
			logger.warn("mapObjectExists {}  {}", key, mapKey, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 删除缓存
	 *
	 * @param key 键
	 */
	public long del(String key) {
		long result = 0;
		Jedis jedis = null;
		try {
			jedis = getResource();
			if (jedis.exists(key)) {
				result = jedis.del(key);
				logger.debug("del {}", key);
			} else {
				logger.debug("del {} not exists", key);
			}
		} catch (Exception e) {
			logger.warn("del {}", key, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 删除缓存
	 *
	 * @param key 键
	 */
	public long delObject(String key) {
		long result = 0;
		Jedis jedis = null;
		try {
			jedis = getResource();
			if (jedis.exists(getBytesKey(key))) {
				result = jedis.del(getBytesKey(key));
				logger.debug("delObject {}", key);
			} else {
				logger.debug("delObject {} not exists", key);
			}
		} catch (Exception e) {
			logger.warn("delObject {}", key, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 缓存是否存在
	 *
	 * @param key 键
	 */
	public boolean exists(String key) {
		boolean result = false;
		Jedis jedis = null;
		try {
			jedis = getResource();
			result = jedis.exists(key);
			logger.debug("exists {}", key);
		} catch (Exception e) {
			logger.warn("exists {}", key, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 统计成员数量
	 *
	 * @param key
	 * @return
	 */
	public Long zcardCount(String key) {
		Long result = null;
		Jedis jedis = null;
		try {
			jedis = getResource();
			result = jedis.zcard(getBytesKey(key));
		} catch (Exception e) {
			logger.warn("zcard {} = {} ", key, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 缓存是否存在
	 *
	 * @param key 键
	 */
	public boolean existsObject(String key) {
		boolean result = false;
		Jedis jedis = null;
		try {
			jedis = getResource();
			result = jedis.exists(getBytesKey(key));
			logger.debug("existsObject {}", key);
		} catch (Exception e) {
			logger.warn("existsObject {}", key, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 加载lua脚本
	 *
	 * @param script 键
	 * @return 值
	 */
	public String scriptLoad(String script) {
		Jedis jedis = null;
		String sha = null;
		try {
			jedis = getResource();
			sha = jedis.scriptLoad(script);
		} catch (Exception e) {
			logger.warn("scriptLoad {} = {}", script, sha, e);
		} finally {
			returnResource(jedis);
		}
		return sha;
	}

    /**
     * 清除lua脚本load
     *
     * @return 值
     */
    public void scriptFlush() {
        Jedis jedis = null;
        try {
            jedis = getResource();
            jedis.scriptFlush();
        } catch (Exception e) {
            logger.warn("script sha1 error = {}", e);
        } finally {
            returnResource(jedis);
        }
    }

	/**
	 * 执行lua脚本
	 *
	 * @param script 键
	 * @return 值
	 */
	public Object evalSha(String script, int keyCount, String... params) {
		Jedis jedis = null;
		Object object = null;
		try {
			jedis = getResource();
			object = jedis.evalsha(script, keyCount, params);
		} catch (Exception e) {
			logger.warn("get {} = {}", script, object, e);
		} finally {
			returnResource(jedis);
		}
		return object;
	}

    /**
     * 执行lua脚本
     *
     * @param script 键
     * @return 值
     */
    public Object evalSha(String script) {
        Jedis jedis = null;
        Object object = null;
        try {
            jedis = getResource();
            object = jedis.evalsha(script);
        } catch (Exception e) {
            logger.warn("get {} = {}", script, object, e);
        } finally {
            returnResource(jedis);
        }
        return object;
    }

	/**
	 * 执行redis单点 lua脚本
	 * @param script
	 * @param keys
	 * @param argvs
	 * @return
	 */
	public long evalLua(String script, List<String> keys, List<String> argvs) {
		Jedis jedis = null;
		Object object = null;
		long result = 0 ;
		try {
			jedis = getResource();
			object = jedis.eval(script, keys, argvs);
			result = Long.parseLong(object.toString());
		} catch (Exception e) {
			logger.warn("get {} = {}", script, object, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 执行redis单点 lua脚本
	 * @param script
	 * @param keys
	 * @param argvs
	 * @return
	 */
	public  List<Long> evalLuaTable(String script, List<String> keys, List<String> argvs) {
		Jedis jedis = null;
		Object object = null;
		List<Long> result = new ArrayList<>();
		try {
			jedis = getResource();
			object = jedis.eval(script, keys, argvs);
			result = (ArrayList<Long>) object;
		} catch (Exception e) {
			logger.warn("get {} = {}", script, object, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 获取sort缓存
	 *
	 * @param key               键
	 * @param sortingParameters 排序对象
	 * @return 值
	 */
	public List<String> sort(String key, SortingParams sortingParameters) {
		List<String> value = null;
		Jedis jedis = null;
		try {
			jedis = getResource();
			if (jedis.exists(key)) {
				value = jedis.sort(key, sortingParameters);
			}
		} catch (Exception e) {
			logger.warn("sort {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return value;
	}

	/**
	 * 向zset 赋值
	 *
	 * @param key
	 * @param score
	 * @param member
	 * @param cacheSeconds 过期时间0为不设置过期时间（秒）
	 * @return
	 */
	public boolean zadd(String key, long score, String member, int cacheSeconds) {
		Jedis jedis = getResource();
		try {
			Long zadd = jedis.zadd(key, score, member);
			if (cacheSeconds != 0) {
				jedis.expire(key, cacheSeconds);
			}
		} catch (Exception e) {
			logger.warn("key = {} score = {} member = {}", key, score, member, e);
		} finally {
			returnResource(jedis);
		}
		return true;
	}

	/**
	 * 向zset 赋值
	 *
	 * @param key
	 * @param score
	 * @param member
	 * @return
	 */
	public boolean zadd(String key, long score, String member) {
		Jedis jedis = getResource();
		try {
			Long zadd = jedis.zadd(key, score, member);
		} catch (Exception e) {
			logger.warn("key = {} score = {} member = {}", key, score, member, e);
		} finally {
			returnResource(jedis);
		}
		return true;
	}

	/**
	 * 获取当前key中存在多少个value
	 *
	 * @param key
	 * @return
	 */
	public long zcard(String key) {
		Jedis jedis = getResource();
		long num = 0l;
		try {
			num = jedis.zcard(key);
		} catch (Exception e) {
			logger.warn("key = {}", key, e);
		} finally {
			returnResource(jedis);
		}
		return num;
	}

	/**
	 * 返回区间内的个数
	 *
	 * @param key
	 * @param min
	 * @param max
	 * @return
	 */
	public long zcount(String key, double min, double max) {
		Jedis jedis = getResource();
		long num = 0l;
		try {
			num = jedis.zcount(key, min, max);
		} catch (Exception e) {
			logger.warn("key = {}", key, e);
		} finally {
			returnResource(jedis);
		}
		return num;
	}

	/**
	 * 返回区间内的对象集合
	 *
	 * @param key
	 * @param min
	 * @param max
	 * @return
	 */
	public Set<String> zrevrange(String key, long min, long max) {
		Jedis jedis = getResource();
		Set<String> zrange = null;
		try {
			zrange = jedis.zrevrange(key, min, max);
		} catch (Exception e) {
			logger.warn("key = {} min = {} max {}", key, min, max, e);
		} finally {
			returnResource(jedis);
		}
		return zrange;
	}

	/**
	 * 返回区间内的对象集合 和分数
	 *
	 * @param key
	 * @param min
	 * @param max
	 * @return
	 */
	public Set<Tuple> zrangeWithScores(String key, long min, long max) {
		Jedis jedis = getResource();
		Set<Tuple> tuples = null;
		try {
			tuples = jedis.zrangeWithScores(key, min, max);
		} catch (Exception e) {
			logger.warn("key = {} min = {} max {}", key, min, max, e);
		} finally {
			returnResource(jedis);
		}
		return tuples;
	}

	/**
	 * 给某个member 增加分数
	 *
	 * @param key
	 * @param addScore
	 * @param member
	 * @return
	 */
	public double zincrby(String key, double addScore, String member) {
		Jedis jedis = getResource();
		double num = 0L;
		try {
			num = jedis.zincrby(key, addScore, member);
		} catch (Exception e) {
			logger.warn("key = {} addScore = {} member = {}", key, addScore, member, e);
		} finally {
			returnResource(jedis);
		}
		return num;
	}

	/**
	 * 删除指定的zset中的member
	 *
	 * @param key
	 * @param member
	 * @return
	 */
	public boolean zrem(String key, String member) {
		Jedis jedis = getResource();
		try {
			Long num = jedis.zrem(key, member);
			if (num == 1) {
				return true;
			}
		} catch (Exception e) {
			logger.warn("key = {} member = {}", key, member, e);
		} finally {
			returnResource(jedis);
		}
		return false;
	}

	/**
	 * 获取zset缓存
	 *
	 * @param key   键
	 * @param start 开始索引
	 * @param end   结束索引
	 */
	public List<String> getZSet(String key, long start, long end) {
		List<String> value = null;
		Jedis jedis = null;
		try {
			jedis = getResource();
			if (jedis.exists(getBytesKey(key))) {
				Set<byte[]> list = jedis.zrange(getBytesKey(key), start, end);
				value = Lists.newArrayList();
				if (list == null || list.size() == 0) {
					return null;
				}
				for (byte[] bs : list) {
					value.add(SerializeUtil.unserialize(bs).toString());
				}
			}
		} catch (Exception e) {
			logger.warn("getZSet {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return value;
	}

	/**
	 * 设置zset缓存
	 *
	 * @param key    键
	 * @param score  分值
	 * @param member 成员
	 */
	public Long setZAdd(String key, long score, String member) {
		Long result = null;
		Jedis jedis = null;
		try {
			jedis = getResource();
			result = jedis.zadd(getBytesKey(key), score, toBytes(member));
		} catch (Exception e) {
			logger.warn("zadd {} = score:{},member:{}", key, score, member, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 设置zset缓存（未转换字节码）
	 *
	 * @param key    键
	 * @param score  分值
	 * @param member 成员
	 */
	public Long setZAdd1(String key, long score, String member) {
		Long result = null;
		Jedis jedis = null;
		try {
			jedis = getResource();
			result = jedis.zadd(key, score, member);
		} catch (Exception e) {
			logger.warn("zadd {} = score:{},member:{}", key, score, member, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 消除ZSet缓存的值
	 *
	 * @param key   键
	 * @param value 值
	 * @return
	 */
	public long remZSet(String key, String value) {
		long result = 0;
		Jedis jedis = null;
		try {
			jedis = getResource();
			result = jedis.zrem(getBytesKey(key), toBytes(value));
		} catch (Exception e) {
			logger.warn("srem {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 向Set缓存中删除值
	 *
	 * @param key   键
	 * @param value 值
	 * @return
	 */
	public long remSet(String key, String value) {
		long result = 0;
		Jedis jedis = null;
		try {
			jedis = getResource();
			result = jedis.srem(key, value);
		} catch (Exception e) {
			logger.warn("srem {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 获取并删除，List缓存
	 *
	 * @param key 键
	 * @return 值
	 */
	public List<String> getObjectListAndRem(String key) {
		List<String> value = null;
		Jedis jedis = null;
		try {
			jedis = getResource();
			List<String> list = jedis.lrange(key, 0, -1);
			if (list == null || list.size() == 0) {
				return null;
			}
			value = Lists.newArrayList();
			for (String string : list) {
				jedis.lrem(key, 1, string);
				value.add(string);
			}
		} catch (Exception e) {
			logger.warn("getObjectList {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return value;
	}

	/**
	 * 清除List缓存中指定的值
	 *
	 * @param key
	 * @param count 数量
	 * @param value
	 * @return
	 */
	public long listObjectRem(String key, long count, Object value) {
		long result = 0;
		Jedis jedis = null;
		try {
			jedis = getResource();
			result = jedis.lrem(key, count, value.toString());
		} catch (Exception e) {
			logger.warn("listObjectAdd {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return result;
	}

	/**
	 * 删除指定的sadd中的member
	 *
	 * @param key
	 * @param member
	 * @return
	 */
	public boolean srem(String key, String member) {
		Jedis jedis = getResource();
		try {
			Long num = jedis.srem(key, member);
			if (num == 1) {
				return true;
			}
		} catch (Exception e) {
			logger.warn("key = {} member = {}", key, member, e);
		} finally {
			returnResource(jedis);
		}
		return false;
	}

	/**
	 * 添加hash
	 *
	 * @param key
	 * @param value
	 * @return
	 */
	public boolean hset(String key, String field, String value) {
		Jedis jedis = getResource();
		try {
			Long num = jedis.hset(key, field, value);
			if (num == 1) {
				return true;
			}
		} catch (Exception e) {
			logger.warn("key = {} member = {}", key, field, e);
		} finally {
			returnResource(jedis);
		}
		return false;
	}

	/**
	 * 判断hash的field是否存在
	 *
	 * @param key
	 * @param field
	 * @return
	 */
	public boolean hexists(String key, String field) {
		Jedis jedis = getResource();
		try {
			Boolean hexists = jedis.hexists(key, field);
			return hexists;
		} catch (Exception e) {
			logger.warn("key = {} member = {}", key, field, e);
		} finally {
			returnResource(jedis);
		}
		return false;
	}


	public boolean setNx(String key, String value) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			Long n = jedis.setnx(key, value);
			return n == 1 ? true : false;
		} catch (Exception e) {
			logger.warn("setNx： key {}，exception {}", key, e);
		} finally {
			returnResource(jedis);
		}
		return false;
	}

	/**
	 * 删除hash的field
	 *
	 * @param key
	 * @param field
	 * @return
	 */
	public boolean hdel(String key, String field) {
		Jedis jedis = getResource();
		try {
			Long hdel = jedis.hdel(key, field);
			if (hdel == 1) {
				return true;
			}
		} catch (Exception e) {
			logger.warn("key = {} member = {}", key, field, e);
		} finally {
			returnResource(jedis);
		}
		return false;
	}

	/**
	 * 获取hash的field的值
	 *
	 * @param key
	 * @param field
	 * @return
	 */
	public String hget(String key, String field) {
		Jedis jedis = getResource();
		try {
			String hget = jedis.hget(key, field);
			return hget;
		} catch (Exception e) {
			logger.warn("key = {} member = {}", key, field, e);
		} finally {
			returnResource(jedis);
		}
		return null;
	}

	/**
	 * watch key
	 *
	 * @param key
	 * @return
	 */
	public void watch(String key) {
		List<String> value = null;
		Jedis jedis = null;
		try {
			jedis = getResource();
			if (jedis.exists(key)) {
				jedis.watch(key);
				jedis.multi();
				jedis.spop(key);
			}
		} catch (Exception e) {
			logger.warn("getZSet {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
	}

	/**
	 * 获取缓存
	 *
	 * @param key 键
	 * @return 值
	 */
	public String getWatchSpop(String key) {
		String value = null;
		Jedis jedis = null;
		try {
			jedis = getResource();
			if (jedis.exists(key)) {
				jedis.watch(key);
				Transaction transaction = jedis.multi();
				transaction.spop(key);
				List<Object> results = transaction.exec();
				if (Objects.nonNull(results) && !results.isEmpty()) {
					value = (String) results.get(0);
					logger.info("exec result:{}", results.toString());
				}
			}
		} catch (Exception e) {
			logger.warn("getWatchSpop {} = {}", key, value, e);
		} finally {
			returnResource(jedis);
		}
		return value;
	}

	/**
	 * 更新key的失效时间
	 * @param key
	 * @param cacheSeconds
	 */
	public void expireKeySeconds(String key, int cacheSeconds) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			if (cacheSeconds != 0) {
				jedis.expire(key, cacheSeconds);
			}
		} catch (Exception e) {
			logger.warn("set {} = {}", key, e);
		} finally {
			returnResource(jedis);
		}
	}

	/**
	 * 返回成员member在有序集合中的排名，成员按照分值从小到大排列
	 * @param key
	 * @param member
	 * @return
	 */
	public int zrank(String key, String member) {
		int zrank = 0;
		Jedis jedis = getResource();
		try {
			if(jedis.zrank(key,member) == null){
				//对象不存在
				zrank = -1;
			}
		} catch (Exception e) {
			logger.warn("key = {} member = {}", key, member);
		} finally {
			returnResource(jedis);
		}
		return zrank;
	}

	public <T> T evalLua(String key, T resultType) {
		Jedis jedis = getResource();
		try {
			Object object = jedis.eval("return redis.call('GETs', 'uzdz');");
            System.out.println(object);
		} catch (Exception e) {
			logger.warn("key = {} member = {}", key);
		} finally {
			returnResource(jedis);
		}
		return resultType;
	}

	/**
	 * @desc  判断key 的value 值是否大于 limitCount ，如果大于返回false
	 * @param redisKey
	 * @param limitCount
	 * @return
	 */
	public int checkLimit(String redisKey, String limitCount,String expire) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			String script = "local key = KEYS[1] local limit = tonumber(ARGV[1]) local exp = ARGV[2] local current = tonumber(redis.call('get', key) or 0) if current + 1 > limit then return 0 else  redis.call('INCRBY', key, '1') redis.call('expire', key, exp) return current + 1 end";
			List<String> values = new ArrayList<>(2);
			values.add(limitCount);
			values.add(expire);
			Object result = jedis.eval(script, Collections.singletonList(redisKey), values);
			Long  longResult = (Long)result;
			if (longResult.intValue()>0) {
				return longResult.intValue();
			}else{
				return 0;
			}
		} catch (Exception e) {
			logger.error("set {} = {}", redisKey, e);
			return 2;
		} finally {
			returnResource(jedis);
		}

	}

	/**
	 *
	 * @desc key，不存在时设置key和过期时间，单位毫秒,1设置成功 0 设置失败 2 redis 异常
	 * @param key
	 * @param requestId
	 * @param expireTime
	 * @return
	 */
	public int setKeyAndExpireTime(String key, String requestId, int expireTime) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			String result = jedis.set(key, requestId, "NX", "PX", expireTime);
			if("OK".equals(result)){
				return 1;
			}else{
				return 0;
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return 2;
		} finally {
			returnResource(jedis);
		}
	}

	/**
	 * 释放分布式锁
	 * @param lockKey 锁
	 * @param requestId 请求标识
	 * @return 是否释放成功
	 */
	public boolean releaseDistributedLock(String lockKey, String requestId) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
			Object result = jedis.eval(script, Collections.singletonList(lockKey), Collections.singletonList(requestId));
			if (RELEASE_SUCCESS.equals(result)) {
				return true;
			}
		} catch (Exception e) {
			logger.warn("set {} = {}", lockKey, e);
		} finally {
			returnResource(jedis);
		}
		return false;
	}

	public boolean setNx(String key, String value,int ms) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			Long n = jedis.setnx(key, value);
			if(n == 1){
				// key的超时时间设置1小时
				jedis.expire(key, ms);
			}
			return n == 1 ? true : false;
		} catch (Exception e) {
			e.printStackTrace();
			logger.warn("setNx： key {}，exception {}", key, e);
		} finally {
			returnResource(jedis);
		}
		return false;
	}

	public boolean delVal(String key) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			Long n = jedis.del(key);
			return n == 1 ? true : false;
		} catch (Exception e) {
			e.printStackTrace();
			logger.warn("delVal： key {}，exception {}", key, e);
		} finally {
			returnResource(jedis);
		}
		return false;
	}

}