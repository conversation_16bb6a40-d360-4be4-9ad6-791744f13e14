package com.xyy.saas.web.api.module.pay.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/5/17 14:37
 */
@ApiModel(description = "mcc编码查询")
@Data
public class MccCodeQueryVo {

    @ApiModelProperty(value = "父节点编码信息，不传为一级")
    private String parentCode;

    @ApiModelProperty(value = "根节点编码信息，不传为一级")
    private String basicCode;

}
