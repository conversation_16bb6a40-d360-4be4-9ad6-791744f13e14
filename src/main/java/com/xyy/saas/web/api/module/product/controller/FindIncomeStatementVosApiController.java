package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.web.api.module.product.model.InventoryIncomeStatementVo;
import com.xyy.saas.web.api.module.product.model.InventoryIncomeStatementVoList;
import io.swagger.annotations.ApiParam;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-30T11:13:55.467+08:00")

@Controller
public class FindIncomeStatementVosApiController implements FindIncomeStatementVosApi {



    public ResponseEntity<InventoryIncomeStatementVoList> findIncomeStatementVos(@ApiParam(value = "报损报溢单Vo" ,required=true )  @Valid @RequestBody InventoryIncomeStatementVo inventoryIncomeStatementVo) {
        // do some magic!
        return new ResponseEntity<InventoryIncomeStatementVoList>(HttpStatus.OK);
    }

}
