package com.xyy.saas.web.api.module.member.model;

import lombok.Data;

import java.io.Serializable;

@Data
public class MemberChronicProductInfoVo {

    //商品内码
    private String productPref;
    //商品编号,外码,用作展示
    private String pharmacyPref;
    //通用名称
    private String commonName;
    //规格型号
    private String attributeSpecification;
    //单位名称
    private String unitName;
    //单位id
    private Integer unitId;
    //剂型id
    private Integer dosageFormId;
    //剂型名称
    private String dosageFormName;
    //生产厂家
    private String manufacturer;
    //商品分类id
    private Integer systemTypeId;
    //商品分类名称
    private String systemTypeName;
    //自定义分类id
    private Integer productType;
    //自定义分类名称
    private String productTypeName;
    //abc类型id
    private Integer abcDividing;
    //abc类型名称
    private String abcDividingName;
    //助记码
    private String mnemonicCode;

    ////库存数量
    //private BigDecimal stockNumber;
    ////成本价
    //private BigDecimal costPrice;
    ////零售价
    //private BigDecimal retailPrice;
    ////vip会员价
    //private BigDecimal vipPrice;
    ////毛利率
    //private BigDecimal grossMargin;
    ////产地
    //private String producingArea;
    ////是否为特殊商品 0:否, 1:是
    //private Byte special;
}
