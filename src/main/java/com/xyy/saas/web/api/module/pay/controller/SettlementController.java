package com.xyy.saas.web.api.module.pay.controller;


import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.pay.core.api.MerchantApi;
import com.xyy.saas.pay.core.api.PayInfoApi;
import com.xyy.saas.pay.core.dto.req.QuerySettlementRecordParam;
import com.xyy.saas.pay.core.dto.res.QueryPageSettlementInfoVO;
import com.xyy.saas.pay.core.dto.res.SettlementRecordInfo;
import com.xyy.saas.web.api.module.pay.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.BeanUtils;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2021-05-07T11:16:06.377+08:00")

@RestController
@RequestMapping("/pay/settlement")
@Api(value = "settlement", tags = {"支付服务-结算记录接口"}, description = "pay settlement API", produces = MediaType.APPLICATION_JSON_VALUE)
public class SettlementController {
    @Reference(version = "0.0.1")
    private PayInfoApi payInfoApi;

    @ApiOperation(value = "结算记录查询", notes = "结算记录查询", tags={ "settlement", })
    @ApiResponses(value = {@ApiResponse(code = 0, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/settlementRecordQuery", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    public ResultVO<PageInfo<SettlementRecordVo>> settlementRecordQuery(@RequestBody SettlementQueryVo queryVo, @RequestHeader(name = "organSign", required = true) String organSign){
        QuerySettlementRecordParam param = new QuerySettlementRecordParam();
        param.setOrganSign(organSign);
        param.setSettlementStartTime(queryVo.getStartTime());
        param.setSettlementEndTime(queryVo.getEndTime());
        BeanUtils.copyProperties(queryVo,param);
        QueryPageSettlementInfoVO queryPageSettlementInfoVO = payInfoApi.querySettlementPageQuery(param);
        PageInfo<SettlementRecordVo> pageInfo = new PageInfo<>();
        BeanUtils.copyProperties(queryPageSettlementInfoVO,pageInfo);
        List<SettlementRecordInfo> list = queryPageSettlementInfoVO.getList();
        List<SettlementRecordVo> resultList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)){
            for (SettlementRecordInfo settlementRecordInfo : list) {
                SettlementRecordVo vo = new SettlementRecordVo();
                BeanUtils.copyProperties(settlementRecordInfo,vo);
                resultList.add(vo);
            }
        }
        pageInfo.setList(resultList);
        return ResultVO.createSuccess(pageInfo);
    }


    @ApiOperation(value = "结算明细查询", notes = "结算记录查询",  tags={ "settlement", })
    @RequestMapping(value = "/settlementDetail", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    @ApiResponses(value = {@ApiResponse(code = 0, message = "操作成功", response = ResultVO.class) })
    public ResultVO<SettlementDetailVo> settlementDetail(@RequestBody SettlementDetailQueryVo queryVo, @RequestHeader(name = "organSign", required = true) String organSign){
        SettlementDetailVo settlementDetailVo = new SettlementDetailVo();
        SettlementDetailQueryVo settlementDetailQueryVo  = new SettlementDetailQueryVo();
        settlementDetailQueryVo.setSettlementOrderNo(queryVo.getSettlementOrderNo());
        SettlementRecordInfo settlementRecordInfo = payInfoApi.querySettleRecordDetail(settlementDetailQueryVo);
        if (null != settlementRecordInfo){
            BeanUtils.copyProperties(settlementRecordInfo,settlementDetailVo);
        }
        return ResultVO.createSuccess(settlementDetailVo);
    }

}
