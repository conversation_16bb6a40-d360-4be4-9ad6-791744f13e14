package com.xyy.saas.web.api.module.product.model;


public class ProviderBaseinfoVo extends SaasProviderBaseInfo{

    private String registeredDateStr;

    private String youxianqiDate;

    // 状态
    private String statusName;
    
    /**
     * 经营范围转义字符串
     */
    private String businessScopeStr;
    
    /**
     * 供应商类别
     */
    private String providerTypeName;
    
    
    /**
     * 有效期至方式
     */
    private String expirationDateTypeName;
    
    /**
     * 是否三合一
     */
    private String threeInOneTypeName;
    
    
    /**
     * 开票编号
     */
    private String billNo;
    
    /**
     * 开票日期
     */
    private String billCreateDate;
    
    /**
     * 开票员
     */
    private String billCreateUser;

	/** 附件路径*/
	private String imgUrl;
    
    
    
    private ProviderExtVo providerExtVo;
    
    private String organizationCertificationExpirationDateStr;
    
    private String organizationCertificationDateStr;
    

    public String getRegisteredDateStr() {
        return registeredDateStr;
    }

    public void setRegisteredDateStr(String registeredDateStr) {
        this.registeredDateStr = registeredDateStr;
    }

    public String getYouxianqiDate() {
        return youxianqiDate;
    }

    public void setYouxianqiDate(String youxianqiDate) {
        this.youxianqiDate = youxianqiDate;
    }

	public String getBusinessScopeStr() {
		return businessScopeStr;
	}

	public void setBusinessScopeStr(String businessScopeStr) {
		this.businessScopeStr = businessScopeStr;
	}

	public String getProviderTypeName() {
		return providerTypeName;
	}

	public void setProviderTypeName(String providerTypeName) {
		this.providerTypeName = providerTypeName;
	}

	public String getExpirationDateTypeName() {
		return expirationDateTypeName;
	}

	public void setExpirationDateTypeName(String expirationDateTypeName) {
		this.expirationDateTypeName = expirationDateTypeName;
	}

	public String getThreeInOneTypeName() {
		return threeInOneTypeName;
	}

	public void setThreeInOneTypeName(String threeInOneTypeName) {
		this.threeInOneTypeName = threeInOneTypeName;
	}

	public ProviderExtVo getProviderExtVo() {
		return providerExtVo;
	}

	public void setProviderExtVo(ProviderExtVo providerExtVo) {
		this.providerExtVo = providerExtVo;
	}

	public String getBillNo() {
		return billNo;
	}

	public void setBillNo(String billNo) {
		this.billNo = billNo;
	}

	public String getBillCreateDate() {
		return billCreateDate;
	}

	public void setBillCreateDate(String billCreateDate) {
		this.billCreateDate = billCreateDate;
	}

	public String getBillCreateUser() {
		return billCreateUser;
	}

	public void setBillCreateUser(String billCreateUser) {
		this.billCreateUser = billCreateUser;
	}

	public String getStatusName() {
		return statusName;
	}

	public void setStatusName(String statusName) {
		this.statusName = statusName;
	}

	public String getOrganizationCertificationExpirationDateStr() {
		return organizationCertificationExpirationDateStr;
	}

	public void setOrganizationCertificationExpirationDateStr(String organizationCertificationExpirationDateStr) {
		this.organizationCertificationExpirationDateStr = organizationCertificationExpirationDateStr;
	}

	public String getOrganizationCertificationDateStr() {
		return organizationCertificationDateStr;
	}

	public void setOrganizationCertificationDateStr(String organizationCertificationDateStr) {
		this.organizationCertificationDateStr = organizationCertificationDateStr;
	}

	public String getImgUrl() {
		return imgUrl;
	}

	public void setImgUrl(String imgUrl) {
		this.imgUrl = imgUrl;
	}
}
