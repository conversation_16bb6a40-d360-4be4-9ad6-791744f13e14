package com.xyy.saas.web.api.module.pay.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/7 19:28
 */
@ApiModel(description = "商家信息")
@Data
public class BusinessInfoVo {

    @ApiModelProperty(value = "商户简称")
    private String shortName;

    @ApiModelProperty(value = "商户全称")
    private String name;

    @ApiModelProperty(value = "MCC码商户行业编号")
    private String mccCode;

    @ApiModelProperty(value = "MCC码商户行业名称列表")
    private String mccName;

    @ApiModelProperty(value = "商户所在省（填写支付宝地区码）")
    private String provinceCode;

    @ApiModelProperty(value = "商户所在省名称")
    private String provinceName;

    @ApiModelProperty(value = "市（填写支付宝地区码）")
    private String cityCode;

    @ApiModelProperty(value = "市名称")
    private String cityName;

    @ApiModelProperty(value = "区县（无区县的市传街道编码，东莞、中山）（填写支付宝地区码）")
    private String areaCode;

    @ApiModelProperty(value = "区县名称")
    private String areaName;

    @ApiModelProperty(value = "商户详细地址(5-40个字符)")
    private String address;

    @ApiModelProperty(value = "门头照")
    private String doorPic;

    @ApiModelProperty(value = "经营场所内设照片")
    private String insidePic;

    @ApiModelProperty(value = "收银台照片")
    private String cashierDeskPic;

}
