package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 联营会员资料共享记录Vo
 * <AUTHOR>
 */
@ApiModel(description = "联营会员资料共享记录")
public class MemberShareVo implements Serializable {

    private static final long serialVersionUID = 80641789094056676L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id", example = "37")
    private Long id;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", example = "12")
    private String createUser;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人", example = "14")
    private String updateUser;

    /**
     * 机构标识
     */
    @ApiModelProperty(value = "更新人", required = true, example = "ZHL00001411")
    private String organSign;

    /**
     * 积分折算比例
     */
    @ApiModelProperty(value = "积分折算比例", required = true, example = "1.051")
    private BigDecimal pointRate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public BigDecimal getPointRate() {
        return pointRate;
    }

    public void setPointRate(BigDecimal pointRate) {
        this.pointRate = pointRate;
    }

    @Override
    public String toString() {
        return "MemberShareVo{" +
                "id=" + id +
                ", createUser='" + createUser + '\'' +
                ", updateUser='" + updateUser + '\'' +
                ", organSign='" + organSign + '\'' +
                ", pointRate=" + pointRate +
                '}';
    }
}
