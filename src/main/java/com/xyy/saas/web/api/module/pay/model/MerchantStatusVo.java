package com.xyy.saas.web.api.module.pay.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/5/7 20:28
 */
@ApiModel(description = "商家开户状态")
@Data
public class MerchantStatusVo {

    @ApiModelProperty(value = "状态 （ 0：未开通 ， 1：待完善资料 ， 2：资料审核中 ， 3：审核失败，需要修改， 4：审核成功")
    private Integer status;
    @ApiModelProperty(value = "额外信息")
    private String extraMsg;
    @ApiModelProperty(value = "门店类型")
    private String accountType;

}
