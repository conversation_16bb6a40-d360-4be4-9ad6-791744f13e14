package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.promotion.core.dto.PromotionPlanVoDto;
import com.xyy.saas.rabbitmq.core.api.MessagePushApi;
import com.xyy.saas.web.api.module.product.model.PromotionPlanVo;
import io.swagger.annotations.Api;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> on 2018年9月19日
 */
@Api(value = "促销计划", description = "促销计划", tags = "促销计划")
@RestController
public class PromotionPlanApiController implements PromotionPlanApi {

	@Reference(version = "0.0.1", timeout=3000000)
//	@Reference(version = "0.0.1", url="dubbo://localhost:22999", timeout=3000000)
	private com.xyy.saas.promotion.core.api.PromotionPlanApi promotionPlanApi;

	@Reference(version = "0.0.1")
	private MessagePushApi messagePushApi;

	@Override
	public ResponseEntity<ResultVO> save(HttpServletRequest request, PromotionPlanVo promotionPlan) {
		PromotionPlanVoDto promotionPlanDto = new PromotionPlanVoDto();

		BeanUtils.copyProperties(promotionPlan, promotionPlanDto);
		String organSign = request.getHeader("organSign");

		promotionPlanDto.setOrganSign(organSign);
		promotionPlanApi.save(promotionPlanDto);

		return new ResponseEntity<ResultVO>(ResultVO.createSuccess(null), HttpStatus.OK);
	}

	@Override
	public ResponseEntity<ResultVO> taskUpdatePromotionState() {
		List<String> organSigns = promotionPlanApi.taskPromotionExpireYn(new Date());
		if (null != organSigns && organSigns.size() > 0){
			//发送mq进行数据同步
			String[] tables = new String[]{"saas_promotion_plan"};
			organSigns.forEach(item -> {
				pushProductMessToMQ(item, tables);
			});
		}
		return new ResponseEntity<>(ResultVO.createSuccess(null), HttpStatus.OK);
	}

	@Override
	public ResponseEntity<ResultVO> getPromotionPlanType(@RequestParam(value = "prefList")List<String> prefList) {
		   Map<String, String> map=  new HashMap<>();
		if(prefList == null ||prefList.size()==0){
			return new ResponseEntity<ResultVO>(ResultVO.createSuccess(map) , HttpStatus.OK);
		}else{
			map=promotionPlanApi.queryPromotionPlanType(prefList);
			return new ResponseEntity<ResultVO>(ResultVO.createSuccess(map) , HttpStatus.OK);
		}

	}

	private void pushProductMessToMQ(String organSign, String[] tables) {
		JSONObject json = new JSONObject();
		json.put("code", "sync");
		json.put("tables", tables);
		messagePushApi.sendMsgByOrganSign(organSign, json.toJSONString());
	}

	@Override
	public int promotionSsj(String org) {
//		return promotionPlanApi.promotionSsj(org);;
		return promotionPlanApi.promotionSsj(org);
	}
}
