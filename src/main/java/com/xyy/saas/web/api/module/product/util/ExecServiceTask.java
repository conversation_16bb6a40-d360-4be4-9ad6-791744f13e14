package com.xyy.saas.web.api.module.product.util;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * @ClassName ExecServiceTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/9/24 17:44
 * @Version 1.0
 **/
public class ExecServiceTask {
    private static final int nThreads = Runtime.getRuntime().availableProcessors()+1;
    private static ExecutorService executor=
            Executors.newFixedThreadPool(nThreads);
    public static void execute(Runnable runnable){
        executor.execute(runnable);
    }
}
