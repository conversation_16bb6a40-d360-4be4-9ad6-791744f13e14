package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName CostAdjustsByPrefResultVo
 * @Description 成本调价获取详情查询结果类
 * <AUTHOR>
 * @Date 2020/8/19 18:51
 * @Version 1.0
 **/
@ApiModel(description = "成本调价获取详情查询结果类")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")
public class CostAdjustsByPrefResultVo {

    @JsonProperty("pref")
    private String pref;//方案编号
    @JsonProperty("name")
    private String name;//方案名称
    @JsonProperty("expirationTime")
    private String expirationTime;//有效期截止至
    @JsonProperty("remarks")
    private String remarks;//备注
    @JsonProperty("detailVoList")
    private List<CostAdjustsByPrefResultDetailVo> detailVoList;

    @ApiModelProperty(value = "方案编号")
    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    @ApiModelProperty(value = "方案名称")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @ApiModelProperty(value = "有效期截止至")
    public String getExpirationTime() {
        return expirationTime;
    }

    public void setExpirationTime(String expirationTime) {
        this.expirationTime = expirationTime;
    }

    @ApiModelProperty(value = "备注")
    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    @ApiModelProperty(value = "调价方案商品列表信息")
    public List<CostAdjustsByPrefResultDetailVo> getDetailVoList() {
        return detailVoList;
    }

    public void setDetailVoList(List<CostAdjustsByPrefResultDetailVo> detailVoList) {
        this.detailVoList = detailVoList;
    }
}
