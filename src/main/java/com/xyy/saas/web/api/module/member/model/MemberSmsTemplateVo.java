package com.xyy.saas.web.api.module.member.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

@ApiModel(description = "会员短信模板")
public class MemberSmsTemplateVo implements Serializable {

    private static final long serialVersionUID = -7118028719149923430L;

    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 通知标题
     */
    @ApiModelProperty(value = "通知标题")
    private String title;
    /**
     * 短信签名
     */
    @ApiModelProperty(value = "短信签名")
    private String signature;
    /**
     * 通知内容
     */
    @ApiModelProperty(value = "通知内容")
    private String content;
    /**
     * 通知内容结尾
     */
    @ApiModelProperty(value = "短信结尾")
    private String contentEnd;
    /**
     * 状态 0 启用  1 禁用
     */
    @ApiModelProperty(value = "状态 0 启用  1 禁用", example = "0")
    private Byte status;
    /**
     * 是否删除 0 是  1 否
     */
    @ApiModelProperty(value = "是否删除 0 否  1 是 ", example = "0")
    private Byte yn;
    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识")
    private String organSign;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 创建用户
     */
    @ApiModelProperty(value = "创建用户")
    private String createUser;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    /**
     * 更新用户
     */
    @ApiModelProperty(value = "更新用户")
    private String updateUser;
    /**
     * 版本控制（pos需要存储到本地）
     */
    @ApiModelProperty(value = "版本控制",hidden = true)
    private Integer baseVersion;
    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    private Integer page;
    /**
     * 页码大小
     */
    @ApiModelProperty(value = "页码大小")
    private Integer rows;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title == null ? null : title.trim();
    }

    public String getSignature() {
		return signature;
	}

	public void setSignature(String signature) {
		this.signature = signature == null ? null : signature.trim();
	}

	public String getContentEnd() {
		return contentEnd;
	}

	public void setContentEnd(String contentEnd) {
		this.contentEnd = contentEnd == null ? null : contentEnd.trim();
	}

	public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Byte getYn() {
        return yn;
    }

    public void setYn(Byte yn) {
        this.yn = yn;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign == null ? null : organSign.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }

    public Integer getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(Integer baseVersion) {
        this.baseVersion = baseVersion;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    @Override
    public String toString() {
        return "MemberSmsTemplateDto{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", content='" + content + '\'' +
                ", status=" + status +
                ", yn=" + yn +
                ", organSign='" + organSign + '\'' +
                ", createTime=" + createTime +
                ", createUser='" + createUser + '\'' +
                ", updateTime=" + updateTime +
                ", updateUser='" + updateUser + '\'' +
                ", baseVersion=" + baseVersion +
                '}';
    }
}
