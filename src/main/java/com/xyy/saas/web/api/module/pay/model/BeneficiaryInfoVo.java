package com.xyy.saas.web.api.module.pay.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/5/7 19:29
 */
@ApiModel(description = "受益人信息")
@Data
public class BeneficiaryInfoVo {

    @ApiModelProperty(value = "受益人姓名")
    private String beneficiaryName;
    @ApiModelProperty(value = "受益人证件类型 （  身份证： 01）")
    private String beneficiaryCardType;
    @ApiModelProperty(value = "受益人证件号码")
    private String beneficiaryCardNo;
    @ApiModelProperty(value = "受益人证件期限类型 （短期 0   长期 1）")
    private String beneficiaryCardEndType;
    @ApiModelProperty(value = "受益人证件签发日期 yyyy-MM-dd")
    private String beneficiaryCardStartDate;
    @ApiModelProperty(value = "受益人证件到期日期 yyyy-MM-dd")
    private String beneficiaryCardEndDate;
    @ApiModelProperty(value = "受益人地址")
    private String beneficiaryAddress;
}
