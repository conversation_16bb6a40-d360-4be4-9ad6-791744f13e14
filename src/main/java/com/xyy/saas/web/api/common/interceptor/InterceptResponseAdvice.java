package com.xyy.saas.web.api.common.interceptor;

import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.web.api.common.context.ActionContext;
import com.xyy.saas.web.api.common.context.ActionContextSupport;
import com.xyy.saas.web.api.common.swagger2.SwaggerUrlFilterHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import javax.servlet.http.HttpServletRequest;

/**
 * 拦截@responsebody的返回值，在这可做数据脱敏等操作
 * <AUTHOR>
 * @date 2019-09-05
 * @mondify
 * @copyright
 */
@ControllerAdvice(basePackages = "com.xyy.saas.web.api.module")
public class InterceptResponseAdvice implements ResponseBodyAdvice<Object> {
    private static Logger logger = LoggerFactory.getLogger(InterceptResponseAdvice.class);
    @Override
    public boolean supports(MethodParameter methodParameter, Class aClass) {
        return true;
    }

    /**
     * 重写，获取@responsebody的返回值并设置到自定义上下文中
     * @param body
     * @param methodParameter
     * @param mediaType
     * @param aClass
     * @param serverHttpRequest
     * @param serverHttpResponse
     * @return
     */
    @Override
    public Object beforeBodyWrite(Object body, MethodParameter methodParameter, MediaType mediaType, Class aClass, ServerHttpRequest serverHttpRequest, ServerHttpResponse serverHttpResponse) {
        String urlPath = serverHttpRequest.getURI().getPath();
        if(!"/error".equals(urlPath)){
            //通过swagger请求的接口不记录log
            HttpServletRequest request = ((ServletServerHttpRequest) serverHttpRequest).getServletRequest();
            if(!SwaggerUrlFilterHelper.containsSwaggerUrl(request)){
                ActionContext current = ActionContextSupport.getCurrent();
                current.setResponseResult(JSONUtils.obj2JSON(body));
            }
        }else{
            logger.error("API exceute error,return body:"+ body);
        }
        return body;
    }
}
