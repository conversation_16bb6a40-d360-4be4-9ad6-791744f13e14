package com.xyy.saas.web.api.module.member.controller;


import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.member.core.api.MemberPrepayCardForZBApi;
import com.xyy.saas.member.core.dto.MemberPrepayCardTokenDto;
import com.xyy.saas.member.core.response.ResultVO;
import com.xyy.saas.web.api.module.member.model.MemberPrepayCardDetailQueryVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/member/memberPrepayCard/zb/token")
@Api(value = "memberPrepayCardToken", description = "会员储值token总部端API")
public class MemberPreparCardTokenForZBApiController {

    private static final Logger logger = Logger.getLogger(MemberPreparCardTokenForZBApiController.class);

    @Reference(version = "0.0.1")
    private MemberPrepayCardForZBApi memberPrepayCardForZBApi;

    @ApiOperation(value = "获取会员储值token", notes = "获取会员储值token", response = String.class, tags = {"会员储值总部端",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = String.class)})
    @RequestMapping(value = "/get", method = RequestMethod.POST, produces = "application/json", consumes = "application/json")
    ResponseEntity<ResultVO> get(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                 @RequestBody MemberPrepayCardDetailQueryVo vo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        MemberPrepayCardTokenDto dto = new MemberPrepayCardTokenDto();
        dto.setMemberGuid(vo.getMemberGuid());
        return new ResponseEntity(memberPrepayCardForZBApi.getTokenForZB(dto), HttpStatus.OK);
    }
}
