package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.member.core.api.MemberChronicPatientApi;
import com.xyy.saas.member.core.api.MemberChronicRelationApi;
import com.xyy.saas.member.core.dto.MemberChronicPatientDto;
import com.xyy.saas.member.core.dto.MemberChronicPatientQueryReqDto;
import com.xyy.saas.member.core.dto.MemberChronicRelationDto;
import com.xyy.saas.member.core.dto.MemberChronicVisitInfoDto;
import com.xyy.saas.web.api.module.member.model.MemberBase;
import com.xyy.saas.web.api.module.member.model.MemberChronicPatientQueryReqVo;
import com.xyy.saas.web.api.module.member.model.MemberChronicPatientVo;
import com.xyy.saas.web.api.module.utils.ExportExcelUtil;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import com.xyy.saas.member.core.response.ResultVO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.xyy.saas.web.api.module.utils.OptUtil.notEmpty;


/**
 * @Description 患者资料查询--患者即会员
 * <AUTHOR>
 * @Create 2020-10-12 20:15
 * @menu 患者资料API
 */
@Controller
@RequestMapping(value = "/member/memberChronicPatient")
@Api(value = "MemberChronicPatientAPI", description = "患者资料API")
public class MemberChronicPatientApiController {
    private static final Logger logger = LoggerFactory.getLogger(MemberChronicPatientApiController.class);

    @Reference(version = "0.0.1")
    private MemberChronicPatientApi memberChronicPatientApi;

    @Reference(version = "0.0.1")
    private MemberChronicRelationApi chronicRelationApi;

    /**
     * @param commonRequestModelStr 机构信息
     * @param queryVo 查询条件
     * @return
     */
    @ApiOperation(value = "门店分页查询患者资料", notes = "门店分页查询患者资料", response = MemberChronicPatientVo.class, tags={ "患者资料", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberChronicPatientVo.class) })
    @RequestMapping(value = "/queryMemberChronicPatientPage",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> queryMemberChronicPatientPage(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                                  @RequestBody MemberChronicPatientQueryReqVo queryVo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        logger.info("queryMemberChronicPatientPage->organSign:{},param:{}", commonRequestModel.getOrganSign(),JSON.toJSONString(queryVo));
        //组装Dto参数
        MemberChronicPatientQueryReqDto queryDto = new MemberChronicPatientQueryReqDto();
        BeanUtils.copyProperties(queryVo, queryDto);
        queryDto.setOrganSign(commonRequestModel.getOrganSign());
        queryDto.setHeadquartersOrganSign(commonRequestModel.getHeadquartersOrganSign());
        //分页参数校验
        if(queryDto.getPageNum() == null || queryDto.getPageSize() == null) {
            return new ResponseEntity(ResultVO.createError("分页参数不能为空"), HttpStatus.OK);
        }
        return new ResponseEntity(ResultVO.createSuccess(
                memberChronicPatientApi.queryMemberChronicPatientPage(queryDto)),
                HttpStatus.OK);
    }


    /**
     * @param commonRequestModelStr 机构信息
     * @param queryVo 查询条件
     * @return
     */
    @ApiOperation(value = "总部分页查询患者资料", notes = "总部分页查询患者资料", response = MemberChronicPatientVo.class, tags={ "患者资料", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberChronicPatientVo.class) })
    @RequestMapping(value = "/zb/queryMemberChronicPatientPage",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> queryMemberChronicPatientPageForZB(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                                       @RequestBody MemberChronicPatientQueryReqVo queryVo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        //组装Dto参数
        MemberChronicPatientQueryReqDto queryDto = new MemberChronicPatientQueryReqDto();
        BeanUtils.copyProperties(queryVo, queryDto);
        queryDto.setOrganSign(commonRequestModel.getOrganSign());
        //分页参数校验
        if(queryDto.getPageNum() == null || queryDto.getPageSize() == null) {
            return new ResponseEntity(ResultVO.createError("分页参数不能为空"), HttpStatus.OK);
        }
        return new ResponseEntity(ResultVO.createSuccess(
                memberChronicPatientApi.queryMemberChronicPatientPageForZB(queryDto)),
                HttpStatus.OK);
    }

    @ApiOperation(value = "POS端:根据Guid查找患者信息", notes = "根据Guid查找患者信息", response = MemberChronicPatientVo.class, tags = {"member",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberChronicPatientVo.class)})
    @RequestMapping(value = "/getPatientByMemberGuid", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getPatientByMemberGuid(@RequestHeader("commonRequestModel") String commonRequestModel,
                                                           @ApiParam(value = "会员基本信息", required = true) @RequestBody MemberChronicPatientVo patientVo) {
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        if (patientVo == null || StringUtils.isEmpty(patientVo.getGuid())) {
            return new ResponseEntity(new ResultVO(-1, "会员编号不能为空", false), HttpStatus.OK);
        }
        MemberChronicRelationDto patient = chronicRelationApi.selectChronicByGuid(patientVo.getGuid());
        if (patient != null ) {
            return new ResponseEntity(ResultVO.createSuccess(patient), HttpStatus.OK);
        } else {
            return new ResponseEntity(new ResultVO(-1, "查询失败", false), HttpStatus.OK);
        }
    }

    /**
     * @param commonRequestModelStr 机构信息
     * @param queryReqVo 查询条件
     * @return
     */
    @ApiOperation(value = "门店导出患者资料", notes = "门店导出患者资料", response = MemberChronicPatientVo.class, tags={ "患者资料", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberChronicPatientVo.class) })
    @RequestMapping(value = "/exportExcel",
            method = RequestMethod.POST)
    public void exportChronicPatientExcel(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                          @RequestBody MemberChronicPatientQueryReqVo queryReqVo, HttpServletRequest request, HttpServletResponse response) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        //组装Dto参数
        MemberChronicPatientQueryReqDto queryReqDto = new MemberChronicPatientQueryReqDto();
        BeanUtils.copyProperties(queryReqVo, queryReqDto);
        queryReqDto.setOrganSign(commonRequestModel.getOrganSign());
        queryReqDto.setHeadquartersOrganSign(commonRequestModel.getHeadquartersOrganSign());
        //暂时不对接电驴
        queryReqDto.setPageNum(1);
        queryReqDto.setPageSize(200);
        PageInfo pageInfo = memberChronicPatientApi.queryMemberChronicPatientPage(queryReqDto);
        List<MemberChronicPatientDto> dtoList = new ArrayList<>();
        if (pageInfo != null && notEmpty(pageInfo.getList())) {
            dtoList.addAll(pageInfo.getList());
            int firstPage = 1;
            int pages = pageInfo.getPages();
            /** 遍历查询数据 */
            while (firstPage < pages) {
                firstPage += 1;
                queryReqDto.setPageNum(firstPage);
                pageInfo  = memberChronicPatientApi.queryMemberChronicPatientPage(queryReqDto);
                dtoList.addAll(pageInfo.getList());
            }
        }
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String extfilename="患者资料"+df.format(new Date())+".xls";
        String sheetName = "患者资料";
        String headers[] = new String[]{"手机号","姓名","卡号","来源门店","性别","年龄","身份证号","关心病种","状态","创建日期",
                "身高","体重","血型","婚姻状况","过敏史","既往病史","健康需求","用药禁忌","常用药物","职业","电子邮件","所需地区","详细地址"};
        String fieldNames[] = new String[]{"telephone","name","cartNo","drugstoreName","sexName","age","idCard","chronicNames","stateName","createTimeStr",
                "height","weight","blood","marriage","historyOfAllergy","jiWangBingShi","jianKangXuQiu","yongYaoJinJi","changYongYao","profession","mailBox","provinceCityAreaName","address"};
        try {
            ExportExcelUtil.createExcel(response,request,extfilename,sheetName, headers, fieldNames, dtoList,true);
        } catch (Exception e) {
            logger.error("exportChronicPatientExcel exportRecordExcel is error.", e);
        }
    }

    /**
     * @param commonRequestModelStr 机构信息
     * @param queryReqVo 查询条件
     * @return
     */
    @ApiOperation(value = "总部导出患者资料", notes = "总部导出患者资料", response = MemberChronicPatientVo.class, tags={ "患者资料", })
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberChronicPatientVo.class) })
    @RequestMapping(value = "/zb/exportExcel",
            method = RequestMethod.POST)
    public void exportChronicPatientExcelForZB(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                               @RequestBody MemberChronicPatientQueryReqVo queryReqVo, HttpServletRequest request, HttpServletResponse response) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        //组装Dto参数
        MemberChronicPatientQueryReqDto queryReqDto = new MemberChronicPatientQueryReqDto();
        BeanUtils.copyProperties(queryReqVo, queryReqDto);
        queryReqDto.setOrganSign(commonRequestModel.getOrganSign());
        //待对接电驴
        //暂时不对接电驴
        queryReqDto.setPageNum(1);
        queryReqDto.setPageSize(200);
        PageInfo pageInfo = memberChronicPatientApi.queryMemberChronicPatientPageForZB(queryReqDto);
        List<MemberChronicPatientDto> dtoList = new ArrayList<>();
        if (pageInfo != null && notEmpty(pageInfo.getList())) {
            dtoList.addAll(pageInfo.getList());
            int firstPage = 1;
            int pages = pageInfo.getPages();
            /** 遍历查询数据 */
            while (firstPage < pages) {
                firstPage += 1;
                queryReqDto.setPageNum(firstPage);
                pageInfo  = memberChronicPatientApi.queryMemberChronicPatientPageForZB(queryReqDto);
                dtoList.addAll(pageInfo.getList());
            }
        }
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String extfilename="患者资料"+df.format(new Date())+".xls";
        String sheetName = "患者资料";
        String headers[] = new String[]{"手机号","姓名","卡号","来源门店","性别","年龄","身份证号","关心病种","状态","创建日期",
                "身高","体重","血型","婚姻状况","过敏史","既往病史","健康需求","用药禁忌","常用药物","职业","电子邮件","所需地区","详细地址"};
        String fieldNames[] = new String[]{"telephone","name","cartNo","drugstoreName","sexName","age","idCard","chronicNames","stateName","createTimeStr",
                "height","weight","blood","marriage","historyOfAllergy","jiWangBingShi","jianKangXuQiu","yongYaoJinJi","changYongYao","profession","mailBox","provinceCityAreaName","address"};
        try {
            ExportExcelUtil.createExcel(response,request,extfilename,sheetName, headers, fieldNames, dtoList,true);
        } catch (Exception e) {
            logger.error("exportChronicPatientExcelForZB exportRecordExcel is error.", e);
        }
    }




    //<editor-folder desc="Yapi接口文档">
//    /**
//     * @param commonRequestModelStr 机构信息
//     * @param queryVo 查询条件
//     * @return
//     */
//    @ApiOperation(value = "门店分页查询患者资料", notes = "门店分页查询患者资料", response = MemberChronicPatientVo.class, tags={ "患者资料", })
//    @ApiResponses(value = {
//            @ApiResponse(code = 200, message = "操作成功", response = MemberChronicPatientVo.class) })
//    @RequestMapping(value = "/queryMemberChronicPatientPage",
//            method = RequestMethod.POST)
//    public ResultVO<PageInfo<MemberChronicPatientVo>> queryMemberChronicPatientPage(@RequestHeader("commonRequestModel") String commonRequestModelStr,
//                                                                                    @RequestBody MemberChronicPatientQueryReqVo queryVo) {
//        return null;
//    }
//
//
//    /**
//     * @param commonRequestModelStr 机构信息
//     * @param queryVo 查询条件
//     * @return
//     */
//    @ApiOperation(value = "总部分页查询患者资料", notes = "总部分页查询患者资料", response = MemberChronicPatientVo.class, tags={ "患者资料", })
//    @ApiResponses(value = {
//            @ApiResponse(code = 200, message = "操作成功", response = MemberChronicPatientVo.class) })
//    @RequestMapping(value = "/zb/queryMemberChronicPatientPage",
//            method = RequestMethod.POST)
//    public ResultVO<PageInfo<MemberChronicPatientVo>> queryMemberChronicPatientPageForZB(@RequestHeader("commonRequestModel") String commonRequestModelStr,
//                                                                       @RequestBody MemberChronicPatientQueryReqVo queryVo) {
//        return null;
//    }
//
//    /**
//     * @param commonRequestModelStr 机构信息
//     * @param queryVo 查询条件
//     * @return
//     */
//    @ApiOperation(value = "门店导出患者资料", notes = "门店导出患者资料", response = MemberChronicPatientVo.class, tags={ "患者资料", })
//    @ApiResponses(value = {
//            @ApiResponse(code = 200, message = "操作成功", response = MemberChronicPatientVo.class) })
//    @RequestMapping(value = "/exportExcel",
//            method = RequestMethod.POST)
//    public void exportChronicWarnRecordExcel(@RequestHeader("commonRequestModel") String commonRequestModelStr,
//                                             @RequestBody MemberChronicPatientQueryReqVo queryVo) {
//    }
//
//    /**
//     * @param commonRequestModelStr 机构信息
//     * @param queryVo 查询条件
//     * @return
//     */
//    @ApiOperation(value = "总部导出患者资料", notes = "总部导出患者资料", response = MemberChronicPatientVo.class, tags={ "患者资料", })
//    @ApiResponses(value = {
//            @ApiResponse(code = 200, message = "操作成功", response = MemberChronicPatientVo.class) })
//    @RequestMapping(value = "/zb/exportExcel",
//            method = RequestMethod.POST)
//    public void exportChronicWarnRecordExcelForZB(@RequestHeader("commonRequestModel") String commonRequestModelStr,
//                                                  @RequestBody MemberChronicPatientQueryReqVo queryVo) {
//    }
    //</editor-folder>
}
