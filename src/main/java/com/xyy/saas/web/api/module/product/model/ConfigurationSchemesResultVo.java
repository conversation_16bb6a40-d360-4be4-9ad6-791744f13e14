package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName ConfigurationSchemesResultVo
 * @Description 配置方案，列表查询接口返回对象
 * <AUTHOR>
 * @Date 2020/8/19 18:33
 * @Version 1.0
 **/
@ApiModel(description = "配置方案，列表查询接口返回对象")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")
public class ConfigurationSchemesResultVo {

    @JsonProperty("organSign")
    private String organSign;//门店机构id
    @JsonProperty("organSignName")
    private String organSignName;//门店名称
    @JsonProperty("address")
    private String address;//门店地址
    @JsonProperty("adjustName")
    private String adjustName;//调价方案名称
    @JsonProperty("expirationTime")
    private String expirationTime;//方案有效期截止至
    @JsonProperty("configureTime")
    private String configureTime;//配置时间
    @JsonProperty("auditStateName")
    private String auditStateName;//审批状态名称
    @JsonProperty("auditState")
    private Byte auditState;//审核状态 1:审核中 2:同意 3:拒绝
    @JsonProperty("auditTime")
    private String auditTime;//审批时间
    @JsonProperty("toDoPersion")
    private String toDoPersion;//当前代办人
    @JsonProperty("businessKey")
    private String businessKey;//业务key
    @JsonProperty("taskId")
    private String taskId;//任务id
    @JsonProperty("businessScene")
    private String businessScene;//业务场景
    @JsonProperty("id")
    private Integer id;
    @ApiModelProperty(value = "方案编号")


    public String getAdjustPref() {
        return adjustPref;
    }

    public void setAdjustPref(String adjustPref) {
        this.adjustPref = adjustPref;
    }

    @JsonProperty("adjustPref")
    private String adjustPref;//方案编号

    @ApiModelProperty(value = "0，禁用，1启用")
    public Byte getDisable() {
        return disable;
    }

    public void setDisable(Byte disable) {
        this.disable = disable;
    }

    @ApiModelProperty(value = "过期状态：0否，1是")
    public Byte getExpirationStatus() {
        return expirationStatus;
    }

    public void setExpirationStatus(Byte expirationStatus) {
        this.expirationStatus = expirationStatus;
    }

    @JsonProperty("disable")
    private Byte disable;//0，禁用，1启用

    @JsonProperty("expirationStatus")
    private Byte expirationStatus;//过期状态：0否，1是

    @ApiModelProperty(value = "列表唯一键id,前端在用")
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @ApiModelProperty(value = "业务key")
    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    @ApiModelProperty(value = "任务id")
    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    @ApiModelProperty(value = "业务场景")
    public String getBusinessScene() {
        return businessScene;
    }

    public void setBusinessScene(String businessScene) {
        this.businessScene = businessScene;
    }

    @ApiModelProperty(value = "门店机构id")
    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    @ApiModelProperty(value = "门店名称")
    public String getOrganSignName() {
        return organSignName;
    }

    public void setOrganSignName(String organSignName) {
        this.organSignName = organSignName;
    }

    @ApiModelProperty(value = "门店地址")
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    @ApiModelProperty(value = "调价方案名称")
    public String getAdjustName() {
        return adjustName;
    }

    public void setAdjustName(String adjustName) {
        this.adjustName = adjustName;
    }

    @ApiModelProperty(value = "方案有效期截止至")
    public String getExpirationTime() {
        return expirationTime;
    }

    public void setExpirationTime(String expirationTime) {
        this.expirationTime = expirationTime;
    }

    @ApiModelProperty(value = "配置时间")
    public String getConfigureTime() {
        return configureTime;
    }

    public void setConfigureTime(String configureTime) {
        this.configureTime = configureTime;
    }

    @ApiModelProperty(value = "审批状态名称")
    public String getAuditStateName() {
        return auditStateName;
    }

    public void setAuditStateName(String auditStateName) {
        this.auditStateName = auditStateName;
    }

    @ApiModelProperty(value = "审核状态 1:审核中 2:同意 3:拒绝")
    public Byte getAuditState() {
        return auditState;
    }

    public void setAuditState(Byte auditState) {
        this.auditState = auditState;
    }

    @ApiModelProperty(value = "审批时间")
    public String getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(String auditTime) {
        this.auditTime = auditTime;
    }

    @ApiModelProperty(value = "当前代办人")
    public String getToDoPersion() {
        return toDoPersion;
    }

    public void setToDoPersion(String toDoPersion) {
        this.toDoPersion = toDoPersion;
    }
}
