package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * InventoryLotnumAdjustDetailVo
 */
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-29T17:04:23.698+08:00")

public class InventoryLotnumAdjustDetailVo   implements Serializable{
    private static final long serialVersionUID = 3158111339711007620L;
  @JsonProperty("id")
  private Long id = null;

  @JsonProperty("adjustPref")
  private String adjustPref = null;

  @JsonProperty("createUser")
  private String createUser = null;

  @JsonProperty("createTime")
  private Date createTime = null;

  @JsonProperty("updateUser")
  private String updateUser = null;

  @JsonProperty("updateTime")
  private Date updateTime = null;

  @JsonProperty("yn")
  private byte yn = 1;

  @JsonProperty("baseVersion")
  private String baseVersion = null;

  @JsonProperty("organsign")
  private String organsign = null;

  @JsonProperty("productPref")
  private String productPref = null;

  @JsonProperty("inLotNum")
  private String inLotNum = null;

  @JsonProperty("outLotNum")
  private String outLotNum = null;

  @JsonProperty("quantity")
  private BigDecimal quantity = null;

  @JsonProperty("inLotStatus")
  private BigDecimal inLotStatus = null;

  @JsonProperty("outLotStatus")
  private BigDecimal outLotStatus = null;

  public InventoryLotnumAdjustDetailVo id(Long id) {
    this.id = id;
    return this;
  }

   /**
   * 主键id
   * @return id
  **/
  @ApiModelProperty(value = "主键id")


  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public InventoryLotnumAdjustDetailVo adjustPref(String adjustPref) {
    this.adjustPref = adjustPref;
    return this;
  }

   /**
   * 调整表编号
   * @return adjustPref
  **/
  @ApiModelProperty(value = "调整表编号")


  public String getAdjustPref() {
    return adjustPref;
  }

  public void setAdjustPref(String adjustPref) {
    this.adjustPref = adjustPref;
  }

  public InventoryLotnumAdjustDetailVo createUser(String createUser) {
    this.createUser = createUser;
    return this;
  }

   /**
   * 创建人
   * @return createUser
  **/
  @ApiModelProperty(value = "创建人")


  public String getCreateUser() {
    return createUser;
  }

  public void setCreateUser(String createUser) {
    this.createUser = createUser;
  }

  public InventoryLotnumAdjustDetailVo createTime(Date createTime) {
    this.createTime = createTime;
    return this;
  }

   /**
   * 创建时间
   * @return createTime
  **/
  @ApiModelProperty(value = "创建时间")

  @Valid

  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public InventoryLotnumAdjustDetailVo updateUser(String updateUser) {
    this.updateUser = updateUser;
    return this;
  }

   /**
   * 更新人
   * @return updateUser
  **/
  @ApiModelProperty(value = "更新人")


  public String getUpdateUser() {
    return updateUser;
  }

  public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
  }

  public InventoryLotnumAdjustDetailVo updateTime(Date updateTime) {
    this.updateTime = updateTime;
    return this;
  }

   /**
   * 更新时间
   * @return updateTime
  **/
  @ApiModelProperty(value = "更新时间")

  @Valid

  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public InventoryLotnumAdjustDetailVo yn(byte yn) {
    this.yn = yn;
    return this;
  }

   /**
   * 是否删除
   * @return yn
  **/
  @ApiModelProperty(value = "是否删除")

  public byte getYn() {
    return yn;
  }

  public void setYn(byte yn) {
    this.yn = yn;
  }

  public InventoryLotnumAdjustDetailVo baseVersion(String baseVersion) {
    this.baseVersion = baseVersion;
    return this;
  }

   /**
   * 版本号
   * @return baseVersion
  **/
  @ApiModelProperty(value = "版本号")


  public String getBaseVersion() {
    return baseVersion;
  }

  public void setBaseVersion(String baseVersion) {
    this.baseVersion = baseVersion;
  }

  public InventoryLotnumAdjustDetailVo organsign(String organsign) {
    this.organsign = organsign;
    return this;
  }

   /**
   * 机构代码
   * @return organsign
  **/
  @ApiModelProperty(value = "机构代码")


  public String getOrgansign() {
    return organsign;
  }

  public void setOrgansign(String organsign) {
    this.organsign = organsign;
  }

  public InventoryLotnumAdjustDetailVo productPref(String productPref) {
    this.productPref = productPref;
    return this;
  }

   /**
   * 商品编号
   * @return productPref
  **/
  @ApiModelProperty(value = "商品编号")


  public String getProductPref() {
    return productPref;
  }

  public void setProductPref(String productPref) {
    this.productPref = productPref;
  }

  public InventoryLotnumAdjustDetailVo inLotNum(String inLotNum) {
    this.inLotNum = inLotNum;
    return this;
  }

   /**
   * 调入批号
   * @return inLotNum
  **/
  @ApiModelProperty(value = "调入批号")


  public String getInLotNum() {
    return inLotNum;
  }

  public void setInLotNum(String inLotNum) {
    this.inLotNum = inLotNum;
  }

  public InventoryLotnumAdjustDetailVo outLotNum(String outLotNum) {
    this.outLotNum = outLotNum;
    return this;
  }

   /**
   * 调出批号
   * @return outLotNum
  **/
  @ApiModelProperty(value = "调出批号")


  public String getOutLotNum() {
    return outLotNum;
  }

  public void setOutLotNum(String outLotNum) {
    this.outLotNum = outLotNum;
  }

  public InventoryLotnumAdjustDetailVo quantity(BigDecimal quantity) {
    this.quantity = quantity;
    return this;
  }

   /**
   * 调整数量
   * @return quantity
  **/
  @ApiModelProperty(value = "调整数量")

  @Valid

  public BigDecimal getQuantity() {
    return quantity;
  }

  public void setQuantity(BigDecimal quantity) {
    this.quantity = quantity;
  }

  public InventoryLotnumAdjustDetailVo inLotStatus(BigDecimal inLotStatus) {
    this.inLotStatus = inLotStatus;
    return this;
  }

   /**
   * 调入批号状态
   * @return inLotStatus
  **/
  @ApiModelProperty(value = "调入批号状态")

  @Valid

  public BigDecimal getInLotStatus() {
    return inLotStatus;
  }

  public void setInLotStatus(BigDecimal inLotStatus) {
    this.inLotStatus = inLotStatus;
  }

  public InventoryLotnumAdjustDetailVo outLotStatus(BigDecimal outLotStatus) {
    this.outLotStatus = outLotStatus;
    return this;
  }

   /**
   * 调出批号状态
   * @return outLotStatus
  **/
  @ApiModelProperty(value = "调出批号状态")

  @Valid

  public BigDecimal getOutLotStatus() {
    return outLotStatus;
  }

  public void setOutLotStatus(BigDecimal outLotStatus) {
    this.outLotStatus = outLotStatus;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InventoryLotnumAdjustDetailVo inventoryLotnumAdjustDetailVo = (InventoryLotnumAdjustDetailVo) o;
    return Objects.equals(this.id, inventoryLotnumAdjustDetailVo.id) &&
        Objects.equals(this.adjustPref, inventoryLotnumAdjustDetailVo.adjustPref) &&
        Objects.equals(this.createUser, inventoryLotnumAdjustDetailVo.createUser) &&
        Objects.equals(this.createTime, inventoryLotnumAdjustDetailVo.createTime) &&
        Objects.equals(this.updateUser, inventoryLotnumAdjustDetailVo.updateUser) &&
        Objects.equals(this.updateTime, inventoryLotnumAdjustDetailVo.updateTime) &&
        Objects.equals(this.yn, inventoryLotnumAdjustDetailVo.yn) &&
        Objects.equals(this.baseVersion, inventoryLotnumAdjustDetailVo.baseVersion) &&
        Objects.equals(this.organsign, inventoryLotnumAdjustDetailVo.organsign) &&
        Objects.equals(this.productPref, inventoryLotnumAdjustDetailVo.productPref) &&
        Objects.equals(this.inLotNum, inventoryLotnumAdjustDetailVo.inLotNum) &&
        Objects.equals(this.outLotNum, inventoryLotnumAdjustDetailVo.outLotNum) &&
        Objects.equals(this.quantity, inventoryLotnumAdjustDetailVo.quantity) &&
        Objects.equals(this.inLotStatus, inventoryLotnumAdjustDetailVo.inLotStatus) &&
        Objects.equals(this.outLotStatus, inventoryLotnumAdjustDetailVo.outLotStatus);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, adjustPref, createUser, createTime, updateUser, updateTime, yn, baseVersion, organsign, productPref, inLotNum, outLotNum, quantity, inLotStatus, outLotStatus);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InventoryLotnumAdjustDetailVo {\n");

    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    adjustPref: ").append(toIndentedString(adjustPref)).append("\n");
    sb.append("    createUser: ").append(toIndentedString(createUser)).append("\n");
    sb.append("    createTime: ").append(toIndentedString(createTime)).append("\n");
    sb.append("    updateUser: ").append(toIndentedString(updateUser)).append("\n");
    sb.append("    updateTime: ").append(toIndentedString(updateTime)).append("\n");
    sb.append("    yn: ").append(toIndentedString(yn)).append("\n");
    sb.append("    baseVersion: ").append(toIndentedString(baseVersion)).append("\n");
    sb.append("    organsign: ").append(toIndentedString(organsign)).append("\n");
    sb.append("    productPref: ").append(toIndentedString(productPref)).append("\n");
    sb.append("    inLotNum: ").append(toIndentedString(inLotNum)).append("\n");
    sb.append("    outLotNum: ").append(toIndentedString(outLotNum)).append("\n");
    sb.append("    quantity: ").append(toIndentedString(quantity)).append("\n");
    sb.append("    inLotStatus: ").append(toIndentedString(inLotStatus)).append("\n");
    sb.append("    outLotStatus: ").append(toIndentedString(outLotStatus)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

