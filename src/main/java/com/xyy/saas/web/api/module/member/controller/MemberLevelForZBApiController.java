package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.member.core.api.MemberLevelForZBApi;
import com.xyy.saas.member.core.api.MemberPrepayCardConfigForZBApi;
import com.xyy.saas.member.core.api.MemberShareApi;
import com.xyy.saas.member.core.dto.*;
import com.xyy.saas.rabbitmq.core.api.MessagePushApi;
import com.xyy.saas.web.api.module.member.model.*;
import com.xyy.saas.web.api.module.member.utils.ResultCodeEnum;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/member/memberLevel/zb")
@Api(value = "会员等级（总部端）API", description = "会员等级（总部端）API")
public class MemberLevelForZBApiController {

	private static final Logger logger = LogManager.getLogger(MemberLevelForZBApiController.class);

    @Reference(version = "0.0.1")
    private MessagePushApi messagePushApi;

    @Reference(version = "0.0.1")
    private MemberPrepayCardConfigForZBApi memberPrepayCardConfigForZBApi;

    @Reference(version = "0.0.1")
    private MemberLevelForZBApi memberLevelForZBApi;

    @Reference(version = "0.0.1")
    private MemberShareApi memberShareApi;

    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;

//-----------------------------------------------------总部端：改造会员等级--------------------------------------------------------------------------------------

    @ApiOperation(value = "总部端:查询等级列表", notes = "总部端:查询等级列表", response = MemberLevel.class, tags={ "memberLevel", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberLevel.class) })
    @RequestMapping(value = "/query")
    public ResponseEntity<ResultVO> query(@RequestHeader("commonRequestModel") String commonRequestModel,
                                            @RequestBody MemberLevelVo memberLevelVo){
        MemberLevelDto memberLevelDto = new MemberLevelDto();
            //没有传来源门店的取登录门店数据
        if (StringUtils.isEmpty(memberLevelVo.getOrgansign())) {
            memberLevelDto.setOrgansign(JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class).getOrganSign());
        }else{
            memberLevelDto.setOrgansign(memberLevelVo.getOrgansign());
        }

        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(10);
        PageInfo<MemberLevelDto> resultList = memberLevelForZBApi.getMemberLevelListPagerZB(pageInfo, memberLevelDto);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(resultList),HttpStatus.OK);
    }

    @ApiOperation(value = "总部端:查询等级列表（根据来源门店）", notes = "总部端:查询等级列表（根据来源门店）", response = MemberLevel.class, tags={ "memberLevel", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberLevel.class) })
    @RequestMapping(value = "/queryByStore")
    public ResponseEntity<ResultVO> queryByStore(@RequestHeader("commonRequestModel") String commonRequestModel,
                                                 @RequestBody MemberLevelVo memberLevelVo){
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        MemberLevelDto memberLevelDto = new MemberLevelDto();
        BeanUtils.copyProperties(memberLevelVo, memberLevelDto);
        if (StringUtils.isEmpty(memberLevelDto.getOrgansign())){
            //没有传来源门店的取登录门店数据
            memberLevelDto.setOrgansign(model.getOrganSign());
        }else {
            SaaSDrugstoreDto drugstoreDto = drugstoreApi.getDrugstoreByOrganSign(memberLevelDto.getOrgansign());
            if (drugstoreDto.getBizModel() != 1 && drugstoreDto.getOrganSignType() != 3) {
                //来源门店是联营/连锁门店
                List<MemberShareDto> shareRel = memberShareApi.selectSharedList(drugstoreDto.getHeadquartersOrganSign()).getResult();
                List<String> shares = shareRel.stream().map(s -> s.getOrganSign()).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(shares) && shares.contains(memberLevelDto.getOrgansign())){
                    //共享门店取总部
                    memberLevelDto.setOrgansign(drugstoreDto.getHeadquartersOrganSign());
                }
            }
        }
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(10);
        PageInfo<MemberLevelDto> resultList = memberLevelForZBApi.getMemberLevelListPagerZB(pageInfo, memberLevelDto);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(resultList),HttpStatus.OK);
    }

    @ApiOperation(value = "总部端:查询等级列表(把总部下不共享会员的门店的等级合并成“非共享会员等级)", notes = "总部端:查询等级列表(把总部下不共享会员的门店的等级合并成“非共享会员等级)", response = MemberLevel.class, tags={ "memberLevelContainNOShare", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberLevelDto.class) })
    @RequestMapping(value = "/queryContainNoShare")
    public ResponseEntity<ResultVO> queryContainNoShare(@RequestHeader("commonRequestModel") String commonRequestModel){
        CommonRequestModel model = JSONUtils.json2Obj(commonRequestModel, CommonRequestModel.class);
        String organSign = model.getOrganSign();
        MemberLevelDto memberLevelDto = new MemberLevelDto();
        memberLevelDto.setOrgansign(organSign);
        List<MemberLevelDto> list = memberLevelForZBApi.getMemberLevelContainNoShareZB(memberLevelDto);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(list),HttpStatus.OK);
    }

    @ApiOperation(value = "总部端:获取各个等级的人数", notes = "获取各个等级的人数", response = MemberLevel.class, tags={ "memberLevel", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberLevel.class) })
    @RequestMapping(value = "/num", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> queryMemberLevelNum(@RequestHeader("commonRequestModel") String commonRequestModel) {
        CommonRequestModel model = JSONObject.parseObject(commonRequestModel, CommonRequestModel.class);
        String organSign = model.getOrganSign();
        MemberLevelDto memberLevelDto = new MemberLevelDto();
        memberLevelDto.setOrgansign(organSign);
        List<MemberLevelDto> list = memberLevelForZBApi.queryMemberLevelNumZB(memberLevelDto);
        return new ResponseEntity<ResultVO>(ResultVO.createSuccess(list), HttpStatus.OK);
    }


    @ApiOperation(value = "总部端:启用,编辑会员等级", notes = "总部端:启用,编辑会员等级", response = MemberLevel.class, tags={ "memberLevel", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberLevel.class) })
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> enableMemberLevel(@RequestHeader("commonRequestModel") String commonRequestModel,@RequestBody MemberLevelVo memberLevelVo) {
        CommonRequestModel model = JSONObject.parseObject(commonRequestModel, CommonRequestModel.class);
        String organSign = model.getOrganSign();
        String employeeId = model.getEmployeeId();
        logger.info("总部端:会员等级CommonRequestModel:" + JSONUtils.obj2JSON(model));
        if (StringUtils.isEmpty(memberLevelVo.getName())) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.NO_LEVEL_NAME.getCode(),ResultCodeEnum.NO_LEVEL_NAME.getMsg(), null), HttpStatus.OK);

        }
        if (StringUtils.isEmpty(memberLevelVo.getDiscount())) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.NO_DISCOUNT.getCode(),ResultCodeEnum.NO_DISCOUNT.getMsg(), null), HttpStatus.OK);
        }
        if (StringUtils.isEmpty(memberLevelVo.getNeedPoint())) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.NO_NEED_POINT.getCode(),ResultCodeEnum.NO_NEED_POINT.getMsg(), null), HttpStatus.OK);
        }
        if (StringUtils.isEmpty(memberLevelVo.getPriceStrategy())) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.NOPRICE_STRATEGY.getCode(),ResultCodeEnum.NOPRICE_STRATEGY.getMsg(), null), HttpStatus.OK);
        }
        MemberLevelDto memberLevelDto = new MemberLevelDto();
        BeanUtils.copyProperties(memberLevelVo, memberLevelDto);
        memberLevelDto.setOrgansign(organSign);
        memberLevelDto.setUpPoint(BigDecimal.ZERO);
        boolean flag = false;
        try {
            Integer compare = memberLevelForZBApi.compareMemberLevelPointZB(memberLevelDto);
            if (2 == compare) {
                return new ResponseEntity(new ResultVO(-1, "当前等级所需积分不能比上级等级对应的所需积分高", flag), HttpStatus.OK);
            }
            if (3 == compare) {
                return new ResponseEntity(new ResultVO(-1, "当前等级所需积分不能比下级等级对应的所需积分低", flag), HttpStatus.OK);
            }
            //会员储值集合
            MemberPrepayCardConfigSaveVo configVo = memberLevelVo.getMemberPrepayCardConfigSaveVo();
            if (configVo != null) {
                List<MemberPrepayCardConfigVo> list = configVo.getList();
                List<MemberPrepayCardConfigDto> copyList = new ArrayList<>();
                if (list != null && list.size() > 0) {
                    MemberPrepayCardConfigDto dto;
                    for (MemberPrepayCardConfigVo item : list) {
                        dto = new MemberPrepayCardConfigDto();
                        BeanUtils.copyProperties(item, dto);
                        dto.setOrgansign(organSign);
                        if (dto.getId() == null) {
                            dto.setCreateUser(employeeId);
                        } else {
                            dto.setUpdateUser(employeeId);
                        }
                        copyList.add(dto);
                    }
                    com.xyy.saas.member.core.response.ResultVO<Boolean> resultVO = memberPrepayCardConfigForZBApi.checkConfigDataForZB(copyList);
                    if (!resultVO.getResult()) {
                        return new ResponseEntity(new ResultVO(-1, "会员储值设置区间不能重叠，下限应该小于上限", false), HttpStatus.OK);
                    }
                }
                MemberPrepayCardConfigSaveDto saveDto = new MemberPrepayCardConfigSaveDto();
                saveDto.setList(copyList);
                saveDto.setDeleteIds(configVo.getDeleteIds());
                saveDto.setLevelIds(configVo.getLevelIds());
                saveDto.setEmployeeId(employeeId);
                memberLevelDto.setMemberPrepayCardConfigSaveDto(saveDto);
            }
            //去掉会员日关系--替换为：添加积分规则
            MemberPointExchange memberPointExchange = memberLevelVo.getMemberPointExchange();
            if (memberPointExchange != null && memberPointExchange.getConsumePrice()!=null && memberPointExchange.getPoint()!=null) {
                if(memberPointExchange.getYn()==null || memberPointExchange.getYn()==1){
                    if(StringUtils.isEmpty(memberPointExchange.getExchangeType())){
                        return new ResponseEntity<ResultVO>(new ResultVO( ResultCodeEnum.NO_EXCHANGE_TYPE.getCode(),ResultCodeEnum.NO_EXCHANGE_TYPE.getMsg() ,null),HttpStatus.OK);
                    }
                    if(memberPointExchange.getPoint() == null){
                        return new ResponseEntity<ResultVO>(new ResultVO( ResultCodeEnum.NO_POINT.getCode(),ResultCodeEnum.NO_POINT.getMsg(),null),HttpStatus.OK);
                    }
                    if(StringUtils.isEmpty(memberPointExchange.getConsumePrice())){
                        return new ResponseEntity<ResultVO>(new ResultVO( ResultCodeEnum.NO_CONSUME_PRICE.getCode(),ResultCodeEnum.NO_CONSUME_PRICE.getMsg(),null),HttpStatus.OK);
                    }
                }
                MemberPointExchangeDto memberPointExchangeDto = new MemberPointExchangeDto();
                BeanUtils.copyProperties(memberPointExchange,memberPointExchangeDto);
                memberPointExchangeDto.setOrgansign(organSign);
                if (memberPointExchangeDto.getId() != null && memberPointExchangeDto.getId() != 0) {
                    memberPointExchangeDto.setUpdateUser(employeeId);
                } else {
                    memberPointExchangeDto.setCreateUser(employeeId);
                }
                memberLevelDto.setMemberPointExchangeDto(memberPointExchangeDto);
            }
            boolean level;
            if (memberLevelDto.getId() != null) {
                memberLevelDto.setUpdateUser(employeeId);
                level = memberLevelForZBApi.updateMemberLevelZB(memberLevelDto);
            } else {
                memberLevelDto.setCreateUser(employeeId);
                memberLevelDto.setStatus(1);
                memberLevelDto.setYn(1);
                level = memberLevelForZBApi.saveMemberLevelZB(memberLevelDto);
            }
            if(level){
                return new ResponseEntity<ResultVO>(new ResultVO(0, "操作成功", true), HttpStatus.OK);
            }else{
                return new ResponseEntity<ResultVO>(new ResultVO(-1, "操作失败,请填写正确信息", flag), HttpStatus.OK);
            }
        } catch (Exception e) {
            logger.info("新增或修改会员级别失败" + e.getMessage());
            e.printStackTrace();
            return new ResponseEntity<ResultVO>(new ResultVO(-1, "操作失败,请填写正确信息", flag), HttpStatus.OK);
        }
    }

}
