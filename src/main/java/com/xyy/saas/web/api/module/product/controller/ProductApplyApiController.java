package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.api.SaasAreaApi;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.common.util.DateUtil;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.product.core.api.ProductTemporaryApi;
import com.xyy.saas.product.core.api.SnProductTemporaryApi;
import com.xyy.saas.product.core.dto.*;
import com.xyy.saas.web.api.common.filter.ParameterCheckServletRequestWrapper;
import com.xyy.saas.web.api.module.product.model.*;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.api.RoleApi;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import com.xyy.user.module.enums.DrugstoreBizModelEnum;
import com.xyy.user.module.enums.DrugstoreTypeEnum;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName ProductApplyApiController
 * @Description 商品提报实现类
 * <AUTHOR>
 * @Date 2020/10/29 10:36
 * @Version 1.0
 **/
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T15:22:37.401+08:00")
@Controller
public class ProductApplyApiController implements ProductApplyApi {

    private static final Logger logger = LoggerFactory.getLogger(ProductApplyApiController.class);

    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;

    //@Reference(version = "0.0.1",url = "localhost:22997")
    @Reference(version = "0.0.1")
    private com.xyy.saas.product.core.api.ProductApplyApi productApplyApi;

    @Reference(version = "0.0.1")
    private ProductTemporaryApi productTemporaryApi;

    //@Reference(version = "0.0.1",url = "localhost:22997")
    @Reference(version = "0.0.1")
    private SnProductTemporaryApi snProductTemporaryApi;

    @Reference(version = "0.0.1")
    private SaasAreaApi saasAreaApi;

    @Reference(version = "0.0.1")
    private RoleApi roleApi;

    @Override
    public ResponseEntity<ResultVO> proList(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody ProductApplyQueryVo queryVo) {
        String organSign = request.getHeader("organSign");
        String modelJson = request.getHeader("commonRequestModel");
        String headerOrganSign = "";
        Byte bizModel = null;
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            bizModel = model.getBizModel();
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                headerOrganSign = model.getHeadquartersOrganSign();
            }
        }
        ProductApplyQueryDto queryDto = new ProductApplyQueryDto();
        queryDto.setAuditState(StringUtils.isEmpty(queryVo.getAuditState())?null:new Byte(queryVo.getAuditState()));//审批状态
        queryDto.setProductAuditState(StringUtils.isEmpty(queryVo.getProductAuditState())?null:new Byte(queryVo.getProductAuditState()));//首营审批状态
        queryDto.setHeaderOrganSign(headerOrganSign);//总部机构号
        queryDto.setOrganSign(organSign);//门店机构号
        queryDto.setManufacturer(queryVo.getManufacturer());//生产厂商
        queryDto.setMixQuery(queryVo.getMixQuery());//混合查询条件
        PageInfo pageInfo = new PageInfo<>();
        Integer page = queryVo.getPage();
        Integer rows = queryVo.getRows();
        if ( null == page ) {
            page = 1;
        }
        if ( null == rows ) {
            rows = 50;
        }
        pageInfo.setPageNum(page);
        pageInfo.setPageSize(rows);
        ResultVO<PageInfo<ProductApplyDto>> resultVO = productApplyApi.mdProductApplyList(queryDto,pageInfo);
        PageInfo<ProductApplyDto> pageDtos = resultVO.getResult();
        PageInfo<ProductApplyVo> pageVos = new PageInfo<>();
        BeanUtils.copyProperties(pageDtos,pageVos);
        List<ProductApplyDto> proDtos = pageDtos.getList();
        List<ProductApplyVo> vos = new ArrayList<>();
        if(!CollectionUtils.isEmpty(proDtos)){
            for(ProductApplyDto dto:proDtos){
                ProductApplyVo vo = new ProductApplyVo();
                BeanUtils.copyProperties(dto,vo);
                vo.setCreateTime(DateUtil.parseDateToStr(dto.getCreateTime(),DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS));
                vos.add(vo);
            }
        }
        pageVos.setList(vos);
        return new ResponseEntity<ResultVO>(new ResultVO(pageVos),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> zBproList(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody ProductZbApplyQueryVo queryVo) {
        String organSign = request.getHeader("organSign");
        String userId = request.getHeader("employeeId");
        PageInfo pageInfo = new PageInfo<>();
        Integer page = queryVo.getPage();
        Integer rows = queryVo.getRows();
        if ( null == page ) {
            page = 1;
        }
        if ( null == rows ) {
            rows = 50;
        }
        pageInfo.setPageNum(page);
        pageInfo.setPageSize(rows);
        ProductZbApplyQueryDto queryDto = new ProductZbApplyQueryDto();
        queryDto.setAuditState(StringUtils.isEmpty(queryVo.getAuditState())?null:Byte.valueOf(queryVo.getAuditState()));//审批状态
        queryDto.setBusinessScope(StringUtils.isEmpty(queryVo.getBusinessScope())?null:Integer.valueOf(queryVo.getBusinessScope()));//经营范围
        queryDto.setChainStoreOrgansign(queryVo.getChainStoreOrgansign());//来源门店
        queryDto.setOrganSign(organSign);//总部机构号
        queryDto.setSystemType(StringUtils.isEmpty(queryVo.getSystemType())?null:Integer.valueOf(queryVo.getSystemType()));//商品分类
        queryDto.setMixQuery(queryVo.getMixQuery());//混合查询字段
        queryDto.setManufacturer(queryVo.getManufacturer());//生产厂商
        queryDto.setCreateTimeBegin(queryVo.getCreateTimeBegin());//开始时间
        queryDto.setCreateTimeEnd(queryVo.getCreateTimeEnd());//结束时间
        queryDto.setIsDrugIdentCode(StringUtils.isEmpty(queryVo.getIsDrugIdentCode())?null:Byte.valueOf(queryVo.getIsDrugIdentCode()));
        queryDto.setIsDrugstoreHidden(queryVo.getIsDrugstoreHidden());
        ResultVO<PageInfo<ProductApplyDto>> resultVO = productApplyApi.zbProductApplyList(queryDto, pageInfo);
        PageInfo<ProductApplyDto> dtoPageInfo = resultVO.getResult();
        PageInfo<ProductApplyZbVo> vosPageInfo = new PageInfo();
        BeanUtils.copyProperties(dtoPageInfo,vosPageInfo);
        List<ProductApplyDto> dtos = dtoPageInfo.getList();
        List<ProductApplyZbVo> vos = new ArrayList<>();
        if(!CollectionUtils.isEmpty(dtos)){
            ResultVO<List<Integer>> listResultVO = roleApi.queryRoleIdsByEmployeeId(Integer.valueOf(userId));//获取当前登录用户的权限列表
            List<Integer> currentRoleIds = listResultVO.getResult();
            for(ProductApplyDto dto:dtos){
                ProductApplyZbVo vo = new ProductApplyZbVo();
                BeanUtils.copyProperties(dto,vo);
                vo.setCreateTime(DateUtil.parseDateToStr(dto.getCreateTime(),DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS));
                if(!CollectionUtils.isEmpty(currentRoleIds)){
                    if(currentRoleIds.contains(dto.getTodoRoleId())){
                        vo.setCanApprove((byte)1);
                    }else{
                        vo.setCanApprove((byte)2);
                    }
                }else{
                    vo.setCanApprove((byte)2);//不可以审批
                }
                vos.add(vo);
            }
        }
        vosPageInfo.setList(vos);
        return new ResponseEntity<ResultVO>(new ResultVO(vosPageInfo),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> zBdtqProList(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody ProductZbDtqApplyQueryVo queryVo) {
        String organSign = request.getHeader("organSign");
        PageInfo pageInfo = new PageInfo<>();
        Integer page = queryVo.getPage();
        Integer rows = queryVo.getRows();
        if ( null == page ) {
            page = 1;
        }
        if ( null == rows ) {
            rows = 50;
        }
        pageInfo.setPageNum(page);
        pageInfo.setPageSize(rows);
        ProductZbDtqApplyQueryDto applyQueryDto = new ProductZbDtqApplyQueryDto();
        applyQueryDto.setOrganSign(organSign);//总部机构号
        applyQueryDto.setChainStoreOrgansign(queryVo.getChainStoreOrgansign());//来源门店
        applyQueryDto.setManufacturer(queryVo.getManufacturer());//生产厂商
        applyQueryDto.setMixQuery(queryVo.getMixQuery());//混合查询字段
        PageInfo<ProductApplyZbDtqVo> vosPageInfo = new PageInfo();
        try{
            applyQueryDto.setStandardLibraryId(StringUtils.isEmpty(queryVo.getStandardLibraryId())?null:Long.valueOf(queryVo.getStandardLibraryId()));//标准库id
        }catch (Exception e){
            return new ResponseEntity<ResultVO>(new ResultVO(vosPageInfo),HttpStatus.OK);
        }
        ResultVO<PageInfo<ProductApplyDto>> resultVO = productApplyApi.zbDtqProductApplyList(applyQueryDto , pageInfo);
        PageInfo<ProductApplyDto> dtoPageInfo = resultVO.getResult();
        BeanUtils.copyProperties(dtoPageInfo,vosPageInfo);
        List<ProductApplyDto> dtos = dtoPageInfo.getList();
        List<ProductApplyZbDtqVo> vos = new ArrayList<>();
        if(!CollectionUtils.isEmpty(dtos)){
            for(ProductApplyDto dto:dtos){
                ProductApplyZbDtqVo vo = new ProductApplyZbDtqVo();
                BeanUtils.copyProperties(dto,vo);
                vo.setRetailPrice(dto.getRetailPrice() == null ? null:String.valueOf(dto.getRetailPrice()));
                vo.setVipPrice(dto.getVipPrice() == null ? null:String.valueOf(dto.getVipPrice()));
                vo.setCreateTime(DateUtil.parseDateToStr(dto.getCreateTime(),DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS));
                vos.add(vo);
            }
        }
        vosPageInfo.setList(vos);
        return new ResponseEntity<ResultVO>(new ResultVO(vosPageInfo),HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> findAllOrganSigns(HttpServletRequest request) {
        String organSign = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        List<AdjustOrganSignVo> vos = new ArrayList<>();
        if(!StringUtils.isEmpty(organSign)){
            List<SaaSDrugstoreDto> drugs = new ArrayList<>();
            try{
                drugs = drugstoreApi.getDrugstoreByHeadquartersOrganSign(organSign);
            }catch (Exception e){
                logger.error("call drugstoreApi.getDrugstoreByHeadquartersOrganSign occur error,organSign:",organSign,e);
            }
            if(drugs != null && drugs.size() > 0){
                for(SaaSDrugstoreDto dto:drugs){
                    AdjustOrganSignVo vo = new AdjustOrganSignVo();
                    vo.setAdjustOrganSignName(dto.getDrugstoreName());
                    vo.setAdjustOrganSign(dto.getOrganSign());
                    vos.add(vo);
                }
            }
        }
        ResultVO result = new ResultVO(vos);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> findAllDtqProductNum(HttpServletRequest request) {
        String organSign = request.getHeader("organSign");
        ProductZbDtqApplyQueryDto ztqReq = new ProductZbDtqApplyQueryDto();
        ztqReq.setOrganSign(organSign);
        ResultVO<Integer> result = productApplyApi.zbDtqProductApplyCount(ztqReq);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> zuofei(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody ProductApplyZfVo queryVo) {
        String organSign = request.getHeader("organSign");
        String modelJson = request.getHeader("commonRequestModel");
        String headerOrganSign = "";
        Byte bizModel = null;
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            bizModel = model.getBizModel();
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                headerOrganSign = model.getHeadquartersOrganSign();
            }
        }
        ProductApplyReqDto ztqReq = new ProductApplyReqDto();
        ztqReq.setPref(queryVo.getPref());//提报单编号
        ztqReq.setHeaderOrganSign(organSign);
        return new ResponseEntity<ResultVO>(productApplyApi.zuofeiApplyProduct(ztqReq), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getDetailinfo(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody ProductApplyQueryDetailVo productQueryVo) {
        String organSign = request.getHeader("organSign");
        String modelJson = request.getHeader("commonRequestModel");
        String headerOrganSign = "";
        Byte bizModel = null;
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            bizModel = model.getBizModel();
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                headerOrganSign = model.getHeadquartersOrganSign();
            }
        }
        ProductApplyReqDto reqDto = new ProductApplyReqDto();
        if (StringUtils.isEmpty(headerOrganSign)){
            reqDto.setHeaderOrganSign(organSign);//总部机构号
        }else {
            reqDto.setHeaderOrganSign(headerOrganSign);//总部机构号
        }

        reqDto.setPref(productQueryVo.getPref());//提报单编号
        ResultVO<ProductApplyDetailDto> result = productApplyApi.getDetailByApplyPref(reqDto);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> saveOrUpdateProductApply(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody ProductApplyDetailDto productApplyDetailDto) {
        if (StringUtils.isEmpty(productApplyDetailDto)) {
            logger.error("==============参数不合法");
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, null),HttpStatus.OK);
        }
        ResultVO<Boolean> paramResult = productParamLengthCheck(productApplyDetailDto);
        if (!paramResult.getResult()){
            logger.error("==============参数不合法 "+paramResult.getMsg());
            return new ResponseEntity<ResultVO>(paramResult, HttpStatus.OK);
        }
        String username = request.getHeader("employeeId");
        String organSign = request.getHeader("organSign");
        String modelJson = request.getHeader("commonRequestModel");
        String headerOrganSign = "";
        Byte bizModel = null;
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            bizModel = model.getBizModel();
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                headerOrganSign = model.getHeadquartersOrganSign();
            }
        }
//        if(!StringUtils.isEmpty(productApplyDetailDto.getAreaCode())){
//            //产地
//            Map<String,String> areCodeMap = new HashMap<>();
//            Set<Integer> areCodes = new HashSet<>();
//            String[] areCodestrs = productApplyDetailDto.getAreaCode().split(",");
//            for(String str : areCodestrs){
//                if(!StringUtils.isEmpty(str)){
//                    areCodes.add(Integer.valueOf(str));
//                }
//            }
//            if (!CollectionUtils.isEmpty(areCodes)) {
//                XyySaasRegionParamsDto paramsDto = new XyySaasRegionParamsDto();
//                paramsDto.setAreaCodes(new ArrayList(areCodes));
//                List<SaasRegionBusinessDto> xyyRegionBusinessDtos = saasAreaApi.queryRegionByAreaCodeList(paramsDto);
//                areCodeMap = xyyRegionBusinessDtos.stream().collect((Collectors.toMap(p -> p.getAreaCode() + "", p -> p.getAreaName())));
//            }
//            StringBuffer sb = new StringBuffer("");
//            for(String str : areCodestrs){
//                if(!StringUtils.isEmpty(str)){
//                    sb.append(areCodeMap.get(str));
//                }
//            }
//            productApplyDetailDto.setProducingArea(sb.toString());
//        }
        productApplyDetailDto.setChainStoreOrgansign(organSign);//门店提报机构号
        productApplyDetailDto.setOrganSign(organSign);
        productApplyDetailDto.setHeaderOrganSign(headerOrganSign);//总部机构号
        productApplyDetailDto.setProductType(productApplyDetailDto.getProductType() == null ? 0 : productApplyDetailDto.getProductType()); //自定义分类
        productApplyDetailDto.setProductFunctionCatagory (productApplyDetailDto.getProductFunctionCatagory() == null ? 0 : productApplyDetailDto.getProductFunctionCatagory());// 功能分类
        ProductPrefDto productPrefDto = productApplyApi.productApplyAddOrUpdate(productApplyDetailDto, Integer.parseInt(username), bizModel,headerOrganSign);
        logger.info("addOrUpdate productbaseinfo result status:{}", productPrefDto.getStatus());
        if (productPrefDto.getStatus() > 0) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.SUCCESS, "save success"), HttpStatus.OK);
        } else if (-1 == productPrefDto.getStatus()) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "该标准库id对应的商品已被创建过，请重新核对后提交（变更）！",-1),HttpStatus.OK);
        }else if (-2 == productPrefDto.getStatus()) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "商品编号已存在，请重新输入",-2),HttpStatus.OK);
        }else if (-3 == productPrefDto.getStatus()) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "提交异常，请重新提交",-3),HttpStatus.OK);
        }else if (-4 == productPrefDto.getStatus()) {
            String msg = "商品库里已存在该商品（商品编号："+productPrefDto.getPharmacyPref()+"），请核对商品信息后重新提交！";
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, msg,-4),HttpStatus.OK);
        }  else if (-5 == productPrefDto.getStatus()) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "提交失败,审批流生成异常,请重新提交！", -5), HttpStatus.OK);
        } else if (-6 == productPrefDto.getStatus()) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, productPrefDto.getResultMsg(), -6), HttpStatus.OK);
        } else if (-7 == productPrefDto.getStatus()) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "商品名称、通用名、条形码过长！", -7), HttpStatus.OK);
        }else if (-8 == productPrefDto.getStatus()) {
            String msg = "该商品已被提报过（商品编号："+productPrefDto.getPharmacyPref()+"），请联系总部人员！";
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, msg,-8),HttpStatus.OK);
        }else if (-9 == productPrefDto.getStatus()) {
            String msg = "该标准库id已被提报过，请联系总部人员！";
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, msg,-9),HttpStatus.OK);
        }else {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "发生未知错误，需要进行排查"),HttpStatus.OK);
        }
    }

    @Override
    public ResponseEntity<ResultVO> saveOrUpdateProductTemporary(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody ProductDto productApplyDetailDto) {
        logger.info("saveOrUpdateProductTemporary pram:{}",JSON.toJSONString(productApplyDetailDto));
        String username = request.getHeader("employeeId");
        String organSign = request.getHeader("organSign");
        String modelJson = request.getHeader("commonRequestModel");
//        if(!StringUtils.isEmpty(productApplyDetailDto.getAreaCode())){
//            //产地
//            Map<String,String> areCodeMap = new HashMap<>();
//            Set<Integer> areCodes = new HashSet<>();
//            String[] areCodestrs = productApplyDetailDto.getAreaCode().split(",");
//            for(String str : areCodestrs){
//                if(!StringUtils.isEmpty(str)){
//                    areCodes.add(Integer.valueOf(str));
//                }
//            }
//            if (!CollectionUtils.isEmpty(areCodes)) {
//                XyySaasRegionParamsDto paramsDto = new XyySaasRegionParamsDto();
//                paramsDto.setAreaCodes(new ArrayList(areCodes));
//                List<SaasRegionBusinessDto> xyyRegionBusinessDtos = saasAreaApi.queryRegionByAreaCodeList(paramsDto);
//                areCodeMap = xyyRegionBusinessDtos.stream().collect((Collectors.toMap(p -> p.getAreaCode() + "", p -> p.getAreaName())));
//            }
//            StringBuffer sb = new StringBuffer("");
//            for(String str : areCodestrs){
//                if(!StringUtils.isEmpty(str)){
//                    sb.append(areCodeMap.get(str));
//                }
//            }
//            productApplyDetailDto.setProducingArea(sb.toString());
//        }
        productApplyDetailDto.setOrganSign(organSign);
        productApplyDetailDto.setProductType(productApplyDetailDto.getProductType() == null ? 0 : productApplyDetailDto.getProductType()); //自定义分类
        productApplyDetailDto.setProductFunctionCatagory (productApplyDetailDto.getProductFunctionCatagory() == null ? 0 : productApplyDetailDto.getProductFunctionCatagory());// 功能分类
        ResultVO resultVO = productTemporaryApi.addOrUpdate(productApplyDetailDto, Integer.parseInt(username));
        logger.info("saveOrUpdateProductTemporary productbaseinfo result status:{}", resultVO.getResult());
        if ((Integer)resultVO.getResult() > 0) {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.SUCCESS, "save success"), HttpStatus.OK);
        }else {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR, "发生未知错误，需要进行排查"),HttpStatus.OK);
        }
    }

    @Override
    public ResponseEntity<ResultVO> getTemporaryDetailinfo(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody  ProductApplyQueryDetailVo productQueryVo) {
        logger.info("getTemporaryDetailinfo param:{}",JSON.toJSONString(productQueryVo));
        String organSign = request.getHeader("organSign");
        String modelJson = request.getHeader("commonRequestModel");
        ResultVO<ProductDto> result = productTemporaryApi.getProductByPref(productQueryVo.getPref(),organSign);
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> temporaryDelete(HttpServletRequest request,@ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody ProductApplyQueryDetailVo productQueryVo) {
        logger.info("getTemporaryDetailinfo param:{}",JSON.toJSONString(productQueryVo));
        String organSign = request.getHeader("organSign");
        String modelJson = request.getHeader("commonRequestModel");
        ResultVO<Integer> result = productTemporaryApi.delete(productQueryVo.getPref());
        return new ResponseEntity<ResultVO>(result, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getProductTemproryList(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody  ProductTemproryQueryVo queryVo) {
        long start_time0 = System.currentTimeMillis();
        String organSign = request.getHeader("organSign");
        String currentOrgan = organSign;
        String employee = request.getHeader("employeeId");
        logger.info("organSign:" + organSign + ",employee:" + employee);
        ProductDto productDto = new ProductDto();
        PageInfo pageInfo = new PageInfo();
        Integer page = null;
        Integer rows = null;
        if ( null == queryVo.getPage() ){
            page = 1;
        }else{
            page = queryVo.getPage();
        }

        if ( null == queryVo.getRows() ){
            rows = 10;
        }else{
            rows = queryVo.getRows();
        }
        pageInfo.setPageNum(page);
        pageInfo.setPageSize(rows);
        productDto.setPage(page);
        productDto.setRows(rows);
        //如果是连锁门店，需要获取总部的机构号
        String modelJson = request.getHeader("commonRequestModel");
        Byte bizModel = null;
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                productDto.setSelectOrganSign(organSign);//查询价格的机构号
                organSign = model.getHeadquartersOrganSign();
            }
            bizModel = model.getBizModel();
        }
        productDto.setOrganSign(organSign);
        productDto.setManufacturer(queryVo.getManufacturer());
        productDto.setProductName(queryVo.getMixQuery());
        productDto.setStandardLibraryId(StringUtils.isEmpty(queryVo.getStandardLibraryId())?null:Long.valueOf(queryVo.getStandardLibraryId()));
        logger.info("getProductTemproryList query product list user api need time:"+(System.currentTimeMillis() - start_time0)+"ms");
        long start_time = System.currentTimeMillis();
        ResultVO result = productTemporaryApi.selectPager(productDto,pageInfo,bizModel);
        logger.info("getProductTemproryList query product list api need time:"+(System.currentTimeMillis() - start_time)+"ms");
        return new ResponseEntity<ResultVO>(result,HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getSnProductTemproryList(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody  ProductTemproryQueryVo queryVo) {
        long start_time0 = System.currentTimeMillis();

        String organSign = request.getHeader("organSign"),currentOrgan = request.getHeader("organSign");
        String employee = request.getHeader("employeeId");
        logger.info("organSign:" + organSign + ",employee:" + employee);

        // 拼接分页参数
        Integer page = queryVo.getPage() == null ? 1 : queryVo.getPage();
        Integer rows = queryVo.getRows()  == null ? 10 : queryVo.getRows() ;
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(page );
        pageInfo.setPageSize(rows);
        ProductDto productDto = new ProductDto();
        productDto.setPage(page);
        productDto.setRows(rows);

        //如果是连锁门店，需要获取总部的机构号
        String modelJson = request.getHeader("commonRequestModel");
        Byte bizModel = null;
        if(!StringUtils.isEmpty(modelJson)){
            CommonRequestModel model =  JSONObject.toJavaObject(JSON.parseObject(modelJson), CommonRequestModel.class);
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(model.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(model.getOrganSignType())){
                productDto.setSelectOrganSign(organSign);//查询价格的机构号
                organSign = model.getHeadquartersOrganSign();
            }
            bizModel = model.getBizModel();
        }
        productDto.setOrganSign(organSign);
        productDto.setManufacturer(queryVo.getManufacturer());
        productDto.setMixedQuery(queryVo.getMixQuery());
        productDto.setStandardLibraryId(StringUtils.isEmpty(queryVo.getStandardLibraryId())?null:Long.valueOf(queryVo.getStandardLibraryId()));

        logger.info("getProductTemproryList query product list user api need time:"+(System.currentTimeMillis() - start_time0)+"ms");
        long start_time = System.currentTimeMillis();
        ResultVO result = snProductTemporaryApi.selectPager(productDto,pageInfo,bizModel);
        logger.info("getProductTemproryList query product list api need time:"+(System.currentTimeMillis() - start_time)+"ms");
        return new ResponseEntity<ResultVO>(result,HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getProductTemproryCount(HttpServletRequest request) {
        String organSign = request.getHeader("organSign");
        // 本地创建暂存商品数据
        ResultVO localTempProduct = productTemporaryApi.getProductTemproryCount(organSign);
        Integer localTemProductNum = (Integer) localTempProduct.getResult();

        // 神农同步暂存商品数据
        ResultVO snTempProduct = snProductTemporaryApi.getSnProductTempCount(organSign);
        Integer snTempProductNum = (Integer) snTempProduct.getResult();

        // 合计
        localTempProduct.setResult(localTemProductNum + snTempProductNum);
        return new ResponseEntity<ResultVO>(localTempProduct,HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getSnProductTemproryCount(HttpServletRequest request) {
        String organSign = request.getHeader("organSign");
        ResultVO resultVO = snProductTemporaryApi.getSnProductTempCount(organSign);
        return new ResponseEntity<ResultVO>(resultVO,HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ResultVO> getSnProductTemporary(HttpServletRequest request, @ApiParam(value = "商品详情信息对象", required = true) @Valid @RequestBody ProductApplyQueryDetailVo productQueryVo) {
        String organSign = request.getHeader("organSign");
        Long standardLibraryId = productQueryVo.getStandardLibraryId();
        ResultVO resultVO = snProductTemporaryApi.getProductByOrganSignAndStandardLibraryId(standardLibraryId,organSign);
        return new ResponseEntity<ResultVO>(resultVO,HttpStatus.OK);
    }

    public ResultVO<Boolean> productParamLengthCheck(ProductDto product){
        if (product.getCommonName()!=null&&product.getCommonName().length()>100){
            return new ResultVO(ResultCodeEnum.ERROR, "通用名称长度不可超过100个字符", false);
        }
        if (product.getProductName()!=null&&product.getProductName().length()>100){
            return new ResultVO(ResultCodeEnum.ERROR, "商品名称长度不可超过100个字符", false);
        }
        if (product.getMnemonicCode()!=null&&product.getMnemonicCode().length()>201){
            return new ResultVO(ResultCodeEnum.ERROR, "助记码长度不可超过201个字符", false);
        }
        if (product.getAttributeSpecification()!=null&&product.getAttributeSpecification().length()>100){
            return new ResultVO(ResultCodeEnum.ERROR, "规格/型号不可超过100个字符", false);
        }
        if (product.getUsageAndDosage()!=null&&product.getUsageAndDosage().length()>15000){
            return new ResultVO(ResultCodeEnum.ERROR, "用法用量不可超过15000个字符", false);
        }
        if (product.getPref()!=null&&product.getPref().length()>32){
            return new ResultVO(ResultCodeEnum.ERROR, "商品编号不可超过32个字符", false);
        }
        // 新增校验  修改不校验
        if (product.getId() == null && product.getPharmacyPref()!=null&&product.getPharmacyPref().length()>20){
            return new ResultVO(ResultCodeEnum.ERROR, "商品编号不可超过20个字符", false);
        }
        if (product.getManufacturer()!=null&&product.getManufacturer().length()>100){
            return new ResultVO(ResultCodeEnum.ERROR, "生产厂家不可超过100个字符", false);
        }
        if (product.getProducingArea()!=null&&product.getProducingArea().length()>100){
            return new ResultVO(ResultCodeEnum.ERROR, "产地不可超过100个字符", false);
        }
        if (product.getBarCode()!=null&&product.getBarCode().length()>100){
            return new ResultVO(ResultCodeEnum.ERROR, "条形码不可超过100个字符", false);
        }
        if (product.getApprovalNumber()!=null&&product.getApprovalNumber().length()>100){
            return new ResultVO(ResultCodeEnum.ERROR, "批准文号/备案/注册证号不可超过100个字符", false);
        }
        if (product.getDrugPermissionPerson()!=null&&product.getDrugPermissionPerson().length()>64){
            return new ResultVO(ResultCodeEnum.ERROR, "上市许可持有人不可超过64字符", false);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(product.getDrugIdentCode())){
            if (product.getDrugIdentCode().length() != 7){
                return new ResultVO(ResultCodeEnum.ERROR, "录入追溯码前7位或扫描追溯码", false);
            }
            try {
                Integer.valueOf(product.getDrugIdentCode());
            } catch (Exception e) {
                return new ResultVO(ResultCodeEnum.ERROR, "格式错误，仅支持数字", false);
            }
        }
        return new ResultVO(ResultCodeEnum.SUCCESS, "", true);
    }
}
