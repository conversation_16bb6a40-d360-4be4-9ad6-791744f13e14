package com.xyy.saas.web.api.module.product.model;

import io.swagger.annotations.ApiModel;

import java.io.Serializable;

/**
 * @ClassName ExportProductScatteredRuleVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/9/26 21:36
 * @Version 1.0
 **/
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-07T14:34:49.141+08:00")

@ApiModel(description = "商品配伍禁忌导出查询对象")
public class ExportProductScatteredRuleVo implements Serializable {
    private String productName;
    private String unitId;
    private String scatteredNumber;
    private String pharmacyPref;
    private String manufacturer;
    private String createTimeStr;
    private String createUserName;
    private String  drugPermissionPerson; //药品上市许可证持有人

    public String getDrugPermissionPerson() {
        return drugPermissionPerson;
    }

    public void setDrugPermissionPerson(String drugPermissionPerson) {
        this.drugPermissionPerson = drugPermissionPerson;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public String getScatteredNumber() {
        return scatteredNumber;
    }

    public void setScatteredNumber(String scatteredNumber) {
        this.scatteredNumber = scatteredNumber;
    }

    public String getPharmacyPref() {
        return pharmacyPref;
    }

    public void setPharmacyPref(String pharmacyPref) {
        this.pharmacyPref = pharmacyPref;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getCreateTimeStr() {
        return createTimeStr;
    }

    public void setCreateTimeStr(String createTimeStr) {
        this.createTimeStr = createTimeStr;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

}
