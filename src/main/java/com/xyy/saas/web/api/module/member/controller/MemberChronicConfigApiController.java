package com.xyy.saas.web.api.module.member.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xyy.saas.common.dto.CommonRequestModel;
import com.xyy.saas.member.core.api.MemberChronicConfigApi;
import com.xyy.saas.member.core.api.MemberChronicProductApi;
import com.xyy.saas.member.core.dto.*;
import com.xyy.saas.member.core.response.ResultVO;
import com.xyy.saas.product.core.enums.DrugstoreTypeProductEnum;
import com.xyy.saas.web.api.module.member.model.MemberChronicConfigQueryReqVo;
import com.xyy.saas.web.api.module.member.model.MemberChronicConfigVo;
import com.xyy.saas.web.api.module.member.model.MemberPosChronicConfigParamVo;
import com.xyy.saas.web.api.module.member.service.DrugstoreService;
import com.xyy.saas.web.api.module.product.util.DrugstoreTypeUtil;
import com.xyy.saas.web.api.module.utils.StringUtils;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

import static com.xyy.saas.web.api.module.utils.OptUtil.isEmpty;

/**
 * @Description 会员慢病种类API
 * <AUTHOR>
 * @Create 2020-09-24 16:15
 * @menu 会员慢病种类API
 */
@Slf4j
@Controller
@RequestMapping(value = "/member/memberChronicConfig")
@Api(value = "memberChronicConfig", description = "会员慢病种类API")
public class MemberChronicConfigApiController {

    @Reference(version = "0.0.1")
    private MemberChronicConfigApi memberChronicConfigApi;

    @Autowired
    private DrugstoreService drugstoreService;

    @Reference(version = "0.0.1")
    private MemberChronicProductApi memberChronicProductApi;

    /*
     * @param commonRequestModelStr 机构信息
     * @param memberChronicConfigVo 实体VO信息
     */
    @ApiOperation(value = "新增或编辑会员慢病种类", notes = "新增或编辑会员慢病种类", response = Boolean.class, tags = {"会员慢病种类",})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/saveOrUpdateChronicConfig",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> saveOrUpdateChronicConfig(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                              @RequestBody MemberChronicConfigVo memberChronicConfigVo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        MemberChronicConfigDto memberChronicConfigDto = new MemberChronicConfigDto();
        BeanUtils.copyProperties(memberChronicConfigVo, memberChronicConfigDto);
        memberChronicConfigDto.setOrganSign(commonRequestModel.getOrganSign());
        //当前操作机构(权限管控--单体、联营总部、连锁总部)
        ResponseEntity<ResultVO> operResponseEntity = checkChronicConfigOper(commonRequestModel.getOrganSign());
        if (operResponseEntity != null) {
            return operResponseEntity;
        }
        if(memberChronicConfigVo == null ){
            return new ResponseEntity(new ResultVO(-1, "参数不能为空", false), HttpStatus.OK);
        }
        if (isEmpty(memberChronicConfigVo.getChronicName())) {
            return new ResponseEntity(new ResultVO(-1, "慢病种类名称不能为空！", false), HttpStatus.OK);
        }
        MemberChronicConfigExistDto existDto = new MemberChronicConfigExistDto();
        existDto.setChronicName(memberChronicConfigDto.getChronicName());
        existDto.setId(memberChronicConfigDto.getId());
        existDto.setOrganSign(commonRequestModel.getOrganSign());
        boolean nameExist = memberChronicConfigApi.existChronicConfigName(existDto);
        if (nameExist) {
            if (memberChronicConfigDto.getId() == null) {
                return new ResponseEntity(new ResultVO(-1, "慢病种类(" + memberChronicConfigDto.getChronicName() + ")已存在,添加失败！", false), HttpStatus.OK);
            } else {
                return new ResponseEntity(new ResultVO(-1, "慢病种类(" + memberChronicConfigDto.getChronicName() + ")已存在,修改失败！", false), HttpStatus.OK);
            }
        }
        if (memberChronicConfigDto.getId() == null) { //新增
            memberChronicConfigDto.setCreateUser(commonRequestModel.getEmployeeId() != null ? Integer.valueOf(commonRequestModel.getEmployeeId()) : null);
        } else { //编辑
            memberChronicConfigDto.setUpdateUser(commonRequestModel.getEmployeeId() != null ? Integer.valueOf(commonRequestModel.getEmployeeId()) : null);
        }
        boolean chronicConfig = memberChronicConfigApi.saveOrUpdateChronicConfig(memberChronicConfigDto);
        if (chronicConfig) {
            return new ResponseEntity(new ResultVO(0, "创建成功", true), HttpStatus.OK);
        }
        return new ResponseEntity(new ResultVO(-1, "创建失败", false), HttpStatus.OK);
    }

    /**
     * 通过ID删除会员慢病种类
     * @param commonRequestModelStr
     * @param memberChronicConfigVo
     * @return
     */
    @ApiOperation(value = "通过ID删除会员慢病种类", notes = "通过ID删除会员慢病种类", response = Boolean.class, tags = {"会员慢病种类",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/deleteChronicConfigById", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> deleteChronicConfigById(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                            @RequestBody MemberChronicConfigVo memberChronicConfigVo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        //当前操作机构(权限管控--单体、联营总部、连锁总部)
        ResponseEntity<ResultVO> operResponseEntity = checkChronicConfigOper(commonRequestModel.getOrganSign());
        if (operResponseEntity != null) {
            return operResponseEntity;
        }
        if (memberChronicConfigVo == null) {
            return new ResponseEntity(new ResultVO(-1, "参数不能为空", false), HttpStatus.OK);
        }
        if (memberChronicConfigVo.getId() == null) {
            return new ResponseEntity(new ResultVO(-1, "参数id不能为空", false), HttpStatus.OK);
        }
        MemberChronicConfigDto memberChronicConfigDto = new MemberChronicConfigDto();
        memberChronicConfigDto.setOrganSign(commonRequestModel.getOrganSign());
        memberChronicConfigDto.setId(memberChronicConfigVo.getId());
        memberChronicConfigDto.setUpdateUser(Integer.parseInt(commonRequestModel.getEmployeeId()));
        com.xyy.saas.common.util.ResultVO resultVO = memberChronicConfigApi.deleteChronicConfigById(memberChronicConfigDto);
        return new ResponseEntity(resultVO, HttpStatus.OK);
    }

    /**
     * @param commonRequestModelStr 机构信息
     * @param queryVo               查询条件
     */
    @ApiOperation(value = "分页查询会员慢病种类", notes = "分页查询会员慢病种类", response = MemberChronicConfigVo.class, tags = {"会员慢病种类",})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberChronicConfigVo.class)})
    @RequestMapping(value = "/queryChronicConfigPage",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> queryChronicConfigPage(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                           @RequestBody MemberChronicConfigQueryReqVo queryVo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        if(queryVo == null ){
            return new ResponseEntity(new ResultVO(-1, "参数不能为空", false), HttpStatus.OK);
        }
        MemberChronicConfigQueryDto queryDto = new MemberChronicConfigQueryDto();
        queryDto.setName(queryVo.getChronicName());
        queryDto.setOrganSign(commonRequestModel.getOrganSign());
        queryDto.setPageSize(queryVo.getPageSize());
        queryDto.setPageNum(queryVo.getPageNum());
        return new ResponseEntity(ResultVO.createSuccess(memberChronicConfigApi.queryChronicConfigPage(queryDto)), HttpStatus.OK);
    }

    /**
     * 列表查询会员慢病种类--会员所属机构的慢病列表(会员选择关注的慢病的时候用)
     *
     * @param commonRequestModelStr 机构信息
     */
    @ApiOperation(value = "列表查询会员慢病种类", notes = "列表查询会员慢病种类", response = MemberChronicConfigVo.class, tags = {"会员慢病种类",})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberChronicConfigVo.class)})
    @RequestMapping(value = "/queryChronicConfigList",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> queryChronicConfigList(@RequestHeader("commonRequestModel") String commonRequestModelStr,
                                                           @RequestBody MemberChronicConfigQueryReqVo queryVo) {
        CommonRequestModel commonRequestModel = JSONObject.parseObject(commonRequestModelStr, CommonRequestModel.class);
        if(queryVo == null ){
            return new ResponseEntity(new ResultVO(-1, "参数不能为空", false), HttpStatus.OK);
        }
        MemberChronicConfigQueryDto queryDto = new MemberChronicConfigQueryDto();
        queryDto.setName(queryVo.getChronicName());
        queryDto.setOrganSign(commonRequestModel.getOrganSign());
        return new ResponseEntity(ResultVO.createSuccess(memberChronicConfigApi.queryChronicConfigList(queryDto)), HttpStatus.OK);
    }

    /**
     * pos端：提供病种列表
     *
     * @param queryVo 机构信息
     */
    @ApiOperation(value = "pos端：提供病种列表", notes = "pos端：提供病种列表", response = MemberChronicConfigVo.class, tags = {"pos端：提供病种列表",})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberChronicConfigVo.class)})
    @RequestMapping(value = "/pos/queryChronicConfigList",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> psoQueryChronicConfigList(@RequestBody MemberPosChronicConfigParamVo queryVo) {
        if(queryVo == null ){
            return new ResponseEntity(new ResultVO(-1, "参数不能为空", false), HttpStatus.OK);
        }
        if(StringUtils.isEmpty(queryVo.getOrganSign()) ){
            return new ResponseEntity(new ResultVO(-1, "机构号不能为空", false), HttpStatus.OK);
        }
        MemberChronicConfigQueryDto queryDto = new MemberChronicConfigQueryDto();
        queryDto.setOrganSign(queryVo.getOrganSign());
        return new ResponseEntity(ResultVO.createSuccess(memberChronicConfigApi.queryChronicConfigList(queryDto)), HttpStatus.OK);
    }

    /**
     * pos端：通过GuidList查询病种列表
     *
     * @param queryVo 机构信息
     */
    @ApiOperation(value = "pos端：通过GuidList查询病种列表", notes = "pos端：通过GuidList查询病种列表", response = MemberChronicConfigVo.class, tags = {"pos端：通过GuidList查询病种列表",})
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "操作成功", response = MemberChronicConfigVo.class)})
    @RequestMapping(value = "/pos/queryChronicConfigListByGuids",
            method = RequestMethod.POST)
    public ResponseEntity<ResultVO> queryChronicConfigListByGuids(@RequestBody MemberPosChronicConfigParamVo queryVo) {
        if(queryVo == null ){
            return new ResponseEntity(new ResultVO(-1, "参数不能为空", false), HttpStatus.OK);
        }
        if (queryVo.getGuidList() == null || queryVo.getGuidList().size() == 0) {
            return new ResponseEntity(new ResultVO(-1, "GuidList参数不能为空", false), HttpStatus.OK);
        }
        MemberChronicConfigQueryDto queryDto = new MemberChronicConfigQueryDto();
        queryDto.setGuidList(queryVo.getGuidList());
        return new ResponseEntity(ResultVO.createSuccess(memberChronicConfigApi.posQueryChronicConfigListByGuigs(queryDto)), HttpStatus.OK);
    }
    private ResponseEntity<ResultVO> checkChronicConfigOper(String organSign) {
        if (organSign == null) {
            return new ResponseEntity(new ResultVO(-1, "机构参数不能为空", false), HttpStatus.OK);
        }
        SaaSDrugstoreDto saaSDrugstoreDto = drugstoreService.getDrugstoreByOrganSign(organSign);
        if (saaSDrugstoreDto != null) {
            DrugstoreTypeProductEnum drugstoreTypeProductEnum = DrugstoreTypeUtil.isDrugstoreTypeEnum(saaSDrugstoreDto.getBizModel(), saaSDrugstoreDto.getOrganSignType());
            if (drugstoreTypeProductEnum != null && (drugstoreTypeProductEnum.getType() == DrugstoreTypeProductEnum.SINGLE_DRUGSTORE.getType()
                    || drugstoreTypeProductEnum.getType() == DrugstoreTypeProductEnum.CHAIN_HEADQUARTERS.getType()
                    || drugstoreTypeProductEnum.getType() == DrugstoreTypeProductEnum.JOIN_HEADQUARTERS.getType())
                    ) {
                //当前机构具备权限
            } else {
                //当前机构不具备操作权限
                return new ResponseEntity(new ResultVO(-1, "当前机构无法操作慢病种类", false), HttpStatus.OK);
            }
        } else {
            return new ResponseEntity(new ResultVO(-1, "当前机构不存在", false), HttpStatus.OK);
        }
        return null;
    }
}
