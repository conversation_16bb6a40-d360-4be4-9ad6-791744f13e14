package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName CostPriceCommonVo
 * @Description 调价方案通用返回信息类
 * <AUTHOR>
 * @Date 2020/8/19 11:04
 * @Version 1.0
 **/
@ApiModel(description = "调价方案通用返回信息类")
@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T21:07:46.794+08:00")
public class CostPriceCommonVo {


    @JsonProperty("pref")
    private String pref;//方案编号

    @JsonProperty("name")
    private String name;//方案名称

    @JsonProperty("productNum")
    private Integer productNum;//商品种类

    @JsonProperty("expirationTime")
    private String expirationTime;//截止有效期

    @JsonProperty("createUserName")
    private String createUserName;//创建人名称

    @JsonProperty("createUser")
    private String createUser;//创建人id

    @JsonProperty("createTime")
    private String createTime;//创建时间

    @JsonProperty("remarks")
    private String remarks;//备注

    @JsonProperty("showStatusName")
    private String showStatusName;//状态描述，：暂存，禁用，启用

    @JsonProperty("showStatus")
    private Byte showStatus;//状态值：1，暂存，2，禁用，3，启用

    @JsonProperty("expirationStatus")
    private Byte expirationStatus;//过期状态，-1：全部，0：否，1：是

    @JsonProperty("expirationStatusName")
    private String expirationStatusName;//过期状态描述，全部，否，是

    @ApiModelProperty(value = "方案编号")
    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    @ApiModelProperty(value = "方案名称")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @ApiModelProperty(value = "商品种类")
    public Integer getProductNum() {
        return productNum;
    }

    public void setProductNum(Integer productNum) {
        this.productNum = productNum;
    }

    @ApiModelProperty(value = "截止有效期")
    public String getExpirationTime() {
        return expirationTime;
    }

    public void setExpirationTime(String expirationTime) {
        this.expirationTime = expirationTime;
    }

    @ApiModelProperty(value = "创建人名称")
    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    @ApiModelProperty(value = "创建人id")
    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    @ApiModelProperty(value = "创建时间")
    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    @ApiModelProperty(value = "备注")
    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    @ApiModelProperty(value = "状态描述，：暂存，禁用，启用")
    public String getShowStatusName() {
        return showStatusName;
    }

    public void setShowStatusName(String showStatusName) {
        this.showStatusName = showStatusName;
    }

    @ApiModelProperty(value = "状态值：1，暂存，2，禁用，3，启用")
    public Byte getShowStatus() {
        return showStatus;
    }

    public void setShowStatus(Byte showStatus) {
        this.showStatus = showStatus;
    }

    @ApiModelProperty(value = "过期状态，-1：全部，0：否，1：是")
    public Byte getExpirationStatus() {
        return expirationStatus;
    }

    public void setExpirationStatus(Byte expirationStatus) {
        this.expirationStatus = expirationStatus;
    }

    @ApiModelProperty(value = "过期状态描述，全部，否，是")
    public String getExpirationStatusName() {
        return expirationStatusName;
    }

    public void setExpirationStatusName(String expirationStatusName) {
        this.expirationStatusName = expirationStatusName;
    }




}
