package com.xyy.saas.web.api.module.product.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.inventory.core.api.InventoryLotNumberApi;
import com.xyy.saas.web.api.module.product.model.InventoryLotNumberPageVo;
import com.xyy.saas.web.api.module.product.model.InventoryLotNumberVo;
import com.xyy.saas.web.api.module.product.model.ResultCodeEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-28T11:40:30.801+08:00")

@Controller
public class SaveOrUpdateInventoryLotNumberApiController implements SaveOrUpdateInventoryLotNumberApi {
    @Reference(version = "0.0.1")
    private InventoryLotNumberApi inventoryLotNumberApi;

    public ResponseEntity<ResultVO> saveOrUpdateInventoryLotNumber(/*@ApiParam(value = "批号库存Vo" ,required=true )*/ @RequestHeader("userName") String userName, @RequestHeader("organSign") String organSign, @Valid @RequestBody InventoryLotNumberPageVo inventoryLotNumberPageVo) {
        List<InventoryLotNumberVo> inventoryLotNumberVoList = inventoryLotNumberPageVo.getLotNumberVos();
        List<com.xyy.saas.inventory.core.dto.InventoryLotNumberVo> vos = new ArrayList<>();
        for(InventoryLotNumberVo ivo : inventoryLotNumberVoList){
            ivo.setOrgansign(organSign);
            com.xyy.saas.inventory.core.dto.InventoryLotNumberVo inventoryLotNumberVo = new com.xyy.saas.inventory.core.dto.InventoryLotNumberVo();
            BeanUtils.copyProperties(ivo,inventoryLotNumberVo);
            vos.add(inventoryLotNumberVo);
        }

        String s = inventoryLotNumberApi.saveOrUpdateInventoryLotnumbers(vos, userName);
        if(!s.isEmpty()){
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.ERROR), HttpStatus.INTERNAL_SERVER_ERROR);
        }else {
            return new ResponseEntity<ResultVO>(new ResultVO(ResultCodeEnum.SUCCESS), HttpStatus.OK);
        }



    }

}
