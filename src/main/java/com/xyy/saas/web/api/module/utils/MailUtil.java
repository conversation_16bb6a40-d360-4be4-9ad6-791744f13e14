/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.xyy.saas.web.api.module.utils;

import com.github.pagehelper.util.StringUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Configuration;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.mail.*;
import javax.mail.internet.*;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Properties;

/**
 * 邮件工具类 ClassName: MailUtil <br/>
 * date: 2016-1-5 下午3:06:22 <br/>
 *
 * <AUTHOR>
 * @version
 * @since JDK 1.7
 */
@Configuration
public class MailUtil {

    private static Properties props;
    private static final String smtpHost = "smtp.ybm100.com";
    private static final String mailBodyType = "text/html;charset=utf-8";
    private static final String userName = "<EMAIL>";
    private static final String password = "A5XkDdW4z85vfn2Q";
    private static final String dateformat = "yyyy-MM-dd HH:mm:ss";
    private StringBuilder bodytext = new StringBuilder();

    public static void sendMail(String address, String subject, String content)
            throws Exception {

        setProperties();// 设置属性

        Session session = Session.getDefaultInstance(props, null); // 获得邮件会话对象
        MimeMessage mimeMsg = new MimeMessage(session); // 创建MIME邮件对象
        mimeMsg.setSubject(subject + " " + new SimpleDateFormat(dateformat).format(new Date()));
        mimeMsg.setRecipients(Message.RecipientType.TO, InternetAddress
                .parse(address));
        mimeMsg.setFrom(new InternetAddress(userName));

        // 设置邮件体
        MimeBodyPart mbp = new MimeBodyPart();
        mbp.setContent(content, mailBodyType);

        MimeMultipart mp = new MimeMultipart();
        mp.addBodyPart(mbp);

        mimeMsg.setContent(mp);
        // 保存更改
        mimeMsg.saveChanges();

        // 发送邮件
        Transport transport = session.getTransport("smtp");
        transport.connect(userName, password);
        transport.sendMessage(mimeMsg, mimeMsg
                .getRecipients(Message.RecipientType.TO));
        transport.close();

    }

    /**
     * 发送html格式的邮件
     * @param address 收件人(以逗号分隔的字符串)
     * @param address2 抄送人
     * @param subject 主题
     * @param htmlText html内容
     * @throws Exception
     */
    public static void sendMail(String address,String address2, String subject, String htmlText) throws Exception {

        setProperties();// 设置属性
        // 发件人
        InternetAddress from = new InternetAddress(userName);
        Session session = Session.getDefaultInstance(props, null); // 获得邮件会话对象
        MimeMessage message = new MimeMessage(session); // 创建MIME邮件对象
        message.setFrom(from);

        String[] addr = null;
        addr = address.split(",");
        // 收件人
        InternetAddress[] to = new InternetAddress[addr.length];
        for (int i = 0; i < to.length; i++) {
            to[i] = new InternetAddress(addr[i]);
        }
        message.setRecipients(Message.RecipientType.TO, to);

        // 抄送人
        if (StringUtil.isNotEmpty(address2)) {
            InternetAddress cc = new InternetAddress(address2);
            message.setRecipient(Message.RecipientType.CC, cc);
        }
        // 邮件主题
        message.setSubject(subject);
        MimeBodyPart text = new MimeBodyPart();
        // setContent(“邮件的正文内容”,”设置邮件内容的编码方式”)
        text.setContent(htmlText, "text/html;charset=utf-8");
        MimeMultipart mm = new MimeMultipart();
        mm.addBodyPart(text);

        message.setContent(mm);
        message.saveChanges(); // 保存修改

        Transport transport = session.getTransport("smtp");
        transport.connect(userName,password);
        transport.sendMessage(message, message.getRecipients(Message.RecipientType.TO));
        transport.close();

    }
    

    /**
     *
     * @Description: 发送带附件邮件
     * <AUTHOR>
     * @date Jun 5, 2014 3:12:19 PM
     * @param subject 主题
     * @param content 邮件内容
     * @param addressTo 主送人地址
     * @param addressCc 抄送人地址
     * @param addressBcc 密送人地址
     * @param attachment 附件
     * @param fileName 附件名称（XXXX.xlsx）
     * @version V1.0
     */
    public static void sendMail(String subject, String content, String addressTo, String addressCc, String addressBcc, File attachment, String fileName) throws Exception {
        setProperties();// 设置属性
        // 发件人
        InternetAddress from = new InternetAddress(userName);
        Session session = Session.getDefaultInstance(props, null); // 获得邮件会话对象
        MimeMessage message = new MimeMessage(session); // 创建MIME邮件对象
        message.setFrom(from);

        // 收件人
        InternetAddress[] to = new InternetAddress().parse(addressTo);
        message.setRecipients(Message.RecipientType.TO, to);

        // 抄送人
        if (StringUtils.isNotEmpty(addressCc)) {
            InternetAddress[] cc = new InternetAddress().parse(addressCc);
            message.setRecipients(Message.RecipientType.CC, cc);
        }

        // 密送人
        if (StringUtils.isNotEmpty(addressBcc)) {
            InternetAddress[] bcc = new InternetAddress().parse(addressBcc);
            message.setRecipients(Message.RecipientType.BCC, bcc);
        }

        // 邮件主题
        message.setSubject(subject);

        // 向multipart对象中添加邮件的各个部分内容，包括文本内容和附件
        Multipart multipart = new MimeMultipart();

        // 添加邮件正文
        BodyPart contentPart = new MimeBodyPart();
        contentPart.setContent(content, "text/html;charset=UTF-8");
        multipart.addBodyPart(contentPart);

        // 添加附件的内容
        if (attachment != null) {
            BodyPart attachmentBodyPart = new MimeBodyPart();
            DataSource source = new FileDataSource(attachment);
            attachmentBodyPart.setDataHandler(new DataHandler(source));

			// 网上流传的解决文件名乱码的方法，其实用MimeUtility.encodeWord就可以很方便的搞定
            // 这里很重要，通过下面的Base64编码的转换可以保证你的中文附件标题名在发送时不会变成乱码
            // sun.misc.BASE64Encoder enc = new sun.misc.BASE64Encoder();
            // messageBodyPart.setFileName("=?GBK?B?" +
            // enc.encode(attachment.getName().getBytes()) + "?=");
            // MimeUtility.encodeWord可以避免文件名乱码
            // attachmentBodyPart.setFileName(MimeUtility.encodeWord(attachment
            // .getName()));
            attachmentBodyPart.setFileName(MimeUtility.encodeWord(fileName));
            multipart.addBodyPart(attachmentBodyPart);
        }

        // 将multipart对象放到message中
        message.setContent(multipart);
        // 保存邮件
        message.saveChanges();

        Transport transport = session.getTransport("smtp");
        // smtp验证，就是你用来发邮件的邮箱用户名密码
        transport.connect(props.get("mail.smtp.host").toString(), userName,
                password);
        // 发送
        transport.sendMessage(message, message.getAllRecipients());
        transport.close();
    }

    /**
     * chencan加
     * @param address 主送
     * @param address2  抄送
     * @param emailImage 图片地址
     * @param subject  主题
     * @param htmlText 内容
     * @throws Exception
     */
    public static void sendEmail(String address,String address2, List<String> emailImage,String subject, String htmlText) throws Exception {
        setProperties();// 设置属性
        // 发件人
        InternetAddress from = new InternetAddress(userName);
        Session session = Session.getDefaultInstance(props, null); // 获得邮件会话对象
        MimeMessage message = new MimeMessage(session); // 创建MIME邮件对象
        message.setFrom(from);
        // 收件人
        InternetAddress to = new InternetAddress(address);
        message.setRecipient(Message.RecipientType.TO, to);

        // 抄送人
        if (StringUtil.isNotEmpty(address2)) {
            InternetAddress cc = new InternetAddress(address2);
            message.setRecipient(Message.RecipientType.CC, cc);
        }
        // 邮件主题
        message.setSubject(subject);
        MimeBodyPart text = new MimeBodyPart();
        // setContent(“邮件的正文内容”,”设置邮件内容的编码方式”)
        text.setContent(htmlText, "text/html;charset=utf-8");
        // 创建图片
        MimeBodyPart img = new MimeBodyPart();
        DataHandler dh = new DataHandler(new FileDataSource(emailImage.get(0)));// 图片路径
        img.setDataHandler(dh);
        // 创建图片的一个表示用于显示在邮件中显示
        img.setContentID("<image>");

//        MimeBodyPart img2 = new MimeBodyPart();
//        DataHandler dh2 = new DataHandler(new FileDataSource(emailImage.get(1)));// 第二张图片路径
//        img2.setDataHandler(dh2);
//        img2.setContentID("<aa>");
        MimeMultipart mm = new MimeMultipart();
        mm.addBodyPart(text);
        mm.addBodyPart(img);
//        mm.addBodyPart(img2);
        mm.setSubType("related");// 设置正文与图片之间的关系
        // 带附件 body
//        MimeBodyPart all = new MimeBodyPart();
//        all.setContent(mm);
//        // 附件与正文（text 和 img）的关系
//        MimeMultipart mm2 = new MimeMultipart();
//        mm2.addBodyPart(all);
//        mm2.addBodyPart(img2);
//        mm2.setSubType("related");// 设置正文与附件之间的关系

        message.setContent(mm);
        message.saveChanges(); // 保存修改

        Transport transport = session.getTransport("smtp");
        transport.connect(userName,password);
        transport.sendMessage(message, message.getRecipients(Message.RecipientType.TO));
        transport.close();
    }

    /**
     * 获得邮件正文内容
     */
    public String getBodyText() {
        return bodytext.toString();
    }

    public static Date getTaskTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY,
                calendar.get(Calendar.HOUR_OF_DAY) - 1);
        return calendar.getTime();
    }


    /**
     * 获得发件人的地址和姓名
     */
    public String getFrom(MimeMessage mimeMessage) throws Exception {
        InternetAddress address[] = (InternetAddress[]) mimeMessage.getFrom();
        String from = address[0].getAddress();
        if (from == null) {
            from = "";
        }
        // String personal = address[0].getPersonal();
        // if (personal == null)
        // personal = "";
        // String fromaddr = personal + "<" + from + ">";
        return from;
    }

    /**
     * 获得邮件主题
     */
    public String getSubject(MimeMessage mimeMessage) throws MessagingException {
        String subject = "";
        try {
            subject = MimeUtility.decodeText(mimeMessage.getSubject());
            if (subject == null) {
                subject = "";
            }
        } catch (Exception exce) {
        }
        return subject;
    }

    /**
     * 获得邮件发送日期
     */
    public String getSentDate(MimeMessage mimeMessage) throws Exception {
        Date sentdate = mimeMessage.getSentDate();
        SimpleDateFormat format = new SimpleDateFormat(dateformat);
        return format.format(sentdate);
    }

    /**
     * 解析邮件，把得到的邮件内容保存到一个StringBuffer对象中，解析邮件 主要是根据MimeType类型的不同执行不同的操作，一步一步的解析
     *
     * @throws UnsupportedEncodingException
     */
    public void getMailContent(Part part) throws MessagingException,
            IOException {
        bodytext = new StringBuilder();
        String contentType = part.getContentType();
        int nameindex = contentType.indexOf("name");
        boolean conname = false;
        if (nameindex != -1) {
            conname = true;
        }
        if (part.isMimeType("text/plain") && !conname) {
            bodytext.append((String) part.getContent());
        } else if (part.isMimeType("text/html") && !conname) {
            bodytext.append((String) part.getContent());
        } else if (part.isMimeType("multipart/*")) {
            Multipart multipart = (Multipart) part.getContent();
            int count = multipart.getCount();
            for (int i = 0; i < count; i++) {
                getMailContent(multipart.getBodyPart(i));
            }
        } else if (part.isMimeType("message/rfc822")) {
            getMailContent((Part) part.getContent());
        }
    }

    /**
     * 【判断此邮件是否已读，如果未读返回返回false,反之返回true】
     */
    public boolean isNew(MimeMessage mimeMessage) throws MessagingException {
        boolean isnew = false;
        Flags flags = ((Message) mimeMessage).getFlags();
        Flags.Flag[] flag = flags.getSystemFlags();
        for (int i = 0; i < flag.length; i++) {
            if (flag[i] == Flags.Flag.SEEN) {
                isnew = true;
                break;
            }
        }
        return isnew;
    }

    private static void setProperties() {

        if (null == props) {
            props = System.getProperties();
            props.put("mail.smtp.host", smtpHost);
            props.put("mail.smtp.auth", "true");
        }
    }



}
