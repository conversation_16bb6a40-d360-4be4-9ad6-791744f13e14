package com.xyy.saas.web.api.module.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.Date;
import java.util.Map;

/**
 * @Description 通用辅助方法
 * <AUTHOR>
 * @Create 2020-08-21 16:29
 */
public class OptUtil {

    public static boolean ENABLE_MOCK = true;

    /*************************isEmpty/notEmpty start******************************
     /**
     * 字符串判空
     * @param str
     * @return
     */
    public static boolean isEmpty(String str) {
        return StringUtils.isBlank(str);
    }

    /**
     * 字符串非空判断
     * 通过参数类型自动匹配正确的重载版本，而不是通过在方法名中指定类型，减少API复杂性
     * @param str
     * @return
     */
    public static boolean notEmpty(String str) {
        return StringUtils.isNotBlank(str);
    }

    /**
     * 判断数组为空
     * @param arr
     * @return
     */
    public static <T> boolean isEmpty(T[] arr) {
        if (arr == null || arr.length == 0) {
            return true;
        }
        return false;
    }

    /**
     * 判断数组非空
     * @param arr
     * @return
     */
    public static <T> boolean notEmpty(T[] arr) {
        return !isEmpty(arr);
    }

    /**
     * 集合判空
     * @param collection
     * @return
     */
    public static boolean isEmpty(Collection<?> collection) {
        return collection == null || collection.isEmpty();
    }

    /**
     * 集合非空判断
     * <AUTHOR> 2018年10月15日 下午2:36:48
     * @param collection
     * @return
     */
    public static boolean notEmpty(Collection<?> collection) {
        return !isEmpty(collection);
    }

    /**
     * map判空
     * @param map
     * @return
     */
    public static boolean isEmpty(Map<?, ?> map) {
        return map == null || map.isEmpty();
    }

    /**
     * 判断map非空
     * @param map
     * @return
     */
    public static boolean notEmpty(Map<?, ?> map) {
        return !isEmpty(map);
    }


    /*************************isEmpty/notEmpty end******************************/
    public static Long dateToLong(Date date) {
        Long dateLong = null;
        if(date != null) {
            dateLong = date.getTime();
        }
        return dateLong;
    }

}
