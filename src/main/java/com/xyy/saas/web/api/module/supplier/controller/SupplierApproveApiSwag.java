package com.xyy.saas.web.api.module.supplier.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.supplier.dto.FirstCheckProviderDto;
import com.xyy.saas.web.api.module.supplier.model.ApproveHistory;
import com.xyy.saas.web.api.module.supplier.model.AreaBaseinfo;
import com.xyy.saas.web.api.module.supplier.model.FirstCheckSupplier;
import com.xyy.saas.web.api.module.supplier.model.ProviderAreaInfo;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Classname SupplierAreaInfoApiSwag
 * @Description 区域信息Api
 * @Date 2020/5/15 17:41
 */

@RequestMapping("/product/supplier/approve")
@Api(value = "供应商首营审批API接口", description = "供应商首营审批接口")
public interface SupplierApproveApiSwag {


    @ApiOperation(value = "分页查询首营供应商信息", notes = "分页查询首营供应商信息", response = ResultVO.class, tags={ "供应商首营审批API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ApiImplicitParams({
            @ApiImplicitParam(name="organSign", value="机构标识", required=true, paramType="header", dataType = "string")
    })
    @ResponseBody
    @RequestMapping(value="/query", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> query(HttpServletRequest request,
                                                                @ApiParam(value = "首营实体信息", required = true) @NotNull @RequestBody FirstCheckSupplier firstCheckSupplier);

    /**
     * 根据ID获取供应商信息
     * @param id
     * @return
     */
    @ApiOperation(value = "根据ID获取供应商信息", notes = "根据ID获取供应商信息", response = ResultVO.class, tags={ "供应商首营审批API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ApiImplicitParams({
            @ApiImplicitParam(name="organSign", value="机构标识", required=true, paramType="header", dataType = "string")
    })
    @ResponseBody
    @RequestMapping("/get/{id}")
    public ResponseEntity<ResultVO> getProductById(HttpServletRequest request, @PathVariable("id") Integer id);

    /**
     * 查询审核记录
     * @param approveHistory
     * @return
     */
    @ApiOperation(value = "查询审核记录", notes = "查询审核记录", response = ResultVO.class, tags={ "供应商首营审批API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ApiImplicitParams({
            @ApiImplicitParam(name="organSign", value="机构标识", required=true, paramType="header", dataType = "string")
    })
    @ResponseBody
    @RequestMapping(value="/getApproveFlowList", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getApproveFlowList(HttpServletRequest request,
                                                                @ApiParam(value = "审核记录", required = true) @NotNull @RequestBody ApproveHistory approveHistory);

    /**
     * 打印功能
     * @param request
     * @param guid
     * @param businessNo
     * @return
     */
    @ApiOperation(value = "打印功能", notes = "打印功能", response = ResultVO.class, tags={ "供应商首营审批API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ApiImplicitParams({
            @ApiImplicitParam(name="organSign", value="机构标识", required=true, paramType="header", dataType = "string"),
            @ApiImplicitParam(name="guid", value="guid", required=true, paramType="query", dataType = "string"),
            @ApiImplicitParam(name="businessNo", value="业务编号", required=true, paramType="query", dataType = "string")
    })
    @ResponseBody
    @RequestMapping(value="/printExport", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> printExport(HttpServletRequest request,
                                                @ApiParam(value = "guid", required = true) String guid,
                                                @ApiParam(value = "业务编号", required = true)String businessNo);

    /**
     * 查询打印供应商信息
     * @param request
     * @param guid
     * @param businessNo
     * @return
     */
    @ApiOperation(value = "查询打印供应商信息", notes = "查询打印供应商信息", response = ResultVO.class, tags={ "供应商首营审批API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ApiImplicitParams({
            @ApiImplicitParam(name="organSign", value="机构标识", required=true, paramType="header", dataType = "string"),
            @ApiImplicitParam(name="employeeId", value="员工id", required=true, paramType="header", dataType = "string"),
    })
    @ResponseBody
    @RequestMapping(value="/getProviderPrintInfo", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getProviderPrintInfo(HttpServletRequest request,
                                                         @ApiParam(value = "审核记录", required = true) @NotNull @RequestBody ApproveHistory approveHistory);

    /**
     * 批量审批
     * @param request
     * @param guid
     * @param businessNo
     * @return
     */
    @ApiOperation(value = "供应商批量审批", notes = "供应商批量审批", response = ResultVO.class, tags={ "供应商首营审批API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ApiImplicitParams({
            @ApiImplicitParam(name="organSign", value="机构标识", required=true, paramType="header", dataType = "string"),
            @ApiImplicitParam(name="employeeId", value="员工id", required=true, paramType="header", dataType = "string"),
    })
    @ResponseBody
    @RequestMapping(value="/batchUpdate", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> batchUpdate(HttpServletRequest request,
                                                         @ApiParam(value = "审核记录", required = true) @NotNull @RequestBody ApproveHistory approveHistory);

    /**
     * 批量审批
     * @param request
     * @param guid
     * @param businessNo
     * @return
     */
    @ApiOperation(value = "供应商分页查询接口", notes = "供应商批量审批", response = ResultVO.class, tags={ "供应商首营审批API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ApiImplicitParams({
            @ApiImplicitParam(name="organSign", value="机构标识", required=true, paramType="header", dataType = "string"),
    })
    @ResponseBody
    @RequestMapping(value="/list", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> list(HttpServletRequest request,
                                                @ApiParam(value = "审核记录", required = true) @NotNull @RequestBody ApproveHistory approveHistory);



}
