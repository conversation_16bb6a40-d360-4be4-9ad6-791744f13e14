package com.xyy.saas.web.api.module.utils;

import com.google.common.base.Strings;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import javax.servlet.http.HttpServletRequest;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-08-07
 * @mondify
 * @copyright
 */
public class IpUtil {
    private static final Log log = LogFactory.getLog(IpUtil.class);

    private static List<IpRange> privateNetIp = new LinkedList();

    public static String getRealIp(HttpServletRequest request)
    {
        String ip = request.getHeader("x-real-ip");
        if ((ip == null) || (ip.length() == 0) || ("unknown".equalsIgnoreCase(ip))) {
            ip = request.getHeader("x-forwarded-for");
        }
        if ((ip == null) || (ip.length() == 0) || ("unknown".equalsIgnoreCase(ip))) {
            ip = request.getHeader("Proxy-Client-IP");
        }

        if ((ip == null) || (ip.length() == 0) || ("unknown".equalsIgnoreCase(ip))) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }

        if ((ip == null) || (ip.length() == 0) || ("unknown".equalsIgnoreCase(ip))) {
            ip = request.getRemoteAddr();
        }

        ip = getFirstIP(ip);
        return ip;
    }

    public static String getFirstIP(String ipstr) {
        String ip = ipstr;
        String[] arr = ipstr.split(",");
        String[] var6 = arr;
        int var5 = arr.length;

        for (int var4 = 0; var4 < var5; var4++) {
            String item = var6[var4];
            if (item.contains(".")) {
                ip = item.trim();
                break;
            }
        }
        return ip;
    }

    public static long ip2Long(String ip) {
        if (Strings.isNullOrEmpty(ip)) {
            log.error("ip can't be null or empty.");
            return -1L;
        }

        ip = ip.replace(" ", "");
        ip = ip.replace("　", "");
        String[] ips = ip.trim().split("\\.");
        long longIp = 0L;
        try {
            longIp = Long.parseLong(ips[0]) * 256L * 256L * 256L + Long.parseLong(ips[1]) * 256L * 256L + Long.parseLong(ips[2]) * 256L + Long.parseLong(ips[3]);
        }
        catch (Exception e) {
            log.error("ip error: " + ip, e);
        }
        return longIp;
    }

    public static boolean isPrivateNet(String ip)
    {
        long currentIp = ip2Long(ip);
        for (IpRange ipRang : privateNetIp) {
            if (ipRang.contain(currentIp)) {
                return true;
            }
        }
        return false;
    }

    static
    {
        privateNetIp.add(new IpRange(ip2Long("127.0.0.1")));
        privateNetIp.add(new IpRange(ip2Long("0.0.0.0")));
        privateNetIp.add(new IpRange(ip2Long("10.0.0.0"), ip2Long("**************")));
        privateNetIp.add(new IpRange(ip2Long("**********"), ip2Long("**************")));
        privateNetIp.add(new IpRange(ip2Long("***********"), ip2Long("***************")));
    }
}
