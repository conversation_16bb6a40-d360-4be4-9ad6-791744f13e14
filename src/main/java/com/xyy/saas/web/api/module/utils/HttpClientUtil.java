package com.xyy.saas.web.api.module.utils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.LayeredConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeader;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName CommonHttpClientUtil
 * @Description httpclient工具类
 * <AUTHOR>
 **/
public class HttpClientUtil {

    private static final Logger log = LoggerFactory.getLogger(HttpClientUtil.class);
    private static int connectionRequestTimeOut = 1000;//设置从connect Manager获取Connection 超时时间
    private static int connectionTimeout = 2000;//连接超时时间
    private static int socketTimeout = 30000;//请求获取数据的超时时间
    private static int maxConnections = 500;
    private static int maxConnectionsPerRoute = 100;
    private static int evictIdleTime = 60000;
    private static RequestConfig requestConfig = RequestConfig.custom().setConnectionRequestTimeout(connectionRequestTimeOut)
                    .setConnectTimeout(connectionTimeout)
                    .setSocketTimeout(socketTimeout)
                    .build();
    private static CloseableHttpClient httpClient;
    private static PoolingHttpClientConnectionManager manager; //连接池管理类

    //初始化
    static {
        initHttpClientPool();
    }

    private static void initHttpClientPool() {
        ConnectionSocketFactory plainSocketFactory = PlainConnectionSocketFactory.getSocketFactory();
        LayeredConnectionSocketFactory sslSocketFactory = SSLConnectionSocketFactory.getSocketFactory();
        Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create().register("http", plainSocketFactory)
                .register("https", sslSocketFactory).build();

        manager = new PoolingHttpClientConnectionManager(registry);
        manager.setMaxTotal(maxConnections);
        manager.setDefaultMaxPerRoute(maxConnectionsPerRoute);
        httpClient = HttpClients.custom().
                setConnectionManager(manager).
                ////开启后台线程清除过期的连接
//                evictExpiredConnections().
                //开启后台线程清除闲置60秒以上的连接
                evictIdleConnections(evictIdleTime, TimeUnit.MILLISECONDS).
                disableAutomaticRetries().build();

    }

    /**
     * 无请求头的get默认超时时间调用
     * @param url
     * @param requestMap
     * @return
     * @throws Exception
     */
    public static String httpGet(String url, Map<String, Object> requestMap) throws Exception {
        return httpGet(url,requestMap,null,null);
    }

    /**
     * 无请求头的get调用
     * @param url
     * @param requestMap
     * @param requestConfig
     * @return
     * @throws Exception
     */
    public static String httpGet(String url, Map<String, Object> requestMap,RequestConfig requestConfig) throws Exception {
        return httpGet(url,requestMap,null,requestConfig);
    }

    /**
     * 无请求头的get调用
     * @param url
     * @param requestMap
     * @param headerMap
     * @return
     * @throws Exception
     */
    public static String httpGet(String url, Map<String, Object> requestMap,Map<String, String> headerMap) throws Exception {
        return httpGet(url,requestMap,headerMap,null);
    }

    /**
     * 有请求头的get调用
     * @param url
     * @param requestMap
     * @param headerMap
     * @param requestConfig
     * @return
     * @throws Exception
     */
    public static String httpGet(String url, Map<String, Object> requestMap, Map<String, String> headerMap,RequestConfig requestConfig) throws Exception {

        String queryParam = getHttpQueryParamStr(requestMap);
        if(StringUtils.isNotEmpty(queryParam)) {
            url = url + "?" + queryParam;
        }
        HttpGet httpGet = new HttpGet(url);
        processHttpHeaders(headerMap, httpGet);
        setRequestConfig(httpGet,requestConfig);
        if (log.isDebugEnabled()) {
            log.debug("httpGet request: {}",url);
        }
        CloseableHttpResponse httpResponse = httpClient.execute(httpGet);
        HttpEntity entity = httpResponse.getEntity();
        String result = EntityUtils.toString(entity, "utf-8");
        if (log.isDebugEnabled()) {
            log.debug("httpGet result: {}",result);
        }
        if (log.isDebugEnabled()) {
            log.debug("httpGet httpResponse: {}",JSON.toJSONString(httpResponse.getStatusLine()));
        }
        int statusCode = httpResponse.getStatusLine().getStatusCode();
        if (statusCode != 200) {
            throw new Exception("HTTP status code: " + statusCode);
        }
        return result;
    }


    public static String httpPost(String url,String str, Map<String, String> headerMap,RequestConfig requestConfig) throws Exception {
        HttpPost httpPost = new HttpPost(url);
        processHttpHeaders(headerMap, httpPost);
        String contentType = ContentType.APPLICATION_JSON.getMimeType();
        if(MapUtils.isNotEmpty(headerMap)&&StringUtils.isNotEmpty(headerMap.get(HttpHeaders.CONTENT_TYPE))) {
            contentType = headerMap.get(HttpHeaders.CONTENT_TYPE);
        }
        //解决中文乱码问题
        StringEntity stringEntity = new StringEntity(str,"UTF-8");
        stringEntity.setContentEncoding("UTF-8");
        stringEntity.setContentType(contentType);
        httpPost.setEntity(stringEntity);
        if (log.isDebugEnabled()) {
            log.debug("httpPost request url: {}  request param:{}",url,str);
        }
        setRequestConfig(httpPost,requestConfig);
        CloseableHttpResponse httpResponse = httpClient.execute(httpPost);
        HttpEntity entity = httpResponse.getEntity();
        String result = EntityUtils.toString(entity, Consts.UTF_8);
        if (log.isDebugEnabled()) {
            log.debug("httpPost result: {}",result);
        }
        if (log.isDebugEnabled()) {
            log.debug("httpPost httpResponse: {}",JSON.toJSONString(httpResponse.getStatusLine()));
        }
        int statusCode = httpResponse.getStatusLine().getStatusCode();
        if (statusCode != 200) {
            throw new Exception("HTTP status code: " + statusCode);
        }

        return result;
    }

    /**
     * 不关心响应码是否=200
     * @param url
     * @param requestMap
     * @param headerMap
     * @param requestConfig
     * @return
     * @throws Exception
     */
    public static String httpPostNotCareResponseStatusCode(String url, Map<String, Object> requestMap, Map<String, String> headerMap, RequestConfig requestConfig) throws Exception {
        String contentType = ContentType.APPLICATION_FORM_URLENCODED.getMimeType();
        HttpPost httpPost = new HttpPost(url);

        processHttpHeaders(headerMap, httpPost);

        if(MapUtils.isNotEmpty(headerMap)&&StringUtils.isNotEmpty(headerMap.get(HttpHeaders.CONTENT_TYPE))) {
            contentType = headerMap.get(HttpHeaders.CONTENT_TYPE);
        }
        if (contentType.equals(ContentType.APPLICATION_FORM_URLENCODED.getMimeType())) {
            List<NameValuePair> pairs = getNameValuePairs(requestMap);
            httpPost.setEntity(new UrlEncodedFormEntity(pairs, Consts.UTF_8));

        } else {
            StringEntity stringEntity = new StringEntity(JSON.toJSONString(requestMap),"UTF-8");
            stringEntity.setContentEncoding(new BasicHeader("Content-Type", "application/json"));
            stringEntity.setContentType(contentType);
            httpPost.setEntity(stringEntity);
        }
        setRequestConfig(httpPost,requestConfig);
        if (log.isDebugEnabled()) {
            log.debug("httpPost request url: {}  request param:{}",url, JSON.toJSONString(requestMap));
        }
        CloseableHttpResponse httpResponse = httpClient.execute(httpPost);
        HttpEntity entity = httpResponse.getEntity();
        String result = EntityUtils.toString(entity, Consts.UTF_8);
        if (log.isDebugEnabled()) {
            log.debug("httpPost result: {}",result);
        }
        if (log.isDebugEnabled()) {
            log.debug("httpPost httpResponse: {}",JSON.toJSONString(httpResponse.getStatusLine()));
        }
        return result;
    }


    /**
     * 无请求头的post调用
     * @param url
     * @param requestMap
     * @return
     * @throws Exception
     */
    public static String httpPost(String url, Map<String, Object> requestMap) throws Exception {
        return httpPost(url,requestMap,null,null);
    }

    /**
     * 无请求头的默认post调用
     * @param url
     * @param requestMap
     * @param requestConfig
     * @return
     * @throws Exception
     */
    public static String httpPost(String url, Map<String, Object> requestMap,RequestConfig requestConfig) throws Exception {
        return httpPost(url,requestMap,null,requestConfig);
    }

    /**
     * 有请求头的默认post调用
     * @param url
     * @param requestMap
     * @param headerMap
     * @return
     * @throws Exception
     */
    public static String httpPost(String url, Map<String, Object> requestMap,Map<String, String> headerMap) throws Exception {
        return httpPost(url,requestMap,headerMap,null);
    }

    /**
     * 有请求头的post调用
     * @param url
     * @param requestMap
     * @param headerMap
     * @param requestConfig
     * @return
     * @throws Exception
     */
    public static String httpPost(String url, Map<String, Object> requestMap, Map<String, String> headerMap,RequestConfig requestConfig) throws Exception {
        String contentType = ContentType.APPLICATION_FORM_URLENCODED.getMimeType();
        HttpPost httpPost = new HttpPost(url);

        processHttpHeaders(headerMap, httpPost);
        if (headerMap == null) {
            headerMap = Maps.newHashMap();
        }
        if(MapUtils.isNotEmpty(headerMap)&&StringUtils.isNotEmpty(headerMap.get(HttpHeaders.CONTENT_TYPE))) {
            contentType = headerMap.get(HttpHeaders.CONTENT_TYPE);
        }
        if (contentType.equals(ContentType.APPLICATION_FORM_URLENCODED.getMimeType())) {
            List<NameValuePair> pairs = getNameValuePairs(requestMap);
            httpPost.setEntity(new UrlEncodedFormEntity(pairs, Consts.UTF_8));

        } else {
            StringEntity stringEntity = new StringEntity(JSON.toJSONString(requestMap),"UTF-8");
            stringEntity.setContentEncoding(new BasicHeader("Content-Type", "application/json"));
            stringEntity.setContentType(contentType);
            httpPost.setEntity(stringEntity);
        }
        setRequestConfig(httpPost,requestConfig);
        if (log.isDebugEnabled()) {
            log.debug("httpPost request url: {}  request param:{}",url,JSON.toJSONString(requestMap));
        }
        CloseableHttpResponse httpResponse = httpClient.execute(httpPost);
        HttpEntity entity = httpResponse.getEntity();
        String result = EntityUtils.toString(entity, Consts.UTF_8);
        if (log.isDebugEnabled()) {
            log.debug("httpPost result: {}",result);
        }
        if (log.isDebugEnabled()) {
            log.debug("httpPost httpResponse: {}",JSON.toJSONString(httpResponse.getStatusLine()));
        }
        int statusCode = httpResponse.getStatusLine().getStatusCode();
        if (statusCode != 200) {
            throw new Exception("HTTP status code: " + statusCode);
        }

        return result;
    }


    /**
     * 设置requestconfig
     * @param httpRequestBase
     * @param rc
     */
    private static void setRequestConfig(HttpRequestBase httpRequestBase,RequestConfig rc) {
        if(null==rc) {
            rc = requestConfig;
        }
        httpRequestBase.setConfig(rc);
    }


    /**
     * 封装请求头
     * @param headerMap
     * @param httpRequestBase
     */
    private static void processHttpHeaders(Map<String, String> headerMap, HttpRequestBase httpRequestBase) {
        if (MapUtils.isNotEmpty(headerMap)) {
            for (String key : headerMap.keySet()) {
                httpRequestBase.addHeader(key, headerMap.get(key));
            }
        }
    }

    public static List<NameValuePair> getNameValuePairs(Map<String, Object> params) {
        if(MapUtils.isEmpty(params)) return new ArrayList<>();;
        List<NameValuePair> pairs = Lists.newArrayListWithCapacity(params.size());
        Iterator iterator = params.entrySet().iterator();

        while (iterator.hasNext()) {
            Map.Entry<String, Object> entry = (Map.Entry) iterator.next();
            String value = String.valueOf(entry.getValue());
            if (StringUtils.isNotEmpty(value) && !"null".equals(value)) {
                pairs.add(new BasicNameValuePair(entry.getKey(), value));
            }
        }

        return pairs;
    }

    public static String getHttpQueryParamStr(Map<String, Object> params) {
        try {
            List<NameValuePair> pairs = getNameValuePairs(params);
            String paramStr = EntityUtils.toString(new UrlEncodedFormEntity(pairs, Consts.UTF_8));
            return paramStr;
        } catch (Exception e) {
            log.error("getHttpQueryParamStr Error: ", e);
            return null;
        }
    }

    public static void setMaxConnections(int maxConnections) {
        HttpClientUtil.maxConnections = maxConnections;
    }

    public static void setMaxConnectionsPerRoute(int maxConnectionsPerRoute) {
        HttpClientUtil.maxConnectionsPerRoute = maxConnectionsPerRoute;
    }

    public static void setEvictIdleTime(int evictIdleTime) {
        HttpClientUtil.evictIdleTime = evictIdleTime;
    }
}