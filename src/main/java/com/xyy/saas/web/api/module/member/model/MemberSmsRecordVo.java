package com.xyy.saas.web.api.module.member.model;

import com.xyy.saas.member.core.dto.MemberSmsRelationDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@ApiModel(description = "会员短信记录")
public class MemberSmsRecordVo implements Serializable {

    private static final long serialVersionUID = -6252112083895280216L;

    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 通知标题
     */
    @ApiModelProperty(value = "通知标题")
    private String title;
    /**
     * 短信签名
     */
    @ApiModelProperty(value = "短信签名")
    private String signature;
    /**
     * 通知内容
     */
    @ApiModelProperty(value = "通知内容")
    private String content;
    /**
     * 通知内容结尾
     */
    @ApiModelProperty(value = "短信结尾")
    private String contentEnd;
    /**
     * 发送条数
     */
    @ApiModelProperty(value = "发送条数")
    private Integer sendCount;
    /**
     * 发送人数
     */
    @ApiModelProperty(value = "发送人数")
    private Integer sendNumber;
    /**
     * 发送状态 0 成功  1 失败
     */
    @ApiModelProperty(value = "发送状态 0 成功  1 失败")
    private Byte sendType = null;
    /**
     * 通知类型 0 自定义 1 储值变更
     */
    @ApiModelProperty(value = "通知类型 0 自定义 1 储值变更")
    private Byte smsType = null;
    /**
     * 机构标识
     */
    @ApiModelProperty(value = "机构标识")
    private String organSign;
    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称")
    private String organName;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 创建用户
     */
    @ApiModelProperty(value = "创建用户")
    private String createUser;
    /**
     * 失败原因
     */
    @ApiModelProperty(value = "失败原因")
    private String failDesc;
    /**
     * 版本控制（pos需要存储到本地）
     */
    @ApiModelProperty(value = "版本控制",hidden = true)
    private Integer baseVersion;
    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    private Integer pageNum;
    /**
     * 页码大小
     */
    @ApiModelProperty(value = "页码大小")
    private Integer pageSize;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private String startDate;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String endDate;
    /**
     * 混合查询
     * @return mixedQuery
     **/
    @ApiModelProperty(value = "混合查询")
    private String mixedQuery = null;
    /**
     * 性别
     * @return mixedQuery
     **/
    @ApiModelProperty(value = "性别 1 男 2 女 ")
    private Integer sex;
    /**
     * 级别id
     */
    @ApiModelProperty(value = "级别id")
    private Long vipLevelId;
    /**
     * 选择会员 1、全部  2、部分
     */
    @ApiModelProperty(value = "选择会员 1、全部  2、部分")
    private Integer type;
    /**
     * 图片验证码
     */
    @ApiModelProperty(value = "图片验证码")
    private String vcode;
    /**
     *  短信记录会员关系，关联
     */
    @ApiModelProperty(value = "会员关系")
    private List<MemberSmsRelationDto> relationDtoList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
    public String getSignature() {
		return signature;
	}

	public void setSignature(String signature) {
		this.signature = signature == null ? null : signature.trim();
	}

	public String getContentEnd() {
		return contentEnd;
	}

	public void setContentEnd(String contentEnd) {
		this.contentEnd = contentEnd == null ? null : contentEnd.trim();
	}
	
    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getSendCount() {
        return sendCount;
    }

    public void setSendCount(Integer sendCount) {
        this.sendCount = sendCount;
    }

    public Integer getSendNumber() {
        return sendNumber;
    }

    public void setSendNumber(Integer sendNumber) {
        this.sendNumber = sendNumber;
    }

    public Byte getSendType() {
        return sendType;
    }

    public void setSendType(Byte sendType) {
        this.sendType = sendType;
    }

    public Byte getSmsType() {
        return smsType;
    }

    public void setSmsType(Byte smsType) {
        this.smsType = smsType;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public String getOrganName() {
        return organName;
    }

    public void setOrganName(String organName) {
        this.organName = organName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getFailDesc() {
        return failDesc;
    }

    public void setFailDesc(String failDesc) {
        this.failDesc = failDesc;
    }

    public Integer getBaseVersion() {
        return baseVersion;
    }

    public void setBaseVersion(Integer baseVersion) {
        this.baseVersion = baseVersion;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public List<MemberSmsRelationDto> getRelationDtoList() {
        return relationDtoList;
    }

    public void setRelationDtoList(List<MemberSmsRelationDto> relationDtoList) {
        this.relationDtoList = relationDtoList;
    }

    public String getMixedQuery() {
        return mixedQuery;
    }

    public void setMixedQuery(String mixedQuery) {
        this.mixedQuery = mixedQuery;
    }

    public Integer getSex() {
        return sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public Long getVipLevelId() {
        return vipLevelId;
    }

    public void setVipLevelId(Long vipLevelId) {
        this.vipLevelId = vipLevelId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getVcode() {
        return vcode;
    }

    public void setVcode(String vcode) {
        this.vcode = vcode;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }
}
