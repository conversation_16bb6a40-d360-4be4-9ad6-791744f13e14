package com.xyy.saas.web.api.module.product.controller;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.dto.SystemDictDto;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.match.dto.MatchProductVoDto;
import com.xyy.saas.product.core.dto.*;
import com.xyy.saas.product.core.vo.PosDrugIdentCodeUpdateVo;
import com.xyy.saas.web.api.common.config.annotition.RepeatSubmitValidation;
import com.xyy.saas.web.api.module.product.model.*;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.net.UnknownHostException;
import java.util.List;
import java.util.Map;

@RequestMapping("/product")
@Api(value = "4.0商品基本信息API接口", description = "4.0商品基本信息API接口")
public interface ProductInfoApi {

    /**
     * 检查是否开启了快捷审核 且是否有审核人
     * @return       -1：未开启快捷审核
     *               -2：首营品种一审没有审核人信息
     *               -3：首营品种二审没有审核人信息
     *               -4：开启了自动审核
     *               0：开启了快捷审核且符合条件
     */
    @ApiOperation(value = "检查是否开启了快捷审核 且是否有审核人", notes = "检查是否开启了快捷审核 且是否有审核人", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/product/approveCheck", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> checkselfMotion(HttpServletRequest request);

    /**
     * 检查登录用户是否具有查看商品扩展信息的权限
     * @return       0：有权限
     *               1：没有权限
     */
    @ApiOperation(value = "检查登录用户是否具有查看商品扩展信息的权限", notes = "检查登录用户是否具有查看商品扩展信息的权限", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/product/isRoleExt", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> checkIsRoleExt(HttpServletRequest request);

    /**
     * 校验审核人的名称是否正确
     * @param id
     * @param pwd
     * @return
     */
    @ApiOperation(value = "校验审核人的名称是否正确", notes = "校验审核人的名称是否正确", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/product/firstCheckUser", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> firstCheckToUser(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody UserQueryVo userQueryVo);

    /**
     * 删除商品待确认接口
     * @param request
     * @param id
     * @return
     */
    @ApiOperation(value = "删除商品待确认接口", notes = "删除商品待确认接口", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/product/deleteProductAffirm",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> deleteProductAffirmOperation(HttpServletRequest request, @RequestParam(required = true) Long id);

    @ApiOperation(value = "根据商品id删除商品接口", notes = "根据商品id删除商品接口", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value ="/baseinfo/product/deleteProduct",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> deleteProductForId(HttpServletRequest request, @RequestParam(required = true) Long id);

    /**
     * 导出一个商品到excel中
     * @param request
     * @param response
     * @param id
     */
    @ApiOperation(value = "导出一个商品到excel中", notes = "导出一个商品到excel的接口", tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功") })
    @RequestMapping(value ="/baseinfo/product/exportOneProduct",method = RequestMethod.POST)
    public void exportExcelOneProduct(HttpServletRequest request, HttpServletResponse response, @RequestParam(required = true) Long id);

    /**
     * 重置标准库id
     * @param request
     * @param productQueryVo
     * @return
     */
    @ApiOperation(value = "重置标准库id", notes = "重置标准库id", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/product/resertBinnaryId",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> resertLibraryIdToNull(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody ProductQueryVo productQueryVo);

    /**
     * 设置商品是否启用接口，
     * @param request  请求参数
     * @param productQueryVo   禁用flag参数传0，启用flag参数传1
     * @return
     */
    @ApiOperation(value = "设置商品是否禁用接口", notes = "设置商品是否禁用接口", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/product/resertIsUsed",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> resertIsUsed(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody ProductQueryVo productQueryVo);

    /**
     *跳转到打印标签页面
     * @param request
     * @param model
     * @return
     */
    @ApiOperation(value = "跳转到打印标签页面", notes = "跳转到打印标签页面", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/product/toPrintList",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> toPrintList(HttpServletRequest request, Model model);

    /**
     * 查询打印标签列表
     */
    @ApiOperation(value = "查询打印标签列表", notes = "查询打印标签列表", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/product/toShowAdd",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> toShowAdd(HttpServletRequest request, Model model, String productName);

    @ApiOperation(value = "打印商品列表查询列表接口", notes = "打印商品列表查询列表接口", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/product/queryList",method = RequestMethod.POST)
    public ResponseEntity<ResultVO<PageInfo>> proQueryList(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody ProductCommonQueryVo productCommonQueryVo);

    @ApiOperation(value = "打印选中接口", notes = "打印选中接口", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/baseinfo/product/sendMsg",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> sendMsg(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody ProductQueryVo productQueryVo) ;

    /**
     * 带条件分页查询
     * @return
     */
    @ApiOperation(value = "查询商品数据接口", notes = "查询商品数据接口", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/baseinfo/product/query",method = RequestMethod.POST)
    public ResponseEntity<ResultVO<List<ProductDto>>> proList(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody ProductDto product);

    @ApiOperation(value = "导出商品列表到excel", notes = "导出商品列表到excel", produces="application/octet-stream",tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Void.class) })
    @RequestMapping(value = "/baseinfo/product/exportExcelProduct",method = RequestMethod.POST)
    public void exportExcelProduct(HttpServletRequest request, HttpServletResponse response,
                                   @ApiParam(value = "当前登录机构码", required = true) @RequestHeader(value = "organSign", required = true) String organSign,
                                   @ApiParam(value = "当前登录员工的ID", required = true) @RequestHeader(value = "employeeId", required = true) String employeeId,
                                   @ApiParam(value = "商品详情信息对象", required = true) @Valid @RequestBody ProductExportDto product);

    @ApiOperation(value = "新增商品跳转页面信息回显接口", notes = "新增商品跳转页面信息回显接口", tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功") })
    @RequestMapping(value = "/baseinfo/product/toAdd",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> toAdd(HttpServletRequest request, Model model) throws UnknownHostException ;

    @ApiOperation(value = "商品保存，更新提交接口", notes = "商品保存，更新提交接口", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/product/save", method = RequestMethod.POST)
    @RepeatSubmitValidation(resultType = 4)
    public ResponseEntity<ResultVO> addOrUpdate(HttpServletRequest request, @ApiParam(value = "商品详情信息对象", required = true) @Valid @RequestBody ProductDto product);

    @ApiOperation(value = "商品保存零售价风险风险提醒接口", notes = "商品保存零售价风险风险提醒接口", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/product/forSaveProductPrice", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> forSaveProductPrice(HttpServletRequest request, @ApiParam(value = "商品详情信息对象", required = true) @Valid @RequestBody ProductDto product);

    @ApiOperation(value = "商品重置标准库风险风险提醒接口", notes = "商品重置标准库风险风险提醒接口", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/product/forResertBinnaryId", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> forResertBinnaryId(HttpServletRequest request, @ApiParam(value = "商品详情信息对象", required = true) @Valid @RequestBody ProductDto product);

    @ApiOperation(value = "商品更新数据回显接口", notes = "商品更新数据回显接口", response = ProductDto.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ProductDto.class) })
    @RequestMapping(value = "/baseinfo/product/toUpdate",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getProductById(HttpServletRequest request, @ApiParam(value = "商品详情信息对象", required = true) @Valid @RequestBody ProductQueryVo productQueryVo);

    @ApiOperation(value = "《商品基本信息》页面信息回显接口", notes = "《商品基本信息》页面信息回显接口", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/product/toList",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> toList(HttpServletRequest request, Model model);

    @ApiOperation(value = "点击标准库查询数据回显接口", notes = "点击标准库查询数据回显接口", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/product/toLibraryList",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> toStandardLibraryList(HttpServletRequest request, Model model);

    @ApiOperation(value = "标准库列表页面查询接口", notes = "标准库列表页面查询接口", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/product/libraryQuery",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> query(HttpServletRequest request, @ApiParam(value = "标准库信息查询", required = true) @Valid @RequestBody BinnaryQueryDto vo) throws Exception ;

    /**
     * 刷新数据需求脚本，按照机构号批量刷新单位，商品系统类型，剂型id
     * @param request
     * @param refreshDataVo
     * @return
     */
    @ApiOperation(value = "刷新数据需求脚本，按照机构号批量刷新单位，商品系统类型，剂型id", notes = "刷新数据需求脚本，按照机构号批量刷新单位，商品系统类型，剂型id", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/product/refreshZJMByOrganSigns",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> refreshZJMByOrganSigns(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody RefreshDataVo refreshDataVo);

    /**
     * 刷新数据需求脚本，按照机构号批量刷新单位，商品系统类型，剂型id
     * @param request
     * @param organSign
     * @return
     */
    @ApiOperation(value = "用药提醒列表接口查询", notes = "用药提醒列表接口查询", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/product/listAllDrugRemind",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> listAllDrugRemind(HttpServletRequest request);

    @ApiOperation(value = "根据用药提醒ID查询", notes = "用药提醒查询", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/product/findDrugRemindByRemindId",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> findDrugRemindByRemindId(@ApiParam(value = "用药提醒ID", required = true) @RequestHeader(value = "drugRemindId", required = true) String drugRemindId,
                                                             @ApiParam(value = "当前登录机构码", required = true) @RequestHeader(value = "organSign", required = true) String organSign);
    /**
     * @param ykqQueryVo
     * @return
     */
    @ApiOperation(value = "提供给宜块钱，根据药店机构号和商品编号集合批量获取商品数据接口", notes = "提供给宜块钱，根据药店机构号和商品编号集合批量获取商品数据接口", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/product/getProductsByParamsForYkq",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getProductsByParamsForYkq(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody YkqQueryVo ykqQueryVo);
    /**
     * 带条件分页查询
     * @return
     */
    @ApiOperation(value = "提供给宜块钱，分页查询商品信息接口", notes = "提供给宜块钱，分页查询商品信息接口", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/baseinfo/product/queryProductForYkq",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> queryProductForYkq(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody ProductDto product);



    /**
     * 促销和会员模块，字典分类查询接口，包括：商品分类，自定义分类，abc分类，货位
     * @param organSign
     * @param employeeId
     * @param systemDictQueryVo
     * @return
     */
    @ApiOperation(value = "获取特定的商品字典类型数据", notes = "获取特定的商品字典类型数据", response = SystemDictDto.class, tags={ "新促销和会员活动", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = SystemDictVo.class) })
    @RequestMapping(value = "/product/getAppointTypeList", method = RequestMethod.POST)
    ResponseEntity<ResultVO> getAppointTypeList(@ApiParam(value = "当前登录机构码", required = true) @RequestHeader(value = "organSign", required = true) String organSign,
                                                @ApiParam(value = "当前登录员工的ID", required = true) @RequestHeader(value = "employeeId", required = true) String employeeId,
                                                @ApiParam(value = "请求字典类型", required = true) @Valid @RequestBody SystemDictQueryVo systemDictQueryVo);

    /**
     * 促销调用商品查询接口，分页展示
     * @param request
     * @param promotionQueryVo
     * @return
     */
    @ApiOperation(value = "促销调用商品查询接口", notes = "促销调用商品查询接口", response = PromotionMixResultVo.class, tags={ "新促销和会员活动", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = PromotionMixResultVo.class) })
    @RequestMapping(value = "/product/getCanTakePartInPromotionProduct", method = RequestMethod.POST)
    ResponseEntity<ResultVO> getCanTakePartInPromotionProduct(HttpServletRequest request,
                                                              @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody PromotionQueryVo promotionQueryVo);

    /**
     * 会员模块商品查询接口，分页展示
     * @param request
     * @param memberDayQueryVo
     * @return
     */
    @ApiOperation(value = "会员日调用商品查询接口,分页展示", notes = "会员日调用商品查询接口,分页展示", response = MemberDayMixResultVo.class, tags={ "新促销和会员活动", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberDayMixResultVo.class) })
    @RequestMapping(value = "/product/getCanTakePartInMemberDayProduct", method = RequestMethod.POST)
    ResponseEntity<ResultVO> getCanTakePartInMemberDayProduct(HttpServletRequest request,
                                                              @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody MemberDayQueryVo memberDayQueryVo);

    /**
     * 会员模块商品查询接口，分页展示
     * @param request
     * @param memberDayQueryVo
     * @return
     */
    @ApiOperation(value = "会员日调用商品查询接口,不分页展示", notes = "会员日调用商品查询接口,不分页展示", response = MemberDayMixResultVo.class, tags={ "新促销和会员活动", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = MemberDayMixResultVo.class) })
    @RequestMapping(value = "/product/getCanTakePartInMemberDayProductNoPage", method = RequestMethod.POST)
    ResponseEntity<ResultVO> getCanTakePartInMemberDayProductNoPage(HttpServletRequest request,
                                                                    @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody MemberDayQueryVo memberDayQueryVo);

    /**
     * 设置积分商品积分倍率接口
     * @param request  请求参数
     * @param specialProductUpdateVo   积分商品修改信息
     * @return
     */
    @ApiOperation(value = "设置积分商品积分倍率接口，支持单个和批量", notes = "设置积分商品积分倍率接口，支持单个和批量", response = ResultVO.class, tags={ "新促销和会员活动", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/product/updateSpecialProduct",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> updateSpecialProduct(HttpServletRequest request, @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody SpecialProductUpdateVo specialProductUpdateVo);

    /**
     * 获取单个商品批号列表信息
     * @param request
     * @param singleProductParamVo
     * @return
     */
    @ApiOperation(value = "获取单个商品批号列表信息", notes = "获取单个商品批号列表信息", response = SingleProductInventoryInfoVo.class, tags={ "新促销和会员活动", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = SingleProductInventoryInfoVo.class) })
    @RequestMapping(value = "/product/getSingleProductInventoryInfoVo", method = RequestMethod.POST)
    ResponseEntity<ResultVO> getSingleProductInventoryInfoVo(HttpServletRequest request,
                                                             @ApiParam(value = "请求信息对象", required = true) @Valid @RequestBody SingleProductParamVo singleProductParamVo);

    /**
     * 查询商品价签模板列表
     * @return
     */
    @ApiOperation(value = "查询商品价签模板列表接口", notes = "查询商品价签模板列表接口", response = PriceLabelDto.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = PriceLabelDto.class) })
    @RequestMapping(value = "/baseinfo/product/queryPriceLabelList",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> queryPriceLabelList(HttpServletRequest request);

    /**
     * 查询商品价签模板列表
     * @return
     */
    @ApiOperation(value = "查询商品价签导入商品接口", notes = "查询商品价签导入商品接口", response = PriceLabelDto.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = PriceLabelDto.class) })
    @RequestMapping(value = "/baseinfo/product/queryPriceLabelImportId",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> queryPriceLabelImportId(HttpServletRequest request,@RequestBody PriceLabelDto priceLabel);

    /**
     * 查询商品价签模板列表
     * @return
     */
    @ApiOperation(value = "查询商品价签模板列表接口HTML版", notes = "查询商品价签模板列表接口HTML版", response = PriceLabelDto.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = PriceLabelDto.class) })
    @RequestMapping(value = "/baseinfo/product/queryPriceLabelListHtml",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> queryPriceLabelListHtml(HttpServletRequest request,@ApiParam(value = "价签模板详情信息对象", required = true) @Valid @RequestBody PriceLabelDto priceLabel);

    /**
     * 商品价签模板保存或修改
     * @param request
     * @param priceLabel
     * @return
     */
    @ApiOperation(value = "价签模板保存，更新提交接口", notes = "价签模板保存，更新提交接口", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/product/savePriceLabel", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> addOrUpdatePriceLabel(HttpServletRequest request, @ApiParam(value = "价签模板详情信息对象", required = true) @Valid @RequestBody PriceLabelDto priceLabel);


    @ApiOperation(value = "根据价签模板id删除接口", notes = "根据价签模板id删除接口", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value ="/baseinfo/product/deletePriceLabel",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> deletePriceLabelForId(HttpServletRequest request, @ApiParam(value = "价签模板详情信息对象", required = true) @Valid @RequestBody PriceLabelDto priceLabel);


    @ApiOperation(value = "价签模板根据商品编码查询商品信息", notes = "价签模板根据商品编码查询商品信息", response = ProductPriceLabelDto.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ProductPriceLabelDto.class) })
    @RequestMapping(value = "baseinfo/product/getProductListByPref", method = RequestMethod.POST)
    ResponseEntity<ResultVO> getProductListByPref(HttpServletRequest request, @ApiParam(value = "价签模板详情信息对象", required = true) @Valid @RequestBody PriceLabelDto priceLabel);



    @ApiOperation(value = "价签模板修改商品打印状态", notes = "价签模板修改商品打印状态", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "baseinfo/product/updateProductPrintStatusByPref", method = RequestMethod.POST)
    ResponseEntity<ResultVO> updateProductPrintStatusByPref(HttpServletRequest request, @ApiParam(value = "价签模板详情信息对象", required = true) @Valid @RequestBody PriceLabelDto priceLabel);

    @ApiOperation(value = "根据价签编号获取单个价签模板详情信息", notes = "根据价签编号获取单个价签模板详情信息", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "baseinfo/product/getPriceLabelByPref", method = RequestMethod.POST)
    ResponseEntity<ResultVO> getPriceLabelByPref(HttpServletRequest request, @ApiParam(value = "价签模板详情信息对象", required = true) @Valid @RequestBody PriceLabelDto priceLabel);

    @ApiOperation(value = "机构绑定模板编号接口", notes = "机构绑定模板编号接口", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "baseinfo/product/configPriceLabel", method = RequestMethod.POST)
    ResponseEntity<ResultVO> configPriceLabel(HttpServletRequest request, @ApiParam(value = "价签模板详情信息对象", required = true) @Valid @RequestBody PriceLabelDto priceLabel);


    /**
     * 带条件分页查询
     * @return
     */
    @ApiOperation(value = "根据商品名称模糊查询商品列表", notes = "根据商品名称模糊查询商品列表", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/product/getProductsByProductName",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getProductsByProductName(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody ProductCommonQueryVo product);


    /**
     * 查询商品价格修改明细，分页展示
     * @param proPriceAdjustDto
     * @return
     */
    @ApiOperation(value = "查询商品价格修改明细接口", notes = "查询商品价格修改明细接口", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/product/getAdjustPriceListByPref", method = RequestMethod.POST)
    ResponseEntity<ResultVO> getAdjustPriceListByPref(HttpServletRequest request, @ApiParam(value = "商品售价调整信息实体", required = true) @Valid @RequestBody ProPriceAdjustDto proPriceAdjustDto);

    /**
     * @param updateRedisVo
     * @return
     */
    @ApiOperation(value = "刷新商品外码redis的key值", notes = "刷新商品外码redis的key值", response = ResultVO.class, tags={ "商品数据刷新工具接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/product/updateProductPharmacyPrefRedisKey",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> updateProductPharmacyPrefRedisKey(HttpServletRequest request, @ApiParam(value = "外码更新后的value", required = true) @Valid @RequestBody UpdateRedisVo updateRedisVo);

//    /**
//     * 删除重复商品fix1028bug
//     * @param refreshVo
//     * @return
//     */
//    @ApiOperation(value = "删除重复商品fix1028bug", notes = "删除重复商品fix1028bug", response = ResultVO.class, tags={ "商品数据刷新工具接口", })
//    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
//    @ResponseBody
//    @RequestMapping(value = "/baseinfo/product/rereshProductChongfu",method = RequestMethod.POST)
//    public ResponseEntity<ResultVO> rereshProductChongfu(HttpServletRequest request, @ApiParam(value = "外码更新后的value", required = true) @Valid @RequestBody ProductRefreshVo refreshVo);

    @ResponseBody
    @RequestMapping(value="/baseinfo/tjcheck/canOpenSwitch", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> firstCheckToUser(HttpServletRequest request, @RequestBody TjCheckQueryVo queryVo);

    /**
     * 功能描述: <br>匹配标准库id
     * 〈〉
     * @Param: [request, queryVo]
     * @Return: org.springframework.http.ResponseEntity<com.xyy.saas.common.util.ResultVO>
     */
    @ResponseBody
    @RequestMapping(value="/baseinfo/product/standardLibraryMatch", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> standardLibraryMatch(@RequestBody String organSign);


    /**
     * 功能描述: <br>匹配标准库id
     * 〈〉isDelete  1 表示先删除再初始化  0表示直接初始化
     * @Param: [request, queryVo]
     * @Return: org.springframework.http.ResponseEntity<com.xyy.saas.common.util.ResultVO>
     */
    @ResponseBody
    @RequestMapping(value="/baseinfo/product/standardLibraryInit", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> standardLibraryInit(@RequestBody String isDelete);

    /**
     * 查询商品自定义一二三级分类数据
     * @param
     * @return
     */
    @ApiOperation(value = "查询商品自定义一二三级分类数据接口", notes = "查询商品自定义一二三级分类数据接口", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/product/getproductType", method = RequestMethod.POST)
    ResponseEntity<ResultVO> getproductType(HttpServletRequest request);

    /**
     * 根据条件查询商品信息
     * @param request
     * @param productQueryVo
     * @return
     */
    @ResponseBody
    @RequestMapping(value="/baseinfo/product/getByPref", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getProductByPref(HttpServletRequest request, @ApiParam(value = "商品详情信息对象", required = true) @Valid @RequestBody ProductQueryVo productQueryVo);
    /**
     * 查询预警提醒信息
     * @return
     */
    @ResponseBody
    @ApiOperation(value = "查询预警提醒信息", notes = "查询预警提醒信息", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/warningInfo",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> warningInfo(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true)  @RequestBody ProductDto product);

    /**
     * 查询预警提醒信息
     * @return
     */
    @ResponseBody
    @ApiOperation(value = "根据机构信息查询业务必填信息", notes = "根据机构信息查询业务必填信息", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/baseinfo/product/getMustFieldByOrganSign",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getMustFieldByOrganSign(HttpServletRequest request,HttpServletResponse response);


    /**
     * 异常商品，批量匹配标准库id，并将匹配结果存入redis
     */
    @ResponseBody
    @ApiOperation(value = "批量匹配", notes = "批量匹配", response = ResultVO.class, tags={ "批量匹配API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/baseinfo/product/banchMatchStandLibrary",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> banchMatchStandLibrary(HttpServletRequest request, @Valid @RequestBody MatchProductVoDto matchProductVoDto);

    /**
     * 异常商品信息列表
     */
    @ResponseBody
    @ApiOperation(value = "异常商品信息列表", notes = "异常商品信息列表", response = ResultVO.class, tags={ "异常商品信息列表API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/baseinfo/product/getAbnormalProList",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getAbnormalProList(HttpServletRequest request, @Valid @RequestBody ProductVoDto productVoDto);

    /**
     * 标准库查询
     */
    @ResponseBody
    @ApiOperation(value = "标准库查询", notes = "标准库查询", response = ResultVO.class, tags={ "标准库查询API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/baseinfo/product/libraryQueryWeb",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> libraryQuery(HttpServletRequest request, @Valid @RequestBody BinnaryQueryDto vo);

    /**
     * 手动删除匹配成功的redis中商品信息
     */
    @ResponseBody
    @ApiOperation(value = "手动删除匹配成功的redis中商品信息", notes = "手动删除匹配成功的redis中商品信息", response = ResultVO.class, tags={ "手动删除匹配成功的redis中商品信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/baseinfo/product/deleteMatchedSuccess",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> deleteMatchedSuccess(HttpServletRequest request ,@ApiParam(value = "productDto" ,required=true ) @RequestBody @Valid ProductDto productDto);

    /**
     * 提交匹配成功的匹配结果
     */
    @ResponseBody
    @ApiOperation(value = "提交匹配成功的匹配结果", notes = "提交匹配成功的匹配结果", response = ResultVO.class, tags={ "提交匹配成功的匹配结果API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/baseinfo/product/saveMatchedSuccess",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> saveMatchedSuccess(HttpServletRequest request);

    /**
     * 批量匹配结果查询
     */
    @ResponseBody
    @ApiOperation(value = "批量匹配结果查询", notes = "批量匹配结果查询", response = ResultVO.class, tags={ "批量匹配API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/baseinfo/product/queryMatchedSuccessPage",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> queryMatchedSuccessPage(HttpServletRequest request, @ApiParam(value = "pageDto" ,required=true )@RequestBody  @Valid PageDto pageDto);

    /**
     * 校验商品信息唯一性
     */
    @ResponseBody
    @ApiOperation(value = "校验商品信息唯一性", notes = "校验商品信息唯一性", response = ResultVO.class, tags={ "校验商品信息唯一性API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/baseinfo/product/uniqueProduct",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> uniqueProduct(HttpServletRequest request, @ApiParam(value = "productDto" ,required=true ) @RequestBody @Valid ProductDto productDto);

    /**
     * 查询机构下自定义字段
     */
    @ResponseBody
    @ApiOperation(value = "查询机构下自定义字段", notes = "查询机构下自定义字段", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/baseinfo/product/customField",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> queryCustomField(HttpServletRequest request);

    /**
     * 查验商品是否强制追溯
     * @param request
     * @param prefList
     * @return
     */
    @ApiOperation(value = "查验商品是否强制追溯", notes = "查验商品是否强制追溯", response = ResultVO.class, tags={ "商品是否强制追溯API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/baseinfo/product/checkTraceProduct",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> checkTraceProduct(HttpServletRequest request, List<String> prefList);

    /**
     * 查询预警提醒信息
     * @return
     */
    @ResponseBody
    @ApiOperation(value = "根据机构信息查询业务必填信息", notes = "根据机构信息查询业务必填信息", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/baseinfo/product/getMustFieldByOrganSignLinyi",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getMustFieldByOrganSignLinyi(HttpServletRequest request,HttpServletResponse response);

    @ApiOperation(value = "查询药帮忙商品信息", notes = "查询药帮忙商品信息", response = ResultVO.class, tags={ "查询药帮忙商品信息", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/baseinfo/product/ybmProduct",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> convertYBMInfo(HttpServletRequest request,String data);

    @ApiOperation(value = "查询智鹿新增的商品通知中台", notes = "查询智鹿新增的商品通知中台", response = ResultVO.class, tags={ "查询智鹿新增的商品通知中台", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/baseinfo/product/noticeMiddle",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> noticeMiddle(HttpServletRequest request, @RequestBody String data);


    @ApiOperation(value = "同步智鹿单个商品通知中台", notes = "同步智鹿单个商品通知中台", response = ResultVO.class, tags={ "同步智鹿单个商品通知中台", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/baseinfo/product/syncSingleToMiddle",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> syncSingleToMiddle(HttpServletRequest request, @RequestBody String data);


    @ApiOperation(value = "同步智鹿所有商品通知中台", notes = "同步智鹿所有商品通知中台", response = ResultVO.class, tags={ "同步智鹿所有商品通知中台", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/baseinfo/product/syncAllToMiddle",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> syncAllToMiddle(HttpServletRequest request, @RequestBody String data);


    @ApiOperation(value = "查询智鹿医药配置", notes = "查询智鹿医药配置", response = ResultVO.class, tags={ "查询智鹿医药配置", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/checkSNOrgan",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> checkSNOrgan(HttpServletRequest request);

    @ApiOperation(value = "查询药帮忙商品最低价格", notes = "查询药帮忙商品最低价格", response = ResultVO.class, tags={ "查询药帮忙商品最低价格", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/queryYbmLowestPrice",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> queryYbmLowestPrice(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody YbmProductDto ybmProductDto);

    @ApiOperation(value = "查询药帮忙商品最低价格", notes = "查询药帮忙商品最低价格", response = ResultVO.class, tags={ "查询药帮忙商品最低价格", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/single/queryYbmLowestPriceV2",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> queryYbmLowestPriceV2(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody YbmProductDto ybmProductDto);

    @ApiOperation(value = "查询药帮忙商品最低价格（全省热销）", notes = "查询药帮忙商品最低价格", response = ResultVO.class, tags={ "查询药帮忙商品最低价格", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/single/queryYbmProvinceRankingV2",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> queryYbmLowestPriceByProvinceV2(HttpServletRequest request, @RequestBody YbmProductDto ybmProductDto);

    @ApiOperation(value = "导出药帮忙商品最低价格", notes = "查询药帮忙商品最低价格", response = ResultVO.class, tags={ "查询药帮忙商品最低价格", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/single/exportYbmLowestPriceV2",method = RequestMethod.POST)
    @ResponseBody
    public void exportYbmLowestPriceV2(HttpServletRequest request,HttpServletResponse response, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody ProductDto productDto);

    @ApiOperation(value = "导出药帮忙商品最低价格（全省热销）", notes = "查询药帮忙商品最低价格", response = ResultVO.class, tags={ "查询药帮忙商品最低价格", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/single/exportYbmProvinceRankingV2",method = RequestMethod.POST)
    @ResponseBody
    public void exportYbmLowestPriceByProvinceV2(HttpServletRequest request,HttpServletResponse response);

    /**
     * 带条件分页查询
     * @return
     */
    @ApiOperation(value = "首页动销数据查询", notes = "首页动销数据查询", response = ResultVO.class, tags={ "4.首页动销数据查询", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/single/queryMarketingAnalyseDataV2",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> queryMarketingAnalyseDataV2(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody ProductDto product);


    /**
     * 带条件分页查询
     * @return
     */
    @ApiOperation(value = "首页动销数据查询", notes = "首页动销数据查询", response = ResultVO.class, tags={ "4.首页动销数据查询", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/queryMarketingAnalyseData",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> queryMarketingAnalyseData(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody ProductDto product);

    /**
     * 带条件分页查询
     * @return
     */
    @ApiOperation(value = "首页动销数据灰度判断", notes = "首页动销数据灰度判断", response = ResultVO.class, tags={ "4.首页动销数据灰度判断", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/queryMarketingAnalyseFlag",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> queryMarketingAnalyseFlag(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody ProductDto product);


    @ApiOperation(value = "查询手动配置得药帮忙商品", notes = "查询手动配置得药帮忙商品", response = ResultVO.class, tags={ "查询手动配置得药帮忙商品", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/getYbmGoodsSpecialManual",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> getYbmGoodsSpecialManual(HttpServletRequest request, @ApiParam(value = "查询条件信息对象", required = true) @Valid @RequestBody YbmProductDto ybmProductDto);

    @ApiOperation(value = "门店获取标识码", notes = "门店获取标识码", response = ResultVO.class, tags={ "门店获取标识码", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/getDrugIdentCode",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> getDrugIdentCode(HttpServletRequest request);

    @ApiOperation(value = "期初门店标识码", notes = "期初门店标识码", response = ResultVO.class, tags={ "期初门店标识码", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/syncDrugIdentCode",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> syncDrugIdentCode(HttpServletRequest request);

    @ApiOperation(value = "POS更新药品标识码", notes = "POS更新药品标识码", response = ResultVO.class, tags={ "POS更新药品标识码", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/posUpdateDrugIdentCode",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> posUpdateDrugIdentCode(
            @RequestHeader("commonRequestModel") String commonRequestModelStr,
            @Valid @RequestBody PosDrugIdentCodeUpdateVo posUpdateVo);

    /**
     * 根据追溯码码查询69码
     * @param request
     * @return
     */
    @ResponseBody
    @ApiOperation(value = "根据追溯码码查询69码", notes = "根据追溯码码查询69码", response = ResultVO.class, tags={ "POS接口"})
    @ApiResponses(value = { @ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/queryMsfxByTraceCode", method = RequestMethod.POST)
    ResponseEntity<ResultVO> queryMsfxByTraceCode(HttpServletRequest request, String reqData);

}
