package com.xyy.saas.web.api.module.consult.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.api.SystemDictApi;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.consult.cores.api.ConsultPrescriptionApi;
import com.xyy.saas.consult.cores.api.ConsultRecordApi;
import com.xyy.saas.consult.cores.api.PatientBaseApi;
import com.xyy.saas.consult.cores.dto.ConsultPrescriptionDto;
import com.xyy.saas.consult.cores.dto.ConsultRecordDetailDto;
import com.xyy.saas.consult.cores.dto.ConsultRecordPrescriptionDto;
import com.xyy.saas.consult.cores.dto.PatientBaseDto;
import com.xyy.saas.product.core.api.ProductApi;
import com.xyy.saas.web.api.module.consult.model.ConsultPrescriptionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/member/consultPrescription")
@Api(value = "consultPrescription", description = "远程问诊第三方处方API")
public class ConsultPrescriptionApiController {

    @Reference(version = "0.0.2")
    private ConsultPrescriptionApi consultPrescriptionApi;

    @Reference(version = "0.0.2")
    private ConsultRecordApi consultRecordApi;

    @Reference(version = "0.0.2")
    private PatientBaseApi patientBaseApi;

    @Reference(version = "0.0.1")
    private ProductApi productApi;

    @Reference(version = "0.0.1")
    private SystemDictApi systemDictApi;


    @ApiOperation(value = "根据远程问诊id查询处方", notes = "根据远程问诊id查询处方", response = ConsultPrescriptionDto.class, tags = {"consultPrescription",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ConsultPrescriptionDto.class)})
    @RequestMapping(value = "/selectByConsultRecordId", method = RequestMethod.POST,produces =  "application/json",
            consumes = "application/json")
    ResponseEntity<ResultVO> selectByConsultRecordId(@RequestHeader(name = "organSign", required = true) String organSign,
                                                     @ApiParam(value = "远程问诊id", required = true) @RequestBody ConsultPrescriptionVo consultPrescriptionVo) {
        Long consultRecordId = consultPrescriptionVo.getConsultRecordId();
        if (consultRecordId == null || consultRecordId <= 0) {
            new ResponseEntity(new ResultVO(-1, "远程问诊id不能为空", false), HttpStatus.OK);
        }
        ConsultPrescriptionDto prescriptionDto = consultPrescriptionApi.selectByConsultRecordId(consultRecordId, organSign);
        if (prescriptionDto == null) {
            return new ResponseEntity(ResultVO.createError("处方不存在"), HttpStatus.OK);
        }
        //获取处方记录
        ConsultRecordDetailDto consultRecordDetailDto = consultRecordApi.selectLastestByPatientId(prescriptionDto.getPatientId(), organSign);
        PatientBaseDto patientDto = patientBaseApi.selectById(prescriptionDto.getPatientId(), organSign);
        prescriptionDto.setName(patientDto.getName());
        prescriptionDto.setSex(patientDto.getSex());
        prescriptionDto.setBirthday(patientDto.getBirthday());
        prescriptionDto.setAllergy(patientDto.getAllergy());
        prescriptionDto.setIdCard(patientDto.getIdCard());
        prescriptionDto.setTelephone(patientDto.getTelephone());
        if (consultRecordDetailDto == null){
            return new ResponseEntity(ResultVO.createSuccess(prescriptionDto), HttpStatus.OK);
        }
        //组装年龄和处方来源
        ConsultRecordPrescriptionDto consultRecordPrescriptionDto = new ConsultRecordPrescriptionDto();
        BeanUtils.copyProperties(prescriptionDto, consultRecordPrescriptionDto);
        consultRecordPrescriptionDto.setAge(consultRecordDetailDto.getAge());
        consultRecordPrescriptionDto.setSource(consultRecordDetailDto.getSource());
        return new ResponseEntity(ResultVO.createSuccess(consultRecordPrescriptionDto), HttpStatus.OK);
    }
}
