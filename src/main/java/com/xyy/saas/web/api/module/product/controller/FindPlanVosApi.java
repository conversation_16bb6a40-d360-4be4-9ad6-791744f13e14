/**
 * NOTE: This class is auto generated by the swagger code generator program (2.2.3).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.web.api.module.product.model.InventoryPlanVo;
import com.xyy.saas.web.api.module.product.model.InventoryPlanVoList;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-29T18:34:31.623+08:00")
@RequestMapping("/product")
@Api(value = "findPlanVos", description = "the findPlanVos API")
public interface FindPlanVosApi {

    @ApiOperation(value = "查询所有盘点计划单", notes = "查询所有盘点计划单", response = InventoryPlanVoList.class, tags={ "InventoryPlanVo", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "操作成功", response = InventoryPlanVoList.class) })
    
    @RequestMapping(value = "/findPlanVos",
        produces = { "application/xml", "application/json" }, 
        consumes = { "application/json", "application/xml" },
        method = RequestMethod.POST)
    ResponseEntity<InventoryPlanVoList> findPlanVos(@ApiParam(value = "盘点计划单Vo", required = true) @Valid @RequestBody InventoryPlanVo inventoryPlanVo);

}
