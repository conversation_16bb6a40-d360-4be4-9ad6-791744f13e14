package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.web.api.module.product.model.TjCheckQueryVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

/**
 * @ClassName ProductTjCheckApi
 * @Description 天津静海过检需求接口api
 * <AUTHOR>
 * @Date 2019/10/25 12:55
 * @Version 1.0
 **/
@RequestMapping("/product")
@Api(value = "天津静海过检需求接口api", description = "天津静海过检需求接口api")
public interface ProductTjCheckApi {
    /**
     * 根据机构号查询能否打开天津过检小窗口
     * @param request
     * @param queryVo
     * @return
     */
    @ApiOperation(value = "根据机构号判断是否可以打开天津静海过检的开关", notes = "根据机构号判断是否可以打开天津静海过检的开关", response = ResultVO.class, tags={ "天津静海过检需求接口api", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value="/tjcheck/canOpenSwitch", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> firstCheckToUser(HttpServletRequest request, @RequestBody TjCheckQueryVo queryVo);
}
