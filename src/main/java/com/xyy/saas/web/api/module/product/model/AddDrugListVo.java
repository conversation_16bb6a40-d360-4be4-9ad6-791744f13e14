package com.xyy.saas.web.api.module.product.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(value = "添加商品标准库列表", description = "添加商品标准库列表")
public class AddDrugListVo implements Serializable {

    private static final long serialVersionUID = 4882101235819900010L;
    /**
     * 通用名(对应字典表dictionary_name)
     */
    @ApiModelProperty(
            value = "通用名",
            name = "commonName"
    )
    private String commonName;

    @ApiModelProperty(
            value = "页码",
            name = "page"
    )
    private Integer page;
    @ApiModelProperty(
            value = "每页数据量",
            name = "rows"
    )
    private Integer rows;

    @ApiModelProperty(
            value = "类型 0:标准库 1:商品库",
            name = "type"
    )
    private Integer type;

    @ApiModelProperty(
            value = "适应症",
            name = "symptom"
    )
    private String symptom;

    @ApiModelProperty(
            value = "适应病种编号",
            name = "symptomPref"
    )
    private String symptomPref;

    public String getSymptomPref() {
        return symptomPref;
    }

    public void setSymptomPref(String symptomPref) {
        this.symptomPref = symptomPref;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getSymptom() {
        return symptom;
    }

    public void setSymptom(String symptom) {
        this.symptom = symptom;
    }

    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }
}
