package com.xyy.saas.web.api.module.product.model;

/**
 * @ClassName AdjustBaseInfoVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/1/14 13:30
 * @Version 1.0
 **/
public class AdjustBaseInfoVo {

    private Long id;//单据id，
    private String pref;//":"单据编号",
    private String adjustOrganSign;//":"调价部门编号",
    private String adjustOrganSignName;//":"调价部门名称",
    private String createUserName;//":"创建人名称",
    private String createTimeStr;//":"创建时间，格式yyyy-MM-dd HH:mm:ss",
    private String adjustReason;//":"调价原因",
    private Integer applyState;//":" -------审核状态 3:拒绝 2:同意 1：审核中
    private Integer roleId;//":1001,     ---------角色id
    private String currentAgent;//":"当前代办人",
    private String checkId;//":"审批流id"
    private Byte canApprove;//1代表可以审核，2代表不可审核

    public String getApproveOrganSignName() {
        return approveOrganSignName;
    }

    public void setApproveOrganSignName(String approveOrganSignName) {
        this.approveOrganSignName = approveOrganSignName;
    }

    private String approveOrganSignName;//当前代办部门

    public Byte getApproveOrganSign() {
        return approveOrganSign;
    }

    public void setApproveOrganSign(Byte approveOrganSign) {
        this.approveOrganSign = approveOrganSign;
    }

    private Byte approveOrganSign;

    public Byte getCanApprove() {
        return canApprove;
    }

    public void setCanApprove(Byte canApprove) {
        this.canApprove = canApprove;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    public String getAdjustOrganSign() {
        return adjustOrganSign;
    }

    public void setAdjustOrganSign(String adjustOrganSign) {
        this.adjustOrganSign = adjustOrganSign;
    }

    public String getAdjustOrganSignName() {
        return adjustOrganSignName;
    }

    public void setAdjustOrganSignName(String adjustOrganSignName) {
        this.adjustOrganSignName = adjustOrganSignName;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getCreateTimeStr() {
        return createTimeStr;
    }

    public void setCreateTimeStr(String createTimeStr) {
        this.createTimeStr = createTimeStr;
    }

    public String getAdjustReason() {
        return adjustReason;
    }

    public void setAdjustReason(String adjustReason) {
        this.adjustReason = adjustReason;
    }

    public Integer getApplyState() {
        return applyState;
    }

    public void setApplyState(Integer applyState) {
        this.applyState = applyState;
    }

    public Integer getRoleId() {
        return roleId;
    }

    public void setRoleId(Integer roleId) {
        this.roleId = roleId;
    }

    public String getCurrentAgent() {
        return currentAgent;
    }

    public void setCurrentAgent(String currentAgent) {
        this.currentAgent = currentAgent;
    }

    public String getCheckId() {
        return checkId;
    }

    public void setCheckId(String checkId) {
        this.checkId = checkId;
    }
}
