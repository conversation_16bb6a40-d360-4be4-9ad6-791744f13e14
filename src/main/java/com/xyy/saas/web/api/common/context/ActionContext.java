package com.xyy.saas.web.api.common.context;

import com.xyy.saas.common.dto.CommonRequestModel;

import java.util.HashMap;
import java.util.Map;

/**
 * 请求的数据上下文
 */
public class ActionContext {

	/**
	 * data业务参数
	 */
	private String data;

	/**
	 * 登录后颁发的令牌token
	 */
	private String token;

	/**
	 * 输入参数签名
	 */
	private String clientSign;
	/**
	 * 服务签名结果
	 */
	private String serverSign;
	/**
	 * 客户端请求所有参数
	 */
	private String clientParams;

	/**
	 * 客户端
	 */
	private String client;

	/**
	 * 版本号
	 */
	private String version;
	//时间戳
//	private long timestamp;
	private String urlMatcher;
	private String method;
	private String ip;
	private String server;
	private String url;
	private String query;
	private String refer;
	private String userAgent;
	private AppActionContext appContext;
	private long requestTime;
	private long responseTime;
	//经过api网关及服务总耗时
	private long timeCost;
	//调用服务耗时
	private long serviceTimeConsuming;
	private String responseResult;
	/**
	 * 异常信息
	 */
	private String errorMsg;

	private Map<String, String> params = new HashMap<String, String>();
	private Map<String, String> headers = new HashMap<String, String>();
	private Map<String, String> cookies = new HashMap<String, String>();
	private Map<String, Object> contextMap = new HashMap<String, Object>();
	//用户登录态信息
	private CommonRequestModel model;

	public ActionContext() {
	}

	public String getUrlMatcher() {
		return urlMatcher;
	}

	public void setUrlMatcher(String urlMatcher) {
		this.urlMatcher = urlMatcher;
	}

	public String getMethod() {
		return method;
	}

	public void setMethod(String method) {
		this.method = method;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public String getServer() {
		return server;
	}

	public void setServer(String server) {
		this.server = server;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getQuery() {
		return query;
	}

	public void setQuery(String query) {
		this.query = query;
	}

	public String getRefer() {
		return refer;
	}

	public void setRefer(String refer) {
		this.refer = refer;
	}

	public String getUserAgent() {
		return userAgent;
	}

	public void setUserAgent(String userAgent) {
		this.userAgent = userAgent;
	}

	public AppActionContext getAppContext() {
		return appContext;
	}

	public void setAppContext(AppActionContext appContext) {
		this.appContext = appContext;
	}

	public long getRequestTime() {
		return requestTime;
	}

	public void setRequestTime(long requestTime) {
		this.requestTime = requestTime;
	}

	public long getResponseTime() {
		return responseTime;
	}

	public void setResponseTime(long responseTime) {
		this.responseTime = responseTime;
	}

	public long getTimeCost() {
		return timeCost;
	}

	public void setTimeCost(long timeCost) {
		this.timeCost = timeCost;
	}

	public Map<String, String> getParams() {
		return params;
	}

	public void setParams(String key, String value) {
		this.params.put(key, value);
	}

	public Map<String, String> getHeaders() {
		return headers;
	}

	public void setHeaders(String key, String value) {
		this.headers.put(key, value);
	}

	public Map<String, String> getCookies() {
		return cookies;
	}

	public void setCookies(String key, String value) {
		this.cookies.put(key, value);
	}

	public Object getContext(String key) {
		return contextMap.get(key);
	}

	public void setContext(String key, Object value) {
		this.contextMap.put(key, value);
	}

	public String getData() {
		return data;
	}

	public void setData(String data) {
		this.data = data;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}


	public String getClient() {
		return client;
	}

	public void setClient(String client) {
		this.client = client;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getResponseResult() {
		return responseResult;
	}

	public void setResponseResult(String responseResult) {
		this.responseResult = responseResult;
	}

	public CommonRequestModel getModel() {
		return model;
	}

	public void setModel(CommonRequestModel model) {
		this.model = model;
	}

	public long getServiceTimeConsuming() {
		return serviceTimeConsuming;
	}

	public void setServiceTimeConsuming(long serviceTimeConsuming) {
		this.serviceTimeConsuming = serviceTimeConsuming;
	}

	public String getClientParams() {
		return clientParams;
	}

	public void setClientParams(String clientParams) {
		this.clientParams = clientParams;
	}

	public String getServerSign() {
		return serverSign;
	}

	public void setServerSign(String serverSign) {
		this.serverSign = serverSign;
	}

	public String getClientSign() {
		return clientSign;
	}

	public void setClientSign(String clientSign) {
		this.clientSign = clientSign;
	}

	public String getErrorMsg() {
		return errorMsg;
	}

	public void setErrorMsg(String errorMsg) {
		this.errorMsg = errorMsg;
	}
}
