package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.web.api.module.product.model.OperationGuideReadRecordVo;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T18:13:39.358+08:00")
@RequestMapping("/operationGuide")
@Api(value = "operationGuide", description = "操作指引相关接口")
public interface OperationGuideApi {

    @ApiOperation(value = "是否需要显示操作指引", notes = "是否需要显示操作指引", response = ResultVO.class, tags={ "是否需要显示操作指引", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/needShowOperationGuide")
    ResponseEntity<ResultVO> needShowOperationGuide(HttpServletRequest request, @ApiParam(value = "是否需要显示操作指引", required = true) @RequestBody OperationGuideReadRecordVo dto);

    @ApiOperation(value = "新增操作指引已读记录", notes = "新增操作指引已读记录", response = ResultVO.class, tags={ "新增操作指引已读记录", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @PostMapping(value = "/addOperationGuide")
    ResponseEntity<ResultVO> addOperationGuide(HttpServletRequest request, @ApiParam(value = "新增操作指引已读记录", required = true) @RequestBody OperationGuideReadRecordVo dto);


}
