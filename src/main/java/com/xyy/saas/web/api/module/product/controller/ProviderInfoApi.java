package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.supplier.dto.ProviderVoDto;
import com.xyy.saas.web.api.common.config.annotition.RepeatSubmitValidation;
import com.xyy.saas.web.api.module.product.model.*;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@Api(value = "4.0供应商基本信息API接口", description = "4.0供应商基本信息API接口", tags={ "4.0供应商基本信息API接口", })
@RequestMapping("/product")
public interface ProviderInfoApi {


    @ApiOperation(value = "供应商新增数据信息回显接口", notes = "供应商新增数据信息回显接口", response = ResultVO.class, tags={ "4.0供应商基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/provider/toAdd",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> toAddProvider(HttpServletRequest request, Model model);

    @ApiOperation(value = "供应商保存和编辑提交接口", notes = "供应商保存和编辑提交接口", response = ResultVO.class, tags={ "4.0供应商基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/provider/save", method = RequestMethod.POST)
    @RepeatSubmitValidation(resultType = 4)
    public ResponseEntity<ResultVO> addOrUpdate(HttpServletRequest request, @ApiParam(value = "供应商查询对象", required = true) @Valid @RequestBody ProviderVoDto provider);

    @ApiOperation(value = "供应商编辑数据信息回显接口", notes = "供应商编辑数据信息回显接口", response = ProviderVoDto.class, tags={ "4.0供应商基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ProviderVoDto.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/provider/toUpdate",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getProviderInfoById(HttpServletRequest request,@ApiParam(value = "供应商查询对象" ,required=true )  @Valid @RequestBody ProviderQueryVo providerQueryVo);

    @ApiOperation(value = "供应商删除接口", notes = "供应商删除接口", response = ResultVO.class, tags={ "4.0供应商基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/provider/delete",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> deleteProviderInfoById(HttpServletRequest request,@ApiParam(value = "供应商查询对象" ,required=true )  @Valid @RequestBody ProviderQueryVo providerQueryVo);

    @ApiOperation(value = "供应商基础信息请求数据回显接口", notes = "供应商基础信息请求数据回显接口", response = ResultVO.class, tags={ "4.0供应商基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/provider/list",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> toList(HttpServletRequest request,Model model);

    @ApiOperation(value = "供应商基础信息查询列表接口", notes = "供应商基础信息查询列表接口", response = ResultVO.class, tags={ "4.0供应商基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/baseinfo/provider/query",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> queryProvider(HttpServletRequest request,@ApiParam(value = "供应商查询对象" ,required=true )  @Valid @RequestBody ProviderQueryVo providerQueryVo);

    @ApiOperation(value = "供应商基础信息验证存在接口", notes = "供应商基础信息验证存在接口", response = ResultVO.class, tags={ "4.0供应商基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @RequestMapping(value = "/baseinfo/provider/verify",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> verifyProvider(HttpServletRequest request,@ApiParam(value = "供应商查询对象" ,required=true )  @Valid @RequestBody ProviderQueryVo providerQueryVo);

    @ApiOperation(value = "供应商列表导出excel接口", notes = "供应商列表导出excel接口", response = ResultVO.class, tags={ "4.0供应商基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功") })
    @RequestMapping(value = "/baseinfo/provider/exportExcel",method = RequestMethod.POST)
    public void exportExcelProvider(HttpServletRequest request, HttpServletResponse response,
                                    @RequestParam(required = false) String name,
                                    @RequestParam(required = false) String ids,
                                    @RequestParam(required = false) Byte used);

    @ApiOperation(value = "供应商编辑导出excel接口", notes = "供应商编辑导出excel接口", response = ResultVO.class, tags={ "4.0供应商基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功") })
    @RequestMapping(value = "/baseinfo/provider/exportOneProvider",method = RequestMethod.POST)
    public void exportExcelOneProduct(HttpServletRequest request, HttpServletResponse response, ProviderVoDto providerBaseinfoVo);

    /**
     * 检查是否开启了快捷审核 且是否有审核人
     * @param request
     * @return       -1：未开启快捷审核
     *               -2：首营品种一审没有审核人信息
     *               -3：首营品种二审没有审核人信息
     *               -4：开启了自动审核
     *               0：开启了快捷审核且符合条件
     */
    @ApiOperation(value = "检查是否开启了快捷审核 且是否有审核人", notes = "检查是否开启了快捷审核 且是否有审核人", response = ResultVO.class, tags={ "4.0供应商基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/provider/approveCheck", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> checkselfMotion(HttpServletRequest request);

    @ApiOperation(value = "初始化小药药供应商接口", notes = "初始化小药药供应商接口", response = ResultVO.class, tags={ "4.0供应商基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value="/baseinfo/provider/initXYYProvider",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> initXYYProvider();

    @ApiOperation(value = "供应商是否启用接口", notes = "供应商是否启用接口", response = ResultVO.class, tags={ "4.0供应商基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/provider/resertIsUsed",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> resertIsUsed(HttpServletRequest request,@ApiParam(value = "供应商查询对象" ,required=true )  @Valid @RequestBody ProviderQueryVo providerQueryVo);

    /**
     * 刷新数据需求脚本，按照机构号批量刷新单位，商品系统类型，剂型id
     * @param request
     * @param refreshDataVo
     * @return
     */
    @ApiOperation(value = "根据机构号修复存在审核主表数据，但是不存在审核详情的数据", notes = "根据机构号修复存在审核主表数据，但是不存在审核详情的数据", response = ResultVO.class, tags={ "4.0商品基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/provider/refreshApproveDetailByOrganSigns",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> refreshApproveDetailByOrganSigns(HttpServletRequest request,@ApiParam(value = "请求信息对象" ,required=true )  @Valid @RequestBody RefreshDataVo refreshDataVo);

    @ApiOperation(value = "供应商列表新导出excel接口", notes = "供应商列表新导出excel接口", response = ResultVO.class, tags={ "4.0供应商基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功") })
    @RequestMapping(value = "/baseinfo/provider/exportExcelNew",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> exportExcelProviderNew(HttpServletRequest request, HttpServletResponse response,@ApiParam(value = "供应商查询对象" ,required=true )  @Valid @RequestBody ProviderQueryExportVo providerQueryExportVo);

    /**
     * 删除重复供应商fix1028bug
     * @param refreshVo
     * @return
     */
    @ApiOperation(value = "删除重复供应商fix1028bug", notes = "删除重复供应商fix1028bug", response = ResultVO.class, tags={ "商品数据刷新工具接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/provider/rereshProviderChongfu",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> rereshProviderChongfu(HttpServletRequest request,@ApiParam(value = "外码更新后的value" ,required=true )  @Valid @RequestBody ProductRefreshVo refreshVo);

    @ApiOperation(value = "供应商不分页查询接口", notes = "供应商不分页查询接口", response = ResultVO.class, tags={ "4.0供应商基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功") })
    @RequestMapping(value = "/baseinfo/provider/queryNoPage",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> queryNoPage(HttpServletRequest request,@ApiParam(value = "供应商查询对象" ,required=true )  @Valid @RequestBody ProviderQueryVo providerQueryVo);

    @ApiOperation(value = "供应商查询资质过期数量和近效期数量", notes = "供应商查询资质过期数量和近效期数量", response = ResultVO.class, tags={ "4.0供应商基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功") })
    @ApiImplicitParams({
            @ApiImplicitParam(name="organSign", value="机构标识", required=true, paramType="header", dataType = "string")
    })
    @RequestMapping(value = "/baseinfo/provider/queryEffectiveAndExpireCount",method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> queryEffectiveAndExpireCount(HttpServletRequest request);

    @ApiOperation(value = "删除供应商销售人员", notes = "删除供应商销售人员", response = ResultVO.class, tags={ "4.0供应商基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/provider/deleteProviderSales",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> deleteProviderSalesById(HttpServletRequest request,@ApiParam(value = "id" ,required=true )  Long id);

    @ApiOperation(value = "根据条件查询供应商销售人员", notes = "根据条件查询供应商销售人员", response = ResultVO.class, tags={ "4.0供应商基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/provider/getProviderSales",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getProviderSales(@RequestHeader(value = "organSign", required = true) String organSign,@ApiParam(value = "供应商查询对象" ,required=true )  @Valid @RequestBody ProviderQueryVo providerQueryVo);


    @ApiOperation(value = "供应商销售人员刷数据", notes = "供应商销售人员刷数据", response = ResultVO.class, tags={ "4.0供应商基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/provider/refreshProviderSales",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> refreshProviderSales(Integer beginId,Integer endId);

	@ApiOperation(value = "供应商经营范围拆分刷数据", notes = "供应商经营范围拆分刷数据", response = ResultVO.class, tags={ "4.0供应商基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/provider/refreshQualificationInfos",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> refreshQualificationInfos(Integer beginId,Integer endId);

    //<editor-fold desc="码上放心平台">  add by zhuzc
    @ApiOperation(value = "码上放心-获取开关标识", notes = "码上放心-获取开关标识", response = ResultVO.class, tags={ "4.0供应商基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/provider/getEnableMsfx",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getEnableMsfx(@RequestHeader(value = "organSign", required = true) String organSign);

    @ApiOperation(value = "码上放心-码上放心机构标识", notes = "码上放心-码上放心机构标识", response = ResultVO.class, tags={ "4.0供应商基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/provider/getMsfxOrganSignInfo",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> getMsfxOrganSignInfo(@RequestHeader(value = "organSign", required = true) String organSign, @ApiParam(value = "码上放心机构标识-查询VO" ,required=true ) @Valid @RequestBody MsfxOrganSignInfoQueryReqVo msfxOrganSignInfoQueryReqVo);

    @ApiOperation(value = "码上放心-配合Apollo刷所有指定的机构", notes = "码上放心-配合Apollo刷所有指定的机构", response = ResultVO.class, tags={ "4.0供应商基本信息API接口", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class) })
    @ResponseBody
    @RequestMapping(value = "/baseinfo/provider/refreshProviderMsfxInfoByApollo",method = RequestMethod.POST)
    public ResponseEntity<ResultVO> refreshProviderMsfxInfoByApollo();
    //</editor-folder>
}

