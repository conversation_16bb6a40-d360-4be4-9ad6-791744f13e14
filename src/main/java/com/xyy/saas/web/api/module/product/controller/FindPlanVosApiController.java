package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.web.api.module.product.model.InventoryPlanVo;
import com.xyy.saas.web.api.module.product.model.InventoryPlanVoList;
import io.swagger.annotations.ApiParam;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-08-29T18:34:31.623+08:00")

@Controller
public class FindPlanVosApiController implements FindPlanVosApi {



    public ResponseEntity<InventoryPlanVoList> findPlanVos(@ApiParam(value = "盘点计划单Vo" ,required=true )  @Valid @RequestBody InventoryPlanVo inventoryPlanVo) {
        // do some magic!
        return new ResponseEntity<InventoryPlanVoList>(HttpStatus.OK);
    }

}
