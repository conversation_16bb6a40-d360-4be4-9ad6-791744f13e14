package com.xyy.saas.web.api.module.medical.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 医保服务订单查询条件
 * <AUTHOR>
 * @date 2021/08/30
 */
@Data
public class MedicalInsuranceOrderCondition implements Serializable {
    private static final long serialVersionUID = -1072822588904992461L;
    /**
     * 机构号
     */
    private String organSign;
    /**
     * 支付时间-开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date payTimeFrom;
    /**
     * 支付时间-结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date payTimeTo;
    /**
     * 操作人
     */
    private String employeeName;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 订单状态
     */
    private Integer orderStatus;
    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 分页大小
     */
    private Integer pageSize;
}
