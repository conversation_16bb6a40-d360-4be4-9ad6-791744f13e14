package com.xyy.saas.web.api.module.utils;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class ExecServiceTask {
    private static final int nThreads = Runtime.getRuntime().availableProcessors()+1;
    private static ExecutorService executor=
            Executors.newFixedThreadPool(nThreads);
    public static void execute(Runnable runnable){
        executor.execute(runnable);
    }
}
