package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.supplier.dto.FirstCheckProviderDto;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@Api(value = "4.0首营供应商基本信息API接口", description = "4.0首营供应商基本信息API接口")
@RequestMapping("/product")
public interface FirstProviderInfoApi {


    @ApiOperation(value = "首营供应商信息查询列表接口", notes = "首营供应商信息查询列表接口", response = ResultVO.class, tags = {"4.0首营供应商信息API接口",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class)})
    @RequestMapping(value = "/baseinfo/approver/queryProviderList", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> queryFirstProviderList(HttpServletRequest request, @ApiParam(value = "首营供应商查询对象", required = true) @Valid @RequestBody FirstCheckProviderDto firstProvider);

    @ApiOperation(value = "首营供应商详情接口", notes = "首营供应商详情接口", response = ResultVO.class, tags = {"4.0首营供应商信息API接口",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class)})
    @RequestMapping(value = "/baseinfo/approver/approval", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> getFirstProvider(HttpServletRequest request, @ApiParam(value = "首营供应商查询对象", required = true) @Valid @RequestBody FirstCheckProviderDto firstProvider);

    @ApiOperation(value = "首营供应商审批接口", notes = "首营供应商审批接口", response = ResultVO.class, tags = {"4.0首营供应商信息API接口",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = ResultVO.class)})
    @RequestMapping(value = "/baseinfo/approver/printProvider", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<ResultVO> printProvider(HttpServletRequest request, @ApiParam(value = "首营供应商查询对象", required = true) @Valid @RequestBody FirstCheckProviderDto firstProvider);
}