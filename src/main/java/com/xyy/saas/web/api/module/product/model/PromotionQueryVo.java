package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName PromotionQueryVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/8/26 17:01
 * @Version 1.0
 **/
@ApiModel("促销查询相关信息类")
public class PromotionQueryVo {

    //特价除外：1表示排除特价商品，不填表示不排除特价商品
    private Byte isSpecial;
    //当前页
    @JsonProperty("page")
    private Integer page;
    //每页显示数量
    @JsonProperty("rows")
    private Integer rows;
    //混合查询字段（通用名称，商品名称，助记码）
    @JsonProperty("mixedQuery")
    private String mixedQuery;
    //商品7大类,填写分类id
    @JsonProperty("systemTypeId")
    private Integer systemTypeId;
    //自定义分类，填写分类id
    @JsonProperty("customTypeId")
    private Integer customTypeId;
    //abc分类，填写分类id
    @JsonProperty("abcTypeId")
    private Integer abcTypeId;
    //货位，填写货位id
    @JsonProperty("positionId")
    private Integer positionId;
    //最小毛利率
    @JsonProperty("minGrossMargin")
    private BigDecimal minGrossMargin;
    //最大毛利率
    @JsonProperty("maxGrossMargin")
    private BigDecimal maxGrossMargin;
    //生产厂家
    @JsonProperty("manufacturer")
    private String manufacturer;
    //排除的商品列表,商品
    @JsonProperty("exclusivePrefs")
    private List<String> exclusivePrefs;
    //包含的商品列表,商品
    @JsonProperty("includePrefs")
    private List<String> includePrefs;
    //1：不展示库存为零的商品， 0：展示库存为零的商品
    private Byte stockNumberYn;
    //1：不展示禁用商品， 0：展示禁用商品
    private Byte usedYn;


    public Byte getUsedYn() {
        return usedYn;
    }

    public void setUsedYn(Byte usedYn) {
        this.usedYn = usedYn;
    }


    public Byte getStockNumberYn() {
        return stockNumberYn;
    }

    public void setStockNumberYn(Byte stockNumberYn) {
        this.stockNumberYn = stockNumberYn;
    }
    //    //机构唯一标识
//    @JsonProperty("organSign")
//    private String organSign;

//    @ApiModelProperty(value = "机构唯一标识")
//    public String getOrganSign() {
//        return organSign;
//    }
//
//    public void setOrganSign(String organSign) {
//        this.organSign = organSign;
//    }

    @ApiModelProperty(value = "包含的商品列表,商品内码集合")
    public List<String> getIncludePrefs() {
        return includePrefs;
    }

    public void setIncludePrefs(List<String> includePrefs) {
        this.includePrefs = includePrefs;
    }

    @ApiModelProperty(value = "排除的商品列表,商品内码集合")
    public List<String> getExclusivePrefs() {
        return exclusivePrefs;
    }

    public void setExclusivePrefs(List<String> exclusivePrefs) {
        this.exclusivePrefs = exclusivePrefs;
    }

    @ApiModelProperty(value = "混合查询字段（通用名称，商品名称，助记码）")
    public String getMixedQuery() {
        return mixedQuery;
    }

    public void setMixedQuery(String mixedQuery) {
        this.mixedQuery = mixedQuery;
    }

    @ApiModelProperty(value = "商品7大类,填写分类id")
    public Integer getSystemTypeId() {
        return systemTypeId;
    }

    public void setSystemTypeId(Integer systemTypeId) {
        this.systemTypeId = systemTypeId;
    }

    @ApiModelProperty(value = "自定义分类，填写分类id")
    public Integer getCustomTypeId() {
        return customTypeId;
    }

    public void setCustomTypeId(Integer customTypeId) {
        this.customTypeId = customTypeId;
    }

    @ApiModelProperty(value = "abc分类，填写分类id")
    public Integer getAbcTypeId() {
        return abcTypeId;
    }

    public void setAbcTypeId(Integer abcTypeId) {
        this.abcTypeId = abcTypeId;
    }

    @ApiModelProperty(value = "货位，填写货位id")
    public Integer getPositionId() {
        return positionId;
    }

    public void setPositionId(Integer positionId) {
        this.positionId = positionId;
    }

    @ApiModelProperty(value = "最小毛利率")
    public BigDecimal getMinGrossMargin() {
        return minGrossMargin;
    }

    public void setMinGrossMargin(BigDecimal minGrossMargin) {
        this.minGrossMargin = minGrossMargin;
    }

    @ApiModelProperty(value = "最大毛利率")
    public BigDecimal getMaxGrossMargin() {
        return maxGrossMargin;
    }

    public void setMaxGrossMargin(BigDecimal maxGrossMargin) {
        this.maxGrossMargin = maxGrossMargin;
    }

    @ApiModelProperty(value = "生产厂家")
    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    @ApiModelProperty(value = "当前页")
    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    @ApiModelProperty(value = "每页显示数量")
    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    @ApiModelProperty(value = "特价除外：1表示排除特价商品，不填表示不排除特价商品")
    public Byte getIsSpecial() {
        return isSpecial;
    }

    public void setIsSpecial(Byte isSpecial) {
        this.isSpecial = isSpecial;
    }
}
