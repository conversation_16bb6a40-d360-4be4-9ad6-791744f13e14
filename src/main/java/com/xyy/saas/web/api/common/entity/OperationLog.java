package com.xyy.saas.web.api.common.entity;

import java.util.Date;

public class OperationLog {

    /**
     * 日志id
     */
    private Integer id;

    /**
     * 机构号
     */
    private String organSign;

    /**
     * 操作时间
     */
    private Date operationTime;

    /**
     * 客户端版本号
     */
    private String version;

    /**
     * 请求mac地址
     */
    private String mac;

    /**
     * 请求ip
     */
    private String ip;

    /**
     * 请求地址
     */
    private String requestPath;

    /**
     * 员工id
     */
    private String employeeId;

    /**
     * 请求总共耗时
     */
    private String timeConsuming;

    /**
     * 异常信息
     */
    private String errorMsg;

    /**
     * 响应结果
     */
    private String responseResult;

    /**
     * 客户端
     */
    private String client;

    /**
     * 服务签名后sign
     */
    private String serverSign;

    /**
     * 公共参数params
     */
    private String clientParams;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public Date getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(Date operationTime) {
        this.operationTime = operationTime;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getRequestPath() {
        return requestPath;
    }

    public void setRequestPath(String requestPath) {
        this.requestPath = requestPath;
    }

    public String getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }

    public String getTimeConsuming() {
        return timeConsuming;
    }

    public void setTimeConsuming(String timeConsuming) {
        this.timeConsuming = timeConsuming;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getResponseResult() {
        return responseResult;
    }

    public void setResponseResult(String responseResult) {
        this.responseResult = responseResult;
    }

    public String getClient() {
        return client;
    }

    public void setClient(String client) {
        this.client = client;
    }

    public String getServerSign() {
        return serverSign;
    }

    public void setServerSign(String serverSign) {
        this.serverSign = serverSign;
    }

    public String getClientParams() {
        return clientParams;
    }

    public void setClientParams(String clientParams) {
        this.clientParams = clientParams;
    }
}
