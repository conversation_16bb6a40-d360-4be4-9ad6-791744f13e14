package com.xyy.saas.web.api.module.product.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * @ClassName DrugStandardVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/11/16 12:34
 * @Version 1.0
 **/
@ApiModel(value = "药监匹配地区对象", description = "药监匹配地区对象")
public class DrugStandardVo  implements Serializable {
    @ApiModelProperty(value = "中心品种编码")
    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    @ApiModelProperty(value = "品种名称")
    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    @ApiModelProperty(value = "生产厂商")
    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    @ApiModelProperty(value = "产地")
    public String getProducingArea() {
        return producingArea;
    }

    public void setProducingArea(String producingArea) {
        this.producingArea = producingArea;
    }

    @ApiModelProperty(value = "执行标准")
    public String getStandardsImpl() {
        return standardsImpl;
    }

    public void setStandardsImpl(String standardsImpl) {
        this.standardsImpl = standardsImpl;
    }

    @ApiModelProperty(value = "统一社会信用代码")
    public String getUnionSocialcreditCode() {
        return unionSocialcreditCode;
    }

    public void setUnionSocialcreditCode(String unionSocialcreditCode) {
        this.unionSocialcreditCode = unionSocialcreditCode;
    }

    @ApiModelProperty(value = "更新时间")
    public String getUpdateTimeStr() {
        return updateTimeStr;
    }

    public void setUpdateTimeStr(String updateTimeStr) {
        this.updateTimeStr = updateTimeStr;
    }

    /**
     * 中心编码
     */
    @JsonProperty("pref")
    private String pref;

    /**
     * 中心名称
     */
    @JsonProperty("commonName")
    private String commonName;
    /**
     * 生产厂商
     */
    @JsonProperty("manufacturer")
    private String manufacturer;
    /**
     * 产地
     */
    @JsonProperty("producingArea")
    private String producingArea;
    /**
     * 执行标准
     */
    @JsonProperty("standardsImpl")
    private String standardsImpl;
    /**
     * 统一社会信用代码
     */
    @JsonProperty("unionSocialcreditCode")
    private String unionSocialcreditCode;
    /**
     * 更新时间：2020-11-06 23:59:59
     */
    @JsonProperty("updateTimeStr")
    private String updateTimeStr;


}
