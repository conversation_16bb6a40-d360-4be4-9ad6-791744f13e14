package com.xyy.saas.web.api.module.member.controller;


import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.member.core.api.MemberSmsTemplateApi;
import com.xyy.saas.member.core.dto.MemberSmsTemplateDto;
import com.xyy.saas.web.api.module.member.model.MemberSmsTemplateVo;
import com.xyy.saas.web.api.module.member.utils.ToolUtil;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.dto.result.EmployeeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2019-08-28T11:16:06.377+08:00")

@Controller
@RequestMapping("/member/memberSmsTemplate")
@Api(value = "memberSmsTemplate", description = "the memberSmsTemplate API")
public class MemberSmsTemplateApiController {


    @Reference( version = "0.0.1")
    private MemberSmsTemplateApi memberSmsTemplateApi;
    @Reference(version = "0.0.1")
    public EmployeeApi employeeApi;

    @ApiOperation(value = "短信模板列表", notes = "短信模板列表", response = JSONObject.class, tags={ "memberSmsTemplate", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = JSONObject.class) })
    @RequestMapping(value = "/getMemberSmsTemplateList", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    public ResponseEntity<ResultVO> getMemberSmsTemplateList(@RequestHeader(name = "organSign", required = true) String organSign,
                                                             @RequestBody MemberSmsTemplateVo templateVo){
        MemberSmsTemplateDto smsRecordDto = new MemberSmsTemplateDto();
        BeanUtils.copyProperties(templateVo, smsRecordDto);
        smsRecordDto.setOrganSign(organSign);
        smsRecordDto.setPageNum(templateVo.getPage() == null ? 1 : templateVo.getPage());
        smsRecordDto.setPageSize(templateVo.getRows() == null ? 10 : templateVo.getRows());
        return new ResponseEntity(ResultVO.createSuccess(memberSmsTemplateApi.getMemberSmsTemplate(smsRecordDto)), HttpStatus.OK);
    }

    @ApiOperation(value = "新增短信模板", notes = "新增短信模板", response = JSONObject.class, tags={ "memberSmsTemplate", })
    @ApiResponses(value = { @ApiResponse(code = 200, message = "操作成功", response = JSONObject.class) })
    @RequestMapping(value = "/saveMemberSmsTemplateList", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    public ResponseEntity<ResultVO> saveMemberSmsTemplateList(@RequestHeader(name = "organSign", required = true) String organSign,
                                                              @RequestHeader("employeeId") Integer employeeId,
                                                              @RequestBody MemberSmsTemplateVo templateVo){
        Map map = new HashMap();
//        if(StringUtils.isEmpty(templateVo.getSignature())){
//        	map.put("code",1);
//        	map.put("result","模板签名为空");
//        	return new ResponseEntity(ResultVO.createSuccess(map), HttpStatus.OK);
//        }
        if(StringUtils.isEmpty(templateVo.getContent())){
            map.put("code",1);
            map.put("result","模板内容为空");
            return new ResponseEntity(ResultVO.createSuccess(map), HttpStatus.OK);
        }
//        if(StringUtils.isEmpty(templateVo.getContentEnd())){
//        	map.put("code",1);
//        	map.put("result","模板内容结尾为空");
//        	return new ResponseEntity(ResultVO.createSuccess(map), HttpStatus.OK);
//        }
        if(memberSmsTemplateApi.checkTemplateCount(organSign)){
            map.put("code",2);
            map.put("result","系统目前只支持最多创建5个模板");
            return new ResponseEntity(ResultVO.createSuccess(map), HttpStatus.OK);
        }
        if(ToolUtil.checkTemplateLength(templateVo.getContent())){
            map.put("code",3);
            map.put("result","模板内容超长");
            return new ResponseEntity(ResultVO.createSuccess(map), HttpStatus.OK);
        }
        com.xyy.saas.common.util.ResultVO<EmployeeDto> result = employeeApi.queryEmployeeById(employeeId);
        if(ResultCodeEnum.SUCCESS.getCode() != result.getCode()){
            map.put("code",4);
            map.put("result","当前用户不可用");
            return new ResponseEntity(ResultVO.createSuccess(map), HttpStatus.OK);
        }
        MemberSmsTemplateDto smsRecordDto = new MemberSmsTemplateDto();
        BeanUtils.copyProperties(templateVo, smsRecordDto);
        smsRecordDto.setOrganSign(organSign);
        smsRecordDto.setCreateUser(((EmployeeDto)result.getResult()).getName());
        return new ResponseEntity(ResultVO.createSuccess(memberSmsTemplateApi.saveMemberSmsTemplate(smsRecordDto)), HttpStatus.OK);
    }


    @ApiOperation(value = "短信模板更新", notes = "短信模板更新", response = JSONObject.class, tags={ "memberSmsTemplate", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = JSONObject.class) })
    @RequestMapping(value = "/updateMemberSmsTemplateList",  method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    public ResponseEntity<ResultVO> updateMemberSmsTemplateList(@RequestHeader(name = "organSign", required = true) String organSign,
                                                                @RequestHeader(name = "employeeId",required = true) Integer employeeId,
                                                                @RequestBody MemberSmsTemplateVo templateVo){
        Map map = new HashMap();
        if(StringUtils.isEmpty(templateVo.getTitle())){
            map.put("code",1);
            map.put("result","模板名称为空");
            return new ResponseEntity(ResultVO.createSuccess(map), HttpStatus.OK);
        }
        if(ToolUtil.checkTemplateLength(templateVo.getContent())){
            map.put("code",3);
            map.put("result","模板内容超长");
            return new ResponseEntity(ResultVO.createSuccess(map), HttpStatus.OK);
        }
        com.xyy.saas.common.util.ResultVO<EmployeeDto> result = employeeApi.queryEmployeeById(employeeId);
        if(ResultCodeEnum.SUCCESS.getCode() != result.getCode()){
            map.put("code",4);
            map.put("result","当前用户不可用");
            return new ResponseEntity(ResultVO.createSuccess(map), HttpStatus.OK);
        }

        MemberSmsTemplateDto smsRecordDto = new MemberSmsTemplateDto();
        BeanUtils.copyProperties(templateVo, smsRecordDto);
        smsRecordDto.setOrganSign(organSign);
        smsRecordDto.setUpdateUser(((EmployeeDto)result.getResult()).getName());
        return new ResponseEntity(ResultVO.createSuccess(memberSmsTemplateApi.updateMemberSmsTemplate(smsRecordDto)), HttpStatus.OK);
    }


    @ApiOperation(value = "短信模板删除", notes = "短信模板删除", response = JSONObject.class, tags={ "memberSmsTemplate", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = JSONObject.class) })
    @RequestMapping(value = "/deleteMemberSmsTemplateList",  method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    public ResponseEntity<ResultVO> deleteMemberSmsTemplateList(@RequestHeader(name = "organSign", required = true) String organSign,
                                                                @RequestBody MemberSmsTemplateVo templateVo){
        MemberSmsTemplateDto smsRecordDto = new MemberSmsTemplateDto();
        BeanUtils.copyProperties(templateVo, smsRecordDto);
        smsRecordDto.setOrganSign(organSign);
        return new ResponseEntity(ResultVO.createSuccess(memberSmsTemplateApi.deleteMemberSmsTemplate(smsRecordDto)), HttpStatus.OK);
    }

    @ApiOperation(value = "通过id查询", notes = "通过id查询", response = JSONObject.class, tags={ "memberSmsTemplate", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = JSONObject.class) })
    @RequestMapping(value = "/selectById", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    public ResponseEntity<ResultVO> selectById(@RequestHeader(name = "organSign", required = true) String organSign,
                                               @RequestBody MemberSmsTemplateVo templateVo){
        return new ResponseEntity(ResultVO.createSuccess(memberSmsTemplateApi.getTemplate(templateVo.getId())), HttpStatus.OK);
    }

    @ApiOperation(value = "跳转编辑", notes = "跳转编辑", response = JSONObject.class, tags={ "memberSmsTemplate", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = JSONObject.class) })
    @RequestMapping(value = "/toUpdate", method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    public ResponseEntity<ResultVO> toUpdate(@RequestHeader(name = "organSign", required = true) String organSign,
                                             @RequestBody MemberSmsTemplateVo templateVo){
        Map map = new HashMap();
        if(Objects.isNull(templateVo.getId())){
            map.put("code",1);
            map.put("result","id 为空");
            return new ResponseEntity(ResultVO.createSuccess(map), HttpStatus.OK);
        }
        return new ResponseEntity(ResultVO.createSuccess(memberSmsTemplateApi.getTemplate(templateVo.getId())), HttpStatus.OK);
    }


    @ApiOperation(value = "启用禁用", notes = "启用禁用", response = JSONObject.class, tags={ "memberSmsTemplate", })
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = JSONObject.class) })
    @RequestMapping(value = "/updateStatus",  method = RequestMethod.POST,produces =  "application/json", consumes = "application/json")
    public ResponseEntity<ResultVO> updateStatus(@RequestHeader(name = "organSign", required = true) String organSign,
                                                 @RequestHeader(name = "employeeId",required = true) Integer employeeId,
                                                 @RequestBody MemberSmsTemplateVo templateVo){
        Map map = new HashMap();
        com.xyy.saas.common.util.ResultVO<EmployeeDto> result = employeeApi.queryEmployeeById(employeeId);
        if(ResultCodeEnum.SUCCESS.getCode() != result.getCode()){
            map.put("code",4);
            map.put("result","当前用户不可用");
            return new ResponseEntity(ResultVO.createSuccess(map), HttpStatus.OK);
        }
        MemberSmsTemplateDto smsRecordDto = new MemberSmsTemplateDto();
        smsRecordDto.setUpdateUser(((EmployeeDto)result.getResult()).getName());
        smsRecordDto.setId(templateVo.getId());
        smsRecordDto.setStatus(templateVo.getStatus());
        return new ResponseEntity(ResultVO.createSuccess(memberSmsTemplateApi.updateMemberSmsTemplate(smsRecordDto)), HttpStatus.OK);
    }

}
