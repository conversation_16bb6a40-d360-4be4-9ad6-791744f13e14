/**
 * NOTE: This class is auto generated by the swagger code generator program (2.2.3).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.web.api.module.product.model.ApproveHistoryVo;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T19:34:49.364+08:00")
@RequestMapping("/product")
@Api(value = "approveHistory", description = "the approveHistory API")
public interface ApproveHistoryApi {

    @ApiOperation(value = "批量修改指定商品的审核状态", notes = "批量修改指定商品的审核状态", response = ApproveHistoryVo.class, tags={ "approveHistory", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "操作成功", response = ApproveHistoryVo.class) })
    
    @RequestMapping(value = "/approveHistory/batchUpdatePro",
        method = RequestMethod.POST)
    ResponseEntity<ResultVO> batchUpdatePro(@ApiParam(value = "商品审核列表实体以及查询条件实体", required = true) @Valid @RequestBody ApproveHistoryVo approveHistoryVo);


    @ApiOperation(value = "获取商品审核记录主表信息列表", notes = "获取商品审核记录主表信息列表", response = ApproveHistoryVo.class, tags={ "approveHistory", })
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "操作成功", response = ApproveHistoryVo.class) })

    @RequestMapping(value = "/approveHistory/proList",
        method = RequestMethod.POST)
    ResponseEntity<ResultVO> proList(@ApiParam(value = "商品审核列表实体以及查询条件实体", required = true) @Valid @RequestBody ApproveHistoryVo approveHistoryVo);


    @ApiOperation(value = "修改指定商品的审核状态", notes = "修改指定商品的审核状态", response = ApproveHistoryVo.class, tags={ "approveHistory", })
    @ApiResponses(value = {
        @ApiResponse(code = 200, message = "操作成功", response = ApproveHistoryVo.class) })

    @RequestMapping(value = "/approveHistory/updatePro",
        method = RequestMethod.POST)
    ResponseEntity<ResultVO> updatePro(@ApiParam(value = "商品审核列表实体以及查询条件实体", required = true) @Valid @RequestBody ApproveHistoryVo approveHistoryVo);

}
