/**
 * NOTE: This class is auto generated by the swagger code generator program (2.2.3).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.xyy.saas.web.api.module.product.controller;

import com.xyy.saas.web.api.module.product.model.InventoryVerifyVo;
import com.xyy.saas.web.api.module.product.model.InventoryVerifyVoList;
import io.swagger.annotations.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;

@javax.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2018-09-05T17:19:18.933+08:00")
@RequestMapping("/product")
@Api(value = "findVerifyVos", description = "the findVerifyVos API")
public interface FindVerifyVosApi {

    @ApiOperation(value = "查询所有盘点确认单", notes = "查询所有盘点确认单", response = InventoryVerifyVoList.class, tags={ "InventoryVerifyVo", })
    @ApiResponses(value = { 
        @ApiResponse(code = 200, message = "操作成功", response = InventoryVerifyVoList.class) })
    
    @RequestMapping(value = "/findVerifyVos",
        produces = { "application/xml", "application/json" }, 
        consumes = { "application/json", "application/xml" },
        method = RequestMethod.POST)
    ResponseEntity<InventoryVerifyVoList> findVerifyVos(@ApiParam(value = "盘点计划单Vo", required = true) @Valid @RequestBody InventoryVerifyVo inventoryVerifyVo);

}
